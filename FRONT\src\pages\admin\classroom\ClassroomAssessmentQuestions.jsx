import React, { useState, useEffect, useRef } from 'react'
import { Icon } from '@iconify/react';
import { <PERSON><PERSON>, <PERSON><PERSON>, Spinner } from 'react-bootstrap';
import { toast } from 'react-toastify';
import { addQuestionsToAssessment, getQuestionsInAssessmentForAddModule, getAllQuestionsForClassroomAssessment, removeQuestionFromAssessment } from '../../../services/adminService';
import { usePermissions } from '../../../context/PermissionsContext';

function ClassroomAssessmentQuestions({decodedClassroomId, decodedAssessmentId}) {
    const { permissions } = usePermissions();
    console.log('decodedClassroomId',decodedClassroomId);
    console.log('decodedAssessmentId',decodedAssessmentId);

    // Assessment Questions State (questions already in the assessment)
    const [questions, setQuestions] = useState([]);
    const [isLoadingAssessmentQuestions, setIsLoadingAssessmentQuestions] = useState(false);
    const [assessmentSearchTerm, setAssessmentSearchTerm] = useState('');
    const [currentPage, setCurrentPage] = useState(1);
    const [totalPages, setTotalPages] = useState(1);
    const [totalRecords, setTotalRecords] = useState(0);
    const [itemsPerPage, setItemsPerPage] = useState(10);

    // Add Questions Modal State
    const [showAddModal, setShowAddModal] = useState(false);
    const [availableQuestions, setAvailableQuestions] = useState([]);
    const [selectedQuestionIds, setSelectedQuestionIds] = useState([]);
    const [isLoadingQuestions, setIsLoadingQuestions] = useState(false);
    const [isLoadingMoreQuestions, setIsLoadingMoreQuestions] = useState(false);
    const [searchQuery, setSearchQuery] = useState('');
    const [hasMoreQuestions, setHasMoreQuestions] = useState(true);
    const [availableCurrentPage, setAvailableCurrentPage] = useState(1);
    const [isAddingQuestions, setIsAddingQuestions] = useState(false);

    // Delete confirmation state
    const [showDeleteConfirmation, setShowDeleteConfirmation] = useState(false);
    const [questionToDelete, setQuestionToDelete] = useState(null);
    const [isDeleting, setIsDeleting] = useState(false);

    // Refs for infinite scrolling
    const modalBodyRef = useRef(null);
    const searchDebounceTimeout = useRef(null);
    const assessmentSearchDebounceTimeout = useRef(null);

    // Fetch assessment questions on component mount
    useEffect(() => {
        if (decodedClassroomId && decodedAssessmentId) {
            fetchAssessmentQuestions(1, '');
        } else {
            toast.error('Classroom ID or Assessment ID is missing');
        }
    }, [decodedClassroomId, decodedAssessmentId, itemsPerPage]);

    // Function to fetch assessment questions (questions already in the assessment)
    const fetchAssessmentQuestions = async (page = 1, search = '') => {
        if (!decodedClassroomId || !decodedAssessmentId) {
            toast.error('Classroom ID or Assessment ID is missing');
            return;
        }

        setIsLoadingAssessmentQuestions(true);

        try {
            const payload = {
                class_id: decodedClassroomId,
                assessment_id: decodedAssessmentId,
                page: page,
                limit: itemsPerPage,
                search: search
            };

            console.log('Fetching assessment questions with payload:', payload);
            const response = await getAllQuestionsForClassroomAssessment(payload);
            console.log('Assessment questions response:', response);

            if (response.success) {
                // Add expanded property to each question for UI toggle
                const questionsWithExpandState = (response.questions || []).map(question => ({
                    ...question,
                    isExpanded: false
                }));

                setQuestions(questionsWithExpandState);
                setCurrentPage(response.pagination?.currentPage || 1);
                setTotalPages(response.pagination?.totalPages || 1);
                setTotalRecords(response.pagination?.totalRecords || 0);
            } else {
                toast.error(response.message || 'Failed to fetch assessment questions');
                setQuestions([]);
            }
        } catch (error) {
            console.error('Error fetching assessment questions:', error);
            toast.error('An error occurred while fetching assessment questions');
            setQuestions([]);
        } finally {
            setIsLoadingAssessmentQuestions(false);
        }
    };

    // Function to fetch available questions (questions that can be added to assessment)
    const fetchAvailableQuestions = async (page = 1, search = '', append = false) => {
        if (!decodedClassroomId || !decodedAssessmentId) {
            toast.error('Classroom ID or Assessment ID is missing');
            return;
        }

        if (page === 1) {
            setIsLoadingQuestions(true);
        } else {
            setIsLoadingMoreQuestions(true);
        }

        try {
            const payload = {
                class_id: decodedClassroomId,
                assessment_id: decodedAssessmentId,
                page: page,
                limit: 100000, // Large limit for modal
                search: search
            };

            console.log('Fetching available questions with payload:', payload);
            const response = await getQuestionsInAssessmentForAddModule(payload);
            console.log('Available questions response:', response);

            if (response.success) {
                const newQuestions = response.questions || [];

                if (append) {
                    setAvailableQuestions(prev => [...prev, ...newQuestions]);
                } else {
                    setAvailableQuestions(newQuestions);
                }

                setAvailableCurrentPage(response.pagination?.currentPage || 1);
                setHasMoreQuestions((response.pagination?.currentPage || 1) < (response.pagination?.totalPages || 1));
            } else {
                toast.error(response.message || 'Failed to fetch available questions');
                if (!append) setAvailableQuestions([]);
            }
        } catch (error) {
            console.error('Error fetching available questions:', error);
            toast.error('An error occurred while fetching available questions');
            if (!append) setAvailableQuestions([]);
        } finally {
            setIsLoadingQuestions(false);
            setIsLoadingMoreQuestions(false);
        }
    };

    // Handle assessment questions search with debouncing
    const handleAssessmentQuestionsSearch = (searchValue = assessmentSearchTerm) => {
        setCurrentPage(1);
        fetchAssessmentQuestions(1, searchValue);
    };

    const handleAssessmentQuestionsSearchInputChange = (e) => {
        const value = e.target.value;
        setAssessmentSearchTerm(value);

        if (assessmentSearchDebounceTimeout.current) {
            clearTimeout(assessmentSearchDebounceTimeout.current);
        }

        assessmentSearchDebounceTimeout.current = setTimeout(() => {
            handleAssessmentQuestionsSearch(value);
        }, 300);
    };

    // Handle available questions search with debouncing
    const handleAvailableQuestionsSearch = (searchValue = searchQuery) => {
        setAvailableCurrentPage(1);
        setHasMoreQuestions(true);
        fetchAvailableQuestions(1, searchValue, false);
    };

    const handleAvailableQuestionsSearchInputChange = (e) => {
        const value = e.target.value;
        setSearchQuery(value);

        if (searchDebounceTimeout.current) {
            clearTimeout(searchDebounceTimeout.current);
        }

        searchDebounceTimeout.current = setTimeout(() => {
            handleAvailableQuestionsSearch(value);
        }, 300);
    };

    // Toggle question selection in modal
    const toggleQuestionSelection = (questionId) => {
        const newSelectedIds = selectedQuestionIds.includes(questionId)
            ? selectedQuestionIds.filter(id => id !== questionId)
            : [...selectedQuestionIds, questionId];

        setSelectedQuestionIds(newSelectedIds);
    };

    // Toggle select all questions in modal
    const toggleSelectAll = () => {
        if (selectedQuestionIds.length === availableQuestions.length) {
            setSelectedQuestionIds([]);
        } else {
            setSelectedQuestionIds(availableQuestions.map(q => q.id));
        }
    };

    // Open select questions modal
    const openSelectQuestionsModal = () => {
        if (!permissions.classroom_assessment_question_create) {
            toast.warning("You don't have permission to add questions");
            return;
        }
        setSelectedQuestionIds([]);
        setSearchQuery('');
        setAvailableQuestions([]);
        setAvailableCurrentPage(1);
        setHasMoreQuestions(true);
        setShowAddModal(true);
        fetchAvailableQuestions(1, '', false);
    };

    // Add selected questions to assessment
    const addSelectedQuestionsToAssessment = async () => {
        if (selectedQuestionIds.length === 0) {
            toast.warning('Please select at least one question');
            return;
        }

        setIsAddingQuestions(true);

        try {
            const payload = {
                assessment_id: decodedAssessmentId,
                questionIds: selectedQuestionIds,
                class_id: decodedClassroomId
            };

            console.log('Adding questions to assessment with payload:', payload);
            const response = await addQuestionsToAssessment(payload);

            if (response.success) {
                toast.success(response.message || `${selectedQuestionIds.length} questions added to assessment`);

                // Show loading state for 2 seconds before closing modal
                setTimeout(() => {
                    setShowAddModal(false);
                    setSelectedQuestionIds([]);
                    setIsAddingQuestions(false);

                    // Refresh the assessment questions after adding new ones
                    fetchAssessmentQuestions(currentPage, assessmentSearchTerm);
                }, 2000);
            } else {
                toast.error(response.message || 'Failed to add questions to assessment');
                setIsAddingQuestions(false);
            }
        } catch (error) {
            console.error('Error adding questions to assessment:', error);
            toast.error('An error occurred while adding questions to assessment');
            setIsAddingQuestions(false);
        }
    };

    // Handle delete question
    const handleDeleteQuestion = (question) => {
        if (!permissions.classroom_assessment_question_delete) {
            toast.warning("You don't have permission to delete questions");
            return;
        }
        setQuestionToDelete(question);
        setShowDeleteConfirmation(true);
    };

    // Confirm delete question
    const confirmDeleteQuestion = async () => {
        if (!questionToDelete) return;

        setIsDeleting(true);

        try {
            const payload = {
                question_id: questionToDelete.question_id,
                assessment_id: decodedAssessmentId,
                class_id: decodedClassroomId
            };

            console.log('Removing question from assessment with payload:', payload);
            const response = await removeQuestionFromAssessment(payload);

            if (response.success) {
                toast.success(response.message || "Question removed successfully");

                // Refresh the questions list
                fetchAssessmentQuestions(currentPage, assessmentSearchTerm);
            } else {
                toast.error(response.message || "Failed to remove question");
            }
        } catch (error) {
            console.error('Error removing question from assessment:', error);
            toast.error('An error occurred while removing the question');
        } finally {
            setIsDeleting(false);
            setShowDeleteConfirmation(false);
            setQuestionToDelete(null);
        }
    };

    const cancelDeleteQuestion = () => {
        setShowDeleteConfirmation(false);
        setQuestionToDelete(null);
    };

    // Toggle question expand
    const toggleQuestionExpand = (questionId) => {
        setQuestions(questions.map(q =>
            q.question_id === questionId ? { ...q, isExpanded: !q.isExpanded } : q
        ));
    };

    // Pagination handlers
    const handlePageChange = (newPage) => {
        if (newPage >= 1 && newPage <= totalPages) {
            setCurrentPage(newPage);
            fetchAssessmentQuestions(newPage, assessmentSearchTerm);
        }
    };

    const handleItemsPerPageChange = (e) => {
        const newItemsPerPage = parseInt(e.target.value);
        setItemsPerPage(newItemsPerPage);
        setCurrentPage(1);
        fetchAssessmentQuestions(1, assessmentSearchTerm);
    };

    return (
        <>
            <style>
                {`
                    .wider-modal {
                        max-width: 90%;
                        width: 1200px;
                    }
                `}
            </style>

            {/* Search and Create button  */}
            <div className="row mb-3">
                {/* search and add question button */}
                <div className="col-12 col-md-6 mb-1">
                    <input
                        type="text"
                        className="form-control"
                        placeholder="Search Question"
                        style={{ width: '300px' }}
                        value={assessmentSearchTerm}
                        onChange={handleAssessmentQuestionsSearchInputChange}
                    />
                </div>
                <div className="col-12 col-md-6 d-flex justify-content-end mb-1">
                    <div className="row">
                        <div className="col-12">
                            <button
                                className="btn btn-primary d-flex align-items-center gap-2"
                                onClick={openSelectQuestionsModal}
                                disabled={!permissions.classroom_assessment_question_create}
                                title={!permissions.classroom_assessment_question_create ? "You don't have permission to add questions" : "Add questions to assessment"}
                            >
                                <Icon icon="fluent:add-24-regular" width="20" height="20" />
                                Add Question
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            {/* Questions Table */}
            <div className="row mb-3">
                <div className="col-12">
                    <div className="card">
                        {isLoadingAssessmentQuestions ? (
                            <div className="text-center py-5">
                                <Spinner animation="border" variant="primary" />
                                <p className="mt-2">Loading questions...</p>
                            </div>
                        ) : questions.length === 0 ? (
                            <div className="text-center py-5">
                                <Icon icon="fluent:document-search-24-regular" width="48" height="48" className="mb-3 text-muted" />
                                <p className="text-muted">No questions added for this assessment</p>
                            </div>
                        ) : (
                            <div className="table-responsive">
                                <table className="table table-borderless mb-0">
                                    <thead>
                                        <tr>
                                            <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500', width: '80px' }}>SL No</th>
                                            <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500' }}>Question</th>
                                            <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500', width: '100px' }}>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {questions.map((question, index) => (
                                            <tr key={question.question_id} className="border-bottom">
                                                <td className="py-3 align-middle">{(currentPage - 1) * itemsPerPage + index + 1}</td>
                                                <td className="py-3 align-middle">
                                                    <div>
                                                        <div>{question.question_name}</div>
                                                        {question.isExpanded && question.options && (
                                                            <div className="mt-2">
                                                                <small className="text-muted">Options:</small>
                                                                {question.options.map((option, idx) => (
                                                                    <div key={idx} className={`small p-2 mt-1 rounded ${option.is_correct === 1 ? 'bg-success-subtle' : 'bg-light'}`}>
                                                                        {option.option_value}
                                                                        {option.is_correct === 1 && (
                                                                            <span className="badge bg-success ms-2">Correct</span>
                                                                        )}
                                                                    </div>
                                                                ))}
                                                            </div>
                                                        )}
                                                        <button
                                                            className="btn btn-link text-primary p-0 text-decoration-none mt-1"
                                                            onClick={() => toggleQuestionExpand(question.question_id)}
                                                        >
                                                            {question.isExpanded ? 'Hide Options' : 'Show Options'}
                                                        </button>
                                                    </div>
                                                </td>
                                                <td className="py-3 align-middle">
                                                    <button
                                                        className="btn btn-outline-danger btn-sm border border-dark"
                                                        style={{ width: '36px', height: '36px' }}
                                                        title={!permissions.classroom_assessment_question_delete ? "You don't have permission to delete questions" : "Remove Question"}
                                                        onClick={() => {
                                                            if (!permissions.classroom_assessment_question_delete) {
                                                                toast.warning("You don't have permission to delete questions");
                                                                return;
                                                            }
                                                            handleDeleteQuestion(question);
                                                        }}
                                                        disabled={!permissions.classroom_assessment_question_delete}
                                                    >
                                                        <Icon icon="fluent:delete-24-regular" width="16" height="16" />
                                                    </button>
                                                </td>
                                            </tr>
                                        ))}
                                    </tbody>
                                </table>
                            </div>
                        )}
                    </div>
                </div>
            </div>

            {/* Record per page and pagination  */}
            <div className="row">
                <div className="col-md-6 d-flex align-items-center gap-3">
                    <select
                        className="form-select"
                        style={{ width: 'auto' }}
                        value={itemsPerPage}
                        onChange={handleItemsPerPageChange}
                    >
                        <option value={10}>10 records per page</option>
                        <option value={25}>25 records per page</option>
                        <option value={50}>50 records per page</option>
                    </select>
                    <span className="text-muted">
                        Total Records: {totalRecords}
                    </span>
                </div>
                <div className="col-md-6 d-flex justify-content-end align-items-center gap-3">
                    <button
                        className="btn btn-primary"
                        style={{ width: '150px' }}
                        onClick={() => handlePageChange(currentPage - 1)}
                        disabled={currentPage === 1 || isLoadingAssessmentQuestions}
                    >
                        Prev
                    </button>
                    <span>Page {currentPage} of {totalPages}</span>
                    <button
                        className="btn btn-primary"
                        style={{ width: '150px' }}
                        onClick={() => handlePageChange(currentPage + 1)}
                        disabled={currentPage >= totalPages || isLoadingAssessmentQuestions}
                    >
                        Next
                    </button>
                </div>
            </div>

            {/* Add Question Modal */}
            <Modal
                show={showAddModal}
                onHide={() => setShowAddModal(false)}
                centered
                dialogClassName="wider-modal"
            >
                <Modal.Header closeButton>
                    <Modal.Title>Add Questions</Modal.Title>
                </Modal.Header>
                <Modal.Body ref={modalBodyRef} style={{ maxHeight: '70vh', overflowY: 'auto' }}>
                    {/* Search Bar */}
                    <div className="mb-3">
                        <input
                            type="text"
                            className="form-control"
                            placeholder="Search questions by name or tag"
                            value={searchQuery}
                            onChange={handleAvailableQuestionsSearchInputChange}
                        />
                    </div>

                    {isLoadingQuestions ? (
                        <div className="text-center py-5">
                            <Spinner animation="border" variant="primary" />
                            <p className="mt-2">Loading questions...</p>
                        </div>
                    ) : availableQuestions.length === 0 ? (
                        <div className="text-center py-5">
                            <Icon icon="fluent:document-search-24-regular" width="48" height="48" className="mb-3 text-muted" />
                            <p className="text-muted">No questions available to add</p>
                        </div>
                    ) : (
                        <>
                            {/* Select All */}
                            <div className="mb-3">
                                <div className="form-check">
                                    <input
                                        type="checkbox"
                                        className="form-check-input"
                                        checked={selectedQuestionIds.length === availableQuestions.length && availableQuestions.length > 0}
                                        onChange={toggleSelectAll}
                                    />
                                    <label className="form-check-label">
                                        Select All Questions ({availableQuestions.length})
                                    </label>
                                </div>
                            </div>

                            {/* Questions Table */}
                            <div className="table-responsive">
                                <table className="table table-borderless mb-0">
                                    <thead>
                                        <tr>
                                            <th style={{ width: '40px' }}></th>
                                            <th style={{ width: '60px' }}>ID</th>
                                            <th>Question</th>
                                            <th style={{ width: '120px' }}>Tags</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {availableQuestions.map((question) => (
                                            <tr
                                                key={question.id}
                                                className={`border-bottom ${selectedQuestionIds.includes(question.id) ? 'table-primary' : ''}`}
                                                onClick={() => toggleQuestionSelection(question.id)}
                                                style={{ cursor: 'pointer' }}
                                            >
                                                <td className="align-middle">
                                                    <div className="form-check">
                                                        <input
                                                            type="checkbox"
                                                            className="form-check-input"
                                                            checked={selectedQuestionIds.includes(question.id)}
                                                            onChange={() => toggleQuestionSelection(question.id)}
                                                            onClick={(e) => e.stopPropagation()}
                                                        />
                                                    </div>
                                                </td>
                                                <td className="align-middle">{question.id}</td>
                                                <td className="align-middle">
                                                    <div>
                                                        <div>{question.question_name}</div>
                                                        {question.options && question.options.length > 0 && (
                                                            <div>
                                                                <button
                                                                    className="btn btn-link text-primary p-0 text-decoration-none"
                                                                    onClick={(e) => {
                                                                        e.stopPropagation();
                                                                        setAvailableQuestions(availableQuestions.map(q =>
                                                                            q.id === question.id ? { ...q, isExpanded: !q.isExpanded } : q
                                                                        ));
                                                                    }}
                                                                >
                                                                    {question.isExpanded ? 'Hide Options' : 'Show Options'}
                                                                </button>
                                                                {question.isExpanded && (
                                                                    <div className="mt-2">
                                                                        {question.options.map((option, idx) => (
                                                                            <div key={idx} className={`small p-2 mt-1 rounded ${option.is_correct === 1 ? 'bg-success-subtle' : 'bg-light'}`}>
                                                                                {option.option_value}
                                                                                {option.is_correct === 1 && (
                                                                                    <span className="badge bg-success ms-2">Correct</span>
                                                                                )}
                                                                            </div>
                                                                        ))}
                                                                    </div>
                                                                )}
                                                            </div>
                                                        )}
                                                    </div>
                                                </td>
                                                <td className="align-middle">
                                                    {(() => {
                                                        // Handle different tag formats safely
                                                        let tagsToDisplay = [];

                                                        if (question.tags) {
                                                            if (Array.isArray(question.tags)) {
                                                                // If tags is already an array, use it directly
                                                                tagsToDisplay = question.tags;
                                                            } else if (typeof question.tags === 'string') {
                                                                // If tags is a string, try to parse it as JSON
                                                                try {
                                                                    const parsed = JSON.parse(question.tags);
                                                                    if (Array.isArray(parsed)) {
                                                                        tagsToDisplay = parsed;
                                                                    } else {
                                                                        // If not an array after parsing, treat as single tag
                                                                        tagsToDisplay = [question.tags];
                                                                    }
                                                                } catch (e) {
                                                                    // If parsing fails, treat it as a single tag
                                                                    tagsToDisplay = [question.tags];
                                                                }
                                                            } else if (typeof question.tags === 'object') {
                                                                // If tags is an object, convert to array
                                                                tagsToDisplay = Object.values(question.tags);
                                                            }
                                                        }

                                                        // Render the tags
                                                        if (tagsToDisplay.length > 0) {
                                                            return (
                                                                <div className="d-flex flex-wrap gap-1">
                                                                    {tagsToDisplay.slice(0, 2).map((tag, idx) => {
                                                                        // Get the tag text, handling different formats
                                                                        const tagText = typeof tag === 'object'
                                                                            ? (tag.name || tag.tag_name || '')
                                                                            : String(tag);

                                                                        // Skip empty tags
                                                                        if (!tagText || tagText.startsWith('custom-')) {
                                                                            return null;
                                                                        }

                                                                        return (
                                                                            <span key={idx} className="badge bg-primary rounded-pill">
                                                                                {tagText}
                                                                            </span>
                                                                        );
                                                                    })}
                                                                    {tagsToDisplay.length > 2 && (
                                                                        <span className="badge bg-secondary">+{tagsToDisplay.length - 2}</span>
                                                                    )}
                                                                </div>
                                                            );
                                                        } else {
                                                            return <span className="text-muted">No tags</span>;
                                                        }
                                                    })()}
                                                </td>
                                            </tr>
                                        ))}
                                    </tbody>
                                </table>
                            </div>

                            {/* Loading indicator for infinite scroll */}
                            {isLoadingMoreQuestions && (
                                <div className="text-center py-3">
                                    <Spinner animation="border" variant="primary" size="sm" />
                                    <span className="ms-2">Loading more questions...</span>
                                </div>
                            )}
                        </>
                    )}
                </Modal.Body>
                <Modal.Footer>
                <div>
                            <span className="text-muted">
                                {selectedQuestionIds.length} question{selectedQuestionIds.length !== 1 ? 's' : ''} selected
                            </span>
                        </div>
                    <div className="d-flex flex-column gap-2 justify-content-between w-100">
                      
                            <Button variant="secondary" onClick={() => setShowAddModal(false)} className="me-2">
                                Cancel
                            </Button>
                            <Button
                                variant="primary"
                                onClick={addSelectedQuestionsToAssessment}
                                disabled={selectedQuestionIds.length === 0 || isAddingQuestions}
                            >
                                {isAddingQuestions ? (
                                    <>
                                        <Spinner
                                            as="span"
                                            animation="border"
                                            size="sm"
                                            role="status"
                                            aria-hidden="true"
                                            className="me-2"
                                        />
                                        Adding...
                                    </>
                                ) : (
                                    `Add Selected Questions (${selectedQuestionIds.length})`
                                )}
                            </Button>
                    </div>
                </Modal.Footer>
            </Modal>

            {/* Delete Confirmation Modal */}
            <Modal
                show={showDeleteConfirmation}
                onHide={cancelDeleteQuestion}
                size="md"
                centered
            >
                <Modal.Header closeButton>
                    <Modal.Title className="text-danger">
                        <Icon icon="fluent:warning-24-regular" className="me-2" />
                        Remove Question
                    </Modal.Title>
                </Modal.Header>
                <Modal.Body>
                    {questionToDelete && (
                        <div>
                            <p>Are you sure you want to remove this question from the assessment?</p>
                            <p className="fw-bold">"{questionToDelete.question_name}"</p>
                            <p className="text-muted mb-0">This action cannot be undone.</p>
                        </div>
                    )}
                </Modal.Body>
                <Modal.Footer>
                    <Button
                        variant="secondary"
                        onClick={cancelDeleteQuestion}
                        disabled={isDeleting}
                        className="me-2"
                    >
                        Cancel
                    </Button>
                    <Button
                        variant="danger"
                        onClick={confirmDeleteQuestion}
                        disabled={isDeleting}
                    >
                        {isDeleting ? (
                            <>
                                <Spinner
                                    as="span"
                                    animation="border"
                                    size="sm"
                                    role="status"
                                    aria-hidden="true"
                                    className="me-2"
                                />
                                Removing...
                            </>
                        ) : (
                            'Remove Question'
                        )}
                    </Button>
                </Modal.Footer>
            </Modal>
        </>
    )
}

export default ClassroomAssessmentQuestions

