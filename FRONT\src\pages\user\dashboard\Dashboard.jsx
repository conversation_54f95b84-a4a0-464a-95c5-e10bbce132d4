import React, { useEffect, useState } from 'react';
import { Icon } from '@iconify/react';
import Activity from '../../../components/common/Activity';
import UserStatistics from './Statistics';
import TodoList from './TodoList';
import TaskList from './TaskList';
import { getTraineeStats } from '../../../services/userService';
import { useNavigate } from 'react-router-dom';

function UserDashboard() {
  const [monthlyPerformance, setMonthlyPerformance] = useState([]);
  const [traineeStats, setTraineeStats] = useState({
    certificate: 0,
    ranking: 0,
    points: 0,
    totalClassrooms: 0,
    totalCourses: 0,
    totalFreeCoursesPurchased: 0,
    totalPaidCoursesPurchased: 0,
    totalCompletedCourses: 0
  });

  const navigate = useNavigate();

  const handleCardClick = (route) => {
    navigate(route);
  };

  useEffect(() => {
    fetchTraineeStats();  
  }, []);

  const fetchTraineeStats = async () => {
    try {
      console.log('fetching trainee stats');
      const response = await getTraineeStats();
      console.log('trainee stats response', response);
      if (response?.data) {
        setTraineeStats(response.data);
      }
    } catch (error) {
      console.log('error fetching trainee stats', error);
    }
  };
  
  return (
    <>
      {/* Main Dashboard Content */}
      <div className="row mb-4">

        {/* Left Column - Stats Cards */}
        <div className="col-md-8 mb-3">

          {/* First Row - Stats Cards */}
          <div className="row mb-3">
            {/* Classrooms Card */}
            <div className="col-md-4 mb-3">
              <div className="card h-100 jump clickable" style={{ backgroundColor: '#f8e5ff', cursor: 'pointer' }} onClick={() => handleCardClick('/user/AllClassroom')}>
                <div className="card-body d-flex align-items-center">
                  <div className="rounded-circle bg-white p-3 me-3">
                    <Icon icon="fluent:home-24-regular" width="24" height="24" style={{ color: '#9c27b0' }} />
                  </div>
                  <div>
                    <h6 className="card-title text-muted mb-1">Classrooms</h6>
                    <h3 className="mb-0 fw-bold">{traineeStats.totalClassrooms || '--'}</h3>
                  </div>
                </div>
              </div>
            </div>  
            
            {/* Certificates Card */}
            <div className="col-md-4 mb-3">
              <div className="card h-100 jump clickable" style={{ backgroundColor: '#e3f2fd', cursor: 'pointer' }} onClick={() => handleCardClick('/user/certificates')}>
                <div className="card-body d-flex align-items-center">
                  <div className="rounded-circle bg-white p-3 me-3">
                    <Icon icon="fluent:document-24-regular" width="24" height="24" style={{ color: '#2196f3' }} />
                  </div>
                  <div>
                    <h6 className="card-title text-muted mb-1">Certificates</h6>
                    <h3 className="mb-0 fw-bold">{traineeStats.certificate || '--'}</h3>
                  </div>
                </div>
              </div>
            </div>
            
            {/* Courses Card */}
            <div className="col-md-4 mb-3">
              <div className="card h-100 jump clickable" style={{ backgroundColor: '#e0f2f1', cursor: 'pointer' }} onClick={() => handleCardClick('/user/courses')}>
                <div className="card-body d-flex align-items-center">
                  <div className="rounded-circle bg-white p-3 me-3">
                    <Icon icon="fluent:book-24-regular" width="24" height="24" style={{ color: '#009688' }} />
                  </div>
                  <div>
                    <h6 className="card-title text-muted mb-1">Courses</h6>
                    <h3 className="mb-0 fw-bold">{traineeStats.totalCourses || '--'}</h3>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          {/* Second Row - Additional Stats */}
          <div className="row mb-3">
            {/* Free Courses Card */}
            <div className="col-md-4 mb-3">
              <div className="card h-100 jump clickable" style={{ backgroundColor: '#e8f5e9', cursor: 'pointer' }} onClick={() => handleCardClick('/user/courses')}>
                <div className="card-body d-flex align-items-center">
                  <div className="rounded-circle bg-white p-3 me-3">
                    <Icon icon="fluent:gift-24-regular" width="24" height="24" style={{ color: '#4caf50' }} />
                  </div>
                  <div>
                    <h6 className="card-title text-muted mb-1">Free Courses</h6>
                    <h3 className="mb-0 fw-bold">{traineeStats.totalFreeCoursesPurchased || '--'}</h3>
                  </div>
                </div>
              </div>
            </div>
            
            {/* Paid Courses Card */}
            <div className="col-md-4 mb-3">
              <div className="card h-100 jump clickable" style={{ backgroundColor: '#fce4ec', cursor: 'pointer' }} onClick={() => handleCardClick('/user/courses')}>
                <div className="card-body d-flex align-items-center">
                  <div className="rounded-circle bg-white p-3 me-3">
                    <Icon icon="fluent:premium-24-regular" width="24" height="24" style={{ color: '#e91e63' }} />
                  </div>
                  <div>
                    <h6 className="card-title text-muted mb-1">Paid Courses</h6>
                    <h3 className="mb-0 fw-bold">{traineeStats.totalPaidCoursesPurchased || '--'}</h3>
                  </div>
                </div>
              </div>
            </div>
            
            {/* Completed Courses Card */}
            <div className="col-md-4 mb-3">
              <div className="card h-100 jump clickable" style={{ backgroundColor: '#ede7f6', cursor: 'pointer' }} onClick={() => handleCardClick('/user/courses')}>
                <div className="card-body d-flex align-items-center">
                  <div className="rounded-circle bg-white p-3 me-3">
                    <Icon icon="fluent:task-list-square-24-regular" width="24" height="24" style={{ color: '#673ab7' }} />
                  </div>
                  <div>
                    <h6 className="card-title text-muted mb-1">Completed</h6>
                    <h3 className="mb-0 fw-bold">{traineeStats.totalCompletedCourses || '--'}</h3>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Third Row - Stats */}
          <div className="row">
            {/* Rank Card */}
            <div className="col-md-4 mb-3">
              <div className="card h-100 jump clickable" style={{ backgroundColor: '#fff8e1', cursor: 'pointer' }} onClick={() => handleCardClick('/user/profile')}>
                <div className="card-body d-flex align-items-center">
                  <div className="rounded-circle bg-white p-3 me-3">
                    <Icon icon="fluent:trophy-24-regular" width="24" height="24" style={{ color: '#ffc107' }} />
                  </div>
                  <div>
                    <h6 className="card-title text-muted mb-1">Rank</h6>
                    <h3 className="mb-0 fw-bold">{traineeStats.ranking || '--'}</h3>
                  </div>
                </div>
              </div>
            </div>
            
            {/* Points Card */}
            <div className="col-md-4 mb-3">
              <div className="card h-100 jump clickable" style={{ backgroundColor: '#f1f8e9', cursor: 'pointer' }} onClick={() => handleCardClick('/user/profile')}>
                <div className="card-body d-flex align-items-center">
                  <div className="rounded-circle bg-white p-3 me-3">
                    <Icon icon="fluent:star-24-regular" width="24" height="24" style={{ color: '#8bc34a' }} />
                  </div>
                  <div>
                    <h6 className="card-title text-muted mb-1">Points</h6>
                    <h3 className="mb-0 fw-bold">{traineeStats.points || '--'}</h3>
                  </div>
                </div>
              </div>
            </div>
            
            {/* Assessment Card */}
            <div className="col-md-4 mb-3">
              <div className="card h-100 jump clickable" style={{ backgroundColor: '#ffebee', cursor: 'pointer' }} onClick={() => handleCardClick('/user/AllClassroom')}>
                <div className="card-body d-flex align-items-center">
                  <div className="rounded-circle bg-white p-3 me-3">
                    <Icon icon="fluent:clipboard-task-24-regular" width="24" height="24" style={{ color: '#f44336' }} />
                  </div>
                  <div>
                    <h6 className="card-title text-muted mb-1">Assessments</h6>
                    <h3 className="mb-0 fw-bold">--</h3>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Graph  */}
          <div className="row">
            <div className="col-12">
              <UserStatistics />  
            </div>
          </div>
          
        </div>
        
        {/* Right Column - Activity Card and Todo List */}
        <div className="col-md-4">
          <div className="row">
            <Activity />
          </div>
          <div className="row">
            <TaskList />
          </div>
        </div>
      </div>
    </>
  );  
}

export default UserDashboard;