const { mysqlServerConnection } = require('../../../db/db');

const getTickets = async (req, res) => {
  console.log("📡 [API] getTickets called");

  try {
    const { page = 1, limit = 10, search = "", status = "" } = req.body;
    const dbName = req.user.db_name;
    const offset = (page - 1) * limit;

    console.log("🏢 Database:", dbName);
    console.log("📄 Page:", page, "Limit:", limit, "Search:", search, "Status:", status);

    // Build search condition
    let searchCondition = "";
    let queryParams = [];

    if (search && search.trim() !== "") {
      searchCondition = "AND (t.ticket_id LIKE ? OR t.subject LIKE ? OR u.name LIKE ? OR u.email LIKE ?)";
      queryParams.push(`%${search}%`, `%${search}%`, `%${search}%`, `%${search}%`);
    }

    // Build status filter condition
    let statusCondition = "";
    if (status && status.trim() !== "" && status !== "all") {
      statusCondition = "AND t.status = ?";
      queryParams.push(status);
    }

    // Get total count for pagination
    const countQuery = `
      SELECT COUNT(*) as total
      FROM ${dbName}.ticket t
      LEFT JOIN ${dbName}.users u ON t.sender_id = u.id
      WHERE t.is_deleted = 0 ${searchCondition} ${statusCondition}
    `;
    const [countResult] = await mysqlServerConnection.query(countQuery, queryParams);
    const totalRecords = countResult[0].total;

    // Get paginated data with user information
    const dataQuery = `
      SELECT
        t.id,
        t.ticket_id,
        t.sender_id,
        t.subject,
        t.description,
        t.response,
        t.status,
        t.file_url,
        t.assigned_to_id,
        t.createdAt,
        t.updatedAt,
        u.name as sender_name,
        u.email as sender_email,
        u.mobile as sender_phone
      FROM ${dbName}.ticket t
      LEFT JOIN ${dbName}.users u ON t.sender_id = u.id
      WHERE t.is_deleted = 0 ${searchCondition} ${statusCondition}
      ORDER BY t.createdAt DESC
      LIMIT ? OFFSET ?
    `;
    const dataParams = [...queryParams, limit, offset];
    const [result] = await mysqlServerConnection.query(dataQuery, dataParams);

    console.log("✅ Found", result.length, "ticket records");

    return res.status(200).json({
      success: true,
      status: 200,
      data: {
        records: result,
        pagination: {
          currentPage: page,
          totalPages: Math.ceil(totalRecords / limit),
          totalRecords: totalRecords,
          recordsPerPage: limit
        }
      },
      message: result.length > 0 ? "Tickets fetched successfully" : "No tickets found"
    });

  } catch (err) {
    console.error("❌ Error in getTickets:", err);
    return res.status(500).json({
      success: false,
      status: 500,
      message: "Server error while fetching tickets",
      error: err.message
    });
  }
};

const sendTicketResponse = async (req, res) => {
  console.log("📡 [API] sendTicketResponse called");

  try {
    const { id } = req.params;
    const { response, status } = req.body;
    const dbName = req.user.db_name;
    const admin_id = req.user.id;

    console.log("✏️ Updating ticket:", { id, response, status, admin_id });

    // Validate required fields
    if (!id) {
      return res.status(400).json({
        success: false,
        status: 400,
        message: "Ticket ID is required"
      });
    }

    if (!response && !status) {
      return res.status(400).json({
        success: false,
        status: 400,
        message: "Response or status is required"
      });
    }

    // Check if ticket exists and is not deleted
    const [existingTicket] = await mysqlServerConnection.query(
      `SELECT id, ticket_id, status FROM ${dbName}.ticket WHERE id = ? AND is_deleted = 0`,
      [id]
    );

    if (existingTicket.length === 0) {
      return res.status(404).json({
        success: false,
        status: 404,
        message: "Ticket not found or has been deleted"
      });
    }

    // Build update query dynamically
    let updateFields = [];
    let updateParams = [];

    if (response) {
      updateFields.push("response = ?");
      updateParams.push(response);
    }

    if (status) {
      updateFields.push("status = ?");
      updateParams.push(status);
    }

    updateFields.push("assigned_to_id = ?");
    updateParams.push(admin_id);

    updateFields.push("updatedAt = CURRENT_TIMESTAMP");

    // Add the ID parameter for WHERE clause
    updateParams.push(id);

    // Update the ticket
    const updateQuery = `
      UPDATE ${dbName}.ticket
      SET ${updateFields.join(", ")}
      WHERE id = ? AND is_deleted = 0
    `;

    const [result] = await mysqlServerConnection.query(updateQuery, updateParams);

    if (result.affectedRows === 0) {
      return res.status(404).json({
        success: false,
        status: 404,
        message: "Ticket not found or could not be updated"
      });
    }

    console.log("✅ Ticket updated successfully");

    return res.status(200).json({
      success: true,
      status: 200,
      data: {
        id,
        response,
        status,
        assigned_to_id: admin_id
      },
      message: "Ticket response sent successfully"
    });

  } catch (err) {
    console.error("❌ Error in sendTicketResponse:", err);
    return res.status(500).json({
      success: false,
      status: 500,
      message: "Server error while updating ticket",
      error: err.message
    });
  }
};

module.exports = {
  getTickets,
  sendTicketResponse,
};