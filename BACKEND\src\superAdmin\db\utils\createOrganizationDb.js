const mysql = require('mysql2/promise');
const { mysqlServerConnection } = require('../../../db/db'); // Pool for the main DB
const { createOrganizationDatabase } = require('../../../db/utils/createOrgDb');
const maindb = process.env.MAIN_DB;
const bcrypt = require('bcrypt');

async function createOrganization(db_name, adminEmail, admin_name, adminPassword) {
    const orgDBName = `${db_name}`;
    const connection = await mysqlServerConnection.getConnection();

    try {
        // Start transaction
        await connection.query('START TRANSACTION');

        // Step 1: Create the organization database
        await connection.query(`CREATE DATABASE IF NOT EXISTS ${orgDBName}`);
        await createOrganizationDatabase(orgDBName);

        // Step 2: Insert Default Roles and Permissions
        await insertDefaultRolesAndPermissions(orgDBName);

        // Step 3: Insert Default Admin User
        await insertAdminUser(orgDBName, adminEmail, admin_name, adminPassword);
        
        // If everything is successful, commit the transaction
        await connection.query('COMMIT');
        console.log(`Organization ${orgDBName} created successfully`);
    } catch (error) {
        // If any error occurs, rollback the transaction and drop the database
        await connection.query('ROLLBACK');
        
        // Try to drop the database if it was created
        try {
            await connection.query(`DROP DATABASE IF EXISTS ${orgDBName}`);
            console.log(`Rolled back and dropped database ${orgDBName} due to error`);
        } catch (dropError) {
            console.error(`Failed to drop database ${orgDBName}:`, dropError);
        }
        
        console.error('Error creating organization database:', error);
        throw error;
    } finally {
        // Release the connection back to the pool
        connection.release();
    }
}

// Step 2: Insert default roles and permissions
async function insertDefaultRolesAndPermissions(db_name) {
    try {
        // Insert roles (admin, trainee)
        await mysqlServerConnection.query(`
            INSERT INTO ${db_name}.roles (name, description, role_type)
            VALUES 
                ('admin', 'Administrator role with full access', 'admin'),
                ('trainee', 'Trainee role with limited access', 'trainee')
            ON DUPLICATE KEY UPDATE id=id;
        `);

        // Fetch admin role ID
        const [adminRole] = await mysqlServerConnection.query(`
            SELECT id FROM ${db_name}.roles WHERE name = 'admin' LIMIT 1
        `);
        const adminRoleId = adminRole[0]?.id;

        // Fetch trainee role ID
        const [traineeRole] = await mysqlServerConnection.query(`
            SELECT id FROM ${db_name}.roles WHERE name = 'trainee' LIMIT 1
        `);
        const traineeRoleId = traineeRole[0]?.id;

        // Fetch all permissions
        const [permissions] = await mysqlServerConnection.query(`SELECT id FROM ${maindb}.permissions`);

        if (permissions.length > 0) {
            const permissionValues = permissions.map(permission => [adminRoleId, permission.id]);

            // Assign all permissions to admin role
            await mysqlServerConnection.query(`
                INSERT INTO ${db_name}.role_permissions (role_id, permission_id)
                VALUES ?
                ON DUPLICATE KEY UPDATE role_id=role_id;
            `, [permissionValues]);
        }

        console.log("Default roles (admin, trainee) and permissions added successfully.");
    } catch (error) {
        console.error("Error inserting default roles and permissions:", error);
        throw error;
    }
}


// Step 3: Insert default admin user
async function insertAdminUser(db_name, adminEmail, admin_name, adminPassword) {
    try {
        const hashedPassword = await bcrypt.hash(adminPassword, 10);

        const [adminUser] = await mysqlServerConnection.query(`
            INSERT INTO ${db_name}.users (name, email, password, db_name, is_verified)
            VALUES (?, ?, ?, ?, TRUE)
            ON DUPLICATE KEY UPDATE id=id;
        `, [admin_name, adminEmail, hashedPassword, db_name]);

        const adminUserId = adminUser.insertId;
        await mysqlServerConnection.query(`
            INSERT INTO ${db_name}.user_roles (user_id, role_id, createdAt)
            VALUES (?, (SELECT id FROM ${db_name}.roles WHERE name='admin'), NOW())
            ON DUPLICATE KEY UPDATE user_id=user_id;
        `, [adminUserId]);

        console.log("Default admin user created successfully.");
    } catch (error) {
        console.error("Error inserting default admin user:", error);
        throw error;
    }
}


module.exports = { createOrganization };