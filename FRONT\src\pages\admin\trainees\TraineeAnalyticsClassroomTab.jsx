import React, { useState, useEffect } from 'react';
import NoData from '../../../components/common/NoData';
import Loader from '../../../components/common/Loader';
import { Icon } from '@iconify/react';
import { getTraineeAnalytics } from '../../../services/traineeService';
import { toast } from 'react-toastify';

function TraineeAnalyticsClassroomTab({ traineeId }) {
  const [loading, setLoading] = useState(false);
  const [classroomData, setClassroomData] = useState([]);

  const fetchClassroomData = async () => {
    try {
      setLoading(true);
      const response = await getTraineeAnalytics({ trainee_id: traineeId });
      console.log('classrooms Data:', response.data.classrooms);

      if (response.success) {
        setClassroomData(response.data.classrooms || []);
      } else {
        toast.error(response.message || 'Failed to fetch classroom data');
      }
    } catch (error) {
      console.error('Error fetching classroom data:', error);
      toast.error('Something went wrong while fetching classroom data');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (traineeId) {
      fetchClassroomData();
    }
  }, [traineeId]);

  if (loading) {
    return (
      <div className="d-flex justify-content-center align-items-center" style={{ minHeight: '200px' }}>
        <Loader />
      </div>
    );
  }

  if (!classroomData || classroomData.length === 0) {
    return <NoData message="No classrooms found" />;
  }

  return (
    <div className="classroom-list">
      <div className="table-responsive">
        <table className="table table-borderless">
          <thead>
            <tr>
              <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500' }}>SL No.</th>
              <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500' }}>Classroom Name</th>
              <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500', textAlign: 'center' }}>Total Trainees</th>
            </tr>
          </thead>
          <tbody>
            {classroomData.map((classroom, index) => (
              <tr key={classroom.class_id} className="border-bottom">
                <td className="py-3 align-middle">{index + 1}</td>
                <td className="py-3 align-middle">
                  <div className="d-flex align-items-center">
                    <Icon icon="fluent:people-team-24-regular" className="text-muted me-2" width="24" />
                    <span>{classroom.cls_name}</span>
                  </div>
                </td>
                <td className="py-3 align-middle text-center">
                  <span className="badge bg-primary-subtle text-primary">
                    {classroom.total_trainees} Trainees
                  </span>
                </td>

              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}

export default TraineeAnalyticsClassroomTab; 