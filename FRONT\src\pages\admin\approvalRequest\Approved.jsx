import React, { useEffect, useState } from 'react'
import { Icon } from '@iconify/react';
import { useNavigate } from 'react-router-dom';
import defaultCourseImage from '../../../assets/images/admin/course/course1.png'
import { Mo<PERSON>, But<PERSON> } from 'react-bootstrap';
import { toast } from 'react-toastify';
import { encodeData } from '../../../utils/encodeAndEncode';
import { getCourseApprovalANDApprovedANDRejected, RejectApprovalCourse } from '../../../services/adminService';
import NoData from '../../../components/common/NoData';
import Loader from '../../../components/common/Loader';
import { usePermissions } from '../../../context/PermissionsContext';

function Approved() {
    const navigate = useNavigate();
    const { permissions } = usePermissions();
    const [approvedCoursesData, setApprovedCoursesData] = useState([]);
    const [loading, setLoading] = useState(true);
    const [searchTerm, setSearchTerm] = useState('');
    const [currentPage, setCurrentPage] = useState(1);
    const [itemsPerPage, setItemsPerPage] = useState(10);
    const [showRejectModal, setShowRejectModal] = useState(false);
    const [selectedCourseId, setSelectedCourseId] = useState(null);
    const [selectedCourseName, setSelectedCourseName] = useState('');
    const [rejectionReason, setRejectionReason] = useState('');
    const [processing, setProcessing] = useState(false);

    const getApprovedCourses = async () => {
        try {
            setLoading(true);
            const response = await getCourseApprovalANDApprovedANDRejected();
            const approvedCourses = response.data.approved || [];
            setApprovedCoursesData(approvedCourses);
        } catch (error) {
            console.error('Error fetching approved courses:', error);
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        getApprovedCourses();
    }, []);

    // Reset to first page when search term changes
    useEffect(() => {
        setCurrentPage(1);
    }, [searchTerm]);

    const getStatusClass = (status) => {
        return 'bg-success-subtle';
    }

    const formatDate = (dateString) => {
        return new Date(dateString).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        });
    }

    // Search functionality
    const filteredData = approvedCoursesData.filter(item => {
        const searchString = searchTerm.toLowerCase();
        return (
            item.course_name?.toLowerCase().includes(searchString) ||
            item.trainer_name?.toLowerCase().includes(searchString) ||
            item.trainer_email?.toLowerCase().includes(searchString) ||
            item.course_desc?.toLowerCase().includes(searchString)
        );
    });

    // Pagination logic
    const indexOfLastItem = currentPage * itemsPerPage;
    const indexOfFirstItem = indexOfLastItem - itemsPerPage;
    const currentItems = filteredData.slice(indexOfFirstItem, indexOfLastItem);
    const totalPages = Math.ceil(filteredData.length / itemsPerPage);

    // Page navigation
    const handlePageChange = (pageNumber) => {
        if (pageNumber >= 1 && pageNumber <= totalPages) {
            setCurrentPage(pageNumber);
        }
    };

    // Items per page change handler
    const handleItemsPerPageChange = (e) => {
        const newItemsPerPage = parseInt(e.target.value);
        setItemsPerPage(newItemsPerPage);
        setCurrentPage(1); // Reset to first page when changing items per page
    };

    const openRejectModal = (courseId, courseName) => {
        if (!permissions.course_approval_workflow_reject) {
            toast.warning("You don't have permission to reject courses");
            return;
        }
        setSelectedCourseId(courseId);
        setSelectedCourseName(courseName);
        setShowRejectModal(true);
    };

    const handleReject = async () => {
        if (!rejectionReason.trim()) {
            toast.warning('Please provide a rejection reason');
            return;
        }

        try {
            setProcessing(true);
            await RejectApprovalCourse({ 
                id: selectedCourseId, 
                reason: rejectionReason 
            });
            // Remove the rejected course from the list
            setApprovedCoursesData(prev => prev.filter(course => course.id !== selectedCourseId));
            // Close modal and reset states
            setShowRejectModal(false);
            setSelectedCourseId(null);
            setSelectedCourseName('');
            setRejectionReason('');
            // Show success message
            toast.success('Course rejected successfully!');
        } catch (error) {
            console.error('Error rejecting course:', error);
            toast.error('Failed to reject course. Please try again.');
        } finally {
            setProcessing(false);
        }
    };

    const handleViewCourse = (courseId, e) => {
        // Prevent row click when clicking on action buttons
        if (e && e.target.closest('.action-buttons')) {
            return;
        }
        const encodedCourseId = encodeData(courseId);
        navigate(`/admin/courses/module/${encodedCourseId}`);
    };

    return (
        <>
            {/* Search bar */}
            <div className="row mb-3">
                <div className="col-md-4  mt-2">
                    <input
                        type="search"
                        className="form-control"
                        placeholder="Search by course name..."
                        style={{ minWidth: '300px' }}
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                    />
                </div>
            </div>

            {/* Table */}
            <div className="row mb-3">
                <div className="col-12">
                    <div className="card">
                        <div className="table-responsive">
                            <table className="table table-borderless mb-0">
                                <thead>
                                    <tr>
                                        <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500', width: '40%' }}>Course Name</th>
                                        <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500', width: '120px' }}>Approved Date</th>
                                        <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500', width: '150px' }}>Instructor</th>
                                        <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500', width: '100px' }}>Status</th>
                                        <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500', width: '120px' }}>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {loading ? (
                                        <tr>
                                            <td colSpan="5">
                                                <Loader />
                                            </td>
                                        </tr>
                                    ) : currentItems.length === 0 ? (
                                        <tr>
                                            <td colSpan="5">
                                                <NoData 
                                                    message={searchTerm ? 'No courses found matching your search.' : 'No approved courses available.'} 
                                                />
                                            </td>
                                        </tr>
                                    ) : (
                                        currentItems.map((course) => (
                                            <tr 
                                                key={course.id} 
                                                className="border-bottom"
                                                onClick={(e) => handleViewCourse(course.id, e)}
                                                style={{ cursor: 'pointer' }}
                                            >
                                                <td className="py-3">
                                                    <div className="d-flex align-items-center">
                                                        <img
                                                            src={course.banner_image || defaultCourseImage}
                                                            alt={course.course_name}
                                                            className="me-3 rounded"
                                                            style={{
                                                                width: '160px',
                                                                height: '90px',
                                                                objectFit: 'cover',
                                                                flexShrink: 0
                                                            }}
                                                            onError={(e) => {
                                                                e.target.src = defaultCourseImage;
                                                            }}
                                                        />
                                                        <div>
                                                            <div className="fw-medium mb-2">{course.course_name}</div>
                                                            <div className="d-flex flex-wrap" style={{ gap: '16px', fontSize: '14px', color: '#6c757d' }}>
                                                                <span className="d-inline-flex align-items-center">
                                                                    <Icon icon="tabler:video" className="me-2" width="16" height="16" />
                                                                    {course.course_summary.total_videos} Videos
                                                                </span>
                                                                <span className="d-inline-flex align-items-center">
                                                                    <Icon icon="tabler:file-text" className="me-2" width="16" height="16" />
                                                                    {course.course_summary.total_documents} Documents
                                                                </span>
                                                                <span className="d-inline-flex align-items-center">
                                                                    <Icon icon="tabler:writing" className="me-2" width="16" height="16" />
                                                                    {course.course_summary.total_assessments} Assessments
                                                                </span>
                                                                <span className="d-inline-flex align-items-center">
                                                                    <Icon icon="tabler:chart-dots" className="me-2" width="16" height="16" />
                                                                    {course.course_summary.total_surveys} Surveys
                                                                </span>
                                                                <span className="d-inline-flex align-items-center">
                                                                    <Icon icon="tabler:clock" className="me-2" width="16" height="16" />
                                                                    {course.course_summary.total_duration}
                                                                </span>
                                                                <span className="d-inline-flex align-items-center">
                                                                    <Icon icon="tabler:users" className="me-2" width="16" height="16" />
                                                                    {course.course_summary.total_purchased} Enrolled
                                                                </span>
                                                                <span className="d-inline-flex align-items-center">
                                                                    <Icon icon="tabler:star" className="me-2" width="16" height="16" />
                                                                    {course.course_summary.average_rating} Rating
                                                                </span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td className="py-3 align-middle">{formatDate(course.request_date)}</td>
                                                <td className="py-3 align-middle">{course.trainer_name}</td>
                                                <td className="py-3 align-middle">
                                                    <span className={`badge text-dark ${getStatusClass(course.status)}`}>
                                                        {course.status}
                                                    </span>
                                                </td>
                                                <td className="py-3 align-middle">
                                                    <div className="d-flex gap-2 action-buttons">
                                                        <button
                                                            className="btn btn-outline-dark btn-sm border border-dark"
                                                            style={{ width: '36px', height: '36px' }}
                                                            title="View Details"
                                                            onClick={(e) => {
                                                                e.stopPropagation();
                                                                handleViewCourse(course.id);
                                                            }}
                                                        >
                                                            <Icon icon="fluent:eye-24-regular" width="16" height="16" />
                                                        </button>
                                                        <button
                                                            className="btn btn-outline-danger btn-sm border border-danger"
                                                            style={{ width: '36px', height: '36px' }}
                                                            title="Reject Course"
                                                            onClick={(e) => {
                                                                e.stopPropagation();
                                                                openRejectModal(course.id, course.course_name);
                                                            }}
                                                            disabled={processing || !permissions.course_approval_workflow_reject}
                                                        >
                                                            <Icon icon="fluent:dismiss-24-regular" width="16" height="16" />
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                        ))
                                    )}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            {/* Pagination */}
            <div className="row">
                <div className="col-md-6 d-flex align-items-center">
                    <select
                        className="form-select"
                        style={{ width: 'auto' }}
                        value={itemsPerPage}
                        onChange={handleItemsPerPageChange}
                    >
                        <option value={10}>10 records per page</option>
                        <option value={25}>25 records per page</option>
                        <option value={50}>50 records per page</option>
                    </select>
                    <span className="ms-3 text-muted">
                        Total Records: {filteredData.length}
                    </span>
                </div>
                <div className="col-md-6 d-flex justify-content-end align-items-center gap-3">
                    <button
                        className="btn btn-primary"
                        style={{ width: '150px' }}
                        onClick={() => handlePageChange(currentPage - 1)}
                        disabled={currentPage === 1}
                    >
                        Prev
                    </button>
                    <span>Page {currentPage} of {totalPages || 1}</span>
                    <button
                        className="btn btn-primary"
                        style={{ width: '150px' }}
                        onClick={() => handlePageChange(currentPage + 1)}
                        disabled={currentPage === totalPages || totalPages === 0}
                    >
                        Next
                    </button>
                </div>
            </div>

            {/* Reject Modal */}
            <Modal show={showRejectModal} onHide={() => setShowRejectModal(false)} centered>
                <Modal.Header closeButton>
                    <Modal.Title>Reject Approved Course</Modal.Title>
                </Modal.Header>
                <Modal.Body>
                    <p>Are you sure you want to reject the course "{selectedCourseName}"?</p>
                    <div className="form-group">
                        <label htmlFor="rejectionReason" className="form-label">Rejection Reason</label>
                        <textarea
                            id="rejectionReason"
                            className="form-control"
                            rows="4"
                            value={rejectionReason}
                            onChange={(e) => setRejectionReason(e.target.value)}
                            placeholder="Please provide a reason for rejection..."
                        ></textarea>
                    </div>
                </Modal.Body>
                <Modal.Footer>
                    <Button 
                        variant="secondary" 
                        onClick={() => {
                            setShowRejectModal(false);
                            setSelectedCourseId(null);
                            setSelectedCourseName('');
                            setRejectionReason('');
                        }}
                    >
                        Cancel
                    </Button>
                    <Button 
                        variant="danger" 
                        onClick={handleReject}
                        disabled={processing || !rejectionReason.trim()}
                    >
                        {processing ? 'Rejecting...' : 'Reject'}
                    </Button>
                </Modal.Footer>
            </Modal>
        </>
    )
}

export default Approved
