import React, { useState, useEffect } from 'react'
import { useParams } from 'react-router-dom';
import { Icon } from '@iconify/react';
import './CourseWatch.css';
import CourseModule from './CourseModule'
import VideoPlayer from './VideoPlayer'
import CourseDocument from './CourseDocument'
import CourseQuiz from './CourseQuiz' 
import Course<PERSON>urvey from './CourseSurvey'
import NoData from '../../../../components/common/NoData'
import { decodeData } from '../../../../utils/encodeAndEncode';
import { getModuleList } from '../../../../services/userService';

function CourseWatch() {
  const [moduleList, setModuleList] = useState([]);
  const [selectedContent, setSelectedContent] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [showMobileMenu, setShowMobileMenu] = useState(false);
  const [courseProgress, setCourseProgress] = useState(0);

  const { encodedId } = useParams();
  const decoded = decodeData(encodedId);
  const courseId = decoded?.id;

  useEffect(() => {
    if (courseId) {
      fetchModuleList();
    } else {
      setError('Invalid course ID');
      setIsLoading(false);
    }
  }, [courseId]);

  // Effect to select first content when moduleList is loaded
  useEffect(() => {
    if (moduleList.length > 0 && !selectedContent) {
      const firstModule = moduleList[0];
      if (firstModule.content && firstModule.content.length > 0) {
        console.log('🏁 Auto-selecting first content:', firstModule.content[0].title, 'ID:', firstModule.content[0].id);
        setSelectedContent(firstModule.content[0]);
      }
    }
  }, [moduleList]);

  // Debug effect to track selectedContent changes
  useEffect(() => {
    console.log('📋 selectedContent state updated:', selectedContent?.title, 'ID:', selectedContent?.id);
  }, [selectedContent]);
  

  // Handle body overflow when mobile menu is open
  useEffect(() => {
    if (showMobileMenu) {
      document.body.classList.add('mobile-menu-open');
    } else {
      document.body.classList.remove('mobile-menu-open');
    }
    return () => {
      document.body.classList.remove('mobile-menu-open');
    };
  }, [showMobileMenu]);

  async function fetchModuleList() {
    try {
      setIsLoading(true);
      setError(null);
      const response = await getModuleList({ course_id: courseId });

      console.log("Module List************:", response);

      if (response?.data?.response) {
        const sortedModules = response.data.response.map(module => ({
          ...module,
          content: Array.isArray(module.content)
            ? [...module.content]
                .map(item => ({
                  ...item,
                  completed: typeof item.completed === 'number' ? item.completed : 0
                }))
                .sort((a, b) => (a.serial_no || 0) - (b.serial_no || 0))
            : []
        }));

        console.log("Processed Module List with completion status:", sortedModules);
        setModuleList(sortedModules);
      } else {
        setError('No course content available');
      }
    } catch (error) {
      console.error('Error fetching module list:', error);
      setError('Failed to load course content. Please try again later.');
      setModuleList([]);
    } finally {
      setIsLoading(false);
    }
  }

  const handleContentClick = (content) => {
    if (!content) return;
    console.log('🎯 Content selected:', content.title, 'ID:', content.id);
    console.log('🎯 Full content object:', content);
    console.log('🎯 Previous selectedContent:', selectedContent);
    setSelectedContent(content);
    console.log('🎯 After setSelectedContent called');
    setShowMobileMenu(false); // Close mobile menu after selection
  };

  // Handle video progress update - update module list dynamically without reload
  const handleVideoProgress = async (videoId) => {
    console.log('Video progress updated for video ID:', videoId);

    // Update the module list state directly without refetching
    setModuleList(prevModules => {
      return prevModules.map(module => ({
        ...module,
        content: module.content.map(item =>
          item.id === videoId
            ? { ...item, completed: 1 } // Mark as completed
            : item
        )
      }));
    });

    console.log('✅ Module completion status updated dynamically for video ID:', videoId);
  };

  // Handle assessment completion - update module list when assessment is passed
  const handleAssessmentCompletion = async (assessmentId, isPassed) => {
    console.log('Assessment completed:', { assessmentId, isPassed });

    if (isPassed) {
      console.log('🎉 Assessment passed! Updating module completion status...');

      // Option 1: Update state directly (faster, no reload)
      setModuleList(prevModules => {
        return prevModules.map(module => ({
          ...module,
          content: module.content.map(item =>
            item.id === assessmentId
              ? { ...item, completed: 1 } // Mark assessment as completed
              : item
          )
        }));
      });

      // Option 2: Refresh from server to get latest data (more accurate)
      // Uncomment this if you prefer to fetch fresh data from server
      /*
      try {
        console.log('🔄 Fetching updated module list from server...');
        await fetchModuleList();
        console.log('✅ Module list refreshed after assessment completion');
      } catch (error) {
        console.error('❌ Error refreshing module list:', error);
      }
      */

      console.log('✅ Assessment completion status updated for ID:', assessmentId);
    } else {
      console.log('❌ Assessment not passed, no status update needed');
    }
  };

  // Handle survey completion - update module list dynamically without reload
  const handleSurveyCompletion = async (surveyId) => {
    console.log('📋 Survey completed for ID:', surveyId);

    // Update the module list state directly without refetching (same as assessment/video)
    setModuleList(prevModules => {
      return prevModules.map(module => ({
        ...module,
        content: module.content.map(item =>
          item.id === surveyId
            ? { ...item, completed: 1 } // Mark survey as completed
            : item
        )
      }));
    });

    console.log('✅ Survey completion status updated dynamically for ID:', surveyId);
  };

  // Handle course progress updates from CourseModule
  const handleCourseProgressUpdate = (progress) => {
    // setCourseProgress(progress);
    // console.log('📊 Course progress updated to:', progress + '%');
  };

  const LoadingSpinner = () => (
    <div className="d-flex justify-content-center align-items-center" style={{ minHeight: '60vh' }}>
      <div className="text-center">
        <div className="spinner-border text-primary" role="status">
          <span className="visually-hidden">Loading...</span>
        </div>
        <p className="mt-2">Loading course content...</p>
      </div>
    </div>
  );

  const ErrorMessage = ({ message }) => (
    <div className="d-flex justify-content-center align-items-center" style={{ minHeight: '60vh' }}>
      <NoData message={message} />
    </div>
  );

  const renderContent = () => {

    if (!selectedContent) {
      return (
        <div className="d-flex justify-content-center align-items-center" style={{ minHeight: '60vh' }}>
          <NoData caption="No content available in this course." />
        </div>
      );
    }

    switch (selectedContent.type?.toLowerCase()) {
      case 'video':
        return (
          <>
            <VideoPlayer
              videoUrl={selectedContent.url}
              contentData={selectedContent}
              courseId={courseId}
              onVideoProgress={handleVideoProgress}
            />
          </>
        );
      case 'document':
        return <CourseDocument documentUrl={selectedContent.url} />;
      case 'assessment':
        return <CourseQuiz
          assessmentId={selectedContent.id}
          moduleData={selectedContent}
          onAssessmentComplete={handleAssessmentCompletion}
        />;
      case 'survey':
        return <CourseSurvey
          surveyId={selectedContent.id}
          moduleData={selectedContent}
          onSurveyComplete={handleSurveyCompletion}
        />;
      default:
        return (
          <div className="text-center p-5">
            <p>Unsupported content type: {selectedContent.type}</p>
          </div>
        );
    }
  };

  if (isLoading) return <LoadingSpinner />;
  if (error) return <ErrorMessage message={error} />;

  return (
    <div className="course-watch-container">
      {/* Header with menu button */}
      <div className="course-watch-header d-flex justify-content-end d-md-none">
        <button 
          className="menu-toggle-btn"
          onClick={() => setShowMobileMenu(true)}
        >
          <Icon icon="mdi:menu" width="24" height="24" />
        </button>
      </div>

      <div className="row">
        <div className="col-md-8 mt-4">
          {renderContent()}
        </div>
        <div className={`col-md-4 course-module-wrapper ${showMobileMenu ? 'show-mobile' : ''}`}>
          {/* Close button for mobile */}
          {showMobileMenu && (
            <button 
              className="mobile-close-btn d-md-none"
              onClick={() => setShowMobileMenu(false)}
            >
              <Icon icon="mdi:close" width="24" height="24" />
            </button>
          )}
          <CourseModule
            courseModules={moduleList}
            courseId={courseId}
            onContentClick={handleContentClick}
            selectedContent={selectedContent}
            // onProgressUpdate={handleCourseProgressUpdate}
          />
        </div>
      </div>
    </div>
  );
} 

export default CourseWatch;