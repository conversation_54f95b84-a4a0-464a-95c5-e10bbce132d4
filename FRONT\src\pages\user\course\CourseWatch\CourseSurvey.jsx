import React, { useState, useEffect, memo } from 'react';
import { Icon } from '@iconify/react';
import './CourseSurvey.css';
import { getCourseSurvey, submitCourseSurvey } from '../../../../services/userService';

const CourseSurvey = memo(function CourseSurvey({ surveyId, moduleData, onSurveyComplete }) {
  const [currentQuestion, setCurrentQuestion] = useState(0);
  const [selectedAnswers, setSelectedAnswers] = useState(() => {
    const saved = localStorage.getItem(`survey-answers-${surveyId}`);
    return saved ? JSON.parse(saved) : {};
  });
  const [showResults, setShowResults] = useState(false);
  const [quizStarted, setQuizStarted] = useState(false);
  const [questions, setQuestions] = useState([]);
  const [totalQuestions, setTotalQuestions] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [assessmentTitle, setAssessmentTitle] = useState('');
  
  // Reset state when surveyId changes
  useEffect(() => {
    console.log('🔄 Survey ID changed to:', surveyId);

    // Reset all state for new survey
    setCurrentQuestion(0);
    setShowResults(false);
    setQuizStarted(false);
    setQuestions([]);
    setTotalQuestions(0);
    setIsLoading(true);
    setError(null);
    setIsSubmitting(false);
    setAssessmentTitle('');

    // Load saved answers for this specific survey
    const saved = localStorage.getItem(`survey-answers-${surveyId}`);
    setSelectedAnswers(saved ? JSON.parse(saved) : {});

    console.log('✅ Survey state reset for ID:', surveyId);
  }, [surveyId]);

  useEffect(() => {
    if (moduleData?.completed === 1) {
      setShowResults(true);
      setIsLoading(false);
    } else {
      fetchSurveyQuestions();
    }
  }, [surveyId, moduleData?.completed]); // Only depend on completion status, not entire moduleData

  useEffect(() => {
    if (Object.keys(selectedAnswers).length > 0) {
      localStorage.setItem(`survey-answers-${surveyId}`, JSON.stringify(selectedAnswers));
    }
  }, [selectedAnswers, surveyId]);

  async function fetchSurveyQuestions() {
    try {
      setIsLoading(true);
      setError(null);
      const response = await getCourseSurvey({ assessmentId: surveyId });
      console.log("Survey Questions************:", response);
      if (response?.data) {
        setQuestions(response.data.questions || []);
        setTotalQuestions(response.data.total_questions || 0);
        setAssessmentTitle(response.data.assessment_title || '');
      } else {
        setError('Failed to load survey questions. Please try again.');
      }
    } catch (error) {
      console.error('Error fetching survey questions:', error);
      setError('An error occurred while loading the survey. Please try again later.');
      setQuestions([]);
      setTotalQuestions(0);
    } finally {
      setIsLoading(false);
    }
  }

  const handleStartSurvey = () => {
    setQuizStarted(true);
  };

  const handleOptionSelect = (questionId, optionId, optionValue) => {
    setSelectedAnswers({
      ...selectedAnswers,
      [questionId]: {
        assessment_id: surveyId,
        question_id: questionId,
        selected_option_id: optionId,
        selected_value: optionValue || ""
      }
    });
  };

  const isQuestionAnswered = (questionId) => {
    return !!selectedAnswers[questionId];
  };

  const handleNext = () => {
    // Remove the confirmation dialog - allow navigation without answering
    if (currentQuestion < questions.length - 1) {
      setCurrentQuestion(currentQuestion + 1);
    }
  };

  const handlePrevious = () => {
    if (currentQuestion > 0) {
      setCurrentQuestion(currentQuestion - 1);
    }
  };

  const validateSurvey = () => {
    const answeredQuestions = questions.filter(q => isQuestionAnswered(q.id));
    if (answeredQuestions.length === 0) {
      return 'Please answer at least one question before submitting the survey.';
    }
    return null;
  };

  const handleSubmit = async () => {
    const validationMessage = validateSurvey();
    if (validationMessage) {
      alert(validationMessage);
      return;
    }

    try {
      setIsSubmitting(true);
      setError(null);
      const formattedAnswers = Object.values(selectedAnswers);
      const response = await submitCourseSurvey(formattedAnswers);

      console.log('📋 Survey submission response:', response);
      setShowResults(true);
      localStorage.removeItem(`survey-answers-${surveyId}`);

      // Call the completion callback to update module list
      if (onSurveyComplete) {
        console.log('🎯 Survey completed successfully, updating module status...');
        onSurveyComplete(surveyId);
      }
    } catch (error) {
      console.error('Error submitting survey:', error);
      setError('Failed to submit survey. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (moduleData?.completed === 1 || showResults) {
    return (
      <div className="survey-container completed-survey p-4">
        <div className="text-center">
          <Icon icon="mdi:check-circle" className="completed-icon pulse" width="80" height="80" />
          <h2 className="mt-4 mb-3">You have completed the survey</h2>
          <p className="text-muted">Thank you! Please give your feedback.</p>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="quiz-container p-0 p-md-5">
        <div className="text-center">
          <div className="spinner-border text-primary" role="status">
            <span className="visually-hidden">Loading survey questions...</span>
          </div>
          <p className="mt-2">Loading survey questions...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="survey-container error-state p-4">
        <div className="text-center">
          <Icon icon="mdi:alert-circle" className="error-icon" width="64" height="64" />
          <h3 className="mt-3">Error</h3>
          <p className="text-danger">{error}</p>
          <button className="btn btn-primary mt-3" onClick={fetchSurveyQuestions}>
            Try Again
          </button>
        </div>
      </div>
    );
  }

  if (!questions.length) {
    return (
      <div className="empty-survey-container">
        <div className="empty-survey-content">
          <Icon icon="mdi:clipboard-text-outline" className="empty-icon bounce" width="64" height="64" />
          <h3>No Questions Found</h3>
          <p>This survey doesn't have any questions yet.</p>
        </div>
      </div>
    );
  }

  if (!quizStarted) {
    return (
      <div className="survey-container start-survey p-4">
        <div className="text-center mb-4">
          <Icon icon="mdi:clipboard-text-outline" width="64" height="64" className="text-primary mb-3" />
          {assessmentTitle && (
            <h1 className="mb-3 text-break  text-wrap fw-bold lh-base">
              {assessmentTitle}
            </h1>
          )}
          <h2 className="mb-4 text-muted">Survey Instructions</h2>
        </div>
        <div className="instruction-list">
          <div className="instruction-item">
            <Icon icon="mdi:play-circle" className="me-2" />
            Click on Start to begin the survey.
          </div>
          <div className="instruction-item">
            <Icon icon="mdi:arrow-left-circle" className="me-2" />
            You can go back to previous questions at any time.
          </div>
          <div className="instruction-item">
            <Icon icon="mdi:check-circle" className="me-2" />
            Your answers will be saved automatically.
          </div>
          <div className="instruction-item">
            <Icon icon="mdi:information" className="me-2" />
            Answer at least one question to submit the survey.
          </div>
        </div>
        <div className="text-center mt-4">
          <button className="btn btn-primary btn-lg" onClick={handleStartSurvey}>
            Start Survey <Icon icon="mdi:arrow-right" className="ms-2" />
          </button>
        </div>
      </div>
    );
  }

  const question = questions[currentQuestion];
  const isLastQuestion = currentQuestion === questions.length - 1;
  const progress = ((currentQuestion + 1) / questions.length) * 100;

  return (
    <div className="survey-container p-4">
      <div className="d-flex justify-content-between align-items-center mb-2">
        <div className="total-questions">
          <span className="text-muted">Question {currentQuestion + 1} of {questions.length}</span>
        </div>
        <div className="question-progress">
          <span className="text-muted">{Math.round(progress)}% Complete</span>
        </div>
      </div>

      <div className="progress mb-4" style={{ height: '8px' }}>
        <div 
          className="progress-bar bg-primary" 
          role="progressbar" 
          style={{ width: `${progress}%` }}
          aria-valuenow={progress}
          aria-valuemin="0" 
          aria-valuemax="100"
        />
      </div>

      <div className="question-section mb-4">
        <div className="question-header mb-3">
        
          <h4 className="question-text mb-3">
          
            <div className="question-content">
            {currentQuestion + 1}) &nbsp;  {question.question}
            </div>
          </h4>
        </div>
        
        <div className="options-list">
          {question.options.map((option) => (
            <div
              key={option.id}
              className={`option-item p-3 mb-3 rounded ${
                selectedAnswers[question.id]?.selected_option_id === option.id ? 'selected' : ''
              }`}
              onClick={() => handleOptionSelect(question.id, option.id, option.value)}
              role="button"
              tabIndex={0}
            >
              {option.value}
            </div>
          ))}
        </div>
      </div>

      {error && (
        <div className="alert alert-danger mb-4">
          {error}
        </div>
      )}

      <div className="d-flex justify-content-between align-items-center mt-4">
        <div>
          {currentQuestion > 0 && (
            <button 
              className="btn btn-outline-primary"
              onClick={handlePrevious}
              disabled={isSubmitting}
            >
              <Icon icon="mdi:chevron-left" /> Previous
            </button>
          )}
        </div>
        <div>
          {!isLastQuestion ? (
            <button 
              className="btn btn-primary"
              onClick={handleNext}
              disabled={isSubmitting}
            >
              Next <Icon icon="mdi:chevron-right" />
            </button>
          ) : (
            <button 
              className="btn btn-success"
              onClick={handleSubmit}
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <>
                  <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                  Submitting...
                </>
              ) : (
                <>Submit <Icon icon="mdi:check" /></>
              )}
            </button>
          )}
        </div>
      </div>
    </div>
  );
});

export default CourseSurvey;