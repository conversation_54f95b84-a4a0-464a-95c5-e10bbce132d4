.course-comments-container {
  padding: 20px;
  background: #fff;
  min-height: calc(120vh - 400px); /* Account for header, video player, and tabs */
  display: flex;
  flex-direction: column;
}

.comments-header {
  margin-bottom: 20px;
}

.add-comment-btn {
  display: flex;
  align-items: center;
  gap: 5px;
}

.comment-input-container {
  background-color: #f8f9fa;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 20px;
}

.comment-input {
  width: 100%;
  min-height: 100px;
  padding: 12px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  margin-bottom: 15px;
  resize: vertical;
  font-size: 14px;
}

.comment-input:focus {
  outline: none;
  border-color: #3152e8;
}

.comment-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.comments-list {
  flex: 1;
  overflow-y: auto;
  max-height: calc(120vh - 550px); /* Adjust based on other elements */
  padding-right: 10px; /* Space for scrollbar */
}

/* Custom scrollbar for comments list */
.comments-list::-webkit-scrollbar {
  width: 6px;
}

.comments-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.comments-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.comments-list::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.comment-item {
  background: #fff;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 15px;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.comment-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.comment-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 10px;
}

.comment-author {
  display: flex;
  align-items: center;
  gap: 12px;
}

.author-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid #f0f0f0;
}

.author-name {
  font-weight: 600;
  color: #333;
  font-size: 15px;
}

.comment-meta {
  display: flex;
  align-items: center;
  gap: 15px;
}

.comment-time {
  font-weight: 600;
  color: #3152e8;
  font-size: 14px;
}

.comment-date {
  color: #666;
  font-size: 13px;
}

.comment-edited {
  color: #888;
  font-size: 12px;
  font-style: italic;
}

.comment-text {
  color: #333;
  line-height: 1.5;
  margin: 0;
  font-size: 14px;
  white-space: pre-wrap;
}

.empty-comments {
  text-align: center;
  padding: 40px 20px;
  color: #666;
  background: #f8f9fa;
  border-radius: 8px;
  margin-top: 20px;
}

.empty-comments p {
  margin: 0;
  font-size: 15px;
}

.no-content-message {
  text-align: center;
  padding: 40px 20px;
  color: #666;
  background: #f8f9fa;
  border-radius: 8px;
  font-size: 15px;
}

/* Button styles */
.comment-actions button {
  padding: 6px 8px;
  margin-left: 8px;
  transition: all 0.2s ease;
}

.comment-actions button:hover {
  opacity: 0.8;
}

.edit-icon, .delete-icon {
  width: 18px;
  height: 18px;
}

/* Button hover states */
.border.border-black:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

/* Dark mode support */
[data-theme='dark'] .comment-item {
  background: var(--bg-secondary);
}

[data-theme='dark'] .border.border-black {
  border-color: var(--border-color) !important;
  color: var(--text-primary) !important;
}

[data-theme='dark'] .border.border-black:hover {
  background-color: var(--bg-tertiary);
}

@media (max-width: 768px) {
  .btn {
    padding: 5px 10px;
    font-size: 13px;
  }
}

/* Ensure proper spacing in mobile view */
@media (max-width: 576px) {
  .comment-header {
    flex-direction: column;
    gap: 0.5rem;
  }

  .comment-meta {
    width: 100%;
    justify-content: flex-end;
  }

  .comment-actions {
    justify-content: flex-end;
  }
}

.replying-to {
  background-color: #f8f9fa;
  padding: 8px 12px;
  border-radius: 4px;
  margin-bottom: 10px;
  font-size: 14px;
  color: #666;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.replies-container {
  border-left: 2px solid #e9ecef;
  padding-left: 20px;
  margin-top: 10px;
}

.reply-item {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 8px;
}

.reply-header {
  margin-bottom: 8px;
}

.reply-author {
  display: flex;
  align-items: center;
}

.reply-author img {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  object-fit: cover;
}

.reply-meta {
  font-size: 12px;
  color: #6c757d;
}

.reply-text {
  font-size: 14px;
  color: #212529;
  margin: 0;
  padding: 0;
}

.comment-footer {
  margin-top: 10px;
  padding-top: 5px;
  border-top: 1px solid #eee;
}

.comment-footer button {
  font-size: 14px;
  color: #666;
  text-decoration: none;
}

.comment-footer button:hover {
  color: #0d6efd;
  text-decoration: underline;
}

.reply-input-container {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 12px;
  margin-top: 8px;
}

.reply-input {
  width: 100%;
  min-height: 60px;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  padding: 8px;
  resize: vertical;
}

.reply-actions {
  margin-top: 8px;
}

.replying-to {
  font-size: 13px;
  color: #6c757d;
  margin-bottom: 8px;
}
