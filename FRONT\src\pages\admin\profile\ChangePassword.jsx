import React, { useState } from 'react';
import { Icon } from '@iconify/react';
import { validatePassword, validateConfirmPassword, validateForm } from '../../../utils/validation';
import { toast } from 'react-toastify';
import { changePassword } from '../../../services/userService';

function ChangePassword() {
  const [formData, setFormData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });

  const [passwordVisible, setPasswordVisible] = useState({
    currentPassword: false,
    newPassword: false,
    confirmPassword: false
  });

  const [passwordRequirements, setPasswordRequirements] = useState({
    length: false,
    letters: false,
    numbers: false,
    special: false
  });

  const [errors, setErrors] = useState({});
  const [isLoading, setIsLoading] = useState(false);

  const validationRules = {
    currentPassword: (value) => value ? [] : ['Current password is required'],
    newPassword: validatePassword,
    confirmPassword: (value) => validateConfirmPassword(value, formData.newPassword)
  };

  const checkPasswordRequirements = (password) => ({
    length: password.length >= 8,
    letters: /[a-zA-Z]/.test(password),
    numbers: /[0-9]/.test(password),
    special: /[!@#$%^&*(),.?":{}|<>]/.test(password)
  });

  const handleChange = (e) => {
    const { id, value } = e.target;
    const newFormData = { ...formData, [id]: value };
    const newErrors = { ...errors, [id]: undefined };

    if (id === 'newPassword') {
      setPasswordRequirements(checkPasswordRequirements(value));
      if (newFormData.confirmPassword) {
        const confirmErrors = validateConfirmPassword(newFormData.confirmPassword, value);
        if (confirmErrors.length > 0) {
          newErrors.confirmPassword = confirmErrors;
        }
      }
    }

    if (id === 'confirmPassword') {
      const confirmErrors = validateConfirmPassword(value, newFormData.newPassword);
      if (confirmErrors.length > 0) {
        newErrors.confirmPassword = confirmErrors;
      }
    }

    setFormData(newFormData);
    setErrors(newErrors);
  };

  const togglePasswordVisibility = (field) => {
    setPasswordVisible(prev => ({ ...prev, [field]: !prev[field] }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    const validation = validateForm(formData, validationRules);
    if (!validation.isValid) {
      setErrors(validation.errors);
      return;
    }

    setIsLoading(true);
    await new Promise(resolve => setTimeout(resolve, 2000));

    try {
      const response = await changePassword({
        currentPassword: formData.currentPassword,
        newPassword: formData.newPassword
      });

      if (response.success) {
        toast.success(response.message || 'Password updated successfully!');
      } else {
        toast.error(response.message || 'Current password is invalid.');
      }
    } catch (error) {
      console.error("❌ Error changing password:", error);
      toast.error(error.response?.data?.message || "Something went wrong");
    } finally {
      setIsLoading(false);
      setFormData({
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      });
      setErrors({});
    }
  };

  const renderInputField = (label, id, value, visible, onChange, error, placeholder) => (
    <div className="col-md-12 mb-3">
      <div className="profile-field-group">
        <label className="profile-label">{label}</label>
        <div className="profile-field position-relative">
          <input
            type={visible ? "text" : "password"}
            className={`form-control pe-5 ${error ? 'is-invalid' : ''}`}
            id={id}
            value={value}
            onChange={onChange}
            placeholder={placeholder}
            disabled={isLoading}
          />
          <button
            type="button"
            style={{
              position: 'absolute',
              right: '0.75rem',
              top: '50%',
              transform: 'translateY(-50%)',
              background: 'transparent',
              border: 'none',
              zIndex: 10
            }}
            onClick={() => togglePasswordVisibility(id)}
            disabled={isLoading}
          >
            <Icon icon={visible ? "mdi:eye-off" : "mdi:eye"} width="18" height="18" />
          </button>
          {error && <div className="invalid-feedback d-block">{error[0]}</div>}
        </div>
      </div>
    </div>
  );

  return (
    <div className="profile-container">
      <div className="profile-card">
        <div className="profile-card-header">
          <h5>Change Password</h5>
        </div>
        <div className="profile-card-body">
          <form onSubmit={handleSubmit}>
            <div className="row">
              <div className="col-12 col-md-6">

                {renderInputField(
                  "Current Password",
                  "currentPassword",
                  formData.currentPassword,
                  passwordVisible.currentPassword,
                  handleChange,
                  errors.currentPassword,
                  "Enter current password"
                )}

                {renderInputField(
                  "New Password",
                  "newPassword",
                  formData.newPassword,
                  passwordVisible.newPassword,
                  handleChange,
                  errors.newPassword,
                  "Enter new password"
                )}

                {/* Password Requirements */}
                <div className="password-requirements mt-2">
                  <div className={`requirement-badge ${passwordRequirements.length ? 'valid' : ''}`}>
                    <Icon icon={passwordRequirements.length ? "mdi:check" : "mdi:close"} className="icon" /> 8+ Characters
                  </div>
                  <div className={`requirement-badge ${passwordRequirements.letters ? 'valid' : ''}`}>
                    <Icon icon={passwordRequirements.letters ? "mdi:check" : "mdi:close"} className="icon" /> Letters
                  </div>
                  <div className={`requirement-badge ${passwordRequirements.numbers ? 'valid' : ''}`}>
                    <Icon icon={passwordRequirements.numbers ? "mdi:check" : "mdi:close"} className="icon" /> Numbers
                  </div>
                  <div className={`requirement-badge ${passwordRequirements.special ? 'valid' : ''}`}>
                    <Icon icon={passwordRequirements.special ? "mdi:check" : "mdi:close"} className="icon" /> Special Chars
                  </div>
                </div>

                {renderInputField(
                  "Confirm New Password",
                  "confirmPassword",
                  formData.confirmPassword,
                  passwordVisible.confirmPassword,
                  handleChange,
                  errors.confirmPassword,
                  "Confirm new password"
                )}

                {/* Submit */}
                <div className="col-12 mt-3 d-flex justify-content-center">
                  <button type="submit" className={`btn btn-primary w-50 ${isLoading ? 'loading' : ''}`} disabled={isLoading}>
                    {isLoading ? (
                      <>
                        <div className="spinner-border spinner-border-sm me-2" role="status" />
                        Changing Password...
                      </>
                    ) : 'Change Password'}
                  </button>
                </div>

              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}

export default ChangePassword;
