.course-rating-container {
  padding: 20px;
  background: #fff;
  min-height: calc(100vh - 400px); /* Account for header, video player, and tabs */
  display: flex;
  flex-direction: column;
}

.rating-header {
  margin-bottom: 20px;
}

.rating-input-container {
  background-color: #f8f9fa;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.rating-stars {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
  justify-content: center;
}

.star-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 5px;
  color: #ddd;
  transition: color 0.2s ease;
}

.star-btn:hover {
  color: #ffd700;
}

.star-btn.active {
  color: #ffd700;
}

.rating-input {
  width: 100%;
  min-height: 100px;
  padding: 15px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  resize: none;
  font-family: inherit;
  font-size: 15px;
  margin-bottom: 20px;
  background-color: white;
  color: #555;
}

.rating-input:focus {
  outline: none;
  border-color: #3152e8;
}

.rating-actions {
  display: flex;
  justify-content: flex-end;
  gap: 15px;
}

.ratings-list {
  flex: 1;
  overflow-y: auto;
  max-height: calc(100vh - 550px); /* Adjust based on other elements */
  padding-right: 10px; /* Space for scrollbar */
}

/* Custom scrollbar for ratings list */
.ratings-list::-webkit-scrollbar {
  width: 6px;
}

.ratings-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.ratings-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.ratings-list::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.rating-item {
  background: #fff;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.rating-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.rating-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.rating-author {
  display: flex;
  align-items: center;
  gap: 12px;
}

.author-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid #f0f0f0;
}

.author-name {
  font-weight: 600;
  color: #333;
  font-size: 15px;
}

.rating-date {
  color: #666;
  font-size: 13px;
}

.rating-stars-display {
  display: flex;
  gap: 5px;
  margin: 12px 0;
}

.rating-stars-display .star-btn {
  cursor: default;
}

.rating-text {
  margin: 12px 0 0 0;
  color: #444;
  line-height: 1.5;
  font-size: 14px;
  white-space: pre-wrap;
}

.empty-ratings {
  text-align: center;
  padding: 40px 20px;
  color: #666;
  background: #f8f9fa;
  border-radius: 8px;
  margin-top: 20px;
}

.empty-ratings p {
  margin: 0;
  font-size: 15px;
}

.no-content-message {
  text-align: center;
  padding: 40px 20px;
  color: #666;
  background: #f8f9fa;
  border-radius: 8px;
  font-size: 15px;
}

/* Loading spinner styles */
.spinner-border {
  width: 3rem;
  height: 3rem;
  color: #3152e8;
}

/* Button styles */
.btn-link {
  text-decoration: none;
  padding: 0;
  margin: 0;
  color: #dc3545;
  transition: opacity 0.2s ease;
}

.btn-link:hover {
  opacity: 0.8;
  color: #dc3545;
}

@media (max-width: 768px) {
  .btn {
    padding: 5px 10px;
    font-size: 13px;
  }
}
