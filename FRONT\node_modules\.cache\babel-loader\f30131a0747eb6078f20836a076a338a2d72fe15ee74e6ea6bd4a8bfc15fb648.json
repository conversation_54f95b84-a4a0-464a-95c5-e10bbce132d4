{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NEW_LMS_FIXING\\\\FRONT\\\\src\\\\pages\\\\user\\\\settings\\\\SuccessPayUPayment.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState, useRef, useContext } from \"react\";\nimport { successPayUPayment } from \"../../../services/userService\";\nimport { useNavigate, useLocation } from \"react-router-dom\";\nimport { Icon } from \"@iconify/react\";\nimport \"./SuccessPayment.css\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SuccessPayUPayment = () => {\n  _s();\n  var _data$price;\n  const navigate = useNavigate();\n  const location = useLocation();\n  const hasInitialized = useRef(false);\n  const authCheckInterval = useRef(null);\n  const maxRetries = 5;\n  const [queryReady, setQueryReady] = useState(false);\n  const [courseId, setCourseId] = useState(\"\");\n  const [txnId, setTxnId] = useState(\"\");\n  const [data, setData] = useState({});\n  const [isLoading, setIsLoading] = useState(true);\n  const [hasError, setHasError] = useState(false);\n  const [retryCount, setRetryCount] = useState(0);\n  const [isRetrying, setIsRetrying] = useState(false);\n  const [authReady, setAuthReady] = useState(false);\n  const sendtovalidation = async (isRetryAttempt = false) => {\n    console.log(\"=== PayU Success Payment Validation Started ===\");\n    console.log(\"Course ID:\", courseId);\n    console.log(\"Transaction ID:\", txnId);\n    console.log(\"Is Retry Attempt:\", isRetryAttempt);\n    if (isRetryAttempt) {\n      setIsRetrying(true);\n      setHasError(false);\n    }\n    try {\n      console.log(\"Calling successPayUPayment API...\");\n      const response = await successPayUPayment(courseId, txnId);\n      console.log(\"Success PayU payment response:\", response);\n      if (response.success) {\n        console.log(\"Payment validation successful:\", response.data);\n        setData(response.data);\n        setHasError(false);\n      } else {\n        console.error(\"Payment validation failed:\", response);\n        if (retryCount < 2 && !isRetryAttempt) {\n          console.log(\"Retrying payment validation...\");\n          setRetryCount(prev => prev + 1);\n          setTimeout(() => sendtovalidation(true), 2000);\n          return;\n        }\n        setHasError(true);\n      }\n    } catch (error) {\n      console.error(\"Error in PayU success payment:\", error);\n      if (retryCount < 2 && !isRetryAttempt) {\n        console.log(\"Retrying due to error...\");\n        setRetryCount(prev => prev + 1);\n        setTimeout(() => sendtovalidation(true), 2000);\n        return;\n      }\n      setHasError(true);\n    } finally {\n      console.log(\"Setting loading to false\");\n      setIsLoading(false);\n      setIsRetrying(false);\n    }\n  };\n  const redirectToCourse = () => {\n    navigate(\"/user/courses\");\n  };\n  const handleManualRetry = () => {\n    setRetryCount(0);\n    setIsLoading(true);\n    setHasError(false);\n    sendtovalidation();\n  };\n\n  // Extract query params when location.search changes\n  useEffect(() => {\n    const queryParams = new URLSearchParams(location.search);\n    const course_id = queryParams.get(\"course_id\");\n    const txnid = queryParams.get(\"txnid\");\n    if (course_id && txnid) {\n      setCourseId(course_id);\n      setTxnId(txnid);\n      setQueryReady(true);\n    } else {\n      setIsLoading(false);\n      setHasError(true);\n    }\n  }, [location.search]);\n\n  // Run validation only when query params are set\n  useEffect(() => {\n    if (queryReady && courseId && txnId) {\n      sendtovalidation();\n    }\n  }, [queryReady, courseId, txnId]);\n  if (isLoading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"d-flex justify-content-center align-items-center\",\n      style: {\n        minHeight: \"60vh\"\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"spinner-border text-primary mb-3\",\n          role: \"status\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 109,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-muted\",\n          children: isRetrying ? `Retrying payment verification... (Attempt ${retryCount + 1}/3)` : \"Verifying your PayU payment...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 110,\n          columnNumber: 11\n        }, this), retryCount > 0 && /*#__PURE__*/_jsxDEV(\"small\", {\n          className: \"text-info d-block mt-2\",\n          children: \"Please wait, we're ensuring your payment is properly processed...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 116,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 108,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 107,\n      columnNumber: 7\n    }, this);\n  }\n  if (hasError) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"payment-error-container\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"error-content text-center\",\n        children: [/*#__PURE__*/_jsxDEV(Icon, {\n          icon: \"mdi:alert-circle\",\n          className: \"text-danger mb-3\",\n          width: \"64\",\n          height: \"64\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Payment Verification Issue\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-muted mb-4\",\n          children: \"We're having trouble verifying your PayU payment. Your payment may have been successful, but we need to confirm it.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"d-flex gap-3 justify-content-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn btn-primary px-4 py-2\",\n            onClick: handleManualRetry,\n            children: [/*#__PURE__*/_jsxDEV(Icon, {\n              icon: \"mdi:refresh\",\n              className: \"me-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 137,\n              columnNumber: 15\n            }, this), \"Try Again\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 136,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn btn-outline-secondary px-4 py-2\",\n            onClick: () => navigate(\"/\"),\n            children: \"Back to Home\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 140,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 135,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-3\",\n          children: /*#__PURE__*/_jsxDEV(\"small\", {\n            className: \"text-muted\",\n            children: [\"Transaction ID: \", txnId, /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 147,\n              columnNumber: 15\n            }, this), \"If the issue persists, please contact support with this transaction ID.\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 145,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 144,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 128,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 127,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"success-payment-container\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"success-payment-card\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"success-icon-wrapper mb-4\",\n        children: /*#__PURE__*/_jsxDEV(Icon, {\n          icon: \"mdi:check-circle\",\n          className: \"text-success\",\n          width: \"64\",\n          height: \"64\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 161,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 160,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"success-title\",\n        children: \"PayU Payment Successful!\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 165,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"success-subtitle\",\n        children: \"Your transaction has been completed successfully via PayU.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 166,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"amount-display my-4\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"amount-value\",\n          children: [\"\\u20B9\", (_data$price = data.price) !== null && _data$price !== void 0 ? _data$price : \"0\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 170,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 169,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"transaction-details\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"detail-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"detail-label\",\n            children: \"Transaction ID\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 176,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"detail-value\",\n            children: [\"#\", data.payment_id ? data.payment_id.toString().toUpperCase().slice(0, 15) : \"-\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 177,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 175,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"detail-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"detail-label\",\n            children: \"PayU Transaction ID\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 182,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"detail-value\",\n            children: [\"#\", data.transaction_id ? data.transaction_id.toString().toUpperCase().slice(0, 15) : \"-\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 183,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 181,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"detail-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"detail-label\",\n            children: \"Date\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"detail-value\",\n            children: new Date().toLocaleDateString(\"en-IN\", {\n              year: \"numeric\",\n              month: \"long\",\n              day: \"numeric\"\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 187,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"detail-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"detail-label\",\n            children: \"Status\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"detail-value status-success\",\n            children: [/*#__PURE__*/_jsxDEV(Icon, {\n              icon: \"mdi:check-circle\",\n              className: \"me-1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 200,\n              columnNumber: 15\n            }, this), \"Completed\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 199,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 197,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"detail-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"detail-label\",\n            children: \"Payment Method\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 205,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"detail-value\",\n            children: \"PayU Gateway\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 206,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 174,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"action-buttons mt-4\",\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-primary btn-lg w-100 mb-3\",\n          onClick: redirectToCourse,\n          children: [/*#__PURE__*/_jsxDEV(Icon, {\n            icon: \"mdi:play-circle\",\n            className: \"me-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 13\n          }, this), \"Go Back to Course\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 212,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 211,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 158,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 157,\n    columnNumber: 5\n  }, this);\n};\n_s(SuccessPayUPayment, \"XQe5c3CcFRQDB/UZyP9K2ZEQEVU=\", false, function () {\n  return [useNavigate, useLocation];\n});\n_c = SuccessPayUPayment;\nexport default SuccessPayUPayment;\nvar _c;\n$RefreshReg$(_c, \"SuccessPayUPayment\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useRef", "useContext", "successPayUPayment", "useNavigate", "useLocation", "Icon", "jsxDEV", "_jsxDEV", "SuccessPayUPayment", "_s", "_data$price", "navigate", "location", "hasInitialized", "authCheckInterval", "maxRetries", "queryReady", "set<PERSON><PERSON><PERSON>R<PERSON>y", "courseId", "setCourseId", "txnId", "setTxnId", "data", "setData", "isLoading", "setIsLoading", "<PERSON><PERSON><PERSON><PERSON>", "setHasError", "retryCount", "setRetryCount", "isRetrying", "setIsRetrying", "authReady", "setAuthReady", "sendtovalidation", "isRetryAttempt", "console", "log", "response", "success", "error", "prev", "setTimeout", "redirectToCourse", "handleManualRetry", "queryParams", "URLSearchParams", "search", "course_id", "get", "txnid", "className", "style", "minHeight", "children", "role", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "icon", "width", "height", "onClick", "price", "payment_id", "toString", "toUpperCase", "slice", "transaction_id", "Date", "toLocaleDateString", "year", "month", "day", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/NEW_LMS_FIXING/FRONT/src/pages/user/settings/SuccessPayUPayment.jsx"], "sourcesContent": ["import React, { useEffect, useState, useRef, useContext } from \"react\";\nimport { successPayUPayment } from \"../../../services/userService\";\nimport { useNavigate, useLocation } from \"react-router-dom\";\nimport { Icon } from \"@iconify/react\";\nimport \"./SuccessPayment.css\";\n\nconst SuccessPayUPayment = () => {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const hasInitialized = useRef(false);\n  const authCheckInterval = useRef(null);\n  const maxRetries = 5;\n\n  const [queryReady, setQueryReady] = useState(false);\n  const [courseId, setCourseId] = useState(\"\");\n  const [txnId, setTxnId] = useState(\"\");\n\n  const [data, setData] = useState({});\n  const [isLoading, setIsLoading] = useState(true);\n  const [hasError, setHasError] = useState(false);\n  const [retryCount, setRetryCount] = useState(0);\n  const [isRetrying, setIsRetrying] = useState(false);\n  const [authReady, setAuthReady] = useState(false);\n\n  const sendtovalidation = async (isRetryAttempt = false) => {\n    console.log(\"=== PayU Success Payment Validation Started ===\");\n    console.log(\"Course ID:\", courseId);\n    console.log(\"Transaction ID:\", txnId);\n    console.log(\"Is Retry Attempt:\", isRetryAttempt);\n\n    if (isRetryAttempt) {\n      setIsRetrying(true);\n      setHasError(false);\n    }\n\n    try {\n      console.log(\"Calling successPayUPayment API...\");\n      const response = await successPayUPayment(courseId, txnId);\n      console.log(\"Success PayU payment response:\", response);\n\n      if (response.success) {\n        console.log(\"Payment validation successful:\", response.data);\n        setData(response.data);\n        setHasError(false);\n      } else {\n        console.error(\"Payment validation failed:\", response);\n        if (retryCount < 2 && !isRetryAttempt) {\n          console.log(\"Retrying payment validation...\");\n          setRetryCount((prev) => prev + 1);\n          setTimeout(() => sendtovalidation(true), 2000);\n          return;\n        }\n        setHasError(true);\n      }\n    } catch (error) {\n      console.error(\"Error in PayU success payment:\", error);\n      if (retryCount < 2 && !isRetryAttempt) {\n        console.log(\"Retrying due to error...\");\n        setRetryCount((prev) => prev + 1);\n        setTimeout(() => sendtovalidation(true), 2000);\n        return;\n      }\n      setHasError(true);\n    } finally {\n      console.log(\"Setting loading to false\");\n      setIsLoading(false);\n      setIsRetrying(false);\n    }\n  };\n\n  const redirectToCourse = () => {\n    navigate(\"/user/courses\");\n  };\n\n  const handleManualRetry = () => {\n    setRetryCount(0);\n    setIsLoading(true);\n    setHasError(false);\n    sendtovalidation();\n  };\n\n  // Extract query params when location.search changes\n  useEffect(() => {\n    const queryParams = new URLSearchParams(location.search);\n    const course_id = queryParams.get(\"course_id\");\n    const txnid = queryParams.get(\"txnid\");\n\n    if (course_id && txnid) {\n      setCourseId(course_id);\n      setTxnId(txnid);\n      setQueryReady(true);\n    } else {\n      setIsLoading(false);\n      setHasError(true);\n    }\n  }, [location.search]);\n\n  // Run validation only when query params are set\n  useEffect(() => {\n    if (queryReady && courseId && txnId) {\n      sendtovalidation();\n    }\n  }, [queryReady, courseId, txnId]);\n\n  if (isLoading) {\n    return (\n      <div className=\"d-flex justify-content-center align-items-center\" style={{ minHeight: \"60vh\" }}>\n        <div className=\"text-center\">\n          <div className=\"spinner-border text-primary mb-3\" role=\"status\" />\n          <p className=\"text-muted\">\n            {isRetrying\n              ? `Retrying payment verification... (Attempt ${retryCount + 1}/3)`\n              : \"Verifying your PayU payment...\"}\n          </p>\n          {retryCount > 0 && (\n            <small className=\"text-info d-block mt-2\">\n              Please wait, we're ensuring your payment is properly processed...\n            </small>\n          )}\n        </div>\n      </div>\n    );\n  }\n\n  if (hasError) {\n    return (\n      <div className=\"payment-error-container\">\n        <div className=\"error-content text-center\">\n          <Icon icon=\"mdi:alert-circle\" className=\"text-danger mb-3\" width=\"64\" height=\"64\" />\n          <h3>Payment Verification Issue</h3>\n          <p className=\"text-muted mb-4\">\n            We're having trouble verifying your PayU payment. Your payment may have been successful, but we need to\n            confirm it.\n          </p>\n          <div className=\"d-flex gap-3 justify-content-center\">\n            <button className=\"btn btn-primary px-4 py-2\" onClick={handleManualRetry}>\n              <Icon icon=\"mdi:refresh\" className=\"me-2\" />\n              Try Again\n            </button>\n            <button className=\"btn btn-outline-secondary px-4 py-2\" onClick={() => navigate(\"/\")}>\n              Back to Home\n            </button>\n          </div>\n          <div className=\"mt-3\">\n            <small className=\"text-muted\">\n              Transaction ID: {txnId}\n              <br />\n              If the issue persists, please contact support with this transaction ID.\n            </small>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"success-payment-container\">\n      <div className=\"success-payment-card\">\n        {/* Success Icon */}\n        <div className=\"success-icon-wrapper mb-4\">\n          <Icon icon=\"mdi:check-circle\" className=\"text-success\" width=\"64\" height=\"64\" />\n        </div>\n\n        {/* Success Message */}\n        <h2 className=\"success-title\">PayU Payment Successful!</h2>\n        <p className=\"success-subtitle\">Your transaction has been completed successfully via PayU.</p>\n\n        {/* Amount */}\n        <div className=\"amount-display my-4\">\n          <span className=\"amount-value\">₹{data.price ?? \"0\"}</span>\n        </div>\n\n        {/* Transaction Details */}\n        <div className=\"transaction-details\">\n          <div className=\"detail-row\">\n            <span className=\"detail-label\">Transaction ID</span>\n            <span className=\"detail-value\">\n              #{data.payment_id ? data.payment_id.toString().toUpperCase().slice(0, 15) : \"-\"}\n            </span>\n          </div>\n          <div className=\"detail-row\">\n            <span className=\"detail-label\">PayU Transaction ID</span>\n            <span className=\"detail-value\">\n              #{data.transaction_id ? data.transaction_id.toString().toUpperCase().slice(0, 15) : \"-\"}\n            </span>\n          </div>\n          <div className=\"detail-row\">\n            <span className=\"detail-label\">Date</span>\n            <span className=\"detail-value\">\n              {new Date().toLocaleDateString(\"en-IN\", {\n                year: \"numeric\",\n                month: \"long\",\n                day: \"numeric\",\n              })}\n            </span>\n          </div>\n          <div className=\"detail-row\">\n            <span className=\"detail-label\">Status</span>\n            <span className=\"detail-value status-success\">\n              <Icon icon=\"mdi:check-circle\" className=\"me-1\" />\n              Completed\n            </span>\n          </div>\n          <div className=\"detail-row\">\n            <span className=\"detail-label\">Payment Method</span>\n            <span className=\"detail-value\">PayU Gateway</span>\n          </div>\n        </div>\n\n        {/* Action Buttons */}\n        <div className=\"action-buttons mt-4\">\n          <button className=\"btn btn-primary btn-lg w-100 mb-3\" onClick={redirectToCourse}>\n            <Icon icon=\"mdi:play-circle\" className=\"me-2\" />\n            Go Back to Course\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default SuccessPayUPayment;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,UAAU,QAAQ,OAAO;AACtE,SAASC,kBAAkB,QAAQ,+BAA+B;AAClE,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,SAASC,IAAI,QAAQ,gBAAgB;AACrC,OAAO,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9B,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,WAAA;EAC/B,MAAMC,QAAQ,GAAGR,WAAW,CAAC,CAAC;EAC9B,MAAMS,QAAQ,GAAGR,WAAW,CAAC,CAAC;EAC9B,MAAMS,cAAc,GAAGb,MAAM,CAAC,KAAK,CAAC;EACpC,MAAMc,iBAAiB,GAAGd,MAAM,CAAC,IAAI,CAAC;EACtC,MAAMe,UAAU,GAAG,CAAC;EAEpB,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACmB,QAAQ,EAAEC,WAAW,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACqB,KAAK,EAAEC,QAAQ,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EAEtC,MAAM,CAACuB,IAAI,EAAEC,OAAO,CAAC,GAAGxB,QAAQ,CAAC,CAAC,CAAC,CAAC;EACpC,MAAM,CAACyB,SAAS,EAAEC,YAAY,CAAC,GAAG1B,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAAC2B,QAAQ,EAAEC,WAAW,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAAC6B,UAAU,EAAEC,aAAa,CAAC,GAAG9B,QAAQ,CAAC,CAAC,CAAC;EAC/C,MAAM,CAAC+B,UAAU,EAAEC,aAAa,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACiC,SAAS,EAAEC,YAAY,CAAC,GAAGlC,QAAQ,CAAC,KAAK,CAAC;EAEjD,MAAMmC,gBAAgB,GAAG,MAAAA,CAAOC,cAAc,GAAG,KAAK,KAAK;IACzDC,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;IAC9DD,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEnB,QAAQ,CAAC;IACnCkB,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEjB,KAAK,CAAC;IACrCgB,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEF,cAAc,CAAC;IAEhD,IAAIA,cAAc,EAAE;MAClBJ,aAAa,CAAC,IAAI,CAAC;MACnBJ,WAAW,CAAC,KAAK,CAAC;IACpB;IAEA,IAAI;MACFS,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;MAChD,MAAMC,QAAQ,GAAG,MAAMpC,kBAAkB,CAACgB,QAAQ,EAAEE,KAAK,CAAC;MAC1DgB,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEC,QAAQ,CAAC;MAEvD,IAAIA,QAAQ,CAACC,OAAO,EAAE;QACpBH,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEC,QAAQ,CAAChB,IAAI,CAAC;QAC5DC,OAAO,CAACe,QAAQ,CAAChB,IAAI,CAAC;QACtBK,WAAW,CAAC,KAAK,CAAC;MACpB,CAAC,MAAM;QACLS,OAAO,CAACI,KAAK,CAAC,4BAA4B,EAAEF,QAAQ,CAAC;QACrD,IAAIV,UAAU,GAAG,CAAC,IAAI,CAACO,cAAc,EAAE;UACrCC,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;UAC7CR,aAAa,CAAEY,IAAI,IAAKA,IAAI,GAAG,CAAC,CAAC;UACjCC,UAAU,CAAC,MAAMR,gBAAgB,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC;UAC9C;QACF;QACAP,WAAW,CAAC,IAAI,CAAC;MACnB;IACF,CAAC,CAAC,OAAOa,KAAK,EAAE;MACdJ,OAAO,CAACI,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtD,IAAIZ,UAAU,GAAG,CAAC,IAAI,CAACO,cAAc,EAAE;QACrCC,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;QACvCR,aAAa,CAAEY,IAAI,IAAKA,IAAI,GAAG,CAAC,CAAC;QACjCC,UAAU,CAAC,MAAMR,gBAAgB,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC;QAC9C;MACF;MACAP,WAAW,CAAC,IAAI,CAAC;IACnB,CAAC,SAAS;MACRS,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;MACvCZ,YAAY,CAAC,KAAK,CAAC;MACnBM,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC;EAED,MAAMY,gBAAgB,GAAGA,CAAA,KAAM;IAC7BhC,QAAQ,CAAC,eAAe,CAAC;EAC3B,CAAC;EAED,MAAMiC,iBAAiB,GAAGA,CAAA,KAAM;IAC9Bf,aAAa,CAAC,CAAC,CAAC;IAChBJ,YAAY,CAAC,IAAI,CAAC;IAClBE,WAAW,CAAC,KAAK,CAAC;IAClBO,gBAAgB,CAAC,CAAC;EACpB,CAAC;;EAED;EACApC,SAAS,CAAC,MAAM;IACd,MAAM+C,WAAW,GAAG,IAAIC,eAAe,CAAClC,QAAQ,CAACmC,MAAM,CAAC;IACxD,MAAMC,SAAS,GAAGH,WAAW,CAACI,GAAG,CAAC,WAAW,CAAC;IAC9C,MAAMC,KAAK,GAAGL,WAAW,CAACI,GAAG,CAAC,OAAO,CAAC;IAEtC,IAAID,SAAS,IAAIE,KAAK,EAAE;MACtB/B,WAAW,CAAC6B,SAAS,CAAC;MACtB3B,QAAQ,CAAC6B,KAAK,CAAC;MACfjC,aAAa,CAAC,IAAI,CAAC;IACrB,CAAC,MAAM;MACLQ,YAAY,CAAC,KAAK,CAAC;MACnBE,WAAW,CAAC,IAAI,CAAC;IACnB;EACF,CAAC,EAAE,CAACf,QAAQ,CAACmC,MAAM,CAAC,CAAC;;EAErB;EACAjD,SAAS,CAAC,MAAM;IACd,IAAIkB,UAAU,IAAIE,QAAQ,IAAIE,KAAK,EAAE;MACnCc,gBAAgB,CAAC,CAAC;IACpB;EACF,CAAC,EAAE,CAAClB,UAAU,EAAEE,QAAQ,EAAEE,KAAK,CAAC,CAAC;EAEjC,IAAII,SAAS,EAAE;IACb,oBACEjB,OAAA;MAAK4C,SAAS,EAAC,kDAAkD;MAACC,KAAK,EAAE;QAAEC,SAAS,EAAE;MAAO,CAAE;MAAAC,QAAA,eAC7F/C,OAAA;QAAK4C,SAAS,EAAC,aAAa;QAAAG,QAAA,gBAC1B/C,OAAA;UAAK4C,SAAS,EAAC,kCAAkC;UAACI,IAAI,EAAC;QAAQ;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAClEpD,OAAA;UAAG4C,SAAS,EAAC,YAAY;UAAAG,QAAA,EACtBxB,UAAU,GACP,6CAA6CF,UAAU,GAAG,CAAC,KAAK,GAChE;QAAgC;UAAA4B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC,EACH/B,UAAU,GAAG,CAAC,iBACbrB,OAAA;UAAO4C,SAAS,EAAC,wBAAwB;UAAAG,QAAA,EAAC;QAE1C;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CACR;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAIjC,QAAQ,EAAE;IACZ,oBACEnB,OAAA;MAAK4C,SAAS,EAAC,yBAAyB;MAAAG,QAAA,eACtC/C,OAAA;QAAK4C,SAAS,EAAC,2BAA2B;QAAAG,QAAA,gBACxC/C,OAAA,CAACF,IAAI;UAACuD,IAAI,EAAC,kBAAkB;UAACT,SAAS,EAAC,kBAAkB;UAACU,KAAK,EAAC,IAAI;UAACC,MAAM,EAAC;QAAI;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACpFpD,OAAA;UAAA+C,QAAA,EAAI;QAA0B;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACnCpD,OAAA;UAAG4C,SAAS,EAAC,iBAAiB;UAAAG,QAAA,EAAC;QAG/B;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJpD,OAAA;UAAK4C,SAAS,EAAC,qCAAqC;UAAAG,QAAA,gBAClD/C,OAAA;YAAQ4C,SAAS,EAAC,2BAA2B;YAACY,OAAO,EAAEnB,iBAAkB;YAAAU,QAAA,gBACvE/C,OAAA,CAACF,IAAI;cAACuD,IAAI,EAAC,aAAa;cAACT,SAAS,EAAC;YAAM;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,aAE9C;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTpD,OAAA;YAAQ4C,SAAS,EAAC,qCAAqC;YAACY,OAAO,EAAEA,CAAA,KAAMpD,QAAQ,CAAC,GAAG,CAAE;YAAA2C,QAAA,EAAC;UAEtF;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACNpD,OAAA;UAAK4C,SAAS,EAAC,MAAM;UAAAG,QAAA,eACnB/C,OAAA;YAAO4C,SAAS,EAAC,YAAY;YAAAG,QAAA,GAAC,kBACZ,EAAClC,KAAK,eACtBb,OAAA;cAAAiD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,2EAER;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACEpD,OAAA;IAAK4C,SAAS,EAAC,2BAA2B;IAAAG,QAAA,eACxC/C,OAAA;MAAK4C,SAAS,EAAC,sBAAsB;MAAAG,QAAA,gBAEnC/C,OAAA;QAAK4C,SAAS,EAAC,2BAA2B;QAAAG,QAAA,eACxC/C,OAAA,CAACF,IAAI;UAACuD,IAAI,EAAC,kBAAkB;UAACT,SAAS,EAAC,cAAc;UAACU,KAAK,EAAC,IAAI;UAACC,MAAM,EAAC;QAAI;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7E,CAAC,eAGNpD,OAAA;QAAI4C,SAAS,EAAC,eAAe;QAAAG,QAAA,EAAC;MAAwB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC3DpD,OAAA;QAAG4C,SAAS,EAAC,kBAAkB;QAAAG,QAAA,EAAC;MAA0D;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eAG9FpD,OAAA;QAAK4C,SAAS,EAAC,qBAAqB;QAAAG,QAAA,eAClC/C,OAAA;UAAM4C,SAAS,EAAC,cAAc;UAAAG,QAAA,GAAC,QAAC,GAAA5C,WAAA,GAACY,IAAI,CAAC0C,KAAK,cAAAtD,WAAA,cAAAA,WAAA,GAAI,GAAG;QAAA;UAAA8C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvD,CAAC,eAGNpD,OAAA;QAAK4C,SAAS,EAAC,qBAAqB;QAAAG,QAAA,gBAClC/C,OAAA;UAAK4C,SAAS,EAAC,YAAY;UAAAG,QAAA,gBACzB/C,OAAA;YAAM4C,SAAS,EAAC,cAAc;YAAAG,QAAA,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACpDpD,OAAA;YAAM4C,SAAS,EAAC,cAAc;YAAAG,QAAA,GAAC,GAC5B,EAAChC,IAAI,CAAC2C,UAAU,GAAG3C,IAAI,CAAC2C,UAAU,CAACC,QAAQ,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG;UAAA;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3E,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACNpD,OAAA;UAAK4C,SAAS,EAAC,YAAY;UAAAG,QAAA,gBACzB/C,OAAA;YAAM4C,SAAS,EAAC,cAAc;YAAAG,QAAA,EAAC;UAAmB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACzDpD,OAAA;YAAM4C,SAAS,EAAC,cAAc;YAAAG,QAAA,GAAC,GAC5B,EAAChC,IAAI,CAAC+C,cAAc,GAAG/C,IAAI,CAAC+C,cAAc,CAACH,QAAQ,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG;UAAA;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACNpD,OAAA;UAAK4C,SAAS,EAAC,YAAY;UAAAG,QAAA,gBACzB/C,OAAA;YAAM4C,SAAS,EAAC,cAAc;YAAAG,QAAA,EAAC;UAAI;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC1CpD,OAAA;YAAM4C,SAAS,EAAC,cAAc;YAAAG,QAAA,EAC3B,IAAIgB,IAAI,CAAC,CAAC,CAACC,kBAAkB,CAAC,OAAO,EAAE;cACtCC,IAAI,EAAE,SAAS;cACfC,KAAK,EAAE,MAAM;cACbC,GAAG,EAAE;YACP,CAAC;UAAC;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACNpD,OAAA;UAAK4C,SAAS,EAAC,YAAY;UAAAG,QAAA,gBACzB/C,OAAA;YAAM4C,SAAS,EAAC,cAAc;YAAAG,QAAA,EAAC;UAAM;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC5CpD,OAAA;YAAM4C,SAAS,EAAC,6BAA6B;YAAAG,QAAA,gBAC3C/C,OAAA,CAACF,IAAI;cAACuD,IAAI,EAAC,kBAAkB;cAACT,SAAS,EAAC;YAAM;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,aAEnD;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACNpD,OAAA;UAAK4C,SAAS,EAAC,YAAY;UAAAG,QAAA,gBACzB/C,OAAA;YAAM4C,SAAS,EAAC,cAAc;YAAAG,QAAA,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACpDpD,OAAA;YAAM4C,SAAS,EAAC,cAAc;YAAAG,QAAA,EAAC;UAAY;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNpD,OAAA;QAAK4C,SAAS,EAAC,qBAAqB;QAAAG,QAAA,eAClC/C,OAAA;UAAQ4C,SAAS,EAAC,mCAAmC;UAACY,OAAO,EAAEpB,gBAAiB;UAAAW,QAAA,gBAC9E/C,OAAA,CAACF,IAAI;YAACuD,IAAI,EAAC,iBAAiB;YAACT,SAAS,EAAC;UAAM;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,qBAElD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAClD,EAAA,CArNID,kBAAkB;EAAA,QACLL,WAAW,EACXC,WAAW;AAAA;AAAAuE,EAAA,GAFxBnE,kBAAkB;AAuNxB,eAAeA,kBAAkB;AAAC,IAAAmE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}