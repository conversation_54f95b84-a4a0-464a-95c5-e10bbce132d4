const {mysqlServerConnection} = require("../../../db/db");
const { deleteFileIfExists } = require("../../../tools/tools");

const maindb = process.env.MAIN_DB;

const getCertificateTemplates = async (req, res, next) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 10;
        const offset = (page - 1) * limit;


        // Fetch the certificate templates with pagination, ordered by updatedAt
        const [certificates] = await mysqlServerConnection.query(`
            SELECT * FROM ${maindb}.certificate_template
            WHERE active= 1
            ORDER BY updatedAt DESC
            LIMIT ? OFFSET ?
        `, [limit, offset]);


        // Fetch total records count
        const [totalRecordsResult] = await mysqlServerConnection.query(`
            SELECT COUNT(*) as totalRecords FROM ${maindb}.certificate_template
            WHERE active= 1
        `);

        const totalRecords = totalRecordsResult[0].totalRecords; // Extract the total records count

        res.status(200).json({
            success: true,
            data: certificates,
            pagination: {
                page,
                limit,
                totalPages: Math.ceil(totalRecords / limit), // Calculate total pages
                totalRecords: totalRecords // Include total records count
            }
        });
    } catch (error) {
        return next({ statusCode: 500, message: error.message || 'Internal Server Error' });
    }
};


const addCertificateTemplate = async (req, res, next) => {
    try {
        const { template_name, banner_id } = req.body;
        console.log("template_name: " + template_name);
        
        // Get the original file path
        const originalFilePath = req.files["banner_img"][0].path;
        console.log("Original file path: " + originalFilePath);
        
        // Modify the file path to the desired format
        const file = originalFilePath.replace(/^(src[\\\/]app[\\\/])/, ''); // Remove the leading 'src/app/' part
        const formattedFilePath = '/uploads/' + file.split(/uploads[\\\/]/)[1]; // Prepend '/uploads/' to the file name
        
        console.log("Formatted file path: " + formattedFilePath);
        
        if (!formattedFilePath) {
            return next({ statusCode: 400, message: 'Please choose a certificate template to be added' });
        }

        const [result] = await mysqlServerConnection.query(`
            INSERT INTO ${req.user.db_name}.certificate_templates (template_name, customised_banner_img, banner_id) 
            VALUES (?, ?, ?)
        `, [template_name, formattedFilePath, banner_id ? banner_id : null]);

        console.log("CERTIFICATE----", result);

        res.status(201).json({
            success: true,
            message: 'Certificate template added successfully',
        });
    } catch (error) {
        console.log(error.message || "Internal Server Error");
        return next({ statusCode: 500, message: error.message || 'Internal Server Error' });
    }
}

const updateCertificateTemplate = async (req,res,next)=>{

    try {
        const {template_name,banner_id,template_id} = req.body;
    const file = req.files["banner_img"] || null;

    if(!template_id){
        return next({statusCode: 404,messge:'Please provide template id'})
    }

    const [data] = await mysqlServerConnection.query(`SELECT * from ${req.user.db_name}.certificate_templates where id = ?`,[template_id]);

    if(data.length === 0){
        return next({statusCode: 404,message: 'Template not found'});
    }

    if(file && data[0].customised_banner_img != null){
        deleteFileIfExists(data[0].customised_banner_img);
    }

    await mysqlServerConnection.query(`
     UPDATE ${req.user.db_name}.certificate_templates
     SET template_name = ?,customised_banner_img = ?,banner_id = ?
     WHERE id = ?,  
    `,[template_name,file? req.files["banner_img"][0].path : data[0].customised_banner_img,banner_id? banner_id : null,template_id]);

    res.status(201).json({
        success: true,
        message: 'Certificate template updated successfully'
    })

    } catch (error) {
        return next({statusCode: 500,message:error.message || 'Internal Server Error'});        
    }

}

const deleteCertificateTemplate = async (req,res,next) =>{
 
    try {
        const {template_id} = req.params;
        if(!template_id){
            return next({statusCode: 404,message: 'Please provide template id'})
        }
        console.log("No template")
        const [result] = await mysqlServerConnection.query(`UPDATE ${req.user.db_name}.certificate_templates SET is_deleted = 1 WHERE id = ?`,template_id);

        if(result.changedRows === 0){
            return next({
                statusCode: 404,
                message: 'Template not found or is already deleted'
            })
        }
        console.log("Template deleted")

        res.status(201).json({
            success: true,
            message: 'Certificate Template deleted successfully'
        })

    } catch (error) {
        return next({statusCode: 500,message: error.message || 'Internal Server Error'})
    }
}

const changeCertificateStatus = async (req,res,next)=>{
 
   try {
        const {template_id} = req.params;
        const {status} = req.body;

        if(!template_id){
            return next({statusCode: 404,message: 'Please provide template id'});
        }

        const [result] = await mysqlServerConnection.query(`UPDATE ${req.user.db_name}.certificate_templates SET is_active = ? where id = ?`[status,template_id]);
        if(result.afectedRows === 0){

        }

        res.status(200).json({
            success: true,
            message: `Certificate ${status === 0?'Deactivated':'Activated'} successfully`
        })

    } catch (error) {
        return next({statusCode: 500,message: error.message || 'Internal Server Error'})
    }
}


module.exports = {
    addCertificateTemplate,
    updateCertificateTemplate,
    getCertificateTemplates,
    deleteCertificateTemplate,
    changeCertificateStatus
}