import React, { useState, useEffect } from 'react';
import { Icon } from '@iconify/react';
import './CourseRating.css';
import DefaultImage from '../../../../assets/images/profile/default-profile.png';
import { addCourseReview, getCourseReviews, deleteReview, editCourseReview } from '../../../../services/userService';
import { toast } from 'react-toastify';

function CourseRating({ contentData }) {
  const [ratings, setRatings] = useState([]);
  const [showInput, setShowInput] = useState(false);
  const [ratingText, setRatingText] = useState('');
  const [selectedRating, setSelectedRating] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [hasUserRated, setHasUserRated] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [editReviewId, setEditReviewId] = useState(null);

  const videoId = contentData?.id;
  const courseId = contentData?.course_id;

  const user = JSON.parse(localStorage.getItem('user'));
  const userId = user?.id;

  console.log('CourseRating Component Data:', {
    courseId,
    videoId,
    contentData
  });

  useEffect(() => {
    if (videoId && courseId) {
      console.log('Fetching reviews for:', {
        courseId,
        videoId
      });
      fetchReviews();
    }
  }, [videoId, courseId]);

  const fetchReviews = async () => {
    try {
      setIsLoading(true);
      const response = await getCourseReviews(courseId, videoId);
      console.log('Course Reviews Response+++++++++++++++++++++++++++++++++++++:', response);
      if (response.success) {
        setRatings(response.data);
        // Check if current user has already rated
        const userHasRated = response.data.some(rating => rating.user_id === userId);
        setHasUserRated(userHasRated);
      }
    } catch (error) {
      console.error('Error fetching reviews:', error);
      toast.error('Failed to load reviews');
    } finally {
      setIsLoading(false);
    }
  };

  const handleAddRating = () => {
    setShowInput(true);
    setIsEditing(false);
    setEditReviewId(null);
    setRatingText('');
    setSelectedRating(0);
  };

  const handleStarClick = (rating) => {
    setSelectedRating(rating);
  };

  const handleSaveRating = async () => {
    if (selectedRating > 0) {
      try {
        let response;
        
        if (isEditing && editReviewId) {
          // Edit existing review
          response = await editCourseReview({
            id: editReviewId,
            class_rating: selectedRating,
            review_text: ratingText
          });
          
          if (response.success) {
            toast.success('Review updated successfully');
          }
        } else {
          // Add new review
          response = await addCourseReview({
            course_id: courseId,
            video_id: videoId,
            class_rating: selectedRating,
            mentor_rating: 0,
            review_text: ratingText
          });
          
          if (response.success) {
            toast.success('Review added successfully');
          }
        }

        if (response.success) {
          await fetchReviews();
          setRatingText('');
          setSelectedRating(0);
          setShowInput(false);
          setIsEditing(false);
          setEditReviewId(null);
        }
      } catch (error) {
        console.error('Error saving review:', error);
        toast.error('Failed to save review');
      }
    }
  };

  const handleDeleteReview = async (review_id) => {
    try {
      const response = await deleteReview(review_id);
      if (response.success) {
        await fetchReviews();
        toast.success('Review deleted successfully');
      }
    } catch (error) {
      console.error('Error deleting review:', error);
      toast.error('Failed to delete review');
    }
  };
  
  const handleEditReview = (rating) => {
    setIsEditing(true);
    setEditReviewId(rating.review_id);
    setRatingText(rating.review_text || '');
    setSelectedRating(rating.class_rating || 0);
    setShowInput(true);
  };

  if (!videoId || !courseId) {
    return <div className="no-content-message">No content selected</div>;
  }

  const renderStars = (rating, interactive = false) => {
    return Array.from({ length: 5 }, (_, index) => (
      <button
        key={index}
        className={`star-btn ${index < rating ? 'active' : ''}`}
        onClick={() => interactive && handleStarClick(index + 1)}
        type="button"
      >
        <Icon icon="mdi:star" width="24" height="24" />
      </button>
    ));
  };

  if (isLoading) {
    return (
      <div className="text-center p-4">
        <div className="spinner-border text-primary" role="status">
          <span className="visually-hidden">Loading reviews...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="course-rating-container p-0">
  <div className="col-8 col-md-3 ms-auto">

      <div className="rating-header">
        <button 
          className="btn btn-primary" 
          onClick={handleAddRating}
          disabled={hasUserRated}
          title={hasUserRated ? "You have already rated this course" : "Add your rating"}
        >
          <Icon icon="mdi:plus" width="16" height="16" />
          {hasUserRated ? "Already Rated" : "Add Rating"}
        </button>
        </div>
      </div>

      {showInput && (
        <div className="rating-input-container">
          <div className="rating-stars">
            {renderStars(selectedRating, true)}
          </div>
          <textarea
            className="rating-input"
            placeholder="Write your review..."
            value={ratingText}
            onChange={(e) => setRatingText(e.target.value)}
          ></textarea>
          <div className="rating-actions">
            <button 
              className="btn btn-secondary" 
              onClick={() => {
                setRatingText('');
                setSelectedRating(0);
                setShowInput(false);
                setIsEditing(false);
                setEditReviewId(null);
              }}
            >
              Cancel
            </button>
            <button 
              className="btn btn-primary" 
              onClick={handleSaveRating}
              disabled={selectedRating === 0}
            >
              {isEditing ? 'Update' : 'Submit'}
            </button>
          </div>
        </div>
      )}

      <div className="ratings-list">
        {ratings.length > 0 ? (
          ratings.map(rating => (
            <div key={rating.review_id} className="rating-item">
              <div className="rating-item-header">
                <div className="rating-author">
                  <img 
                    src={rating.user?.profile_picture || DefaultImage} 
                    alt="avatar" 
                    className="author-avatar" 
                  />
                  <span className="author-name">{rating.user?.name || 'Anonymous'}</span>
                </div>
                <div className="rating-actions">
                  <span className="rating-date">
                    {new Date(rating.review_createdAt).toLocaleDateString()}
                  </span>
                  {userId === rating.user_id && (
                    <>
                      <button 
                        className="btn btn-link text-dark p-0 ms-3"
                        onClick={() => handleEditReview(rating)}
                        title="Edit review"
                      >
                        <Icon icon="mdi:pencil" width="20" height="20" />
                      </button>
                      <button 
                        className="btn btn-link text-danger p-0 ms-2 border-dark"
                        onClick={() => handleDeleteReview(rating.review_id)}
                        title="Delete review"
                      >
                        <Icon icon="mdi:delete" width="20" height="20" />
                      </button>
                    </>
                  )}
                </div>
              </div>
              <div className="rating-stars-display">
                {renderStars(rating.class_rating)}
              </div>
              {rating.review_text && <p className="rating-text">{rating.review_text}</p>}
            </div>
          ))
        ) : (
          <div className="empty-ratings">
            <p>No ratings yet. Be the first to rate this content!</p>
          </div>
        )}
      </div>
    </div>
  );
}

export default CourseRating;