// Name validation
export const validateName = (name) => {
  if (!name) {
    return ['Name is required'];
  }
  if (name.length < 2) {
    return ['Name must be at least 2 characters long'];
  }
  if (!/^[a-zA-Z\s]*$/.test(name)) {
    return ['Name can only contain letters and spaces'];
  }
  return [];
};

// Email validation
export const validateEmail = (email) => {
  if (!email) {
    return ['Email is required'];
  }
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(email)) {
    return ['Please enter a valid email address'];
  }
  return [];
};

// Password validation
export const validatePassword = (password) => {
  if (!password) {
    return ['Password is required'];
  }
  if (password.length < 8) {
    return ['Password must be at least 8 characters long'];
  }
  if (!/[a-zA-Z]/.test(password)) {
    return ['Password must contain at least one letter'];
  }
  if (!/[0-9]/.test(password)) {
    return ['Password must contain at least one number'];
  }
  if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
    return ['Password must contain at least one special character'];
  }
  return [];
};

// Phone validation
export const validatePhone = (phone) => {
  if (!phone) {
    return ['Phone number is required'];
  }
  if (!/^\d{5,15}$/.test(phone)) {
    return ['Phone number must be 5–15 digits'];
  }
  return [];
};

// Address validation
export const validateAddress = {
  street: (street) => {
    const errors = [];
    if (!street) {
      errors.push('Street address is required');
    } else if (street.length < 5) {
      errors.push('Street address must be at least 5 characters long');
    } else if (street.length > 100) {
      errors.push('Street address cannot exceed 100 characters');
    }
    return { isValid: errors.length === 0, errors };
  },

  city: (city) => {
    const errors = [];
    const cityRegex = /^[a-zA-Z\s-']+$/;

    if (!city) {
      errors.push('City is required');
    } else if (!cityRegex.test(city)) {
      errors.push('City can only contain letters, spaces, hyphens, and apostrophes');
    }
    return { isValid: errors.length === 0, errors };
  },

  state: (state) => {
    const errors = [];
    if (!state) {
      errors.push('State is required');
    } else if (state.length < 2) {
      errors.push('Please enter a valid state');
    }
    return { isValid: errors.length === 0, errors };
  },

  zipCode: (zipCode, country = 'US') => {
    const errors = [];
    const zipRegex = {
      US: /^\d{5}(-\d{4})?$/,
      CA: /^[A-Za-z]\d[A-Za-z][ -]?\d[A-Za-z]\d$/,
      UK: /^[A-Z]{1,2}\d[A-Z\d]? ?\d[A-Z]{2}$/,
      // Add more country-specific patterns as needed
    };

    if (!zipCode) {
      errors.push('ZIP/Postal code is required');
    } else {
      const regex = zipRegex[country] || /^[A-Za-z0-9\s-]{3,10}$/;
      if (!regex.test(zipCode)) {
        errors.push('Please enter a valid ZIP/Postal code');
      }
    }
    return { isValid: errors.length === 0, errors };
  }
};

// Profession/Occupation validation
export const validateProfession = (profession) => {
  const errors = [];
  const professionRegex = /^[a-zA-Z\s\-&/(),.]+$/;
  const commonProfessions = [
    'student', 'teacher', 'professor', 'developer', 'engineer',
    'doctor', 'nurse', 'accountant', 'manager', 'consultant',
    'designer', 'artist', 'writer', 'entrepreneur', 'other'
  ];

  if (!profession) {
    errors.push('Profession is required');
  } else {
    if (profession.length < 2) {
      errors.push('Profession must be at least 2 characters long');
    }
    if (profession.length > 50) {
      errors.push('Profession cannot exceed 50 characters');
    }
    if (!professionRegex.test(profession)) {
      errors.push('Profession can only contain letters, spaces, and basic punctuation');
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    suggestions: profession ? 
      commonProfessions.filter(p => p.includes(profession.toLowerCase())) : []
  };
};

// URL validation
export const validateURL = (url) => {
  const errors = [];
  const urlRegex = /^(https?:\/\/)?([\da-z.-]+)\.([a-z.]{2,6})([/\w .-]*)*\/?$/;

  if (!url) {
    errors.push('URL is required');
  } else if (!urlRegex.test(url)) {
    errors.push('Please enter a valid URL');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

// Date validation with DD-MM-YYYY format
export const validateDate = (date, { minAge, maxAge, minDate, maxDate } = {}) => {
  const errors = [];
  
  // Check if date is provided
  if (!date) {
    errors.push('Date is required');
    return {
      isValid: false,
      errors
    };
  }

  // Check format DD-MM-YYYY
  const dateRegex = /^(0[1-9]|[12][0-9]|3[01])-(0[1-9]|1[0-2])-\d{4}$/;
  if (!dateRegex.test(date)) {
    errors.push('Date must be in DD-MM-YYYY format (e.g., 02-05-2025)');
    return {
      isValid: false,
      errors
    };
  }

  // Parse the date
  const [day, month, year] = date.split('-').map(Number);
  const inputDate = new Date(year, month - 1, day); // month is 0-based in JS
  const today = new Date();

  // Check if date is valid
  if (isNaN(inputDate.getTime())) {
    errors.push('Please enter a valid date');
    return {
      isValid: false,
      errors
    };
  }

  // Check if the parsed date matches the input (to catch invalid dates like 31-02-2025)
  if (inputDate.getDate() !== day || 
      inputDate.getMonth() !== month - 1 || 
      inputDate.getFullYear() !== year) {
    errors.push('Please enter a valid date');
    return {
      isValid: false,
      errors
    };
  }

  // Validate year is not too far in the past or future
  const currentYear = today.getFullYear();
  if (year < currentYear - 100 || year > currentYear + 100) {
    errors.push('Year seems invalid');
  }

  // Additional validations if provided
  if (minAge) {
    const minDate = new Date();
    minDate.setFullYear(minDate.getFullYear() - minAge);
    if (inputDate > minDate) {
      errors.push(`Age must be at least ${minAge} years`);
    }
  }

  if (maxAge) {
    const maxDate = new Date();
    maxDate.setFullYear(maxDate.getFullYear() - maxAge);
    if (inputDate < maxDate) {
      errors.push(`Age cannot exceed ${maxAge} years`);
    }
  }

  if (minDate && inputDate < new Date(minDate)) {
    errors.push(`Date cannot be earlier than ${formatDate(new Date(minDate))}`);
  }

  if (maxDate && inputDate > new Date(maxDate)) {
    errors.push(`Date cannot be later than ${formatDate(new Date(maxDate))}`);
  }

  return {
    isValid: errors.length === 0,
    errors,
    parsedDate: inputDate,
    formattedDate: formatDate(inputDate)
  };
};

// Helper function to format date as DD-MM-YYYY
const formatDate = (date) => {
  const day = String(date.getDate()).padStart(2, '0');
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const year = date.getFullYear();
  return `${day}-${month}-${year}`;
};

// Confirm Password validation
export const validateConfirmPassword = (confirmPassword, password) => {
  if (!confirmPassword) {
    return ['Confirm password is required'];
  }
  if (confirmPassword !== password) {
    return ['Passwords do not match'];
  }
  return [];
};

// Form validation
export const validateForm = (formData, validationRules) => {
  const errors = {};
  let isValid = true;

  Object.keys(validationRules).forEach(field => {
    const validationFn = validationRules[field];
    const value = formData[field];
    
    // Special handling for confirm password validation
    const fieldErrors = field === 'confirmPassword' 
      ? validationFn(value, formData.password)
      : validationFn(value);

    if (fieldErrors.length > 0) {
      errors[field] = fieldErrors;
      isValid = false;
    }
  });

  return { isValid, errors };
};

// Gender validation
export const validateGender = (gender) => {
  const errors = [];
  const validGenders = ['male', 'female', 'other', 'prefer not to say'];

  if (!gender) {
    errors.push('Gender is required');
  } else if (!validGenders.includes(gender.toLowerCase())) {
    errors.push('Please select a valid gender option');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

// Bio/About validation
export const validateBio = (bio, { minLength = 10, maxLength = 500 } = {}) => {
  const errors = [];

  if (!bio) {
    errors.push('Bio is required');
  } else {
    if (bio.length < minLength) {
      errors.push(`Bio must be at least ${minLength} characters long`);
    }
    if (bio.length > maxLength) {
      errors.push(`Bio cannot exceed ${maxLength} characters`);
    }
    // Check for basic formatting
    if (/^\s+|\s+$/.test(bio)) {
      errors.push('Bio should not start or end with whitespace');
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    charCount: bio ? bio.length : 0
  };
};

// State validation with basic rules
export const validateState = (state) => {
  const errors = [];

  if (!state) {
    errors.push('State is required');
  } else {
    if (state.length < 2) {
      errors.push('State name is too short');
    }
    if (state.length > 50) {
      errors.push('State name is too long');
    }
    // Allow letters, spaces, and basic punctuation
    const stateRegex = /^[a-zA-Z\s\-'().]+$/;
    if (!stateRegex.test(state)) {
      errors.push('State name can only contain letters, spaces, and basic punctuation');
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

// Highest Qualification validation with basic rules
export const validateQualification = (qualification) => {
  const errors = [];

  if (!qualification) {
    errors.push('Highest qualification is required');
  } else {
    if (qualification.length < 2) {
      errors.push('Qualification is too short');
    }
    if (qualification.length > 100) {
      errors.push('Qualification is too long');
    }
    // Allow letters, numbers, spaces, and basic punctuation
    const qualificationRegex = /^[a-zA-Z0-9\s\-'().,&]+$/;
    if (!qualificationRegex.test(qualification)) {
      errors.push('Qualification can only contain letters, numbers, spaces, and basic punctuation');
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

// Experience validation
export const validateExperience = (experience) => {
  const errors = [];
  
  // For years of experience
  if (typeof experience === 'number') {
    if (experience < 0) {
      errors.push('Experience cannot be negative');
    }
    if (experience > 50) {
      errors.push('Please enter a valid years of experience');
    }
    return {
      isValid: errors.length === 0,
      errors
    };
  }

  // For detailed experience object
  if (typeof experience === 'object') {
    if (!experience.years && !experience.months) {
      errors.push('Please specify years or months of experience');
    }

    if (experience.years) {
      if (!Number.isInteger(experience.years) || experience.years < 0) {
        errors.push('Please enter valid years of experience');
      }
      if (experience.years > 50) {
        errors.push('Years of experience seems too high');
      }
    }

    if (experience.months) {
      if (!Number.isInteger(experience.months) || experience.months < 0) {
        errors.push('Please enter valid months of experience');
      }
      if (experience.months > 11) {
        errors.push('Months should be between 0 and 11');
      }
    }

    if (experience.description) {
      if (experience.description.length < 10) {
        errors.push('Experience description is too short');
      }
      if (experience.description.length > 1000) {
        errors.push('Experience description is too long');
      }
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    totalMonths: experience.years * 12 + (experience.months || 0)
  };
};

// OTP validation
export const validateOTP = (otp) => {
  if (!otp) {
    return ['OTP is required'];
  }
  if (!/^\d{6}$/.test(otp)) {
    return ['OTP must be exactly 6 digits'];
  }
  return [];
};

// Example usage:
/*
const formData = {
  name: 'John Doe',
  email: '<EMAIL>',
  password: 'Password123!',
  phone: '1234567890',
  address: {
    street: '123 Main St',
    city: 'New York',
    state: 'NY',
    zipCode: '10001'
  },
  profession: 'Software Engineer'
};

const validationRules = {
  name: validateName,
  email: validateEmail,
  password: validatePassword,
  phone: (value) => validatePhone(value, '+1'),
  'address.street': validateAddress.street,
  'address.city': validateAddress.city,
  'address.state': validateAddress.state,
  'address.zipCode': (value) => validateAddress.zipCode(value, 'US'),
  profession: validateProfession
};

const result = validateForm(formData, validationRules);
console.log(result);
*/
