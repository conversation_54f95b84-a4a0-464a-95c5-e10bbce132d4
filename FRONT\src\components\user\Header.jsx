import React, { useEffect, useRef, useState } from 'react';
import { logoutApi } from '../../services/authService';
import { Link, useNavigate } from 'react-router-dom';
import { Icon } from '@iconify/react';
import DefaultProfile from '../../assets/images/profile/default-profile.png'
import { getProfile, newGetNotifications } from '../../services/userService';
import { useNotification } from '../../context/NotificationContext';

function Header() {
  const navigate = useNavigate();
  const dropdownRef = useRef(null);
  const [isMobile, setIsMobile] = useState(window.innerWidth < 768);
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [profileData, setProfileData] = useState(null);
  const { unreadCount, updateUnreadCount } = useNotification();

  const fetchNotifications = async () => {
    try {
      const response = await newGetNotifications({});
      console.log('Notifications response:', response);
      if (response && response.notifications) {
        // Count unread notifications
        const unreadCount = response.notifications.filter(notification => notification.is_read === 0).length;
        updateUnreadCount(unreadCount);
      }
    } catch (error) {
      console.error('Error fetching notifications:', error);
    }
  };


  useEffect(() => {
    fetchProfile();
    fetchNotifications(); // Add this line to fetch notifications on component mount
  }, []);

  // Add this effect to periodically check for new notifications
  useEffect(() => {
    const interval = setInterval(() => {
      fetchNotifications();
    }, 30000); // Check every 30 seconds

    return () => clearInterval(interval);
  }, []);

  const fetchProfile = async () => {
    try {
      const response = await getProfile();
      // console.log('Profile response header************:', response);
      setProfileData(response.data);
    } catch (error) {
      console.error('Error fetching profile:', error);
    }
  };

  useEffect(() => {
    // Handle window resize
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
    };

    // Set initial state
    handleResize();

    // Add event listener
    window.addEventListener('resize', handleResize);

    // Cleanup
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  useEffect(() => {
    // Initialize dropdown with hover functionality
    const dropdownElement = dropdownRef.current;

    if (dropdownElement) {
      const dropdownMenu = dropdownElement.querySelector('.dropdown-menu');
      const dropdownButton = dropdownElement.querySelector('button');

      // Keep track if user is over the dropdown button or menu
      let isOverDropdown = false;
      let isOverMenu = false;

      const handleButtonMouseEnter = () => {
        isOverDropdown = true;
        dropdownMenu.classList.add('show');
      };

      const handleButtonMouseLeave = () => {
        isOverDropdown = false;

        // Only hide if not over menu either
        setTimeout(() => {
          if (!isOverDropdown && !isOverMenu) {
            dropdownMenu.classList.remove('show');
          }
        }, 200);
      };

      const handleMenuMouseEnter = () => {
        isOverMenu = true;
      };

      const handleMenuMouseLeave = () => {
        isOverMenu = false;

        // Only hide if not over button either
        setTimeout(() => {
          if (!isOverDropdown && !isOverMenu) {
            dropdownMenu.classList.remove('show');
          }
        }, 200);
      };

      // Add event listeners
      dropdownButton.addEventListener('mouseenter', handleButtonMouseEnter);
      dropdownButton.addEventListener('mouseleave', handleButtonMouseLeave);
      dropdownMenu.addEventListener('mouseenter', handleMenuMouseEnter);
      dropdownMenu.addEventListener('mouseleave', handleMenuMouseLeave);

      // Cleanup event listeners on component unmount
      return () => {
        dropdownButton.removeEventListener('mouseenter', handleButtonMouseEnter);
        dropdownButton.removeEventListener('mouseleave', handleButtonMouseLeave);
        dropdownMenu.removeEventListener('mouseenter', handleMenuMouseEnter);
        dropdownMenu.removeEventListener('mouseleave', handleMenuMouseLeave);
      };
    }
  }, []);


  const handleLogout = async () => {
    try {
      // Optionally call logout API
      await logoutApi();

      // Clear all local storage items
      localStorage.clear();

      // Redirect to login page
      navigate('/auth/login');
    } catch (error) {
      console.error("Logout error:", error);
      localStorage.clear();
      navigate('/auth/login');
    }
  };


  // Toggle dropdown for mobile view
  const toggleDropdown = () => {
    if (isMobile) {
      setDropdownOpen(!dropdownOpen);
    }
  };

  return (
    <header className={`header-container  ${isMobile ? 'p-0' : ''}`}  >
      <div className="d-flex justify-content-end align-items-center">
        {/* Notification Bell */}
        <div className="notification-bell-container me-3">
          <Link to="/user/notifications" className="notification-bell position-relative d-flex align-items-center">
            <Icon
              icon="heroicons-outline:bell"
              className="text-primary"
              width="24"
              height="24"
            />
            {unreadCount > 0 && (
              <span className="position-absolute top-0 start-100 translate-middle badge border border-danger text-danger bg-danger-subtle rounded-circle d-flex align-items-center justify-content-center p-1"
                style={{ minWidth: '20px', height: '20px', fontSize: '11px', transform: 'translate(-50%, -50%)' }}>
                {unreadCount > 99 ? '99+' : unreadCount}
              </span>
            )}
          </Link>
        </div>

        {/* User Profile Dropdown */}
        <div className="dropdown user-dropdown" ref={dropdownRef}>
          <button
            className="d-flex align-items-center"
            type="button"
            id="userDropdown"
            onClick={toggleDropdown}
          >
            <img
              src={profileData?.profile_pic_url || DefaultProfile}
              alt="User"
              className="rounded-circle me-md-2"
              width="32"
              height="32"
            />
            <span className="me-2 user-name" >{profileData?.name || 'User'}</span>
            <Icon icon="mdi:chevron-down" width="18" height="18" />
          </button>
          <ul className={`dropdown-menu dropdown-menu-end shadow-sm ${isMobile && dropdownOpen ? 'show' : ''}`} aria-labelledby="userDropdown">
            <li>
              <Link to="/user/profile" className="dropdown-item d-flex align-items-center">
                <Icon icon="fluent:person-24-filled" className="me-2" width="18" />
                <span>Profile</span>
              </Link>
            </li>
            <li>
              <Link to="/user/settings" className="dropdown-item d-flex align-items-center">
                <Icon icon="fluent:settings-24-filled" className="me-2" width="18" />
                <span>Settings</span>
              </Link>
            </li>
            <li><hr className="dropdown-divider" /></li>

            <div
              className="nav-item d-flex align-items-center py-2 px-3 text-danger mt-2"
              onClick={handleLogout}
              role="button"
              style={{ cursor: 'pointer' }}
            >
              <Link to="/user/logout" className="dropdown-item text-danger d-flex align-items-center">
                <Icon icon="fluent:sign-out-24-filled" className="me-2" width="18" />
                <span>Logout</span>
              </Link>
            </div>
          </ul>
        </div>
      </div>
    </header>
  );
}

export default Header;