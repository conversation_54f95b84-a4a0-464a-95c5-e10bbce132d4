const { mysqlServerConnection } = require("../../../db/db");


const getClassroomResourcesForTrainees = async (req, res) => {
  try {
    // Extract query parameters with default values
    let { page = 1, limit = 10, search = '', id, resource_type } = req.body;
    page = parseInt(page) || 1;
    limit = parseInt(limit) || 10;
    const offset = (page - 1) * limit;
    const dbName = req.user.db_name;

    // Log initial parameters from the request body
    console.log('Request body parameters:', { page, limit, search, id, resource_type });

    // Initial where condition and parameters array
    let where = 'is_active = 1 AND is_deleted = 0';
    let params = [];

    // Debugging: log initial query filters
    console.log('Initial WHERE clause:', where);
    console.log('Initial parameters:', params);

    // Add id filter if provided
    if (id) {
      where += ' AND id = ?';
      params.push(id);
      console.log('Added id filter:', { where, params });
    }

    // Add resource_type filter if provided
    if (resource_type) {
      where += ' AND resource_type = ?';
      params.push(resource_type);
      console.log('Added resource_type filter:', { where, params });
    }

    // Add search filter if provided
    if (search) {
      where += ' AND (resource_name LIKE ? OR resource_description LIKE ?)';
      params.push(`%${search}%`, `%${search}%`);
      console.log('Added search filter:', { where, params });
    }

    // Log the final constructed WHERE clause and params before querying
    console.log('Final WHERE clause before COUNT query:', where);
    console.log('Final parameters before COUNT query:', params);

    // Get total count for pagination
    const [countRows] = await mysqlServerConnection.query(
      `SELECT COUNT(*) as total FROM ${dbName}.classroom_resources WHERE ${where}`,
      params
    );

    // Debugging: log the total count result
    console.log('Total count result from COUNT query:', countRows);

    const total = countRows[0]?.total || 0;

    // Log total before fetching paginated data
    console.log('Total resources available:', total);

    // Get the actual data with pagination
    const [rows] = await mysqlServerConnection.query(
      `SELECT * FROM ${dbName}.classroom_resources WHERE ${where} ORDER BY created_at DESC LIMIT ? OFFSET ?`,
      [...params, limit, offset]
    );

    // Debugging: log the paginated result
    console.log('Paginated data result:', rows);

    // Log final response object
    const pagination = {
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit)
    };
    console.log('Pagination information:', pagination);

    // Send the response with paginated data and pagination info
    return res.status(200).json({
      success: true,
      data: rows,
      pagination
    });
  } catch (error) {
    // Error handling and debugging
    console.error('Error in getClassroomResourcesForTrainees:', error);
    return res.status(500).json({ success: false, message: 'Server error', error: error.message });
  }
};


module.exports = {
    getClassroomResourcesForTrainees,
  };










