.upload-area {
    border: 2px dashed #dee2e6;
    border-radius: 8px;
    padding: 2rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background-color: #fff;
}

.upload-area:hover {
    border-color: #0d6efd;
    background-color: #f8f9fa;
}

.upload-text {
    color: #0d6efd;
    font-size: 1rem;
    margin-bottom: 0.5rem;
}

.upload-hint {
    color: #6c757d;
    font-size: 0.875rem;
}

.selected-trainer {
    background-color: #f8f9fa;
    border-radius: 6px;
    padding: 8px 12px;
    margin: 4px;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.remove-trainer {
    background: none;
    border: none;
    padding: 0;
    color: #dc3545;
    cursor: pointer;
    display: flex;
    align-items: center;
}

.remove-trainer:hover {
    color: #bb2d3b;
}

.trainers-container {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 10px;
}

/* Trainer Cards Styles */
.trainer-card {
  background: #fff;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.trainer-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.trainer-avatar img,
.trainer-avatar div {
  border: 2px solid #e9ecef;
  transition: all 0.3s ease;
}

.trainer-card:hover .trainer-avatar img,
.trainer-card:hover .trainer-avatar div {
  border-color: #0d6efd;
}

.trainer-details a {
  color: #495057;
  transition: color 0.2s ease;
}

.trainer-details a:hover {
  color: #0d6efd;
}

.badge.bg-primary-subtle {
  background-color: #e7f1ff !important;
  border: 1px solid #b6d4fe;
}

/* Trainers Dropdown Styles */
.trainers-dropdown .collapse {
  transition: all 0.3s ease-in-out;
}

.trainers-dropdown .card-header {
  transition: background-color 0.2s ease;
}

.trainers-dropdown .card-header:hover {
  background-color: #f8f9fa !important;
}

.trainers-dropdown .card-header Icon {
  transition: transform 0.3s ease;
}

.trainers-dropdown .collapse.show + .card-header Icon {
  transform: rotate(180deg);
}

.tab-settings-dropdown {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e0e0e0;
}

.tab-settings-dropdown label {
  cursor: pointer;
  padding: 6px 8px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.tab-settings-dropdown label:hover {
  background-color: #f5f5f5;
}

.tab-settings-dropdown input[type="checkbox"] {
  cursor: pointer;
}

/* Add overlay to close dropdown when clicking outside */
.tab-settings-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
}

/* Animation for dropdown */
.tab-settings-dropdown {
  animation: fadeIn 0.2s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Assignment Status Badge Animations */
@keyframes pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(30, 142, 62, 0.7);
  }
  
  70% {
    transform: scale(1);
    box-shadow: 0 0 0 6px rgba(30, 142, 62, 0);
  }
  
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(30, 142, 62, 0);
  }
}

@keyframes dotPulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  
  50% {
    transform: scale(1.2);
    opacity: 0.8;
  }
  
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.assignment-status {
  padding: 8px 16px;
  font-size: 12px;
  font-weight: 500;
  /* border-radius: 20px; */
  display: flex;
  align-items: center;
  min-width: 140px;
}

.assignment-status.ongoing {
  background-color: #e6f4ea;
  color: #1e8e3e;
  border: 1px solid #1e8e3e;
}

.assignment-status.ended {
  background-color: #fce8e8;
  color: #dc3545;
  border: 1px solid #dc3545;
}

.pulse-container {
  position: relative;
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-dot {
  width: 8px;
  height: 8px;
  background-color: #1e8e3e;
  border-radius: 50%;
  display: inline-block;
  animation: dotPulse 2s infinite ease-in-out;
  position: relative;
}

.status-dot::after {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

/* Assessment Status Styles */
.assessment-status {
  padding: 6px 12px;
  font-size: 12px;
  font-weight: 500;
  border-radius: 4px;
  display: flex;
  align-items: center;
  gap: 6px;
}

.assessment-status.ongoing {
  background-color: #e6f4ea;
  color: #1e8e3e;
  border: 1px solid #1e8e3e;
}

.assessment-status.ended {
  background-color: #fce8e8;
  color: #d32f2f;
  border: 1px solid #d32f2f;
}

.pulse-container {
  display: flex;
  align-items: center;
  gap: 6px;
}

.status-dot {
  width: 6px;
  height: 6px;
  background-color: #1e8e3e;
  border-radius: 50%;
  display: inline-block;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.5);
    opacity: 0.5;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Disabled tab styles */
.nav-tabs .nav-link.disabled,
.nav-tabs button.disabled,
.nav-tabs button[disabled] {
  cursor: not-allowed !important;
  pointer-events: all !important;
  opacity: 0.6;
}

