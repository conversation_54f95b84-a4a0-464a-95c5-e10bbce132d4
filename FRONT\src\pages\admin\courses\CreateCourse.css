
.stepper-card {
    background: white;
    border-radius: 8px;
    padding: 24px;
    margin-bottom: 24px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.stepper-wrapper {
    padding: 20px 0;
    position: relative;
    max-width: 100%;
    overflow: hidden;
}

.stepper-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    padding: 0 20px;
}

.stepper-line-bg {
    position: absolute;
    top: 40px;
    left: 50px;
    right: 50px;
    height: 2px;
    background-color: #e9ecef;
    z-index: 1;
}

.stepper-line-progress {
    position: absolute;
    top: 40px;
    left: 50px;
    height: 2px;
    background-color: #0d6efd;
    z-index: 1;
    transition: width 0.3s ease-in-out;
}

.stepper-item {
    position: relative;
    z-index: 2;
    flex: 1;
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    min-width: 0;
    max-width: 20%;
}

.stepper-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 8px;
    transition: all 0.3s ease;
    font-weight: 500;
}

.stepper-circle.active {
    background-color: #0d6efd;
    color: white;
}

.stepper-circle.completed {
    background-color: #0d6efd;
    color: white;
}

.stepper-label {
    font-size: 12px;
    color: #6c757d;
    text-align: center;
    line-height: 1.2;
    word-wrap: break-word;
    max-width: 100%;
}

.stepper-label.active {
    color: #0d6efd;
    font-weight: 500;
}

/* Responsive adjustments for smaller screens */
@media (max-width: 768px) {
    .stepper-label {
        font-size: 10px;
    }

    .stepper-circle {
        width: 32px;
        height: 32px;
        font-size: 12px;
    }

    .stepper-content {
        padding: 0 10px;
    }

    .stepper-line-bg {
        left: 40px;
        right: 40px;
        top: 36px;
    }

    .stepper-line-progress {
        left: 40px;
        top: 36px;
    }
}

.btn-previous,
.btn-next {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 8px 16px;
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
    height: 40px;
    min-width: 120px;
    border: none;
}

.btn-previous {
    background-color: transparent;
    border: 1px solid #0d6efd;
    color: #0d6efd;
}

.btn-previous:hover:not(:disabled) {
    background-color: #f8f9fa;
}

.btn-next {
    background-color: #0d6efd;
    color: white;
}

.btn-next:hover:not(:disabled) {
    background-color: #0b5ed7;
}

.btn-previous:disabled,
.btn-next:disabled {
    opacity: 0.6;
    cursor: not-allowed;
} 