import React, { useState, useEffect } from 'react';
import NoData from '../../../components/common/NoData';
import Loader from '../../../components/common/Loader';
import { Icon } from '@iconify/react';
import { getTraineeAnalytics } from '../../../services/traineeService';
import { toast } from 'react-toastify';
import { Modal } from 'react-bootstrap';

function TraineeAnalyticsTicketsTab({ traineeId }) {
  const [loading, setLoading] = useState(false);
  const [ticketsData, setTicketsData] = useState([]);
  const [showModal, setShowModal] = useState(false);
  const [selectedTicket, setSelectedTicket] = useState(null);

  const fetchTicketsData = async () => {
    try {
      setLoading(true);
      const response = await getTraineeAnalytics({ trainee_id: traineeId });
      console.log('Tickets Data---------------------:', response.data.tickets);

      if (response.success) {
        setTicketsData(response.data.tickets || []);
      } else {
        toast.error(response.message || 'Failed to fetch tickets data');
      }
    } catch (error) {
      console.error('Error fetching tickets data:', error);
      toast.error('Something went wrong while fetching tickets data');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (traineeId) {
      fetchTicketsData();
    }
  }, [traineeId]);

  const getStatusBadgeClass = (status) => {
    switch (status.toLowerCase()) {
      case 'solved':
        return 'bg-success';
      case 'pending':
        return 'bg-warning';
      case 'in progress':
        return 'bg-info';
      case 'cancelled':
        return 'bg-danger';
      default:
        return 'bg-secondary';
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const handleShowModal = (ticket) => {
    setSelectedTicket(ticket);
    setShowModal(true);
  };

  const handleCloseModal = () => {
    setSelectedTicket(null);
    setShowModal(false);
  };

  if (loading) {
    return (
      <div className="d-flex justify-content-center align-items-center" style={{ minHeight: '200px' }}>
        <Loader />
      </div>
    );
  }

  if (!ticketsData || ticketsData.length === 0) {
    return <NoData message="No tickets found" />;
  }

  return (
    <>
      <div className="card">
        <div className="table-responsive">
          <table className="table table-borderless mb-0">
            <thead>
              <tr>
                <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500', width: '80px' }}>SL No.</th>
                <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500', width: '150px' }}>Ticket ID</th>
                <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500',  width: '200px' }}>Subject</th>
                <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500',  width: '200px' }}>Response</th>
                <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500', width: '200px' }}>Created Date</th>
                <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500', width: '120px' }}>Status</th>
                <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500', width: '100px' }}>Actions</th>
              </tr>
            </thead>
            <tbody>
              {ticketsData.map((ticket, index) => (
                <tr key={ticket.id} className="border-bottom">
                  <td className="py-3 align-middle">{index + 1}</td>
                  <td className="py-3 align-middle">
                    <span className="fw-medium">{ticket.ticket_id}</span>
                  </td>
                  <td className="py-3 align-middle text-truncate" style={{ maxWidth: '200px' }}>
                    <span className="fw-semibold">{ticket.subject}</span>
                  </td>
                  <td className="py-3 align-middle">
                    {ticket.response ? (
                      <div className="text-muted text-truncate" style={{ maxWidth: '200px' }} >
                        {ticket.response}
                      </div>
                    ) : (
                      <span className="text-warning">Not responded yet</span>
                    )}
                  </td>
                  <td className="py-3 align-middle">{formatDate(ticket.createdAt)}</td>
                  <td className="py-3 align-middle">
                    <span className={`badge ${getStatusBadgeClass(ticket.status)}`}>
                      {ticket.status}
                    </span>
                  </td>
                  <td className="py-3 align-middle">
                    <button
                      className="btn btn-outline-dark btn-sm border border-dark"
                      style={{ width: '36px', height: '36px' }}
                      title="View Details"
                      onClick={() => handleShowModal(ticket)}
                    >
                      <Icon 
                        icon="fluent:eye-24-regular"
                        width="16" 
                        height="16" 
                      />
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Ticket Details Modal */}
      <Modal show={showModal} onHide={handleCloseModal}>
        <Modal.Header closeButton className="border-bottom">
          <Modal.Title className="d-flex align-items-center gap-2">
            Ticket Details
            {selectedTicket && (
              <span className={`badge ${getStatusBadgeClass(selectedTicket.status)}`}>
                {selectedTicket.status}
              </span>
            )}
          </Modal.Title>
        </Modal.Header>
        <Modal.Body className="p-0">
          {selectedTicket && (
            <div className="px-4">
              <div className="border-bottom py-3">
                <div className="row mb-2">
                  <div className="col-4">
                    <div className="text-muted">Ticket ID</div>
                  </div>
                  <div className="col-8">
                    <div className="fw-medium">{selectedTicket.ticket_id}</div>
                  </div>
                </div>
                <div className="row mb-2">
                  <div className="col-4">
                    <div className="text-muted">Created Date</div>
                  </div>
                  <div className="col-8">
                    <div>{formatDate(selectedTicket.createdAt)}</div>
                  </div>
                </div>
                {selectedTicket.updatedAt !== selectedTicket.createdAt && (
                  <div className="row">
                    <div className="col-4">
                      <div className="text-muted">Last Updated</div>
                    </div>
                    <div className="col-8">
                      <div>{formatDate(selectedTicket.updatedAt)}</div>
                    </div>
                  </div>
                )}
              </div>

              <div className="border-bottom py-3">
                <div className="text-muted mb-2">Subject</div>
                <div>{selectedTicket.subject}</div>
              </div>

              <div className="border-bottom py-3">
                <div className="text-muted mb-2">Description</div>
                <div style={{ whiteSpace: 'pre-wrap' }}>{selectedTicket.description}</div>
              </div>

              <div className="py-3">
                <div className="text-muted mb-2 text-truncate">Response</div>
                {selectedTicket.response ? (
                  <div style={{ whiteSpace: 'pre-wrap' }}>{selectedTicket.response}</div>
                ) : (
                  <div className="text-warning">Not responded yet</div>
                )}
              </div>

              {selectedTicket.file_url && (
                <div className="border-top py-3">
                  <div className="text-muted mb-2">Attachment</div>
                  <a 
                    href={selectedTicket.file_url} 
                    target="_blank" 
                    rel="noopener noreferrer" 
                    className="btn btn-outline-primary btn-sm"
                  >
                    <Icon icon="fluent:document-24-regular" className="me-2" />
                    View Attachment
                  </a>
                </div>
              )}
            </div>
          )}
        </Modal.Body>
        <Modal.Footer className="border-top">
          <button className="btn btn-light" onClick={handleCloseModal}>
            Close
          </button>
        </Modal.Footer>
      </Modal>
    </>
  );
}

export default TraineeAnalyticsTicketsTab; 