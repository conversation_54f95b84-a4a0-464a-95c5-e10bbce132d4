import React, { useState, useEffect } from 'react';
import { Icon } from '@iconify/react';
import { toast } from 'react-toastify';
import './CourseNotes.css';
import { getNotes, addNote, deleteNotes, updateNote , updateComment} from '../../../../services/userService';

function CourseNotes({ contentData, player }) {
  const [notes, setNotes] = useState([]);
  const [showInput, setShowInput] = useState(false);
  const [noteText, setNoteText] = useState('');
  const [editingNoteId, setEditingNoteId] = useState(null);

  const moduleId = contentData?.module_id;
  const videoId = contentData?.id;

  useEffect(() => {
    if (videoId) {
      getNotesData();
    }
  }, [moduleId]);

  async function getNotesData() {
    try {
      const response = await getNotes(videoId);
      console.log('Notes response:', response);
      setNotes(response.data);
    } catch (error) {
      console.error('Error fetching notes:', error);
    }
  }

  if (!moduleId || !videoId) {
    return <div className="no-module-message">No content selected</div>;
  }

  const handleAddNote = () => {
    setShowInput(true);
    setEditingNoteId(null);
    setNoteText('');
  };

  const formatTime = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  // Function to parse time string (mm:ss) to seconds
  const parseTimeToSeconds = (timeString) => {
    if (!timeString) return 0;
    const [minutes, seconds] = timeString.split(':').map(Number);
    return (minutes * 60) + seconds;
  };

  // Function to handle timestamp click and seek to that time
  const handleTimestampClick = (timeString) => {
    if (player && timeString) {
      try {
        const timeInSeconds = parseTimeToSeconds(timeString);
        player.seek(timeInSeconds);
        console.log(`Seeking to ${timeString} (${timeInSeconds} seconds)`);
      } catch (error) {
        console.error('Error seeking to timestamp:', error);
      }
    }
  };

  // Function to format date safely
  const formatDate = (dateString) => {
    if (!dateString) return '';
    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) return '';
      return date.toLocaleDateString();
    } catch (error) {
      console.error('Error formatting date:', error);
      return '';
    }
  };

  const handleSaveNote = async () => {
    if (noteText.trim()) {
      try {
        const currentTime = player ? Math.floor(player.getCurrentTime()) : 0;
        const formattedTime = formatTime(currentTime);

        
        if (editingNoteId) {
          // Handle edit note
          console.log('formattedTime-------------------------', editingNoteId);
          console.log('formattedTime-------------------------', noteText);

          const response = await updateNote({
            noteId : editingNoteId,
            note : noteText, 
            // id: editingNoteId,
            // note: noteText,
            // video_id: videoId,
            // module_id: moduleId,
            // stream_time: formattedTime
          });
          
          if (response.success) {
            await getNotesData();
          }
        } else {
          // Add new note
          const response = await addNote({
            note: noteText,
            video_id: videoId,
            module_id: moduleId,
            stream_time: formattedTime
          });
          
          if (response.success) {
            await getNotesData();
          }
        }
        
        setNoteText('');
        setShowInput(false);
        setEditingNoteId(null);
      } catch (error) {
        console.error('Error saving note:', error);
        alert('Failed to save note. Please try again.');
      }
    }
  };

  const handleDeleteNote = async (noteId) => {
    try {
      const response = await deleteNotes(noteId);
      if (response.success) {
        // Immediately remove the note from local state
        setNotes(prevNotes => prevNotes.filter(note => note.id !== noteId));
        // Show success toast
        toast.success('Note deleted successfully');
        // Then refresh from server to ensure sync
        await getNotesData();
      } else {
        toast.error('Failed to delete note');
      }
    } catch (error) {
      console.error('Error deleting note:', error);
      toast.error('Failed to delete note');
    }
  };

  return (
    <div className="course-notes-container">
<div className="notes-header row">
  <div className="col-8 col-md-3 ms-auto">
    <button className="btn btn-primary add-note-btn w-100" onClick={handleAddNote}>
      <Icon icon="mdi:plus" className="me-2" />
      Add Note
    </button>
  </div>
</div>


      {showInput && (
        <div className="note-input-container">
          <textarea
            className="note-input"
            placeholder="Make a Note"
            value={noteText}
            onChange={(e) => setNoteText(e.target.value)}
          ></textarea>
          <div className="note-actions">
            <button className="btn btn-secondary" onClick={() => {
              setNoteText('');
              setShowInput(false);
              setEditingNoteId(null);
            }}>
              Cancel
            </button>
            <button className="btn btn-primary" onClick={handleSaveNote}>
              {editingNoteId ? 'Update' : 'Save'}
            </button>
          </div>
        </div>
      )}

      <div className="notes-list">
        {notes.length > 0 ? (
          notes.map(note => (
            <div key={note.id} className="note-item">
              <div className="note-header">
                <div className="note-meta">
                  <span
                    className="note-timestamp clickable-timestamp"
                    onClick={() => handleTimestampClick(note.stream_time)}
                    title="Click to jump to this time in the video"
                  >
                    {note.stream_time || '0:00'}
                  </span>
                  {formatDate(note.created_at) && (
                    <span className="note-date">{formatDate(note.created_at)}</span>
                  )}
                  {note.updated_at && note.updated_at !== note.created_at && formatDate(note.updated_at) && (
                    <span className="note-edited">(edited {formatDate(note.updated_at)})</span>
                  )}
                </div>
                <div className="note-actions">
                  <button
                 className="btn btn-link text-dark p-0 ms-3"
                    onClick={() => {
                      setNoteText(note.note);
                      setEditingNoteId(note.id);
                      setShowInput(true);
                    }}
                    title="Edit note"
                    aria-label="Edit note"
                  >
                    <Icon icon="mdi:pencil" className="edit-icon" />
                  </button>

                  <button
                 className="btn btn-link text-danger p-0 ms-3"
                    onClick={() => handleDeleteNote(note.id)}
                    title="Delete note"
                    aria-label="Delete note"
                  >
                    <Icon icon="mdi:delete" className="delete-icon" />
                  </button>
                </div>
              </div>
              <p className="note-text">{note.note}</p>
            </div>
          ))
        ) : (
          <div className="empty-notes">
            <p>No notes yet. Click "Add Note" to create one.</p>
          </div>
        )}
      </div>
    </div>
  );
}

export default CourseNotes;
