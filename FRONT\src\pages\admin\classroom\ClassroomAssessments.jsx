import React, { useState, useEffect, useCallback } from 'react'
import { Icon } from '@iconify/react';
import { useNavigate, useParams } from 'react-router-dom';
import { Modal } from 'react-bootstrap';
import { toast } from 'react-toastify';
import moment from 'moment-timezone';
import Loader from '../../../components/common/Loader';
import NoData from '../../../components/common/NoData';
import { getClassroomDetails, getAllAssessmentToClassroom, addAssessmentToClassroom, removeAssessmentFromClassroom, updateAssessmentInClassroom, copyAssessment } from '../../../services/adminService';
import { decodeData } from '../../../utils/encodeAndEncode';
import { encodeData } from '../../../utils/encodeAndEncode';
import timeZones from '../../../utils/timeZone.json';
import { usePermissions } from '../../../context/PermissionsContext';
const DefaultTimeZone = process.env.REACT_APP_DEFAULT_TIMEZONE;

function ClassroomAssessment() {
  const navigate = useNavigate();
  const { permissions } = usePermissions();
  const { classroomId: encodedClassroomId } = useParams();
  const classroomId = decodeData(encodedClassroomId);

  // State management
  const [loading, setLoading] = useState(false);
  const [assessments, setAssessments] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    totalPages: 1,
    totalRecords: 0
  });

  // Modal states
  const [showModal, setShowModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showCopyModal, setShowCopyModal] = useState(false);

  // Form states
  const [formData, setFormData] = useState({
    assessmentName: '',
    isTimeBased: false,
    status: 'Inactive',
    duration: '',
    passingPercentage: '35',
    opensAt: '',
    closesAt: '',
    visibility: 'visible',
    timeZone: DefaultTimeZone // Add timezone with default value
  });

  // Update editFormData state
  const [editFormData, setEditFormData] = useState({
    assessmentId: '',
    assessmentName: '',
    isTimeBased: false,
    status: 'Inactive',
    duration: '',
    passingPercentage: '35',
    opensAt: '',
    closesAt: '',
    visibility: 'hidden',
    timeZone: DefaultTimeZone // Add timezone to edit form
  });

  // Loading states
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [isCopying, setIsCopying] = useState(false);

  // Error states
  const [formErrors, setFormErrors] = useState({});

  // Delete and copy states
  const [selectedAssessment, setSelectedAssessment] = useState(null);
  const [availableClassrooms, setAvailableClassrooms] = useState([]);
  const [selectedClassrooms, setSelectedClassrooms] = useState([]);
  const [classroomSearchTerm, setClassroomSearchTerm] = useState('');

  // Add new states for timezone
  const [timeZoneSearch, setTimeZoneSearch] = useState('');
  const [isTimeZoneDropdownOpen, setIsTimeZoneDropdownOpen] = useState(false);

  // Fetch assessments from API
  const fetchAssessments = useCallback(async (page = 1, search = '') => {
    if (!classroomId) {
      toast.error("Classroom ID is missing");
      return;
    }

    setLoading(true);
    try {
      const payload = {
        class_id: classroomId,
        page: page,
        limit: pagination.limit,
        search: search,
        startDate: "",
        endDate: ""
      };

      const response = await getAllAssessmentToClassroom(payload);
      console.log('response------------------------------------------', response);

      if (response.success) {
        // Transform API data to match our component's expected format
        const transformedAssessments = response.assessments.map(assessment => ({
          id: assessment.id,
          title: assessment.assessment_name,
          status: assessment.is_active === 1 ? "Active" : "Inactive",
          is_active: assessment.is_active,
          is_visible: assessment.is_visible,
          duration: `${assessment.duration} mins`,
          passingPercentage: `${assessment.pass_percentage}%`,
          opensAt: assessment.activate_time,
          closesAt: assessment.deactivate_time,
          manualActivation: assessment.is_time_based === 0,
          isTimeBased: assessment.is_time_based === 1,
          stats: {
            passingPercentage: `${assessment.pass_percentage}%`,
            attended: assessment.completed_count || 0,
            noOfDue: assessment.total_questions || 0
          },
          // Store raw values for edit form
          activate_time: assessment.activate_time,
          deactivate_time: assessment.deactivate_time,
          pass_percentage: assessment.pass_percentage,
          raw_duration: assessment.duration,
          raw_time_zone: assessment.time_zone // Add raw timezone
        }));

        setAssessments(transformedAssessments);
        setPagination({
          page: response.pagination.page,
          limit: response.pagination.limit,
          totalPages: response.pagination.totalPages,
          totalRecords: response.pagination.totalRecords
        });
      } else {
        toast.error(response.message || "Failed to fetch assessments");
      }
    } catch (error) {
      console.error("Error fetching assessments:", error);
      toast.error("An error occurred while fetching assessments");
    } finally {
      setLoading(false);
    }
  }, [classroomId, pagination.limit]);

  // Fetch available classrooms for copy functionality
  const fetchAvailableClassrooms = useCallback(async () => {
    try {
      const userDetails = JSON.parse(localStorage.getItem("user"));
      const user_id = userDetails?.id;

      const payload = {
        page: 1,
        limit: 10000,
        search: "",
        user_id: user_id
      };

      const response = await getClassroomDetails(payload);

      if (response && response.data && response.data.classes) {
        const formattedClassrooms = response.data.classes
          .filter(cls => cls.id !== classroomId) // Exclude current classroom
          .map(cls => ({
            id: cls.id,
            name: cls.cls_name
          }));
        setAvailableClassrooms(formattedClassrooms);
      } else {
        toast.error("Failed to fetch classrooms for copying");
        setAvailableClassrooms([]);
      }
    } catch (error) {
      console.error("Error fetching classroom details:", error);
      toast.error("An error occurred while fetching classrooms.");
      setAvailableClassrooms([]);
    }
  }, [classroomId]);

  // Initialize data on component mount
  useEffect(() => {
    if (classroomId) {
      fetchAssessments(1, '');
      fetchAvailableClassrooms();
    }
  }, [classroomId, fetchAssessments, fetchAvailableClassrooms]);

  // Handle search
  const handleSearch = (e) => {
    const value = e.target.value;
    setSearchTerm(value);
    fetchAssessments(1, value);
  };

  // Clear form errors when user types
  const clearFieldError = (field) => {
    if (formErrors[field]) {
      setFormErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  };

  // Format date for datetime-local input
  const formatDateForInput = (dateString) => {
    if (!dateString) return '';
    try {
      const date = new Date(dateString);
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      return `${year}-${month}-${day}T${hours}:${minutes}`;
    } catch (error) {
      console.error("Error formatting date:", error);
      return '';
    }
  };

  // Format date to API format with timezone
  const formatToLocalString = (dateTimeString) => {
    if (!dateTimeString) return "";
    const localDate = new Date(dateTimeString);

    const year = localDate.getFullYear();
    const month = String(localDate.getMonth() + 1).padStart(2, '0');
    const day = String(localDate.getDate()).padStart(2, '0');
    const hours = String(localDate.getHours()).padStart(2, '0');
    const minutes = String(localDate.getMinutes()).padStart(2, '0');
    const seconds = String(localDate.getSeconds()).padStart(2, '0');

    const tzOffset = -localDate.getTimezoneOffset();
    const tzOffsetHours = Math.floor(Math.abs(tzOffset) / 60);
    const tzOffsetMinutes = Math.abs(tzOffset) % 60;
    const tzOffsetSign = tzOffset >= 0 ? '+' : '-';
    const tzOffsetString = `${tzOffsetSign}${String(tzOffsetHours).padStart(2, '0')}:${String(tzOffsetMinutes).padStart(2, '0')}`;

    return `${year}-${month}-${day}T${hours}:${minutes}:${seconds}${tzOffsetString}`;
  };

  function HandleAssessmentManage(assessmentId) {
    if (!permissions.classroom_assessment_manage) {
      toast.warning("You don't have permission to manage assessments");
      return;
    }
    const encodedClassroomId = encodeData(classroomId);
    const encodedAssessmentId = encodeData(assessmentId);
    navigate(`/admin/classrooms/classroom-dashboard/${encodedClassroomId}/assessment/${encodedAssessmentId}/ClassroomAssessmentDetails`);
  }

  // Form input handlers
  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
    clearFieldError(name);
  };

  const handleEditInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setEditFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
    clearFieldError(name);
  };

  // Modal handlers
  const handleModalClose = () => {
    setShowModal(false);
    setFormErrors({});
    setFormData({
      assessmentName: '',
      isTimeBased: false,
      status: 'Inactive',
      duration: '',
      passingPercentage: '35',
      opensAt: '',
      closesAt: '',
      visibility: 'visible',
      timeZone: DefaultTimeZone // Also update reset state to visible
    });
  };

  const handleEditModalClose = () => {
    setShowEditModal(false);
    setFormErrors({});
    setEditFormData({
      assessmentId: '',
      assessmentName: '',
      isTimeBased: false,
      status: 'Inactive',
      duration: '',
      passingPercentage: '35',
      opensAt: '',
      closesAt: '',
      visibility: 'hidden',
      timeZone: DefaultTimeZone
    });
  };

  const handleDeleteModalClose = () => {
    setShowDeleteModal(false);
    setSelectedAssessment(null);
  };

  const handleCopyModalClose = () => {
    setShowCopyModal(false);
    setSelectedAssessment(null);
    setSelectedClassrooms([]);
    setClassroomSearchTerm('');
  };

  // Validation function
  const validateForm = (data) => {
    const errors = {};

    if (!data.assessmentName.trim()) {
      errors.assessmentName = "Assessment name is required";
    } else if (data.assessmentName.trim().length < 3) {
      errors.assessmentName = "Assessment name must be at least 3 characters";
    }

    if (!data.duration.trim()) {
      errors.duration = "Duration is required";
    } else if (isNaN(data.duration) || Number(data.duration) <= 0) {
      errors.duration = "Duration must be a positive number";
    }

    if (data.passingPercentage === undefined || data.passingPercentage === null ||
      (typeof data.passingPercentage === 'string' && data.passingPercentage.trim() === '')) {
      errors.passingPercentage = "Passing percentage is required";
    } else {
      const passPercentage = Number(data.passingPercentage);
      if (isNaN(passPercentage) || passPercentage < 1 || passPercentage > 100) {
        errors.passingPercentage = "Passing percentage must be between 1 and 100";
      }
    }

    if (data.isTimeBased) {
      if (!data.opensAt) {
        errors.opensAt = "Opens At is required";
      }

      if (!data.closesAt) {
        errors.closesAt = "Closes At is required";
      }

      if (data.opensAt && data.closesAt) {
        const opensDate = new Date(data.opensAt);
        const closesDate = new Date(data.closesAt);

        if (opensDate >= closesDate) {
          errors.closesAt = "Closing time must be after opening time";
        }

        const now = new Date();
        if (opensDate < now) {
          errors.opensAt = "Opening time cannot be in the past";
        }
      }
    }

    return errors;
  };

  // Add assessment handler
  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!permissions.classroom_assessment_create) {
      toast.warning("You don't have permission to create assessments");
      return;
    }

    if (!classroomId) {
      toast.error("Classroom ID is missing. Please navigate from the classroom dashboard.");
      return;
    }

    const errors = validateForm(formData);
    if (Object.keys(errors).length > 0) {
      setFormErrors(errors);
      return;
    }

    setIsSubmitting(true);

    try {
      let payload;

      if (formData.isTimeBased) {
        // Convert dates to UTC based on selected timezone
        const opensAtLocal = moment.tz(formData.opensAt, formData.timeZone);
        const closesAtLocal = moment.tz(formData.closesAt, formData.timeZone);

        const formattedOpenDate = opensAtLocal.utc().format();
        const formattedCloseDate = closesAtLocal.utc().format();


        payload = {
          class_id: classroomId,
          assessment_name: formData.assessmentName,
          activate_time: formattedOpenDate,
          deactivate_time: formattedCloseDate,
          duration: Number(formData.duration),
          is_time_based: 1,
          is_active: 1, // Always set to 1 for time-based assessments
          pass_percentage: Number(formData.passingPercentage),
          is_visible: formData.visibility === 'visible' ? 1 : 0,
          time_zone: formData.timeZone // Add timezone to payload
        };
      } else {
        payload = {
          class_id: classroomId,
          assessment_name: formData.assessmentName,
          activate_time: "",
          deactivate_time: "",
          duration: Number(formData.duration),
          is_time_based: 0,
          is_active: formData.status === 'Active' ? 1 : 0,
          pass_percentage: Number(formData.passingPercentage),
          is_visible: formData.visibility === 'visible' ? 1 : 0,
          time_zone: formData.timeZone // Add timezone to payload
        };
      }

      const response = await addAssessmentToClassroom(payload);

      if (response.success) {
        toast.success(response.message || `Assessment "${formData.assessmentName}" has been created`);
        handleModalClose();
        fetchAssessments(pagination.page, searchTerm);
      } else {
        toast.error(response.message || "Failed to create assessment");
      }
    } catch (error) {
      console.error("Error creating assessment:", error);
      toast.error("An error occurred while creating the assessment");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Edit assessment handler
  const handleEditAssessment = (assessment) => {
    if (!permissions.classroom_assessment_edit) {
      toast.warning("You don't have permission to edit assessments");
      return;
    }

    // Convert UTC dates to local time in the selected timezone
    const timezone = assessment.raw_time_zone || DefaultTimeZone;
    const opensAt = assessment.activate_time ? moment(assessment.activate_time).format('YYYY-MM-DDTHH:mm') : '';
    const closesAt = assessment.deactivate_time ? moment(assessment.deactivate_time).format('YYYY-MM-DDTHH:mm') : '';

    setEditFormData({
      assessmentId: assessment.id,
      assessmentName: assessment.title,
      isTimeBased: assessment.isTimeBased,
      status: assessment.status,
      duration: assessment.raw_duration.toString(),
      passingPercentage: assessment.pass_percentage.toString(),
      opensAt: opensAt,
      closesAt: closesAt,
      visibility: assessment.is_visible === 1 ? 'visible' : 'hidden',
      timeZone: timezone
    });
    setFormErrors({});
    setShowEditModal(true);
  };

  // Update assessment handler
  const handleEditSubmit = async () => {
    if (!classroomId || !editFormData.assessmentId) {
      toast.error("Missing required information. Cannot update assessment.");
      return;
    }

    const errors = validateForm(editFormData);
    if (Object.keys(errors).length > 0) {
      setFormErrors(errors);
      return;
    }

    setIsEditing(true);

    try {
      let payload;

      if (editFormData.isTimeBased) {
        // Convert local dates to UTC for API
        const opensAtUTC = moment(editFormData.opensAt).utc().format();
        const closesAtUTC = moment(editFormData.closesAt).utc().format();

        payload = {
          assessment_id: editFormData.assessmentId,
          class_id: classroomId,
          assessment_name: editFormData.assessmentName,
          activate_time: opensAtUTC,
          deactivate_time: closesAtUTC,
          duration: Number(editFormData.duration),
          is_time_based: 1,
          is_active: 1, // Always set to 1 for time-based assessments
          pass_percentage: Number(editFormData.passingPercentage),
          is_visible: editFormData.visibility === 'visible' ? 1 : 0,
          time_zone: editFormData.timeZone
        };
      } else {
        payload = {
          assessment_id: editFormData.assessmentId,
          class_id: classroomId,
          assessment_name: editFormData.assessmentName,
          activate_time: "",
          deactivate_time: "",
          duration: Number(editFormData.duration),
          is_time_based: 0,
          is_active: editFormData.status === 'Active' ? 1 : 0,
          pass_percentage: Number(editFormData.passingPercentage),
          is_visible: editFormData.visibility === 'visible' ? 1 : 0,
          time_zone: editFormData.timeZone
        };
      }

      const response = await updateAssessmentInClassroom(payload);

      if (response.success) {
        toast.success(response.message || `Assessment "${editFormData.assessmentName}" has been updated`);
        handleEditModalClose();
        fetchAssessments(pagination.page, searchTerm);
      } else {
        toast.error(response.message || "Failed to update assessment");
      }
    } catch (error) {
      console.error("Error updating assessment:", error);
      toast.error("An error occurred while updating the assessment");
    } finally {
      setIsEditing(false);
    }
  };

  // Delete assessment handlers
  const handleDeleteAssessment = (assessment) => {
    if (!permissions.classroom_assessment_delete) {
      toast.warning("You don't have permission to delete assessments");
      return;
    }
    setSelectedAssessment(assessment);
    setShowDeleteModal(true);
  };

  const confirmDeleteAssessment = async () => {
    if (!selectedAssessment) return;

    setIsDeleting(true);

    try {
      const payload = {
        class_id: classroomId,
        assessment_id: selectedAssessment.id
      };

      const response = await removeAssessmentFromClassroom(payload);

      if (response.success) {
        toast.success(response.message || `Assessment "${selectedAssessment.title}" has been deleted`);
        handleDeleteModalClose();
        fetchAssessments(pagination.page, searchTerm);
      } else {
        toast.error(response.message || "Failed to delete assessment");
      }
    } catch (error) {
      console.error("Error deleting assessment:", error);
      toast.error("An error occurred while deleting the assessment");
    } finally {
      setIsDeleting(false);
    }
  };

  // Copy assessment handlers
  const handleCopyAssessment = (assessment) => {
    setSelectedAssessment(assessment);
    setShowCopyModal(true);
  };

  const toggleClassroomSelection = (classroomId) => {
    if (selectedClassrooms.includes(classroomId)) {
      setSelectedClassrooms(selectedClassrooms.filter(id => id !== classroomId));
    } else {
      setSelectedClassrooms([...selectedClassrooms, classroomId]);
    }
  };

  const handleCopySubmit = async () => {
    if (selectedClassrooms.length === 0) {
      toast.warn("Please select at least one classroom to copy the assessment to.");
      return;
    }
    if (!selectedAssessment || !classroomId) {
      toast.error("Missing assessment or current classroom information.");
      return;
    }

    setIsCopying(true);
    const payload = {
      current_classroom_id: classroomId,
      copy_classroom_ids: selectedClassrooms,
      assessment_id: selectedAssessment.id
    };

    console.log('copy assessment payload', payload)

    try {
      const response = await copyAssessment(payload);
      if (response.success) {
        toast.success(response.message || "Assessment copied successfully to selected classrooms!");
        handleCopyModalClose();
      } else {
        toast.error(response.message || "Failed to copy assessment.");
      }
    } catch (error) {
      console.error("Error copying assessment:", error);
      toast.error("An error occurred while copying the assessment.");
    } finally {
      setIsCopying(false);
    }
  };

  // Filter classrooms for copy modal
  const filteredClassrooms = availableClassrooms.filter(classroom =>
    classroom.name.toLowerCase().includes(classroomSearchTerm.toLowerCase())
  );

  return (
    <>
      <div className="row mb-3">
        <div className="col-12">
          <div className="row">
            <div className="col-12 col-md-6">
              <input
                type="text"
                className="form-control"
                placeholder="Search Assessment"
                style={{ width: '300px' }}
                value={searchTerm}
                onChange={handleSearch}
              />
            </div>
            <div className="col-12 col-md-6 d-flex justify-content-end" >
              <button
                className="btn btn-primary"
                style={{ width: '300px' }}
                onClick={() => setShowModal(true)}
                disabled={!permissions.classroom_assessment_create}
                title={!permissions.classroom_assessment_create ? "You don't have permission to create assessments" : ""}
              >
                <Icon icon="fluent:add-24-regular" width="20" height="20" />
                Add Assessment
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Assessment cards display */}
      <div className="row">
        {loading ? (
          <div className="col-12 d-flex justify-content-center my-5">
            <Loader />
          </div>
        ) : assessments.length === 0 ? (
          <div className="col-12 d-flex justify-content-center my-5">
            <NoData message="No assessments found" />
          </div>
        ) : (
          assessments.map(assessment => (
            <div className="col-12 mb-3" key={assessment.id}>
              <div className="card">
                <div className="card-body">
                  {/* title and icons */}
                  <div className="col-12 d-flex justify-content-between align-items-center mb-3">
                    <h5 className="card-title mb-0">{assessment.title}</h5>
                    <div className="d-flex gap-2 align-items-center">
                      <span
                        style={{
                          padding: '6px 12px',
                          fontSize: '12px',
                          fontWeight: '500',
                          borderRadius: '4px',
                          backgroundColor: assessment.is_visible === 1 ? '#e6f4ea' : '#fff3e0',
                          color: assessment.is_visible === 1 ? '#1e8e3e' : '#f9a825',
                          border: `1px solid ${assessment.is_visible === 1 ? '#1e8e3e' : '#f9a825'}`
                        }}
                      >
                        {assessment.is_visible === 1 ? 'Visible to Trainees' : 'Hidden from Trainees'}
                      </span>
                      <span className={`assessment-status ${assessment.is_active === 1 ? 'ongoing' : 'ended'}`}>
                        {assessment.is_active === 1 ? (
                          <div className="pulse-container">
                            Assessment Ongoing
                            <span className="status-dot" />
                          </div>
                        ) : (
                          'Assessment Ended'
                        )}
                      </span>
                      <div className="d-flex gap-2">
                        <button
                          className="btn btn-outline-primary btn-sm"
                          title={!permissions.classroom_assessment_edit ? "You don't have permission to edit assessments" : 'Edit'}
                          onClick={() => handleEditAssessment(assessment)}
                          disabled={!permissions.classroom_assessment_edit}
                        >
                          <Icon icon="fluent:edit-24-regular" width="20" height="20" />
                        </button>
                        <button
                          className="btn btn-outline-primary btn-sm"
                          title='Copy to Other Classrooms'
                          onClick={() => handleCopyAssessment(assessment)}
                        >
                          <Icon icon="fluent:copy-24-regular" width="20" height="20" />
                        </button>
                        <button
                          className="btn btn-outline-danger btn-sm"
                          title={!permissions.classroom_assessment_delete ? "You don't have permission to delete assessments" : 'Delete'}
                          onClick={() => handleDeleteAssessment(assessment)}
                          disabled={!permissions.classroom_assessment_delete}
                        >
                          <Icon icon="fluent:delete-24-regular" width="20" height="20" />
                        </button>
                      </div>
                    </div>
                  </div>
                  <hr />

                  {/* assessment details  */}
                  <div className="col-12 mb-3">
                    <div className="row">
                      <div className="col-md-6">
                        <div className="d-flex align-items-center gap-2 mb-2">
                          <Icon icon="fluent:timer-24-regular" width="20" height="20" className="text-muted" />
                          <span>Duration: {assessment.duration}</span>
                        </div>
                        <div className="d-flex align-items-center gap-2">
                          <Icon icon="fluent:checkmark-circle-24-regular" width="20" height="20" className="text-muted" />
                          <span>Passing Percentage: {assessment.passingPercentage}</span>
                        </div>
                      </div>
                      <div className="col-md-6">
                        {assessment.manualActivation ? (
                          <div className="d-flex align-items-center gap-2">
                            <Icon icon="fluent:calendar-24-regular" width="20" height="20" className="text-muted" />
                            <span>Manual activation: No time constraints</span>
                          </div>
                        ) : (
                          <>
                            <div className="d-flex align-items-center gap-2 mb-2">
                              <Icon icon="fluent:calendar-24-regular" width="20" height="20" className="text-muted" />
                              <span>
                                Opens: {assessment.opensAt
                                  ? moment.utc(assessment.opensAt).local().format('DD MMM YYYY, hh:mm A')
                                  : 'N/A'}
                              </span>
                            </div>

                            <div className="d-flex align-items-center gap-2">
                              <Icon icon="fluent:calendar-24-regular" width="20" height="20" className="text-muted" />
                              <span>
                                Closes: {assessment.closesAt
                                  ? moment.utc(assessment.closesAt).local().format('DD MMM YYYY, hh:mm A')
                                  : 'N/A'}
                              </span>
                            </div>

                          </>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Status Message */}
                  <div className="col-12 mb-3">
                    {assessment.status === 'Active' ? (
                      <div className="d-flex align-items-center gap-2 text-success">
                        <span className="spinner-grow spinner-grow-sm"></span>
                        <small>Trainee can write the assessment when active</small>
                      </div>
                    ) : (
                      <div className="d-flex align-items-center gap-2 text-muted">
                        <span className="rounded-circle bg-secondary" style={{ width: '6px', height: '6px' }}></span>
                        <small>
                          {assessment.activate_time ?
                            `Opens on ${new Date(assessment.activate_time.replace(' ', 'T')).toLocaleDateString('en-US', { month: 'short', day: '2-digit' })}` :
                            'Manual activation required'
                          }
                        </small>
                      </div>
                    )}
                  </div>

                  <hr />

                  {/* assessment footer  */}
                  <div className="col-12">
                    <div className="d-flex justify-content-between align-items-center">
                      <div className="d-flex gap-4">
                        <div className="text-center">
                          <div className="h5 mb-0 text-primary">{assessment.stats.passingPercentage}</div>
                          <small className="text-muted">Passing Percentage</small>
                        </div>
                        <div className="text-center">
                          <div className="h5 mb-0 text-primary">{assessment.stats.attended}</div>
                          <small className="text-muted">Attended</small>
                        </div>
                        <div className="text-center">
                          <div className="h5 mb-0 text-primary">{assessment.stats.noOfDue}</div>
                          <small className="text-muted">No Of Questions</small>
                        </div>
                      </div>
                      <div className="d-flex gap-2">
                        <button
                          className="btn btn-primary d-flex align-items-center gap-2"
                          onClick={() => HandleAssessmentManage(assessment.id)}
                          disabled={!permissions.classroom_assessment_manage}
                          title={!permissions.classroom_assessment_manage ? "You don't have permission to manage assessments" : ""}
                        >
                          <Icon icon="fluent:settings-24-regular" width="20" height="20" />
                          Manage
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))
        )}
      </div>

      {/* Add Assessment Modal */}
      <Modal show={showModal} onHide={handleModalClose} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>Add Assessment</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <form onSubmit={handleSubmit}>
            <div className="mb-3">
              <label className="form-label">Assessment Name <span className="text-danger">*</span></label>
              <input
                type="text"
                className={`form-control ${formErrors.assessmentName ? 'is-invalid' : ''}`}
                name="assessmentName"
                value={formData.assessmentName}
                onChange={handleInputChange}
                placeholder="Enter assessment name"
              />
              {formErrors.assessmentName && (
                <div className="invalid-feedback">{formErrors.assessmentName}</div>
              )}
            </div>

            <div className="mb-3">
              <div className="form-check">
                <input
                  type="checkbox"
                  className="form-check-input"
                  id="isTimeBased"
                  name="isTimeBased"
                  checked={formData.isTimeBased}
                  onChange={handleInputChange}
                />
                <label className="form-check-label" htmlFor="isTimeBased">
                  Is Time Based
                </label>
              </div>
              <div className="form-text text-muted">
                Time-based assessments will automatically be set to Active when the opening time is reached.
              </div>
            </div>

            {!formData.isTimeBased && (
              <div className="mb-3">
                <label className="form-label">Status</label>
                <select
                  className="form-select"
                  name="status"
                  value={formData.status}
                  onChange={handleInputChange}
                >
                  <option value="Inactive">Inactive</option>
                  <option value="Active">Active</option>
                </select>
                <div className="form-text text-muted">
                  Default is Inactive. Set to Active if you want to make it available immediately.
                </div>
              </div>
            )}

            <div className="mb-3">
              <label className="form-label">Visibility</label>
              <select
                className="form-select"
                name="visibility"
                value={formData.visibility}
                onChange={handleInputChange}
              >
                <option value="hidden">Hidden from Trainees</option>
                <option value="visible">Visible to Trainees</option>
              </select>
              <div className="form-text text-muted">
                Control whether trainees can see this assessment in their list
              </div>
            </div>

            <div className="mb-3">
              <label className="form-label">Duration (minutes) <span className="text-danger">*</span></label>
              <input
                type="number"
                className={`form-control ${formErrors.duration ? 'is-invalid' : ''}`}
                name="duration"
                value={formData.duration}
                onChange={handleInputChange}
                placeholder="Enter duration in minutes"
              />
              {formErrors.duration && (
                <div className="invalid-feedback">{formErrors.duration}</div>
              )}
              <div className="form-text text-muted">
                Time allowed for trainees to complete the assessment once started
              </div>
            </div>

            <div className="mb-3">
              <label className="form-label">Passing Percentage <span className="text-danger">*</span></label>
              <input
                type="number"
                className={`form-control ${formErrors.passingPercentage ? 'is-invalid' : ''}`}
                name="passingPercentage"
                value={formData.passingPercentage}
                onChange={handleInputChange}
                placeholder="Enter passing percentage"
                min="1"
                max="100"
              />
              {formErrors.passingPercentage && (
                <div className="invalid-feedback">{formErrors.passingPercentage}</div>
              )}
              <div className="form-text text-muted">
                Percentage required to pass the assessment
              </div>
            </div>

            {formData.isTimeBased && (
              <>
                <div className="mb-3">
                  <label className="form-label">Time Zone <span className="text-danger">*</span></label>
                  <div className="position-relative">
                    <div
                      className={`form-select d-flex align-items-center justify-content-between ${formErrors.timeZone ? 'is-invalid' : ''}`}
                      onClick={() => setIsTimeZoneDropdownOpen(!isTimeZoneDropdownOpen)}
                      style={{
                        borderRadius: '8px',
                        border: formErrors.timeZone ? '1px solid #dc3545' : '1px solid #e0e0e0',
                        padding: '12px 16px',
                        fontSize: '0.875rem',
                        backgroundColor: '#fff',
                        cursor: 'pointer'
                      }}
                    >
                      <span>{formData.timeZone ? timeZones.find(zone => zone.value === formData.timeZone)?.label : 'Select Time Zone'}</span>
                      <Icon icon={isTimeZoneDropdownOpen ? "eva:arrow-up-fill" : "eva:arrow-down-fill"} />
                    </div>
                    {isTimeZoneDropdownOpen && (
                      <div
                        className="position-absolute w-100 bg-white border rounded-3 mt-1 shadow-sm"
                        style={{
                          maxHeight: '300px',
                          overflowY: 'auto',
                          zIndex: 1000
                        }}
                      >
                        <div className="p-2 sticky-top bg-white">
                          <div className="position-relative">
                            <input
                              type="text"
                              className="form-control"
                              placeholder="Search timezone..."
                              value={timeZoneSearch}
                              onChange={(e) => setTimeZoneSearch(e.target.value)}
                              onClick={(e) => e.stopPropagation()}
                              style={{
                                paddingLeft: '35px',
                                border: '1px solid #e0e0e0',
                                borderRadius: '6px',
                                fontSize: '0.875rem'
                              }}
                            />
                            <Icon
                              icon="eva:search-outline"
                              style={{
                                position: 'absolute',
                                left: '12px',
                                top: '50%',
                                transform: 'translateY(-50%)',
                                color: '#6c757d'
                              }}
                            />
                          </div>
                        </div>
                        <div className="timezone-options">
                          {timeZones
                            .filter(zone =>
                              zone.label.toLowerCase().includes(timeZoneSearch.toLowerCase()) ||
                              zone.value.toLowerCase().includes(timeZoneSearch.toLowerCase())
                            )
                            .map((zone) => (
                              <div
                                key={zone.value}
                                className="timezone-option"
                                onClick={() => {
                                  setFormData(prev => ({ ...prev, timeZone: zone.value }));
                                  setIsTimeZoneDropdownOpen(false);
                                  setTimeZoneSearch('');
                                }}
                                style={{
                                  padding: '10px 16px',
                                  cursor: 'pointer',
                                  backgroundColor: formData.timeZone === zone.value ? '#f8f9fa' : 'transparent',
                                  transition: 'background-color 0.2s',
                                  fontSize: '0.875rem',
                                  color: '#333'
                                }}
                                onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#f8f9fa'}
                                onMouseLeave={(e) => e.currentTarget.style.backgroundColor = formData.timeZone === zone.value ? '#f8f9fa' : 'transparent'}
                              >
                                {zone.label}
                              </div>
                            ))
                          }
                        </div>
                      </div>
                    )}
                  </div>
                  {formErrors.timeZone && (
                    <div className="invalid-feedback">{formErrors.timeZone}</div>
                  )}
                </div>

                <div className="row">
                  <div className="col-md-6 mb-3">
                    <label className="form-label">Opens At  #*<span className="text-danger">*</span></label>
                    <input
                      type="datetime-local"
                      className={`form-control ${formErrors.opensAt ? 'is-invalid' : ''}`}
                      name="opensAt"
                      value={formData.opensAt}
                      onChange={handleInputChange}
                    />
                    {formErrors.opensAt && (
                      <div className="invalid-feedback">{formErrors.opensAt}</div>
                    )}
                  </div>
                  <div className="col-md-6 mb-3">
                    <label className="form-label">Closes At <span className="text-danger">*</span></label>
                    <input
                      type="datetime-local"
                      className={`form-control ${formErrors.closesAt ? 'is-invalid' : ''}`}
                      name="closesAt"
                      value={formData.closesAt}
                      onChange={handleInputChange}
                    />
                    {formErrors.closesAt && (
                      <div className="invalid-feedback">{formErrors.closesAt}</div>
                    )}
                  </div>
                </div>
              </>
            )}

            <div className="d-flex justify-content-end gap-2">
              <button
                type="button"
                className="btn btn-secondary"
                onClick={handleModalClose}
                disabled={isSubmitting}
              >
                Cancel
              </button>
              <button
                type="submit"
                className="btn btn-primary"
                disabled={isSubmitting}
              >
                {isSubmitting ? (
                  <>
                    <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                    Creating...
                  </>
                ) : (
                  'Save Assessment'
                )}
              </button>
            </div>
          </form>
        </Modal.Body>
      </Modal>

      {/* Edit Assessment Modal */}
      <Modal show={showEditModal} onHide={handleEditModalClose} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>Edit Assessment</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <form onSubmit={(e) => { e.preventDefault(); handleEditSubmit(); }}>
            <div className="mb-3">
              <label className="form-label">Assessment Name <span className="text-danger">*</span></label>
              <input
                type="text"
                className={`form-control ${formErrors.assessmentName ? 'is-invalid' : ''}`}
                name="assessmentName"
                value={editFormData.assessmentName}
                onChange={handleEditInputChange}
                placeholder="Enter assessment name"
              />
              {formErrors.assessmentName && (
                <div className="invalid-feedback">{formErrors.assessmentName}</div>
              )}
            </div>

            <div className="mb-3">
              <div className="form-check">
                <input
                  type="checkbox"
                  className="form-check-input"
                  id="editIsTimeBased"
                  name="isTimeBased"
                  checked={editFormData.isTimeBased}
                  onChange={handleEditInputChange}
                  disabled={true}
                />
                <label className="form-check-label" htmlFor="editIsTimeBased">
                  Is Time Based
                </label>
              </div>
              <div className="form-text text-muted">
                Time-based setting cannot be changed after creation.
              </div>
            </div>

            <div className="mb-3">
              <label className="form-label">Visibility</label>
              <select
                className="form-select"
                name="visibility"
                value={editFormData.visibility}
                onChange={handleEditInputChange}
              >
                <option value="hidden">Hidden from Trainees</option>
                <option value="visible">Visible to Trainees</option>
              </select>
              <div className="form-text text-muted">
                Control whether trainees can see this assessment in their list
              </div>
            </div>

            {editFormData.isTimeBased ? (
              <>
                <div className="mb-3">
                  <label className="form-label">Duration (minutes) <span className="text-danger">*</span></label>
                  <input
                    type="number"
                    className={`form-control ${formErrors.duration ? 'is-invalid' : ''}`}
                    name="duration"
                    value={editFormData.duration}
                    onChange={handleEditInputChange}
                    placeholder="Enter duration in minutes"
                  />
                  {formErrors.duration && (
                    <div className="invalid-feedback">{formErrors.duration}</div>
                  )}
                  <div className="form-text text-muted">
                    Time allowed for trainees to complete the assessment once started
                  </div>
                </div>

                <div className="mb-3">
                  <label className="form-label">Passing Percentage <span className="text-danger">*</span></label>
                  <input
                    type="number"
                    className={`form-control ${formErrors.passingPercentage ? 'is-invalid' : ''}`}
                    name="passingPercentage"
                    value={editFormData.passingPercentage}
                    onChange={handleEditInputChange}
                    placeholder="Enter passing percentage"
                    min="1"
                    max="100"
                  />
                  {formErrors.passingPercentage && (
                    <div className="invalid-feedback">{formErrors.passingPercentage}</div>
                  )}
                  <div className="form-text text-muted">
                    Percentage required to pass the assessment
                  </div>
                </div>

                <div className="mb-3">
                  <label className="form-label">Time Zone <span className="text-danger">*</span></label>
                  <div className="position-relative">
                    <div
                      className={`form-select d-flex align-items-center justify-content-between ${formErrors.timeZone ? 'is-invalid' : ''}`}
                      onClick={() => setIsTimeZoneDropdownOpen(!isTimeZoneDropdownOpen)}
                      style={{
                        borderRadius: '8px',
                        border: formErrors.timeZone ? '1px solid #dc3545' : '1px solid #e0e0e0',
                        padding: '12px 16px',
                        fontSize: '0.875rem',
                        backgroundColor: '#fff',
                        cursor: 'pointer'
                      }}
                    >
                      <span>{editFormData.timeZone ? timeZones.find(zone => zone.value === editFormData.timeZone)?.label : 'Select Time Zone'}</span>
                      <Icon icon={isTimeZoneDropdownOpen ? "eva:arrow-up-fill" : "eva:arrow-down-fill"} />
                    </div>
                    {isTimeZoneDropdownOpen && (
                      <div
                        className="position-absolute w-100 bg-white border rounded-3 mt-1 shadow-sm"
                        style={{
                          maxHeight: '300px',
                          overflowY: 'auto',
                          zIndex: 1000
                        }}
                      >
                        <div className="p-2 sticky-top bg-white">
                          <div className="position-relative">
                            <input
                              type="text"
                              className="form-control"
                              placeholder="Search timezone..."
                              value={timeZoneSearch}
                              onChange={(e) => setTimeZoneSearch(e.target.value)}
                              onClick={(e) => e.stopPropagation()}
                              style={{
                                paddingLeft: '35px',
                                border: '1px solid #e0e0e0',
                                borderRadius: '6px',
                                fontSize: '0.875rem'
                              }}
                            />
                            <Icon
                              icon="eva:search-outline"
                              style={{
                                position: 'absolute',
                                left: '12px',
                                top: '50%',
                                transform: 'translateY(-50%)',
                                color: '#6c757d'
                              }}
                            />
                          </div>
                        </div>
                        <div className="timezone-options">
                          {timeZones
                            .filter(zone =>
                              zone.label.toLowerCase().includes(timeZoneSearch.toLowerCase()) ||
                              zone.value.toLowerCase().includes(timeZoneSearch.toLowerCase())
                            )
                            .map((zone) => (
                              <div
                                key={zone.value}
                                className="timezone-option"
                                onClick={() => {
                                  setEditFormData(prev => ({ ...prev, timeZone: zone.value }));
                                  setIsTimeZoneDropdownOpen(false);
                                  setTimeZoneSearch('');
                                }}
                                style={{
                                  padding: '10px 16px',
                                  cursor: 'pointer',
                                  backgroundColor: editFormData.timeZone === zone.value ? '#f8f9fa' : 'transparent',
                                  transition: 'background-color 0.2s',
                                  fontSize: '0.875rem',
                                  color: '#333'
                                }}
                                onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#f8f9fa'}
                                onMouseLeave={(e) => e.currentTarget.style.backgroundColor = editFormData.timeZone === zone.value ? '#f8f9fa' : 'transparent'}
                              >
                                {zone.label}
                              </div>
                            ))
                          }
                        </div>
                      </div>
                    )}
                  </div>
                  {formErrors.timeZone && (
                    <div className="invalid-feedback">{formErrors.timeZone}</div>
                  )}
                </div>

                <div className="row">
                  {/* Optional Debug Output */}

                  <div className="col-md-6 mb-3">
                    <label className="form-label">Opens At <span className="text-danger">*</span></label>
                    <input
                      type="datetime-local"
                      className={`form-control ${formErrors.opensAt ? 'is-invalid' : ''}`}
                      name="opensAt"
                      value={
                        editFormData.opensAt
                          ? moment.utc(editFormData.opensAt).tz(editFormData.timeZone).format('YYYY-MM-DDTHH:mm')
                          : ''
                      }
                      onChange={handleEditInputChange}
                      min={moment().tz(editFormData.timeZone).format('YYYY-MM-DDTHH:mm')}
                    />
                    {formErrors.opensAt && (
                      <div className="invalid-feedback">{formErrors.opensAt}</div>
                    )}
                  </div>

                  <div className="col-md-6 mb-3">
                    <label className="form-label">Closes At <span className="text-danger">*</span></label>
                    <input
                      type="datetime-local"
                      className={`form-control ${formErrors.closesAt ? 'is-invalid' : ''}`}
                      name="closesAt"
                      value={
                        editFormData.closesAt
                          ? moment.utc(editFormData.closesAt).tz(editFormData.timeZone).format('YYYY-MM-DDTHH:mm')
                          : ''
                      }
                      onChange={handleEditInputChange}
                      min={moment().tz(editFormData.timeZone).format('YYYY-MM-DDTHH:mm')}
                    />
                    {formErrors.closesAt && (
                      <div className="invalid-feedback">{formErrors.closesAt}</div>
                    )}
                  </div>
                </div>



              </>
            ) : (
              <>
                <div className="mb-3">
                  <label className="form-label">Status</label>
                  <select
                    className="form-select"
                    name="status"
                    value={editFormData.status}
                    onChange={handleEditInputChange}
                  >
                    <option value="Inactive">Inactive</option>
                    <option value="Active">Active</option>
                  </select>
                  <div className="form-text text-muted">
                    Set to Active to make the assessment available to trainees.
                  </div>
                </div>

                <div className="mb-3">
                  <label className="form-label">Duration (minutes) <span className="text-danger">*</span></label>
                  <input
                    type="number"
                    className={`form-control ${formErrors.duration ? 'is-invalid' : ''}`}
                    name="duration"
                    value={editFormData.duration}
                    onChange={handleEditInputChange}
                    placeholder="Enter duration in minutes"
                  />
                  {formErrors.duration && (
                    <div className="invalid-feedback">{formErrors.duration}</div>
                  )}
                  <div className="form-text text-muted">
                    Time allowed for trainees to complete the assessment once started
                  </div>
                </div>

                <div className="mb-3">
                  <label className="form-label">Passing Percentage <span className="text-danger">*</span></label>
                  <input
                    type="number"
                    className={`form-control ${formErrors.passingPercentage ? 'is-invalid' : ''}`}
                    name="passingPercentage"
                    value={editFormData.passingPercentage}
                    onChange={handleEditInputChange}
                    placeholder="Enter passing percentage"
                    min="1"
                    max="100"
                  />
                  {formErrors.passingPercentage && (
                    <div className="invalid-feedback">{formErrors.passingPercentage}</div>
                  )}
                  <div className="form-text text-muted">
                    Percentage required to pass the assessment
                  </div>
                </div>
              </>
            )}

            <div className="d-flex justify-content-end gap-2">
              <button
                type="button"
                className="btn btn-secondary"
                onClick={handleEditModalClose}
                disabled={isEditing}
              >
                Cancel
              </button>
              <button
                type="submit"
                className="btn btn-primary"
                disabled={isEditing}
              >
                {isEditing ? (
                  <>
                    <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                    Updating...
                  </>
                ) : (
                  'Update Assessment'
                )}
              </button>
            </div>
          </form>
        </Modal.Body>
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal show={showDeleteModal} onHide={handleDeleteModalClose} centered>
        <Modal.Header closeButton>
          <Modal.Title className="text-danger">
            <Icon icon="fluent:warning-24-regular" className="me-2" />
            Confirm Deletion
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {selectedAssessment && (
            <div>
              <p>Are you sure you want to delete the assessment <strong>"{selectedAssessment.title}"</strong>?</p>
              <p className="text-danger mb-0">This action cannot be undone.</p>
            </div>
          )}
        </Modal.Body>
        <Modal.Footer>
          <button
            type="button"
            className="btn btn-secondary"
            onClick={handleDeleteModalClose}
            disabled={isDeleting}
          >
            Cancel
          </button>
          <button
            type="button"
            className="btn btn-danger"
            onClick={confirmDeleteAssessment}
            disabled={isDeleting}
          >
            {isDeleting ? (
              <>
                <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                Deleting...
              </>
            ) : (
              'Delete Assessment'
            )}
          </button>
        </Modal.Footer>
      </Modal>

      {/* Copy Assessment Modal */}
      <Modal show={showCopyModal} onHide={handleCopyModalClose} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>
            <Icon icon="fluent:copy-24-regular" className="me-2" />
            Copy Assessment to Other Classrooms
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {selectedAssessment && (
            <div className="mb-3">
              <h6>Assessment: <span className="text-primary">{selectedAssessment.title}</span></h6>
              <p className="text-muted">Select the classrooms where you want to copy this assessment.</p>
            </div>
          )}

          <div className="mb-3">
            <label className="form-label">Search Classrooms</label>
            <input
              type="text"
              className="form-control"
              placeholder="Search classrooms..."
              value={classroomSearchTerm}
              onChange={(e) => setClassroomSearchTerm(e.target.value)}
            />
          </div>

          <div className="mb-3">
            <label className="form-label">Available Classrooms</label>
            <div className="border rounded p-3" style={{ maxHeight: '300px', overflowY: 'auto' }}>
              {filteredClassrooms.length === 0 ? (
                <div className="text-center text-muted py-3">
                  <Icon icon="fluent:search-24-regular" width="48" height="48" className="mb-2" />
                  <p>No classrooms found</p>
                </div>
              ) : (
                filteredClassrooms.map(classroom => (
                  <div key={classroom.id} className="form-check mb-2">
                    <input
                      className="form-check-input"
                      type="checkbox"
                      id={`classroom-${classroom.id}`}
                      checked={selectedClassrooms.includes(classroom.id)}
                      onChange={() => toggleClassroomSelection(classroom.id)}
                    />
                    <label className="form-check-label" htmlFor={`classroom-${classroom.id}`}>
                      {classroom.name}
                    </label>
                  </div>
                ))
              )}
            </div>
          </div>

          {selectedClassrooms.length > 0 && (
            <div className="alert alert-info">
              <Icon icon="fluent:info-24-regular" className="me-2" />
              Selected {selectedClassrooms.length} classroom{selectedClassrooms.length > 1 ? 's' : ''}
            </div>
          )}
        </Modal.Body>
        <Modal.Footer>
          <button
            type="button"
            className="btn btn-secondary"
            onClick={handleCopyModalClose}
            disabled={isCopying}
          >
            Cancel
          </button>
          <button
            type="button"
            className="btn btn-primary"
            onClick={handleCopySubmit}
            disabled={isCopying || selectedClassrooms.length === 0}
          >
            {isCopying ? (
              <>
                <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                Copying...
              </>
            ) : (
              <>
                <Icon icon="fluent:copy-24-regular" className="me-2" />
                Copy Assessment
              </>
            )}
          </button>
        </Modal.Footer>
      </Modal>



      {/* Pagination */}
      {!loading && assessments.length > 0 && pagination.totalPages > 1 && (
        <div className="row mt-4">
          <div className="col-12">
            <div className="d-flex justify-content-between align-items-center">
              <div className="text-muted">
                Total {pagination.totalRecords} records
              </div>
              <nav>
                <ul className="pagination mb-0">
                  <li className={`page-item ${pagination.page === 1 ? 'disabled' : ''}`}>
                    <button
                      className="page-link"
                      onClick={() => fetchAssessments(pagination.page - 1, searchTerm)}
                      disabled={pagination.page === 1}
                    >
                      Previous
                    </button>
                  </li>

                  {[...Array(pagination.totalPages)].map((_, index) => {
                    const pageNumber = index + 1;
                    return (
                      <li key={pageNumber} className={`page-item ${pagination.page === pageNumber ? 'active' : ''}`}>
                        <button
                          className="page-link"
                          onClick={() => fetchAssessments(pageNumber, searchTerm)}
                        >
                          {pageNumber}
                        </button>
                      </li>
                    );
                  })}

                  <li className={`page-item ${pagination.page === pagination.totalPages ? 'disabled' : ''}`}>
                    <button
                      className="page-link"
                      onClick={() => fetchAssessments(pagination.page + 1, searchTerm)}
                      disabled={pagination.page === pagination.totalPages}
                    >
                      Next
                    </button>
                  </li>
                </ul>
              </nav>
            </div>
          </div>
        </div>
      )}
    </>
  )
}

export default ClassroomAssessment
