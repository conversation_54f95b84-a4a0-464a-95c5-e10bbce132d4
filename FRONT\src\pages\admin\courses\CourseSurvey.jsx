import React, { useEffect, useState } from 'react';
import { addQuestion, deleteQuestion, editQuestion, getModuleQuestions } from '../../../services/adminService';

import { Icon } from '@iconify/react';
import { decodeData } from '../../../utils/encodeAndEncode';
import { toast } from 'react-toastify';
import { useParams } from 'react-router-dom';
import { usePermissions } from '../../../context/PermissionsContext';

function CourseSurvey() {
    const { courseId, moduleId, contentId } = useParams();
    const { permissions } = usePermissions();
    
    // Decode IDs
    const decodedCourseId = decodeData(courseId);
    const decodedModuleId = decodeData(moduleId);
    const decodedContentId = decodeData(contentId);

    // API data states 
    const [apiQuestions, setApiQuestions] = useState([]);
    const [loading, setLoading] = useState(true);

    // Form data states
    const [questionData, setQuestionData] = useState({ 
        question: '',
        question_type: 'survey',
        options: {},
    });

    // UI control states
    const [showModal, setShowModal] = useState(false);

    // Options state
    const [options, setOptions] = useState([{ 
        id: Date.now(), 
        text: '', 
        optionKey: 'option1' 
    }]);

    // Edit mode states
    const [isEditMode, setIsEditMode] = useState(false);
    const [editingQuestion, setEditingQuestion] = useState(null);

    // Delete confirmation states
    const [showDeleteModal, setShowDeleteModal] = useState(false);
    const [questionToDelete, setQuestionToDelete] = useState(null);

    // Validation states
    const [errors, setErrors] = useState({
        question: '',
        options: []
    });

    // Reset errors when modal opens
    useEffect(() => {
        if (showModal) {
            setErrors({
                question: '',
                options: []
            });
        }
    }, [showModal]);

    // Fetch existing questions
    useEffect(() => {
        const fetchQuestions = async () => {
            try {
                if (!decodedModuleId || !decodedContentId) {
                    toast.error('Missing required parameters', {
                        position: 'top-center',
                        autoClose: 3000
                    });
                    return;
                }

                const response = await getModuleQuestions({
                    module_id: decodedModuleId,
                    assessment_id: decodedContentId
                });

                if (response.success && response.data.response) {
                    setApiQuestions(response.data.response);
                } else {
                    toast.error('Failed to load survey questions', {
                        position: 'top-center',
                        autoClose: 3000
                    });
                }
            } catch (error) {
                console.error('Error fetching survey questions:', error);
                toast.error('Failed to fetch survey questions', {
                    position: 'top-center',
                    autoClose: 3000
                });
            } finally {
                setLoading(false);
            }
        };

        fetchQuestions();
    }, [decodedModuleId, decodedContentId]);

    // Handle question text change
    const handleQuestionChange = (e) => {
        setQuestionData(prev => ({
            ...prev,
            question: e.target.value
        }));
        if (errors.question) {
            setErrors(prev => ({
                ...prev,
                question: ''
            }));
        }
    };

    // Handle option change
    const handleOptionChange = (index, value) => {
        const newOptions = [...options];
        newOptions[index].text = value;
        setOptions(newOptions);

        // Clear error for this option
        if (errors.options[index]) {
            const newOptionErrors = [...errors.options];
            newOptionErrors[index] = '';
            setErrors(prev => ({
                ...prev,
                options: newOptionErrors
            }));
        }

        // Update options data for API
        const optionKey = `option${index + 1}`;
        setQuestionData(prev => ({
            ...prev,
            options: {
                ...prev.options,
                [optionKey]: value
            }
        }));
    };

    // Handle edit button click
    const handleEditClick = (question) => {
        if (!permissions.course_module_content_management_edit) {
            toast.warning("You don't have permission to edit survey questions");
            return;
        }
        setIsEditMode(true);
        setEditingQuestion(question);

        // Set question data
        setQuestionData({
            question: question.question || '',
            question_type: 'survey',
            options: {},
        });

        // Set options with safety checks
        let formattedOptions = [];
        if (question.formatted_options && Array.isArray(question.formatted_options) && question.formatted_options.length > 0) {
            formattedOptions = question.formatted_options.map((opt, index) => ({
                id: Date.now() + index,
                text: opt.content_value || opt.text || opt.value || '',
                optionKey: opt.key || `option${index + 1}`
            }));
        } else {
            // Fallback: create a default option if no options exist
            formattedOptions = [{
                id: Date.now(),
                text: '',
                optionKey: 'option1'
            }];
        }
        setOptions(formattedOptions);

        setShowModal(true);
    };

    // Reset form function
    const resetForm = () => {
        setQuestionData({
            question: '',
            question_type: 'survey',
            options: {},
        });
        setOptions([{ 
            id: Date.now(), 
            text: '', 
            optionKey: 'option1' 
        }]);
        setIsEditMode(false);
        setEditingQuestion(null);
        setErrors({
            question: '',
            options: []
        });
    };

    const validateForm = () => {
        const newErrors = {
            question: '',
            options: []
        };
        let isValid = true;

        // Validate question text
        if (!questionData.question.trim()) {
            newErrors.question = 'Question text is required';
            isValid = false;
        } else if (questionData.question.trim().length > 500) {
            newErrors.question = 'Question text cannot exceed 500 characters';
            isValid = false;
        }

        // Validate options
        const optionErrors = options.map(option => {
            if (!option.text.trim()) {
                isValid = false;
                return 'Option text is required';
            }
            if (option.text.trim().length > 100) {
                isValid = false;
                return 'Option text cannot exceed 100 characters';
            }
            return '';
        });

        // Check if at least one option is provided
        if (options.length === 0) {
            isValid = false;
            newErrors.options = ['At least one option is required'];
        } else {
            newErrors.options = optionErrors;
        }

        setErrors(newErrors);
        return isValid;
    };

    // Handle form submission
    const handleSubmit = async () => {
        if (!validateForm()) {
            return;
        }
        try {
            const formData = new FormData();

            if (isEditMode) {
                // Edit mode payload
                formData.append('id', editingQuestion.id);
                formData.append('question_type', 'mcq');
                formData.append('question_text', questionData.question);
                formData.append('assessment_id', decodedContentId);
                formData.append('module_id', decodedModuleId);

                // Convert options to the format expected by the API
                const formattedOptions = {};
                options.forEach(option => {
                    formattedOptions[option.optionKey] = option.text;
                });
                formData.append('options', JSON.stringify(formattedOptions));

                // Add correct_option field - for survey, all options are treated as correct
                const correctOptions = options.map(option => option.text);
                formData.append('correct_option', JSON.stringify(correctOptions));

                // Handle options data - include all original options
                const optionsData = [];

                // Get all original option keys from the editing question with safety checks
                const originalOptionKeys = editingQuestion.formatted_options && Array.isArray(editingQuestion.formatted_options)
                    ? editingQuestion.formatted_options.map(opt => opt.key || opt.optionKey || `option${Math.random()}`)
                    : [];

                // Add current options
                options.forEach(option => {
                    optionsData.push({
                        option_key: option.optionKey,
                        content_type: 'text',
                        text_value: option.text,
                        is_changed: true
                    });
                });

                // Find deleted options and mark them for deletion
                const deletedOptionKeys = originalOptionKeys.filter(originalKey =>
                    !options.some(opt => opt.optionKey === originalKey)
                );

                formData.append('options_data', JSON.stringify(optionsData));
                formData.append('deleted_option_keys', JSON.stringify(deletedOptionKeys));

                const response = await editQuestion(formData);

                if (response && response.success) {
                    toast.success('Survey question updated successfully!', {
                        position: 'top-center',
                        autoClose: 3000
                    });
                    setShowModal(false);
                    setIsEditMode(false);
                    resetForm();

                    // Refresh questions
                    const questionsResponse = await getModuleQuestions({
                        module_id: decodedModuleId,
                        assessment_id: decodedContentId
                    });
                    if (questionsResponse.success && questionsResponse.data.response) {
                        setApiQuestions(questionsResponse.data.response);
                    }
                } else {
                    toast.error(response?.error || 'Failed to update survey question', {
                        position: 'top-center',
                        autoClose: 3000
                    });
                }
            } else {
                // Add mode payload
                formData.append('module_id', decodedModuleId);
                formData.append('assessment_id', decodedContentId);
                formData.append('question_type', 'mcq');
                formData.append('question_text', questionData.question);

                // Convert options to the format expected by the API
                const formattedOptions = {};
                options.forEach(option => {
                    formattedOptions[option.optionKey] = option.text;
                });
                formData.append('options', JSON.stringify(formattedOptions));

                // Add correct_option field - for survey, all options are treated as correct
                const correctOptions = options.map(option => option.text);
                formData.append('correct_option', JSON.stringify(correctOptions));

                // Handle options data
                const optionsData = options.map(option => ({
                    option_key: option.optionKey,
                    content_type: 'text',
                    text_value: option.text
                }));

                formData.append('options_data', JSON.stringify(optionsData));

                const response = await addQuestion(formData);

                if (response && response.success) {
                    toast.success('Survey question added successfully!', {
                        position: 'top-center',
                        autoClose: 3000
                    });
                    setShowModal(false);
                    resetForm();

                    // Refresh questions
                    const questionsResponse = await getModuleQuestions({
                        module_id: decodedModuleId,
                        assessment_id: decodedContentId
                    });
                    if (questionsResponse.success && questionsResponse.data.response) {
                        setApiQuestions(questionsResponse.data.response);
                    }
                } else {
                    toast.error(response?.error || 'Failed to add survey question', {
                        position: 'top-center',
                        autoClose: 3000
                    });
                }
            }
        } catch (error) {
            console.error(`Error ${isEditMode ? 'editing' : 'adding'} survey question:`, error);
            toast.error(error.message || `An error occurred while ${isEditMode ? 'editing' : 'adding'} the survey question`, {
                position: 'top-center',
                autoClose: 3000
            });
        }
    };

    const addOption = () => {
        const newOptionIndex = options.length + 1;
        setOptions([
            ...options, 
            { 
                id: Date.now(), 
                text: '', 
                optionKey: `option${newOptionIndex}`
            }
        ]);
    };

    const handleDeleteOption = (index) => {
        const newOptions = options.filter((_, i) => i !== index);
        setOptions(newOptions);
    };

    // Handle delete question
    const handleDeleteClick = (question) => {
        if (!permissions.course_module_content_management_delete) {
            toast.warning("You don't have permission to delete survey questions");
            return;
        }
        setQuestionToDelete(question);
        setShowDeleteModal(true);
    };

    const handleDeleteConfirm = async () => {
        try {
            if (!questionToDelete) return;

            const response = await deleteQuestion({
                question_id: questionToDelete.id
            });

            if (response && response.success) {
                toast.success('Survey question deleted successfully', {
                    position: 'top-center',
                    autoClose: 3000
                });
                setApiQuestions(prev => prev.filter(q => q.id !== questionToDelete.id));
            } else {
                toast.error(response?.error || 'Failed to delete survey question', {
                    position: 'top-center',
                    autoClose: 3000
                });
            }
        } catch (error) {
            console.error('Error deleting survey question:', error);
            toast.error(error.message || 'An error occurred while deleting the survey question', {
                position: 'top-center',
                autoClose: 3000
            });
        } finally {
            setShowDeleteModal(false);
            setQuestionToDelete(null);
        }
    };

    // Handle add question click
    const handleAddClick = () => {
        if (!permissions.course_module_content_management_create) {
            toast.warning("You don't have permission to create survey questions");
            return;
        }
        setShowModal(true);
    };

    return (
        <>
            <div className="row">
                <div className="col-12 mb-2 d-flex justify-content-between align-items-center mt-2">
                    <h4 className="mb-0">Course Survey</h4>
                    <button
                        className="btn btn-primary text-nowrap d-flex align-items-center gap-2"
                        style={{ width: '150px' }}
                        onClick={handleAddClick}
                        disabled={!permissions.course_module_content_management_create}
                        title={!permissions.course_module_content_management_create ? "You don't have permission to create questions" : "Add new question"}
                    >
                        <Icon icon="fluent:add-24-regular" width="20" height="20" />
                        Add Question
                    </button>
                </div>

                <div className="col-12">
                    <div className="card">
                        <div className="card-body">
                            {loading ? (
                                <div className="text-center py-4">
                                    <div className="spinner-border text-primary" role="status">
                                        <span className="visually-hidden">Loading...</span>
                                    </div>
                                </div>
                            ) : apiQuestions.length === 0 ? (
                                <div className="text-center py-4">
                                    <p>No survey questions available</p>
                                </div>
                            ) : (
                                apiQuestions.map((question, index) => (
                                    <div className="card mb-3" key={question.id}>
                                        <div className="row">
                                            <div className="col-12 d-flex justify-content-end">
                                            <div className="d-flex gap-2 p-2">
                                                    <button
                                                        className="btn btn-outline-dark btn-sm border border-dark"
                                                        style={{ width: '40px', height: '40px' }}
                                                        title={!permissions.course_module_content_management_edit ? "You don't have permission to edit questions" : "Edit Question"}
                                                        onClick={() => handleEditClick(question)}
                                                        disabled={!permissions.course_module_content_management_edit}
                                                    >
                                                        <Icon icon="fluent:edit-24-regular" width="16" height="16" />
                                                    </button>
                                                    <button
                                                        className="btn btn-outline-danger btn-sm border border-danger"
                                                        style={{ width: '40px', height: '40px' }}
                                                        title={!permissions.course_module_content_management_delete ? "You don't have permission to delete questions" : "Delete Question"}
                                                        onClick={() => handleDeleteClick(question)}
                                                        disabled={!permissions.course_module_content_management_delete}
                                                    >
                                                        <Icon icon="fluent:delete-24-regular" width="16" height="16" />
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                        <div className="card-body">
                                            <div className="d-flex justify-content-between align-items-start mb-3">
                                                <div className="w-100">
                                                    <div className="d-flex align-items-center gap-2 mb-3">
                                                        <span className="badge bg-primary">{index + 1}</span>
                                                        <h6 className="mb-0">{question.question}</h6>
                                                    </div>
                                                    <ul className="list-unstyled">
                                                        {question.formatted_options && Array.isArray(question.formatted_options) && question.formatted_options.length > 0 ? (
                                                            question.formatted_options.map((option) => (
                                                                <li
                                                                    key={option.key || option.id || Math.random()}
                                                                    className="mb-2 d-flex align-items-center gap-2"
                                                                >
                                                                    <Icon
                                                                        icon="fluent:circle-24-regular"
                                                                        className="text-muted"
                                                                        width="18"
                                                                    />
                                                                    <span>{option.content_value || option.text || option.value || 'No content'}</span>
                                                                </li>
                                                            ))
                                                        ) : (
                                                            <li className="mb-2 d-flex align-items-center gap-2 text-muted">
                                                                <Icon
                                                                    icon="fluent:info-24-regular"
                                                                    className="text-muted"
                                                                    width="18"
                                                                />
                                                                <span>No options available for this question</span>
                                                            </li>
                                                        )}
                                                    </ul>
                                                </div>
                                      
                                            </div>
                                        </div>
                                    </div>
                                ))
                            )}
                        </div>
                    </div>
                </div>
            </div>

            {/* Delete Confirmation Modal */}
            {showDeleteModal && (
                <div className="modal show d-block fade" tabIndex="-1" style={{ backgroundColor: "rgba(0,0,0,0.5)" }}>
                    <div className="modal-dialog">
                        <div className="modal-content">
                            <div className="modal-header">
                                <h5 className="modal-title">Delete Survey Question</h5>
                                <button 
                                    type="button" 
                                    className="btn-close" 
                                    onClick={() => {
                                        setShowDeleteModal(false);
                                        setQuestionToDelete(null);
                                    }}
                                ></button>
                            </div>
                            <div className="modal-body">
                                <p>Are you sure you want to delete this survey question? This action cannot be undone.</p>
                            </div>
                            <div className="modal-footer">
                                <button 
                                    type="button" 
                                    className="btn btn-secondary" 
                                    onClick={() => {
                                        setShowDeleteModal(false);
                                        setQuestionToDelete(null);
                                    }}
                                >
                                    Cancel
                                </button>
                                <button 
                                    type="button" 
                                    className="btn btn-danger"
                                    onClick={handleDeleteConfirm}
                                >
                                    Delete
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            )}

            {/* Question Modal */}
            {showModal && (
                <div className="modal show d-block fade" tabIndex="-1" style={{ backgroundColor: "rgba(0,0,0,0.5)" }}>
                    <div className="modal-dialog">
                        <div className="modal-content">
                            <div className="modal-header">
                                <h5 className="modal-title">
                                    {isEditMode ? 'Edit Survey Question' : 'Add New Survey Question'}
                                </h5>
                                <button 
                                    type="button" 
                                    className="btn-close" 
                                    onClick={() => {
                                        setShowModal(false);
                                        resetForm();
                                    }}
                                ></button>
                            </div>
                            <div className="modal-body">
                                {/* Question Text Input */}
                                <div className="mb-3">
                                    <label className="form-label">Question Text *</label>
                                    <input
                                        type="text"
                                        className={`form-control ${errors.question ? 'is-invalid' : ''}`}
                                        value={questionData.question}
                                        onChange={handleQuestionChange}
                                        placeholder="Enter your survey question"
                                        maxLength={500}
                                    />
                                    {errors.question ? (
                                        <div className="invalid-feedback">{errors.question}</div>
                                    ) : (
                                        <small className="text-muted">Maximum 500 characters allowed ({500 - questionData.question.length} remaining)</small>
                                    )}
                                </div>

                                {/* Options Section */}
                                <div className="mb-3">
                                    <label className="form-label">Options *</label>
                                    {options.map((option, index) => (
                                        <div className="border rounded p-2 mb-2" key={option.id}>
                                            <div className='d-flex justify-content-between align-items-center mb-2'>
                                                <span>Option {index + 1}</span>
                                                <button
                                                    className={`btn p-1 d-flex align-items-center justify-content-center ${options.length <= 1 ? 'bg-primary text-white' : 'text-danger'}`}
                                                    style={{ 
                                                        width: '32px', 
                                                        height: '32px',
                                                        border: 'none',
                                                        borderRadius: '4px'
                                                    }}
                                                    title={options.length <= 1 ? "Cannot delete the only option" : "Delete option"}
                                                    disabled={options.length <= 1}
                                                    onClick={() => handleDeleteOption(index)}
                                                >
                                                    <Icon icon="fluent:delete-24-regular" width="18" height="18" />
                                                </button>
                                            </div>
                                            <input
                                                type="text"
                                                className={`form-control ${errors.options[index] ? 'is-invalid' : ''}`}
                                                placeholder={`Option ${index + 1}`}
                                                value={option.text}
                                                onChange={(e) => handleOptionChange(index, e.target.value)}
                                                maxLength={100}
                                            />
                                            {errors.options[index] ? (
                                                <div className="invalid-feedback">{errors.options[index]}</div>
                                            ) : (
                                                <small className="text-muted">Maximum 100 characters allowed ({100 - option.text.length} remaining)</small>
                                            )}
                                        </div>
                                    ))}
                                    <button type="button" className="btn btn-outline-primary btn-sm" onClick={addOption}>
                                        + Add Option
                                    </button>
                                </div>
                            </div>
                            <div className="modal-footer">
                                <button 
                                    className="btn btn-secondary" 
                                    onClick={() => {
                                        setShowModal(false);
                                        resetForm();
                                    }}
                                >
                                    Cancel
                                </button>
                                <button 
                                    className="btn btn-primary"
                                    onClick={handleSubmit}
                                >
                                    {isEditMode ? 'Update Question' : 'Save Question'}
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            )}
        </>
    );
}

export default CourseSurvey;
