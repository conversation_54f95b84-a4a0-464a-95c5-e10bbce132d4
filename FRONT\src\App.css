/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700;800&display=swap');



/* CSS Variables for consistent theming */
:root {
  /* Font families */
  --font-primary: 'Poppins', sans-serif;
  --font-heading: 'Montserrat', sans-serif;

  /* Font weights */
  --font-weight-light: 300;
  --font-weight-regular: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-weight-extrabold: 800;

  /* Color Palette */
  --primary-color: #3152e8;
  --primary-hover: #2843cc;
  --primary-dark: #233bb8;
  --primary-light: #4c69eb;
  --primary-lighter: #667ef0;
  --secondary-color: #64748b;
  --success-color: #22c55e;
  --danger-color: #ef4444;
  --warning-color: #f59e0b;
  --info-color: #3b82f6;
  
  /* Background Colors */
  --bg-primary: #ffffff;
  --bg-secondary: #f8fafb;
  --bg-tertiary: #e2e8f0;
  
  /* Text Colors */
  --text-primary: #1e293b;
  --text-secondary: #64748b;
  --text-light: #94a3b8;
  
  /* Border Colors */
  --border-color: #e2e8f0;
  --border-focus: #3152e8;
  
  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  
  /* Border Radius */
  --radius-sm: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-full: 9999px;
  
  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
}

/* Dark Mode Variables */
[data-theme='dark'] {
  --primary-color: #4c69eb;
  --primary-hover: #667ef0;
  --primary-dark: #3152e8;
  --primary-light: #7b8ef3;
  --primary-lighter: #8fa0f6;
  
  --bg-primary: #1a1a1a;
  --bg-secondary: #242424;
  --bg-tertiary: #2a2a2a;
  
  --text-primary: #e2e8f0;
  --text-secondary: #94a3b8;
  --text-light: #64748b;
  
  --border-color: #2a2a2a;
  --border-focus: #4c69eb;
  
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.3);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.4);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.5);
}

/* Global Styles */
body {
  margin: 0;
  font-family: var(--font-primary);
  font-weight: var(--font-weight-regular);
  color: var(--text-primary);
  background-color: var(--bg-secondary);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  letter-spacing: 0.2px;
  line-height: 1.6;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-heading);
  font-weight: var(--font-weight-bold);
  letter-spacing: -0.5px;
  line-height: 1.3;
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
}

h1 {
  font-weight: var(--font-weight-extrabold);
}

.fw-medium { font-weight: var(--font-weight-medium) !important; }
.fw-semibold { font-weight: var(--font-weight-semibold) !important; }

/* Primary Button - Reusable Component */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.625rem 1.5rem;
  font-weight: 500;
  font-size: 0.9375rem;
  border: none;
  outline: none;
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: var(--radius-md);
}

.btn-primary {
  background: #4361ee;
  color: white;
  box-shadow: 0 4px 12px rgba(67, 97, 238, 0.2);
}

.btn-primary:hover {
  background: #3451d1;
  box-shadow: 0 6px 16px rgba(67, 97, 238, 0.3);
  transform: translateY(-1px);
}

.btn-primary:active {
  transform: translateY(0);
}

.btn-secondary {
  background: #fff;
  color: #666;
  border: 1px solid #e0e0e0;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
}

.btn-secondary:hover {
  background: #f8f9fa;
  color: #333;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
  transform: translateY(-1px);
}

.btn-secondary:active {
  transform: translateY(0);
}

/* Button Sizes */
.btn-sm {
  padding: 0.375rem 0.75rem !important;
  font-size: 0.875rem !important;
  line-height: 1.5 !important;
  border-radius: 0.25rem !important;
}

/* Ensure small buttons maintain proper dimensions */
.btn-sm.d-flex {
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.btn-lg {
  padding: 0.75rem 2rem;
  font-size: 1rem;
}

/* Button Variants */
.btn-outline-primary {
  background: transparent !important;
  border: 1px solid #4361ee !important;
  color: #4361ee !important;
  box-shadow: none !important;
}

.btn-outline-primary:hover {
  background: #4361ee !important;
  color: white !important;
  border-color: #4361ee !important;
}

.btn-outline-danger {
  background: transparent !important;
  border: 1px solid #dc3545 !important;
  color: #dc3545 !important;
  box-shadow: none !important;
}

.btn-outline-danger:hover {
  background: #dc3545 !important;
  color: white !important;
  border-color: #dc3545 !important;
}

.btn-outline-info {
  background: transparent !important;
  border: 1px solid #0dcaf0 !important;
  color: #0dcaf0 !important;
  box-shadow: none !important;
}

.btn-outline-info:hover {
  background: #0dcaf0 !important;
  color: white !important;
  border-color: #0dcaf0 !important;
}

.btn-outline-success {
  background: transparent !important;
  border: 1px solid #198754 !important;
  color: #198754 !important;
  box-shadow: none !important;
}

.btn-outline-success:hover {
  background: #198754 !important;
  color: white !important;
  border-color: #198754 !important;
}

.btn-text {
  background: transparent;
  color: #4361ee;
  box-shadow: none;
  padding: 0.5rem 1rem;
}

.btn-text:hover {
  background: rgba(67, 97, 238, 0.1);
  transform: none;
}

/* Table Action Button Styles */
.table .btn-sm {
  width: auto !important;
  min-width: 32px !important;
  height: 32px !important;
  padding: 0 !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  border-radius: 0.25rem !important;
}

.table .btn-outline-primary {
  border-color: #4361ee !important;
  color: #4361ee !important;
}

.table .btn-outline-primary:hover {
  background-color: #4361ee !important;
  border-color: #4361ee !important;
  color: white !important;
}

.table .btn-outline-danger {
  border-color: #dc3545 !important;
  color: #dc3545 !important;
}

.table .btn-outline-danger:hover {
  background-color: #dc3545 !important;
  border-color: #dc3545 !important;
  color: white !important;
}

.table .btn-outline-info {
  border-color: #0dcaf0 !important;
  color: #0dcaf0 !important;
}

.table .btn-outline-info:hover {
  background-color: #0dcaf0 !important;
  border-color: #0dcaf0 !important;
  color: white !important;
}

.table .btn-outline-success {
  border-color: #198754 !important;
  color: #198754 !important;
}

.table .btn-outline-success:hover {
  background-color: #198754 !important;
  border-color: #198754 !important;
  color: white !important;
}




/* Button States */
.btn:disabled,
.btn:disabled:hover,
.btn[disabled],
.btn[disabled]:hover { 
  background-color: #e9ecef !important;
  color: #6c757d !important;  
  cursor: not-allowed !important;
  transform: none !important;
  opacity: 0.65 !important;
  box-shadow: none !important;
  pointer-events: all !important;
}

.btn.loading:disabled {
  background-color: var(--primary-color) !important;
  color: white !important;
}

[data-theme='dark'] .btn:disabled {
  background-color: #2D3748 !important;
  color: #718096 !important;
}

[data-theme='dark'] .btn.loading:disabled {
  background-color: var(--primary-color) !important;
  color: white !important;
}

/* Spinner Animation */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.btn.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0.9;
  cursor: not-allowed;
}

.btn .spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2.5px solid #ffffff;
  border-radius: 50%;
  border-top-color: transparent;
  animation: spin 0.8s linear infinite;
  vertical-align: middle;
  margin-right: 8px;
}

/* Dark Mode Adjustments */
[data-theme='dark'] .btn-outline-primary {
  border-color: #4895ef;
  color: #4895ef;
}

[data-theme='dark'] .btn-outline-primary:hover {
  background: #4895ef;
  color: white;
}

[data-theme='dark'] .btn-text {
  color: #4895ef;
}

[data-theme='dark'] .btn-text:hover {
  background: rgba(72, 149, 239, 0.1);
}

/* ----------------------------------------------------------------------- Auth Css  */


.auth-form .btn-primary {
  margin-top: var(--spacing-xs);
  margin-bottom: var(--spacing-xs);
}

.auth-forgot-password {
  text-align: right;
  margin: 0;
  padding: 0;
}

.auth-forgot-password a {
  color: #7c8db5;
  font-size: 0.875rem;
  text-decoration: none;
  transition: color 0.2s ease;
  display: inline-block;
  padding: 0.25rem 0;
}

.auth-forgot-password a:hover {
  color: #4361ee;
}

.auth-options {
  margin-top: var(--spacing-xs);
  text-align: center;
  color: #7c8db5;
  font-size: 0.875rem;
}

/* Form Controls */
.form-control {
  padding: 0.625rem 0.875rem;
  border-radius: var(--radius-md);
  border: 1px solid var(--border-color);
  background-color: var(--bg-primary);
  color: var(--text-primary);
  transition: all 0.2s ease-in-out;
}

.form-control:focus {
  border-color: var(--border-focus);
  box-shadow: 0 0 0 2px rgba(76, 105, 235, 0.1);
  background-color: var(--bg-primary);
  color: var(--text-primary);
}

/* Card Styles */
.card {
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  transition: all 0.2s ease-in-out;
  /* margin-bottom: var(--spacing-lg); */
}

/* .card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
} */

.card-header {
  background: transparent;
  border-bottom: 1px solid var(--border-color);
  padding: var(--spacing-md) var(--spacing-lg);
}

.card-header h5, 
.card-header h6 {
  margin-bottom: 0;
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.card-header h5 .iconify,
.card-header h6 .iconify {
  font-size: 1.25rem;
  color: var(--primary-color);
}

.card-body {
  padding: var(--spacing-lg);
  color: var(--text-primary);
}

.card-footer {
  background: transparent;
  border-top: 1px solid var(--border-color);
  padding: var(--spacing-md) var(--spacing-lg);
}

/* Card Variants */
.card.card-hover-primary:hover {
  border-color: var(--primary-color);
}

.card.card-hover-success:hover {
  border-color: var(--success-color);
}

.card.card-hover-danger:hover {
  border-color: var(--danger-color);
}

/* Card with Icons */
.card-icon {
  width: 40px;
  height: 40px;
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
}

.card-icon-primary {
  background: rgba(49, 82, 232, 0.1);
  color: var(--primary-color);
}

.card-icon-success {
  background: rgba(34, 197, 94, 0.1);
  color: var(--success-color);
}

.card-icon-danger {
  background: rgba(239, 68, 68, 0.1);
  color: var(--danger-color);
}

/* Card Stats */
.card-stats {
  padding: var(--spacing-md);
}

.card-stats .stat-value {
  font-size: 1.75rem;
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.card-stats .stat-label {
  color: var(--text-secondary);
  font-size: 0.875rem;
}

/* Dark Mode Adjustments */
[data-theme='dark'] .card {
  background: var(--bg-primary);
  border-color: var(--border-color);
}

[data-theme='dark'] .card-header,
[data-theme='dark'] .card-footer {
  border-color: var(--border-color);
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .card-body {
    padding: var(--spacing-md);
  }
  
  .card-header,
  .card-footer {
    padding: var(--spacing-sm) var(--spacing-md);
  }
  
  .card-stats .stat-value {
    font-size: 1.5rem;
  }
}

/* Course Card Specific Styles */
.course-card {
  border-radius: var(--radius-lg);
  overflow: hidden;
  transition: transform 0.3s ease;
}

.course-card:hover {
  transform: translateY(-5px);
}

.course-image {
  position: relative;
  overflow: hidden;
  padding-top: 56.25%; /* 16:9 Aspect Ratio */
}

.course-image img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.course-details {
  padding: var(--spacing-sm);
}

.course-title {
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.course-instructor {
  color: var(--text-secondary);
  font-size: 0.9rem;
  margin-bottom: var(--spacing-md);
}

/* Navigation */
.nav-tabs {
  border-bottom: none;
  gap: 0.5rem;
  background: #fff;
  margin-bottom: 0;
}

.nav-tabs .nav-item {
  margin-bottom: -1px;
}

.nav-tabs .nav-link {
  border: none;
  border-bottom: 2px solid transparent;
  background: none;
  color: #6c757d;
  padding: 0.75rem 1.25rem;
  font-weight: 500;
  font-size: 0.9375rem;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.nav-tabs .nav-link:hover {
  border-color: #4361ee;
  color: #4361ee;
}

.nav-tabs .nav-link.active {
  color: #4361ee;
  background: none;
  border-bottom: 2px solid #4361ee;
}

/* Add a border bottom to the tab header container */
/* .tab-header {
  border-bottom: 3px solid #e0e0e0;
  margin-bottom: 1.5rem;
} */

.tab-content {
  background: #fff;
}

/* .tab-pane {
  padding: 1.5rem 0;
} */

/* Tab Responsive Styles */
@media (max-width: 768px) {
  .nav-tabs {
    flex-wrap: nowrap;
    overflow-x: auto;
    overflow-y: hidden;
    -webkit-overflow-scrolling: touch;
    -ms-overflow-style: -ms-autohiding-scrollbar;
    padding-bottom: 5px;
  }

  .nav-tabs::-webkit-scrollbar {
    height: 3px;
  }

  .nav-tabs::-webkit-scrollbar-track {
    background: #f1f1f1;
  }

  .nav-tabs::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
  }

  .nav-tabs .nav-link {
    white-space: nowrap;
    padding: 0.75rem 1rem;
  }
}

/* Profile Styles */
.profile-container {
  background-color: var(--bg-primary);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
}

.profile-image {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  object-fit: cover;
  border: 3px solid var(--border-color);
}

/* Utilities */
.text-primary { color: var(--primary-color) !important; }
.text-secondary { color: var(--secondary-color) !important; }
.text-success { color: var(--success-color) !important; }
.text-danger { color: var(--danger-color) !important; }
.text-warning { color: var(--warning-color) !important; }
.text-info { color: var(--info-color) !important; }

.bg-primary { background-color: var(--primary-color) !important; }
.bg-secondary { background-color: var(--secondary-color) !important; }

/* Animation Classes */
.fade-in {
  animation: fadeIn 0.3s ease-in;
}

.slide-up {
  animation: slideUp 0.3s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { transform: translateY(20px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

/* Responsive Utilities */
@media (max-width: 768px) {
  .btn {
    padding: 0.5rem 1rem;
  }
  
  .section-padding {
    padding: var(--spacing-lg) 0;
  }
  
  .course-details {
    padding: var(--spacing-md);
  }
  .profile-container {
    padding: var(--spacing-sm);
  }
}

/* Auth Pages Styles */
.auth-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  background-color: #F5F7FF;
}

.auth-logo {
  margin-bottom: 2rem;
}

.auth-card {
  background: #FFFFFF;
  border-radius: 16px;
  padding: 32px;
  width: 100%;
  max-width: 420px;
  box-shadow: 0px 4px 20px rgba(0, 0, 0, 0.05);
}

.auth-header {
  text-align: center;
  margin-bottom: 32px;
}

.auth-header h2 {
  color: #101828;
  font-size: 24px;
  font-weight: 600;
  line-height: 32px;
  margin: 0 0 8px 0;
}

.auth-header p {
  color: #667085;
  font-size: 16px;
  line-height: 24px;
  margin: 0;
}

.auth-form {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.auth-form-group {
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 6px;
  min-height: 72px;
  margin-bottom: 16px;
}

.auth-form-group label {
  color: #344054;
  font-size: 14px;
  font-weight: 500;
  line-height: 20px;
}

.auth-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

/* Auth Input Styles */
.auth-input {
  width: 100%;
  height: 44px;
  padding: 10px 14px 10px 42px;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  background: #FFFFFF;
  font-size: 16px;
  line-height: 24px;
  color: #101828;
  transition: all 0.15s ease-in-out;
}

.auth-input::placeholder {
  color: #667085;
}

.auth-input-icon {
  position: absolute;
  left: 14px;
  color: #667085;
  font-size: 20px;
}

/* Auth Input Focus States */
.auth-input:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 4px rgba(67, 24, 255, 0.1);
  outline: none;
}

/* Password Toggle Button */
.auth-password-toggle {
  position: absolute;
  right: 14px;
  background: none;
  border: none;
  padding: 0;
  color: #667085;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.auth-password-toggle:hover {
  color: #101828;
}

/* Auth Links */
.auth-forgot-password {
  text-align: right;
  margin-top: -8px;
}

.auth-forgot-password a {
  color: var(--primary-color);
  text-decoration: none;
  font-size: 14px;
  line-height: 20px;
}

.auth-options {
  text-align: center;
  color: #667085;
  font-size: 14px;
  line-height: 20px;
}

.auth-options a {
  color: var(--primary-color);
  text-decoration: none;
  font-weight: 500;
}

/* Disabled States */
.disabled-link {
  opacity: 0.6;
  pointer-events: none;
}

input:disabled,
button:disabled {
  background-color: var(--disabled-bg);
  cursor: not-allowed;
  opacity: 0.7;
}

/* Dark Mode Auth Styles */
[data-theme='dark'] .auth-input {
  background: var(--bg-primary);
  border-color: var(--border-color);
  color: var(--text-primary);
}

[data-theme='dark'] .auth-input::placeholder {
  color: var(--text-light);
}

[data-theme='dark'] .auth-input-icon {
  color: var(--text-light);
}

[data-theme='dark'] .auth-password-toggle {
  color: var(--text-light);
}

[data-theme='dark'] .auth-password-toggle:hover {
  color: var(--text-primary);
}

/* Phone input specific styles */
.phone-input-container {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.country-code-selector {
  position: relative;
  min-width: 90px;
}

.country-code-button {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: var(--bg-secondary);
  border: 1.5px solid var(--border-color);
  border-radius: var(--radius-md);
  padding: var(--spacing-sm) var(--spacing-md);
  font-size: 0.875rem;
  color: var(--text-primary);
  cursor: pointer;
  width: 100%;
  gap: var(--spacing-xs);
}

.country-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  width: 300px;
  max-height: 300px;
  background: var(--bg-primary);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-lg);
  z-index: 100;
  margin-top: var(--spacing-xs);
  border: 1px solid var(--border-color);
}

.country-search {
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--border-color);
}

.country-search input {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-sm) var(--spacing-sm) var(--spacing-xl);
  border: 1px solid var(--border-color);
  background: var(--bg-secondary);
  border-radius: var(--radius-md);
  font-size: 0.875rem;
  color: var(--text-primary);
}

.country-search input:focus {
  outline: none;
  border-color: var(--primary-color);
  background: var(--bg-primary);
}

.country-list {
  max-height: 210px;
  overflow-y: auto;
}

.country-item {
  padding: var(--spacing-sm) var(--spacing-md);
  font-size: 0.875rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  color: var(--text-primary);
}

.country-item:hover {
  background: var(--bg-secondary);
}

/* Password Requirements Badges */
.password-requirements {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 8px;
}

.requirement-badge {
  display: inline-flex;
  align-items: center;
  padding: 4px 12px;
  background: #F2F4F7;
  border-radius: 16px;
  font-size: 12px;
  color: #667085;
  transition: all 0.2s ease;
}

.requirement-badge.valid {
  background: #ECFDF3;
  color: #027A48;
}

.requirement-badge .icon {
  margin-right: 4px;
  font-size: 14px;
}

.requirement-badge.valid .icon {
  color: #12B76A;
}

/* Dark mode adjustments */
[data-theme='dark'] .requirement-badge {
  background: #2D3748;
  color: #A0AEC0;
}

[data-theme='dark'] .requirement-badge.valid {
  background: #1C4532;
  color: #48BB78;
}

@media (max-width: 480px) {
  .auth-container {
    padding: 1rem;
  }
  
  .auth-card {
    padding: 24px;
  }

  .password-requirements {
    gap: 6px;
  }
  
  .requirement-badge {
    padding: 3px 10px;
    font-size: 11px;
  }
}

@keyframes slideDown {
  from {
    transform: translateY(-20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Dark mode adjustments */
[data-theme='dark'] .auth-container {
  background-color: #1a1b1e;
}

[data-theme='dark'] .auth-card {
  background: #25262b;
  box-shadow: 0px 4px 20px rgba(0, 0, 0, 0.15);
}

[data-theme='dark'] .auth-input {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.1);
  color: white;
}

[data-theme='dark'] .auth-input:focus {
  background: rgba(255, 255, 255, 0.08);
  border-color: #4361ee;
}

[data-theme='dark'] .auth-input-icon {
  color: rgba(255, 255, 255, 0.5);
}

[data-theme='dark'] .auth-forgot-password a,
[data-theme='dark'] .auth-options {
  color: rgba(255, 255, 255, 0.6);
}

[data-theme='dark'] .auth-options a {
  color: #4895ef;
}


/* ----------------------------------------------------------------------------- UserCss  */

/* Breadcrumbs Styles */
.breadcrumb {
  margin: 0;
  padding: 0;
}

.breadcrumb-item {
  display: flex;
  align-items: center;
}

/* Header Styles */
.header-container {
  background-color: white;
  padding: 0.75rem 1rem;
  color: #212529;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02);
  width: 100%;
}

.user-dropdown {
  position: relative;
}

.user-dropdown button {
  background-color: white;
  border: none;
  padding: 0.2rem 0.6rem;
  border-radius: 8px;
  transition: all 0.2s ease;
  color: #212529;
  display: flex;
  align-items: center;
}

.user-dropdown .user-name {
  font-size: 1rem;
  font-weight: 500;
  letter-spacing: 0.2px;
}

.user-dropdown button:focus {
  box-shadow: none;
}

.user-dropdown .dropdown-menu {
  margin-top: 0.5rem;
  border-radius: 8px;
  border: none;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.user-dropdown .dropdown-item {
  padding: 0.75rem 1rem;
  font-weight: 500;
  transition: all 0.2s ease;
}

.user-dropdown .dropdown-item:hover {
  background-color: #f8f9fa;
}

.user-dropdown .dropdown-item.text-danger:hover {
  background-color: #fff5f5;
}

/* Notification Bell Styles */
.notification-bell-container {
  position: relative;
  display: flex;
  align-items: center;
}

.notification-bell {
  color: #666;
  background-color: #f2f2f2;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  transition: all 0.2s ease;
}

.notification-bell:hover {
  color: var(--color-primary);
}

.bell-icon-wrapper {
  display: inline-block;
  transform-origin: top center;
}

.notification-badge {
  position: absolute;
  top: -4px;
  right: -4px;
  background-color: #3152e8;
  color: white;
  border-radius: 50%;
  font-size: 10px;
  width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  border: 1.5px solid white;
}

/* Bell Animation */
@keyframes autoshake {
  0%, 100% { transform: rotate(0deg); }
  10% { transform: rotate(20deg); }
  20% { transform: rotate(-20deg); }
  30% { transform: rotate(2.5deg); }
  40% { transform: rotate(-2.5deg); }
  50% { transform: rotate(2deg); }
  60% { transform: rotate(-2deg); }
  70% { transform: rotate(1deg); }
  80% { transform: rotate(-1deg); }
  90% { transform: rotate(0.5deg); }
}

.bell-icon {
  animation: autoshake 2s ease infinite;
  transform-origin: top center;
}

/* Sidebar Styles */
.sidebar-nav {
  background-color: #fff;
  height: calc(100vh - 78px);
  overflow-y: auto;
  width: 100%;
}

.nav-item {
  border-radius: 8px;
  margin: 4px 8px;
  font-family: var(--font-primary);
  font-weight: var(--font-weight-medium);
  color: #555;
  transition: all 0.2s ease;
  text-decoration: none;
  position: relative;
}

.nav-item:hover {
  background-color: rgba(13, 110, 253, 0.05);
  color: #0d6efd;
}

.nav-item.active {
  background-color: var(--primary-color);
  color: white;
}

.nav-icon-wrapper {
  width: 36px;
  height: 36px;
  border-radius: 8px;
  color: inherit;
}

.nav-text {
  font-size: 15px;
  letter-spacing: 0.2px;
}

.mobile-sidebar {
  background-color: #fff;
  height: 100vh;
}

.nav-item.text-danger {
  color: #dc3545;
}

.nav-item.text-danger:hover {
  background-color: rgba(220, 53, 69, 0.05);
  color: #dc3545;
}

/* Custom scrollbar */
.sidebar-nav::-webkit-scrollbar {
  width: 5px;
}

.sidebar-nav::-webkit-scrollbar-track {
  background: transparent;
}

.sidebar-nav::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 10px;
}

.sidebar-nav::-webkit-scrollbar-thumb:hover {
  background-color: rgba(0, 0, 0, 0.2);
}

/* Mobile Styles */
@media (max-width: 767.98px) {
  .header-container {
    padding: 0.5rem;
  }
  
  .user-dropdown button {
    padding: 0.25rem;
  }
  
  .user-dropdown .user-name {
    display: none;
  }
  
  .user-dropdown .dropdown-menu {
    position: absolute;
    right: 0;
    left: auto;
  }

  .nav[aria-label="breadcrumb"] {
    padding: 0.75rem 1rem !important;
  }
  
  .nav[aria-label="breadcrumb"] h4 {
    font-size: 1.1rem;
  }
}

/* ---------------------------------------------------------- card css for all pages use this  */

/* Primary Button Styles */
.primary-btn {
  background-color: #4361ee !important;
  border: none !important;
  color: white !important;
  padding: 0.75rem 1.5rem !important;
  font-size: 0.9375rem !important;
  font-weight: 500 !important;
  transition: all 0.2s ease !important;
  height: 44px !important;
}

.primary-btn:hover {
  background-color: #2541df !important;
  box-shadow: 0 4px 8px rgba(67, 97, 238, 0.2) !important;
}

.primary-btn:active {
  transform: translateY(1px) !important;
}

.primary-btn:focus {
  box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.3) !important;
}

/* Profile specific styles */
.profile-image-container {
  margin-bottom: 1.5rem;
  position: relative;
}

.profile-image {
  width: 120px;
  height: 120px;
  object-fit: cover;
  border: 2px solid #e0e0e0;
  padding: 4px;
}

/* Modal Styles - For all modules */
.modal {
  display: flex !important;
  align-items: center;
  justify-content: center;
}

.modal-dialog {
  max-width: 600px;
  margin: 1.75rem auto;
  width: 100%;
  transition: transform 0.3s ease-out;
}

.modal.fade .modal-dialog {
  transform: translateY(-100px) !important;
  opacity: 0;
  transition: all 0.3s ease-out;
  justify-content: center;
}

.modal.show .modal-dialog {
  transform: translateY(0) !important;
  opacity: 1;
}

.modal-content {
  border: none;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  background: #fff;
  width: 100%;
  position: relative;
  animation: modalContent 0.3s ease-out;
}

@keyframes modalContent {
  from {
    opacity: 0;
    transform: translateY(-50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.modal-header {
  background-color: #f8f9fa;
  border-bottom: 1px solid #e0e0e0;
  border-radius: 12px 12px 0 0;
  padding: 1rem 1.5rem;
}

.modal-title {
  font-weight: 600;
  color: #333;
  font-size: 1.25rem;
  margin: 0;
}

.modal-body {
  padding: 1.5rem;
  max-height: calc(100vh - 210px);
  overflow-y: auto;
}

.modal-footer {
  background-color: #f8f9fa;
  border-top: 1px solid #e0e0e0;
  border-radius: 0 0 12px 12px;
  padding: 1rem 1.5rem;
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
}

/* Form Controls inside Modal */
.modal-body .form-label {
  font-weight: 500;
  color: #333;
  margin-bottom: 0.5rem;
}

.modal-body .form-control,
.modal-body .form-select {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 0.75rem 1rem;
  font-size: 0.9375rem;
  width: 100%;
  transition: all 0.2s ease;
}

.modal-body .form-control:focus,
.modal-body .form-select:focus {
  border-color: #4361ee;
  box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.1);
}

.modal-body textarea.form-control {
  min-height: 100px;
  resize: vertical;
}

/* File input styling */
.modal-body .form-control[type="file"] {
  padding: 0.5rem;
  border: 2px dashed #e0e0e0;
  background: #f8f9fa;
}

.modal-body .form-control[type="file"]:hover {
  border-color: #4361ee;
  background: #f1f3ff;
}

/* Modal Buttons */
.modal-footer .btn {
  padding: 0.75rem 1.5rem;
  font-weight: 500;
  border-radius: 8px;
  min-width: 120px;
}

.modal-footer .btn-primary {
  background-color: #4361ee;
  border: none;
  color: white;
}

.modal-footer .btn-primary:hover {
  background-color: #2541df;
}

.modal-footer .btn-secondary {
  background-color: #fff;
  border: 1px solid #e0e0e0;
  color: #666;
}

.modal-footer .btn-secondary:hover {
  background-color: #f8f9fa;
  color: #333;
}

/* Modal Scrollbar */
.modal-body::-webkit-scrollbar {
  width: 6px;
}

.modal-body::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.modal-body::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.modal-body::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Modal Responsive */
@media (max-width: 768px) {
  .modal-dialog {
    margin: 1rem;
  }
  
  .modal-body {
    padding: 1rem;
  }
  
  .modal-footer {
    padding: 1rem;
  }
}

/* ------------------------------------------------------ Country Dropdown Styles */
.country-select-container {
  position: relative;
}

.country-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  z-index: 1000;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  margin-top: 4px;
}

.dropdown-search {
  position: relative;
  padding: 12px;
  border-bottom: 1px solid #e0e0e0;
}

.dropdown-search .search-icon {
  position: absolute;
  left: 24px;
  top: 50%;
  transform: translateY(-50%);
  color: #666;
  font-size: 20px;
  z-index: 1;
}

.dropdown-search .search-input {
  padding-left: 40px !important;
  border-radius: 6px;
  font-size: 0.9375rem;
}

.country-options {
  max-height: 200px;
  overflow-y: auto;
}

.country-option {
  padding: 10px 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.9375rem;
}

.country-option:hover {
  background-color: #f8f9fa;
  color: #4361ee;
}

.no-results {
  padding: 16px;
  text-align: center;
  color: #666;
  font-style: italic;
}

/* Custom scrollbar for country options */
.country-options::-webkit-scrollbar {
  width: 6px;
}

.country-options::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.country-options::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.country-options::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/*------------------------------------------------------------ Image Upload Modal Styles */
.image-upload-modal .modal-body {
  padding: 2rem;
}

.preview-container {
  width: 200px;
  height: 200px;
  margin: 0 auto;
  position: relative;
}

.preview-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border: 2px solid #e0e0e0;
}

.upload-placeholder {
  width: 100%;
  height: 100%;
  border: 2px dashed #e0e0e0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  color: #666;
}

.placeholder-icon {
  font-size: 48px;
  margin-bottom: 1rem;
  color: #999;
}

.upload-label {
  display: inline-flex;
  align-items: center;
  padding: 0.75rem 1.5rem;
  background: #4361ee;
  color: white;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-weight: 500;
}

.upload-label:hover {
  background: #2541df;
  transform: translateY(-1px);
}

/* Spinner Styles */
.spinner {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #fff;
  animation: spin 0.8s linear infinite;
  margin-right: 8px;
  display: inline-block;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Invalid feedback styles */
.invalid-feedback {
  display: block;
  width: 100%;
  margin-top: 0.25rem;
  font-size: 0.875rem;
  color: #dc3545;
}

.is-invalid {
  border-color: #dc3545 !important;
  padding-right: calc(1.5em + 0.75rem) !important;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e") !important;
  background-repeat: no-repeat !important;
  background-position: right calc(0.375em + 0.1875rem) center !important;
  background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem) !important;
}

.is-invalid:focus {
  border-color: #dc3545 !important;
  box-shadow: 0 0 0 0.25rem rgba(220, 53, 69, 0.25) !important;
}


/* ---------------- chat window  */
.chat-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
  flex-shrink: 0;
}


.country-code-selector {
  min-width: 75px;
  flex-shrink: 0;
}

.invalid-feedback {
  font-size: 0.85rem;
  margin-top: 0.25rem;
}


@media (max-width: 768px) {
  .w-md-50 {
    width: 50% !important;
  }
}

/* Auth Form Error Message Styling */
.auth-error-message {
  color: #dc3545;
  font-size: 0.875rem;
  margin-top: 4px;
  display: block;
}

/* Emoji Reaction Styles */
.emoji-reaction-circle {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  user-select: none;
  transition: all 0.2s ease;
  position: relative;
  font-size: 16px;
  border: 2px solid #dee2e6;
  background-color: #f8f9fa;
}

.emoji-reaction-circle:hover {
  transform: scale(1.1);
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.emoji-reaction-circle.user-reacted {
  border-color: var(--primary-color);
  background-color: var(--primary-color);
  color: white;
}

.emoji-reaction-count {
  position: absolute;
  top: -4px;
  right: -4px;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background-color: var(--danger-color);
  color: white;
  font-size: 10px;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid white;
}

.add-reaction-button {
  width: 32px;
  height: 28px;
  border-radius: 50%;
  border: 2px solid #dee2e6;
  background-color: #ffffff;
  color: #6c757d;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  gap: 1px;
  padding: 0 2px;
  box-shadow: 0 2px 6px rgba(0,0,0,0.1);
}

.add-reaction-button:hover {
  background-color: #e9ecef;
  transform: scale(1.1);
  border-color: #adb5bd;
}

.reaction-picker-container {
  position: relative;
  right: -1px;
  bottom: -26px;
}

.emoji-reactions-container {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  margin-top: 8px;
  align-items: center;
}

/* Approved Button Pulse Animation */
.approved-pulse {
  animation: pulse 2s infinite;
  box-shadow: 0 0 0 rgba(25, 135, 84, 0.4);
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(25, 135, 84, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(25, 135, 84, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(25, 135, 84, 0);
  }
}

/* Drag and Drop Styles for Certificate Upload */
.upload-area {
  border: 2px dashed #dee2e6;
  border-radius: 0.5rem;
  padding: 2rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background-color: #f8f9fa;
}

.upload-area:hover {
  border-color: var(--primary-color);
  background-color: #f0f8ff;
}

.upload-area.dragging {
  border-color: var(--primary-color);
  background-color: #e6f3ff;
  transform: scale(1.02);
}

.document-preview {
  border: 1px solid #dee2e6;
  border-radius: 0.5rem;
  padding: 1rem;
  background-color: #f8f9fa;
}

.document-preview-content {
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}

/* Custom Utility Classes */
.fs-7 {
  font-size: 12px !important;
}

.w-120 {
  width: 120px !important;
}

.cursor-pointer {
  cursor: pointer !important;
}

/* Notification Bell Hover Effect */
.notification-bell:hover .text-primary {
  opacity: 0.8;
  transition: opacity 0.2s ease;
}

/* Remove any old notification badge styles if they exist */
.notification-badge {
  display: none;
}

