import React, { useState, useEffect, useCallback } from 'react'
import { useParams, useLocation, useNavigate } from 'react-router-dom';
import { Icon } from '@iconify/react';
import { decodeData } from '../../../utils/encodeAndEncode';
import { editDocument, getDocumentById } from '../../../services/adminService';
import { toast } from 'react-toastify';
import { usePermissions } from '../../../context/PermissionsContext';

// Utility function to convert blob URL to File
const urlToFile = async (url) => {
    const response = await fetch(url);
    const blob = await response.blob();
    const filename = url.split('/').pop() || 'document.pdf';
    return new File([blob], filename, { type: blob.type });
};

function CourseDocument() {
    const { courseId, moduleId, contentId } = useParams();
    const location = useLocation();
    const navigate = useNavigate();
    const { documentName, pdfUrl } = location.state || {};
    const { permissions } = usePermissions();
    console.log('CourseDocument - Document Data:', {
        documentName,
        pdfUrl
    });


    const decodedCourseId = decodeData(courseId);
    const decodedModuleId = decodeData(moduleId);
    const decodedContentId = decodeData(contentId);

    const [showEditModal, setShowEditModal] = useState(false);
    const [documentData, setDocumentData] = useState({
        name: documentName || 'Untitled Document',
        pdfUrl: pdfUrl || ''
    });
    const [uploadDoc, setUploadDoc] = useState(null);
    const [error, setError] = useState(null);
    const [isSaving, setIsSaving] = useState(false);
    const [errors, setErrors] = useState({
        documentName: '',
        documentFile: ''
    });

    // Define refreshDocumentData first using useCallback
    const refreshDocumentData = useCallback(async () => {
        if (!decodedContentId) return;

        try {
            const response = await getDocumentById({
                doc_id: decodedContentId
            });
            
            if (response.success && response.data) {
                setDocumentData({
                    name: response.data.doc_name || 'Untitled Document',
                    pdfUrl: response.data.doc_url || ''
                });
            } else {
                console.error('Failed to refresh document data');
            }
        } catch (error) {
            console.error('Error refreshing document data:', error);
        }
    }, [decodedContentId]);

    // Use refreshDocumentData in useEffect
    useEffect(() => {
        // Initial fetch
        refreshDocumentData();

        // Set up periodic refresh (every 30 seconds)
        const refreshInterval = setInterval(refreshDocumentData, 30000);

        // Cleanup interval on component unmount
        return () => clearInterval(refreshInterval);
    }, [refreshDocumentData]); // Only depend on refreshDocumentData since it already depends on decodedContentId

    useEffect(() => {
        console.log('CourseDocument - Document Data:', {
            ids: {
                courseId: {
                    encoded: courseId,
                    decoded: decodedCourseId,
                    type: typeof decodedCourseId
                },
                moduleId: {
                    encoded: moduleId,
                    decoded: decodedModuleId,
                    type: typeof decodedModuleId
                },
                contentId: {
                    encoded: contentId,
                    decoded: decodedContentId,
                    type: typeof decodedContentId
                }
            },
            documentInfo: {
                name: documentName,
                url: pdfUrl
            },
            navigationState: location.state
        });

        // Validate IDs and document data
        if (!decodedCourseId || !decodedModuleId || !decodedContentId) {
            console.error('Missing or invalid IDs:', {
                courseId: decodedCourseId,
                moduleId: decodedModuleId,
                contentId: decodedContentId
            });
            setError('Invalid document parameters');
        }

        if (!pdfUrl) {
            console.warn('No PDF URL provided in navigation state');
        }

        // Update document data if received from navigation
        if (documentName || pdfUrl) {
            setDocumentData({
                name: documentName || 'Untitled Document',
                pdfUrl: pdfUrl || ''
            });
        }
    }, [courseId, moduleId, contentId, decodedCourseId, decodedModuleId, decodedContentId, documentName, pdfUrl, location.state]);

    const getDocumentDetailsById = async () => {
        try {
            const response = await getDocumentById({
                doc_id: decodedContentId
            });
            console.log('Document Details:', response);

            if (response.success && response.data) {
                const docData = response.data;
                setDocumentData({
                    name: docData.doc_name || 'Untitled Document',
                    pdfUrl: docData.doc_url || ''
                });

                // Log the updated document data
                console.log('Updated Document Data:', {
                    name: docData.doc_name,
                    url: docData.doc_url,
                    id: docData.id,
                    courseId: docData.course_id,
                    moduleId: docData.module_id
                });
            } else {
                console.error('Failed to fetch document details:', response.message);
                toast.error('Failed to load document details');
            }
        } catch (error) {
            console.error('Error fetching document details:', error);
            toast.error('Error loading document details');
        }
    };

    useEffect(() => {
        if (decodedContentId) {
            getDocumentDetailsById();
        }
    }, [decodedContentId]);

    // Reset errors when modal opens
    useEffect(() => {
        if (showEditModal) {
            setErrors({
                documentName: '',
                documentFile: ''
            });
        }
    }, [showEditModal]);

    if (error) {
        return (
            <div className="alert alert-danger m-3" role="alert">
                {error}
            </div>
        );
    }

    const handleDocumentRemove = () => {
        setDocumentData(prev => ({
            ...prev,
            pdfUrl: ''
        }));
    };

    const handleDocumentUpload = (e) => {
        const file = e.target.files[0];
        if (file) {
            if (file.type !== 'application/pdf') {
                setErrors(prev => ({
                    ...prev,
                    documentFile: 'Please upload a PDF file only'
                }));
                return;
            }

            if (file.size > 10 * 1024 * 1024) { // 10MB
                setErrors(prev => ({
                    ...prev,
                    documentFile: 'File size should not exceed 10MB'
                }));
                return;
            }

            setErrors(prev => ({
                ...prev,
                documentFile: ''
            }));
            setUploadDoc(file);
            setDocumentData(prev => ({
                ...prev,
                pdfUrl: URL.createObjectURL(file)
            }));
        }
    };

    const validateForm = () => {
        const newErrors = {
            documentName: '',
            documentFile: ''
        };
        let isValid = true;

        // Validate document name
        if (!documentData.name.trim()) {
            newErrors.documentName = 'Document Name is required';
            isValid = false;
        } else if (documentData.name.trim().length > 100) {
            newErrors.documentName = 'Document Name cannot exceed 100 characters';
            isValid = false;
        }

        setErrors(newErrors);
        return isValid;
    };

    const handleSaveChanges = async () => {
        if (!validateForm()) {
            return;
        }

        setIsSaving(true);
        console.log('Saving document changes...');
        
        try {
            const formData = new FormData();
            formData.append('doc_id', decodedContentId);
            formData.append('doc_name', documentData.name);

            if (uploadDoc !== null) {
                try {
                    if (typeof uploadDoc === 'string' && uploadDoc.startsWith('blob:')) {
                        console.log('Converting blob URL to File object');
                        const fileObject = await urlToFile(uploadDoc);
                        formData.append('doc_url', fileObject);
                        console.log('File converted:', {
                            name: fileObject.name,
                            type: fileObject.type,
                            size: fileObject.size
                        });
                    } else {
                        formData.append('doc_url', uploadDoc);
                        console.log('Using existing file object');
                    }
                    formData.append('file_changed', 'true');
                } catch (error) {
                    console.error('Error processing file:', error);
                    toast.error('Error processing the file');
                    setIsSaving(false); // Stop loading on error
                    return;
                }
            } else {
                formData.append('doc_url', documentData.pdfUrl);
                formData.append('file_changed', 'false');
            }

            const response = await editDocument(formData);

            if (response.success) {
                // Artificial delay for better UX (2 seconds)
                await new Promise(resolve => setTimeout(resolve, 2000));
                
                toast.success('Document updated successfully!');
                
                try {
                    const updatedDoc = await getDocumentById({
                        doc_id: decodedContentId
                    });
                    
                    if (updatedDoc.success && updatedDoc.data) {
                        console.log('Updated document details:', updatedDoc.data);
                        setDocumentData({
                            name: updatedDoc.data.doc_name || 'Untitled Document',
                            pdfUrl: updatedDoc.data.doc_url || ''
                        });
                        setUploadDoc(null);
                        setShowEditModal(false);
                    } else {
                        console.error('Failed to fetch updated document details');
                        toast.warning('Document updated but failed to refresh display');
                    }
                } catch (error) {
                    console.error('Error fetching updated document:', error);
                    toast.warning('Document updated but failed to refresh display');
                }
            } else {
                toast.error(response.data?.message || 'Failed to update document');
            }
        } catch (error) {
            console.error('Error updating document:', error);
            toast.error('An error occurred while updating the document');
        } finally {
            setIsSaving(false); // Stop loading whether success or error
        }
    };

    const handleCloseModal = () => {
        setShowEditModal(false);
        setErrors({
            documentName: '',
            documentFile: ''
        });
        // Reset the form to original data
        getDocumentDetailsById();
    };

    const handleEditClick = () => {
        if (!permissions.course_module_content_management_edit) {
            toast.warning("You don't have permission to edit documents");
            return;
        }
        setShowEditModal(true);
    };

    return (
        <>
            <div className="row">
                <div className="col-12">
                    {/* Header with Edit Button */}
                    <div className="col-12 mb-2 d-flex justify-content-between align-items-center mt-2">
                        <h4 className="mb-0">Course Document</h4>
                        <div className="d-flex align-items-center gap-2">
                            <button 
                                className="btn btn-primary text-nowrap" 
                                style={{ width: '150px' }}
                                onClick={handleEditClick}
                                disabled={!permissions.course_module_content_management_edit}
                                title={!permissions.course_module_content_management_edit ? "You don't have permission to edit documents" : "Edit Document"}
                            >
                                Edit Document
                            </button>
                        </div>
                    </div>
                </div>

                {/* Document Name Display */}
                <div className="col-12 mb-3">
                    <div className="card">
                        <div className="card-body">
                            <p className="mb-0 text-muted">{documentData.name}</p>
                        </div>
                    </div>
                </div>

                {/* Document Preview */}
                <div className="col-12">
                    <div className="card">
                        <div className="card-body p-0">
                            {documentData.pdfUrl ? (
                                <div style={{ maxWidth: '800px', margin: '0 auto' }}>
                                    <div className="ratio ratio-4x3">
                                        <iframe
                                            src={documentData.pdfUrl}
                                            title="Document Viewer"
                                            className="w-100 h-100 border-0"
                                            style={{ backgroundColor: '#f8f9fa' }}
                                            allowFullScreen
                                        >
                                            This browser does not support PDFs. Please download the PDF to view it.
                                        </iframe>
                                    </div>
                                </div>
                            ) : (
                                <div className="text-center p-4">
                                    <p>No document available</p>
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            </div>

            {/* Edit Document Modal */}
            {showEditModal && (
                <div className="modal show d-block" style={{ backgroundColor: 'rgba(0,0,0,0.5)' }}>
                    <div className="modal-dialog modal-lg">
                        <div className="modal-content">
                            <div className="modal-header">
                                <h5 className="modal-title">Edit Document</h5>
                                <button 
                                    type="button" 
                                    className="btn-close"
                                    onClick={handleCloseModal}
                                ></button>
                            </div>
                            <div className="modal-body">
                                {/* Document Name Field */}
                                <div className="mb-4">
                                    <label className="form-label">Document Name *</label>
                                    <input
                                        type="text"
                                        className={`form-control ${errors.documentName ? 'is-invalid' : ''}`}
                                        value={documentData.name}
                                        onChange={(e) => {
                                            setDocumentData(prev => ({
                                                ...prev,
                                                name: e.target.value
                                            }));
                                            if (errors.documentName) {
                                                setErrors(prev => ({
                                                    ...prev,
                                                    documentName: ''
                                                }));
                                            }
                                        }}
                                        maxLength={100}
                                        placeholder="Enter document name"
                                    />
                                    {errors.documentName ? (
                                        <div className="invalid-feedback">{errors.documentName}</div>
                                    ) : (
                                        <small className="text-muted">Maximum 100 characters allowed ({100 - documentData.name.length} remaining)</small>
                                    )}
                                </div>

                                {/* Document Upload */}
                                <div className="card mb-3">
                                    <div className="card-body">
                                        <div className="text-center p-4">
                                            <input
                                                type="file"
                                                id="documentUpload"
                                                className="d-none"
                                                accept=".pdf"
                                                onChange={handleDocumentUpload}
                                            />
                                            <label 
                                                htmlFor="documentUpload" 
                                                className="btn btn-outline-primary mb-3 d-flex flex-column align-items-center gap-2 mx-auto"
                                                style={{ width: 'fit-content', cursor: 'pointer' }}
                                            >
                                                <Icon icon="fluent:document-pdf-24-regular" width="48" height="48" />
                                                <span>Upload New PDF Document</span>
                                                <small className="text-muted">Maximum file size: 10MB</small>
                                            </label>
                                            {errors.documentFile && (
                                                <div className="alert alert-danger py-2 mt-2">{errors.documentFile}</div>
                                            )}
                                        </div>
                                    </div>
                                </div>

                                {/* Current Document Preview */}
                                {documentData.pdfUrl && (
                                    <div className="card mb-3">
                                        <div className="card-body">
                                            <div className="d-flex justify-content-between align-items-center mb-3">
                                                <h6 className="mb-0">Current Document Preview</h6>
                                             
                                            </div>
                                            <div className="ratio ratio-16x9">
                                                <iframe
                                                    src={documentData.pdfUrl}
                                                    title="Document Preview"
                                                    className="w-100 h-100 border-0 rounded"
                                                    allowFullScreen
                                                >
                                                </iframe>
                                            </div>
                                        </div>
                                    </div>
                                )}
                            </div>
                            <div className="modal-footer">
                                <button 
                                    type="button" 
                                    className="btn btn-secondary"
                                    onClick={handleCloseModal}
                                    disabled={isSaving}
                                >
                                    Cancel
                                </button>
                                <button 
                                    type="button" 
                                    className="btn btn-primary d-flex align-items-center gap-2"
                                    onClick={handleSaveChanges}
                                    disabled={isSaving}
                                >
                                    {isSaving ? (
                                        <>
                                            <span className="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                            <span>Saving...</span>
                                        </>
                                    ) : (
                                        'Save Changes'
                                    )}
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            )}
        </>
    );
}

export default CourseDocument;
