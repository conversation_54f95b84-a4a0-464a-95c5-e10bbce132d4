const { body } = require("express-validator");
const bcrypt = require("bcrypt");
const jwt = require("jsonwebtoken");
const multer = require("multer");
const { mysqlServerConnection, retryQuery } = require("../db/db");
const { registration } = require("../Organization/controller/accounts/orgUser");
const upload = multer();
require("dotenv").config();

const registerValidation = (req, res, next) => {
  const { email, mobile, password, organization_id, role_id } = req.body;

  let errors = {};

  if (!email) errors.email = "Email is required";
  if (!mobile) errors.mobile = "Mobile is required";
  if (!password) errors.password = "Password is required";
  if (!organization_id) errors.organization_id = "Organization ID is required";
  if (!role_id) errors.role_id = "Role ID is required";

  if (Object.keys(errors).length > 0) {
    return res.status(422).json({
      success: false,
      data: {
        error_msg: "The following fields are required",
        response: errors,
      },
    });
  }

  next();
};

const loginValidation = () => {
  return [
    body("password")
      .isLength({ min: 8 })
      .withMessage("Password must be at least 8 characters long"),
  ];
};

const isTokenBlacklisted = async (token, db_name) => {
  try {
    const [rows] = await retryQuery(async () => {
      return await mysqlServerConnection.query(
        `SELECT * FROM ${db_name}.token_blacklist WHERE token = ? AND expires_at > NOW()`,
        [token]
      );
    });
    return rows.length > 0;
  } catch (error) {
    console.log('Error checking token blacklist:', error);
    // If database is unavailable, assume token is not blacklisted to avoid blocking users
    return false;
  }
};

const authenticateJWT = async (req, res, next) => {
  const token = req.header("Authorization");

  if (!token) {
    return res
      .status(401)
      .json({ success: false, error_msg: "Access denied. No token provided." });
  }

  try {
    const decoded = jwt.verify(token, process.env.JWT_ACCESS_KEY);
    const blacklisted = await isTokenBlacklisted(token, decoded.db_name);
    if (blacklisted) {
      return res.status(401).json({
        success: false,
        data: { error_msg: "Token expired. Please log in again." },
      });
    }

    // console.log("DECODED",decoded);
    req.user = decoded;
    next();
  } catch (ex) {
    console.log('JWT Error:', ex);
    if (ex.name === 'TokenExpiredError') {
      return res.status(401).json({ 
        success: false, 
        error_msg: 'Your session has expired. Please log in again.',
        error_type: 'TOKEN_EXPIRED'
      });
    }
    res.status(401).json({ 
      success: false, 
      error_msg: 'Invalid or expired token. Please log in again.',
      error_type: 'INVALID_TOKEN'
    });
  }
};

const authorizePermissions = (...requiredPermissions) => {
  return (req, res, next) => {
    const { permissions } = req.user;
    const hasPermission = requiredPermissions.every((permission) =>
      permissions.includes(permission)
    );
    if (!hasPermission) {
      return res.status(403).json({
        success: false,
        allowed: false,
        error_msg: "Access denied. You do not have the required permissions.",
      });
    }
    next();
  };
};

module.exports = {
  registerValidation,
  loginValidation,
  authenticateJWT,
  authorizePermissions,
};
