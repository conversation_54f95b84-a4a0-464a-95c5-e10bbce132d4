const { mysqlServerConnection } = require('../../../db/db');
const { sendAccountCreatedEmail } = require('../../../tools/emailtemplates');
const { deleteFileIfExists, hashPassword, validateEmail, validatePassword, sendEmail } = require('../../../tools/tools');
const { uploadImageToS3 } = require('../../../tools/aws');
const maindb = process.env.MAIN_DB
const XLSX = require("xlsx");
const bcrypt = require('bcrypt');


// TraineeAnalytics function is defined below
const getOrgProfileDetails = async (req, res, next) => {
    try {
        if (!req.user) {
            return next({ statusCode: 404, message: 'User not found' });
        }

        // Fetch only specific user details
        const [user] = await mysqlServerConnection.query(
            `SELECT id, profile_pic_url, profession, qualification, skill, experience, name, mobile, email, country, state, zipcode, address, language 
            FROM ${req.user.db_name}.users 
            WHERE id = ?`,
            [req.user.userId]
        );

        if (user.length === 0) {
            return next({ statusCode: 404, message: 'User not found' });
        }

        // Fetch user roles and permissions
        const [permissions] = await mysqlServerConnection.query(`
            SELECT p.name 
            FROM ${req.user.db_name}.role_permissions rp
          
             JOIN ${maindb}.permissions p ON rp.permission_id = p.id
            JOIN ${req.user.db_name}.user_roles ur ON ur.role_id = rp.role_id
            WHERE ur.user_id = ?;
        `, [req.user.userId]);

        // Collect permission names into an array
        const permissionNames = permissions.map(permission => permission.name);

        return res.status(200).json({
            success: true,
            data: {
                user: {
                    id:user[0].id,
                    profile_pic_url: user[0].profile_pic_url,
                    profession: user[0].profession,
                    qualification: user[0].qualification,
                    skill: user[0].skill,
                    experience: user[0].experience,
                    name: user[0].name,
                    mobile: user[0].mobile,
                    email: user[0].email,
                    country: user[0].country,
                    state: user[0].state,
                    zipcode: user[0].zipcode,
                    address: user[0].address,
                    language: user[0].language
                },
                permissions: permissionNames,
            }
        });
    } catch (error) {
        return next({ statusCode: 500, message: error.message || 'Internal Server Error' });
    }
};

const deleteOrgProfileDetails = async (req, res, next) => {

    try {
        const { user_id } = req.params;
        console.log("userId from deleteOrgProfileDetails:", user_id)
        if (!user_id) {
            return next({ statusCode: 404, message: "User with provided ID does not exist" })
        }

        const [result] = await mysqlServerConnection.query(`UPDATE ${req.user.db_name}.users SET is_deleted = 1 where id = ?`, [user_id]);
        if (result["changedRows"] == 0) {
            return next({ statusCode: 404, message: "The provided User id does not exist or the user is aldready deleted" })
        }
        console.log("SUCESS")
        res.status(200).json({
            success: true,
            message: `User with ID:${user_id} deleted successfully`
        })
    } catch (error) {
        return next({ statusCode: 500, message: error.message || 'Internal Server Error' })
    }
}

const editOrgProfileDetails = async (req, res, next) => {

    try {
        const { profession, qualification, experience, skill, name, email, mobile, country, state, zipcode, language, address } = req.body;
        const file = req.files["profile_pic"] || null;
        if (!req.user || !req.user.userId) {
            return next({ statusCode: 400, message: 'User not found with the provided ID' })
        }

        const [user] = await mysqlServerConnection.query(`SELECT * from ${req.user.db_name}.users where id = ?`, [req.user.userID]);
        if (user.length == 0) {
            return next({ statusCode: 404, message: "User does not exist" });
        }

        if (file && user[0].profile_pic_url) {
            deleteFileIfExists(user[0].profile_pic_url);
        }

        await mysqlServerConnection.query(`
          UPDATE ${req.user.db_name}.users SET profile_pic_url = ?, profession=?,
          qualification = ?,experience= ?,skill = ?,name = ?,email = ?,
          mobile = ?,country = ?,state = ?,zipcode = ?,language = ?,address = ?
          where id = ?;
        `, [file ? req.files["profile_pic"][0].path : user[0].profile_pic_url, profession, qualification, experience, skill, name, email, mobile, country, state, zipcode, language, address, req.user.userId]);

        res.status(200).json({
            success: true,
            message: 'user details updated successfully'
        })
    } catch (error) {
        return next({ statusCode: 500, message: error.message || 'Internal Server Error' })
    }
}

// Get Trainee Details
const getTraineeDetails = async (req, res) => {
    try {
        console.log("lkjh")
        const { trainee_id } = req.params
        console.log("Trainee Id : ", trainee_id);
        const [user] = await mysqlServerConnection.query(
            `SELECT  * FROM ${req.user.db_name}.users WHERE id = ?`,
            [trainee_id]
        );

        // console.log(user)

        if (user.length === 0) {
            return res.status(404).json({
                success: false,
                data: {
                    error_msg: 'User not found'
                }
            });
        }

        // Format the response to match the profile structure
        const profileData = {
            profile_pic: user[0].profile_pic_url,
            personal_information: {
                name: user[0].name,
                email: user[0].email,
                mobilenocode: user[0].mobileno_code,
                mobile: user[0].mobile,
                profession: user[0].profession,
                employee_no: user[0].employee_no,
                company_description: user[0].company_description,
                ic_no: user[0].ic_no,
                fin_no: user[0].fin_no,
                gender: user[0].gender,
                pwm_rank: user[0].pwm_rank,
                job_title: user[0].job_title,
                date_of_birth: user[0].date_of_birth,
                date_joined: user[0].date_joined,
                company_code: user[0].company_code
            },
            address: {
                country: user[0].country,
                state: user[0].state,
                zipcode: user[0].zipcode,
                language: user[0].language,
                address: user[0].address
            },
            employee_details: {
                employee_no: user[0].employee_no,
                company_description: user[0].company_description,
                ic_no: user[0].ic_no,
                fin_no: user[0].fin_no,
                gender: user[0].gender,
                pwm_rank: user[0].pwm_rank,
                job_title: user[0].job_title
            }
        };

        return res.status(200).json({
            success: true,
            data: profileData
        });
    } catch (error) {
        console.error('Error retrieving profile:', error);
        return res.status(500).json({
            success: false,
            data: {
                error_msg: 'Internal server error.'
            }
        });
    }
};

const updateTraineeProfile = async (req, res) => {
    try {
        const traineeId = req.params.trainee_id;  // Get trainee ID from params
        const result = req.body;  // Get form data from body
        const file = req.file;  // Get file (singular, not files) since we use upload.single()

        console.log("file", file);
        console.log("req.body", req.body);

        if (!req.user || !req.user.db_name) {
            console.error('User database information is missing.');
            return res.status(400).json({
                success: false,
                data: { error_msg: 'User database information is missing.' }
            });
        }

        if (Object.keys(result).length === 0) {
            return res.status(422).json({
                success: false,
                data: {
                    error_msg: 'The following fields are required',
                    response: {
                        profile_pic: "file",
                        name: "",
                        email: "",
                        mobile: "",
                        mobileno_code: "",
                        profession: "",
                        country: "",
                        state: "",
                        zipcode: "",
                        language: "",
                        address: ""
                    }
                }
            });
        }

        const emailError = validateEmail(result.email);
        if (emailError) {
            return res.status(401).json({
                success: false,
                data: {
                    error_msg: emailError
                }
            });
        }

        result.date_of_birth = result.date_of_birth || null;
        result.date_joined = result.date_joined || null;

        const [user] = await mysqlServerConnection.query(
            `SELECT * FROM ${req.user.db_name}.users WHERE id = ?`,
            [traineeId]
        );

        if (user.length > 0) {
            let profilePicUrl = user[0].profile_pic_url; // Keep existing profile pic by default

            // Handle new profile picture upload
            if (file) {
                console.log("New profile picture uploaded, processing...");
                try {
                    // Delete old profile picture if it exists
                    if (user[0].profile_pic_url) {
                        console.log("Deleting old profile picture:", user[0].profile_pic_url);
                        // Add your file deletion logic here if needed
                    }

                    // Upload new profile picture to S3
                    const uploadResult = await uploadImageToS3(file.buffer);
                    if (uploadResult && uploadResult.success) {
                        profilePicUrl = uploadResult.path;
                        console.log("New profile picture uploaded successfully:", profilePicUrl);
                    } else {
                        console.error("Failed to upload profile picture to S3");
                        return res.status(500).json({
                            success: false,
                            data: { error_msg: 'Failed to upload profile picture' }
                        });
                    }
                } catch (uploadError) {
                    console.error("Error uploading profile picture:", uploadError);
                    return res.status(500).json({
                        success: false,
                        data: { error_msg: 'Error uploading profile picture' }
                    });
                }
            } else if (result.profile_pic_url === "null" || result.profile_pic_url === null || result.profile_pic_url === "undefined") {
                // If explicitly setting profile pic to null, remove it
                profilePicUrl = null;
                console.log("Profile picture explicitly set to null");
            } else if (result.profile_pic_url && result.profile_pic_url.trim() !== '') {
                // If a specific profile_pic_url is provided, use it
                profilePicUrl = result.profile_pic_url;
                console.log("Using provided profile_pic_url:", profilePicUrl);
            } else {
                // No new file uploaded and no profile_pic_url provided - keep existing profile picture
                console.log("No new profile picture uploaded and no profile_pic_url provided, keeping existing:", profilePicUrl);
            }

            if (result.password == null) {
                result.password = user[0].password;
            } else {
                result.password = bcrypt.hashSync(result.password, 10);
            }

            await mysqlServerConnection.query(
                `UPDATE ${req.user.db_name}.users 
                 SET profile_pic_url = ?, 
                     name = ?, 
                     email = ?,     
                     mobile = ?, 
                     profession = ?, 
                     country = ?, 
                     state = ?, 
                     zipcode = ?,
                     language = ?,
                     address = ?,
                     employee_no = ?,
                     company_description = ?,
                     ic_no = ?,
                     fin_no = ?,
                     gender = ?,
                     pwm_rank = ?,
                     job_title = ?,
                     password = ?,
                     company_code = ?,
                     date_of_birth = ?,
                     date_joined = ?,
                     mobileno_code = ?
                 WHERE id = ?`,
                [
                    profilePicUrl,
                    result.name,
                    result.email,
                    result.mobile,
                    result.profession,
                    result.country,
                    result.state,
                    result.zipcode,
                    result.language,
                    result.address,
                    result.employee_no,
                    result.company_description,
                    result.ic_no,
                    result.fin_no,
                    result.gender,
                    result.pwm_rank,
                    result.job_title,
                    result.password,
                    result.company_code,
                    result.date_of_birth,
                    result.date_joined,
                    result.mobileno_code,
                    traineeId
                ]
            );

            return res.status(200).json({ success: true, data: { message: 'Profile updated successfully.' } });
        } else {
            return res.status(404).json({ success: false, data: { error_msg: 'Trainee not found.' } });
        }
    } catch (error) {
        console.error('Error updating profile:', error);
        return res.status(500).json({ success: false, data: { error_msg: 'Internal server error.' } });
    }
};


// Download sample Excel template for bulk user upload
async function downloadSampleTemplate(req, res) {
    try {
        // Create a new workbook
        const workbook = XLSX.utils.book_new();

        // Sample data structure with password field and trainee role
        const sampleData = [
            {
                "profession": null,
                "qualification": null,
                "experience": null,
                "skill": null,
                "name": null,
                "email": null,
                "mobile": null,
                "employee_no": null,
                "company_code": null,
                "company_description": null,
                "ic_no": null,
                "fin_no": null,
                "gender": null,
                "date_of_birth": null,
                "date_joined": null,
                "pwm_rank": null,
                "job_title": null,
                "password": null
            }

        ];

        // Create worksheet from sample data
        const ws = XLSX.utils.json_to_sheet(sampleData);

        // Append the worksheet to the workbook
        XLSX.utils.book_append_sheet(workbook, ws, 'Sample');

        // Create buffer from workbook in the desired format (xlsx)
        const buffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });

        // Set response headers to indicate file type and download prompt
        res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        res.setHeader('Content-Disposition', 'attachment; filename=bulk_users_template.xlsx');

        // Send the buffer as the response body
        res.end(buffer); // Ensure the buffer is properly sent as the response

    } catch (error) {
        console.error('Error generating sample template:', error);

        // Return an error response if something goes wrong
        return res.status(500).json({
            status: false,
            message: 'Error generating sample template',
            error: error.message
        });
    }
}


// Bulk upload users from Excel file
async function bulkUploadUsers(req, res) {
    try {
        const result = req.body
        if (!req.files || !req.files.file) {
            return res.status(400).json({
                status: false,
                message: 'No file uploaded'
            });
        }

        const workbook = XLSX.readFile(req.files.file[0].path);
        const worksheet = workbook.Sheets[workbook.SheetNames[0]];
        const users = XLSX.utils.sheet_to_json(worksheet);

        if (!users.length) {
            return res.status(400).json({
                status: false,
                message: 'Excel file is empty'
            });
        }

        const results = {
            success: [],
            failed: [],
            alreadyExists: []
        };

        for (const user of users) {
            try {
                if (!user.email || !user.name) {
                    results.failed.push({
                        email: user.email,
                        reason: 'Missing required fields (email, first_name)'
                    });
                    continue;
                }

                // Check if email is valid
                if (validateEmail(user.email)) {
                    results.failed.push({
                        email: user.email,
                        reason: 'Invalid email format'
                    });
                    continue;
                }

                // Check if the user already exists
                const checkUserQuery = `SELECT id FROM ${req.user.db_name}.users WHERE email = ?`;
                const [existingUser] = await mysqlServerConnection.query(checkUserQuery, [user.email]);

                if (existingUser && existingUser.length > 0) {
                    results.alreadyExists.push(user.email);
                    continue;
                }

                // Generate password
                const name = user.name;
                const mobile = user.phone || '';
                const firstFourLetters = name.substring(0, 4);
                const formattedFirstPart =
                    firstFourLetters.charAt(0).toUpperCase() + firstFourLetters.slice(1).toLowerCase();
                const lastFourDigits = mobile.length >= 4 ? mobile.slice(-4) : '1234'; // Default if mobile unavailable
                const generatedPassword = `${formattedFirstPart}@${lastFourDigits}`;

                // Hash the generated password
                const hashedPassword = await hashPassword(generatedPassword);

                // Prepare user details
                const userDetails = {
                    profession: user.profession || null,
                    qualification: user.qualification || null,
                    experience: user.experience || null,
                    skill: user.skill || null,
                    name: name,
                    email: user.email,
                    mobile: user.phone || null,
                    employee_no: user.employee_no || null,
                    company_code: user.company_code || null,
                    company_description: user.company_description || null,
                    ic_no: user.ic_no || null,
                    fin_no: user.fin_no || null,
                    gender: user.gender || null,
                    date_of_birth: user.date_of_birth ? new Date(user.date_of_birth).toISOString().split('T')[0] : null,
                    date_joined: user.date_joined ? new Date(user.date_joined).toISOString().split('T')[0] : null,
                    pwm_rank: user.pwm_rank || null,
                    job_title: user.job_title || null,
                    db_name: req.user.db_name,
                    password: user.password != null && user.password != "" ? await hashPassword(user.password) : hashedPassword
                };


                // Insert user into the database
                const query = `INSERT INTO ${req.user.db_name}.users 
                    (profession, qualification, experience, skill, name, email, mobile,
                     employee_no, company_code, company_description, ic_no, fin_no, gender, 
                     date_of_birth, date_joined, pwm_rank, job_title, db_name, password,is_email_verified,is_verified) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`;

                const [result] = await mysqlServerConnection.query(query, [
                    userDetails.profession,
                    userDetails.qualification,
                    userDetails.experience,
                    userDetails.skill,
                    userDetails.name,
                    userDetails.email,
                    userDetails.mobile,
                    userDetails.employee_no,
                    userDetails.company_code,
                    userDetails.company_description,
                    userDetails.ic_no,
                    userDetails.fin_no,
                    userDetails.gender,
                    userDetails.date_of_birth,
                    userDetails.date_joined,
                    userDetails.pwm_rank,
                    userDetails.job_title,
                    userDetails.db_name,
                    userDetails.password,
                    true,
                    true
                ]);

                const userId = result.insertId;

                const selectrole = `SELECT id FROM ${req.user.db_name}.roles WHERE name = 'trainee'`
                const [role] = await mysqlServerConnection.query(selectrole);

                // Insert user role into the database
                const roleQuery = `INSERT INTO ${req.user.db_name}.user_roles (user_id, role_id) VALUES (?, ?)`;
                await mysqlServerConnection.query(roleQuery, [userId, role[0].id]);

                if (req.notify_email === "true") {
                    sendAccountCreatedEmail(userDetails.email, userDetails.name, generatedPassword, request.link);
                }

                results.success.push({
                    email: userDetails.email,
                    message: 'User created successfully'
                });

            } catch (error) {
                console.error('Error processing user:', error);
                results.failed.push({
                    email: user.email,
                    reason: error.message
                });
            }
        }

        const alreadyExistsCount = results.alreadyExists.length;

        return res.status(200).json({
            status: true,
            message: `Bulk upload completed. ${alreadyExistsCount > 0
                ? `${alreadyExistsCount} user(s) already exist and were not added.`
                : ''
                }`,
            results
        });

    } catch (error) {
        console.error('Error in bulk upload:', error);
        return res.status(500).json({
            status: false,
            message: 'Error processing bulk upload',
            error: error.message
        });
    }
}

const TraineeAnalytics = async (req, res) => {
  try {
    const { trainee_id } = req.body;
    const db = req.user.db_name;

    // 1. Extended Trainee Profile
    const [[trainee]] = await mysqlServerConnection.query(
      `SELECT 
        id, name, email, profile_pic_url,
        profession, qualification, experience, skill, bio,
        gender, date_of_birth, date_joined,
        country, state, zipcode, address,
        mobileno_code, mobile,
        is_email_verified, is_phone_verified,
        job_title, pwm_rank
      FROM ${db}.users
      WHERE id = ?`,
      [trainee_id]
    );

    // 2. Enrolled Courses
    const [enrolledCourses] = await mysqlServerConnection.query(
      `SELECT c.id AS course_id, c.course_name, c.course_type, c.banner_image, mc.is_completed
       FROM ${db}.mycourses mc
       JOIN ${db}.courses c ON mc.course_id = c.id
       WHERE mc.user_id = ? AND c.is_deleted = 0`,
      [trainee_id]
    );

    // 3. Completion Counts
    const [videoCounts] = await mysqlServerConnection.query(
      `SELECT course_id, COUNT(*) AS video_total 
       FROM ${db}.videos 
       WHERE is_active = 1 AND is_deleted = 0 
       GROUP BY course_id`
    );

    const [completedVideos] = await mysqlServerConnection.query(
      `SELECT v.course_id, COUNT(*) AS completed_video_count
       FROM ${db}.recent_videos rv 
       JOIN ${db}.videos v ON rv.video_id = v.id
       WHERE rv.user_id = ? AND rv.is_complete = 1 AND v.is_active = 1 AND v.is_deleted = 0
       GROUP BY v.course_id`,
      [trainee_id]
    );

    const [assessmentCounts] = await mysqlServerConnection.query(
      `SELECT course_id, COUNT(*) AS assessment_total 
       FROM ${db}.assessments 
       WHERE is_active = 1 AND is_deleted = 0 AND assessment_type = 'assessment' 
       GROUP BY course_id`
    );

    const [completedAssessments] = await mysqlServerConnection.query(
      `SELECT a.course_id, COUNT(*) AS completed_assessment_count
       FROM ${db}.assessment_users au
       JOIN ${db}.assessments a ON au.assessment_id = a.id
       WHERE au.user_id = ? AND au.is_completed = 1 AND a.is_active = 1 AND a.is_deleted = 0
       GROUP BY a.course_id`,
      [trainee_id]
    );

    // 4. Dashboard Stats
    const [[{ active_classrooms }]] = await mysqlServerConnection.query(
      `SELECT COUNT(*) AS active_classrooms
       FROM ${db}.classroom_trainee ct
       JOIN ${db}.classroom c ON ct.class_id = c.id
       WHERE ct.user_id = ? AND c.is_deleted = 0 AND c.is_active = 1`,
      [trainee_id]
    );

    const [[{ certificates }]] = await mysqlServerConnection.query(
      `SELECT COUNT(*) AS certificates 
       FROM ${db}.certificates 
       WHERE user_id = ? AND is_deleted = 0`,
      [trainee_id]
    );

    const [[{ pending_tickets }]] = await mysqlServerConnection.query(
      `SELECT COUNT(*) AS pending_tickets 
       FROM ${db}.ticket 
       WHERE sender_id = ? AND status = 'pending' AND is_deleted = 0`,
      [trainee_id]
    );

    const [[{ resolved_tickets }]] = await mysqlServerConnection.query(
      `SELECT COUNT(*) AS resolved_tickets 
       FROM ${db}.ticket 
       WHERE sender_id = ? AND status = 'resolved' AND is_deleted = 0`,
      [trainee_id]
    );

    // 5. Tickets
    const [userTickets] = await mysqlServerConnection.query(
      `SELECT id, ticket_id, subject, description, response, status,
              file_url, assigned_to_id, createdAt, updatedAt
       FROM ${db}.ticket
       WHERE sender_id = ? AND is_deleted = 0
       ORDER BY createdAt DESC`,
      [trainee_id]
    );

    // 6. Payments
    const [payments] = await mysqlServerConnection.query(
      `SELECT id, amount, currency, status, payment_method, created_at, updated_at,
              payment_uid, course_name, discount_point, receipt_url, receipt_pdf_path
       FROM ${db}.payments
       WHERE user_id = ?
       ORDER BY created_at DESC`,
      [trainee_id]
    );

    // 7. Certificates
    const [certificatesData] = await mysqlServerConnection.query(
      `SELECT 
         cert.id,
         cert.certificate_name,
         cert.certificate_url,
         cert.issued_date,
         cert.expiration_date,
         c.course_name,
         u.name AS user_name,
         u.email,
         CONCAT(u.mobileno_code, u.mobile) AS phone
       FROM ${db}.certificates cert
       JOIN ${db}.courses c ON cert.course_id = c.id
       JOIN ${db}.users u ON cert.user_id = u.id
       WHERE cert.user_id = ? AND cert.is_deleted = 0
       ORDER BY cert.issued_date DESC`,
      [trainee_id]
    );

    // 8. Classrooms with count
    const [classrooms] = await mysqlServerConnection.query(
      `SELECT 
         c.id AS class_id,
         c.cls_name,
         (
           SELECT COUNT(*) 
           FROM ${db}.classroom_trainee ct2 
           WHERE ct2.class_id = c.id
         ) AS total_trainees
       FROM ${db}.classroom_trainee ct
       JOIN ${db}.classroom c ON ct.class_id = c.id
       WHERE ct.user_id = ? AND c.is_deleted = 0 AND c.is_active = 1`,
      [trainee_id]
    );

    // 9. Completion Map
    const videoMap = Object.fromEntries(videoCounts.map(v => [v.course_id, v.video_total]));
    const videoCompletedMap = Object.fromEntries(completedVideos.map(v => [v.course_id, v.completed_video_count]));
    const assessmentMap = Object.fromEntries(assessmentCounts.map(a => [a.course_id, a.assessment_total]));
    const assessmentCompletedMap = Object.fromEntries(completedAssessments.map(a => [a.course_id, a.completed_assessment_count]));

    let completed_courses = 0, free_courses = 0, paid_courses = 0;
    const course_completion = [];

    for (const course of enrolledCourses) {
      const courseId = course.course_id;
      const videoTotal = videoMap[courseId] || 0;
      const assessTotal = assessmentMap[courseId] || 0;
      const completedVideo = videoCompletedMap[courseId] || 0;
      const completedAssess = assessmentCompletedMap[courseId] || 0;
      const total = videoTotal + assessTotal;
      const completed = completedVideo + completedAssess;

      if (course.is_completed) completed_courses++;
      if (course.course_type === "free") free_courses++;
      if (course.course_type === "paid") paid_courses++;

      const [assessments] = await mysqlServerConnection.query(
        `SELECT id AS assessment_id, assessment_name, pass_percentage, assessment_type
         FROM ${db}.assessments
         WHERE course_id = ? AND is_active = 1 AND is_deleted = 0`,
        [courseId]
      );

      const assessment_results = [];

      for (const a of assessments) {
        const [userData] = await mysqlServerConnection.query(
          `SELECT au.is_completed, au.is_passed, au.updatedAt AS last_attempt_date,
                  (
                    SELECT COUNT(*) FROM ${db}.question_answer qa
                    WHERE qa.user_id = au.user_id AND qa.assessment_id = au.assessment_id AND qa.is_correct = 1
                  ) AS correct_answers,
                  (
                    SELECT COUNT(*) FROM ${db}.questions q
                    WHERE q.assessment_id = au.assessment_id AND q.is_deleted = 0
                  ) AS total_questions
           FROM ${db}.assessment_users au
           WHERE au.assessment_id = ? AND au.user_id = ?
           ORDER BY au.updatedAt DESC LIMIT 1`,
          [a.assessment_id, trainee_id]
        );

        if (userData.length > 0) {
          const u = userData[0];
          const score = u.total_questions > 0
            ? ((u.correct_answers / u.total_questions) * 100).toFixed(2)
            : "0.00";

          const result = {
            assessment_id: a.assessment_id,
            assessment_name: a.assessment_name,
            assessment_type: a.assessment_type,
            last_attempt_date: u.last_attempt_date,
            is_completed: u.is_completed === 1,
            correct_answers: u.correct_answers,
            total_questions: u.total_questions,
            score_percentage: score
          };

          if (a.assessment_type === "assessment") {
            result.status = u.is_passed ? "Passed" : "Failed";
          }

          assessment_results.push(result);
        }
      }

      const [[{ total_survey = 0 }]] = await mysqlServerConnection.query(
        `SELECT COUNT(*) AS total_survey 
         FROM ${db}.assessments 
         WHERE course_id = ? AND is_active = 1 AND is_deleted = 0 AND assessment_type = 'survey'`,
        [courseId]
      );

      const [[{ total_document = 0 }]] = await mysqlServerConnection.query(
        `SELECT COUNT(*) AS total_document 
         FROM ${db}.documents 
         WHERE course_id = ? AND is_active = 1 AND is_deleted = 0`,
        [courseId]
      );

      course_completion.push({
        course_id: courseId,
        course_name: course.course_name,
        course_type: course.course_type,
        banner_image: course.banner_image,
        is_completed: course.is_completed,
        completion_percentage: total > 0 ? ((completed / total) * 100).toFixed(2) : "0.00",
        total_assessment: assessTotal,
        total_survey,
        total_video: videoTotal,
        total_document,
        assessment_results
      });
    }

    // ✅ Final Response
    return res.status(200).json({
      success: true,
      data: {
        trainee,
        dashboard: {
          active_classrooms,
          active_courses: enrolledCourses.length,
          completed_courses,
          free_courses,
          paid_courses,
          certificates,
          pending_tickets,
          resolved_tickets
        },
        course_completion,
        tickets: userTickets,
        payments,
        certificates: certificatesData,
        classrooms
      }
    });

  } catch (error) {
    console.error("❌ Error in TraineeAnalytics:", error);
    return res.status(500).json({
      success: false,
      message: "Internal server error",
      error: error.message
    });
  }
};








module.exports = {
    getOrgProfileDetails,
    deleteOrgProfileDetails,
    editOrgProfileDetails,
    getTraineeDetails,
    updateTraineeProfile,
    downloadSampleTemplate,
    bulkUploadUsers,
    TraineeAnalytics
}
