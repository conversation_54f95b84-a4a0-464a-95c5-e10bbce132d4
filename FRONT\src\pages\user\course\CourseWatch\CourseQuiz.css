.quiz-container {
  max-width: 100%;
  /* padding: 20px; */
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow-wrap: break-word;
  word-wrap: break-word;
  word-break: break-word;
  margin: 0 auto;
}

/* Quiz Intro Styles */
.quiz-intro {
  text-align: center;
  background: white;
  /* padding: 2rem; */
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin: 0 auto;
}

.quiz-info {
  display: flex;
  flex-wrap: wrap;
  gap: 1.5rem;
  margin: 1.5rem 0;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.info-icon {
  font-size: 1.25rem;
  color: #0d6efd;
}

.instructions {
  text-align: left;
  margin: 2rem 0;
  /* padding: 1.5rem; */
  background: #f8f9fa;
  border-radius: 8px;
}

.instructions ul {
  list-style-type: none;
  padding: 0;
}

.instructions li {
  margin: 1rem 0;
  padding-left: 1.5rem;
  position: relative;
}

.instructions li:before {
  content: "•";
  color: #4a90e2;
  position: absolute;
  left: 0;
}

.start-quiz-btn {
  background: #4a90e2;
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 8px;
  font-size: 1.1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 2rem auto;
  cursor: pointer;
  transition: background-color 0.3s;
}

.start-quiz-btn:hover {
  background: #357abd;
}

/* Quiz Header Styles */
.quiz-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 16px 20px;
  background: #f8f9fa;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.question-progress {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 1.1rem;
  font-weight: 500;
}

.current-question {
  color: #2196f3;
  font-weight: 600;
}

.total-questions {
  color: #666;
}

.timer {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 1.1rem;
  color: #dc3545;
  font-weight: 500;
}

.timer-icon {
  font-size: 1.3rem;
}

/* Question Container Styles */
.question-container {
  background: #fff;
  border-radius: 12px;
  padding: 25px;
  margin-bottom: 24px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.question-content {
  margin-bottom: 25px;
  width: 100%;
  padding-bottom: 20px;
  /* border-bottom: 1px solid #e0e0e0; */
}

.question-number {
  display: inline-block;
  font-size: 0.95rem;
  font-weight: 500;
  color: #666;
  margin-bottom: 12px;
  background: #f8f9fa;
  padding: 4px 12px;
  border-radius: 4px;
}

.question-text {
  font-size: 1.1rem;
  font-weight: 500;
  line-height: 1.6;
  color: #333;
  margin: 0;
  padding: 0;
}

.question-media-container {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 10px;
  padding: 10px;
  border-radius: 8px;
}

.question-content img.question-image {
  max-width: 100%;
  height: auto;
  max-height: 300px;
  object-fit: contain;
  border-radius: 8px;
}

.question-audio {
  width: 100%;
  max-width: 400px;
  margin: 10px 0;
}

/* Remove or hide any additional question labels */
.question-label,
.question-title {
  display: none;
}

/* Options Container Styles */
.options-container {
  padding-top: 20px;
}

.options-label {
  font-size: 1rem;
  color: #666;
  margin-bottom: 16px;
}

.option {
  display: flex;
  align-items: flex-start;
  padding: 15px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #f8f9fa;
  margin-bottom: 12px;
  width: 100%;
  position: relative;
  gap: 15px;
}

.option:last-child {
  margin-bottom: 0;
}

.option:hover {
  background: #e9ecef;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.option.selected {
  background: #e3f2fd;
  border-color: #2196f3;
  box-shadow: 0 2px 4px rgba(33, 150, 243, 0.1);
}

.option-content-wrapper {
  display: flex;
  flex-direction: column;
  flex: 1;
  min-width: 0;
}

.option-letter {
  min-width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: #e9ecef;
  font-size: 1rem;
  font-weight: 600;
  color: #666;
  flex-shrink: 0;
  margin-top: 2px;
}

.option.selected .option-letter {
  background: #2196f3;
  color: white;
}

.option:hover .option-letter {
  background: #dee2e6;
}

.option-text {
  width: 100%;
  overflow-wrap: break-word;
  word-wrap: break-word;
  word-break: break-word;
  hyphens: auto;
  font-size: 1rem;
  line-height: 1.4;
  color: #333;
}

.option-media-container {
  width: 100%;
  margin-top: 10px;
}

.option img.option-image {
  max-width: 100%;
  height: auto;
  max-height: 150px;
  object-fit: contain;
  border-radius: 4px;
}

.option audio.option-audio {
  width: 100%;
  max-width: 300px;
  margin-top: 8px;
}

/* Navigation Buttons Styles */
.navigation-buttons {
  display: flex;
  justify-content: space-between;
  margin-top: 20px;
  gap: 10px;
  width: 100%;
}

.nav-buttons-left {
  flex: 1;
  display: flex;
  justify-content: flex-start;
}

.nav-buttons-right {
  flex: 1;
  display: flex;
  justify-content: flex-end;
}

.nav-btn {
  display: inline-flex;
  align-items: center;
  gap: 5px;
  padding: 8px 20px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
  min-width: 100px;
  justify-content: center;
}

.prev-btn {
  background-color: #e0e0e0;
  color: #333;
}

.prev-btn:hover {
  background-color: #d0d0d0;
}

.next-btn, .submit-btn {
  background-color: #2196f3;
  color: white;
}

.next-btn:hover, .submit-btn:hover {
  background-color: #1976d2;
}

.nav-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  pointer-events: none;
}

/* Results Container Styles */
.results-container {
  text-align: center;
  padding: 20px;
}

.score-circle {
  width: 150px;
  height: 150px;
  border-radius: 50%;
  border: 8px solid #2196f3;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin: 20px auto;
}

.score-number {
  font-size: 2.5em;
  font-weight: bold;
  color: #2196f3;
}

.score-label {
  color: #6c757d;
}

.results-details {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin: 2rem 0;
}

.result-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  justify-content: center;
}

.result-icon {
  font-size: 1.25rem;
}

.result-icon.correct {
  color: #198754;
}

.result-icon.incorrect {
  color: #dc3545;
}

.pass-fail-badge {
  display: inline-flex;
  align-items: center;
  gap: 5px;
  padding: 8px 16px;
  border-radius: 20px;
  font-weight: bold;
  margin-top: 20px;
}

.pass-fail-badge.passed {
  background-color: #e8f5e9;
  color: #2e7d32;
}

.pass-fail-badge.failed {
  background-color: #ffebee;
  color: #c62828;
}

/* Responsive Styles */
@media (max-width: 768px) {
  .quiz-container {
    padding: 15px;
  }
  
  .option {
    padding: 12px;
  }
  
  .score-circle {
    width: 120px;
    height: 120px;
  }
  
  .score-number {
    font-size: 2em;
  }
}

/* Question styling */
.question-container {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  margin-bottom: 24px;
}

.question-number {
  color: #666;
  font-size: 14px;
  margin-bottom: 16px;
}

.question-text {
  color: #333;
  font-size: 16px;
  line-height: 1.5;
  margin-bottom: 24px;
}

/* Navigation button */
.btn-primary.btn-sm {
  padding: 6px 16px;
  font-size: 14px;
  border-radius: 4px;
}

.navigation-buttons {
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #eee;
}

/* For option images */
.option-content img {
  max-width: 200px;
  max-height: 150px;
  width: auto;
  height: auto;
  object-fit: contain;
  border-radius: 6px;
}

/* Question content styling */
.question-content {
  margin-bottom: 25px;
  width: 100%;
  padding-bottom: 20px;
  /* border-bottom: 1px solid #e0e0e0; */
}

.question-number {
  font-size: 1.1rem;
  font-weight: 600;
  color: #2196f3;
  margin-bottom: 10px;
  display: block;
}

.question-text {
  font-size: 1.2rem;
  font-weight: 500;
  line-height: 1.5;
  color: #333;
  margin-bottom: 15px;
  overflow-wrap: break-word;
  word-wrap: break-word;
  word-break: break-word;
  hyphens: auto;
}

.question-media-container {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 10px;
}

.question-content img.question-image {
  max-width: 100%;
  height: auto;
  max-height: 300px;
  object-fit: contain;
  border-radius: 8px;
}

.btn-warning {
  background-color: #ffc107;
  color: #000;
  border: none;
}

.btn-warning:hover {
  background-color: #e0a800;
  color: #000;
}

.btn-primary {
  background-color: #2196f3;
  color: white;
  border: none;
}

.btn-primary:hover {
  background-color: #1976d2;
  color: white;
}

.btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 10px 24px;
  font-weight: 500;
  border-radius: 4px;
  transition: all 0.3s ease;
  width: 100%;
  justify-content: center;
}

.btn-icon {
  font-size: 20px;
}

.quiz-result-summary {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  margin-top: 20px;
  border: 1px solid #e0e0e0;
}

.result-stat {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 12px;
}

.result-stat:last-child {
  margin-bottom: 0;
}

.stat-label {
  font-weight: 500;
  color: #666;
  min-width: 140px;
}

.stat-value {
  font-weight: 600;
  color: #333;
}

.stat-value.success {
  color: #2e7d32;
}

.stat-value.error {
  color: #c62828;
}

.result-message {
  margin-top: 15px;
  padding: 10px;
  border-radius: 4px;
  background: #e8f5e9;
  color: #2e7d32;
  font-weight: 500;
  text-align: center;
}

.quiz-results {
  background: white;
  border-radius: 12px;
  padding: 30px;
  margin-top: 30px;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.quiz-results h2 {
  color: #1a237e;
  margin-bottom: 30px;
  font-weight: 600;
}

.score-circle {
  width: 150px;
  height: 150px;
  border-radius: 50%;
  border: 8px solid #2196f3;
  margin: 0 auto 30px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background: white;
}

.score-percentage {
  font-size: 2.5rem;
  font-weight: bold;
  color: #2196f3;
  line-height: 1;
}

.score-label {
  font-size: 1rem;
  color: #666;
  margin-top: 5px;
}

.result-stats {
  display: flex;
  flex-direction: column;
  gap: 15px;
  max-width: 400px;
  margin: 0 auto;
}

.stat-item {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  background: #f8f9fa;
  border-radius: 8px;
  gap: 10px;
}

.stat-item.correct {
  color: #2e7d32;
}

.stat-item.incorrect {
  color: #c62828;
}

.stat-item.time {
  color: #1565c0;
}

.stat-icon {
  font-size: 20px;
}

.stat-text {
  flex: 1;
  text-align: left;
  font-weight: 500;
}

.pass-badge {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 8px 20px;
  border-radius: 20px;
  font-weight: 500;
  margin-top: 20px;
}

.pass-badge.passed {
  background-color: #e8f5e9;
  color: #2e7d32;
}

.previous-attempt-results {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 20px;
  margin: 20px auto;
  max-width: 500px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.previous-attempt-results h3 {
  color: #1a237e;
  font-size: 1.5rem;
  font-weight: 600;
}

.previous-attempt-results .score-circle {
  width: 120px;
  height: 120px;
  border-width: 6px;
}

.previous-attempt-results .score-percentage {
  font-size: 2rem;
}

.previous-attempt-results .result-stats {
  gap: 10px;
}

.previous-attempt-results .stat-item {
  padding: 8px 15px;
  font-size: 0.95rem;
}

.previous-attempt-results .pass-badge {
  margin-top: 15px;
  font-size: 0.95rem;
  padding: 6px 15px;
} 