import React, { useState, useEffect } from 'react';
import NoData from '../../../components/common/NoData';
import { getFAQ } from '../../../services/userService';

function Faq() {
    const [searchTerm, setSearchTerm] = useState('');
    const [faqList, setFaqList] = useState([]);
    const [readFaqIds, setReadFaqIds] = useState([]); // Track opened FAQs

    useEffect(() => {
        fetchFaqs();
    }, []);

    const fetchFaqs = async () => {
        try {
            const response = await getFAQ();
            if (response.success && response.data?.response) {
                setFaqList(response.data.response);
            } else {
                setFaqList([]);
                console.error('Failed to fetch FAQs:', response?.error_msg || 'Unknown error');
            }
        } catch (error) {
            console.error('Error fetching FAQs:', error);
        }
    };

    const filteredFaqs = faqList.filter(
        (faq) =>
            faq.question.toLowerCase().includes(searchTerm.toLowerCase()) ||
            faq.answer.toLowerCase().includes(searchTerm.toLowerCase())
    );

    const handleOpen = (faqId) => {
        if (!readFaqIds.includes(faqId)) {
            setReadFaqIds((prev) => [...prev, faqId]);
        }
    };

    return (
        <div className="row py-md-4 px-md-3 py-0 px-0 pt-3 pt-md-0">
            <div className="col-12">
                <h3 className="mb-4 fw-semibold fs-6 fs-sm-3 fs-md-4">Frequently Asked Questions</h3>
            </div>

            <div className="col-12 col-md-4">
                <input
                    type="text"
                    className="form-control mb-4"
                    placeholder="Search FAQ..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                />
            </div>

            <div className="col-12">
                {filteredFaqs.length === 0 ? (
                    <NoData message="No FAQs found." />
                ) : (
                    <div className="accordion" id="faqAccordion">
                        {filteredFaqs.map((faq, index) => {
                            const isRead = readFaqIds.includes(faq.id);
                            return (
                                <div
                                    className={`accordion-item mb-3 ${isRead ? 'bg-light border-primary border-top border-2' : ''}`}
                                    key={faq.id}
                                >
                                    <h2 className="accordion-header">
                                        <button
                                            className={`accordion-button collapsed fw-semibold ${isRead ? 'text-primary' : ''}`}
                                            type="button"
                                            data-bs-toggle="collapse"
                                            data-bs-target={`#faq-${faq.id}`}
                                            onClick={() => handleOpen(faq.id)}
                                        >
                                            {`${index + 1}. ${faq.question}`}
                                        </button>
                                    </h2>
                                    <div
                                        id={`faq-${faq.id}`}
                                        className="accordion-collapse collapse"
                                        data-bs-parent="#faqAccordion"
                                    >
                                        <div className="accordion-body text-muted">
                                            {faq.answer}
                                        </div>
                                    </div>
                                </div>
                            );
                        })}
                    </div>
                )}
            </div>
        </div>
    );
}

export default Faq;
