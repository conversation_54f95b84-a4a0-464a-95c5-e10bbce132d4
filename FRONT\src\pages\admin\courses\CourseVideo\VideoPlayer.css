.video-section-wrapper {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.video-player-container {
  width: 100%;
  max-width: 1000px;
  background-color: #000;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  margin: 0 auto 30px;
}

.player-wrapper {
  position: relative;
  width: 100%;
  height: 0;
  padding-bottom: 56.25%; /* 16:9 aspect ratio */
  background-color: #000;
}

.bitmovin-player {
  position: absolute !important;
  top: 0;
  left: 0;
  width: 100% !important;
  height: 100% !important;
}

/* Fix for video element inside Bitmovin player */
.bitmovin-player video {
  width: 100% !important;
  height: 100% !important;
  object-fit: contain !important;
}

/* Full screen styles */
.bmpui-ui-fullscreen .bitmovin-player,
.bmpui-ui-fullscreen .player-wrapper,
.bmpui-ui-fullscreen .video-player-container {
  width: 100vw !important;
  height: 100vh !important;
  max-width: none !important;
  padding: 0 !important;
  margin: 0 !important;
  border-radius: 0 !important;
}

.bmpui-ui-fullscreen video {
  object-fit: contain !important;
  width: 100vw !important;
  height: 100vh !important;
}

/* Ensure the player UI is properly centered */
.bmpui-ui-container {
  width: 100% !important;
  height: 100% !important;
}

/* Video preview in modal */
video {
  width: 100%;
  height: auto;
  object-fit: contain;
}

/* Video Tabs Styling */
.video-tabs {
  background-color: #fff;
  border-radius: 12px;
  overflow: hidden;
  margin-top: 30px;
}

.tab-header {
  border-bottom: 1px solid #e0e0e0;
  background-color: #f8f9fa;
}

.nav-tabs {
  border: none;
  padding: 0 20px;
}

.nav-tabs .nav-item {
  margin: 0;
}

.nav-tabs .nav-link {
  border: none;
  color: #6c757d;
  padding: 15px 25px;
  font-weight: 500;
  transition: all 0.3s ease;
  position: relative;
}

.nav-tabs .nav-link:hover {
  color: #3152e8;
  border: none;
}

.nav-tabs .nav-link.active {
  color: #3152e8;
  background: none;
  border: none;
}

.nav-tabs .nav-link.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: #3152e8;
}

.tab-content {
  padding: 15px;
}

/* Edit Button Styling */
.edit-video-btn {
  background-color: #3152e8;
  color: white;
  border: none;
  padding: 10px 25px;
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.edit-video-btn:hover {
  background-color: #2440c9;
  transform: translateY(-1px);
}

/* Bitmovin UI Customization */
:root {
  --bmpui-primary-color: #3152e8;
  --bmpui-accent-color: #3152e8;
}

.bmpui-ui-container .bmpui-ui-seekbar .bmpui-seekbar .bmpui-seekbar-playbackposition-marker {
  background-color: #3152e8 !important;
}

.bmpui-ui-container .bmpui-ui-controlbar {
  background: linear-gradient(to top, rgba(0, 0, 0, 0.7), transparent) !important;
}

.bmpui-ui-container .bmpui-ui-button {
  color: white !important;
}

.bmpui-ui-container .bmpui-ui-button:hover {
  color: #3152e8 !important;
}

/* Watermark - Hidden */
.bmpui-ui-watermark {
  display: none !important;
}

/* OR: Uncomment to replace watermark logo */
/*
.bmpui-ui-watermark {
  background-image: url('/your-logo.png') !important;
  background-size: contain !important;
  background-repeat: no-repeat !important;
  width: 120px !important;
  height: 40px !important;
}
*/

/* Responsive Design */
@media (max-width: 768px) {
  .video-section-wrapper {
    padding: 15px;
  }

  .video-player-container {
    border-radius: 8px;
    margin-bottom: 20px;
  }

  .nav-tabs {
    padding: 0 10px;
  }

  .nav-tabs .nav-link {
    padding: 12px 15px;
    font-size: 14px;
  }

  .tab-content {
    padding: 2px ;
  }
}

/* Card Styling */
.card {
  border: none;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  border-radius: 12px;
}

