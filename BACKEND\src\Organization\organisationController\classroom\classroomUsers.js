const { mysqlServerConnection } = require('../../../db/db');
const { LogsHandler } = require('../../../tools/tools');

const deleteTraineeFromClassroom = async (req, res, next) => {
    try {
        const { user_ids, class_id } = req.body;
        const dbName = req.user.db_name;

        console.log("The deleted usersIDs and classID", user_ids, class_id);
        // Validate required fields
        if (!user_ids || !class_id) {
            return res.status(400).json({
                success: false,
                message: 'User IDs and Class ID are required'
            });
        }

        // Ensure user_ids is an array
        const userIdsArray = Array.isArray(user_ids) ? user_ids : [user_ids];
        
        if (userIdsArray.length === 0) {
            return res.status(400).json({
                success: false,
                message: 'At least one user ID is required'
            });
        }

        // Check if the class exists
        const [classExists] = await mysqlServerConnection.query(
            `SELECT * FROM ${dbName}.classroom WHERE id = ? AND is_deleted = 0`,
            [class_id]
        );

        if (classExists.length === 0) {
            return res.status(404).json({
                success: false,
                message: 'Classroom not found'
            });
        }

         // Get all groups associated with this classroom
         const [classGroups] = await mysqlServerConnection.query(
            `SELECT id FROM ${dbName}.group_rooms WHERE class_id = ?`,
            [class_id]
        );

        // If there are groups associated with this classroom, remove the trainees from those groups
        if (classGroups.length > 0) {
            const groupIds = classGroups.map(group => group.id);
            const groupIdPlaceholders = groupIds.map(() => '?').join(',');
            const userIdPlaceholders = userIdsArray.map(() => '?').join(',');
            
            await mysqlServerConnection.query(
                `DELETE FROM ${dbName}.group_members 
                 WHERE user_id IN (${userIdPlaceholders}) AND group_id IN (${groupIdPlaceholders})`,
                [...userIdsArray, ...groupIds]
            );
        }

        // Remove trainees from classroom
        const placeholders = userIdsArray.map(() => '?').join(',');
        const deleteQuery = `
            DELETE FROM ${dbName}.classroom_trainee 
            WHERE class_id = ? AND user_id IN (${placeholders})
        `;

        const [result] = await mysqlServerConnection.query(
            deleteQuery,
            [class_id, ...userIdsArray]
        );

        // Log the action
        LogsHandler(
            dbName,
            req.user.userId,
            "Remove Trainees",
            `Removed ${result.affectedRows} trainees from classroom ID: ${class_id}`
        );

        return res.status(200).json({
            success: true,
            message: `Successfully removed ${result.affectedRows} trainees from the classroom`,
            data: {
                removed_count: result.affectedRows
            }
        });

    } catch (error) {
        console.error('Error removing trainees from classroom:', error);
        return res.status(500).json({
            success: false,
            message: 'Internal server error',
            error: error.message
        });
    }
};

const addTraineeToClassroom = async (req, res, next) => {
    try {
        const { user_ids, class_id } = req.body;
        const dbName = req.user.db_name;

        // Validate required fields
        if (!user_ids || !class_id) {
            return res.status(400).json({
                success: false,
                message: 'User IDs and Class ID are required'
            });
        }

        // Ensure user_ids is an array
        const userIdsArray = Array.isArray(user_ids) ? user_ids : [user_ids];
        
        if (userIdsArray.length === 0) {
            return res.status(400).json({
                success: false,
                message: 'At least one user ID is required'
            });
        }

        // Check if class exists
        const [classExists] = await mysqlServerConnection.query(
            `SELECT id FROM ${dbName}.classroom WHERE id = ? AND is_deleted = 0`,
            [class_id]
        );

        if (classExists.length === 0) {
            return res.status(404).json({
                success: false,
                message: 'Classroom not found or has been deleted'
            });
        }

        // Check if all users exist
        const userIdPlaceholders = userIdsArray.map(() => '?').join(',');
        const [existingUsers] = await mysqlServerConnection.query(
            `SELECT id FROM ${dbName}.users WHERE id IN (${userIdPlaceholders}) AND is_deleted = 0`,
            userIdsArray
        );

        if (existingUsers.length !== userIdsArray.length) {
            return res.status(404).json({
                success: false,
                message: 'One or more users not found or have been deleted'
            });
        }

        // Check which trainees are already added to the classroom
        const [existingTrainees] = await mysqlServerConnection.query(
            `SELECT user_id FROM ${dbName}.classroom_trainee WHERE user_id IN (${userIdPlaceholders}) AND class_id = ?`,
            [...userIdsArray, class_id]
        );

        const existingTraineeIds = existingTrainees.map(trainee => trainee.user_id);
        const newTraineeIds = userIdsArray.filter(id => !existingTraineeIds.includes(parseInt(id)));

        if (newTraineeIds.length === 0) {
            return res.status(400).json({
                success: false,
                message: 'All trainees are already added to this classroom'
            });
        }

        // Add new trainees to classroom
        if (newTraineeIds.length > 0) {
            const insertValues = newTraineeIds.map(userId => [userId, class_id]);
            await mysqlServerConnection.query(
                `INSERT INTO ${dbName}.classroom_trainee (user_id, class_id) VALUES ?`,
                [insertValues]
            );
            // Check if there are any groups associated with this classroom
            // and add the trainees to those groups as well
            const [classGroups] = await mysqlServerConnection.query(
                `SELECT id FROM ${dbName}.group_rooms WHERE class_id = ?`,
                [class_id]
            );
            
            if (classGroups.length > 0) {
                // For each group in the classroom, add all new trainees
                for (const group of classGroups) {
                    const groupMemberValues = newTraineeIds.map(userId => [group.id, userId]);
                    
                    // Check which users are not already members of the group
                    const userIdPlaceholders = newTraineeIds.map(() => '?').join(',');
                    const [existingGroupMembers] = await mysqlServerConnection.query(
                        `SELECT user_id FROM ${dbName}.group_members 
                         WHERE group_id = ? AND user_id IN (${userIdPlaceholders})`,
                        [group.id, ...newTraineeIds]
                    );
                    
                    const existingMemberIds = existingGroupMembers.map(member => member.user_id);
                    const newGroupMemberIds = newTraineeIds.filter(id => !existingMemberIds.includes(parseInt(id)));
                    
                    if (newGroupMemberIds.length > 0) {
                        const newGroupMemberValues = newGroupMemberIds.map(userId => [group.id, userId]);
                        await mysqlServerConnection.query(
                            `INSERT INTO ${dbName}.group_members (group_id, user_id) VALUES ?`,
                            [newGroupMemberValues]
                        );
                    }
                }
            }
        }

        console.log("Trainees added to classroom successfully", user_ids, class_id);
        res.status(201).json({
            success: true,
            message: `${newTraineeIds.length} trainee(s) added to classroom successfully`,
            addedCount: newTraineeIds.length,
            alreadyExistingCount: existingTraineeIds.length
        });

    } catch (error) {
        // LogsHandler(error);
        res.status(500).json({
            success: false,
            message: 'An error occurred while adding trainees to classroom',
            error: error.message
        });
    }
};

const removeTraineeFromClassroom = async (req, res, next) => {
    try {
        const { user_id, class_id } = req.query;
        const dbName = req.user.db_name;

        console.log("The removed userIDs and classID", user_id, class_id);

        // Validate required fields
        if (!user_id || !class_id) {
            return res.status(400).json({
                success: false,
                message: 'User ID and Class ID are required'
            });
        }

        // Check if trainee exists in the classroom
        const [existingTrainee] = await mysqlServerConnection.query(
            `SELECT id FROM ${dbName}.classroom_trainee WHERE user_id = ? AND class_id = ?`,
            [user_id, class_id]
        );

        if (existingTrainee.length === 0) {
            return res.status(404).json({
                success: false,
                message: 'Trainee is not assigned to this classroom'
            });
        }

       // Get all groups associated with this classroom
        const [classGroups] = await mysqlServerConnection.query(
            `SELECT id FROM ${dbName}.group_rooms WHERE class_id = ?`,
            [class_id]
        );

        // If there are groups associated with this classroom, remove the trainee from those groups
    if (classGroups.length > 0) {
        const groupIds = classGroups.map(group => group.id);
        const groupIdPlaceholders = groupIds.map(() => '?').join(',');
        
        await mysqlServerConnection.query(
            `DELETE FROM ${dbName}.group_members 
             WHERE user_id = ? AND group_id IN (${groupIdPlaceholders})`,
            [user_id, ...groupIds]
        );
    }

    // Remove trainee from classroom
    await mysqlServerConnection.query(
        `DELETE FROM ${dbName}.classroom_trainee WHERE user_id = ? AND class_id = ?`,
        [user_id, class_id]
    );

        res.status(200).json({
            success: true,
            message: 'Trainee removed from classroom successfully'
        });

    } catch (error) {
        // LogsHandler(error);
        res.status(500).json({
            success: false,
            message: 'An error occurred while removing trainee from classroom',
            error: error.message
        });
    }
};

const getAllUsersExceptClassroom = async (req, res, next) => {
    try {
        let { class_id, page = 1, limit = 10, search = '' } = req.body;
        const dbName = req.user.db_name;
        const offset = (page - 1) * limit;

        // Validate required fields
        if (!class_id) {
            return res.status(400).json({
                success: false,
                message: 'Class ID is required'
            });
        }

        // Check if class exists
        const [classExists] = await mysqlServerConnection.query(
            `SELECT id FROM ${dbName}.classroom WHERE id = ? AND is_deleted = 0`,
            [class_id]
        );

        if (classExists.length === 0) {
            return res.status(404).json({
                success: false,
                message: 'Classroom not found or has been deleted'
            });
        }

        // Get all trainees (role_id = 2) who are not in the specified classroom
        let query = `
            SELECT u.*
            FROM ${dbName}.users u
            JOIN ${dbName}.user_roles ur ON u.id = ur.user_id
            WHERE ur.role_id = 2
            AND u.is_deleted = 0
            AND u.id NOT IN (
                SELECT user_id FROM ${dbName}.classroom_trainee 
                WHERE class_id = ? AND is_deleted = 0
            )
        `;

        let countQuery = `
            SELECT COUNT(*) as total
            FROM ${dbName}.users u
            JOIN ${dbName}.user_roles ur ON u.id = ur.user_id
            WHERE ur.role_id = 2
            AND u.is_deleted = 0
            AND u.id NOT IN (
                SELECT user_id FROM ${dbName}.classroom_trainee 
                WHERE class_id = ? AND is_deleted = 0
            )
        `;

        // Add search condition if provided
        if (search) {
            const searchTerm = `%${search}%`;
            query += ` AND (u.name LIKE ? OR u.email LIKE ?)`;
            countQuery += ` AND (u.name LIKE ? OR u.email LIKE ?)`;
        }

        // Add pagination
        query += ` LIMIT ? OFFSET ?`;

        // Prepare query parameters
        let queryParams = [class_id];
        let countQueryParams = [class_id];
        
        if (search) {
            const searchTerm = `%${search}%`;
            queryParams = [...queryParams, searchTerm, searchTerm];
            countQueryParams = [...countQueryParams, searchTerm, searchTerm];
        }
        
        queryParams = [...queryParams, parseInt(limit), offset];

        // Execute queries
        const [trainees] = await mysqlServerConnection.query(query, queryParams);
        const [totalCount] = await mysqlServerConnection.query(countQuery, countQueryParams);

        return res.status(200).json({
            success: true,
            data: {
                trainees,
                pagination: {
                    totalRecords: totalCount[0].total,
                    totalPages: Math.ceil(totalCount[0].total / limit),
                    currentPage: parseInt(page),
                    limit: parseInt(limit)
                }
            }
        });

    } catch (error) {
        console.error('Error getting trainees not in classroom:', error);
        return res.status(500).json({
            success: false,
            message: 'An error occurred while fetching trainees',
            error: error.message
        });
    }
};

const getAllTraineesForClassroom = async (req, res, next) => {
    try {
      const { class_id, page = 1, limit = 10, search = '' } = req.body;
      const dbName = req.user.db_name;
      const offset = (page - 1) * limit;

        // Validate required fields
        if (!class_id) {
            return res.status(400).json({
                success: false,
                message: 'Class ID is required'
            });
        }

        // Check if class exists
        const [classExists] = await mysqlServerConnection.query(
            `SELECT id FROM ${dbName}.classroom WHERE id = ? AND is_deleted = 0`,
            [class_id]
        );

        if (classExists.length === 0) {
            return res.status(404).json({
                success: false,
                message: 'Classroom not found or has been deleted'
            });
        }

        const [groupInfo] = await mysqlServerConnection.query(
            `SELECT id as group_id, group_name FROM ${dbName}.group_rooms WHERE class_id = ? LIMIT 1`,
            [class_id]
        );

        const groupData = groupInfo.length > 0 ? {
            group_id: groupInfo[0].group_id,
            group_name: groupInfo[0].group_name
        } : null;

        // If a group exists for this classroom, check if the requesting user is a member
        // and add them if they're not
        if (groupData) {
            // Check if the user is already a member of the group
            const [existingMember] = await mysqlServerConnection.query(
                `SELECT id FROM ${dbName}.group_members WHERE group_id = ? AND user_id = ?`,
                [groupData.group_id, req.user.userId]
            );
            
            // If the user is not a member of the group, add them
            if (existingMember.length === 0) {
                await mysqlServerConnection.query(
                    `INSERT INTO ${dbName}.group_members (group_id, user_id) VALUES (?, ?)`,
                    [groupData.group_id, req.user.userId]
                );
                console.log(`Added user ${req.user.userId} to group ${groupData.group_id}`);
            }
        }
        

        // Get total count of trainees for pagination
        let countQuery = `
            SELECT COUNT(*) as total FROM ${dbName}.classroom_trainee ct
            JOIN ${dbName}.users u ON ct.user_id = u.id
            WHERE ct.class_id = ?`;
        
        let countParams = [class_id];
        
        if (search) {
            countQuery += ` AND (u.name LIKE ? OR u.email LIKE ?)`;
            countParams.push(`%${search}%`, `%${search}%`);
        }
        
        const [totalResults] = await mysqlServerConnection.query(countQuery, countParams);
        const totalRecords = totalResults[0].total;

        // Get trainees with pagination and search
        let query = `
            SELECT u.id, u.name, u.email, u.mobile, u.profile_pic_url, ct.createdAt as joined_at
            FROM ${dbName}.classroom_trainee ct
            JOIN ${dbName}.users u ON ct.user_id = u.id
            WHERE ct.class_id = ?`;
        
        let params = [class_id];
        
        if (search) {
            query += ` AND (u.name LIKE ? OR u.email LIKE ?)`;
            params.push(`%${search}%`, `%${search}%`);
        }
        
        query += ` ORDER BY ct.createdAt DESC LIMIT ? OFFSET ?`;
        params.push(Number(limit), offset);
        
        const [trainees] = await mysqlServerConnection.query(query, params);
        
        const totalPages = Math.ceil(totalRecords / limit);

        res.status(200).json({
            success: true,
            data: {
                trainees,
                groupData,
                pagination: {
                    totalRecords,
                    totalPages,
                    page: Number(page),
                    limit: Number(limit),
                },
            },
        });

    } catch (error) {
        // LogsHandler(error);
        res.status(500).json({
            success: false,
            message: 'An error occurred while fetching trainees',
            error: error.message
        });
    }
};



module.exports = {
    addTraineeToClassroom,
    removeTraineeFromClassroom,
    getAllTraineesForClassroom,
    getAllUsersExceptClassroom,
    deleteTraineeFromClassroom
};