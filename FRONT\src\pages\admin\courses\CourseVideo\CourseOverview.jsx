import React from 'react'
import './CourseOverview.css'

function CourseOverview({ videoData }) {
  console.log('CourseOverview received videoData:', videoData);

  if (!videoData) {
    return (
      <div className="course-overview">
        <div className="course-overview-card">
          <div className="content-main">
            <div className="text-center">
              <div className="spinner-border text-primary" role="status">
                <span className="visually-hidden">Loading...</span>
              </div>
              <p className="mt-2 text-muted">Loading video details...</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="course-overview p-0">
      <div className="course-overview-card">
        <div className="content-duration">
          <i className="fas fa-clock me-2"></i>
          Duration: {videoData?.video_duration || '00:00'}
        </div>
        <div className="content-main">
          <h2 className="content-title">
            {videoData?.video_title || videoData?.video_name || 'Untitled Video'}
          </h2>
          <div className="content-description">
            {videoData?.video_description || 'No description available'}
          </div>


          {videoData?.video_url && (
            <div className="content-type">
              <span className="type-label">Video URL:</span>
              <span className="type-value">
                <a
                  href={videoData.video_url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-decoration-none"
                >
                  View Source
                </a>
              </span>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default CourseOverview