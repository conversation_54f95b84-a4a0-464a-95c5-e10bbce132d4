import React, { useState, useEffect } from 'react'
import { Icon } from '@iconify/react';
import { useParams } from 'react-router-dom';
import { toast } from 'react-toastify';
import { decodeData } from '../../../utils/encodeAndEncode';
import { createClassroomResource, getClassroomResources, updateClassroomResource, deleteClassroomResource, toggleResourceStatus } from '../../../services/adminService';

function ClassroomRecorded() {
  const { classroomId } = useParams();
  const decodedClassroomId = decodeData(classroomId);
  console.log('decodedClassroomId', decodedClassroomId);

  // State management
  const [resources, setResources] = useState([]);
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedResourceType, setSelectedResourceType] = useState('all');
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedResource, setSelectedResource] = useState(null);
  const [formData, setFormData] = useState({
    resource_name: '',
    resource_description: '',
    resource_type: 'document',
    url: ''
  });
  const [selectedFile, setSelectedFile] = useState(null);
  const [filePreview, setFilePreview] = useState(null);
  const [errors, setErrors] = useState({});
  const [touched, setTouched] = useState({});
  const [isCreating, setIsCreating] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [isToggling, setIsToggling] = useState({});

  // Fetch resources on component mount
  useEffect(() => {
    fetchResources();
  }, [decodedClassroomId, currentPage, selectedResourceType]);

  const fetchResources = async () => {
    try {
      setLoading(true);
      const payload = {
        classroom_id: parseInt(decodedClassroomId),
        page: currentPage,
        limit: 12,
        resource_type: selectedResourceType === 'all' ? null : selectedResourceType
      };

      console.log('Fetching resources with payload:', payload);
      const response = await getClassroomResources(payload);

      if (response.success) {
        setResources(response.data.resources);
        setTotalPages(response.data.pagination.totalPages);
        setTotalCount(response.data.pagination.totalCount);
        console.log('Resources fetched successfully:', response.data.resources);
      } else {
        toast.error('Failed to fetch resources');
      }
    } catch (error) {
      console.error('Error fetching resources:', error);
      toast.error('Error fetching resources');
    } finally {
      setLoading(false);
    }
  };

  // Validation function
  const validateForm = () => {
    const newErrors = {};

    // Validate resource name
    if (!formData.resource_name.trim()) {
      newErrors.resource_name = 'Resource name is required';
    }

    // Validate resource description
    if (!formData.resource_description.trim()) {
      newErrors.resource_description = 'Resource description is required';
    }

    // Validate resource type
    if (!formData.resource_type) {
      newErrors.resource_type = 'Resource type is required';
    }

    // Only require file for create, not update
    if (!selectedResource && formData.resource_type !== 'text' && formData.resource_type !== 'url' && !selectedFile) {
      newErrors.file = 'File is required for this resource type';
    }

    // Validate URL for url type
    if (formData.resource_type === 'url' && !formData.url.trim()) {
      newErrors.url = 'URL is required for URL type resources';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle field blur
  const handleBlur = (fieldName) => {
    setTouched(prev => ({ ...prev, [fieldName]: true }));
  };

  // Handle field change
  const handleFieldChange = (fieldName, value) => {
    setFormData(prev => ({ ...prev, [fieldName]: value }));

    // Clear error when user starts typing
    if (errors[fieldName]) {
      setErrors(prev => ({ ...prev, [fieldName]: '' }));
    }

    // If resource type is changed, clear the selected file and preview
    if (fieldName === 'resource_type') {
      setSelectedFile(null);
      setFilePreview(null);
      // Clear file error if it exists
      if (errors.file) {
        setErrors(prev => ({ ...prev, file: '' }));
      }
      // Clear the file input value
      const fileInputs = document.querySelectorAll('input[type="file"]');
      fileInputs.forEach(input => {
        input.value = '';
      });
    }
  };

  // Handle file selection with preview
  const handleFileSelect = (file) => {
    setSelectedFile(file);

    if (file) {
      // Clear error when file is selected
      if (errors.file) {
        setErrors(prev => ({ ...prev, file: '' }));
      }

      // Validate file size
      const fileSizeInMB = file.size / (1024 * 1024);
      let maxSizeInMB = 0;

      switch (formData.resource_type) {
        case 'image':
          maxSizeInMB = 5;
          break;
        case 'video':
          maxSizeInMB = 150;
          break;
        case 'document':
          maxSizeInMB = 50;
          break;
        case 'audio':
          maxSizeInMB = 50;
          break;
        default:
          maxSizeInMB = 50;
      }

      if (fileSizeInMB > maxSizeInMB) {
        setErrors(prev => ({
          ...prev,
          file: `File size (${fileSizeInMB.toFixed(2)}MB) exceeds maximum allowed size (${maxSizeInMB}MB)`
        }));
        setSelectedFile(null);
        setFilePreview(null);
        return;
      }

      // Create preview URL
      const previewUrl = URL.createObjectURL(file);
      setFilePreview({
        url: previewUrl,
        name: file.name,
        type: file.type
      });
    } else {
      setFilePreview(null);
    }
  };

  // Create resource
  const handleCreateResource = async () => {
    try {
      // Mark all fields as touched
      setTouched({
        resource_name: true,
        resource_description: true,
        resource_type: true,
        file: true,
        url: true
      });

      if (!validateForm()) {
        return;
      }

      setIsCreating(true);
      const startTime = Date.now();

      // Create FormData for file upload
      const formDataToSend = new FormData();
      formDataToSend.append('classroom_id', parseInt(decodedClassroomId));
      formDataToSend.append('resource_name', formData.resource_name);
      formDataToSend.append('resource_description', formData.resource_description || '');
      formDataToSend.append('resource_type', formData.resource_type);

      if (formData.resource_type === 'url') {
        formDataToSend.append('url', formData.url);
      }

      if (selectedFile) {
        formDataToSend.append('file', selectedFile);
      }

      console.log('Creating resource with FormData:', {
        classroom_id: parseInt(decodedClassroomId),
        resource_name: formData.resource_name,
        resource_description: formData.resource_description || '',
        resource_type: formData.resource_type,
        url: formData.resource_type === 'url' ? formData.url : null,
        hasFile: !!selectedFile
      });

      const response = await createClassroomResource(formDataToSend);

      // Ensure minimum 1 second loading time
      const elapsedTime = Date.now() - startTime;
      const minLoadingTime = 1000; // 1 second

      if (elapsedTime < minLoadingTime) {
        await new Promise(resolve => setTimeout(resolve, minLoadingTime - elapsedTime));
      }

      if (response.success) {
        toast.success('Resource created successfully');
        setShowCreateModal(false);
        resetForm();

        // Add the new resource to the local state
        const newResource = {
          id: response.data.id,
          classroom_id: parseInt(decodedClassroomId),
          resource_name: formData.resource_name,
          resource_description: formData.resource_description || '',
          resource_type: formData.resource_type,
          media_url: response.data.media_url,
          is_active: 1,
          is_deleted: 0,
          seen: 0,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        };

        setResources(prevResources => [newResource, ...prevResources]);
      } else {
        toast.error(response.message || 'Failed to create resource');
      }
    } catch (error) {
      console.error('Error creating resource:', error);
      toast.error('Error creating resource');
    } finally {
      setIsCreating(false);
    }
  };

  // Update resource
  const handleUpdateResource = async () => {
    try {
      if (!selectedResource) {
        toast.error('No resource selected');
        return;
      }

      // Mark all fields as touched
      setTouched({
        resource_name: true,
        resource_description: true,
        resource_type: true,
        file: selectedFile ? true : false, // Only mark as touched if file is selected
        url: true
      });

      if (!validateForm()) {
        return;
      }

      setIsUpdating(true);
      const startTime = Date.now();

      // Create FormData for file upload
      const formDataToSend = new FormData();
      formDataToSend.append('resource_id', selectedResource.id);
      formDataToSend.append('resource_name', formData.resource_name);
      formDataToSend.append('resource_description', formData.resource_description || '');
      formDataToSend.append('resource_type', formData.resource_type);

      if (formData.resource_type === 'url') {
        formDataToSend.append('url', formData.url);
      }

      if (selectedFile) {
        formDataToSend.append('file', selectedFile);
      }

      console.log('Updating resource with FormData:', {
        resource_id: selectedResource.id,
        resource_name: formData.resource_name,
        resource_description: formData.resource_description || '',
        resource_type: formData.resource_type,
        url: formData.resource_type === 'url' ? formData.url : null,
        hasFile: !!selectedFile
      });

      const response = await updateClassroomResource(formDataToSend);

      // Ensure minimum 1 second loading time
      const elapsedTime = Date.now() - startTime;
      const minLoadingTime = 1000; // 1 second

      if (elapsedTime < minLoadingTime) {
        await new Promise(resolve => setTimeout(resolve, minLoadingTime - elapsedTime));
      }

      if (response.success) {
        toast.success('Resource updated successfully');
        setShowEditModal(false);
        resetForm();

        // Update the resource in the local state
        setResources(prevResources =>
          prevResources.map(resource =>
            resource.id === selectedResource.id
              ? {
                ...resource,
                resource_name: formData.resource_name,
                resource_description: formData.resource_description || '',
                resource_type: formData.resource_type,
                media_url: response.data.media_url || resource.media_url,
                updated_at: new Date().toISOString()
              }
              : resource
          )
        );
      } else {
        toast.error(response.message || 'Failed to update resource');
      }
    } catch (error) {
      console.error('Error updating resource:', error);
      toast.error('Error updating resource');
    } finally {
      setIsUpdating(false);
    }
  };

  // Delete resource
  const handleDeleteResource = async () => {
    try {
      if (!selectedResource) {
        toast.error('No resource selected');
        return;
      }

      setIsDeleting(true);
      const resourceId = selectedResource.id;

      const payload = {
        resource_id: resourceId
      };

      console.log('Deleting resource with payload:', payload);
      const response = await deleteClassroomResource(payload);

      if (response.success) {
        toast.success('Resource deleted successfully');
        setShowDeleteModal(false);
        setSelectedResource(null);

        // Remove the resource from local state
        setResources(prevResources =>
          prevResources.filter(resource => resource.id !== resourceId)
        );
      } else {
        toast.error(response.message || 'Failed to delete resource');
      }
    } catch (error) {
      console.error('Error deleting resource:', error);
      toast.error('Error deleting resource');
    } finally {
      setIsDeleting(false);
    }
  };

  // Toggle resource status
  const handleToggleStatus = async (resourceId, currentStatus) => {
    try {
      setIsToggling(prev => ({ ...prev, [resourceId]: true }));

      const payload = {
        resource_id: resourceId,
        is_active: !currentStatus
      };

      console.log('Toggling resource status with payload:', payload);
      const response = await toggleResourceStatus(payload);

      if (response.success) {
        toast.success(`Resource ${!currentStatus ? 'activated' : 'deactivated'} successfully`);

        // Update the resource status in local state
        setResources(prevResources =>
          prevResources.map(resource =>
            resource.id === resourceId
              ? { ...resource, is_active: !currentStatus }
              : resource
          )
        );
      } else {
        toast.error(response.message || 'Failed to toggle resource status');
      }
    } catch (error) {
      console.error('Error toggling resource status:', error);
      toast.error('Error toggling resource status');
    } finally {
      setIsToggling(prev => ({ ...prev, [resourceId]: false }));
    }
  };

  // Helper functions
  const resetForm = () => {
    setFormData({
      resource_name: '',
      resource_description: '',
      resource_type: 'document',
      url: ''
    });
    setSelectedFile(null);
    setFilePreview(null);
    setErrors({});
    setTouched({});
  };

  const openEditModal = (resource) => {
    setSelectedResource(resource);
    setFormData({
      resource_name: resource.resource_name,
      resource_description: resource.resource_description || '',
      resource_type: resource.resource_type,
      url: resource.resource_type === 'url' ? resource.media_url : ''
    });
    setSelectedFile(null);
    setFilePreview(null);
    setErrors({});
    setTouched({});
    setShowEditModal(true);
  };

  const openDeleteModal = (resource) => {
    setSelectedResource(resource);
    setShowDeleteModal(true);
  };

  const getResourceIcon = (resourceType) => {
    switch (resourceType) {
      case 'image':
        return 'fluent:image-24-regular';
      case 'video':
        return 'fluent:video-24-regular';
      case 'audio':
        return 'fluent:music-note-24-regular';
      case 'document':
        return 'fluent:document-24-regular';
      case 'url':
        return 'fluent:link-24-regular';
      case 'text':
        return 'fluent:text-24-regular';
      default:
        return 'fluent:document-24-regular';
    }
  };

  const getResourceTypeLabel = (resourceType) => {
    switch (resourceType) {
      case 'image':
        return 'Image';
      case 'video':
        return 'Video';
      case 'audio':
        return 'Audio';
      case 'document':
        return 'Document';
      case 'url':
        return 'URL';
      case 'text':
        return 'Text';
      default:
        return 'Unknown';
    }
  };

  // Get file size limit info
  const getFileSizeLimitInfo = () => {
    switch (formData.resource_type) {
      case 'image':
        return { maxSize: 5, unit: 'MB', type: 'Image' };
      case 'video':
        return { maxSize: 150, unit: 'MB', type: 'Video' };
      case 'document':
        return { maxSize: 50, unit: 'MB', type: 'Document' };
      case 'audio':
        return { maxSize: 50, unit: 'MB', type: 'Audio' };
      default:
        return { maxSize: 50, unit: 'MB', type: 'File' };
    }
  };

  // Render file preview
  const renderFilePreview = () => {
    if (!filePreview) return null;

    const { url, name, type } = filePreview;

    return (
      <div className="mt-3 p-3 border rounded bg-light">
        <h6 className="mb-2">New File Preview:</h6>
        <div className="d-flex align-items-center gap-2 mb-2">
          <Icon
            icon={getResourceIcon(formData.resource_type)}
            className="text-primary"
            width="20"
            height="20"
          />
          <span className="text-muted">{name}</span>
        </div>

        {type.startsWith('image/') && (
          <div className="text-center">
            <img
              src={url}
              alt="Preview"
              className="img-fluid rounded"
              style={{ maxHeight: '200px', maxWidth: '100%' }}
            />
          </div>
        )}

        {type.startsWith('video/') && (
          <div className="text-center">
            <video
              controls
              preload="metadata"
              className="img-fluid rounded border"
              style={{ 
                maxHeight: '250px', 
                maxWidth: '100%',
                width: '100%',
                height: 'auto'
              }}
              controlsList="nodownload"
            >
              <source src={url} type={type} />
              Your browser does not support the video tag.
            </video>
          </div>
        )}

        {type.startsWith('audio/') && (
          <div className="text-center p-3">
            <audio 
              controls 
              preload="metadata"
              className="w-100"
              style={{ 
                maxWidth: '100%',
                height: '50px'
              }}
              controlsList="nodownload"
            >
              <source src={url} type={type} />
              Your browser does not support the audio tag.
            </audio>
          </div>
        )}

        {(type === 'application/pdf' || type.includes('html') || type.includes('text/')) && (
          <div className="text-center">
            <iframe
              src={url}
              className="border rounded"
              style={{
                width: '100%',
                height: '300px',
                maxHeight: '400px'
              }}
              title="Document Preview"
            />
          </div>
        )}

        {!type.startsWith('image/') && !type.startsWith('video/') && !type.startsWith('audio/') &&
          type !== 'application/pdf' && !type.includes('html') && !type.includes('text/') && (
            <div className="text-center">
              <Icon
                icon="fluent:document-24-regular"
                className="text-primary"
                width="48"
                height="48"
              />
              <p className="text-muted mt-2">Document preview not available</p>
            </div>
          )}
      </div>
    );
  };

  // Render current media preview for edit modal
  const renderCurrentMediaPreview = () => {
    if (!selectedResource || !selectedResource.media_url || selectedFile) return null;

    const { media_url, resource_type, resource_name } = selectedResource;
    const fileName = media_url.split('/').pop() || 'Current file';

    return (
      <div className="mt-3 p-3 border rounded bg-light">
        <div className="d-flex align-items-center justify-content-between mb-3">
          <h6 className="mb-0 text-primary fw-semibold">
            <Icon icon="fluent:document-24-regular" className="me-2" />
            Current File
          </h6>

        </div>
        
        <div className="d-flex align-items-center gap-2 mb-3">
          <Icon
            icon={getResourceIcon(resource_type)}
            className="text-primary"
            width="20"
            height="20"
          />
          <div className="flex-grow-1">
            <div className="text-muted small">
              {resource_type.charAt(0).toUpperCase() + resource_type.slice(1)} file
            </div>
          </div>
        </div>

        {resource_type === 'image' && (
          <div className="text-center">
            <img
              src={media_url}
              alt="Current file"
              className="img-fluid rounded"
              style={{ maxHeight: '200px', maxWidth: '100%' }}
            />
          </div>
        )}

        {resource_type === 'video' && (
          <div className="text-center">
            <video
              controls
              preload="metadata"
              className="img-fluid rounded border"
              style={{ 
                maxHeight: '250px', 
                maxWidth: '100%',
                width: '100%',
                height: 'auto'
              }}
              controlsList="nodownload"
            >
              <source src={media_url} type="video/mp4" />
              <source src={media_url} type="video/webm" />
              <source src={media_url} type="video/ogg" />
              Your browser does not support the video tag.
            </video>
          </div>
        )}

        {resource_type === 'audio' && (
          <div className="text-center p-3">
            <audio 
              controls 
              preload="metadata"
              className="w-100"
              style={{ 
                maxWidth: '100%',
                height: '50px'
              }}
              controlsList="nodownload"
            >
              <source src={media_url} type="audio/mpeg" />
              <source src={media_url} type="audio/mp3" />
              <source src={media_url} type="audio/wav" />
              <source src={media_url} type="audio/ogg" />
              Your browser does not support the audio tag.
            </audio>
          </div>
        )}

        {(resource_type === 'document' || resource_type === 'url') && (
          <div className="text-center">
            <iframe
              src={media_url}
              className="border rounded"
              style={{
                width: '100%',
                height: '300px',
                maxHeight: '400px'
              }}
              title="Current Document"
            />
          </div>
        )}

<a
            href={media_url}
            target="_blank"
            rel="noopener noreferrer"
            className="btn btn-outline-primary btn-sm"
          >
            <Icon icon="fluent:open-24-regular" className="me-1" />
            View File
          </a>
      </div>
    );
  };

  const filteredResources = resources.filter(resource =>
    resource.resource_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (resource.resource_description && resource.resource_description.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  return (
    <>
      {/* Header with create button and search */}
      <div className="row mb-4">
        <div className="col-md-6">
          <div className="d-flex align-items-center gap-3">
            <div className="input-group">
              <input
                type="text"
                className="form-control"
                placeholder="Search resources..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <select
              className="form-select"
              style={{ width: 'auto' }}
              value={selectedResourceType}
              onChange={(e) => setSelectedResourceType(e.target.value)}
            >
              <option value="all">All Types</option>
              <option value="document">Documents</option>
              <option value="image">Images</option>
              <option value="video">Videos</option>
              <option value="audio">Audio</option>
              <option value="url">URLs</option>
              <option value="text">Text</option>
            </select>
          </div>
        </div>
        <div className="col-md-6 d-flex justify-content-end">
          <button
            className="btn btn-primary d-flex align-items-center gap-2 w-auto"
            onClick={() => setShowCreateModal(true)}
          >
            <Icon icon="fluent:add-24-regular" width="20" height="20" />
            Add Resource
          </button>
        </div>
      </div>

      {/* Resources in cards */}
      <div className="row">
        {loading ? (
          <div className="col-12 text-center py-5">
            <div className="spinner-border text-primary" role="status">
              <span className="visually-hidden">Loading...</span>
            </div>
          </div>
        ) : filteredResources.length === 0 ? (
          <div className="col-12 text-center py-5">
            <Icon icon="fluent:document-24-regular" className="text-muted mb-3" width="48" height="48" />
            <h5>No Resources Found</h5>
            <p className="text-muted">No resources available for this classroom.</p>
          </div>
        ) : (
          filteredResources.map((resource) => (
            <div key={resource.id} className="col-md-6 col-lg-4 mb-4">
              <div className="card h-100 shadow-sm " style={{ minHeight: '320px' }}>
                {/* Card Header with Type and Actions */}
                <div className="card-header bg-transparent border-bottom-0 pb-0">
                  <div className="d-flex justify-content-between align-items-start">
                    <div className="d-flex align-items-center gap-2">
                      <Icon
                        icon={getResourceIcon(resource.resource_type)}
                        className="text-primary"
                        width="20"
                        height="20"
                      />
                      <span className="badge bg-primary-subtle text-primary px-2 py-1">
                        {getResourceTypeLabel(resource.resource_type)}
                      </span>
                    </div>
                    <div className="d-flex align-items-center gap-1">
                      <button
                        className="btn btn-outline-dark btn-sm border border-dark"
                        style={{ width: '40px', height: '40px' }}
                        onClick={() => openEditModal(resource)}
                        title="Edit Resource"
                      >
                        <Icon icon="fluent:edit-24-regular" width="40" height="40" />
                      </button>
                      <button
                  className="btn btn-outline-dark btn-sm border border-dark"
                        style={{ width: '40px', height: '40px' }}
                        onClick={() => handleToggleStatus(resource.id, resource.is_active)}
                        title={resource.is_active ? 'Deactivate Resource' : 'Activate Resource'}
                        disabled={isToggling[resource.id]}
                      >
                        {isToggling[resource.id] ? (
                          <span className="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                        ) : (
                          <Icon
                            icon={resource.is_active ? "fluent:eye-off-24-regular" : "fluent:eye-24-regular"}
                            width="40"
                            height="40"
                          />
                        )}
                      </button>
                      <button
                      className="btn btn-outline-danger btn-sm border border-danger"
                        style={{ width: '40px', height: '40px' }}
                        onClick={() => openDeleteModal(resource)}
                        title="Delete Resource"
                      >
                        <Icon icon="fluent:delete-24-regular" width="40" height="40" />
                      </button>
                    </div>
                  </div>
                </div>

<hr className="my-3" style={{ opacity: 0.2 }} />
                {/* Card Body */}
                <div className="card-body d-flex flex-column">
                  {/* Resource Title */}
                  <h6 className="card-title mb-2 fw-semibold text-truncate" style={{ fontSize: '1rem' }}>
                    {resource.resource_name}
                  </h6>

                  {/* Resource Description */}
                  {resource.resource_description && (
                    <p className="card-text text-muted small mb-3 flex-grow-1" style={{
                      display: '-webkit-box',
                      WebkitLineClamp: 3,
                      WebkitBoxOrient: 'vertical',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      lineHeight: '1.4',
                      maxHeight: '4.2em',
                      fontSize: '0.875rem'
                    }}>
                      {resource.resource_description}
                    </p>
                  )}

                  {/* Separator Line */}
                  <hr className="my-3" style={{ opacity: 0.2 }} />

                  {/* Bottom Section */}
                  <div className="mt-auto">
                    {/* Date and Status Row */}
                    <div className="d-flex justify-content-between align-items-center mb-3">
                      <small className="text-muted fw-medium">
                        <Icon icon="fluent:calendar-24-regular" className="me-1" width="12" height="12" />
                        {new Date(resource.created_at).toLocaleDateString()}
                      </small>
                      <div className="d-flex align-items-center">
                        {resource.is_active ? (
                          <span className="badge rounded-pill border border-success text-success bg-success-subtle px-2 py-1" style={{ fontSize: '0.75rem' }}>
                            <Icon icon="fluent:presence-available-16-regular" className="me-1" width="10" height="10" />
                            Active
                          </span>
                        ) : (
                          <span className="badge rounded-pill border border-danger text-danger bg-danger-subtle px-2 py-1" style={{ fontSize: '0.75rem' }}>
                            <Icon icon="fluent:presence-blocked-16-regular" className="me-1" width="10" height="10" />
                            Inactive
                          </span>
                        )}
                      </div>
                    </div>

                    {/* View Resource Button - Always Visible */}
                    <div className="d-grid">
                      <a
                        href={resource.media_url || '#'}
                        target="_blank"
                        rel="noopener noreferrer"
                        className={`btn btn-primary btn-sm ${!resource.media_url ? 'disabled' : ''}`}
                        style={{ 
                          fontSize: '0.875rem',
                          padding: '0.5rem 1rem',
                          borderWidth: '1px'
                        }}
                        onClick={!resource.media_url ? (e) => e.preventDefault() : undefined}
                      >
                        <Icon icon="fluent:open-24-regular" className="me-2" width="14" height="14" />
                        {resource.media_url ? 'View Resource' : 'No Resource URL'}
                      </a>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))
        )}
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="row mt-4">
          <div className="col-12">
            <nav>
              <ul className="pagination justify-content-end">
                <li className={`page-item ${currentPage === 1 ? 'disabled' : ''}`}>
                  <button
                    className="page-link"
                    onClick={() => setCurrentPage(currentPage - 1)}
                    disabled={currentPage === 1}
                  >
                    Previous
                  </button>
                </li>
                {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                  <li key={page} className={`page-item ${currentPage === page ? 'active' : ''}`}>
                    <button
                      className="page-link"
                      onClick={() => setCurrentPage(page)}
                    >
                      {page}
                    </button>
                  </li>
                ))}
                <li className={`page-item ${currentPage === totalPages ? 'disabled' : ''}`}>
                  <button
                    className="page-link"
                    onClick={() => setCurrentPage(currentPage + 1)}
                    disabled={currentPage === totalPages}
                  >
                    Next
                  </button>
                </li>
              </ul>
            </nav>
          </div>
        </div>
      )}

      {/* Create Resource Modal */}
      {showCreateModal && (
        <div className="modal fade show d-block" style={{ backgroundColor: 'rgba(0,0,0,0.5)' }}>
          <div className="modal-dialog">
            <div className="modal-content">
              <div className="modal-header">
                <h5 className="modal-title">Add New Resource</h5>
                <button
                  type="button"
                  className="btn-close"
                  onClick={() => {
                    setShowCreateModal(false);
                    resetForm();
                  }}
                ></button>
              </div>
              <div className="modal-body">
                <div className="mb-3">
                  <label className="form-label text-primary">Resource Name *</label>
                  <div className="position-relative">
                    <input
                      type="text"
                      className={`form-control ${touched.resource_name && errors.resource_name ? 'border-danger' : ''}`}
                      value={formData.resource_name}
                      onChange={(e) => handleFieldChange('resource_name', e.target.value)}
                      onBlur={() => handleBlur('resource_name')}
                      placeholder="Enter resource name"
                    />
                    {touched.resource_name && errors.resource_name && (
                      <div className="position-absolute top-50 end-0 translate-middle-y pe-2">
                        <Icon icon="fluent:error-circle-24-regular" className="text-danger" width="20" height="20" />
                      </div>
                    )}
                  </div>
                  {touched.resource_name && errors.resource_name && (
                    <div className="text-danger small mt-1">{errors.resource_name}</div>
                  )}
                </div>
                <div className="mb-3">
                  <label className="form-label text-primary">Resource Description *</label>
                  <div className="position-relative">
                    <textarea
                      className={`form-control ${touched.resource_description && errors.resource_description ? 'border-danger' : ''}`}
                      rows="3"
                      value={formData.resource_description}
                      onChange={(e) => handleFieldChange('resource_description', e.target.value)}
                      onBlur={() => handleBlur('resource_description')}
                      placeholder="Enter resource description"
                    ></textarea>
                    {touched.resource_description && errors.resource_description && (
                      <div className="position-absolute top-50 end-0 translate-middle-y pe-2">
                        <Icon icon="fluent:error-circle-24-regular" className="text-danger" width="20" height="20" />
                      </div>
                    )}
                  </div>
                  {touched.resource_description && errors.resource_description && (
                    <div className="text-danger small mt-1">{errors.resource_description}</div>
                  )}
                </div>
                <div className="mb-3">
                  <label className="form-label text-primary">Resource Type *</label>
                  <div className="position-relative">
                    <select
                      className={`form-select ${touched.resource_type && errors.resource_type ? 'border-danger' : ''}`}
                      value={formData.resource_type}
                      onChange={(e) => handleFieldChange('resource_type', e.target.value)}
                      onBlur={() => handleBlur('resource_type')}
                    >
                      <option value="document">Document</option>
                      <option value="image">Image</option>
                      <option value="video">Video</option>
                      <option value="audio">Audio</option>
                      <option value="url">URL</option>
                      <option value="text">Text</option>
                    </select>
                    {touched.resource_type && errors.resource_type && (
                      <div className="position-absolute top-50 end-0 translate-middle-y pe-2">
                        <Icon icon="fluent:error-circle-24-regular" className="text-danger" width="20" height="20" />
                      </div>
                    )}
                  </div>
                  {touched.resource_type && errors.resource_type && (
                    <div className="text-danger small mt-1">{errors.resource_type}</div>
                  )}
                </div>
                {formData.resource_type === 'url' ? (
                  <div className="mb-3">
                    <label className="form-label text-primary">URL *</label>
                    <div className="position-relative">
                      <input
                        type="url"
                        className={`form-control ${touched.url && errors.url ? 'border-danger' : ''}`}
                        value={formData.url}
                        onChange={(e) => handleFieldChange('url', e.target.value)}
                        onBlur={() => handleBlur('url')}
                        placeholder="Enter URL"
                      />
                      {touched.url && errors.url && (
                        <div className="position-absolute top-50 end-0 translate-middle-y pe-2">
                          <Icon icon="fluent:error-circle-24-regular" className="text-danger" width="20" height="20" />
                        </div>
                      )}
                    </div>
                    {touched.url && errors.url && (
                      <div className="text-danger small mt-1">{errors.url}</div>
                    )}
                  </div>
                ) : formData.resource_type !== 'text' ? (
                  <div className="mb-3">
                    <label className="form-label text-primary">File *</label>
                    <div className="position-relative">
                      <input
                        type="file"
                        className={`form-control ${touched.file && errors.file ? 'border-danger' : ''}`}
                        onChange={(e) => handleFileSelect(e.target.files[0])}
                        onBlur={() => handleBlur('file')}
                        accept={
                          formData.resource_type === 'image' ? 'image/*' :
                            formData.resource_type === 'video' ? 'video/*' :
                              formData.resource_type === 'audio' ? 'audio/*' :
                                'application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,.txt'
                        }
                      />
                      {touched.file && errors.file && (
                        <div className="position-absolute top-50 end-0 translate-middle-y pe-2">
                          <Icon icon="fluent:error-circle-24-regular" className="text-danger" width="20" height="20" />
                        </div>
                      )}
                    </div>
                    {touched.file && errors.file && (
                      <div className="text-danger small mt-1">{errors.file}</div>
                    )}
                    <div className="text-muted small mt-1">
                      <Icon icon="fluent:info-24-regular" className="me-1" />
                      Maximum file size: {getFileSizeLimitInfo().maxSize}MB for {getFileSizeLimitInfo().type} files
                    </div>
                    {renderFilePreview()}
                  </div>
                ) : null}
              </div>
              <div className="modal-footer">
                <button
                  type="button"
                  className="btn btn-secondary"
                  onClick={() => {
                    setShowCreateModal(false);
                    resetForm();
                  }}
                >
                  Cancel
                </button>
                <button
                  type="button"
                  className="btn btn-primary"
                  onClick={handleCreateResource}
                  disabled={isCreating}
                >
                  {isCreating ? (
                    <>
                      <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                      Creating...
                    </>
                  ) : (
                    'Create Resource'
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Edit Resource Modal */}
      {showEditModal && selectedResource && (
        <div className="modal fade show d-block" style={{ backgroundColor: 'rgba(0,0,0,0.5)' }}>
          <div className="modal-dialog">
            <div className="modal-content">
              <div className="modal-header">
                <h5 className="modal-title">Edit Resource</h5>
                <button
                  type="button"
                  className="btn-close"
                  onClick={() => {
                    setShowEditModal(false);
                    resetForm();
                  }}
                ></button>
              </div>
              <div className="modal-body">
                <div className="mb-3">
                  <label className="form-label text-primary">Resource Name *</label>
                  <div className="position-relative">
                    <input
                      type="text"
                      className={`form-control ${touched.resource_name && errors.resource_name ? 'border-danger' : ''}`}
                      value={formData.resource_name}
                      onChange={(e) => handleFieldChange('resource_name', e.target.value)}
                      onBlur={() => handleBlur('resource_name')}
                      placeholder="Enter resource name"
                    />
                    {touched.resource_name && errors.resource_name && (
                      <div className="position-absolute top-50 end-0 translate-middle-y pe-2">
                        <Icon icon="fluent:error-circle-24-regular" className="text-danger" width="20" height="20" />
                      </div>
                    )}
                  </div>
                  {touched.resource_name && errors.resource_name && (
                    <div className="text-danger small mt-1">{errors.resource_name}</div>
                  )}
                </div>
                <div className="mb-3">
                  <label className="form-label text-primary">Resource Description *</label>
                  <div className="position-relative">
                    <textarea
                      className={`form-control ${touched.resource_description && errors.resource_description ? 'border-danger' : ''}`}
                      rows="3"
                      value={formData.resource_description}
                      onChange={(e) => handleFieldChange('resource_description', e.target.value)}
                      onBlur={() => handleBlur('resource_description')}
                      placeholder="Enter resource description"
                    ></textarea>
                    {touched.resource_description && errors.resource_description && (
                      <div className="position-absolute top-50 end-0 translate-middle-y pe-2">
                        <Icon icon="fluent:error-circle-24-regular" className="text-danger" width="20" height="20" />
                      </div>
                    )}
                  </div>
                  {touched.resource_description && errors.resource_description && (
                    <div className="text-danger small mt-1">{errors.resource_description}</div>
                  )}
                </div>
                <div className="mb-3">
                  <label className="form-label text-primary">Resource Type *</label>
                  <div className="position-relative">
                    <select
                      className={`form-select ${touched.resource_type && errors.resource_type ? 'border-danger' : ''}`}
                      value={formData.resource_type}
                      onChange={(e) => handleFieldChange('resource_type', e.target.value)}
                      onBlur={() => handleBlur('resource_type')}
                    >
                      <option value="document">Document</option>
                      <option value="image">Image</option>
                      <option value="video">Video</option>
                      <option value="audio">Audio</option>
                      <option value="url">URL</option>
                      <option value="text">Text</option>
                    </select>
                    {touched.resource_type && errors.resource_type && (
                      <div className="position-absolute top-50 end-0 translate-middle-y pe-2">
                        <Icon icon="fluent:error-circle-24-regular" className="text-danger" width="20" height="20" />
                      </div>
                    )}
                  </div>
                  {touched.resource_type && errors.resource_type && (
                    <div className="text-danger small mt-1">{errors.resource_type}</div>
                  )}
                </div>
                {formData.resource_type === 'url' ? (
                  <div className="mb-3">
                    <label className="form-label text-primary">URL *</label>
                    <div className="position-relative">
                      <input
                        type="url"
                        className={`form-control ${touched.url && errors.url ? 'border-danger' : ''}`}
                        value={formData.url}
                        onChange={(e) => handleFieldChange('url', e.target.value)}
                        onBlur={() => handleBlur('url')}
                        placeholder="Enter URL"
                      />
                      {touched.url && errors.url && (
                        <div className="position-absolute top-50 end-0 translate-middle-y pe-2">
                          <Icon icon="fluent:error-circle-24-regular" className="text-danger" width="20" height="20" />
                        </div>
                      )}
                    </div>
                    {touched.url && errors.url && (
                      <div className="text-danger small mt-1">{errors.url}</div>
                    )}
                  </div>
                ) : formData.resource_type !== 'text' ? (
                  <div className="mb-3">
                    <label className="form-label text-primary">
                      {selectedResource && selectedResource.media_url ? 'New File (Optional - leave empty to keep current file)' : 'File *'}
                    </label>
                    <input
                      type="file"
                      className="form-control"
                      onChange={(e) => handleFileSelect(e.target.files[0])}
                      accept={
                        formData.resource_type === 'image' ? 'image/*' :
                          formData.resource_type === 'video' ? 'video/*' :
                            formData.resource_type === 'audio' ? 'audio/*' :
                              'application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,.txt'
                      }
                    />
                    <div className="text-muted small mt-1">
                      <Icon icon="fluent:info-24-regular" className="me-1" />
                      Maximum file size: {getFileSizeLimitInfo().maxSize}MB for {getFileSizeLimitInfo().type} files
                    </div>
                    {renderCurrentMediaPreview()}
                    {renderFilePreview()}
                  </div>
                ) : null}
              </div>
              <div className="modal-footer">
                <button
                  type="button"
                  className="btn btn-secondary"
                  onClick={() => {
                    setShowEditModal(false);
                    resetForm();
                  }}
                >
                  Cancel
                </button>
                <button
                  type="button"
                  className="btn btn-primary"
                  onClick={handleUpdateResource}
                  disabled={isUpdating}
                >
                  {isUpdating ? (
                    <>
                      <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                      Updating...
                    </>
                  ) : (
                    'Update Resource'
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Delete Resource Modal */}
      {showDeleteModal && selectedResource && (
        <div className="modal fade show d-block" style={{ backgroundColor: 'rgba(0,0,0,0.5)' }}>
          <div className="modal-dialog">
            <div className="modal-content">
              <div className="modal-header">
                <h5 className="modal-title">Delete Resource</h5>
                <button
                  type="button"
                  className="btn-close"
                  onClick={() => setShowDeleteModal(false)}
                ></button>
              </div>
              <div className="modal-body">
                <p>Are you sure you want to delete "<strong>{selectedResource.resource_name}</strong>"?</p>
                <p className="text-muted">This action cannot be undone.</p>
              </div>
              <div className="modal-footer">
                <button
                  type="button"
                  className="btn btn-secondary"
                  onClick={() => setShowDeleteModal(false)}
                >
                  Cancel
                </button>
                <button
                  type="button"
                  className="btn btn-danger"
                  onClick={handleDeleteResource}
                  disabled={isDeleting}
                >
                  {isDeleting ? (
                    <>
                      <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                      Deleting...
                    </>
                  ) : (
                    'Delete Resource'
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  )
}

export default ClassroomRecorded