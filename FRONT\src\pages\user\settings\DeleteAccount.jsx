import React, { useState } from 'react';
import { Icon } from '@iconify/react';
import { Modal, Button } from 'react-bootstrap';
import './DeleteAccount.css';
import { softDeleteAccount } from '../../../services/userService';
import { toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';

function DeleteAccount() {
  const [showModal, setShowModal] = useState(false);
  const [confirmText, setConfirmText] = useState('');
  const [reason, setReason] = useState('');
  const [isDeleting, setIsDeleting] = useState(false);

  const handleOpenModal = () => setShowModal(true);
  const handleCloseModal = () => {
    setShowModal(false);
    setConfirmText('');
    setReason('');
  };

  const handleReasonChange = (e) => setReason(e.target.value);
  const handleConfirmTextChange = (e) => setConfirmText(e.target.value);

  const handleDeleteAccount = async () => {
    setIsDeleting(true);
  
    // Simulate 2 seconds loading before making API call
    setTimeout(async () => {
      try {
        const response = await softDeleteAccount();
        if (response.success) {
          toast.success('Account deleted successfully.');
          handleCloseModal();
          // Optional: redirect after deletion
          // setTimeout(() => window.location.href = '/login', 3000);
        } else {
          toast.error(response.error_msg || 'Something went wrong.');
        }
      } catch (error) {
        console.error('Error deleting account:', error);
        toast.error('Failed to delete account.');
      } finally {
        setIsDeleting(false);
      }
    }, 2000); // ⏳ 2 second simulated delay
  };
  

  const isDeleteButtonDisabled = confirmText !== 'DELETE';

  return (
    <div className="delete-account-container">

      <div className="section-header">
        <h3>Delete Account</h3>
        <p>Permanently delete your account and all associated data</p>
      </div>

      <div className="warning-card mb-4">
        <div className="warning-icon">
          <Icon icon="mdi:alert-circle-outline" width="24" height="24" />
        </div>
        <div className="warning-content">
          <h4>Warning: This action cannot be undone</h4>
          <p>
            Deleting your account will permanently remove all your data, including:
          </p>
          <ul>
            <li>Course progress and completion records</li>
            <li>Certificates and achievements</li>
            <li>Personal information and profile data</li>
            <li>Payment history and subscription details</li>
          </ul>
          <p>
            If you're experiencing issues with the platform, please consider
            <a href="#" className="ms-1">contacting support</a> before deleting your account.
          </p>
        </div>
      </div>

      <div className="delete-account-action">
        <button
          className="btn btn-danger"
          onClick={handleOpenModal}
          disabled={true}
        >
          <Icon icon="mdi:delete-outline" width="18" height="18" className="me-2" />
          Coming Soon
        </button>
      </div>

      {/* Confirmation Modal */}
      <Modal show={showModal} onHide={handleCloseModal} centered>
        <Modal.Header closeButton>
          <Modal.Title className="text-danger">
            <Icon icon="mdi:alert-circle-outline" width="24" height="24" className="me-2" />
            Confirm Account Deletion
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <p className="mb-4">
            We're sorry to see you go. Before you proceed, please help us understand why you're leaving:
          </p>

          <div className="mb-3">
            <label className="form-label">Reason for leaving (optional)</label>
            <select
              className="form-select"
              value={reason}
              onChange={handleReasonChange}
            >
              <option value="">Select a reason</option>
              <option value="not_useful">I don't find the platform useful</option>
              <option value="too_expensive">The service is too expensive</option>
              <option value="difficult_to_use">The platform is difficult to use</option>
              <option value="found_alternative">I found a better alternative</option>
              <option value="privacy_concerns">I have privacy concerns</option>
              <option value="other">Other reason</option>
            </select>
          </div>

          <div className="alert alert-danger">
            <p className="mb-2"><strong>This action cannot be undone.</strong></p>
            <p className="mb-0">All your data will be permanently deleted and cannot be recovered.</p>
          </div>

          <div className="mb-3">
            <label className="form-label">Type DELETE to confirm</label>
            <input
              type="text"
              className="form-control"
              value={confirmText}
              onChange={handleConfirmTextChange}
              placeholder="DELETE"
            />
          </div>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="outline-secondary" onClick={handleCloseModal}>
            Cancel
          </Button>
          <Button
            variant="danger"
            onClick={handleDeleteAccount}
            disabled={isDeleteButtonDisabled || isDeleting}
          >
            {isDeleting ? (
              <>
                <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                Deleting...
              </>
            ) : (
              'Permanently Delete Account'
            )}
          </Button>
        </Modal.Footer>
      </Modal>
    </div>
  );
}

export default DeleteAccount;
