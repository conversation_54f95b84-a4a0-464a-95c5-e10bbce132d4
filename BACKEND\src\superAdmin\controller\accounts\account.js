// const { response } = require('express');
// const jwt = require('jsonwebtoken');
// const bcrypt = require('bcrypt');
// const { mysqlServerConnection } = require('../../../db/db');
// const createdb = require('../../../superAdmin/db/utils/createOrganizationDb');
// const { Validate, hashPassword, generateAccessToken, generateRefreshToken, sendEmail } = require('../../../tools/tools');
// const { sendMagicLink, sendForgotPasswordLink, sendOTP } = require('../../../tools/emailtemplates');
// const { body } = require('express-validator');
// const crypto = require('crypto');
// const maindb = process.env.MAIN_DB;

// const superAdminLoginUser = async (req, res) => {
//     try {
//         const result = req.body;
//         console.log("From OrganizationLoginUser :",result, "++++____----",maindb)
//         if (Object.keys(result).length == 0) {
//             return res.status(422).json({
//                 success: false,
//                 data: {
//                     error_msg: 'Following fields are required',
//                     response: {
//                         organization_id: "",
//                         username: "",
//                         password: ""
//                     }
//                 }
//             });
//         }
//         else {
//             const validationError = Validate(req, res);
//             if (validationError) return validationError;

//             // const [dbmain] = await mysqlServerConnection.query(`SELECT db_name FROM ${maindb}.organization WHERE id = ?`, [result.organization_id]);

//             const [rows] = await mysqlServerConnection.query(
//                 `SELECT users.*, roles.name AS role, roles.id AS role_id
//                  FROM ${maindb}.users
//                  JOIN ${maindb}.user_roles ON users.id = user_roles.user_id
//                  JOIN ${maindb}.roles ON user_roles.role_id = roles.id
//                  WHERE users.is_verified=true and users.email = ? OR users.mobile = ?`,
//                 [result.username, result.username]
//             );

//             if (rows.length > 0) {
//                 const user = rows[0];
//                 const passwordMatch = await bcrypt.compare(result.password, user.password);
//                 const accessToken = await generateAccessToken({ id: user.id, db_name: maindb, username: result.username, role: user.role, role_id: user.role_id });
//                 const refreshToken = await generateRefreshToken({ id: user.id, db_name: maindb, username: result.username, role: user.role, role_id: user.role_id });

//                 await mysqlServerConnection.query(`
//                 INSERT INTO ${user.db_name}.user_logs (user_id,log_name,log_description)
//                 VALUES (?,?,?) 
//                 `,[user.id,"login",`${user.name} logged in`])

//                 if (passwordMatch) {
//                     return res.status(200).json({
//                         success: true, data: {
//                             message: 'Login Successful.',
//                             access_token: accessToken,
//                             refresh_token: refreshToken,
//                             role: user.role,
//                             user_details: user
//                         }
//                     });
//                 }
//                 else {
//                     return res.status(401).json({
//                         success: false, data: {
//                             error_msg: 'Username or Password is incorrect'
//                         }
//                     });

//                 }
//             }
//             else {
//                 return res.status(401).json({
//                     success: false, data: {
//                         error_msg: 'Username or Password is incorrect'
//                     }
//                 });

//             }
//         }

//     } catch (error) {
//         console.error('Error', error);
//         res.status(500).json({ success: false, data: { error_msg: 'Internal server error.' } });
//     }
// }


// module.exports = {
//     superAdminLoginUser,

// };