import React, { useState, useRef, useEffect } from 'react';
import { Icon } from '@iconify/react';
import { toast } from 'react-toastify';
import { useNavigate } from 'react-router-dom';
import { getLogoByDomainAndAlt } from '../../utils/logoUtils';
import countryData from '../../utils/countory.json';
import { validateEmail, validatePassword, validateName, validatePhone, validateConfirmPassword, validateForm } from '../../utils/validation';
import { newRegistrationApi } from '../../services/authService';

function Register() {
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    password: '',
    confirmPassword: ''
  });
  const [selectedCountryCode, setSelectedCountryCode] = useState('+91');
  const [showCountryDropdown, setShowCountryDropdown] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredCountries, setFilteredCountries] = useState([]);
  const dropdownRef = useRef(null);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [passwordRequirements, setPasswordRequirements] = useState({
    length: false,
    letters: false,
    numbers: false,
    special: false
  });
  const [errors, setErrors] = useState({});
  const [isFormValid, setIsFormValid] = useState(false);

  // Filter countries based on search query
  useEffect(() => {
    // Filter countries based on search query
    if (searchQuery.trim() === '') {
      setFilteredCountries(countryData.countoryCode);
    } else {
      const filtered = countryData.countoryCode.filter(country => 
        country.country.toLowerCase().includes(searchQuery.toLowerCase())
      );
      setFilteredCountries(filtered);
    }
  }, [searchQuery]);

  // Handle Click Outside
  useEffect(() => {
    // Close dropdown when clicking outside
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setShowCountryDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Initialize filtered countries
  useEffect(() => {
    setFilteredCountries(countryData.countoryCode);
  }, []);

  // Validation Rules
  const validationRules = {
    name: validateName,
    email: validateEmail,
    phone: validatePhone,
    password: validatePassword,
    confirmPassword: validateConfirmPassword
  };

  // Check password requirements
  const checkPasswordRequirements = (password) => {
    return {
      length: password.length >= 8,
      letters: /[a-zA-Z]/.test(password),
      numbers: /[0-9]/.test(password),
      special: /[!@#$%^&*(),.?":{}|<>]/.test(password)
    };
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    
    // Update both form data and clear errors in a single function
    const newFormData = { ...formData, [name]: value };
    const newErrors = { ...errors, [name]: undefined };
    
    // Update password requirements if password field changes
    if (name === 'password') {
      setPasswordRequirements(checkPasswordRequirements(value));
      // Clear confirm password error if password changes
      if (newFormData.confirmPassword) {
        const confirmPasswordErrors = validateConfirmPassword(newFormData.confirmPassword, value);
        if (confirmPasswordErrors.length > 0) {
          newErrors.confirmPassword = confirmPasswordErrors;
        }
      }
    }
    
    // Check confirm password match if confirm password is being changed
    if (name === 'confirmPassword' && newFormData.password) {
      const confirmPasswordErrors = validateConfirmPassword(value, newFormData.password);
      if (confirmPasswordErrors.length > 0) {
        newErrors.confirmPassword = confirmPasswordErrors;
      }
    }
    
    setFormData(newFormData);
    setErrors(newErrors);
  };

  const handleCountrySelect = (code) => {
    console.log('Selected country code:', code);
    setSelectedCountryCode(code);
    setShowCountryDropdown(false);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    const validation = validateForm(formData, validationRules);
    if (validation.isValid) {
      setIsLoading(true);
      
      const organization_url = window.location.origin;

      const registrationData = {
        name: formData.name,
        email: formData.email,
        phone: formData.phone,
        password: formData.password,
        countryCode: selectedCountryCode,
        organization_url: organization_url
      };
      
      try {
        const response = await newRegistrationApi(registrationData);
        console.log('Registration Response:', response);

        if (response.success) {
          // Store registration data in sessionStorage with country code
          sessionStorage.setItem('registrationData', JSON.stringify(registrationData));

          toast.success('Registration successful! Please verify OTP', {
            position: "top-center",
            autoClose: 2000,
            hideProgressBar: false,
            closeOnClick: true,
            pauseOnHover: true,
            draggable: true,
            onClose: () => {
              setIsLoading(false); // Turn off loader only when redirecting
              navigate('/auth/register-otp', {
                state: {
                  email: formData.email
                }
              });
            }
          });
        }
      } catch (error) {
        console.error('Registration Error:', error);

        // Handle 4004 error code specifically
        if (error.response?.status === 4004 || error.response?.data?.code === 4004) {
          toast.error('Email already registered!', {
            position: "top-center",
            autoClose: 2000,
            hideProgressBar: false,
            closeOnClick: true,
            pauseOnHover: true,
            draggable: true
          });
        } else {
          // Handle other errors
          toast.error(error.response?.data?.message || 'Registration failed. Please try again.', {
            position: "top-center",
            autoClose: 2000,
            hideProgressBar: false,
            closeOnClick: true,
            pauseOnHover: true,
            draggable: true
          });
        }
        setIsLoading(false); // Turn off loader immediately for errors
      }
    } else {
      setErrors(validation.errors);
    }
  };

  return (
    <div className="auth-container">
      <div className="auth-logo">
        <img src={getLogoByDomainAndAlt().logo} alt={getLogoByDomainAndAlt().alt} width={230} />
      </div>
      
      <div className="auth-card">
        <div className="auth-header">
          <h2>Create an Account</h2>
          <p>Fill in your details to get started</p>
        </div>
        
        <form onSubmit={handleSubmit} className="auth-form">
          <div className="auth-form-group">
            <label htmlFor="name">Full Name</label>
            <div className="auth-input-wrapper">
              <Icon icon="mdi:account-outline" className="auth-input-icon" />
              <input 
                type="text" 
                id="name" 
                name="name"
                value={formData.name}
                onChange={handleChange}
                placeholder="John Doe" 
                className={`auth-input ${errors.name ? 'error' : ''}`}
                disabled={isLoading}
              />
            </div>
            {errors.name && <span className="auth-error-message">{errors.name[0]}</span>}
          </div>
          
          <div className="auth-form-group">
            <label htmlFor="email">Email Address</label>
            <div className="auth-input-wrapper">
              <Icon icon="mdi:email-outline" className="auth-input-icon" />
              <input 
                type="text"
                id="email" 
                name="email"
                value={formData.email}
                onChange={handleChange}
                placeholder="<EMAIL>" 
                className={`auth-input ${errors.email ? 'error' : ''}`}
                disabled={isLoading}
              />
            </div>
            {errors.email && <span className="auth-error-message">{errors.email[0]}</span>}
          </div>
          
          <div className="auth-form-group">
            <label htmlFor="phone">Phone Number</label>
            <div className="d-flex gap-2">
              <div className="auth-input-wrapper country-code-wrapper" ref={dropdownRef}>
                <div className="country-code-select" onClick={() => setShowCountryDropdown(!showCountryDropdown)}>
                  <span>{selectedCountryCode}</span>
                  <Icon icon="mdi:chevron-down" className="dropdown-icon" />
                </div>

                {showCountryDropdown && (
                  <div className="country-dropdown">
                    <div className="country-search">
                      <input
                        type="text"
                        placeholder="Search country..."
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                      />
                    </div>
                    <div className="country-list">
                      {filteredCountries.map((country, index) => (
                        <div
                          key={index}
                          className="country-item"
                          onClick={() => handleCountrySelect(country.code)}
                        >
                          <span>{country.country}</span>
                          <span>{country.code}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
              
              <div className="auth-input-wrapper flex-grow-1">
                <Icon icon="mdi:phone-outline" className="auth-input-icon" />
                <input 
                  type="tel"
                  id="phone" 
                  name="phone"
                  value={formData.phone}
                  onChange={handleChange}
                  placeholder="Enter 5-15 digits" 
                  className={`auth-input ${errors.phone ? 'error' : ''}`}
                  disabled={isLoading}
                />
              </div>
            </div>
            {errors.phone && <span className="auth-error-message">{errors.phone[0]}</span>}
          </div>
          
          <div className="auth-form-group">
            <label htmlFor="password">Password</label>
            <div className="auth-input-wrapper">
              <Icon icon="mdi:lock-outline" className="auth-input-icon" />
              <input 
                type={showPassword ? "text" : "password"} 
                id="password" 
                name="password"
                value={formData.password}
                onChange={handleChange}
                placeholder="••••••" 
                className={`auth-input ${errors.password ? 'error' : ''}`}
                disabled={isLoading}
              />
              <button 
                type="button" 
                className="auth-password-toggle" 
                onClick={() => setShowPassword(!showPassword)}
                aria-label={showPassword ? "Hide password" : "Show password"}
                disabled={isLoading}
              >
                <Icon icon={showPassword ? "mdi:eye-off-outline" : "mdi:eye-outline"} />
              </button>
            </div>
            {errors.password && <span className="auth-error-message">{errors.password[0]}</span>}
            
            {/* Password Requirements */}
            <div className="password-requirements">
              <div className={`requirement-badge ${passwordRequirements.length ? 'valid' : ''}`}>
                <Icon icon={passwordRequirements.length ? "mdi:check" : "mdi:close"} className="icon" />
                8+ Characters
              </div>
              <div className={`requirement-badge ${passwordRequirements.letters ? 'valid' : ''}`}>
                <Icon icon={passwordRequirements.letters ? "mdi:check" : "mdi:close"} className="icon" />
                Letters
              </div>
              <div className={`requirement-badge ${passwordRequirements.numbers ? 'valid' : ''}`}>
                <Icon icon={passwordRequirements.numbers ? "mdi:check" : "mdi:close"} className="icon" />
                Numbers
              </div>
              <div className={`requirement-badge ${passwordRequirements.special ? 'valid' : ''}`}>
                <Icon icon={passwordRequirements.special ? "mdi:check" : "mdi:close"} className="icon" />
                Special Chars
              </div>
            </div>
          </div>
          
          <div className="auth-form-group">
            <label htmlFor="confirmPassword">Confirm Password</label>
            <div className="auth-input-wrapper">
              <Icon icon="mdi:lock-outline" className="auth-input-icon" />
              <input 
                type={showConfirmPassword ? "text" : "password"} 
                id="confirmPassword" 
                name="confirmPassword"
                value={formData.confirmPassword}
                onChange={handleChange}
                placeholder="••••••" 
                className={`auth-input ${errors.confirmPassword ? 'error' : ''}`}
                disabled={isLoading}
              />
              <button 
                type="button" 
                className="auth-password-toggle" 
                onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                aria-label={showConfirmPassword ? "Hide password" : "Show password"}
                disabled={isLoading}
              >
                <Icon icon={showConfirmPassword ? "mdi:eye-off-outline" : "mdi:eye-outline"} />
              </button>
            </div>
            {errors.confirmPassword && <span className="auth-error-message">{errors.confirmPassword[0]}</span>}
          </div>
          
          <button 
            type="submit" 
            className={`btn btn-primary ${isLoading ? 'loading' : ''}`}
            disabled={isLoading}
          >
            {isLoading ? (
              <>
                <div className="spinner"></div>
                Creating Account...
              </>
            ) : (
              'Create Account'
            )}
          </button>
          
          <div className="auth-options">
            Already have an account? <a href="/auth/login" className={isLoading ? 'disabled-link' : ''}>Login</a>
          </div>
        </form>
      </div>
    </div>
  );
}

export default Register;