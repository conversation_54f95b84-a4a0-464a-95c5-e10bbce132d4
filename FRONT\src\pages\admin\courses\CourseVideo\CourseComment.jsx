import React, { useState, useEffect } from 'react';
import { Icon } from '@iconify/react';
import DefaultImage from '../../../../assets/images/profile/default-profile.png';
import { toast } from 'react-toastify';
import './CourseComment.css';
import { getComments, addComment, deleteComment, updateComment, getProfileComments } from '../../../../services/userService';
import { updateLike, addReplyComment, getRepliesByCommentId, deleteReplyComment } from '../../../../services/userService';

function CourseComment({ contentData, player }) {
  const [comments, setComments] = useState([]);
  const [userProfiles, setUserProfiles] = useState({});
  const [showInput, setShowInput] = useState(false);
  const [commentText, setCommentText] = useState('');
  const [editingCommentId, setEditingCommentId] = useState(null);
  const [replyTexts, setReplyTexts] = useState({});
  const [showReplyInputs, setShowReplyInputs] = useState({});
  const [showReplies, setShowReplies] = useState({});
  const [replies, setReplies] = useState({});

  const user = JSON.parse(localStorage.getItem('user'));
  const userId = user?.id;

  console.log('userId--------------local-----------', userId);

  const videoId = contentData?.id;
  const moduleId = contentData?.module_id;

  useEffect(() => {
    if (videoId) {
      fetchComments();
    }
  }, [videoId]);

  useEffect(() => {


  }, [comments]);

  const fetchCommentsRepliesByCommentId = async (commentId) => {
    try {
      const response = await getRepliesByCommentId({ comment_id: commentId });
      console.log('Replies for comment', commentId, ':', response);
      if (response.success) {
        setReplies(prev => ({
          ...prev,
          [commentId]: response.data
        }));
      }
  
    }catch (error) {
      console.error('Error fetching replies:', error);
    }
  }

  const fetchUserProfile = async (userId) => {
    try {
      const response = await getProfileComments(userId);
      console.log('response-------------------------', response);
      if (response.success) {
        setUserProfiles(prev => ({
          ...prev,
          [userId]: response.data
        }));
      }
    } catch (error) {
      console.error('Error fetching user profile:', error);
    }
  };

  const fetchComments = async () => {
    try {
      const response = await getComments(videoId);
      console.log('Comments response:', response);
      if (response.success) {
        setComments(response.data);
        // Fetch user profiles for each unique user_id
        const uniqueUserIds = [...new Set(response.data.map(comment => comment.user_id))];
        uniqueUserIds.forEach(userId => {
          fetchUserProfile(userId);
        });
      }
    } catch (error) {
      console.error('Error fetching comments:', error);
    }
  };

  const fetchReplies = async (commentId) => {
    try {
      const response = await getRepliesByCommentId({ comment_id: commentId });
      console.log('Replies for comment', commentId, ':', response);
      if (response.success) {
        setReplies(prev => ({
          ...prev,
          [commentId]: response.data
        }));
      }
    } catch (error) {
      console.error('Error fetching replies:', error);
    }
  };

  const formatTime = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const handleSaveComment = async () => {
    if (commentText.trim()) {
      try {
        const currentTime = player ? Math.floor(player.getCurrentTime()) : 0;
        const formattedTime = formatTime(currentTime);

        if (editingCommentId) {
          // Handle edit comment
          const response = await updateComment({
            commentId: editingCommentId,
            comment: commentText
          });
          
          if (response.success) {
            toast.success('Comment updated successfully');
            await fetchComments(); // Refresh comments
          } else {
            toast.error('Failed to update comment');
          }
        } else {
          // Add new comment
          const response = await addComment({
            comment: commentText,
            video_id: videoId,
            module_id: moduleId,
            stream_time: formattedTime
          });
          
          if (response.success) {
            toast.success('Comment added successfully');
            await fetchComments();
          } else {
            toast.error('Failed to add comment');
          }
        }
        
        setCommentText('');
        setShowInput(false);
        setEditingCommentId(null);
      } catch (error) {
        console.error('Error saving comment:', error);
        toast.error('Failed to save comment');
      }
    }
  };

  if (!videoId || !moduleId) {
    return <div className="no-content-message">No content selected</div>;
  }

  const handleDeleteComment = async (commentId) => {
    try {
      const response = await deleteComment(commentId);
      toast.success('Comment deleted successfully');
      
      if (response.success) {
        // Immediately remove comment from UI
        setComments(prevComments => prevComments.filter(comment => comment.id !== commentId));
        // Refresh comments to ensure sync
        await fetchComments();
      }
    } catch (error) {
      console.error('Error deleting comment:', error);
    }
  };

  const handleReplySubmit = async (commentId) => {
    if (replyTexts[commentId]?.trim()) {
      try {
        const response = await addReplyComment({
          parent_comment_id: commentId,
          user_id: userId,
          comment: replyTexts[commentId],
        });

        if (response.success) {
          toast.success('Reply added successfully');
          
          // Update replies state immediately with the new reply
          setReplies(prev => ({
            ...prev,
            [commentId]: [
              {
                id: response.data.id,
                comment_id: commentId,
                user_id: userId,
                reply_comment: replyTexts[commentId],
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString(),
                name: userProfiles[userId]?.name || 'You'
              },
              ...(prev[commentId] || [])
            ]
          }));

          // Clear reply text and hide input
          setReplyTexts(prev => ({ ...prev, [commentId]: '' }));
          setShowReplyInputs(prev => ({ ...prev, [commentId]: false }));
        } else {
          toast.error(response.data?.error_msg || 'Failed to add reply');
        }
      } catch (error) {
        console.error('Error adding reply:', error);
        const errorMsg = error.response?.data?.data?.error_msg || 'Failed to add reply';
        toast.error(errorMsg);
      }
    }
  };

  const handleDeleteReply = async (replyId, commentId) => {
    try {
      let replyCommentId = replyId;
      const response = await deleteReplyComment(replyCommentId);
      
      if (response.success) {
        toast.success('Reply deleted successfully');
        
        // Update the UI by removing the deleted reply
        setReplies(prev => ({
          ...prev,
          [commentId]: prev[commentId].filter(reply => reply.id !== replyId)
        }));
      } else {
        toast.error('Failed to delete reply');
      }
    } catch (error) {
      console.error('Error deleting reply:', error);
      const errorMsg = error.response?.data?.error_msg || 'Failed to delete reply';
      toast.error(errorMsg);
    }
  };

  return (
    <div className="course-comments-container p-0">
      <div className="comments-header">
        <div className="col-8 col-md-3 ms-auto">
          <button className="btn btn-primary add-comment-btn" onClick={() => {
            setShowInput(true);
            setEditingCommentId(null);
            setCommentText('');
          }}>
            <Icon icon="mdi:plus" className="me-2" />
            Add Comment
          </button>
        </div>
      </div>

      {showInput && (
        <div className="comment-input-container">
          <textarea
            className="comment-input"
            placeholder="Write a comment..."
            value={commentText}
            onChange={(e) => setCommentText(e.target.value)}
          ></textarea>
          <div className="comment-actions">
            <button 
              className="btn btn-secondary" 
              onClick={() => {
                setCommentText('');
                setShowInput(false);
                setEditingCommentId(null);
              }}
            >
              Cancel
            </button>
            <button className="btn btn-primary" onClick={handleSaveComment}>
              {editingCommentId ? 'Update' : 'Post'}
            </button>
          </div>
        </div>
      )}

      <div className="comments-list">
        {comments.length > 0 ? (
          comments.map(comment => (
            <div key={comment.id} className="comment-item">
              <div className="comment-header">
                <div className="comment-author">
                  <img 
                    src={userProfiles[comment.user_id]?.profile_pic || DefaultImage} 
                    alt="avatar" 
                    className="author-avatar" 
                  />
                  <span className="author-name">
                    {userProfiles[comment.user_id]?.name || 'Loading...'}
                  </span>
                  {comment.updatedAt && comment.updatedAt !== comment.createdAt && (
                    <span className="comment-edited">(edited)</span>
                  )}
                </div>
                <div className="comment-meta">
                  <div className="comment-actions">
                    <span className="comment-time me-2">{comment.stream_time}</span>
                    <span className="comment-date me-3">
                      {new Date(comment.createdAt).toLocaleDateString()}
                    </span>
                    {comment.user_id === userId && (
                      <>
                        <button 
                          className="border border-black px-1 py-0 rounded-1 text-black me-2"
                          onClick={() => {
                            setCommentText(comment.comment);
                            setEditingCommentId(comment.id);
                            setShowInput(true);
                          }}
                          title="Edit comment"
                        >
                          <Icon icon="mdi:pencil" className="edit-icon" />
                        </button>
                        <button 
                          className="border border-black px-1 py-0 rounded-1 text-black"
                          onClick={() => handleDeleteComment(comment.id)}
                          title="Delete comment"
                        >
                          <Icon icon="mdi:delete" className="delete-icon" />
                        </button>
                      </>
                    )}
                  </div>
                </div>
              </div>
              <p className="comment-text">{comment.comment}</p>
              
              {/* Reply section */}
              <div className="comment-footer">
                <div className="d-flex justify-content-end">
                  <button 
                    className="btn btn-link text-primary p-0"
                    onClick={() => {
                      const isCurrentlyShown = showReplyInputs[comment.id];
                      setShowReplyInputs(prev => ({
                        ...prev,
                        [comment.id]: !isCurrentlyShown
                      }));
                      setShowReplies(prev => ({
                        ...prev,
                        [comment.id]: !isCurrentlyShown
                      }));
                      if (!isCurrentlyShown) {
                        // Only fetch replies when opening
                        fetchCommentsRepliesByCommentId(comment.id);
                      }
                    }}
                  >
                    <Icon icon="mdi:reply" className="me-1" />
                    Reply {replies[comment.id]?.length > 0 && `(${replies[comment.id].length})`}
                  </button>
                </div>
              </div>

              {/* Reply input field */}
              {showReplyInputs[comment.id] && (
                <div className="reply-input-container mt-2">
                  <div className="replying-to mb-2">
                    Replying to {userProfiles[comment.user_id]?.name || 'comment'}
                  </div>
                  <textarea
                    className="reply-input form-control"
                    placeholder="Write a reply..."
                    value={replyTexts[comment.id] || ''}
                    onChange={(e) => setReplyTexts(prev => ({
                      ...prev,
                      [comment.id]: e.target.value
                    }))}
                  ></textarea>
                  <div className="reply-actions mt-2 d-flex justify-content-end">
                    <button 
                      className="btn btn-secondary me-2"
                      onClick={() => {
                        setShowReplyInputs(prev => ({ ...prev, [comment.id]: false }));
                        setReplyTexts(prev => ({ ...prev, [comment.id]: '' }));
                      }}
                    >
                      Cancel
                    </button>
                    <button 
                      className="btn btn-primary"
                      onClick={() => handleReplySubmit(comment.id)}
                    >
                      Reply
                    </button>
                  </div>
                </div>
              )}

              {/* Replies container */}
              {showReplies[comment.id] && replies[comment.id] && replies[comment.id].length > 0 && (
                <div className="replies-container ms-4 mt-2">
                  {replies[comment.id].map(reply => (
                    <div key={reply.id} className="reply-item mb-3">
                      <div className="reply-header d-flex justify-content-between align-items-start">
                        <div className="reply-author d-flex align-items-center">
                          <img 
                            src={userProfiles[reply.user_id]?.profile_pic || DefaultImage} 
                            alt="avatar" 
                            className="author-avatar" 
                            style={{ width: '24px', height: '24px' }}
                          />
                          <span className="author-name ms-2">
                            {reply.name || userProfiles[reply.user_id]?.name || 'Loading...'}
                          </span>
                        </div>
                        <div className="reply-meta d-flex align-items-center">
                          <span className="reply-date me-2">
                            {new Date(reply.createdAt).toLocaleDateString()}
                          </span>
                          {/* {Number(reply.user_id) === Number(userId) && (
                            <button 
                              className="border border-black px-1 py-0 rounded-1 text-black"
                              onClick={() => {
                                let replyCommentId = reply.id;
                                handleDeleteReply(replyCommentId, comment.id);
                              }}
                              title="Delete reply"
                            >
                              <Icon icon="mdi:delete" className="delete-icon" />
                            </button>
                          )} */}
                        </div>
                      </div>
                      <p className="reply-text mt-1 mb-0">{reply.reply_comment || reply.comment}</p>
                    </div>
                  ))}
                </div>
              )}
            </div>
          ))
        ) : (
          <div className="empty-comments">
            <p>No comments yet. Click "Add Comment" to start the discussion.</p>
          </div>
        )}
      </div>
    </div>
  );
}

export default CourseComment;