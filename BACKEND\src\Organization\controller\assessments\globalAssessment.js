const { mysqlServerConnection } = require('../../../db/db');

const getGlobalAssessmentStatus = async (req, res) => {
    try {
        const page = parseInt(req.body.page, 10) || 1;
        const limit = parseInt(req.body.limit, 10) || 10;
        const search = req.body.search ? `%${req.body.search}%` : '';
        const offset = (page - 1) * limit;
        const searchCondition = search ? 'AND (ga.name LIKE ? OR a.status LIKE ?)' : '';

        if (!req.user || !req.user.db_name) {
            return res.status(400).json({ success: false, data: { error_msg: 'Invalid user information.' } });
        }

        const [modules] = await mysqlServerConnection.query(
            `SELECT 
                ga.id, 
                u.name AS created_by_name, 
                ga.name as assessment_name,
                COUNT(DISTINCT au.id) as total_attempts,
                COUNT(DISTINCT CASE WHEN au.is_completed = true THEN au.id END) as completed_count
             FROM ${req.user.db_name}.global_assessment ga
             LEFT JOIN ${req.user.db_name}.assessments a ON ga.id = a.global_assessment
             LEFT JOIN ${req.user.db_name}.users u ON a.created_by = u.id
             LEFT JOIN ${req.user.db_name}.assessment_users au ON a.id = au.assessment_id
             WHERE a.is_private = false ${searchCondition}
             GROUP BY ga.id, u.name, ga.name, a.is_active, a.is_approved
             ORDER BY ga.id DESC
             LIMIT ? OFFSET ?`,
            search ? [search, search, limit, offset] : [limit, offset]
        );

        const [[{ count }]] = await mysqlServerConnection.query(
            `SELECT COUNT(*) AS count FROM ${req.user.db_name}.global_assessment ga 
             LEFT JOIN ${req.user.db_name}.assessments a ON ga.id = a.global_assessment
             WHERE a.is_private = false ${searchCondition}`,
            search ? [search, search] : []
        );

        const groupedByAssessment = modules.reduce((acc, module) => {
            const assessmentName = module.assessment_name || 'Unknown Assessment';
            if (!acc[assessmentName]) {
                const completionPercentage = module.total_attempts > 0 
                    ? Math.round((module.completed_count / module.total_attempts) * 100) 
                    : 0;
                    
                acc[assessmentName] = {
                    assessment_id: module.id,
                    assessment_name: assessmentName,
                    created_by_name: module.created_by_name,
                    is_active: module.is_active || false,
                    is_approved: module.is_approved || false,
                    total_attempts: module.total_attempts || 0,
                    completed_count: module.completed_count || 0,
                    completion_percentage: completionPercentage,
                    assessment_count: 0
                };
            }
            acc[assessmentName].assessment_count++;
            return acc;
        }, {});

        res.status(200).json({
            success: true,
            data: {
                message: 'Global assessments retrieved successfully.',
                response: {
                    assessments: Object.values(groupedByAssessment),
                    pagination: {
                        page,
                        limit,
                        totalPages: Math.ceil(count / limit),
                        totalCount: count
                    }
                }
            }
        });
    } catch (error) {
        console.error('Error retrieving global assessments:', error);
        res.status(500).json({ success: false, data: { error_msg: 'Internal server error.' } });
    }
};

const getGlobalAssessmentData = async (req, res) => {
    try {
        const page = parseInt(req.body.page, 10) || 1;
        const limit = parseInt(req.body.limit, 10) || 10;
        const search = req.body.search ? `%${req.body.search}%` : '';
        const assessmentId = parseInt(req.body.assessment_id, 10);

        if (!assessmentId) {
            return res.status(400).json({ success: false, data: { error_msg: 'Invalid assessment_id.' } });
        }

        const offset = (page - 1) * limit;
        const searchCondition = search ? 'AND (u.name LIKE ? OR au.status LIKE ?)' : '';

        if (!req.user || !req.user.db_name) {
            return res.status(400).json({ success: false, data: { error_msg: 'Invalid user information.' } });
        }

        const [modules] = await mysqlServerConnection.query(
            `SELECT 
                au.id,
                u.name AS user_name,
                au.status,
                au.is_completed,
                au.createdAt,
                (SELECT COUNT(*) FROM ${req.user.db_name}.questions q WHERE q.assessment_id = a.id) AS question_count,
                (SELECT COUNT(*) 
                 FROM ${req.user.db_name}.question_answer qa 
                 INNER JOIN ${req.user.db_name}.questions q ON qa.question_id = q.id 
                 WHERE q.assessment_id = a.id AND qa.user_id = au.user_id) AS completed_question_count
             FROM ${req.user.db_name}.assessments a
             INNER JOIN ${req.user.db_name}.global_assessment ga ON a.global_assessment = ga.id
             LEFT JOIN ${req.user.db_name}.assessment_users au ON a.id = au.assessment_id
             LEFT JOIN ${req.user.db_name}.users u ON au.user_id = u.id
             WHERE ga.id = ? ${searchCondition}
             ORDER BY au.createdAt DESC
             LIMIT ? OFFSET ?`,
            search ? [assessmentId, search, search, limit, offset] : [assessmentId, limit, offset]
        );

        // Add detailed question data and pass/fail status for each module
        for (const module of modules) {
            const [completedQuestions] = await mysqlServerConnection.query(
                `SELECT 
                    qa.id AS answer_id,
                    qa.question_id,
                    q.question,
                    qa.answer,
                    qa.collect_point,
                    qo.options AS correct_option,
                    qo.is_correct,
                    (CASE WHEN qa.answer = qo.id AND qo.is_correct = 1 THEN 1 ELSE 0 END) AS is_correct_answer
                 FROM ${req.user.db_name}.question_answer qa
                 INNER JOIN ${req.user.db_name}.questions q ON qa.question_id = q.id
                 LEFT JOIN ${req.user.db_name}.question_options qo ON q.id = qo.question_id AND qo.is_correct = 1
                 WHERE q.assessment_id = ? AND qa.user_id = ?`,
                [module.assessment_id, module.user_id]
            );

            module.completed_questions = completedQuestions;

            // Calculate correct answers and pass/fail status
            const correctAnswersCount = completedQuestions.reduce((count, question) => count + question.is_correct_answer, 0);
            const totalQuestions = module.question_count;

            const correctPercentage = totalQuestions > 0 ? (correctAnswersCount / totalQuestions) * 100 : 0;
            module.result = correctPercentage >= 60 ? 'Pass' : 'Fail';
            module.correct_percentage = correctPercentage.toFixed(2);
        }

        // Get total count for pagination
        const [[{ count }]] = await mysqlServerConnection.query(
            `SELECT COUNT(*) AS count 
             FROM ${req.user.db_name}.assessments a
             INNER JOIN ${req.user.db_name}.global_assessment ga ON a.global_assessment = ga.id
             LEFT JOIN ${req.user.db_name}.assessment_users au ON a.id = au.assessment_id
             WHERE ga.id = ? ${searchCondition}`,
            search ? [assessmentId, search, search] : [assessmentId]
        );

        res.status(200).json({
            success: true,
            data: {
                message: 'Assessment data retrieved successfully.',
                response: {
                    modules,
                    pagination: {
                        page,
                        limit,
                        totalPages: Math.ceil(count / limit),
                        totalCount: count
                    }
                }
            }
        });
    } catch (error) {
        console.error('Error retrieving assessment data:', error);
        res.status(500).json({ success: false, data: { error_msg: 'Internal server error.' } });
    }
};

module.exports = {
    getGlobalAssessmentStatus,
    getGlobalAssessmentData
};
