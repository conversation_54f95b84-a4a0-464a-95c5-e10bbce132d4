import React, { useEffect, useState, useRef } from 'react';
import { Icon } from '@iconify/react';
import { io } from 'socket.io-client';
import data from '@emoji-mart/data';
import Picker from '@emoji-mart/react';
import './Community.css';
const WEBSOCKET_URL = process.env.REACT_APP_WEBSOCKET_URL;
console.log("WEBSOCKET_URL:", WEBSOCKET_URL);
const moment = require('moment');

function Cummunity({ classroom_id, group_id, group_name }) {
  console.log("Classroom ID:*******", classroom_id);
  console.log("Group ID:*******", group_id);
  console.log("Group Name:***-------****", group_name);
  
  // Get current user from localStorage
  const currentUser = JSON.parse(localStorage.getItem('user'));
  const currentUserId = currentUser?.id;
  console.log("Current User ID:", currentUserId);

  const [socket, setSocket] = useState(null);
  const [connected, setConnected] = useState(false);
  const [chatMessages, setChatMessages] = useState([]);
  const [pagination, setPagination] = useState({});
  const [messageText, setMessageText] = useState('');
  const [isSending, setIsSending] = useState(false);
  const [replyingTo, setReplyingTo] = useState(null);
  const [editingMessage, setEditingMessage] = useState(null);
  const [showUploadModal, setShowUploadModal] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [isUploading, setIsUploading] = useState(false);
  const [dragActive, setDragActive] = useState(false);
  const [selectedFile, setSelectedFile] = useState(null);
  const [filePreview, setFilePreview] = useState(null);
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const token = localStorage.getItem('token');
  
  // Ref for auto-scrolling
  const messagesEndRef = useRef(null);
  const chatContainerRef = useRef(null);
  const emojiPickerRef = useRef(null);
  const messageInputRef = useRef(null);

  // Function to scroll to bottom
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  // Scroll to bottom when new messages arrive
  useEffect(() => {
    scrollToBottom();
  }, [chatMessages]);

  useEffect(() => {
    // Create Socket.IO connection
    const newSocket = io(WEBSOCKET_URL, {
      withCredentials: true,
      transports: ['websocket'],
      query: {
        token: token
      }
    });

    // Connection event handlers
    newSocket.on('connect', () => {
      console.log('✅ Socket.IO Connected');
      console.log('Socket ID:', newSocket.id);
      setConnected(true);
      
      // Join both classroom and group rooms
      if (group_id) {
        const groupIdStr = group_id.toString();
        newSocket.emit('joinChatRoom', groupIdStr);
        console.log(`Joined group chat room: ${groupIdStr} (${group_name})`);
        
        // Also join with user ID for direct messages
        if (currentUserId) {
          newSocket.emit('joinRoom', currentUserId);
          console.log(`Joined user room: ${currentUserId}`);
        }
        
        // Request messages after joining the room
        console.log('📤 Requesting messages for group:', groupIdStr);
        newSocket.emit('getMessages', groupIdStr, 1, 20);
      }
    });

    // Listen for chat history (initial load only)
    newSocket.on('chatHistory', (data) => {
      console.log('📥 Received chat history:', data);
      if (data.messages) {
        // Check if this is initial load (page 1) or pagination
        const isInitialLoad = !data.pagination || data.pagination.currentPage === 1;

        if (isInitialLoad) {
          // Initial load: replace all messages
          const sortedMessages = [...data.messages].sort((a, b) => a.id - b.id);
          setChatMessages(sortedMessages);
          console.log('Initial messages loaded:', sortedMessages);
        }
        // For pagination, let the handlePaginatedHistory function handle it
      }
      if (data.pagination) {
        setPagination(data.pagination);
        console.log('Pagination:', data.pagination);
      }
    });

    // Listen for new messages
    newSocket.on('newMessage', (message) => {
      console.log('📨 New message received:', message);
      setChatMessages(prev => {
        // Check if message already exists (to avoid duplicates)
        const messageExists = prev.some(msg => msg.id === message.id);
        if (messageExists) {
          console.log('Message already exists, skipping duplicate');
          return prev;
        }
        
        // Add new message and sort to maintain order
        const updated = [...prev, message];
        const sorted = updated.sort((a, b) => a.id - b.id);
        console.log('Updated messages after new message:', sorted);
        return sorted;
      });
    });

    // Listen for errors
    newSocket.on('error', (error) => {
      console.error('❌ Socket error:', error);
    });

    // Listen for settings (blocked/hidden status)
    newSocket.on('setting', (settings) => {
      console.log('⚙️ User settings:', settings);
    });

    // Listen for message deletion
    newSocket.on('messageDeleted', (data) => {
      console.log('🗑️ Message deleted:', data);
      setChatMessages(prev => {
        // Remove the deleted message from the list
        return prev.filter(msg => msg.id !== data.messageId);
      });
    });

    // Listen for message edits
    newSocket.on('chatMessageUpdate', (updatedMessage) => {
      console.log('✏️ Message edited:', updatedMessage);
      // Parse likes and dislikes if they come as strings
      if (updatedMessage.likes && typeof updatedMessage.likes === 'string') {
        try {
          updatedMessage.likes = JSON.parse(updatedMessage.likes);
        } catch (e) {
          console.error('Error parsing likes:', e);
          updatedMessage.likes = [];
        }
      }
      
      if (updatedMessage.dislikes && typeof updatedMessage.dislikes === 'string') {
        try {
          updatedMessage.dislikes = JSON.parse(updatedMessage.dislikes);
        } catch (e) {
          console.error('Error parsing dislikes:', e);
          updatedMessage.dislikes = [];
        }
      }

      // Update the existing message in place
      setChatMessages(prev => {
        const messageIndex = prev.findIndex(msg => msg.id === updatedMessage.id);
        if (messageIndex === -1) return prev;

        const newMessages = [...prev];
        newMessages[messageIndex] = {
          ...prev[messageIndex],
          ...updatedMessage,
          // Preserve the original timestamp and other metadata
          createdAt: prev[messageIndex].createdAt,
          user_id: prev[messageIndex].user_id,
          name: prev[messageIndex].name,
          profile_pic_url: prev[messageIndex].profile_pic_url,
          role: prev[messageIndex].role
        };
        return newMessages;
      });
    });

    // Listen for message updates (likes/dislikes)
    newSocket.on('messageUpdated', (updatedMessage) => {
      console.log('💬 Message updated:', updatedMessage);

      // Parse likes and dislikes if they come as strings
      if (updatedMessage.likes && typeof updatedMessage.likes === 'string') {
        try {
          updatedMessage.likes = JSON.parse(updatedMessage.likes);
        } catch (e) {
          console.error('Error parsing likes:', e);
          updatedMessage.likes = [];
        }
      }

      if (updatedMessage.dislikes && typeof updatedMessage.dislikes === 'string') {
        try {
          updatedMessage.dislikes = JSON.parse(updatedMessage.dislikes);
        } catch (e) {
          console.error('Error parsing dislikes:', e);
          updatedMessage.dislikes = [];
        }
      }

      // Ensure arrays
      updatedMessage.likes = Array.isArray(updatedMessage.likes) ? updatedMessage.likes : [];
      updatedMessage.dislikes = Array.isArray(updatedMessage.dislikes) ? updatedMessage.dislikes : [];

      setChatMessages(prev => {
        return prev.map(msg =>
          msg.id === updatedMessage.id ? { ...msg, ...updatedMessage } : msg
        );
      });
    });

    // Note: Media upload events are now handled in handleFileUpload function

    newSocket.on('disconnect', () => {
      console.log('❌ Socket.IO Disconnected');
      setConnected(false);
    });

    // Store socket in state
    setSocket(newSocket);

    // Cleanup on component unmount
    return () => {
      if (newSocket) {
        newSocket.disconnect();
      }
    };
  }, [classroom_id, group_id, group_name, token]); // Added dependencies

  // Function to load more messages (for pagination)
  const loadMoreMessages = (page) => {
    if (socket && connected && group_id) {
      console.log(`📤 Loading page ${page} of messages`);
      
      // Save current scroll position
      const currentScrollHeight = chatContainerRef.current?.scrollHeight || 0;
      
      // Create a one-time listener for the paginated response
      const handlePaginatedHistory = (data) => {
        console.log('📥 Handling paginated history:', data);

        if (data.messages && data.pagination && data.pagination.currentPage > 1) {
          setChatMessages(prev => {
            console.log('📝 Current messages count:', prev.length);
            console.log('📝 New messages count:', data.messages.length);

            // Sort the new messages by ID
            const newMessages = [...data.messages].sort((a, b) => a.id - b.id);

            // Since we're loading previous messages, they should have lower IDs
            // So we prepend them to the existing messages
            const allMessages = [...newMessages, ...prev];

            // Remove duplicates while maintaining order
            const uniqueMessages = Array.from(
              new Map(allMessages.map(msg => [msg.id, msg])).values()
            );

            // Sort by ID to ensure chronological order
            const finalMessages = uniqueMessages.sort((a, b) => a.id - b.id);
            console.log('📝 Final messages count after pagination:', finalMessages.length);

            return finalMessages;
          });

          // Update pagination
          setPagination(data.pagination);

          // Maintain scroll position after DOM update
          setTimeout(() => {
            if (chatContainerRef.current) {
              const newScrollHeight = chatContainerRef.current.scrollHeight;
              const scrollDiff = newScrollHeight - currentScrollHeight;
              chatContainerRef.current.scrollTop = scrollDiff;
              console.log('📜 Scroll position maintained');
            }
          }, 100);
        }

        // Remove this one-time listener
        socket.off('chatHistory', handlePaginatedHistory);
      };
      
      // Add the one-time listener for pagination
      socket.once('chatHistory', handlePaginatedHistory);

      // Request the messages with pagination flag
      console.log(`📤 Requesting page ${page} for group ${group_id}`);
      socket.emit('getMessages', group_id, page, 20);
    }
  };

  // Function to send message
  const sendMessage = () => {
    if (!messageText.trim() || !socket || !connected || isSending || !group_id) return;

    setIsSending(true);
    
    // Capture the message text before clearing
    const messageToSend = messageText.trim();
    
    const messageData = {
      groupId: group_id.toString(), // Ensure group_id is a string
      message: {
        message: messageToSend,
        message_type: 'text',
        attachment: null,
        media_url: null,
        media_metadata: null,
        reply_to_message_id: replyingTo ? replyingTo.id : null
      },
      token: token
    };

    console.log('📤 Sending message:', messageData);

    // Clear input and reply state immediately for better UX
    setMessageText('');
    setReplyingTo(null);

    // Send message with callback
    socket.emit('sendMessage', messageData, (response) => {
      console.log('✅ Message sent response:', response);
      setIsSending(false);

      // Refresh messages from server to get proper timestamps
      if (response && response.messageId && group_id) {
        console.log('Message sent with ID:', response.messageId);
        console.log('📤 Refreshing messages to get proper timestamps');

        // Request fresh messages from server to ensure proper timestamps
        socket.emit('getMessages', group_id.toString(), 1, 20);
      }
    });
  };

  // Handle Enter key press
  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      if (editingMessage) {
        editMessage();
      } else {
        sendMessage();
      }
    } else if (e.key === 'Escape') {
      if (showEmojiPicker) {
        // Close emoji picker on Escape
        setShowEmojiPicker(false);
      } else if (editingMessage) {
        // Cancel editing on Escape
        setEditingMessage(null);
        setMessageText('');
      }
    }
  };

  // Function to delete message
  const deleteMessage = (messageId) => {
    if (!socket || !connected || !group_id) return;

    const deleteData = {
      messageId: messageId,
      groupId: group_id.toString(),
      token: token
    };

    console.log('🗑️ Deleting message:', deleteData);

    // Emit delete message event
    socket.emit('deleteMessage', deleteData);
    
    // Optimistically remove the message from UI
    setChatMessages(prev => prev.filter(msg => msg.id !== messageId));
  };

  // Function to like a message
  const likeMessage = (messageId) => {
    if (!socket || !connected) return;
    
    // Optimistically update UI
    setChatMessages(prev => prev.map(msg => {
      if (msg.id === messageId) {
        const currentLikes = Array.isArray(msg.likes) ? msg.likes : [];
        const isLiked = currentLikes.includes(currentUserId);
        
        return {
          ...msg,
          likes: isLiked 
            ? currentLikes.filter(id => id !== currentUserId)
            : [...currentLikes, currentUserId],
          // Remove from dislikes if exists
          dislikes: Array.isArray(msg.dislikes) 
            ? msg.dislikes.filter(id => id !== currentUserId)
            : []
        };
      }
      return msg;
    }));

    // Send to server
    socket.emit('likeMessage', {
      messageId: messageId,
      token: token
    });
  };

  // Function to handle file selection (for preview)
  const handleFileSelection = (file) => {
    if (!file) return;

    // Validate file size (20MB limit)
    const maxSize = 20 * 1024 * 1024; // 20MB
    if (file.size > maxSize) {
      alert(`File size (${(file.size / (1024 * 1024)).toFixed(2)}MB) exceeds the 20MB limit`);
      return;
    }

    setSelectedFile(file);

    // Create preview for images and videos
    if (file.type.startsWith('image/') || file.type.startsWith('video/')) {
      const reader = new FileReader();
      reader.onload = () => {
        setFilePreview(reader.result);
      };
      reader.readAsDataURL(file);
    } else {
      setFilePreview(null);
    }
  };

  // Function to handle actual file upload
  const handleFileUpload = async () => {
    if (!selectedFile || !socket || !connected) return;

    // Validate file size (max 20MB)
    const maxSize = 20 * 1024 * 1024; // 20MB
    if (selectedFile.size > maxSize) {
      alert('File size must be less than 20MB');
      return;
    }

    setIsUploading(true);
    setUploadProgress(0);

    try {
      // Check if socket is connected
      if (!socket || !socket.connected) {
        alert('Connection lost. Please refresh the page and try again.');
        setIsUploading(false);
        return;
      }

      // Convert file to buffer for socket transmission (same as MediaUpload.jsx)
      console.log('Converting file to buffer...');
      const arrayBuffer = await selectedFile.arrayBuffer();
      console.log('ArrayBuffer created, size:', arrayBuffer.byteLength);
      const fileData = new Uint8Array(arrayBuffer);
      console.log('Uint8Array created, length:', fileData.length);

      // Listen for upload progress and completion
      const handleMediaUploaded = (response) => {
        console.log('Received upload response:', response);
        clearInterval(progressInterval);
        clearTimeout(uploadTimeout);

        if (response.success) {
          setUploadProgress(100);

          // Send message with media (existing logic)
          const messageData = {
            groupId: group_id.toString(),
            message: {
              message: response.fileName || selectedFile.name,
              message_type: response.messageType,
              attachment: null,
              media_url: response.mediaUrl,
              media_metadata: {
                fileName: response.fileName,
                fileSize: selectedFile.size,
                fileType: selectedFile.type
              },
              reply_to_message_id: replyingTo ? replyingTo.id : null
            },
            token: token
          };

          // Send message with media and refresh messages after
          socket.emit('sendMessage', messageData, (mediaResponse) => {
            console.log('✅ Media message sent response:', mediaResponse);

            // Refresh messages from server to get proper timestamps for media message
            if (mediaResponse && mediaResponse.messageId && group_id) {
              console.log('Media message sent with ID:', mediaResponse.messageId);
              console.log('📤 Refreshing messages to get proper timestamps for media');

              // Request fresh messages from server to ensure proper timestamps
              socket.emit('getMessages', group_id.toString(), 1, 20);
            }
          });

          setReplyingTo(null);

          // Small delay to show 100% progress
          setTimeout(() => {
            setShowUploadModal(false);
            setIsUploading(false);
            setUploadProgress(0);
            setSelectedFile(null);
            setFilePreview(null);
          }, 500);
        } else {
          alert('Upload failed: ' + response.error);
          setIsUploading(false);
          setUploadProgress(0);
        }
        socket.off('mediaUploaded', handleMediaUploaded);
        socket.off('uploadError', handleUploadError);
      };

      socket.on('mediaUploaded', handleMediaUploaded);

      // Listen for upload errors
      const handleUploadError = (error) => {
        console.error('Upload error received:', error);
        clearInterval(progressInterval);
        clearTimeout(uploadTimeout);

        const errorMessage = error.message || error || 'Unknown upload error';
        alert('Upload failed: ' + errorMessage);

        setIsUploading(false);
        setUploadProgress(0);
        setSelectedFile(null);
        setFilePreview(null);
        socket.off('mediaUploaded', handleMediaUploaded);
        socket.off('uploadError', handleUploadError);
      };

      socket.on('uploadError', handleUploadError);

      // Simulate upload progress based on file size
      const fileSizeInMB = selectedFile.size / (1024 * 1024);
      const progressIncrement = fileSizeInMB > 2 ? 5 : 10; // Slower progress for larger files
      const progressDelay = fileSizeInMB > 2 ? 500 : 200; // Longer delays for larger files

      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return 90;
          }
          return prev + progressIncrement;
        });
      }, progressDelay);

      // Calculate timeout based on file size (minimum 60 seconds, +10 seconds per MB)
      const timeoutDuration = Math.max(60000, 60000 + (fileSizeInMB * 10000)); // 60s + 10s per MB

      console.log(`Upload timeout set to ${timeoutDuration/1000} seconds for ${fileSizeInMB.toFixed(2)}MB file`);

      const uploadTimeout = setTimeout(() => {
        clearInterval(progressInterval);
        alert(`Upload failed after ${timeoutDuration/1000} seconds. Please try again with a smaller file or check your connection.`);
        setIsUploading(false);
        setUploadProgress(0);
        setSelectedFile(null);
        setFilePreview(null);
        socket.off('mediaUploaded', handleMediaUploaded);
        socket.off('uploadError', handleUploadError);
      }, timeoutDuration);

      // Send file to server (same format as MediaUpload.jsx)
      console.log(`Uploading file: ${selectedFile.name} (${fileSizeInMB.toFixed(2)}MB)`);
      console.log('Socket connected:', socket.connected);
      console.log('Group ID:', group_id);

      console.log('Converting Uint8Array to Array...');
      const startTime = Date.now();
      const fileDataArray = Array.from(fileData);
      const conversionTime = Date.now() - startTime;
      console.log(`Array conversion complete, length: ${fileDataArray.length}, took: ${conversionTime}ms`);

      console.log('Emitting uploadMedia event...');
      socket.emit('uploadMedia', {
        fileData: fileDataArray,
        fileName: selectedFile.name,
        fileType: selectedFile.type,
        fileSize: selectedFile.size,
        groupId: group_id,
        token: token
      });
      console.log('uploadMedia event emitted successfully');

    } catch (error) {
      console.error('Error uploading file:', error);
      alert('Upload failed: ' + error.message);
      setIsUploading(false);
      setUploadProgress(0);
      setSelectedFile(null);
      setFilePreview(null);
    }
  };

  // Function to remove selected file
  const removeSelectedFile = () => {
    setSelectedFile(null);
    setFilePreview(null);
    setUploadProgress(0);
  };

  // Function to close upload modal and reset state
  const closeUploadModal = () => {
    if (!isUploading) {
      setShowUploadModal(false);
      setSelectedFile(null);
      setFilePreview(null);
      setUploadProgress(0);
      setDragActive(false);
    }
  };

  // Function to handle emoji selection
  const handleEmojiSelect = (emoji) => {
    const currentText = messageText;
    const cursorPosition = messageInputRef.current?.selectionStart || currentText.length;

    // Insert emoji at cursor position
    const newText = currentText.slice(0, cursorPosition) + emoji.native + currentText.slice(cursorPosition);
    setMessageText(newText);

    // Close emoji picker
    setShowEmojiPicker(false);

    // Focus back to input and set cursor position after emoji
    setTimeout(() => {
      if (messageInputRef.current) {
        messageInputRef.current.focus();
        const newCursorPosition = cursorPosition + emoji.native.length;
        messageInputRef.current.setSelectionRange(newCursorPosition, newCursorPosition);
      }
    }, 100);
  };

  // Function to toggle emoji picker
  const toggleEmojiPicker = () => {
    setShowEmojiPicker(!showEmojiPicker);
  };

  // Close emoji picker when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (emojiPickerRef.current && !emojiPickerRef.current.contains(event.target)) {
        setShowEmojiPicker(false);
      }
    };

    if (showEmojiPicker) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => {
        document.removeEventListener('mousedown', handleClickOutside);
      };
    }
  }, [showEmojiPicker]);

  // Function to edit message
  const editMessage = () => {
    if (!socket || !connected || !editingMessage || !messageText.trim()) return;

    // Prevent editing messages with media
    if (editingMessage.media_url) {
      console.log('Cannot edit messages with media attachments');
      return;
    }

    const newMessageText = messageText.trim();

    // Optimistically update the message
    setChatMessages(prev => prev.map(msg =>
      msg.id === editingMessage.id
        ? { ...msg, message: newMessageText }
        : msg
    ));

    // Send edit message event
    socket.emit('editMessage', {
      messageId: editingMessage.id,
      newMessage: newMessageText,
      token: token
    });

    // Clear editing state
    setEditingMessage(null);
    setMessageText('');
  };

  const dislikeMessage = (messageId) => {
    if (!socket || !connected) return;
    
    // Optimistically update UI
    setChatMessages(prev => prev.map(msg => {
      if (msg.id === messageId) {
        const currentDislikes = Array.isArray(msg.dislikes) ? msg.dislikes : [];
        const isDisliked = currentDislikes.includes(currentUserId);
        
        return {
          ...msg,
          dislikes: isDisliked 
            ? currentDislikes.filter(id => id !== currentUserId)
            : [...currentDislikes, currentUserId],
          // Remove from likes if exists
          likes: Array.isArray(msg.likes) 
            ? msg.likes.filter(id => id !== currentUserId)
            : []
        };
      }
      return msg;
    }));

    // Send to server
    socket.emit('dislikeMessage', {
      messageId: messageId,
      token: token
    });
  };

  // Remove hardcoded messages - we'll use chatMessages from server
  const messages = chatMessages || [];

  return (
    <div className="community">
      <div className="card">
        <div className="card-header">
          <div className="d-flex align-items-center justify-content-between">
          <div className="d-flex align-items-center">
            <div className="position-relative">
              <img
                src="https://picsum.photos/seed/1/40/40"
                className="rounded-circle"
                alt="Class Avatar"
              />
              <span
                  className={`position-absolute bottom-0 end-0 ${connected ? 'bg-success' : 'bg-danger'} rounded-circle p-1`}
                style={{ width: '10px', height: '10px', border: '2px solid white' }}
              ></span>
            </div>
            <div className="ms-3">
                <h6 className="mb-0">{group_name || 'Loading...'}</h6>
                <small className="text-muted">
                  {connected ? `Connected • ${pagination.totalMessages || 0} messages` : 'Connecting...'}
                </small>
              </div>
            </div>
      
          </div>
        </div>

        <div className="card-body p-0">
          <div 
            ref={chatContainerRef}
            className="chat-messages p-4 bg-light" 
            style={{ height: '500px', overflowY: 'auto' }}
          >
            {/* Load more button at the top */}
            {connected && pagination.currentPage < pagination.totalPages && (
              <div className="text-center mb-3">
                <button 
                  className="btn btn-sm btn-outline-primary"
                  onClick={() => loadMoreMessages(pagination.currentPage + 1)}
                >
                  <Icon icon="mdi:arrow-up" className="me-1" />
                  Load Previous Messages
                </button>
              </div>
            )}

            {/* Show loading state */}
            {!connected && (
              <div className="text-center py-5">
                <div className="spinner-border text-primary" role="status">
                  <span className="visually-hidden">Loading...</span>
                </div>
                <p className="mt-2 text-muted">Connecting to chat...</p>
              </div>
            )}

            {/* Show messages */}
            {connected && messages.length === 0 && (
              <div className="text-center py-5">
                <p className="text-muted">No messages yet. Start the conversation!</p>
              </div>
            )}

            {connected && messages.map((message) => {
              // Check if this message is from current user
              const isMine = message.user_id === currentUserId;
              console.log(`Message ${message.id} - user_id: ${message.user_id}, currentUserId: ${currentUserId}, isMine: ${isMine}`);
              
              // Handle timestamp conversion more reliably
              let messageDate;
              try {
                // Try to parse the timestamp - handle both ISO strings and Date objects
                if (typeof message.createdAt === 'string') {
                  messageDate = moment.utc(message.createdAt).local().toDate();
                } else if (message.createdAt instanceof Date) {
                  messageDate = message.createdAt;
                } else {
                  // Fallback to current time if timestamp is invalid
                  messageDate = new Date();
                }
              } catch (error) {
                console.error('Error parsing message timestamp:', error);
                messageDate = new Date();
              }

              const timeAgo = getTimeAgo(messageDate);
              
              // Parse likes and dislikes arrays
              let likesArray = [];
              let dislikesArray = [];
              try {
                if (message.likes) {
                  likesArray = typeof message.likes === 'string' ? JSON.parse(message.likes) : message.likes;
                }
                if (message.dislikes) {
                  dislikesArray = typeof message.dislikes === 'string' ? JSON.parse(message.dislikes) : message.dislikes;
                }
              } catch (e) {
                console.error('Error parsing likes/dislikes:', e);
              }
              

              
              // Find the message being replied to
              const repliedMessage = message.reply_to_message_id 
                ? messages.find(m => m.id === message.reply_to_message_id)
                : null;
              
              return (
                <div
                  key={message.id}
                  className={`chat-message mb-4 d-flex  ${isMine ? 'justify-content-end' : 'justify-content-start'}`}
                >
                  {!isMine && (
                    <img
                      src={message.profile_pic_url || `https://picsum.photos/seed/${message.user_id}/40/40`}
                      alt={message.name}
                      style={{
                        width: '40px',
                        height: '40px',
                        borderRadius: '50%',
                        objectFit: 'cover',
                        flexShrink: 0
                      }}
                      className="me-3"
                    />
                  )}
                  <div
                    className="p-3 rounded position-relative "
                    style={{
                      backgroundColor: isMine
                        ? 'var(--bs-primary-bg-subtle)'
                        : 'var(--bs-secondary-bg-subtle)',
                      maxWidth: '70%',
                      boxShadow: '0 2px 4px rgba(0,0,0,0.05)'
                    }}
                  >
                    <div className="d-flex flex-column flex-md-row align-items-start align-items-md-center mb-1">
                      <span className="fw-bold me-2">{message.name}</span>
                      {message.role && (
                        <span className="badge bg-info text-dark me-2">{message.role}</span>
                      )}
                      <small className="text-muted">{timeAgo}</small>
                    </div> 
                    
                    {/* Show replied message if exists */}
                    {repliedMessage && (
                      <div 
                        className="replied-message mb-2 p-2 rounded"
                        style={{
                          backgroundColor: 'rgba(0, 0, 0, 0.05)',
                          borderLeft: '3px solid #6c757d',
                          fontSize: '12px'
                        }}
                      >
                        <div className="d-flex align-items-center mb-1">
                          <Icon icon="mdi:reply" width="14" height="14" className="me-1 text-muted" />
                          <span className="fw-semibold text-muted">{repliedMessage.name}</span>
                        </div>
                        <div className="text-truncate text-muted" style={{ maxWidth: '100%' }}>
                          {repliedMessage.message}
                        </div>
                      </div>
                    )}
                    
                    {/* Only show message text if there's no media */}
                    {!message.media_url && (
                      <div className="text-break">{message.message}</div>
                    )}
                    
                    {/* Show media if exists */}
                    {message.media_url && (
                      <div className="mt-2">
                        {message.message_type === 'image' && (
                          <img 
                            src={message.media_url} 
                            alt="Shared image" 
                            className="img-fluid rounded"
                            style={{ maxHeight: '200px' }}
                          />
                        )}
                        {message.message_type === 'video' && (
                          <video 
                            src={message.media_url} 
                            controls 
                            className="img-fluid rounded"
                            style={{ maxHeight: '200px' }}
                          />
                        )}
                        {message.message_type === 'audio' && (
                          <audio 
                            src={message.media_url} 
                            controls 
                            className="w-100"
                            style={{ maxWidth: '300px' }}
                          />
                        )}
                        {message.message_type === 'document' && (
                          <a href={message.media_url} target="_blank" rel="noopener noreferrer" className="btn btn-sm btn-primary">
                            <Icon icon="mdi:file-document" className="me-1" />
                            View Document
                          </a>
                        )}
                      </div>
                    )}

                    
                    {/* Action buttons at bottom */}
                    <div className="message-actions">
                      <button
                        className="action-button reply"
                        onClick={() => {
                          setReplyingTo(message);
                          setTimeout(() => {
                            if (messageInputRef.current) {
                              messageInputRef.current.focus();
                            }
                          }, 100);
                        }}
                        disabled={!connected}
                      >
                        <Icon icon="mdi:reply" width="18" height="18" />
                      </button>
                      
                      <button 
                        className={`action-button ${message.likes?.includes(currentUserId) ? 'liked' : ''}`}
                        onClick={() => likeMessage(message.id)}
                        disabled={!connected}
                      >
                        <Icon icon="mdi:thumb-up" width="18" height="18" />
                        <span className="count">{Array.isArray(message.likes) ? message.likes.length : 0}</span>
                      </button>
                      
                      {/* <button 
                        className={`action-button ${message.dislikes?.includes(currentUserId) ? 'disliked' : ''}`}
                        onClick={() => dislikeMessage(message.id)}
                        disabled={!connected}
                      >
                        <Icon icon="mdi:thumb-down" width="18" height="18" />
                        <span className="count">{Array.isArray(message.dislikes) ? message.dislikes.length : 0}</span>
                      </button> */}

                      {message.user_id === currentUserId && (
                        <>
                          {/* Only show edit button if there's no media */}
                          {!message.media_url && (
                            <button
                              className="action-button edit"
                              onClick={() => {
                                setEditingMessage(message);
                                setMessageText(message.message);
                                document.querySelector('.message-input textarea')?.focus();
                              }}
                              disabled={!connected}
                            >
                              <Icon icon="mdi:pencil" width="18" height="18" />
                            </button>
                          )}
                          <button
                            className="action-button delete"
                            onClick={() => deleteMessage(message.id)}
                            disabled={!connected}
                          >
                            <Icon icon="mdi:delete" width="18" height="18" />
                          </button>
                        </>
                      )}
                    </div>
                  </div>
                  {isMine && (
                    <img
                      src={message.profile_pic_url || `https://picsum.photos/seed/${message.user_id}/40/40`}
                      alt={message.name}
                      style={{
                        width: '40px',
                        height: '40px',
                        borderRadius: '50%',
                        objectFit: 'cover',
                        flexShrink: 0
                      }}
                      className="ms-3 profile-chat"
                    />
                  )}
                </div>
              );
            })}
            
            {/* Invisible element to scroll to */}
            <div ref={messagesEndRef} />
          </div>

          {/* Input Field */}
          <div className="chat-input border-top p-3 bg-white">
            {/* Edit indicator */}
            {editingMessage && (
              <div className="edit-indicator bg-light p-2 mb-2 rounded d-flex justify-content-between align-items-center">
                <div className="d-flex align-items-center">
                  <Icon icon="mdi:pencil" width="20" height="20" className="me-2 text-warning" />
                  <div>
                    <small className="text-muted">Editing message</small>
                    <div className="text-truncate text-muted" style={{ maxWidth: '300px', fontSize: '12px' }}>
                      {editingMessage.message}
                    </div>
                  </div>
                </div>
                <button
                  onClick={() => {
                    setEditingMessage(null);
                    setMessageText('');
                  }}
                  title="Cancel editing"
                >
                  <Icon icon="mdi:close" width="20" height="20" />
                </button>
              </div>
            )}

            {/* Reply indicator */}
            {replyingTo && (
              <div className="reply-indicator bg-light p-2 mb-2 rounded d-flex justify-content-between align-items-center">
                <div className="d-flex align-items-center">
                  <Icon icon="mdi:reply" width="20" height="20" className="me-2 text-primary" />
                  <div>
                    <small className="text-muted">Replying to</small>
                    <div className="fw-bold text-truncate" style={{ maxWidth: '300px' }}>
                      {replyingTo.name}
                    </div>
                    <div className="text-truncate text-muted" style={{ maxWidth: '300px', fontSize: '12px' }}>
                      {replyingTo.message}
                    </div>
                  </div>
                </div>
                <button
                  className="btn btn-sm btn-link text-danger p-0 border border-danger rounded-1"
                  style={{width : 'auto'}}
                  onClick={() => setReplyingTo(null)}
                  title="Cancel reply"
                >
                  <Icon icon="mdi:close" width="20" height="20" />
                </button>
              </div>
            )}
            
            <div className="d-flex align-items-center gap-2">
              {/* Message Input */}
              <div className="flex-grow-1 position-relative">
                <input
                  ref={messageInputRef}
                  type="text"
                  className="form-control"
                  placeholder="Type your message here..."
                  style={{ height: '40px', paddingLeft: '45px', paddingRight: '45px' }}
                  disabled={!connected}
                  value={messageText}
                  onChange={(e) => setMessageText(e.target.value)}
                  onKeyDown={handleKeyPress}
                />
                {/* Upload Button */}
                <button
                  className="btn btn-link position-absolute start-0 top-50 translate-middle-y p-0 d-flex align-items-center justify-content-center"
                  style={{
                    width: '35px',
                    height: '35px',
                    left: '8px',
                    zIndex: 10,
                    color: '#6c757d'
                  }}
                  onClick={() => setShowUploadModal(true)}
                  disabled={!connected}
                  type="button"
                >
                  <Icon icon="mdi:plus" width="20" height="20" />
                </button>

                {/* Emoji Picker Button
                <button
                  className="btn btn-link position-absolute end-0 top-50 translate-middle-y p-0 d-flex align-items-center justify-content-center"
                  style={{
                    width: '35px',
                    height: '35px',
                    right: '8px',
                    zIndex: 10,
                    color: showEmojiPicker ? '#0d6efd' : '#6c757d'
                  }}
                  onClick={toggleEmojiPicker}
                  disabled={!connected}
                  type="button"
                >
                  <Icon icon="mdi:emoticon-happy-outline" width="20" height="20" />
                </button> */}

                {/* Emoji Picker Dropdown */}
                {showEmojiPicker && (
                  <div
                    ref={emojiPickerRef}
                    className="position-absolute"
                    style={{
                      bottom: '50px',
                      right: '0',
                      zIndex: 1050,
                      boxShadow: '0 4px 20px rgba(0,0,0,0.15)',
                      borderRadius: '12px',
                      overflow: 'hidden'
                    }}
                  >
                    <Picker
                      data={data}
                      onEmojiSelect={handleEmojiSelect}
                      theme="light"
                      previewPosition="none"
                      skinTonePosition="none"
                      maxFrequentRows={2}
                      perLine={8}
                      set="native"
                    />
                  </div>
                )}
              </div>

              {/* Send Button */}
              <div className="send-button-container">
                <button
                  className="action-button"
                  disabled={!connected || isSending || !messageText.trim()}
                  onClick={sendMessage}
                >
                  {isSending ? (
                    <div className="spinner-border text-primary" role="status" style={{ width: '20px', height: '20px' }}>
                      <span className="visually-hidden">Sending...</span>
                    </div>
                  ) : (
                    <Icon icon="mdi:send-circle" width="40" height="40" />
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

             {/* Upload Modal */}
       {showUploadModal && (
         <div className="modal show d-block" style={{ backgroundColor: 'rgba(0,0,0,0.5)' }}>
           <div className="modal-dialog modal-dialog-centered">
             <div className="modal-content border-0 shadow">
               <div className="modal-header border-bottom-0 pb-0">
                 <h5 className="modal-title fw-semibold">Upload Media</h5>
                 <button
                   type="button"
                   className="btn-close"
                   onClick={closeUploadModal}
                   disabled={isUploading}
                 ></button>
               </div>
               <div className="modal-body pt-2">
                 {/* Drag and Drop Area */}
                 <div
                   className={`upload-area border border-2 ${
                     dragActive ? 'border-primary bg-primary-subtle' : 'border-dashed border-secondary-subtle'
                   }`}
                   onDragEnter={(e) => {
                     e.preventDefault();
                     e.stopPropagation();
                     setDragActive(true);
                   }}
                   onDragLeave={(e) => {
                     e.preventDefault();
                     e.stopPropagation();
                     setDragActive(false);
                   }}
                   onDragOver={(e) => {
                     e.preventDefault();
                     e.stopPropagation();
                     setDragActive(true);
                   }}
                   onDrop={(e) => {
                     e.preventDefault();
                     e.stopPropagation();
                     setDragActive(false);
                     if (e.dataTransfer.files && e.dataTransfer.files[0]) {
                       handleFileSelection(e.dataTransfer.files[0]);
                     }
                   }}
                   onClick={() => {
                     if (!selectedFile) {
                       const input = document.createElement('input');
                       input.type = 'file';
                       input.accept = '*/*';
                       input.onchange = (e) => {
                         if (e.target.files[0]) {
                           handleFileSelection(e.target.files[0]);
                         }
                       };
                       input.click();
                     }
                   }}
                   style={{
                     minHeight: '180px',
                     borderRadius: '8px',
                     display: 'flex',
                     flexDirection: 'column',
                     justifyContent: 'center',
                     alignItems: 'center',
                     cursor: 'pointer',
                     margin: '0 1rem 1.5rem',
                     transition: 'all 0.2s ease'
                   }}
                 >
                   {!selectedFile ? (
                     <>
                       <div className="upload-icon-wrapper mb-3" style={{
                         width: '64px',
                         height: '64px',
                         borderRadius: '50%',
                         backgroundColor: '#f8f9fa',
                         display: 'flex',
                         alignItems: 'center',
                         justifyContent: 'center'
                       }}>
                         <Icon icon="mdi:cloud-upload" width="32" height="32" className="text-primary" />
                       </div>
                       <p className="mb-1 fw-medium">Drag and drop files here or click to browse</p>
                       <small className="text-muted">Max file size: 20MB</small>
                     </>
                   ) : (
                     <div className="w-100 text-center py-3">
                       {/* File Preview */}
                       <div className="mb-4">
                         {filePreview && selectedFile.type.startsWith('image/') && (
                           <div className="d-inline-block position-relative">
                             <img
                               src={filePreview}
                               alt="Preview"
                               style={{
                                 maxWidth: '220px',
                                 maxHeight: '160px',
                                 objectFit: 'cover',
                                 borderRadius: '12px',
                                 boxShadow: '0 4px 12px rgba(0,0,0,0.1)'
                               }}
                             />
                           </div>
                         )}
                         {filePreview && selectedFile.type.startsWith('video/') && (
                           <div className="d-inline-block position-relative">
                             <video
                               src={filePreview}
                               style={{
                                 maxWidth: '220px',
                                 maxHeight: '160px',
                                 borderRadius: '12px',
                                 boxShadow: '0 4px 12px rgba(0,0,0,0.1)'
                               }}
                               controls
                             />
                           </div>
                         )}
                         {!filePreview && (
                           <div
                             className="d-inline-flex align-items-center justify-content-center"
                             style={{
                               width: '120px',
                               height: '120px',
                               borderRadius: '12px',
                               backgroundColor: '#f8f9fa',
                               border: '2px dashed #dee2e6'
                             }}
                           >
                             <Icon
                               icon={
                                 selectedFile.type.startsWith('audio/') ? 'mdi:music' :
                                 selectedFile.type.includes('pdf') ? 'mdi:file-pdf-box' :
                                 'mdi:file-document'
                               }
                               width="48"
                               height="48"
                               className="text-primary"
                             />
                           </div>
                         )}
                       </div>

                       {/* File Info */}
                       <div className="mb-3 text-center">
                         <p
                           className="mb-1 fw-medium text-truncate mx-auto"
                           style={{
                             maxWidth: '280px',
                             fontSize: '14px',
                             lineHeight: '1.4'
                           }}
                           title={selectedFile.name}
                         >
                           {selectedFile.name}
                         </p>
                         <small className="text-muted d-block">
                           {(selectedFile.size / (1024 * 1024)).toFixed(2)} MB
                         </small>
                       </div>

                       {/* Action Icons */}
                       <div className="d-flex gap-4 justify-content-center align-items-center">
                         <button
                           className="action-icon-btn remove-btn"
                           onClick={(e) => {
                             e.stopPropagation();
                             removeSelectedFile();
                           }}
                           disabled={isUploading}
                           title="Remove file"
                           style={{
                             width: '48px',
                             height: '48px',
                             borderRadius: '50%',
                             display: 'flex',
                             alignItems: 'center',
                             justifyContent: 'center',
                             backgroundColor: '#fff',
                             border: '2px solid #dc3545',
                             color: '#dc3545',
                             transition: 'all 0.2s ease',
                             cursor: 'pointer'
                           }}
                           onMouseEnter={(e) => {
                             e.target.style.backgroundColor = '#dc3545';
                             e.target.style.color = '#fff';
                           }}
                           onMouseLeave={(e) => {
                             e.target.style.backgroundColor = '#fff';
                             e.target.style.color = '#dc3545';
                           }}
                         >
                           <Icon icon="mdi:delete" width="22" height="22" />
                         </button>
                         <button
                           className="action-icon-btn upload-btn"
                           onClick={(e) => {
                             e.stopPropagation();
                             handleFileUpload();
                           }}
                           disabled={isUploading}
                           title="Upload file"
                           style={{
                             width: '48px',
                             height: '48px',
                             borderRadius: '50%',
                             display: 'flex',
                             alignItems: 'center',
                             justifyContent: 'center',
                             backgroundColor: '#0d6efd',
                             border: '2px solid #0d6efd',
                             color: '#fff',
                             transition: 'all 0.2s ease',
                             cursor: 'pointer',
                             boxShadow: '0 2px 8px rgba(13, 110, 253, 0.3)'
                           }}
                           onMouseEnter={(e) => {
                             e.target.style.backgroundColor = '#0b5ed7';
                             e.target.style.borderColor = '#0b5ed7';
                             e.target.style.transform = 'scale(1.05)';
                           }}
                           onMouseLeave={(e) => {
                             e.target.style.backgroundColor = '#0d6efd';
                             e.target.style.borderColor = '#0d6efd';
                             e.target.style.transform = 'scale(1)';
                           }}
                         >
                           <Icon icon="mdi:upload" width="22" height="22" />
                         </button>
                       </div>
                     </div>
                   )}

                   {/* Upload Progress */}
                   {isUploading && (
                     <div className="mt-3 w-75">
                       <div className="progress" style={{ height: '6px' }}>
                         <div
                           className="progress-bar progress-bar-striped progress-bar-animated"
                           role="progressbar"
                           style={{ width: `${uploadProgress}%` }}
                         />
                       </div>
                       <small className="text-muted d-block text-center mt-2">
                         Uploading... {Math.round(uploadProgress)}%
                       </small>
                     </div>
                   )}
                 </div>

                 {/* File Type Buttons */}
                 <div className="d-flex justify-content-center gap-4 px-4">
                   <button
                     className="upload-type-btn"
                     onClick={() => {
                       const input = document.createElement('input');
                       input.type = 'file';
                       input.accept = 'image/*';
                       input.onchange = (e) => {
                         if (e.target.files[0]) handleFileSelection(e.target.files[0]);
                       };
                       input.click();
                     }}
                     disabled={isUploading || selectedFile}
                   >
                     <div className="icon-wrapper">
                       <Icon icon="mdi:image" width="24" height="24" />
                     </div>
                     <span>Images</span>
                   </button>

                   <button
                     className="upload-type-btn"
                     onClick={() => {
                       const input = document.createElement('input');
                       input.type = 'file';
                       input.accept = 'video/*';
                       input.onchange = (e) => {
                         if (e.target.files[0]) handleFileSelection(e.target.files[0]);
                       };
                       input.click();
                     }}
                     disabled={isUploading || selectedFile}
                   >
                     <div className="icon-wrapper">
                       <Icon icon="mdi:video" width="24" height="24" />
                     </div>
                     <span>Videos</span>
                   </button>

                   <button
                     className={`upload-type-btn ${!selectedFile ? 'active' : ''}`}
                     onClick={() => {
                       const input = document.createElement('input');
                       input.type = 'file';
                       input.accept = 'audio/*';
                       input.onchange = (e) => {
                         if (e.target.files[0]) handleFileSelection(e.target.files[0]);
                       };
                       input.click();
                     }}
                     disabled={isUploading || selectedFile}
                   >
                     <div className="icon-wrapper">
                       <Icon icon="mdi:microphone" width="24" height="24" />
                     </div>
                     <span>Audio</span>
                   </button>

                   <button
                     className="upload-type-btn"
                     onClick={() => {
                       const input = document.createElement('input');
                       input.type = 'file';
                       input.accept = '.pdf,.doc,.docx,.txt,.xls,.xlsx,.ppt,.pptx';
                       input.onchange = (e) => {
                         if (e.target.files[0]) handleFileSelection(e.target.files[0]);
                       };
                       input.click();
                     }}
                     disabled={isUploading || selectedFile}
                   >
                     <div className="icon-wrapper">
                       <Icon icon="mdi:file-document" width="24" height="24" />
                     </div>
                     <span>Documents</span>
                   </button>
                 </div>
               </div>
             </div>
           </div>
         </div>
       )}
    </div>
  );
}

// Helper function to format time ago
function getTimeAgo(date) {
  const now = new Date();
  const messageDate = new Date(date);
  const seconds = Math.floor((now - messageDate) / 1000);

  // Handle negative values (future dates) - show "just now"
  if (seconds < 0) return "just now";

  let interval = seconds / 31536000;
  if (interval > 1) return Math.floor(interval) + " years ago";

  interval = seconds / 2592000;
  if (interval > 1) return Math.floor(interval) + " months ago";

  interval = seconds / 86400;
  if (interval > 1) return Math.floor(interval) + " days ago";

  interval = seconds / 3600;
  if (interval > 1) return Math.floor(interval) + " hours ago";

  interval = seconds / 60;
  if (interval > 1) return Math.floor(interval) + " minutes ago";

  // Show "just now" for very recent messages (less than 10 seconds)
  if (seconds < 10) return "just now";

  return Math.floor(seconds) + " seconds ago";
}

export default Cummunity;