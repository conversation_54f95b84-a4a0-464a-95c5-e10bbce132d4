const { mysqlServerConnection } = require('../db');

async function createMainDatabase() {
    const dbName = process.env.MAIN_DB;
    try {
        const connection = await mysqlServerConnection.getConnection();
        await connection.query(`CREATE DATABASE IF NOT EXISTS ${dbName}`);
        console.log(`Database ${dbName} created successfully.`);

        // Create subscriptions table - stores subscription plans (no dependencies)
        await connection.query(`
            CREATE TABLE IF NOT EXISTS ${dbName}.subscriptions (
                id int NOT NULL AUTO_INCREMENT PRIMARY KEY,
                plan_name varchar(255) DEFAULT 'FREE',
                validity varchar(10) DEFAULT '30',
                created_at timestamp NULL DEFAULT CURRENT_TIMESTAMP,
                updated_at timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                max_users int DEFAULT '50'
            );
        `);

        // Create users table - stores user account information (no dependencies)
        await connection.query(`
            CREATE TABLE IF NOT EXISTS ${dbName}.users (
                id INT AUTO_INCREMENT PRIMARY KEY,
                profile_pic_url VARCHAR(255),
                benner_img_url VARCHAR(255),
                name VARCHAR(30),
                username VARCHAR(25) NOT NULL UNIQUE,
                password VARCHAR(255),
                email VARCHAR(25),
                mobile VARCHAR(25),
                country VARCHAR(50),
                state VARCHAR(255),
                zipcode VARCHAR(10),
                address VARCHAR(255),
                createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                is_verified TINYINT(1) DEFAULT 0
            );
        `);

        // Create organization table - stores organization information (depends on subscriptions)
        await connection.query(`
            CREATE TABLE IF NOT EXISTS ${dbName}.organization (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(50) NULL,
                db_name VARCHAR(255) NOT NULL UNIQUE,
                country VARCHAR(50) NULL,
                state VARCHAR(255) NULL,
                zipcode VARCHAR(10) NULL,
                office_address VARCHAR(255) NULL,
                org_desc TEXT NULL,
                authorized_person VARCHAR(255) NOT NULL,
                authorized_person_identity VARCHAR(255) NOT NULL,
                authorized_person_phone VARCHAR(25) NOT NULL,
                authorized_person_email VARCHAR(225) NOT NULL UNIQUE,
                auth_sub_domain VARCHAR(255) NOT NULL UNIQUE,
                org_logo_url VARCHAR(255) NULL,
                org_favicon_url VARCHAR(255) NULL,
                require_doc_url VARCHAR(255) NOT NULL,
                createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                subscription_plan INT NULL,
                is_verified BOOLEAN DEFAULT FALSE,
                is_deleted BOOLEAN DEFAULT FALSE,
                FOREIGN KEY (subscription_plan) REFERENCES ${dbName}.subscriptions(id) ON DELETE SET NULL
            );
        `);

        // Create roles table - defines user roles (no dependencies)
        await connection.query(`
                            CREATE TABLE IF NOT EXISTS ${dbName}.roles (
                            id INT AUTO_INCREMENT PRIMARY KEY,
                            name VARCHAR(255) NOT NULL UNIQUE,
                            description VARCHAR(255) NULL,
                            createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                            );
                        `);

        // Create permission_levels table - defines permission hierarchy levels (no dependencies)
        await connection.query(`
            CREATE TABLE IF NOT EXISTS ${dbName}.permission_levels (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(255) NOT NULL UNIQUE,
                description VARCHAR(255) NULL,
                level_value INT NOT NULL,
                            createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                            );
                        `);

        // Create permissions table - defines system permissions (depends on permission_levels)
        await connection.query(`
                            CREATE TABLE IF NOT EXISTS ${dbName}.permissions (
                                id INT AUTO_INCREMENT PRIMARY KEY,
                                name VARCHAR(255) NOT NULL UNIQUE,
                                description VARCHAR(255) NULL,
                                category VARCHAR(255) NULL,
                                level_id INT NOT NULL,
                                createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                                updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                FOREIGN KEY (level_id) REFERENCES ${dbName}.permission_levels(id) ON DELETE RESTRICT
                            );
                        `);

        // Create permission_actions table - defines available permission actions (no dependencies)
        await connection.query(`
            CREATE TABLE IF NOT EXISTS ${dbName}.permission_actions (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(50) NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                is_deleted TINYINT(1) DEFAULT 0
            );
        `);

        // Inserting all the actions
        await connection.query(`
            INSERT INTO ${dbName}.permission_actions (id, name, created_at, updated_at, is_deleted)
            VALUES 
            (1, 'view', '2025-07-11 07:08:35', '2025-07-11 07:08:35', 0),
            (2, 'create', '2025-07-11 07:08:35', '2025-07-11 07:08:35', 0),
            (3, 'edit', '2025-07-11 07:08:35', '2025-07-11 07:08:35', 0),
            (4, 'delete', '2025-07-11 07:08:35', '2025-07-11 07:08:35', 0),
            (5, 'status', '2025-07-11 07:08:35', '2025-07-11 07:08:35', 0),
            (6, 'send_approval', '2025-07-11 07:08:35', '2025-07-11 07:08:35', 0),
            (7, 'resend_approval', '2025-07-11 07:08:35', '2025-07-11 07:08:35', 0),
            (8, 'approve', '2025-07-11 07:08:35', '2025-07-11 11:44:53', 0),
            (9, 'reject', '2025-07-11 07:08:35', '2025-07-11 11:45:13', 0),
            (10, 'cancel_approval', '2025-07-11 07:08:35', '2025-07-11 07:08:35', 0),
            (11, 'manage', '2025-07-11 07:08:35', '2025-07-11 07:08:35', 0),
            (12, 'view_analytics', '2025-07-11 07:08:35', '2025-07-11 07:08:35', 0),
            (13, 'publish', '2025-07-11 07:08:35', '2025-07-11 07:08:35', 0),
            (14, 'unpublish', '2025-07-11 07:08:35', '2025-07-11 07:08:35', 0);
        `);
        

        // Create permission_modules table - defines permission modules (self-referencing)
        await connection.query(`
            CREATE TABLE IF NOT EXISTS ${dbName}.permission_modules (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(100) NOT NULL,
                slug VARCHAR(100) NOT NULL UNIQUE,
                parent_id INT DEFAULT NULL,
                \`order\` SMALLINT DEFAULT 0,
                is_active TINYINT(1) DEFAULT 1,
                icon VARCHAR(50) DEFAULT NULL,
                description TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (parent_id) REFERENCES ${dbName}.permission_modules(id) ON DELETE SET NULL
            );
        `);

        await connection.query(`
            INSERT INTO ${dbName}.permission_modules (
              id, name, slug, parent_id, \`order\`, is_active, icon, description, created_at, updated_at
            ) VALUES
            (1, 'Dashboard Management', 'dashboard_management', NULL, 1, 1, 'fas fa-tachometer-alt', 'Manage the main dashboard overview', '2025-07-11 10:17:33', '2025-07-12 19:44:39'),
            (2, 'Profile Management', 'profile_management', NULL, 2, 1, 'fas fa-user-circle', 'Manage user profile and settings', '2025-07-11 10:17:33', '2025-07-12 19:44:39'),
            (3, 'Course Management', 'course_management', NULL, 3, 1, 'fas fa-book', 'Manage courses and content', '2025-07-11 10:17:33', '2025-07-12 19:44:39'),
            (4, 'Classroom Management', 'classroom_management', NULL, 4, 1, 'fas fa-chalkboard-teacher', 'Manage classroom and related data', '2025-07-11 10:17:33', '2025-07-12 19:44:39'),
            (5, 'Course Approval Management', 'course_approval_workflow', NULL, 5, 1, 'fas fa-check-circle', 'Manage course approval workflow', '2025-07-11 10:17:33', '2025-07-12 19:44:39'),
            (6, 'Certificates Management', 'certificate_management', NULL, 6, 1, 'fas fa-certificate', 'Manage course certificates', '2025-07-11 10:17:33', '2025-07-12 19:44:39'),
            (7, 'Trainees Management', 'trainee_management', NULL, 7, 1, 'fas fa-user-graduate', 'Manage enrolled trainees', '2025-07-11 10:17:33', '2025-07-12 19:44:39'),
            (8, 'Roles and access Management', 'roles_and_access_management', NULL, 8, 1, 'fas fa-key', 'Manage roles and permission mapping', '2025-07-11 10:17:33', '2025-07-12 19:44:39'),
            (9, 'Question Bank Management', 'question_bank_management', NULL, 9, 1, 'fas fa-database', 'Manage centralized question bank', '2025-07-11 10:17:33', '2025-07-12 19:44:39'),
            (10, 'Announcement Management', 'announcement_management', NULL, 10, 1, 'fas fa-bullhorn', 'Send and manage announcements', '2025-07-11 10:17:33', '2025-07-12 19:44:39'),
            (11, 'Notifications Management', 'notification_management', NULL, 11, 1, 'fas fa-bell', 'View and manage system notifications', '2025-07-11 10:17:33', '2025-07-12 19:44:39'),
            (12, 'Settings Management', 'setting_management', NULL, 12, 1, 'fas fa-cogs', 'System or organization-wide settings', '2025-07-11 10:17:33', '2025-07-12 19:44:39'),
            (13, 'Course Module Management', 'course_module_management', 3, 13, 1, 'fas fa-cogs', 'Manage course modules', '2025-07-11 10:26:10', '2025-07-11 10:28:30'),
            (14, 'Course Module Content Management', 'course_module_content_management', 3, 14, 1, 'fas fa-cogs', 'Manage course module content', '2025-07-11 10:26:10', '2025-07-11 10:28:30'),
            (15, 'Classroom Assignment', 'classroom_assignment', 4, 1, 1, 'fas fa-list-alt', 'Manage classroom assignments', '2025-07-11 10:30:18', '2025-07-11 10:30:18'),
            (16, 'Classroom Assessment', 'classroom_assessment', 4, 2, 1, 'fas fa-clipboard-check', 'Manage classroom assessments', '2025-07-11 10:30:18', '2025-07-11 10:30:18'),
            (17, 'Classroom Resources', 'classroom_resources', 4, 3, 1, 'fas fa-book', 'Manage classroom resources', '2025-07-11 10:30:18', '2025-07-11 10:30:18'),
            (18, 'Classroom Live Classes', 'classroom_liveclasses', 4, 4, 1, 'fas fa-video', 'Manage live classes for classroom', '2025-07-11 10:30:18', '2025-07-11 10:30:18'),
            (19, 'Classroom Community', 'classroom_community', 4, 5, 1, 'fas fa-users', 'Manage classroom community and interaction', '2025-07-11 10:30:18', '2025-07-11 10:30:18'),
            (20, 'Classroom Trainees', 'classroom_trainees', 4, 6, 1, 'fas fa-user-graduate', 'Manage classroom trainees', '2025-07-11 10:30:18', '2025-07-11 10:30:18'),
            (21, 'Classroom Assignment Resource', 'classroom_assignment_resource', 15, 1, 1, 'fas fa-book', 'Manage resources for classroom assignments', '2025-07-11 11:02:34', '2025-07-11 11:02:34'),
            (22, 'Classroom Assignment Response', 'classroom_assignment_response', 15, 2, 1, 'fas fa-reply', 'Manage responses for classroom assignments', '2025-07-11 11:02:34', '2025-07-11 11:02:34'),
            (23, 'Classroom Assessment Question', 'classroom_assessment_question', 16, 1, 1, 'fas fa-book', 'Manage questions for classroom assessments', '2025-07-11 11:02:34', '2025-07-11 11:02:34'),
            (24, 'Classroom Assessment Result', 'classroom_assessment_result', 16, 2, 1, 'fas fa-check', 'View results for classroom assessments', '2025-07-11 11:02:34', '2025-07-11 11:02:34'),
            (25, 'Roles and Access User', 'roles_and_access_user', 8, 1, 1, 'fas fa-users', 'Manage users within roles and access', '2025-07-11 11:11:06', '2025-07-11 11:11:06'),
            (26, 'Roles and Access Permissions', 'roles_and_access_permissions', 8, 2, 1, 'fas fa-key', 'Manage permissions within roles and access', '2025-07-11 11:11:06', '2025-07-11 11:11:06'),
            (27, 'Settings About Organization', 'settings_about_organization', 12, 1, 1, 'fas fa-info-circle', 'View and manage organization information', '2025-07-11 11:11:06', '2025-07-11 21:34:58'),
            (28, 'Settings FAQ', 'settings_faq', 12, 2, 1, 'fas fa-question-circle', 'View and manage FAQ settings', '2025-07-11 11:11:06', '2025-07-11 21:34:58'),
            (29, 'Settings Query', 'settings_query', 12, 3, 1, 'fas fa-comments', 'Manage queries related to settings', '2025-07-11 11:11:06', '2025-07-11 21:34:58'),
            (30, 'Settings Payments', 'settings_payments', 12, 4, 1, 'fas fa-credit-card', 'Manage payment settings', '2025-07-11 11:11:06', '2025-07-11 21:34:58');
          `);
          

        // Create permission_modules_and_actions_mapping table - maps modules to actions (depends on permission_modules and permission_actions)
        await connection.query(`
                            CREATE TABLE IF NOT EXISTS ${dbName}.permission_modules_and_actions_mapping (
                                id INT AUTO_INCREMENT PRIMARY KEY,
                                name VARCHAR(100) NOT NULL,
                                slug VARCHAR(100) NOT NULL,
                                module_id INT NOT NULL,
                                action_id INT NOT NULL,
                                description TEXT,
                                is_action TINYINT(1) DEFAULT 1,
                                is_delete TINYINT(1) DEFAULT 0,
                                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                FOREIGN KEY (module_id) REFERENCES ${dbName}.permission_modules(id) ON DELETE CASCADE,
                                FOREIGN KEY (action_id) REFERENCES ${dbName}.permission_actions(id) ON DELETE CASCADE
                            );
                        `);

        // Create role_permissions table - maps roles to permissions (depends on roles and permissions)
        await connection.query(`
            CREATE TABLE IF NOT EXISTS ${dbName}.role_permissions (
                role_id INT NOT NULL,
                permission_id INT NOT NULL,
                PRIMARY KEY (role_id, permission_id),
                FOREIGN KEY (role_id) REFERENCES ${dbName}.roles(id) ON DELETE CASCADE,
                FOREIGN KEY (permission_id) REFERENCES ${dbName}.permissions(id) ON DELETE CASCADE,
                createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                            );
                        `);

        // Create user_roles table - maps users to their roles (depends on users and roles)
        await connection.query(`
            CREATE TABLE IF NOT EXISTS ${dbName}.user_roles (
                user_id INT NOT NULL,
                role_id INT NOT NULL,
                PRIMARY KEY (user_id, role_id),
                FOREIGN KEY (user_id) REFERENCES ${dbName}.users(id) ON DELETE CASCADE,
                FOREIGN KEY (role_id) REFERENCES ${dbName}.roles(id) ON DELETE CASCADE,
                                createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                                updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                            );
                        `);

        // Create user_logs table - tracks user activity logs (depends on users)
        await connection.query(`
            CREATE TABLE IF NOT EXISTS ${dbName}.user_logs (
                                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                log_name VARCHAR(225) NOT NULL,
                log_description TEXT NULL,
                file_url VARCHAR(225) NULL,
                createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES ${dbName}.users(id) ON DELETE CASCADE
                            );
                        `);

        // Create organization_requests table - stores organization registration requests (depends on organization)
        await connection.query(`
                            CREATE TABLE IF NOT EXISTS ${dbName}.organization_requests (
                                id INT AUTO_INCREMENT PRIMARY KEY,
                                org_id INT NOT NULL,
                                organization_name VARCHAR(255) NOT NULL,
                                contact_person VARCHAR(255),
                                contact_email VARCHAR(255),
                                contact_phone VARCHAR(20),
                                request_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                                request_status ENUM('Pending', 'Approved', 'Rejected') DEFAULT 'Pending',
                                request_type VARCHAR(100),
                                request_description TEXT,
                                approved_by INT,
                                approved_date TIMESTAMP,
                                remarks TEXT,
                                createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                                updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                FOREIGN KEY (org_id) REFERENCES ${dbName}.organization(id) ON DELETE CASCADE
                                );
                            `);

        // Create token_blacklist table - stores invalidated JWT tokens (no dependencies)
                            await connection.query(`
                                CREATE TABLE IF NOT EXISTS ${dbName}.token_blacklist (
                                    id INT AUTO_INCREMENT PRIMARY KEY,
                                    token VARCHAR(255) NOT NULL,
                                    expires_at TIMESTAMP NOT NULL,
                                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                                );
                            `);
                            
        // Create notifications table - stores user notifications (depends on users)
        await connection.query(`
                                CREATE TABLE IF NOT EXISTS ${dbName}.notifications (
                                id INT AUTO_INCREMENT PRIMARY KEY,
                                user_id INT NOT NULL,
                                FOREIGN KEY (user_id) REFERENCES ${dbName}.users(id) ON DELETE CASCADE,
                                send_from VARCHAR(100) NULL,
                                title VARCHAR(100) NULL,
                                body VARCHAR(255) NULL,
                                logo_image VARCHAR(255) NULL,
                                is_read BOOLEAN DEFAULT FALSE,
                                is_deleted BOOLEAN DEFAULT FALSE,
                                createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                                updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            );
        `);

        // Insert default permission modules and actions mapping data
        await connection.query(`
            INSERT IGNORE INTO ${dbName}.permission_modules_and_actions_mapping 
            (id, name, slug, module_id, action_id, description, is_action, is_delete, created_at, updated_at) VALUES
            (1, 'Show in Sidebar', 'dashboard_management_view', 1, 1, 'Permission to view the dashboard management in sidebar', 1, 0, '2025-07-11 11:38:26', '2025-07-18 08:36:04'),
            (2, 'Dashboard Management Cards View', 'dashboard_management_cards_view', 1, 1, 'Permission to view the cards in dashboard management', 1, 0, '2025-07-11 11:38:26', '2025-07-11 11:38:26'),
            (3, 'Dashboard Management Graphs View', 'dashboard_management_graphs_view', 1, 1, 'Permission to view the graphs in dashboard management', 1, 0, '2025-07-11 11:38:26', '2025-07-11 11:38:26'),
            (4, 'Dashboard Management Latest Updates View', 'dashboard_management_view_latest_updates_view', 1, 1, 'Permission to view the latest updates in dashboard management', 1, 0, '2025-07-11 11:38:26', '2025-07-11 11:38:26'),
            (5, 'Show in Sidebar', 'profile_management_view', 2, 1, 'Permission to view the profile', 1, 0, '2025-07-11 11:38:26', '2025-07-18 08:36:04'),
            (6, 'Profile Management Edit', 'profile_management_edit', 2, 3, 'Permission to edit the user profile', 1, 0, '2025-07-11 11:38:26', '2025-07-11 11:38:26'),
            (7, 'Show in Sidebar', 'course_management_view', 3, 1, 'Permission to view course management in sidebar', 1, 0, '2025-07-11 11:40:17', '2025-07-18 08:36:04'),
            (8, 'Course Management Edit', 'course_management_edit', 3, 3, 'Permission to edit course management', 1, 0, '2025-07-11 11:40:17', '2025-07-11 11:40:17'),
            (9, 'Course Management Delete', 'course_management_delete', 3, 4, 'Permission to delete course management', 1, 0, '2025-07-11 11:40:17', '2025-07-11 11:40:17'),
            (10, 'Course Management Send Approval', 'course_management_send_approval', 3, 6, 'Permission to send approval for course management', 1, 0, '2025-07-11 11:40:17', '2025-07-11 11:40:17'),
            (11, 'Course Management Resend Approval', 'course_management_resend_approval', 3, 7, 'Permission to resend approval for course management', 1, 0, '2025-07-11 11:40:17', '2025-07-11 11:40:17'),
            (12, 'Course Management Cancel Approval', 'course_management_cancel_approval', 3, 10, 'Permission to cancel approval for course management', 1, 0, '2025-07-11 11:40:17', '2025-07-11 11:40:17'),
            (13, 'Course Management View Analytics', 'course_management_view_analytics', 3, 12, 'Permission to view course analytics', 1, 0, '2025-07-11 11:40:17', '2025-07-11 11:40:17'),
            (14, 'Show in Sidebar', 'classroom_management_view', 4, 1, 'Permission to view classroom management in sidebar', 1, 0, '2025-07-11 11:42:07', '2025-07-18 08:36:04'),
            (15, 'Classroom Management Create', 'classroom_management_create', 4, 2, 'Permission to create classroom management', 1, 0, '2025-07-11 11:42:07', '2025-07-11 11:42:07'),
            (16, 'Classroom Management Edit', 'classroom_management_edit', 4, 3, 'Permission to edit classroom management', 1, 0, '2025-07-11 11:42:07', '2025-07-11 11:42:07'),
            (17, 'Classroom Management Delete', 'classroom_management_delete', 4, 4, 'Permission to delete classroom management', 1, 0, '2025-07-11 11:42:07', '2025-07-11 11:42:07'),
            (18, 'Classroom Management Status', 'classroom_management_status', 4, 5, 'Permission to update classroom management status', 1, 0, '2025-07-11 11:42:07', '2025-07-11 11:42:07'),
            (19, 'Classroom Management View Analytics', 'classroom_management_view_analytics', 4, 12, 'Permission to view classroom analytics', 1, 0, '2025-07-11 11:42:07', '2025-07-11 11:42:07'),
            (20, 'Show in Sidebar', 'course_approval_workflow_view', 5, 1, 'Permission to view course approval in sidebar', 1, 0, '2025-07-11 11:45:48', '2025-07-18 08:36:04'),
            (21, 'Course Approval Workflow Approve', 'course_approval_workflow_approve', 5, 8, 'Permission to approve course approval workflow', 1, 0, '2025-07-11 11:45:48', '2025-07-11 11:45:48'),
            (22, 'Course Approval Workflow Reject', 'course_approval_workflow_reject', 5, 9, 'Permission to reject course approval workflow', 1, 0, '2025-07-11 11:45:48', '2025-07-11 11:45:48'),
            (23, 'Course Approval Workflow View Analytics', 'course_approval_workflow_view_analytics', 5, 12, 'Permission to view course approval workflow analytics', 1, 0, '2025-07-11 11:45:48', '2025-07-11 11:45:48'),
            (24, 'Show in Sidebar', 'certificate_management_view', 6, 1, 'Permission to view certificate management in sidebar', 1, 0, '2025-07-11 11:46:29', '2025-07-18 08:36:04'),
            (25, 'Certificate Management Create', 'certificate_management_create', 6, 2, 'Permission to create certificates in certificate management', 1, 0, '2025-07-11 11:46:29', '2025-07-11 11:46:29'),
            (26, 'Certificate Management Update', 'certificate_management_update', 6, 3, 'Permission to update certificates in certificate management', 1, 0, '2025-07-11 11:46:29', '2025-07-11 11:46:29'),
            (27, 'Certificate Management Delete', 'certificate_management_delete', 6, 4, 'Permission to delete certificates in certificate management', 1, 0, '2025-07-11 11:46:29', '2025-07-11 11:46:29'),
            (28, 'Show in Sidebar', 'trainee_management_view', 7, 1, 'Permission to view trainee management in sidebar', 1, 0, '2025-07-11 11:47:33', '2025-07-18 08:36:04'),
            (29, 'Trainee Management Create', 'trainee_management_create', 7, 2, 'Permission to create trainees in trainee management', 1, 0, '2025-07-11 11:47:33', '2025-07-11 11:47:33'),
            (30, 'Trainee Management Edit', 'trainee_management_edit', 7, 3, 'Permission to edit trainees in trainee management', 1, 0, '2025-07-11 11:47:33', '2025-07-11 11:47:33'),
            (31, 'Trainee Management Delete', 'trainee_management_delete', 7, 4, 'Permission to delete trainees in trainee management', 1, 0, '2025-07-11 11:47:33', '2025-07-11 11:47:33'),
            (32, 'Trainee Management Status', 'trainee_management_status', 7, 5, 'Permission to update trainee management status', 1, 0, '2025-07-11 11:47:33', '2025-07-11 11:47:33'),
            (33, 'Trainee Management View Analytics', 'trainee_management_view_analytics', 7, 12, 'Permission to view trainee analytics', 1, 0, '2025-07-11 11:47:33', '2025-07-11 11:47:33'),
            (34, 'Show in Sidebar', 'question_bank_management_view', 9, 1, 'Permission to view question bank management in sidebar', 1, 0, '2025-07-11 11:49:40', '2025-07-18 08:36:04'),
            (35, 'Question Bank Management Create', 'question_bank_management_create', 9, 2, 'Permission to create questions in question bank management', 1, 0, '2025-07-11 11:49:40', '2025-07-11 11:49:40'),
            (36, 'Question Bank Management Edit', 'question_bank_management_edit', 9, 3, 'Permission to edit questions in question bank management', 1, 0, '2025-07-11 11:49:40', '2025-07-11 11:49:40'),
            (37, 'Question Bank Management Delete', 'question_bank_management_delete', 9, 4, 'Permission to delete questions in question bank management', 1, 0, '2025-07-11 11:49:40', '2025-07-11 11:49:40'),
            (38, 'Show in Sidebar', 'announcement_management_view', 10, 1, 'Permission to view announcement management in sidebar', 1, 0, '2025-07-11 11:50:54', '2025-07-18 08:36:04'),
            (39, 'Announcement Management Create', 'announcement_management_create', 10, 2, 'Permission to create announcements in announcement management', 1, 0, '2025-07-11 11:50:54', '2025-07-11 11:50:54'),
            (40, 'Announcement Management Edit', 'announcement_management_edit', 10, 3, 'Permission to edit announcements in announcement management', 1, 0, '2025-07-11 11:50:54', '2025-07-11 11:50:54'),
            (41, 'Announcement Management Delete', 'announcement_management_delete', 10, 4, 'Permission to delete announcements in announcement management', 1, 0, '2025-07-11 11:50:54', '2025-07-11 11:50:54'),
            (42, 'Show in Sidebar', 'notification_management_view', 11, 1, 'Permission to view notification management', 1, 0, '2025-07-11 11:51:30', '2025-07-12 19:47:27'),
            (43, 'Show in Sidebar', 'setting_management_view', 12, 1, 'Permission to view settings in sidebar', 1, 0, '2025-07-11 11:51:30', '2025-07-18 08:36:04'),
            (44, 'Course Module Management View', 'course_module_management_view', 13, 1, 'Permission to view course module management', 1, 0, '2025-07-11 11:52:49', '2025-07-11 11:52:49'),
            (45, 'Course Module Management Create', 'course_module_management_create', 13, 2, 'Permission to create course modules in course module management', 1, 0, '2025-07-11 11:52:49', '2025-07-11 11:52:49'),
            (46, 'Course Module Management Edit', 'course_module_management_edit', 13, 3, 'Permission to edit course modules in course module management', 1, 0, '2025-07-11 11:52:49', '2025-07-11 11:52:49'),
            (47, 'Course Module Management Delete', 'course_module_management_delete', 13, 4, 'Permission to delete course modules in course module management', 1, 0, '2025-07-11 11:52:49', '2025-07-11 11:52:49'),
            (48, 'Course Module Management Status', 'course_module_management_status', 13, 5, 'Permission to update status of course modules in course module management', 1, 0, '2025-07-11 11:52:49', '2025-07-11 11:52:49'),
            (49, 'Classroom Assignment View', 'classroom_assignment_view', 15, 1, 'Permission to view classroom assignments', 1, 0, '2025-07-11 11:58:12', '2025-07-11 11:58:12'),
            (50, 'Classroom Assignment Create', 'classroom_assignment_create', 15, 2, 'Permission to create classroom assignments', 1, 0, '2025-07-11 11:58:12', '2025-07-11 11:58:12'),
            (51, 'Classroom Assignment Edit', 'classroom_assignment_edit', 15, 3, 'Permission to edit classroom assignments', 1, 0, '2025-07-11 11:58:12', '2025-07-11 11:58:12'),
            (52, 'Classroom Assignment Delete', 'classroom_assignment_delete', 15, 4, 'Permission to delete classroom assignments', 1, 0, '2025-07-11 11:58:12', '2025-07-11 11:58:12'),
            (53, 'Classroom Assignment Manage', 'classroom_assignment_manage', 15, 11, 'Permission to manage classroom assignments', 1, 0, '2025-07-11 11:58:12', '2025-07-11 11:58:12'),
            (54, 'Classroom Assessment View', 'classroom_assessment_view', 16, 1, 'Permission to view classroom assessments', 1, 0, '2025-07-11 11:58:12', '2025-07-11 11:58:12'),
            (55, 'Classroom Assessment Create', 'classroom_assessment_create', 16, 2, 'Permission to create classroom assessments', 1, 0, '2025-07-11 11:58:12', '2025-07-11 11:58:12'),
            (56, 'Classroom Assessment Edit', 'classroom_assessment_edit', 16, 3, 'Permission to edit classroom assessments', 1, 0, '2025-07-11 11:58:12', '2025-07-11 11:58:12'),
            (57, 'Classroom Assessment Delete', 'classroom_assessment_delete', 16, 4, 'Permission to delete classroom assessments', 1, 0, '2025-07-11 11:58:12', '2025-07-11 11:58:12'),
            (58, 'Classroom Assessment Manage', 'classroom_assessment_manage', 16, 11, 'Permission to manage classroom assessments', 1, 0, '2025-07-11 11:58:12', '2025-07-11 11:58:12'),
            (59, 'Classroom Resources View', 'classroom_resources_view', 17, 1, 'Permission to view classroom resources', 1, 0, '2025-07-11 11:58:12', '2025-07-11 11:58:12'),
            (60, 'Classroom Resources Create', 'classroom_resources_create', 17, 2, 'Permission to create classroom resources', 1, 0, '2025-07-11 11:58:12', '2025-07-11 11:58:12'),
            (61, 'Classroom Resources Edit', 'classroom_resources_edit', 17, 3, 'Permission to edit classroom resources', 1, 0, '2025-07-11 11:58:12', '2025-07-11 11:58:12'),
            (62, 'Classroom Resources Delete', 'classroom_resources_delete', 17, 4, 'Permission to delete classroom resources', 1, 0, '2025-07-11 11:58:12', '2025-07-11 11:58:12'),
            (63, 'Classroom Live Classes View', 'classroom_live_classes_view', 18, 1, 'Permission to view classroom live classes', 1, 0, '2025-07-11 11:58:12', '2025-07-11 11:58:12'),
            (64, 'Classroom Live Classes Create', 'classroom_live_classes_create', 18, 2, 'Permission to create classroom live classes', 1, 0, '2025-07-11 11:58:12', '2025-07-11 11:58:12'),
            (65, 'Classroom Live Classes Edit', 'classroom_live_classes_edit', 18, 3, 'Permission to edit classroom live classes', 1, 0, '2025-07-11 11:58:12', '2025-07-11 11:58:12'),
            (66, 'Classroom Live Classes Delete', 'classroom_live_classes_delete', 18, 4, 'Permission to delete classroom live classes', 1, 0, '2025-07-11 11:58:12', '2025-07-11 11:58:12'),
            (67, 'Classroom Community View', 'classroom_community_view', 19, 1, 'Permission to view classroom community', 1, 0, '2025-07-11 11:58:12', '2025-07-11 11:58:12'),
            (68, 'Classroom Trainees View', 'classroom_trainees_view', 20, 1, 'Permission to view classroom trainees', 1, 0, '2025-07-11 11:58:12', '2025-07-11 11:58:12'),
            (69, 'Classroom Trainees Create', 'classroom_trainees_create', 20, 2, 'Permission to create classroom trainees', 1, 0, '2025-07-11 11:58:12', '2025-07-11 11:58:12'),
            (70, 'Classroom Trainees Edit', 'classroom_trainees_edit', 20, 3, 'Permission to edit classroom trainees', 1, 0, '2025-07-11 11:58:12', '2025-07-11 11:58:12'),
            (71, 'Classroom Trainees Delete', 'classroom_trainees_delete', 20, 4, 'Permission to delete classroom trainees', 1, 0, '2025-07-11 11:58:12', '2025-07-11 11:58:12'),
            (72, 'Show in Sidebar', 'roles_and_access_user_view', 25, 1, 'Permission to view roles and access users', 1, 0, '2025-07-11 12:00:33', '2025-07-12 19:47:27'),
            (73, 'Roles and Access User Create', 'roles_and_access_user_create', 25, 2, 'Permission to create roles and access users', 1, 0, '2025-07-11 12:00:33', '2025-07-11 12:00:33'),
            (74, 'Roles and Access User Edit', 'roles_and_access_user_edit', 25, 3, 'Permission to edit roles and access users', 1, 0, '2025-07-11 12:00:33', '2025-07-11 12:00:33'),
            (75, 'Roles and Access User Delete', 'roles_and_access_user_delete', 25, 4, 'Permission to delete roles and access users', 1, 0, '2025-07-11 12:00:33', '2025-07-11 12:00:33'),
            (76, 'Roles and Access User Status', 'roles_and_access_user_status', 25, 5, 'Permission to update the status of roles and access users', 1, 0, '2025-07-11 12:00:33', '2025-07-11 12:00:33'),
            (77, 'Roles and Access Permissions View', 'roles_and_access_permissions_view', 26, 1, 'Permission to view roles and access permissions', 1, 0, '2025-07-11 12:00:33', '2025-07-11 12:00:33'),
            (78, 'Roles and Access Permissions Create', 'roles_and_access_permissions_create', 26, 2, 'Permission to create roles and access permissions', 1, 0, '2025-07-11 12:00:33', '2025-07-11 12:00:33'),
            (79, 'Roles and Access Permissions Edit', 'roles_and_access_permissions_edit', 26, 3, 'Permission to edit roles and access permissions', 1, 0, '2025-07-11 12:00:33', '2025-07-11 12:00:33'),
            (80, 'Roles and Access Permissions Delete', 'roles_and_access_permissions_delete', 26, 4, 'Permission to delete roles and access permissions', 1, 0, '2025-07-11 12:00:33', '2025-07-11 12:00:33'),
            (81, 'Roles and Access Permissions Status', 'roles_and_access_permissions_status', 26, 5, 'Permission to update the status of roles and access permissions', 1, 0, '2025-07-11 12:00:33', '2025-07-11 12:00:33'),
            (82, 'Settings About Organization View', 'settings_about_organization_view', 27, 1, 'Permission to view organization settings', 1, 0, '2025-07-11 12:03:07', '2025-07-11 12:03:07'),
            (83, 'Settings About Organization Create', 'settings_about_organization_create', 27, 2, 'Permission to create organization settings', 1, 0, '2025-07-11 12:03:07', '2025-07-11 12:03:07'),
            (84, 'Settings About Organization Edit', 'settings_about_organization_edit', 27, 3, 'Permission to edit organization settings', 1, 0, '2025-07-11 12:03:07', '2025-07-11 12:03:07'),
            (85, 'Settings About Organization Delete', 'settings_about_organization_delete', 27, 4, 'Permission to delete organization settings', 1, 0, '2025-07-11 12:03:07', '2025-07-11 12:03:07'),
            (86, 'Settings About Organization Status', 'settings_about_organization_status', 27, 5, 'Permission to update status of organization settings', 1, 0, '2025-07-11 12:03:07', '2025-07-11 12:03:07'),
            (87, 'Settings FAQ View', 'settings_faq_view', 28, 1, 'Permission to view FAQ settings', 1, 0, '2025-07-11 12:03:07', '2025-07-11 12:03:07'),
            (88, 'Settings FAQ Create', 'settings_faq_create', 28, 2, 'Permission to create FAQ settings', 1, 0, '2025-07-11 12:03:07', '2025-07-11 12:03:07'),
            (89, 'Settings FAQ Edit', 'settings_faq_edit', 28, 3, 'Permission to edit FAQ settings', 1, 0, '2025-07-11 12:03:07', '2025-07-11 12:03:07'),
            (90, 'Settings FAQ Delete', 'settings_faq_delete', 28, 4, 'Permission to delete FAQ settings', 1, 0, '2025-07-11 12:03:07', '2025-07-11 12:03:07'),
            (91, 'Settings FAQ Status', 'settings_faq_status', 28, 5, 'Permission to update status of FAQ settings', 1, 0, '2025-07-11 12:03:07', '2025-07-11 12:03:07'),
            (92, 'Settings Query View', 'settings_query_view', 29, 1, 'Permission to view settings queries', 1, 0, '2025-07-11 12:03:07', '2025-07-11 12:03:07'),
            (93, 'Settings Query Edit', 'settings_query_edit', 29, 3, 'Permission to edit settings queries', 1, 0, '2025-07-11 12:03:07', '2025-07-11 12:03:07'),
            (94, 'Settings Payments View', 'settings_payments_view', 30, 1, 'Permission to view payment settings', 1, 0, '2025-07-11 12:03:07', '2025-07-11 12:03:07'),
            (95, 'Settings Payments View Analytics', 'settings_payments_view_analytics', 30, 12, 'Permission to view payment analytics', 1, 0, '2025-07-11 12:03:07', '2025-07-11 12:03:07'),
            (96, 'Course Module Content Management View', 'course_module_content_management_view', 14, 1, 'Permission to view course module content management', 1, 0, '2025-07-11 12:07:02', '2025-07-11 12:07:02'),
            (97, 'Course Module Content Management Create', 'course_module_content_management_create', 14, 2, 'Permission to create course module content in course module content management', 1, 0, '2025-07-11 12:07:02', '2025-07-11 12:07:02'),
            (98, 'Course Module Content Management Edit', 'course_module_content_management_edit', 14, 3, 'Permission to edit course module content in course module content management', 1, 0, '2025-07-11 12:07:02', '2025-07-11 12:07:02'),
            (99, 'Course Module Content Management Delete', 'course_module_content_management_delete', 14, 4, 'Permission to delete course module content in course module content management', 1, 0, '2025-07-11 12:07:02', '2025-07-11 12:07:02'),
            (100, 'Course Module Content Management Status', 'course_module_content_management_status', 14, 5, 'Permission to update status of course module content in course module content management', 1, 0, '2025-07-11 12:07:02', '2025-07-11 12:07:02'),
            (101, 'Classroom Assignment Resource View', 'classroom_assignment_resource_view', 21, 1, 'Permission to view classroom assignment resources', 1, 0, '2025-07-11 12:07:02', '2025-07-11 12:07:02'),
            (102, 'Classroom Assignment Resource Create', 'classroom_assignment_resource_create', 21, 2, 'Permission to create classroom assignment resources', 1, 0, '2025-07-11 12:07:02', '2025-07-11 12:07:02'),
            (103, 'Classroom Assignment Resource Edit', 'classroom_assignment_resource_edit', 21, 3, 'Permission to edit classroom assignment resources', 1, 0, '2025-07-11 12:07:02', '2025-07-11 12:07:02'),
            (104, 'Classroom Assignment Resource Delete', 'classroom_assignment_resource_delete', 21, 4, 'Permission to delete classroom assignment resources', 1, 0, '2025-07-11 12:07:02', '2025-07-11 12:07:02'),
            (105, 'Classroom Assignment Response View', 'classroom_assignment_response_view', 22, 1, 'Permission to view classroom assignment responses', 1, 0, '2025-07-11 12:07:02', '2025-07-11 12:07:02'),
            (106, 'Classroom Assignment Response View Analytics', 'classroom_assignment_response_view_analytics', 22, 12, 'Permission to view analytics for classroom assignment responses', 1, 0, '2025-07-11 12:07:02', '2025-07-11 12:07:02'),
            (107, 'Classroom Assessment Question View', 'classroom_assessment_question_view', 23, 1, 'Permission to view classroom assessment questions', 1, 0, '2025-07-11 12:07:02', '2025-07-11 12:07:02'),
            (108, 'Classroom Assessment Question Create', 'classroom_assessment_question_create', 23, 2, 'Permission to create classroom assessment questions', 1, 0, '2025-07-11 12:07:02', '2025-07-11 12:07:02'),
            (109, 'Classroom Assessment Question Delete', 'classroom_assessment_question_delete', 23, 4, 'Permission to delete classroom assessment questions', 1, 0, '2025-07-11 12:07:02', '2025-07-11 12:07:02'),
            (110, 'Classroom Assessment Result View', 'classroom_assessment_result_view', 24, 1, 'Permission to view classroom assessment results', 1, 0, '2025-07-11 12:07:02', '2025-07-11 12:07:02'),
            (111, 'Classroom Assessment Result Analytics', 'classroom_assessment_result_analytics', 24, 12, 'Permission to view analytics for classroom assessment results', 1, 0, '2025-07-11 12:07:02', '2025-07-11 12:07:02'),
            (112, 'Course Management Create', 'course_management_create', 3, 2, 'Permission to create course management', 1, 0, '2025-07-12 11:55:04', '2025-07-12 11:55:04');
        `);

        // Insert default roles data
        await connection.query(`
            INSERT IGNORE INTO ${dbName}.roles 
            (id, name, description, createdAt, updatedAt) VALUES
            (1, 'admin', 'Administrator role', '2025-05-20 08:16:30', '2025-05-20 08:16:30');
        `);

        // Insert default user roles data
        await connection.query(`
            INSERT IGNORE INTO ${dbName}.user_roles 
            (user_id, role_id, createdAt, updatedAt) VALUES
            (1, 1, '2025-05-20 08:16:30', '2025-05-20 08:16:30');
        `);

        // Insert default users data
        await connection.query(`
            INSERT IGNORE INTO ${dbName}.users 
            (id, profile_pic_url, benner_img_url, name, username, password, email, mobile, country, state, zipcode, address, createdAt, updatedAt, is_verified) VALUES
            (1, NULL, NULL, 'Super Admin', 'superadmin', '$2b$10$dcNOhJEjHeFsiWsfpxtOR.qIlblxDamE04h8aA9fTnQqqsryVMPSe', '<EMAIL>', '1234567890', NULL, NULL, NULL, NULL, '2025-05-20 08:16:30', '2025-05-20 08:16:30', '1');
        `);

        connection.release();
    } catch (error) {
        console.error('Error creating main database:', error);
        throw error;
    }
}

module.exports = { createMainDatabase };
