import React, { useState, useEffect } from 'react';
import NoData from '../../../components/common/NoData';
import Loader from '../../../components/common/Loader';
import { Icon } from '@iconify/react';
import { getTraineeAnalytics } from '../../../services/traineeService';
import { toast } from 'react-toastify';
import { Modal } from 'react-bootstrap';

function TraineeAnalyticsPaymentsTab({ traineeId }) {
  const [loading, setLoading] = useState(false);
  const [paymentData, setPaymentData] = useState([]);
  const [showModal, setShowModal] = useState(false);
  const [selectedPayment, setSelectedPayment] = useState(null);

  const fetchPaymentData = async () => {
    try {
      setLoading(true);
      const response = await getTraineeAnalytics({ trainee_id: traineeId });
      console.log('Payment Data---------------------:', response.data.payments);

      if (response.success) {
        setPaymentData(response.data.payments || []);
      } else {
        toast.error(response.message || 'Failed to fetch payment data');
      }
    } catch (error) {
      console.error('Error fetching payment data:', error);
      toast.error('Something went wrong while fetching payment data');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (traineeId) {
      fetchPaymentData();
    }
  }, [traineeId]);

  const getStatusClass = (status) => {
    const statusClasses = {
      'completed': 'bg-success',
      'pending': 'bg-warning',
      'failed': 'bg-danger',
      'refunded': 'bg-info'
    };
    return statusClasses[status?.toLowerCase()] || 'bg-secondary';
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatAmount = (amount, currency = 'USD') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency.toUpperCase()
    }).format(amount);
  };

  const handleShowModal = (payment) => {
    setSelectedPayment(payment);
    setShowModal(true);
  };

  const handleCloseModal = () => {
    setSelectedPayment(null);
    setShowModal(false);
  };

  const handleViewReceipt = (receiptUrl) => {
    if (receiptUrl) {
      window.open(receiptUrl, '_blank');
    } else {
      toast.warning('Receipt not available for this payment');
    }
  };

  if (loading) {
    return (
      <div className="d-flex justify-content-center align-items-center" style={{ minHeight: '200px' }}>
        <Loader />
      </div>
    );
  }

  if (!paymentData || paymentData.length === 0) {
    return <NoData message="No payment records found" />;
  }

  return (
    <>
      <div className="card">
        <div className="table-responsive">
          <table className="table table-borderless mb-0">
            <thead>
              <tr>
                <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500', width: '80px' }}>SL No.</th>
                <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500', width: '150px' }}>Transaction ID</th>
                <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500',  width: '250px' }}>Course</th>
                <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500', width: '120px' }}>Amount</th>
                <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500', width: '180px' }}>Payment Date</th>
                <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500', width: '120px' }}>Status</th>
                <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500', width: '100px' }}>Actions</th>
              </tr>
            </thead>
            <tbody>
              {paymentData.map((payment, index) => (
                <tr key={payment.id} className="border-bottom">
                  <td className="py-3 align-middle">{index + 1}</td>
                  <td className="py-3 align-middle">
                    <span className="text-truncate d-inline-block" style={{ maxWidth: '130px' }}>
                      {payment.payment_uid}
                    </span>
                  </td>
                  <td className="py-3 align-middle">
                    <span className="text-truncate d-inline-block" style={{ maxWidth: '130px' }}>
                      {payment.course_name}
                    </span>
                  </td>
                  <td className="py-3 align-middle">{formatAmount(payment.amount, payment.currency)}</td>
                  <td className="py-3 align-middle">{formatDate(payment.created_at)}</td>
                  <td className="py-3 align-middle">
                    <span className={`badge ${getStatusClass(payment.status)}`}>
                      {payment.status.charAt(0).toUpperCase() + payment.status.slice(1)}
                    </span>
                  </td>
                  <td className="py-3 align-middle">
                    <div className="d-flex gap-2">
                      {payment.receipt_url && (
                        <button
                          className="btn btn-outline-dark btn-sm border border-dark"
                          style={{ width: '36px', height: '36px' }}
                          title="View Receipt"
                          onClick={() => handleViewReceipt(payment.receipt_url)}
                        >
                          <Icon icon="fluent:document-pdf-24-regular" width="16" height="16" />
                        </button>
                      )}
                      <button
                        className="btn btn-outline-dark btn-sm border border-dark"
                        style={{ width: '36px', height: '36px' }}
                        title="View Details"
                        onClick={() => handleShowModal(payment)}
                      >
                        <Icon icon="fluent:eye-24-regular" width="16" height="16" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Payment Details Modal */}
      <Modal show={showModal} onHide={handleCloseModal}>
        <Modal.Header closeButton className="border-bottom">
          <Modal.Title className="d-flex align-items-center gap-2">
            Payment Details
            {selectedPayment && (
              <span className={`badge ${getStatusClass(selectedPayment.status)}`}>
                {selectedPayment.status.charAt(0).toUpperCase() + selectedPayment.status.slice(1)}
              </span>
            )}
          </Modal.Title>
        </Modal.Header>
        <Modal.Body className="p-0">
          {selectedPayment && (
            <div className="px-4">
              <div className="border-bottom py-3">
                <div className="row mb-2">
                  <div className="col-4">
                    <div className="text-muted">Transaction ID</div>
                  </div>
                  <div className="col-8">
                    <div className="fw-medium" style={{ wordBreak: 'break-all' }}>
                      {selectedPayment.payment_uid}
                    </div>
                  </div>
                </div>
                <div className="row mb-2">
                  <div className="col-4">
                    <div className="text-muted">Course</div>
                  </div>
                  <div className="col-8">
                    <div>{selectedPayment.course_name}</div>
                  </div>
                </div>
                <div className="row mb-2">
                  <div className="col-4">
                    <div className="text-muted">Amount</div>
                  </div>
                  <div className="col-8">
                    <div>{formatAmount(selectedPayment.amount, selectedPayment.currency)}</div>
                  </div>
                </div>
                <div className="row mb-2">
                  <div className="col-4">
                    <div className="text-muted">Payment Method</div>
                  </div>
                  <div className="col-8">
                    <div className="text-capitalize">{selectedPayment.payment_method}</div>
                  </div>
                </div>
                <div className="row">
                  <div className="col-4">
                    <div className="text-muted">Payment Date</div>
                  </div>
                  <div className="col-8">
                    <div>{formatDate(selectedPayment.created_at)}</div>
                  </div>
                </div>
              </div>

              {selectedPayment.receipt_url && (
                <div className="py-3">
                  <button
                    className="btn btn-primary w-100"
                    onClick={() => handleViewReceipt(selectedPayment.receipt_url)}
                  >
                    <Icon icon="fluent:document-pdf-24-regular" className="me-2" width="16" height="16" />
                    View Receipt
                  </button>
                </div>
              )}
            </div>
          )}
        </Modal.Body>
        <Modal.Footer className="border-top">
          <button className="btn btn-secondary" onClick={handleCloseModal}>
            Close
          </button>
        </Modal.Footer>
      </Modal>
    </>
  );
}

export default TraineeAnalyticsPaymentsTab; 