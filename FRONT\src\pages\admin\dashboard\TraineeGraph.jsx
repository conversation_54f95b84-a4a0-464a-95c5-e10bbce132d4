import React from 'react';
import {
  Bar<PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer
} from 'recharts';
import Loader from '../../../components/common/Loader';

const formatChartData = (apiData) => {
  if (!apiData || !Array.isArray(apiData)) return [];

  const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                     'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

  return apiData.map(item => {
    const date = new Date(item.month + '-01');
    const monthIndex = date.getMonth();
    return {
      name: monthNames[monthIndex] || item.month,
      Trainees: item.count || 0
    };
  });
};

function TraineeGraph({ data = [], loading = false }) {
  const chartData = formatChartData(data);
  return (
    <div className="card p-2">
      <h6 className="mb-2">👥 Trainee Statistics (Monthly Joins)</h6>
      {loading ? (
        <div className="d-flex justify-content-center align-items-center" style={{ height: '260px' }}>
          <Loader />
        </div>
      ) : (
        <ResponsiveContainer width="100%" height={260}>
        <BarChart
          data={chartData}
          barSize={24}
          margin={{ top: 10, right: 0, left: -10, bottom: -5 }}
        >
          <defs>
            <linearGradient id="traineeGradient" x1="0" y1="0" x2="0" y2="1">
              <stop offset="0%" stopColor="#6fbaff" />
              <stop offset="100%" stopColor="#0d6efd" />
            </linearGradient>
          </defs>

          <CartesianGrid strokeDasharray="3 3" vertical={false} />
          <XAxis dataKey="name" padding={{ left: 0, right: 0 }} stroke="#888" fontSize={12} />
          <YAxis allowDecimals={false} stroke="#888" fontSize={12} />
          <Tooltip />
          <Bar dataKey="Trainees" fill="url(#traineeGradient)" radius={[10, 10, 0, 0]} />
        </BarChart>
      </ResponsiveContainer>
      )}
    </div>
  );
}

export default TraineeGraph;
