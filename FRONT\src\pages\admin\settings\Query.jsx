import React, { useState, useEffect, useCallback } from 'react'
import { Icon } from '@iconify/react';
import { toast } from 'react-toastify';
import Loader from '../../../components/common/Loader';
import NoData from '../../../components/common/NoData';
import {
  getTickets,
  sendTicketResponse
} from '../../../services/adminService';
import { usePermissions } from '../../../context/PermissionsContext';

function Query() {
  const { permissions } = usePermissions();
  const [showResponseModal, setShowResponseModal] = useState(false);
  const [selectedTicket, setSelectedTicket] = useState(null);
  const [loading, setLoading] = useState(false);
  const [ticketData, setTicketData] = useState([]);
  const [pagination, setPagination] = useState({
    currentPage: 1,
    totalPages: 1,
    totalRecords: 0,
    recordsPerPage: 10
  });
  const [search, setSearch] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [responseForm, setResponseForm] = useState({
    response: '',
    status: ''
  });
  const [formErrors, setFormErrors] = useState({
    response: '',
    status: ''
  });

  // Constants for validation
  const MAX_RESPONSE_LENGTH = 1000;
  const MIN_RESPONSE_LENGTH = 10;

  // Validation function
  const validateForm = () => {
    let isValid = true;
    const errors = {
      response: '',
      status: ''
    };

    // Response validation
    if (!responseForm.response.trim()) {
      errors.response = 'Response is required';
      isValid = false;
    } else if (responseForm.response.trim().length < MIN_RESPONSE_LENGTH) {
      errors.response = `Response must be at least ${MIN_RESPONSE_LENGTH} characters`;
      isValid = false;
    } else if (responseForm.response.trim().length > MAX_RESPONSE_LENGTH) {
      errors.response = `Response cannot exceed ${MAX_RESPONSE_LENGTH} characters`;
      isValid = false;
    }

    // Status validation
    if (!responseForm.status) {
      errors.status = 'Please select a status';
      isValid = false;
    }

    setFormErrors(errors);
    return isValid;
  };

  // Fetch ticket data
  const fetchTicketData = useCallback(async () => {
    try {
      setLoading(true);
      const response = await getTickets({
        page: pagination.currentPage,
        limit: pagination.recordsPerPage,
        search: search,
        status: statusFilter
      });

      if (response.success) {
        setTicketData(response.data.records);
        setPagination(response.data.pagination);
      } else {
        toast.error('Failed to fetch ticket data');
      }
    } catch (error) {
      console.error('Error fetching ticket data:', error);
      toast.error('Error fetching ticket data');
    } finally {
      setLoading(false);
    }
  }, [pagination.currentPage, pagination.recordsPerPage, search, statusFilter]);

  // Load data on component mount and when dependencies change
  useEffect(() => {
    fetchTicketData();
  }, [fetchTicketData]);

  const getStatusClass = (status) => {
    switch (status) {
      case 'Solved':
        return (
          <span className="badge border border-success bg-success bg-opacity-10 text-success d-inline-flex align-items-center gap-1">
            <Icon icon="mdi:check-circle-outline" className="fs-6" />
            Solved
          </span>
        );
      case 'Pending':
        return (
          <span className="badge border border-warning bg-warning bg-opacity-10 text-warning d-inline-flex align-items-center gap-1">
            <Icon icon="mdi:clock-outline" className="fs-6" />
            Pending
          </span>
        );
      case 'In Progress':
        return (
          <span className="badge border border-info bg-info bg-opacity-10 text-info d-inline-flex align-items-center gap-1">
            <Icon icon="mdi:progress-clock" className="fs-6" />
            In Progress
          </span>
        );
      case 'Cancelled':
        return (
          <span className="badge border border-danger bg-danger bg-opacity-10 text-danger d-inline-flex align-items-center gap-1">
            <Icon icon="mdi:close-circle-outline" className="fs-6" />
            Cancelled
          </span>
        );
      default:
        return (
          <span className="badge border border-secondary bg-secondary bg-opacity-10 text-secondary d-inline-flex align-items-center gap-1">
            <Icon icon="mdi:help-circle-outline" className="fs-6" />
            Unknown
          </span>
        );
    }
  };

  const handleResponseClick = (ticket) => {
    if (!permissions.settings_query_edit) {
      toast.warning("You don't have permission to respond to queries");
      return;
    }
    setSelectedTicket(ticket);
    setResponseForm({
      response: ticket.response || '',
      status: ticket.status || ''
    });
    setShowResponseModal(true);
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    
    // Clear error when user starts typing
    setFormErrors(prev => ({
      ...prev,
      [name]: ''
    }));

    // Prevent typing if max length is reached for response
    if (name === 'response') {
      if (value.length <= MAX_RESPONSE_LENGTH) {
        setResponseForm(prev => ({
          ...prev,
          [name]: value
        }));
      }
    } else {
      setResponseForm(prev => ({
        ...prev,
        [name]: value
      }));
    }
  };

  const handleSubmitResponse = async (e) => {
    e.preventDefault();
    
    if (!permissions.settings_query_edit) {
      toast.warning("You don't have permission to respond to queries");
      return;
    }

    // Validate form before submission
    if (!validateForm()) {
      return;
    }

    try {
      setLoading(true);
      const response = await sendTicketResponse(selectedTicket.id, {
        ...responseForm,
        response: responseForm.response.trim()
      });
      if (response.success) {
        toast.success('Ticket response sent successfully');
        fetchTicketData();
        setShowResponseModal(false);
        setSelectedTicket(null);
        resetForm();
      } else {
        toast.error(response.message || 'Failed to send ticket response');
      }
    } catch (error) {
      console.error('Error sending ticket response:', error);
      toast.error('Error sending ticket response');
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    setResponseForm({ response: '', status: '' });
    setFormErrors({ response: '', status: '' });
  };

  const handleSearchChange = (e) => {
    setSearch(e.target.value);
    setPagination(prev => ({ ...prev, currentPage: 1 }));
  };

  const handleStatusFilterChange = (e) => {
    setStatusFilter(e.target.value);
    setPagination(prev => ({ ...prev, currentPage: 1 }));
  };

  const handlePageChange = (newPage) => {
    setPagination(prev => ({ ...prev, currentPage: newPage }));
  };

  const handleLimitChange = (e) => {
    const newLimit = parseInt(e.target.value);
    setPagination(prev => ({
      ...prev,
      recordsPerPage: newLimit,
      currentPage: 1
    }));
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleString();
  };

  return (
    <>
      {/* search and filter section */}
      {!permissions.settings_query_view ? (
        <div className="alert alert-warning">
          <Icon icon="mdi:warning" className="me-2" />
          You don't have permission to view queries.
        </div>
      ) : (
        <>
          <div className="row mb-3">
            <div className="col-md-6 mt-2 d-flex justify-content-start align-items-center gap-2">
              <input
                type="search"
                className="form-control"
                placeholder="Search by Ticket ID, Subject, Name or Email..."
                style={{ maxWidth: '350px' }}
                value={search}
                onChange={handleSearchChange}
              />
            </div>
            <div className="col-md-6 text-end mt-2 d-flex justify-content-end align-items-center gap-2">
              <select
                className="form-select"
                style={{ width: '180px' }}
                value={statusFilter}
                onChange={handleStatusFilterChange}
              >
                <option value="">All Status</option>
                <option value="Solved">Solved</option>
                <option value="Pending">Pending</option>
                <option value="In Progress">In Progress</option>
                <option value="Cancelled">Cancelled</option>
              </select>
            </div>
          </div>

          {/* table */}
          <div className="row mb-3">
            <div className="col-12">
              <div className="card">
                <div className="table-responsive">
                  <table className="table table-borderless mb-0" style={{ minWidth: '1200px' }}>
                    <thead>
                      <tr>
                        <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500', width: '80px' }}>SL No</th>
                        <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500', width: '150px' }}>Ticket ID</th>
                        <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500', width: '200px' }}>Name</th>
                        <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500', width: '200px' }}>Email</th>
                        <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500', width: '250px' }}>Subject</th>
                        <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500', width: '200px' }}>Response</th>
                        <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500', width: '120px' }}>Status</th>
                        <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500', width: '180px' }}>Submitted Date</th>
                        <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500', width: '120px' }}>Action</th>
                      </tr>
                    </thead>
                    <tbody>
                      {loading ? (
                        <tr>
                          <td colSpan="9" className="text-center py-4">
                            <Loader />
                          </td>
                        </tr>
                      ) : ticketData.length === 0 ? (
                        <tr>
                          <td colSpan="9" className="text-center py-4">
                            <NoData />
                          </td>
                        </tr>
                      ) : (
                        ticketData.map((ticket, index) => (
                          <tr key={ticket.id} className="border-bottom">
                            <td className="py-3 align-middle">
                              {(pagination.currentPage - 1) * pagination.recordsPerPage + index + 1}
                            </td>
                            <td className="py-3 align-middle">{ticket.ticket_id}</td>
                            <td className="py-3 align-middle">{ticket.sender_name || 'N/A'}</td>
                            <td className="py-3 align-middle">{ticket.sender_email || 'N/A'}</td>
                            <td className="py-3 align-middle">{ticket.subject}</td>
                            <td className="py-3 align-middle">
                              {ticket.response ? (
                                <span className="text-truncate" style={{ maxWidth: '200px', display: 'inline-block' }}>
                                  {ticket.response}
                                </span>
                              ) : (
                                <span className="text-muted">No response yet</span>
                              )}
                            </td>
                            <td className="py-3 align-middle">
                              {getStatusClass(ticket.status)}
                            </td>
                            <td className="py-3 align-middle">{formatDate(ticket.createdAt)}</td>
                            <td className="py-3 align-middle">
                              <div className="d-flex">
                                <button
                                  className="btn btn-outline-dark btn-sm border border-dark"
                                  style={{ width: '36px', height: '36px' }}
                                  title={!permissions.settings_query_edit ? "You don't have permission to respond" : "Add/Edit Response"}
                                  onClick={() => handleResponseClick(ticket)}
                                  disabled={loading || !permissions.settings_query_edit}
                                >
                                  <Icon icon="fluent:chat-24-regular" width="16" height="16" />
                                </button>
                              </div>
                            </td>
                          </tr>
                        ))
                      )}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>

          {/* record per page and pagination */}
          <div className="row">
            <div className="col-md-6">
              <select
                className="form-select"
                style={{ width: 'auto' }}
                value={pagination.recordsPerPage}
                onChange={handleLimitChange}
              >
                <option value={10}>10 records per page</option>
                <option value={25}>25 records per page</option>
                <option value={50}>50 records per page</option>
              </select>
            </div>
            <div className="col-md-6 d-flex justify-content-end align-items-center gap-3">
              <button
                className="btn btn-primary"
                style={{ width: '150px' }}
                onClick={() => handlePageChange(pagination.currentPage - 1)}
                disabled={pagination.currentPage === 1 || loading}
              >
                Prev
              </button>
              <span>Page {pagination.currentPage} of {pagination.totalPages}</span>
              <button
                className="btn btn-primary"
                style={{ width: '150px' }}
                onClick={() => handlePageChange(pagination.currentPage + 1)}
                disabled={pagination.currentPage === pagination.totalPages || loading}
              >
                Next
              </button>
            </div>
          </div>
        </>
      )}

      {/* Response Modal */}
      {showResponseModal && selectedTicket && permissions.settings_query_edit && (
        <div className="modal show d-block" tabIndex="-1" style={{ backgroundColor: 'rgba(0,0,0,0.5)' }}>
          <div className="modal-dialog modal-dialog-centered modal-lg">
            <div className="modal-content">
              <div className="modal-header border-0 pb-0">
                <h5 className="modal-title">Respond to Ticket - {selectedTicket.ticket_id}</h5>
                <button
                  type="button"
                  className="btn-close"
                  onClick={() => {
                    setShowResponseModal(false);
                    setSelectedTicket(null);
                    resetForm();
                  }}
                ></button>
              </div>
              <div className="modal-body pt-2">
                <form onSubmit={handleSubmitResponse}>
                  <div className="row">
                    <div className="col-12">
                      <div className="mb-3">
                        <label className="form-label fw-bold">Ticket ID:</label>
                        <p className="mb-0">{selectedTicket.ticket_id}</p>
                      </div>
                    </div>
                    <div className="col-12">
                      <div className="mb-3">
                        <label className="form-label fw-bold">Name:</label>
                        <p className="mb-0">{selectedTicket.sender_name || 'N/A'}</p>
                      </div>
                    </div>
                    <div className="col-12">
                      <div className="mb-3">
                        <label className="form-label fw-bold">Email:</label>
                        <p className="mb-0">{selectedTicket.sender_email || 'N/A'}</p>
                      </div>
                    </div>
                    <div className="col-12">
                      <div className="mb-3">
                        <label className="form-label fw-bold">Phone Number:</label>
                        <p className="mb-0">{selectedTicket.sender_phone || 'N/A'}</p>
                      </div>
                    </div>
                    <div className="col-12">
                      <div className="mb-3">
                        <label className="form-label fw-bold">Subject:</label>
                        <p className="mb-0">{selectedTicket.subject}</p>
                      </div>
                    </div>
                    <div className="col-12">
                      <div className="mb-3">
                        <label className="form-label fw-bold">Summary:</label>
                        <p className="mb-0">{selectedTicket.description || 'No description provided'}</p>
                      </div>
                    </div>
                    <div className="col-12">
                      <div className="mb-3">
                        <label className="form-label fw-bold">
                          Response <span className="text-danger">*</span>
                        </label>
                        <textarea
                          className={`form-control ${formErrors.response ? 'is-invalid' : ''}`}
                          name="response"
                          value={responseForm.response}
                          onChange={handleInputChange}
                          placeholder="Enter your response"
                          rows="4"
                          disabled={loading}
                        />
                        {formErrors.response && (
                          <div className="invalid-feedback">{formErrors.response}</div>
                        )}
                        <small className="text-muted">
                          {responseForm.response.length}/{MAX_RESPONSE_LENGTH} characters
                        </small>
                      </div>
                    </div>
                    <div className="col-12">
                      <div className="mb-3">
                        <label className="form-label fw-bold">
                          Update Status <span className="text-danger">*</span>
                        </label>
                        <select
                          className={`form-select ${formErrors.status ? 'is-invalid' : ''}`}
                          name="status"
                          value={responseForm.status}
                          onChange={handleInputChange}
                          disabled={loading}
                        >
                          <option value="">Select Status</option>
                          <option value="Solved">Solved</option>
                          <option value="Pending">Pending</option>
                          <option value="In Progress">In Progress</option>
                          <option value="Cancelled">Cancelled</option>
                        </select>
                        {formErrors.status && (
                          <div className="invalid-feedback">{formErrors.status}</div>
                        )}
                      </div>
                    </div>
                  </div>
                  <div className="modal-footer px-0 pb-0">
                    <button
                      type="button"
                      className="btn btn-secondary"
                      onClick={() => {
                        setShowResponseModal(false);
                        setSelectedTicket(null);
                        resetForm();
                      }}
                      disabled={loading}
                    >
                      Cancel
                    </button>
                    <button
                      type="submit"
                      className="btn btn-primary"
                      disabled={loading}
                    >
                      {loading ? 'Submitting...' : 'Submit Response'}
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  )
}

export default Query
