import { GET, POST } from './apiController';

// ------------------------------------------------- Authentication APIs

export const loginApi = (data) => {
    console.log("Login API called with data:", data);
    return POST("/org/user_login", data);
};

export const signUpApi = (data) => {
    return POST("/org/user_registration", data);
};

export const magicLinkApi = (data) => {
    return POST("/org/send_link", data);
};

export const forgotPasswordApi = (data) => {
    return POST("/org/forgot_password", data);
};

export const verifyRegisterOTPApi = (data) => {
    return POST("/org/verify_register_otp", data);
};

export const resetPasswordApi = (data) => {
    return POST("/org/reset_password", data);
};

export const verifyOTPApi = (data) => {
    return POST("/org/verify_otp", data);
};

export const sendOTPApi = (data) => {
    return POST("/org/forgot_password", data);
};

export const getUserByIdApi = () => {
    return GET("/org/get_user_by_id");
};

export const logoutApi = () => {
    return GET("/org/user_logout");
};

// /new_registration
export const newRegistrationApi = (data) => {
    return POST("/org/new_registration", data);
};


// send_otp
export const resendOTPApi = (data) => {
    return POST("/org/send_otp", data);
};

export const ActiveAccountOTPFromLoginPage = (data) => {
    return POST("/org/active_account_otp_from_login_page", data);
};





