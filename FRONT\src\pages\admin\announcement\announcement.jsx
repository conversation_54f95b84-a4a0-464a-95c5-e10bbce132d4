import React, { useState, useEffect, useCallback } from 'react'
import { Icon } from '@iconify/react';
import { toast } from 'react-toastify';
import moment from 'moment-timezone';
import timeZones from '../../../utils/timeZone.json';
import Loader from '../../../components/common/Loader';
import NoData from '../../../components/common/NoData';
import {
    getAnnouncementsRole,
    getAnnouncements,
    createAnnouncements,
    updateAnnouncements,
    deleteAnnouncement,
    sendAnnouncements,
} from '../../../services/adminService';
import { usePermissions } from '../../../context/PermissionsContext';

const DefaultTimeZone = process.env.REACT_APP_DEFAULT_TIMEZONE;

function Announcement() {
    const { permissions } = usePermissions();
    const [showModal, setShowModal] = useState(false);
    const [showDeleteModal, setShowDeleteModal] = useState(false);
    const [showViewModal, setShowViewModal] = useState(false);
    const [loading, setLoading] = useState(false);
    const [announcementData, setAnnouncementData] = useState([]);
    const [roles, setRoles] = useState([]);
    const [selectedAnnouncement, setSelectedAnnouncement] = useState(null);
    const [editMode, setEditMode] = useState(false);
    const [search, setSearch] = useState('');
    const [statusFilter, setStatusFilter] = useState('');
    const [timeZoneSearch, setTimeZoneSearch] = useState('');
    const [isTimeZoneDropdownOpen, setIsTimeZoneDropdownOpen] = useState(false);
    const [pagination, setPagination] = useState({
        currentPage: 1,
        totalPages: 1,
        totalRecords: 0,
        recordsPerPage: 10
    });
    const [formData, setFormData] = useState({
        name: '',
        description: '',
        scheduleDate: '',
        role: '',
        timeZone: DefaultTimeZone,
        sendNotification: true,
        sendEmail: false
    });
    const [formErrors, setFormErrors] = useState({
        name: '',
        description: '',
        scheduleDate: '',
        role: '',
        timeZone: ''
    });

    // Constants for validation
    const MAX_NAME_LENGTH = 2000;
    const MAX_DESCRIPTION_LENGTH = 2000;
    const MIN_NAME_LENGTH = 5;
    const MIN_DESCRIPTION_LENGTH = 20;

    const user_id = JSON.parse(localStorage.getItem('user')).id;

    // Validation function
    const validateForm = () => {
        let isValid = true;
        const errors = {
            name: '',
            description: '',
            scheduleDate: '',
            role: '',
            timeZone: ''
        };

        // Name validation
        if (!formData.name.trim()) {
            errors.name = 'Announcement name is required';
            isValid = false;
        } else if (formData.name.trim().length < MIN_NAME_LENGTH) {
            errors.name = `Name must be at least ${MIN_NAME_LENGTH} characters`;
            isValid = false;
        } else if (formData.name.trim().length > MAX_NAME_LENGTH) {
            errors.name = `Name cannot exceed ${MAX_NAME_LENGTH} characters`;
            isValid = false;
        }

        // Description validation
        if (!formData.description.trim()) {
            errors.description = 'Description is required';
            isValid = false;
        } else if (formData.description.trim().length < MIN_DESCRIPTION_LENGTH) {
            errors.description = `Description must be at least ${MIN_DESCRIPTION_LENGTH} characters`;
            isValid = false;
        } else if (formData.description.trim().length > MAX_DESCRIPTION_LENGTH) {
            errors.description = `Description cannot exceed ${MAX_DESCRIPTION_LENGTH} characters`;
            isValid = false;
        }

        // Schedule Date validation
        if (!formData.scheduleDate) {
            errors.scheduleDate = 'Schedule date is required';
            isValid = false;
        } else {
            const selectedDate = moment.tz(formData.scheduleDate, formData.timeZone);
            const now = moment();
            if (selectedDate.isBefore(now)) {
                errors.scheduleDate = 'Schedule date cannot be in the past';
                isValid = false;
            }
        }

        // Role validation
        if (!formData.role) {
            errors.role = 'Please select a role';
            isValid = false;
        }

        // Time Zone validation
        if (!formData.timeZone) {
            errors.timeZone = 'Please select a time zone';
            isValid = false;
        } else if (!timeZones.some(zone => zone.value === formData.timeZone)) {
            errors.timeZone = 'Please select a valid time zone';
            isValid = false;
        }

        setFormErrors(errors);
        return isValid;
    };

    // Fetch announcements data
    const fetchAnnouncements = useCallback(async () => {
        try {
            setLoading(true);
            const response = await getAnnouncements({
                page: pagination.currentPage,
                limit: pagination.recordsPerPage,
                search: search,
                status: statusFilter
            });

            console.log('Announcement response', response);

            if (response.success) {
                setAnnouncementData(response.data || []);
                // Note: Update pagination if backend provides it
                // setPagination(response.pagination);
            } else {
                toast.error('Failed to fetch announcements');
            }
        } catch (error) {
            console.error('Error fetching announcements:', error);
            toast.error('Error fetching announcements');
        } finally {
            setLoading(false);
        }
    }, [pagination.currentPage, pagination.recordsPerPage, search, statusFilter]);

    // Fetch roles for dropdown
    const fetchRoles = useCallback(async () => {
        try {
            const response = await getAnnouncementsRole();
            console.log('Announcement Roles response', response);

            if (response.success) {
                setRoles(response.data || []);
            } else {
                toast.error('Failed to fetch roles');
            }
        } catch (error) {
            console.error('Error fetching roles:', error);
            toast.error('Error fetching roles');
        }
    }, []);

    // Load data on component mount and when dependencies change
    useEffect(() => {
        fetchAnnouncements();
        fetchRoles();
    }, [fetchAnnouncements, fetchRoles]);

    const getStatusClass = (status) => {
        const statusClasses = {
            'Published': 'bg-success-subtle',
            'Pending': 'bg-info-subtle'
        }
        return statusClasses[status] || 'bg-secondary-subtle'
    }

    const handleInputChange = (e) => {
        const { name, value, type, checked } = e.target;

        // Clear error when user starts typing
        setFormErrors(prev => ({
            ...prev,
            [name]: ''
        }));

        if (type === 'checkbox') {
            setFormData(prev => ({
                ...prev,
                [name]: checked
            }));
        } else if (name === 'name' && value.length <= MAX_NAME_LENGTH) {
            setFormData(prev => ({
                ...prev,
                [name]: value
            }));
        } else if (name === 'description' && value.length <= MAX_DESCRIPTION_LENGTH) {
            setFormData(prev => ({
                ...prev,
                [name]: value
            }));
        } else if (name !== 'name' && name !== 'description') {
            setFormData(prev => ({
                ...prev,
                [name]: value
            }));
        }
    };

    const handleSubmit = async (e) => {
        e.preventDefault();

        if (editMode && !permissions.announcement_management_edit) {
            toast.warning("You don't have permission to edit announcements");
            return;
        }

        if (!editMode && !permissions.announcement_management_create) {
            toast.warning("You don't have permission to create announcements");
            return;
        }

        // Validate form before submission
        if (!validateForm()) {
            return;
        }

        try {
            setLoading(true);

            // Convert the local date to UTC based on selected timezone
            const localDate = moment.tz(formData.scheduleDate, formData.timeZone);
            const utcDate = localDate.utc().format();

            const payload = {
                createdBy: user_id,
                title: formData.name.trim(),
                message: formData.description.trim(),
                scheduleDateTime: utcDate,
                roles: formData.role,
                send_notification: 1, // Always set to 1
                send_email: 0, // Always set to 0
                time_zone: formData.timeZone // Add timezone to payload
            };

            console.log('Submitting announcement:', payload);

            let response;
            if (editMode && selectedAnnouncement) {
                response = await updateAnnouncements(selectedAnnouncement.id, payload);
                if (response.success) {
                    toast.success('Announcement updated successfully');
                    fetchAnnouncements();
                    handleCloseModal();
                } else {
                    toast.error(response.message || 'Failed to update announcement');
                }
            } else {
                response = await createAnnouncements(payload);
                if (response.success) {
                    toast.success('Announcement created successfully');
                    fetchAnnouncements();
                    handleCloseModal();
                } else {
                    toast.error(response.message || 'Failed to create announcement');
                }
            }
        } catch (error) {
            console.error('Error saving announcement:', error);
            toast.error('Error saving announcement');
        } finally {
            setLoading(false);
        }
    };

    const handleCloseModal = () => {
        setShowModal(false);
        setEditMode(false);
        setSelectedAnnouncement(null);
        setFormData({
            name: '',
            description: '',
            scheduleDate: '',
            role: '',
            timeZone: DefaultTimeZone,
            sendNotification: false,
            sendEmail: false
        });
        setFormErrors({
            name: '',
            description: '',
            scheduleDate: '',
            role: '',
            timeZone: ''
        });
    };

    const handleEdit = (announcement) => {
        if (!permissions.announcement_management_edit) {
            toast.warning("You don't have permission to edit announcements");
            return;
        }
        

        
        setSelectedAnnouncement(announcement);
        setEditMode(true);
        setFormData({
            name: announcement.title || '',
            description: announcement.message || '',
            scheduleDate: moment.tz(announcement.scheduleDateTime, 'UTC').tz(announcement.time_zone || DefaultTimeZone).format('YYYY-MM-DDTHH:mm'), // ✅ Convert from UTC to local time zone
            role: announcement.roles || '',
            timeZone: announcement.time_zone || DefaultTimeZone,
            sendNotification: announcement.send_notification === 1,
            sendEmail: announcement.send_email === 1
          });
        setShowModal(true);
    };

    const handleDelete = async () => {
        if (!selectedAnnouncement) return;

        if (!permissions.announcement_management_delete) {
            toast.warning("You don't have permission to delete announcements");
            return;
        }

        try {
            setLoading(true);
            const response = await deleteAnnouncement(selectedAnnouncement.id);

            if (response.success) {
                toast.success('Announcement deleted successfully');
                fetchAnnouncements();
                setShowDeleteModal(false);
                setSelectedAnnouncement(null);
            } else {
                toast.error(response.message || 'Failed to delete announcement');
            }
        } catch (error) {
            console.error('Error deleting announcement:', error);
            toast.error('Error deleting announcement');
        } finally {
            setLoading(false);
        }
    };

    const handleView = (announcement) => {
        setSelectedAnnouncement(announcement);
        setShowViewModal(true);
    };

    const handleSearchChange = (e) => {
        setSearch(e.target.value);
        setPagination(prev => ({ ...prev, currentPage: 1 }));
    };

    const handleStatusFilterChange = (e) => {
        setStatusFilter(e.target.value);
        setPagination(prev => ({ ...prev, currentPage: 1 }));
    };

    const handlePageChange = (newPage) => {
        setPagination(prev => ({ ...prev, currentPage: newPage }));
    };

    const handleLimitChange = (e) => {
        const newLimit = parseInt(e.target.value);
        setPagination(prev => ({
            ...prev,
            recordsPerPage: newLimit,
            currentPage: 1
        }));
    };

    const formatDate = (dateString) => {
        if (!dateString) return '';
        
        // Create a date object from the UTC string
        const date = new Date(dateString);
        
        // Format the date using the user's local timezone
        const formattedDate = date.toLocaleString('en-US', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            hour12: true,
            timeZone: formData.timeZone || DefaultTimeZone,
            timeZoneName: 'short' // Add timezone abbreviation
        });

        return formattedDate;
    };

    return (
        <>
            {/* search, status and add button announcement  */}
            <div className="row d-flex mb-3">
                <div className="col-md-6 mt-2 d-flex justify-content-start align-items-center">
                    <input
                        type="search"
                        className="form-control"
                        placeholder="Search announcements..."
                        style={{ maxWidth: '300px' }}
                        value={search}
                        onChange={handleSearchChange}
                    />
                </div>
                <div className="col-md-6 text-end mt-2 d-flex justify-content-end align-items-center gap-2">
                    <select
                        className="form-select"
                        style={{ width: '180px' }}
                        value={statusFilter}
                        onChange={handleStatusFilterChange}
                    >
                        <option value="">All Status</option>
                        <option value="1">Published</option>
                        <option value="0">Pending</option>
                    </select>
                    <button
                        className="btn btn-primary text-nowrap"
                        style={{ width: '180px' }}
                        onClick={() => {
                            if (!permissions.announcement_management_create) {
                                toast.warning("You don't have permission to create announcements");
                                return;
                            }
                            setShowModal(true);
                        }}
                        disabled={loading || !permissions.announcement_management_create}
                        title={permissions.announcement_management_create ? "Create Announcement" : "You don't have permission to create announcements"}
                    >
                        Create Announcement
                    </button>
                </div>
            </div>

            {/* Create Announcement Modal */}
            {showModal && (
                <div className="modal show d-block" tabIndex="-1" style={{ backgroundColor: 'rgba(0,0,0,0.5)' }}>
                    <div className="modal-dialog modal-dialog-centered">
                        <div className="modal-content">
                            <div className="modal-header border-bottom">
                                <h5 className="modal-title fw-medium">
                                    {editMode ? 'Edit Announcement' : 'Create Announcement'}
                                </h5>
                                <button type="button" className="btn-close" onClick={handleCloseModal}></button>
                            </div>
                            <div className="modal-body">
                                {(editMode && !permissions.announcement_management_edit) || (!editMode && !permissions.announcement_management_create) ? (
                                    <div className="alert alert-warning">
                                        <Icon icon="mdi:warning" className="me-2" />
                                        {editMode ? "You don't have permission to edit announcements" : "You don't have permission to create announcements"}
                                    </div>
                                ) : (
                                    <form onSubmit={handleSubmit}>
                                        <div className="mb-3">
                                            <label className="form-label fw-medium">
                                                Announcement Name <span className="text-danger">*</span>
                                            </label>
                                            <input
                                                type="text"
                                                className={`form-control ${formErrors.name ? 'is-invalid' : ''}`}
                                                name="name"
                                                value={formData.name}
                                                onChange={handleInputChange}
                                                placeholder="Enter announcement name (max 2000 characters)"
                                            />
                                            <div className="d-flex justify-content-between align-items-center mt-1">
                                                <small className={`${formData.name.length >= MAX_NAME_LENGTH ? 'text-danger' : 'text-muted'}`}>
                                                    {formData.name.length}/{MAX_NAME_LENGTH} characters
                                                </small>
                                                {formErrors.name && (
                                                    <small className="text-danger">{formErrors.name}</small>
                                                )}
                                            </div>
                                        </div>

                                        <div className="mb-3">
                                            <label className="form-label fw-medium">
                                                Description <span className="text-danger">*</span>
                                            </label>
                                            <textarea
                                                className={`form-control ${formErrors.description ? 'is-invalid' : ''}`}
                                                name="description"
                                                value={formData.description}
                                                onChange={handleInputChange}
                                                placeholder="Enter description (max 2000 characters)"
                                                rows="4"
                                            />
                                            <div className="d-flex justify-content-between align-items-center mt-1">
                                                <small className={`${formData.description.length >= MAX_DESCRIPTION_LENGTH ? 'text-danger' : 'text-muted'}`}>
                                                    {formData.description.length}/{MAX_DESCRIPTION_LENGTH} characters
                                                </small>
                                                {formErrors.description && (
                                                    <small className="text-danger">{formErrors.description}</small>
                                                )}
                                            </div>
                                        </div>

                                        <div className="mb-3">
                                            <label className="form-label fw-medium">
                                                Schedule Date and Time <span className="text-danger">*</span>
                                            </label>
                                            <input
                                                type="datetime-local"
                                                className={`form-control ${formErrors.scheduleDate ? 'is-invalid' : ''}`}
                                                name="scheduleDate"
                                                value={formData.scheduleDate}
                                                onChange={handleInputChange}
                                                disabled={editMode && new Date(selectedAnnouncement?.scheduleDateTime) < new Date()}
                                            />
                                            <small className="text-muted d-block mt-1">
                                                Note: Schedule date cannot be changed once the announcement time has passed.
                                            </small>
                                            {formErrors.scheduleDate && (
                                                <small className="text-danger d-block mt-1">{formErrors.scheduleDate}</small>
                                            )}
                                        </div>

                                        {/* Time Zone Dropdown */}
                                        <div className="mb-3">
                                            <label className="form-label fw-medium">
                                                Time Zone <span className="text-danger">*</span>
                                            </label>
                                            <div className="position-relative">
                                                <div 
                                                    className={`form-select d-flex align-items-center justify-content-between ${formErrors.timeZone ? 'is-invalid' : ''}`}
                                                    onClick={() => setIsTimeZoneDropdownOpen(!isTimeZoneDropdownOpen)}
                                                    style={{
                                                        borderRadius: '8px',
                                                        border: formErrors.timeZone ? '1px solid #dc3545' : '1px solid #e0e0e0',
                                                        padding: '12px 16px',
                                                        fontSize: '0.875rem',
                                                        backgroundColor: '#fff',
                                                        cursor: 'pointer'
                                                    }}
                                                >
                                                    <span>{formData.timeZone ? timeZones.find(zone => zone.value === formData.timeZone)?.label : 'Select Time Zone'}</span>
                                                    <Icon icon={isTimeZoneDropdownOpen ? "eva:arrow-up-fill" : "eva:arrow-down-fill"} />
                                                </div>
                                                {isTimeZoneDropdownOpen && (
                                                    <div 
                                                        className="position-absolute w-100 bg-white border rounded-3 mt-1 shadow-sm" 
                                                        style={{ 
                                                            maxHeight: '300px',
                                                            overflowY: 'auto',
                                                            zIndex: 1000
                                                        }}
                                                    >
                                                        <div className="p-2 sticky-top bg-white">
                                                            <div className="position-relative">
                                                                <input
                                                                    type="text"
                                                                    className="form-control"
                                                                    placeholder="Search timezone..."
                                                                    value={timeZoneSearch}
                                                                    onChange={(e) => setTimeZoneSearch(e.target.value)}
                                                                    onClick={(e) => e.stopPropagation()}
                                                                    style={{
                                                                        paddingLeft: '35px',
                                                                        border: '1px solid #e0e0e0',
                                                                        borderRadius: '6px',
                                                                        fontSize: '0.875rem'
                                                                    }}
                                                                />
                                                                <Icon 
                                                                    icon="eva:search-outline"
                                                                    style={{
                                                                        position: 'absolute',
                                                                        left: '12px',
                                                                        top: '50%',
                                                                        transform: 'translateY(-50%)',
                                                                        color: '#6c757d'
                                                                    }}
                                                                />
                                                            </div>
                                                        </div>
                                                        <div className="timezone-options">
                                                            {timeZones
                                                                .filter(zone => 
                                                                    zone.label.toLowerCase().includes(timeZoneSearch.toLowerCase()) ||
                                                                    zone.value.toLowerCase().includes(timeZoneSearch.toLowerCase())
                                                                )
                                                                .map((zone) => (
                                                                    <div
                                                                        key={zone.value}
                                                                        className="timezone-option"
                                                                        onClick={() => {
                                                                            setFormData(prev => ({ ...prev, timeZone: zone.value }));
                                                                            setIsTimeZoneDropdownOpen(false);
                                                                            setTimeZoneSearch('');
                                                                        }}
                                                                        style={{ 
                                                                            padding: '10px 16px',
                                                                            cursor: 'pointer',
                                                                            backgroundColor: formData.timeZone === zone.value ? '#f8f9fa' : 'transparent',
                                                                            transition: 'background-color 0.2s',
                                                                            fontSize: '0.875rem',
                                                                            color: '#333'
                                                                        }}
                                                                        onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#f8f9fa'}
                                                                        onMouseLeave={(e) => e.currentTarget.style.backgroundColor = formData.timeZone === zone.value ? '#f8f9fa' : 'transparent'}
                                                                    >
                                                                        {zone.label}
                                                                    </div>
                                                                ))
                                                            }
                                                        </div>
                                                    </div>
                                                )}
                                            </div>
                                            {formErrors.timeZone && (
                                                <small className="text-danger d-block mt-1">{formErrors.timeZone}</small>
                                            )}
                                        </div>

                                        {/* add dropdown with role */}
                                        <div className="mb-3">
                                            <label className="form-label fw-medium">
                                                Select Role Where You Announce <span className="text-danger">*</span>
                                            </label>
                                            <select
                                                className="form-select"
                                                name="role"
                                                value={formData.role}
                                                onChange={handleInputChange}
                                                disabled={editMode}
                                                style={{
                                                    backgroundColor: '#f8f9fa',
                                                    border: '1px solid #ced4da',
                                                    color: '#212529',
                                                    cursor: editMode ? 'not-allowed' : 'pointer'
                                                }}
                                            >
                                                <option value="" disabled style={{ color: '#6c757d' }}>Select Role</option>
                                                {roles.map((role) => (
                                                    <option 
                                                        key={role.id} 
                                                        value={role.id}
                                                        style={{ 
                                                            color: '#212529',
                                                            backgroundColor: '#ffffff',
                                                            padding: '8px'
                                                        }}
                                                    >
                                                        {role.name}
                                                    </option>
                                                ))}
                                            </select>
                                            {formErrors.role && (
                                                <small className="text-danger d-block mt-1">{formErrors.role}</small>
                                            )}
                                            {editMode && (
                                                <small className="text-muted d-block mt-1">
                                                    Role cannot be changed after announcement is created.
                                                </small>
                                            )}
                                        </div>


                                        {/* Notification settings are now handled automatically */}

                                        <div className="modal-footer border-top px-4 pb-3">
                                            <button
                                                type="button"
                                                className="btn btn-secondary"
                                                onClick={handleCloseModal}
                                                disabled={loading}
                                            >
                                                Cancel
                                            </button>
                                            <button
                                                type="submit"
                                                className="btn btn-primary px-4"
                                                disabled={loading}
                                            >
                                                {loading ? 'Saving...' : (editMode ? 'Update Announcement' : 'Create Announcement')}
                                            </button>
                                        </div>
                                    </form>
                                )}
                            </div>
                        </div>
                    </div>
                </div>
            )}

            {/* table with SL No, title, description, publish date, status, action */}
            <div className="row mb-3">
                <div className="col-12">
                    <div className="card">
                        <div className="table-responsive">
                            <table className="table table-borderless mb-0" style={{ minWidth: '800px' }}>
                                <thead>
                                    <tr>
                                        <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500', width: '80px' }}>SL No</th>
                                        <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500', width: '20%' }}>Title</th>
                                        <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500', width: '40%' }}>Description</th>
                                        <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500', width: '200px' }}>Publish Date & Time</th>
                                        <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500', width: '100px' }}>Status</th>
                                        <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500', width: '120px' }}>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {loading ? (
                                        <tr>
                                            <td colSpan="6" className="text-center py-4">
                                                <Loader />
                                            </td>
                                        </tr>
                                    ) : announcementData.length === 0 ? (
                                        <tr>
                                            <td colSpan="6" className="text-center py-4">
                                                <NoData />
                                            </td>
                                        </tr>
                                    ) : (
                                        announcementData.map((announcement, index) => (
                                            <tr key={announcement.id} className="border-bottom">
                                                <td className="py-3 align-middle">
                                                    {(pagination.currentPage - 1) * pagination.recordsPerPage + index + 1}
                                                </td>
                                                <td className="py-3 align-middle">{announcement.title}</td>
                                                <td className="py-3 align-middle">
                                                    <span className="text-truncate" style={{ maxWidth: '300px', display: 'inline-block' }}>
                                                        {announcement.message}
                                                    </span>
                                                </td>
                                                <td className="py-3 align-middle">
                                                    {new Date(announcement.scheduleDateTime).toLocaleString('en-US', {
                                                        year: 'numeric',
                                                        month: 'short',
                                                        day: '2-digit',
                                                        hour: '2-digit',
                                                        minute: '2-digit',
                                                        hour12: true,
                                                        timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone, // Use local timezone
                                                    })}
                                                </td>
                                                <td className="py-3 align-middle">
                                                    <span className={`badge text-dark ${getStatusClass(announcement.status === 1 ? 'Published' : 'Pending')}`}>
                                                        {announcement.status === 1 ? 'Published' : 'Pending'}
                                                    </span>
                                                </td>
                                                <td className="py-3 align-middle">
                                                    <div className="d-flex gap-2">
                                                        <button
                                                            className="btn btn-outline-dark btn-sm border border-dark"
                                                            style={{ width: '36px', height: '36px' }}
                                                            title={permissions.announcement_management_edit ? "Edit Announcement" : "You don't have permission to edit"}
                                                            onClick={() => handleEdit(announcement)}
                                                            disabled={loading || !permissions.announcement_management_edit}
                                                        >
                                                            <Icon icon="fluent:edit-24-regular" width="16" height="16" />
                                                        </button>
                                                        <button
                                                            className="btn btn-outline-dark btn-sm border border-dark"
                                                            style={{ width: '36px', height: '36px' }}
                                                            title={permissions.announcement_management_delete ? "Delete Announcement" : "You don't have permission to delete"}
                                                            onClick={() => {
                                                                if (!permissions.announcement_management_delete) {
                                                                    toast.warning("You don't have permission to delete announcements");
                                                                    return;
                                                                }
                                                                setSelectedAnnouncement(announcement);
                                                                setShowDeleteModal(true);
                                                            }}
                                                            disabled={loading || !permissions.announcement_management_delete}
                                                        >
                                                            <Icon icon="fluent:delete-24-regular" width="16" height="16" />
                                                        </button>
                                                        <button
                                                            className="btn btn-outline-dark btn-sm border border-dark"
                                                            style={{ width: '36px', height: '36px' }}
                                                            title="View Announcement"
                                                            onClick={() => handleView(announcement)}
                                                            disabled={loading}
                                                        >
                                                            <Icon icon="fluent:eye-24-regular" width="16" height="16" />
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                        ))
                                    )}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            {/* record per page and pagination */}
            <div className="row">
                <div className="col-md-6">
                    <select
                        className="form-select"
                        style={{ width: 'auto' }}
                        value={pagination.recordsPerPage}
                        onChange={handleLimitChange}
                    >
                        <option value={10}>10 records per page</option>
                        <option value={25}>25 records per page</option>
                        <option value={50}>50 records per page</option>
                    </select>
                </div>
                <div className="col-md-6 d-flex justify-content-end align-items-center gap-3">
                    <button
                        className="btn btn-primary"
                        style={{ width: '150px' }}
                        onClick={() => handlePageChange(pagination.currentPage - 1)}
                        disabled={pagination.currentPage === 1 || loading}
                    >
                        Prev
                    </button>
                    <span>Page {pagination.currentPage} of {pagination.totalPages}</span>
                    <button
                        className="btn btn-primary"
                        style={{ width: '150px' }}
                        onClick={() => handlePageChange(pagination.currentPage + 1)}
                        disabled={pagination.currentPage === pagination.totalPages || loading}
                    >
                        Next
                    </button>
                </div>
            </div>

            {/* Delete Confirmation Modal */}
            {showDeleteModal && selectedAnnouncement && (
                <div className="modal show d-block" tabIndex="-1" style={{ backgroundColor: 'rgba(0,0,0,0.5)' }}>
                    <div className="modal-dialog modal-dialog-centered">
                        <div className="modal-content">
                            <div className="modal-header border-0 pb-0">
                                <h5 className="modal-title">Confirm Delete</h5>
                                <button
                                    type="button"
                                    className="btn-close"
                                    onClick={() => {
                                        setShowDeleteModal(false);
                                        setSelectedAnnouncement(null);
                                    }}
                                ></button>
                            </div>
                            <div className="modal-body pt-2">
                                {!permissions.announcement_management_delete ? (
                                    <div className="alert alert-warning">
                                        <Icon icon="mdi:warning" className="me-2" />
                                        You don't have permission to delete announcements.
                                    </div>
                                ) : (
                                    <>
                                        <p>Are you sure you want to delete the announcement "{selectedAnnouncement.title}"?</p>
                                        <p className="text-muted small">This action cannot be undone.</p>
                                    </>
                                )}
                            </div>
                            <div className="modal-footer">
                                <button
                                    type="button"
                                    className="btn btn-secondary"
                                    onClick={() => {
                                        setShowDeleteModal(false);
                                        setSelectedAnnouncement(null);
                                    }}
                                    disabled={loading}
                                >
                                    Cancel
                                </button>
                                <button
                                    type="button"
                                    className="btn btn-danger"
                                    onClick={handleDelete}
                                    disabled={loading || !permissions.announcement_management_delete}
                                >
                                    {loading ? 'Deleting...' : 'Delete'}
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            )}

            {/* View Announcement Modal */}
            {showViewModal && selectedAnnouncement && (
                <div className="modal show d-block" tabIndex="-1" style={{ backgroundColor: 'rgba(0,0,0,0.5)' }}>
                    <div className="modal-dialog modal-dialog-centered modal-lg">
                        <div className="modal-content">
                            <div className="modal-header border-0 pb-0">
                                <h5 className="modal-title">Announcement Details</h5>
                                <button
                                    type="button"
                                    className="btn-close"
                                    onClick={() => {
                                        setShowViewModal(false);
                                        setSelectedAnnouncement(null);
                                    }}
                                ></button>
                            </div>
                            <div className="modal-body pt-2">
                                <div className="row">
                                    <div className="col-12">
                                        <div className="mb-3">
                                            <label className="form-label fw-bold">Title:</label>
                                            <p className="mb-0">{selectedAnnouncement.title}</p>
                                        </div>
                                    </div>
                                    <div className="col-12">
                                        <div className="mb-3">
                                            <label className="form-label fw-bold">Message:</label>
                                            <p className="mb-0">{selectedAnnouncement.message}</p>
                                        </div>
                                    </div>
                                    <div className="col-12">
                                        <div className="mb-3">
                                                                                <label className="form-label fw-bold">Schedule Date and Time:</label>
                                    <p className="mb-0">{formatDate(selectedAnnouncement.scheduleDateTime)}</p>
                                        </div>
                                    </div>
                                    <div className="col-12">
                                        <div className="mb-3">
                                            <label className="form-label fw-bold">Status:</label>
                                            <div>
                                                <span className={`badge text-dark ${getStatusClass(selectedAnnouncement.status === 1 ? 'Published' : 'Pending')}`}>
                                                    {selectedAnnouncement.status === 1 ? 'Published' : 'Pending'}
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                    {/* <div className="col-12">
                                        <div className="mb-3">
                                            <label className="form-label fw-bold">Notification Settings:</label>
                                            <div className="d-flex gap-3">
                                                <span className={`badge ${selectedAnnouncement.send_notification === 1 ? 'bg-success' : 'bg-secondary'}`}>
                                                    Notification: {selectedAnnouncement.send_notification === 1 ? 'Enabled' : 'Disabled'}
                                                </span>
                                                <span className={`badge ${selectedAnnouncement.send_email === 1 ? 'bg-success' : 'bg-secondary'}`}>
                                                    Email: {selectedAnnouncement.send_email === 1 ? 'Enabled' : 'Disabled'}
                                                </span>
                                            </div>
                                        </div>
                                    </div> */}
                                </div>
                            </div>
                            <div className="modal-footer">
                                <button
                                    type="button"
                                    className="btn btn-secondary"
                                    onClick={() => {
                                        setShowViewModal(false);
                                        setSelectedAnnouncement(null);
                                    }}
                                >
                                    Close
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            )}
        </>
    )
}

export default Announcement
