-- MySQL dump 10.13  Distrib 8.0.33, for Win64 (x86_64)
--
-- Host: localhost    Database: lms_tpi
-- ------------------------------------------------------
-- Server version	8.0.33

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `role_permissions`
--

DROP TABLE IF EXISTS `role_permissions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `role_permissions` (
  `role_id` int NOT NULL,
  `permission_id` int NOT NULL,
  `createdAt` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updatedAt` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`role_id`,`permission_id`),
  KEY `permission_id` (`permission_id`),
  CONSTRAINT `role_permissions_ibfk_1` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE,
  CONSTRAINT `role_permissions_ibfk_2` FOREIGN KEY (`permission_id`) REFERENCES `auth`.`permissions` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `role_permissions`
--

LOCK TABLES `role_permissions` WRITE;
/*!40000 ALTER TABLE `role_permissions` DISABLE KEYS */;
INSERT INTO `role_permissions` VALUES (1,1,'2025-03-26 11:15:17','2025-03-26 11:19:55'),(1,2,'2025-03-26 11:15:17','2025-03-26 11:19:55'),(1,3,'2025-03-26 11:15:17','2025-03-26 11:19:55'),(1,4,'2025-03-26 11:15:17','2025-03-26 11:19:55'),(1,5,'2025-03-26 11:15:17','2025-03-26 11:19:55'),(1,6,'2025-03-26 11:15:17','2025-03-26 11:19:55'),(1,7,'2025-03-26 11:15:17','2025-03-26 11:19:55'),(1,8,'2025-03-26 11:15:17','2025-03-26 11:19:55'),(1,9,'2025-03-26 11:15:17','2025-03-26 11:19:55'),(1,10,'2025-03-26 11:15:17','2025-03-26 11:19:55'),(1,11,'2025-03-26 11:15:17','2025-03-26 11:19:55'),(1,12,'2025-03-26 11:15:17','2025-03-26 11:19:55'),(1,13,'2025-03-26 11:15:17','2025-03-26 11:19:55'),(1,14,'2025-03-26 11:15:17','2025-03-26 11:19:55'),(1,15,'2025-03-26 11:15:17','2025-03-26 11:19:55'),(1,16,'2025-03-26 11:15:17','2025-03-26 11:19:55'),(1,17,'2025-03-26 11:15:17','2025-03-26 11:19:55'),(1,18,'2025-03-26 11:15:17','2025-03-26 11:19:55'),(1,19,'2025-03-26 11:15:17','2025-03-26 11:19:55'),(1,20,'2025-03-26 11:15:17','2025-03-26 11:19:55'),(1,21,'2025-03-26 11:15:17','2025-03-26 11:19:55'),(1,22,'2025-03-26 11:15:17','2025-03-26 11:19:55'),(1,23,'2025-03-26 11:15:17','2025-03-26 11:19:55'),(1,24,'2025-03-26 11:15:17','2025-03-26 11:19:55'),(1,25,'2025-03-26 11:15:17','2025-03-26 11:19:55'),(1,26,'2025-03-26 11:15:17','2025-03-26 11:19:55'),(1,27,'2025-03-26 11:15:17','2025-03-26 11:19:55'),(1,28,'2025-03-26 11:15:17','2025-03-26 11:19:55'),(1,29,'2025-03-26 11:15:17','2025-03-26 11:19:55'),(1,30,'2025-03-26 11:15:17','2025-03-26 11:19:55'),(1,31,'2025-03-26 11:15:17','2025-03-26 11:19:55'),(1,32,'2025-03-26 11:15:17','2025-03-26 11:19:55'),(1,33,'2025-03-26 11:15:17','2025-03-26 11:19:55'),(1,34,'2025-03-26 11:15:17','2025-03-26 11:19:55'),(1,35,'2025-03-26 11:15:17','2025-03-26 11:19:55'),(1,36,'2025-03-26 11:15:17','2025-03-26 11:19:55'),(1,37,'2025-03-26 11:15:17','2025-03-26 11:19:55'),(1,38,'2025-03-26 11:15:17','2025-03-26 11:19:55'),(1,39,'2025-03-26 11:15:17','2025-03-26 11:19:55'),(1,41,'2025-03-26 11:15:17','2025-03-26 11:19:55'),(1,42,'2025-03-26 11:15:17','2025-03-26 11:19:55'),(1,43,'2025-03-26 11:15:17','2025-03-26 11:19:55'),(1,44,'2025-03-26 11:15:17','2025-03-26 11:19:55'),(1,45,'2025-03-26 11:15:17','2025-03-26 11:19:55'),(1,46,'2025-03-26 11:15:17','2025-03-26 11:19:55'),(1,47,'2025-03-26 11:15:17','2025-03-26 11:19:55'),(1,48,'2025-03-26 11:15:17','2025-03-26 11:19:55'),(1,49,'2025-03-26 11:15:17','2025-03-26 11:19:55'),(1,50,'2025-03-26 11:15:17','2025-03-26 11:19:55'),(1,51,'2025-03-26 11:15:17','2025-03-26 11:19:55'),(1,52,'2025-03-26 11:15:17','2025-03-26 11:19:55'),(1,53,'2025-03-26 11:15:17','2025-03-26 11:19:55'),(1,54,'2025-03-26 11:15:17','2025-03-26 11:19:55'),(1,55,'2025-03-26 11:15:17','2025-03-26 11:19:55'),(1,56,'2025-03-26 11:15:17','2025-03-26 11:19:55'),(1,57,'2025-03-26 11:15:17','2025-03-26 11:19:55'),(1,58,'2025-03-26 11:15:17','2025-03-26 11:19:55'),(1,59,'2025-03-26 11:15:17','2025-03-26 11:19:55'),(1,60,'2025-03-26 11:15:17','2025-03-26 11:19:55'),(1,61,'2025-03-26 11:15:17','2025-03-26 11:19:55'),(1,62,'2025-03-26 11:15:17','2025-03-26 11:19:55'),(1,63,'2025-03-26 11:15:17','2025-03-26 11:19:55'),(1,64,'2025-03-26 11:15:17','2025-03-26 11:19:55'),(1,65,'2025-03-26 11:15:17','2025-03-26 11:19:55'),(1,66,'2025-03-26 11:15:17','2025-03-26 11:19:55'),(1,67,'2025-03-26 11:15:17','2025-03-26 11:19:55'),(1,68,'2025-03-26 11:15:17','2025-03-26 11:19:55'),(1,69,'2025-03-26 11:15:17','2025-03-26 11:19:55'),(1,70,'2025-03-26 11:15:17','2025-03-26 11:19:55'),(1,71,'2025-03-26 11:15:17','2025-03-26 11:19:55'),(1,72,'2025-03-26 11:15:17','2025-03-26 11:19:55'),(1,73,'2025-03-26 11:15:17','2025-03-26 11:19:55'),(1,74,'2025-03-26 11:15:17','2025-03-26 11:19:55'),(1,75,'2025-03-26 11:15:17','2025-03-26 11:19:55'),(1,76,'2025-03-26 11:15:17','2025-03-26 11:19:55'),(1,77,'2025-03-26 11:15:17','2025-03-26 11:19:55'),(1,78,'2025-03-26 11:15:17','2025-03-26 11:19:55'),(1,79,'2025-03-26 11:15:17','2025-03-26 11:19:55'),(1,80,'2025-03-26 11:15:17','2025-03-26 11:19:55'),(1,81,'2025-03-26 11:15:17','2025-03-26 11:19:55'),(1,82,'2025-03-26 11:15:17','2025-03-26 11:19:55'),(1,83,'2025-03-26 11:15:17','2025-03-26 11:19:55'),(1,84,'2025-03-26 11:15:17','2025-03-26 11:19:55'),(1,85,'2025-03-26 11:15:17','2025-03-26 11:19:55'),(1,86,'2025-03-26 11:15:17','2025-03-26 11:19:55'),(1,87,'2025-03-26 11:15:17','2025-03-26 11:19:55'),(1,88,'2025-03-26 11:15:17','2025-03-26 11:19:55'),(1,89,'2025-03-26 11:15:17','2025-03-26 11:19:55'),(1,90,'2025-03-26 11:15:17','2025-03-26 11:19:55'),(1,91,'2025-03-26 11:15:17','2025-03-26 11:19:55'),(2,2,'2025-03-26 19:04:47','2025-03-26 19:04:47'),(2,28,'2025-03-26 19:04:47','2025-03-26 19:04:47'),(2,36,'2025-03-26 19:04:47','2025-03-26 19:04:47'),(2,38,'2025-03-26 19:04:47','2025-03-26 19:04:47'),(2,42,'2025-03-26 19:04:47','2025-03-26 19:04:47'),(2,56,'2025-03-26 19:04:47','2025-03-26 19:04:47'),(2,59,'2025-03-26 19:04:47','2025-03-26 19:04:47'),(2,63,'2025-03-26 19:04:47','2025-03-26 19:04:47'),(2,73,'2025-03-26 19:04:47','2025-03-26 19:04:47'),(2,80,'2025-03-26 19:04:47','2025-03-26 19:04:47'),(2,82,'2025-03-26 19:04:47','2025-03-26 19:04:47'),(2,83,'2025-03-26 19:04:47','2025-03-26 19:04:47');
/*!40000 ALTER TABLE `role_permissions` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-03-27  2:52:05
