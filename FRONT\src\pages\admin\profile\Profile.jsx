import React, { useState } from 'react';
import { Icon } from '@iconify/react';
import PersonalInformation from './PersonalInformation'
import ChangePassword from './ChangePassword'


function UserProfile() {
  const [activeTab, setActiveTab] = useState('personal');

  const tabs = [
    {
      id: 'personal',
      label: 'Personal Information',
      icon: 'fluent:person-24-regular',
      component: PersonalInformation
    },
    {
      id: 'password',
      label: 'Change Password',
      icon: 'fluent:key-24-regular',
      component: ChangePassword
    }
  ];

  return (
    <div className="container-fluid p-0">
      <div className="card border-0">
        {/* Tabs Navigation */}
        <div className="tab-header border-bottom">
          <ul className="nav nav-tabs">
            {tabs.map(tab => (
              <li className="nav-item" key={tab.id}>
                <button
                  className={`nav-link ${activeTab === tab.id ? 'active' : ''}`}
                  onClick={() => setActiveTab(tab.id)}
                >
                  <Icon icon={tab.icon} width="20" height="20" />
                  {tab.label}
                </button>
              </li>
            ))}
          </ul>
        </div>

        {/* Tab Content */}
        <div className="tab-content">
          {tabs.map(tab => (
            <div
              key={tab.id}
              className={`tab-pane ${activeTab === tab.id ? 'active' : ''}`}
            >
              <tab.component />
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

export default UserProfile;