import React, { useState, useEffect } from 'react'
import { Icon } from '@iconify/react';
import { useNavigate, useParams } from 'react-router-dom';
import { decodeData } from '../../../utils/encodeAndEncode';
import './Classroom.css';
import { getTrainees, removeTraineeFromClassroom, getAllUsersExceptClassroom, addTraineesToClassroom, deleteMultipleTrainees } from '../../../services/adminService';
import { toast } from 'react-toastify';
import { usePermissions } from '../../../context/PermissionsContext';
import Loader from '../../../components/common/Loader'
import Nodata from '../../../components/common/NoData'

function ClassroomTrainees() {
    const navigate = useNavigate();
    const { permissions } = usePermissions();
    const { classroomId } = useParams();
    const decodedClassroomId = decodeData(classroomId);
    console.log('decodedClassroomId', decodedClassroomId);

    // Main data states
    const [trainees, setTrainees] = useState([]);
    const [loading, setLoading] = useState(true);

    // Modal states
    const [showAddModal, setShowAddModal] = useState(false);
    const [showDeleteModal, setShowDeleteModal] = useState(false);
    const [traineeToDelete, setTraineeToDelete] = useState(null);
    const [showBulkDeleteModal, setShowBulkDeleteModal] = useState(false);

    // Selection states
    const [selectedTrainees, setSelectedTrainees] = useState([]);
    const [selectAll, setSelectAll] = useState(false);

    // Search and pagination states
    const [searchTerm, setSearchTerm] = useState('');
    const [currentPage, setCurrentPage] = useState(1);
    const [itemsPerPage, setItemsPerPage] = useState(10);
    const [totalPages, setTotalPages] = useState(0);
    const [totalRecords, setTotalRecords] = useState(0);

    // Add trainee modal states
    const [modalSearchTerm, setModalSearchTerm] = useState('');
    const [availableTrainees, setAvailableTrainees] = useState([]);
    const [selectedTraineeIds, setSelectedTraineeIds] = useState(new Set());
    const [modalLoading, setModalLoading] = useState(false);

    // Loading states
    const [isDeletingTrainee, setIsDeletingTrainee] = useState(false);
    const [isDeletingMultiple, setIsDeletingMultiple] = useState(false);

    // Fetch trainees data
    const fetchTrainees = async () => {
        setLoading(true);
        try {
            const payload = {
                class_id: parseInt(decodedClassroomId),
                page: currentPage,
                limit: itemsPerPage,
                search: searchTerm
            };

            console.log('Fetching trainees with payload:', payload);

            const response = await getTrainees(payload);
            console.log('Trainees response:', response);

            if (response.success) {
                setTrainees(response.data.trainees || []);

                // Set pagination data
                const pagination = response.data.pagination || {};
                setTotalPages(pagination.totalPages || 0);
                setTotalRecords(pagination.totalRecords || 0);
            } else {
                toast.error('Failed to fetch trainees data');
                setTrainees([]);
            }
        } catch (error) {
            console.error('Error fetching trainees:', error);
            toast.error('Error loading trainees data');
            setTrainees([]);
        } finally {
            // Add minimum loading time for better UX
            setTimeout(() => {
                setLoading(false);
            }, 500);
        }
    };

    // Fetch available trainees for the classroom
    const fetchAvailableTrainees = async () => {
        setModalLoading(true);
        try {
            const payload = {
                class_id: parseInt(decodedClassroomId),
                page: 1,
                limit: 1000, // Get a large number to show all available trainees
                search: modalSearchTerm
            };

            console.log('Fetching available trainees with payload:', payload);

            const response = await getAllUsersExceptClassroom(payload);
            console.log('Available trainees response:', response);

            if (response.success) {
                // Get the current set of selected IDs
                const selectedIds = selectedTraineeIds;

                // Map the trainees and mark as selected if their ID is in our selectedTraineeIds Set
                const mappedTrainees = response.data.trainees.map(trainee => ({
                    ...trainee,
                    selected: selectedIds.has(trainee.id)
                }));

                setAvailableTrainees(mappedTrainees);
                console.log('Available trainees mapped with selection preserved:', mappedTrainees);
            } else {
                console.error('Failed to fetch available trainees:', response.message);
                toast.error('Failed to load available trainees');
            }
        } catch (error) {
            console.error('Error fetching available trainees:', error);
            toast.error('Failed to load available trainees');
        } finally {
            setModalLoading(false);
        }
    };

    // Fetch data when component mounts or pagination/search changes
    useEffect(() => {
        fetchTrainees();
    }, [decodedClassroomId, currentPage, itemsPerPage, searchTerm]);

    // Effect to fetch available trainees when search term changes
    useEffect(() => {
        if (showAddModal) {
            const timer = setTimeout(() => {
                fetchAvailableTrainees();
            }, 500); // Debounce search

            return () => clearTimeout(timer);
        }
    }, [modalSearchTerm, showAddModal]);

    const handleSelectAll = (e) => {
        const newSelectAll = e.target.checked;
        setSelectAll(newSelectAll);

        if (newSelectAll) {
            // Select all trainee IDs
            const allTraineeIds = trainees.map(trainee => trainee.id);
            setSelectedTrainees(allTraineeIds);
        } else {
            // Deselect all
            setSelectedTrainees([]);
        }
    };

    const handleSelectTrainee = (traineeId) => {
        setSelectedTrainees(prev => {
            if (prev.includes(traineeId)) {
                // Remove from selection
                setSelectAll(false);
                return prev.filter(id => id !== traineeId);
            } else {
                // Add to selection
                const newSelection = [...prev, traineeId];
                // Check if all rows are now selected
                if (newSelection.length === trainees.length) {
                    setSelectAll(true);
                }
                return newSelection;
            }
        });
    };

    const handleDeleteClick = (trainee) => {
        if (!permissions.classroom_trainees_delete) {
            toast.warning("You don't have permission to delete trainees");
            return;
        }
        setTraineeToDelete(trainee);
        setShowDeleteModal(true);
    };

    const handleConfirmDelete = async () => {
        if (!traineeToDelete) return;

        setIsDeletingTrainee(true);
        try {
            const payload = {
                user_id: traineeToDelete.id,
                class_id: parseInt(decodedClassroomId)
            };

            console.log('Removing trainee with payload:', payload);

            const response = await removeTraineeFromClassroom(payload);

            if (response.success) {
                toast.success('Trainee removed successfully');
                // Refresh the trainees list to get updated data and pagination
                fetchTrainees();
            } else {
                toast.error(response.message || 'Failed to remove trainee');
            }
        } catch (error) {
            console.error('Error removing trainee:', error);
            toast.error('An error occurred while removing trainee');
        } finally {
            setIsDeletingTrainee(false);
            setShowDeleteModal(false);
            setTraineeToDelete(null);
        }
    };

    const handleBulkDeleteClick = () => {
        if (!permissions.classroom_trainees_delete) {
            toast.warning("You don't have permission to delete trainees");
            return;
        }
        if (selectedTrainees.length === 0) {
            toast.warning('Please select trainees to delete');
            return;
        }
        setShowBulkDeleteModal(true);
    };

    const handleConfirmBulkDelete = async () => {
        if (selectedTrainees.length === 0) return;

        setIsDeletingMultiple(true);
        try {
            const payload = {
                user_ids: selectedTrainees,
                class_id: parseInt(decodedClassroomId)
            };

            console.log('Removing multiple trainees with payload:', payload);

            const response = await deleteMultipleTrainees(payload);

            if (response.success) {
                toast.success(`${selectedTrainees.length} trainees removed successfully`);

                // Clear selection
                setSelectedTrainees([]);
                setSelectAll(false);

                // Refresh the trainees list to get updated data and pagination
                fetchTrainees();
            } else {
                toast.error(response.message || 'Failed to remove trainees');
            }
        } catch (error) {
            console.error('Error removing multiple trainees:', error);
            toast.error('An error occurred while removing trainees');
        } finally {
            setIsDeletingMultiple(false);
            setShowBulkDeleteModal(false);
        }
    };

    // Handle individual trainee selection in modal
    const handleSelectTraineeToAdd = (id) => {
        // Update the UI state
        const updatedTrainees = availableTrainees.map(t =>
            t.id === id ? { ...t, selected: !t.selected } : t
        );
        setAvailableTrainees(updatedTrainees);

        // Update the selected IDs set
        const newSelectedIds = new Set(selectedTraineeIds);
        const trainee = updatedTrainees.find(t => t.id === id);

        if (trainee.selected) {
            newSelectedIds.add(id);
        } else {
            newSelectedIds.delete(id);
        }

        setSelectedTraineeIds(newSelectedIds);
    };

    // Handle selecting all trainees in modal
    const handleSelectAllToAdd = () => {
        if (availableTrainees.every(t => t.selected)) {
            // If all are selected, unselect all
            const updatedTrainees = availableTrainees.map(t => ({ ...t, selected: false }));
            setAvailableTrainees(updatedTrainees);
            setSelectedTraineeIds(new Set());
        } else {
            // Otherwise, select all
            const updatedTrainees = availableTrainees.map(t => ({ ...t, selected: true }));
            setAvailableTrainees(updatedTrainees);

            // Add all trainee IDs to the selected set
            const newSelectedIds = new Set(selectedTraineeIds);
            updatedTrainees.forEach(trainee => {
                newSelectedIds.add(trainee.id);
            });
            setSelectedTraineeIds(newSelectedIds);
        }
    };

    // Handle add trainees submission
    const handleAddSelectedTrainees = async () => {
        // Convert the Set to an array
        const userIds = Array.from(selectedTraineeIds);

        // Create the payload in the requested format
        const payload = {
            user_ids: userIds,
            class_id: parseInt(decodedClassroomId)
        };

        console.log('Adding trainees with payload:', payload);

        try {
            const response = await addTraineesToClassroom(payload);

            console.log('Add trainees response:', response);

            if (response.success) {
                toast.success('Trainees added successfully');
                handlePopupClose();
                // Refresh the trainees list to show newly added trainees
                fetchTrainees();
            } else {
                toast.error(response.message || 'Failed to add trainees');
            }
        } catch (error) {
            console.error('Error adding trainees:', error);
            toast.error('An error occurred while adding trainees');
        }
    };

    // Handle popup close
    const handlePopupClose = () => {
        setShowAddModal(false);
        setModalSearchTerm('');
        setAvailableTrainees([]);
        setSelectedTraineeIds(new Set());
    };

    // Open modal and fetch available trainees
    const handleOpenAddTraineeModal = () => {
        if (!permissions.classroom_trainees_create) {
            toast.warning("You don't have permission to add trainees");
            return;
        }
        setShowAddModal(true);
        setModalSearchTerm(""); // Reset search term
        setSelectedTraineeIds(new Set()); // Reset selected trainees
        fetchAvailableTrainees();
    };

    // Handle search input change with debouncing
    const handleSearchChange = (e) => {
        setSearchTerm(e.target.value);
        setCurrentPage(1); // Reset to first page on new search
    };

    // Handle items per page change
    const handleItemsPerPageChange = (e) => {
        setItemsPerPage(parseInt(e.target.value));
        setCurrentPage(1); // Reset to first page when changing items per page
    };

    // Format date to readable format
    const formatDate = (dateString) => {
        if (!dateString) return 'N/A';
        const date = new Date(dateString);
        return date.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        });
    };

    // Filter trainees based on search (for modal)
    const filteredTrainees = availableTrainees.filter(trainee =>
        trainee.name.toLowerCase().includes(modalSearchTerm.toLowerCase()) ||
        trainee.email.toLowerCase().includes(modalSearchTerm.toLowerCase())
    );

    return (
        <>
            {/* Search and Add Trainee button  */}
            <div className="row d-flex mb-3">
                <div className="col-md-6 mt-2 d-flex justify-content-start align-items-center">
                    <input
                        type="search"
                        className="form-control"
                        placeholder="Search trainees..."
                        value={searchTerm}
                        onChange={handleSearchChange}
                        style={{ maxWidth: '300px' }}
                    />
                </div>
                <div className="col-md-6 text-end mt-2 d-flex justify-content-end align-items-center gap-2">
                    {selectedTrainees.length > 0 && (
                        <button
                            className="btn btn-danger text-nowrap"
                            onClick={handleBulkDeleteClick}
                            disabled={!permissions.classroom_trainees_delete}
                            title={!permissions.classroom_trainees_delete ? "You don't have permission to delete trainees" : "Delete selected trainees"}
                        >
                            Delete Selected ({selectedTrainees.length})
                        </button>
                    )}
                    <button
                        className="btn btn-primary text-nowrap d-flex align-items-center gap-2"
                        style={{ width: '150px' }}
                        onClick={handleOpenAddTraineeModal}
                        disabled={!permissions.classroom_trainees_create}
                        title={!permissions.classroom_trainees_create ? "You don't have permission to add trainees" : "Add trainees to classroom"}
                    >
                        <Icon icon="fluent:people-add-24-regular" width="20" height="20" />
                        Add Trainees
                    </button>
                </div>
            </div>

            {/* Table  */}
            <div className="row mb-3">
                <div className="col-12">
                    <div className="card">
                        <div className="table-responsive">
                            <table className="table table-borderless mb-0">
                                <thead>
                                    <tr>
                                        <th style={{ backgroundColor: '#f8f9fa', width: '40px' }}>
                                            <div className="form-check d-flex justify-content-center">
                                                <input
                                                    type="checkbox"
                                                    className="form-check-input"
                                                    checked={selectAll}
                                                    onChange={handleSelectAll}
                                                />
                                            </div>
                                        </th>
                                        <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500', width: '80px' }}>SL No</th>
                                        <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500' }}>Trainee</th>
                                        <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500' }}>Email</th>
                                        <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500' }}>Mobile</th>
                                        <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500' }}>Joined Date</th>
                                        <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500', width: '100px' }}>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {loading ? (
                                        <tr>
                                            <td colSpan="7" style={{ height: '400px' }}>
                                                <Loader caption="Loading trainees..." />
                                            </td>
                                        </tr>
                                    ) : trainees.length > 0 ? (
                                        trainees.map((trainee, index) => (
                                            <tr key={trainee.id} className="border-bottom">
                                                <td className="py-3 align-middle">
                                                    <div className="form-check d-flex justify-content-center">
                                                        <input
                                                            type="checkbox"
                                                            className="form-check-input"
                                                            checked={selectedTrainees.includes(trainee.id)}
                                                            onChange={() => handleSelectTrainee(trainee.id)}
                                                        />
                                                    </div>
                                                </td>
                                                <td className="py-3 align-middle">{(currentPage - 1) * itemsPerPage + index + 1}</td>
                                                <td className="py-3 align-middle">
                                                    <div className="d-flex align-items-center">
                                                        {trainee.profile_pic_url ? (
                                                            <img
                                                                src={trainee.profile_pic_url}
                                                                alt={trainee.name}
                                                                className="rounded-circle me-2"
                                                                style={{ width: '24px', height: '24px', objectFit: 'cover' }}
                                                                onError={(e) => {
                                                                    e.target.style.display = 'none';
                                                                    e.target.nextSibling.style.display = 'inline';
                                                                }}
                                                            />
                                                        ) : null}
                                                        <Icon icon="fluent:person-24-regular" className="me-2" width="24" height="24" style={{ display: trainee.profile_pic_url ? 'none' : 'inline' }} />
                                                        {trainee.name || 'N/A'}
                                                    </div>
                                                </td>
                                                <td className="py-3 align-middle">{trainee.email || '-'}</td>
                                                <td className="py-3 align-middle">{trainee.mobile || '-'}</td>
                                                <td className="py-3 align-middle">
                                                    {formatDate(trainee.joined_at)}
                                                </td>
                                                <td className="py-3 align-middle">
                                                    <div className="d-flex gap-2">
                                                        <button
                                                            className="btn btn-outline-dark btn-sm border border-dark"
                                                            style={{ width: '36px', height: '36px' }}
                                                            title={!permissions.classroom_trainees_delete ? "You don't have permission to delete trainees" : "Remove Trainee"}
                                                            onClick={() => handleDeleteClick(trainee)}
                                                            disabled={!permissions.classroom_trainees_delete}
                                                        >
                                                            <Icon icon="fluent:delete-24-regular" width="16" height="16" />
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                        ))
                                    ) : (
                                        <tr>
                                            <td colSpan="7" style={{ height: '400px' }}>
                                                <Nodata caption="No trainees found in this classroom" />
                                            </td>
                                        </tr>
                                    )}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            {/* Record per page and pagination  */}
            <div className="row">
                <div className="col-md-6">
                    <div className="d-flex align-items-center gap-2">
                        <select
                            className="form-select"
                            style={{ width: 'auto' }}
                            value={itemsPerPage}
                            onChange={handleItemsPerPageChange}
                        >
                            <option value={5}>5 records per page</option>
                            <option value={10}>10 records per page</option>
                            <option value={25}>25 records per page</option>
                            <option value={50}>50 records per page</option>
                        </select>
                        <span className="text-muted">
                            Total: {totalRecords}
                        </span>
                    </div>
                </div>
                <div className="col-md-6 d-flex justify-content-end align-items-center gap-3">
                    <button
                        className="btn btn-primary"
                        style={{ width: '150px' }}
                        onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                        disabled={currentPage === 1}
                    >
                        Prev
                    </button>
                    <span>Page {currentPage} of {totalPages}</span>
                    <button
                        className="btn btn-primary"
                        style={{ width: '150px' }}
                        onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                        disabled={currentPage === totalPages || totalPages === 0}
                    >
                        Next
                    </button>
                </div>
            </div>

            {/* Single Delete Confirmation Modal */}
            {showDeleteModal && traineeToDelete && (
                <div className="modal show d-block" style={{ backgroundColor: 'rgba(0,0,0,0.5)' }}>
                    <div className="modal-dialog modal-dialog-centered">
                        <div className="modal-content">
                            <div className="modal-header">
                                <h5 className="modal-title">Confirm Delete</h5>
                                <button
                                    type="button"
                                    className="btn-close"
                                    onClick={() => {
                                        setShowDeleteModal(false);
                                        setTraineeToDelete(null);
                                    }}
                                ></button>
                            </div>
                            <div className="modal-body">
                                <p>Are you sure you want to remove this trainee?</p>
                                <div className="alert alert-warning">
                                    <div className="d-flex align-items-center mb-2">
                                        <Icon icon="fluent:person-24-regular" className="me-2" width="24" height="24" />
                                        <strong>{traineeToDelete.name}</strong>
                                    </div>
                                    <div className="text-muted">{traineeToDelete.email}</div>
                                </div>
                            </div>
                            <div className="modal-footer">
                                <button
                                    type="button"
                                    className="btn btn-secondary"
                                    onClick={() => {
                                        setShowDeleteModal(false);
                                        setTraineeToDelete(null);
                                    }}
                                >
                                    Cancel
                                </button>
                                <button
                                    type="button"
                                    className="btn btn-danger"
                                    onClick={handleConfirmDelete}
                                    disabled={isDeletingTrainee}
                                >
                                    {isDeletingTrainee ? (
                                        <>
                                            <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                                            Removing...
                                        </>
                                    ) : 'Remove Trainee'}
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            )}

            {/* Bulk Delete Confirmation Modal */}
            {showBulkDeleteModal && selectedTrainees.length > 0 && (
                <div className="modal show d-block" style={{ backgroundColor: 'rgba(0,0,0,0.5)' }}>
                    <div className="modal-dialog modal-dialog-centered">
                        <div className="modal-content">
                            <div className="modal-header">
                                <h5 className="modal-title">Confirm Bulk Delete</h5>
                                <button
                                    type="button"
                                    className="btn-close"
                                    onClick={() => setShowBulkDeleteModal(false)}
                                ></button>
                            </div>
                            <div className="modal-body">
                                <p>Are you sure you want to remove {selectedTrainees.length} selected trainees?</p>
                                <div className="alert alert-warning">
                                    <Icon icon="fluent:warning-24-regular" className="me-2" width="24" height="24" />
                                    This action cannot be undone.
                                </div>
                            </div>
                            <div className="modal-footer">
                                <button
                                    type="button"
                                    className="btn btn-secondary"
                                    onClick={() => setShowBulkDeleteModal(false)}
                                >
                                    Cancel
                                </button>
                                <button
                                    type="button"
                                    className="btn btn-danger"
                                    onClick={handleConfirmBulkDelete}
                                    disabled={isDeletingMultiple}
                                >
                                    {isDeletingMultiple ? (
                                        <>
                                            <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                                            Removing {selectedTrainees.length} trainees...
                                        </>
                                    ) : `Remove ${selectedTrainees.length} Trainees`}
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            )}

            {/* Add Trainee Modal */}
            {showAddModal && (
                <div className="modal show d-block" style={{ backgroundColor: 'rgba(0,0,0,0.5)' }}>
                    <div className="modal-dialog modal-dialog-centered">
                        <div className="modal-content">
                            <div className="modal-header border-bottom-0">
                                <h5 className="modal-title">Add Trainees</h5>
                                <button
                                    type="button"
                                    className="btn-close"
                                    onClick={handlePopupClose}
                                ></button>
                            </div>
                            <div className="modal-body pt-0">
                                <input
                                    type="search"
                                    className="form-control mb-3"
                                    placeholder="Search trainees..."
                                    value={modalSearchTerm}
                                    onChange={(e) => setModalSearchTerm(e.target.value)}
                                    disabled={modalLoading}
                                />
                                
                                <div className="d-flex justify-content-between align-items-center mb-2">
                                    <div className="form-check">
                                        <input
                                            type="checkbox"
                                            className="form-check-input"
                                            checked={availableTrainees.length > 0 && availableTrainees.every(t => t.selected)}
                                            onChange={handleSelectAllToAdd}
                                        />
                                        <label className="form-check-label">Select All</label>
                                    </div>
                                    <small className="text-muted">
                                        {selectedTraineeIds.size} trainee(s) selected
                                    </small>
                                </div>

                                <div className="trainee-list" style={{ maxHeight: '300px', overflowY: 'auto', border: '1px solid #dee2e6', borderRadius: '0.375rem', padding: '0.5rem' }}>
                                    {modalLoading ? (
                                        <div style={{ height: '200px' }}>
                                            <Loader caption="Loading available trainees..." />
                                        </div>
                                    ) : filteredTrainees.length > 0 ? (
                                        filteredTrainees.map(trainee => (
                                            <div
                                                key={trainee.id}
                                                className="d-flex align-items-center p-2 mb-2"
                                                style={{
                                                    border: '1px solid #dee2e6',
                                                    borderRadius: '8px',
                                                    backgroundColor: trainee.selected ? '#f0f7ff' : '#f8f9fa',
                                                    cursor: 'pointer'
                                                }}
                                                onClick={() => handleSelectTraineeToAdd(trainee.id)}
                                            >
                                                <div className="form-check me-3">
                                                    <input
                                                        type="checkbox"
                                                        className="form-check-input"
                                                        checked={trainee.selected}
                                                        onChange={() => {}} // Handled by the div click
                                                    />
                                                </div>
                                                <div className="d-flex align-items-center">
                                                    {trainee.profile_pic_url ? (
                                                        <img
                                                            src={trainee.profile_pic_url}
                                                            alt={trainee.name}
                                                            className="rounded-circle me-2"
                                                            style={{ width: '40px', height: '40px', objectFit: 'cover' }}
                                                            onError={(e) => {
                                                                e.target.style.display = 'none';
                                                                e.target.nextSibling.style.display = 'flex';
                                                            }}
                                                        />
                                                    ) : null}
                                                    <div
                                                        className="rounded-circle me-2 d-flex align-items-center justify-content-center"
                                                        style={{
                                                            width: '40px',
                                                            height: '40px',
                                                            backgroundColor: '#f8f9fa',
                                                            color: '#6c757d',
                                                            display: trainee.profile_pic_url ? 'none' : 'flex'
                                                        }}
                                                    >
                                                        <Icon icon="fluent:person-24-regular" width="24" height="24" />
                                                    </div>
                                                    <div>
                                                        <div className="fw-medium">{trainee.name}</div>
                                                        <div className="text-muted small">{trainee.email}</div>
                                                    </div>
                                                </div>
                                            </div>
                                        ))
                                    ) : (
                                        <div style={{ height: '200px' }}>
                                            <Nodata caption="No trainees available to add" />
                                        </div>
                                    )}
                                </div>
                            </div>
                            <div className="modal-footer">
                                <button
                                    type="button"
                                    className="btn btn-secondary"
                                    onClick={handlePopupClose}
                                >
                                    Cancel
                                </button>
                                <button
                                    type="button"
                                    className="btn btn-primary"
                                    onClick={handleAddSelectedTrainees}
                                    disabled={selectedTraineeIds.size === 0}
                                >
                                    Add Selected Trainees
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            )}
        </>
    )
}

export default ClassroomTrainees

