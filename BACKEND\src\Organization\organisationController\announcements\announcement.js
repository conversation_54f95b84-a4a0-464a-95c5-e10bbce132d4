const { mysqlServerConnection } = require("../../../db/db");
const { deleteFileIfExists } = require("../../../tools/tools");
const { SetNotification } = require("../../../utils/sendNotificaiton");
const { sentEmailNotification } = require("../../../tools/tools");

const getAnnouncements = async (req, res, next) => {
  try {
    const search = req.query.search ? req.query.search.trim() : "";
    const status = req.query.status;
    const db = req.user.db_name;

    console.log("STATUS FILTER", status);

    // Build dynamic query for announcements with role join
    let baseQuery = `
        SELECT 
          a.id, 
          a.createdBy, 
          a.title, 
          a.message, 
          a.createdAt, 
          a.updatedAt, 
          DATE_FORMAT(a.scheduleDateTime, '%Y-%m-%dT%H:%i:%sZ') AS scheduleDateTime, 
          a.status, 
          a.is_delete, 
          a.send_notification, 
          a.send_email,
          a.roles,
          a.time_zone,
          r.name as role_name
        FROM ${db}.announcements a
        LEFT JOIN ${db}.roles r ON a.roles = r.id
        WHERE a.is_delete = 0
      `;

    const params = [];

    if (search) {
      baseQuery += ` AND (a.title LIKE ? OR a.message LIKE ?)`;
      params.push(`%${search}%`, `%${search}%`);
    }

    if (status === "true" || status === "false") {
      baseQuery += ` AND a.status = ?`;
      params.push(status === "true" ? 1 : 0);
    }

    baseQuery += ` ORDER BY a.updatedAt DESC`;

    // Fetch all announcements (no pagination)
    const [announcements] = await mysqlServerConnection.query(
      baseQuery,
      params
    );

    return res.status(200).json({
      success: true,
      data: announcements,
      message: "All announcements fetched successfully",
    });
  } catch (error) {
    console.error("An error occurred while fetching announcements:", error);
    return next({
      statusCode: 500,
      message: error.message || "Internal Server Error",
    });
  }
};

const createAnnouncement = async (req, res, next) => {
  try {
    let {
      createdBy,
      title,
      message,
      scheduleDateTime,
      roles,
      send_notification,
      send_email,
      time_zone
    } = req.body;

    roles = roles || 1;

    console.log("📥 [API] createAnnouncement called");
    console.log("🧾 Payload:", {
      createdBy,
      title,
      message,
      scheduleDateTime,
      roles,
      send_notification,
      send_email,
      time_zone
    });

    // Validate required fields
    if (!createdBy || !title || !message || !scheduleDateTime) {
      console.log("❌ Missing required fields");
      return res.status(400).json({
        success: false,
        message:
          "createdBy, title, message, and scheduleDateTime and time_zone fields are required.",
      });
    }

    const createdAt = new Date();
    const updatedAt = createdAt;

    // Parse the UTC scheduleDateTime (already in UTC from frontend)
    const scheduledTime = new Date(scheduleDateTime);
    const currentTime = new Date();

    // Convert UTC datetime to MySQL-compatible format (YYYY-MM-DD HH:mm:ss)
    const mysqlDateTime = scheduledTime.toISOString().slice(0, 19).replace('T', ' ');

    // Check if scheduled time is in the past or within 1 minute
    const initialStatus = scheduledTime <= currentTime || (scheduledTime - currentTime) <= 60000 ? 1 : 0;

    console.log("📅 Schedule DateTime (UTC):", scheduleDateTime);
    console.log("📅 MySQL DateTime Format:", mysqlDateTime);
    console.log("🕒 Current Time (UTC):", currentTime);
    console.log("📌 Initial Status (0=pending, 1=published):", initialStatus);

    const [result] = await mysqlServerConnection.query(
      `
        INSERT INTO ${req.user.db_name}.announcements 
        (createdBy, title, message, createdAt, updatedAt, scheduleDateTime, roles, send_notification, send_email, status, time_zone)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `,
      [
        req.user.userId,
        title,
        message,
        createdAt,
        updatedAt,
        mysqlDateTime, // Use MySQL-compatible format
        roles,
        send_notification ? 1 : 0,
        send_email ? 1 : 0,
        initialStatus,
        time_zone
      ]
    );

    if (result.affectedRows === 0) {
      console.error("❌ Failed to insert announcement into DB");
      return res.status(500).json({
        success: false,
        message: "Failed to create the announcement.",
      });
    }

    console.log("✅ Announcement inserted. ID:", result.insertId);

    // === SEND NOTIFICATIONS ===
    if (send_notification) {
      try {
        console.log("🔔 Processing notification sending logic...");
        
        // Check if should send immediately (past time or within 1 minute)
        if (scheduledTime <= currentTime || (scheduledTime - currentTime) <= 60000) {
          console.log("📨 Sending notifications immediately");

          const [users] = await mysqlServerConnection.query(
            `
              SELECT u.id
              FROM ${req.user.db_name}.users u
              JOIN ${req.user.db_name}.user_roles ur ON u.id = ur.user_id
              WHERE ur.role_id = ?
            `,
            [roles]
          );

          console.log(`👥 Found ${users.length} users for role ID ${roles}`);

          for (const user of users) {
            await SetNotification({
              dbName: req.user.db_name,
              userId: user.id,
              sendFrom: req.user.userId,
              scheduleDateTime: scheduleDateTime,
              title: title,
              body: message,
            });
          }

          console.log("✅ Notifications sent");
        } else {
          // Schedule for later
          const delay = scheduledTime - currentTime;
          console.log(`⏳ Scheduling notifications in ${delay} ms`);

          setTimeout(async () => {
            try {
              console.log("🚀 Time reached, sending scheduled notifications");
              const [users] = await mysqlServerConnection.query(
                `
                  SELECT u.id
                  FROM ${req.user.db_name}.users u
                  JOIN ${req.user.db_name}.user_roles ur ON u.id = ur.user_id
                  WHERE ur.role_id = ?
                `,
                [roles]
              );

              for (const user of users) {
                await SetNotification({
                  dbName: req.user.db_name,
                  userId: user.id,
                  sendFrom: req.user.userId,
                  scheduleDateTime: scheduleDateTime,
                  title: title,
                  body: message,
                });
              }

              // Update status to published
              await mysqlServerConnection.query(
                `UPDATE ${req.user.db_name}.announcements SET status = 1 WHERE id = ? AND status = 0`,
                [result.insertId]
              );

              console.log("✅ Scheduled notifications sent and status updated");
            } catch (error) {
              console.error("❌ Error sending scheduled notifications:", error);
            }
          }, delay);
        }
      } catch (error) {
        console.error("❌ Notification logic error:", error);
      }
    }

    // === SEND EMAILS ===
    if (send_email) {
      try {
        console.log("📧 Processing email sending logic...");
        
        // Check if should send immediately (past time or within 1 minute)
        if (scheduledTime <= currentTime || (scheduledTime - currentTime) <= 60000) {
          console.log("📨 Sending emails immediately");

          const [users] = await mysqlServerConnection.query(
            `
              SELECT u.id, u.email, u.name
              FROM ${req.user.db_name}.users u
              JOIN ${req.user.db_name}.user_roles ur ON u.id = ur.user_id
              WHERE ur.role_id = ?
            `,
            [roles]
          );

          for (const user of users) {
            if (user.email) {
              console.log(`📧 Sending email to ${user.email}`);
              sentEmailNotification(user.email, title, title, message);
            }
          }

          console.log("✅ Emails sent");
        } else {
          // Schedule for later
          const delay = scheduledTime - currentTime;
          console.log(`⏳ Scheduling emails in ${delay} ms`);

          setTimeout(async () => {
            try {
              console.log("🚀 Time reached, sending scheduled emails");
              const [users] = await mysqlServerConnection.query(
                `
                  SELECT u.id, u.email, u.name
                  FROM ${req.user.db_name}.users u
                  JOIN ${req.user.db_name}.user_roles ur ON u.id = ur.user_id
                  WHERE ur.role_id = ?
                `,
                [roles]
              );

              for (const user of users) {
                if (user.email) {
                  console.log(`📧 Sending scheduled email to ${user.email}`);
                  sentEmailNotification(user.email, title, title, message);
                }
              }

              console.log("✅ Scheduled emails sent");
            } catch (error) {
              console.error("❌ Error sending scheduled emails:", error);
            }
          }, delay);
        }
      } catch (error) {
        console.error("❌ Email logic error:", error);
      }
    }

    res.status(201).json({
      success: true,
      message: "Announcement created successfully.",
      data: {
        id: result.insertId,
        createdBy,
        title,
        message,
        createdAt,
        updatedAt,
        scheduleDateTime: mysqlDateTime, // Return MySQL format
        status: initialStatus,
        is_delete: false,
        send_notification: send_notification || false,
        send_email: send_email || false,
        time_zone
      },
    });

    console.log("✅ API createAnnouncement completed successfully");
  } catch (error) {
    console.error("🔥 Internal Server Error:", error);
    return next({
      statusCode: 500,
      message: error.message || "Internal Server Error",
    });
  }
};

const getAnnouncementsRoles = async (req, res, next) => {
  try {
    const dbName = req.user.db_name;

    // Fetch all roles
    const [roles] = await mysqlServerConnection.query(
      `SELECT id, name FROM ${dbName}.roles`
    );

    console.log(roles);
    res.status(200).json({
      success: true,
      data: roles,
    });
  } catch (error) {
    console.log(error);
    return next({
      statusCode: 500,
      message: error.message || "Internal Server Error",
    });
  }
};

const updateAnnouncement = async (req, res, next) => {
  try {
    const { id } = req.params;
    const {
      title,
      message,
      scheduleDateTime,
      role,
      send_notification,
      send_email,
    } = req.body;

    console.log("Update Announcement", {
      id,
      title,
      message,
      scheduleDateTime,
      role,
      send_notification,
      send_email
    });

    if (!id || !title || !message || !scheduleDateTime) {
      return res.status(400).json({
        success: false,
        message: "id, title, message, and scheduleDateTime fields are required.",
      });
    }

    const updatedAt = new Date();

    const scheduledTime = new Date(scheduleDateTime); // already in UTC
    const mysqlDateTime = scheduledTime.toISOString().slice(0, 19).replace('T', ' '); // ✅ Convert to MySQL DATETIME format

    const currentTime = new Date();
    const updateStatus = scheduledTime <= currentTime || (scheduledTime - currentTime) <= 60000 ? 1 : 0;

    console.log("Update status set to:", updateStatus, "(0=pending, 1=published)");

    const [result] = await mysqlServerConnection.query(
      `
        UPDATE ${req.user.db_name}.announcements
        SET title = ?, message = ?, updatedAt = ?, scheduleDateTime = ?, roles = ?, send_notification = ?, send_email = ?, status = ?
        WHERE id = ?
      `,
      [
        title,
        message,
        updatedAt,
        mysqlDateTime, // ✅ Proper UTC string saved
        role,
        send_notification ? 1 : 0,
        send_email ? 1 : 0,
        updateStatus,
        id,
      ]
    );

    if (result.affectedRows === 0) {
      return res.status(404).json({
        success: false,
        message: "Announcement not found or no changes made.",
      });
    }

    // Notification and Email logic (unchanged) – using `scheduledTime` for delay calculations
    if (send_notification) {
      try {
        if (scheduledTime <= currentTime || (scheduledTime - currentTime) <= 60000) {
          console.log("Sending update notifications immediately");
          const [users] = await mysqlServerConnection.query(
            `
              SELECT u.id
              FROM ${req.user.db_name}.users u
              JOIN ${req.user.db_name}.user_roles ur ON u.id = ur.user_id
              WHERE ur.role_id = ?
            `,
            [role]
          );
          for (const user of users) {
            await SetNotification({
              dbName: req.user.db_name,
              userId: user.id,
              sendFrom: req.user.userId,
              scheduleDateTime: scheduleDateTime,
              title: `Updated: ${title}`,
              body: message,
            });
          }
          console.log(`Update notifications sent immediately to ${users.length} users`);
        } else {
          const delay = scheduledTime - currentTime;
          console.log(`Scheduling update notifications for ${delay}ms from now`);
          setTimeout(async () => {
            try {
              console.log("Sending scheduled update notifications now");
              const [users] = await mysqlServerConnection.query(
                `
                  SELECT u.id
                  FROM ${req.user.db_name}.users u
                  JOIN ${req.user.db_name}.user_roles ur ON u.id = ur.user_id
                  WHERE ur.role_id = ?
                `,
                [role]
              );
              for (const user of users) {
                await SetNotification({
                  dbName: req.user.db_name,
                  userId: user.id,
                  sendFrom: req.user.userId,
                  scheduleDateTime: scheduleDateTime,
                  title: `Updated: ${title}`,
                  body: message,
                });
              }
              console.log(`Scheduled update notifications sent to ${users.length} users`);
            } catch (error) {
              console.error("Error sending scheduled update notifications:", error);
            }
          }, delay);
        }
      } catch (error) {
        console.error("Error handling update notifications:", error);
      }
    }

    if (send_email) {
      try {
        if (scheduledTime <= currentTime || (scheduledTime - currentTime) <= 60000) {
          console.log("Sending update emails immediately");
          const [users] = await mysqlServerConnection.query(
            `
              SELECT u.id, u.email, u.name
              FROM ${req.user.db_name}.users u
              JOIN ${req.user.db_name}.user_roles ur ON u.id = ur.user_id
              WHERE ur.role_id = ?
            `,
            [role]
          );
          for (const user of users) {
            if (user.email) {
              sentEmailNotification(
                user.email,
                `Updated: ${title}`,
                `Updated: ${title}`,
                message
              );
            }
          }
          console.log(`Update emails sent immediately to ${users.length} users`);
        } else {
          const delay = scheduledTime - currentTime;
          console.log(`Scheduling update emails for ${delay}ms from now`);
          setTimeout(async () => {
            try {
              console.log("Sending scheduled update emails now");
              const [users] = await mysqlServerConnection.query(
                `
                  SELECT u.id, u.email, u.name
                  FROM ${req.user.db_name}.users u
                  JOIN ${req.user.db_name}.user_roles ur ON u.id = ur.user_id
                  WHERE ur.role_id = ?
                `,
                [role]
              );
              for (const user of users) {
                if (user.email) {
                  sentEmailNotification(
                    user.email,
                    `Updated: ${title}`,
                    `Updated: ${title}`,
                    message
                  );
                }
              }
              console.log(`Scheduled update emails sent to ${users.length} users`);
            } catch (error) {
              console.error("Error sending scheduled update emails:", error);
            }
          }, delay);
        }
      } catch (error) {
        console.error("Error handling update emails:", error);
      }
    }

    console.log("Successfully updated the announcement");

    res.status(200).json({
      success: true,
      message: "Announcement updated successfully.",
      data: {
        id,
        title,
        message,
        updatedAt,
        scheduleDateTime: mysqlDateTime,
        status: updateStatus,
        send_notification: send_notification || false,
        send_email: send_email || false,
      },
    });
  } catch (error) {
    console.log("An error occurred while updating the announcement", error);
    return next({
      statusCode: 500,
      message: error.message || "Internal Server Error",
    });
  }
};


const deleteAnnouncement = async (req, res, next) => {
  try {
    const { id } = req.params;
    console.log("Delete Announcement", id);
    
    // Validate required fields
    if (!id) {
      return res.status(400).json({
        success: false,
        message: "id field is required.",
      });
    }

    // Delete announcement from the database
    const [result] = await mysqlServerConnection.query(
      `
        UPDATE ${req.user.db_name}.announcements
        SET is_delete = 1
        WHERE id = ?
      `,
      [id]
    );

    // Check if the announcement was deleted successfully
    if (result.affectedRows === 0) {
      return res.status(404).json({
        success: false,
        message: "Announcement not found or is already deleted.",
      });
    }

    console.log("Successfully deleted the announcement");

    res.status(200).json({
      success: true,
      message: "Announcement deleted successfully.",
    });
  } catch (error) {
    return next({
      statusCode: 500,
      message: error.message || "Internal Server Error",
    });
  }
};

module.exports = {
  getAnnouncements,
  createAnnouncement,
  updateAnnouncement,
  deleteAnnouncement,
  getAnnouncementsRoles,
};
