import React, { useEffect, useRef } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { Icon } from '@iconify/react';
import './Resultvideo.css';
import { saveRecentVideo } from '../../../../services/userService';

const bitmovinKey = process.env.REACT_APP_BITMOVIN_PLAYER_KEY;

function Resultvideo() {
  const location = useLocation();
  const navigate = useNavigate();
  const { videoId, timestamp, videoUrl } = location.state || {};
  const playerRef = useRef(null);
  const playerContainerRef = useRef(null);

  const handleBack = () => {
    navigate(-1);
  };

  const jumpToTimestamp = () => {
    if (playerRef.current && timestamp) {
      const [minutes, seconds] = timestamp.split(':').map(Number);
      const timeInSeconds = minutes * 60 + seconds;
      playerRef.current.seek(timeInSeconds);

      // ✅ Play video on user interaction
      const playPromise = playerRef.current.play();
      if (playPromise && typeof playPromise.then === 'function') {
        playPromise.catch((err) =>
          console.warn('Autoplay blocked by browser, user must press play:', err)
        );
      }
    }
  };

  useEffect(() => {
    const script = document.createElement('script');
    script.src = 'https://cdn.bitmovin.com/player/web/8/bitmovinplayer.js';
    script.async = true;
    script.onload = initPlayer;
    document.body.appendChild(script);

    const link = document.createElement('link');
    link.href = 'https://cdn.bitmovin.com/player/web/8/bitmovinplayer-ui.css';
    link.rel = 'stylesheet';
    document.head.appendChild(link);

    return () => {
      if (playerRef.current) {
        playerRef.current.destroy();
      }
      if (script.parentNode) document.body.removeChild(script);
      if (link.parentNode) document.head.removeChild(link);
    };
  }, []);

  const initPlayer = () => {
    if (!window.bitmovin || !window.bitmovin.player) {
      console.error('Bitmovin player not loaded');
      return;
    }

    const playerConfig = {
      key: bitmovinKey,
      playback: {
        autoplay: false, // ❌ Don't autoplay
        muted: true       // ✅ Allow muted autoplay if needed
      },
      ui: {
        playbackSpeedSelection: true,
        watermark: false,
        customColors: {
          timeline: '#3152e8',
          buttons: '#3152e8'
        }
      }
    };

    const source = {
      progressive: videoUrl
    };

    try {
      const container = playerContainerRef.current;
      const player = new window.bitmovin.player.Player(container, playerConfig);
      playerRef.current = player;

      player.load(source).then(() => {
        console.log('Player loaded successfully');
        // ❌ Don't call .play() or jump here
      }).catch((error) => {
        console.error('Error loading player:', error);
      });

    } catch (error) {
      console.error('Error initializing player:', error);
    }
  };

  if (!videoUrl) {
    return (
      <div className="result-video-container">
        <div className="error-message">
          <p>Video URL not found</p>
          <button onClick={handleBack} className="back-btn">
            <Icon icon="mdi:arrow-left" /> Go Back
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="result-video-container">
      <div className="video-header">
        <button onClick={handleBack} className="back-btn">
          <Icon icon="mdi:arrow-left" /> Back to Results
        </button>
        <div className="video-controls">
          <button onClick={jumpToTimestamp} className="replay-btn">
            <Icon icon="mdi:replay" /> Replay from {timestamp}
          </button>
          <span className="timestamp-display">Timestamp: {timestamp}</span>
        </div>
      </div>

      <div className="video-wrapper">
        <div ref={playerContainerRef} className="bitmovin-player"></div>
      </div>
    </div>
  );
}

export default Resultvideo;
