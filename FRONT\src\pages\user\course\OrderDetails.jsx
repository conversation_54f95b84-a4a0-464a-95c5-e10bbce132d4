import React, { useState, useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import { loadStripe } from '@stripe/stripe-js';
import { processPayment } from '../../../services/userService';
import NoData from '../../../components/common/NoData';

const stripePromise = loadStripe(process.env.REACT_APP_STRIPE_PUBLIC_KEY);

function OrderDetails() {
  const location = useLocation();
  const navigate = useNavigate();
  const courseData = location.state;

  const [isCheckoutLoading, setIsCheckoutLoading] = useState(false);

  useEffect(() => {
    // Validate and log the received data
    console.log('OrderDetails - Received State:', location.state);
    
    if (!location.state || !location.state.course_id) {
      console.error('No course data or course ID found');
      // Optionally redirect back or show error
      // navigate(-1);
    }

    

    // Log all the course data
    console.log('Order Details - Course Data:', {
      course_id: courseData?.course_id,
      course_name: courseData?.course_name,
      course_price: courseData?.course_price,
      banner_image: courseData?.banner_image,
      course_type: courseData?.course_type,
      course_desc: courseData?.course_desc,
      discountCode: courseData?.discountCode,
      discountValue: courseData?.discountValue,
      points: courseData?.points
    });
  }, [location.state]);

  const subtotal = Number(courseData.course_price) || 0;
  const discount = Number(courseData.discountValue) || 0;
  const total = Math.max(0, subtotal - discount);

  const handleCheckout = async () => {
    if (total <= 0) {
      toast.error('Total must be greater than $0');
      return;
    }

    setIsCheckoutLoading(true);
    try {
      const stripe = await stripePromise;
      if (!stripe) {
        toast.error('Stripe failed to initialize');
        setIsCheckoutLoading(false);
        return;
      }

      const response = await processPayment({
        amount: total * 100, // in cents
        currency: 'usd',
        paymentMethodId: 'pm_card_visa',
        origin: window.location.origin,
        courseName: courseData.course_name,
        points: courseData.points,
        course_id: courseData.course_id, // ✅ Make sure this is present
      });
      
      

      if (response.success && response.sessionUrl) {
        window.location.href = response.sessionUrl;
      } else {
        toast.error(response.error_msg || 'Payment failed. Try again.');
        setIsCheckoutLoading(false);
      }
    } catch (error) {
      console.error('Checkout Error:', error);
      toast.error('Failed to process payment.');
      setIsCheckoutLoading(false);
    }
  };

  if (!courseData || !courseData.course_id) {
    return (
      <div className="d-flex justify-content-center align-items-center" style={{ minHeight: '60vh' }}>
        <div className="text-center">
          <h3>No order data available</h3>
          <p>Please select a course to purchase</p>
          <button 
            className="btn btn-primary mt-3"
            onClick={() => navigate(-1)}
          >
            Go Back
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="container py-4">
      <h2 className="mb-4">Order Details</h2>

      <div className="card shadow-sm rounded-4">
        <div className="card-body">
          <div className="row g-4">
            <div className="col-md-4">
              <img
                src={courseData.banner_image}
                alt={courseData.course_name}
                className="img-fluid rounded border"
                style={{ height: '180px', objectFit: 'cover' }}
                onError={(e) => {
                  e.target.onerror = null;
                  e.target.src = 'fallback-image-url'; // Add a fallback image URL
                }}
              />
            </div>
            <div className="col-md-8">
              <h4 className="fw-light">{courseData.course_name}</h4>
              <p className="text-muted">{courseData.course_desc}</p>

              <div className="mt-4">
                <h5 className="mb-3">Price Details</h5>

                <div className="d-flex justify-content-between">
                  <span>Course Price</span>
                  <span>${subtotal.toFixed(2)}</span>
                </div>

                {discount > 0 && (
                  <div className="d-flex justify-content-between text-success">
                    <span>Discount ({courseData.discountCode})</span>
                    <span>-${discount.toFixed(2)}</span>
                  </div>
                )}

                {courseData.points > 0 && (
                  <div className="d-flex justify-content-between text-info">
                    <span>Points Used</span>
                    <span>{courseData.points} points</span>
                  </div>
                )}

                <hr />
                <div className="d-flex justify-content-between fw-bold fs-5">
                  <span>Total</span>
                  <span>${total.toFixed(2)}</span>
                </div>

                  <div className="row d-flex justify-content-end">
                  <div className="col-6">
               <button
                  className="btn btn-primary mt-4 w-100"
                  onClick={handleCheckout}
                  disabled={isCheckoutLoading || total <= 0}
                >
                  {isCheckoutLoading ? (
                    <>
                      <span className="spinner-border spinner-border-sm me-2"></span>
                      Processing...
                    </>
                  ) : (
                    'Proceed to Checkout'
                  )}
                </button>
               </div>
                  </div>
              </div>
            </div>
          </div>
        </div>
      </div>

    </div>
  );
}

export default OrderDetails;
