import React, { useEffect, useState } from "react";
import { cancelPayUPayment } from "../../../services/userService";
import { useNavigate, useSearchParams } from "react-router-dom";
import { Icon } from '@iconify/react';
import './SuccessPayment.css';

const CancelPayUPayment = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const course_id = searchParams.get("course_id");
  const txnid = searchParams.get("txnid");

  const [data, setData] = useState({});
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);

  const handleCancelValidation = async () => {
    console.log('=== PayU Cancel Payment Validation Started ===');
    console.log('Course ID:', course_id);
    console.log('Transaction ID:', txnid);

    try {
      console.log('Calling cancelPayUPayment API...');
      const response = await cancelPayUPayment(course_id, txnid);
      console.log("Cancel PayU payment response:", response);

      if (response.success) {
        console.log('Payment cancellation processed:', response.data);
        setData(response.data);
      } else {
        console.error('Payment cancellation failed:', response);
        setHasError(true);
      }
    } catch (error) {
      console.error("Error in PayU cancel payment:", error);
      setHasError(true);
    } finally {
      console.log('Setting loading to false');
      setIsLoading(false);
    }
  };

  function redirectToCourse(){
    navigate(`/user/courses`);
  }

  function retryPayment(){
    navigate(`/user/courses/orderDetailsWithPayu?course_id=${course_id}`);
  }

  useEffect(() => {
    console.log('useEffect triggered with:', { course_id, txnid });
    if (course_id && txnid) {
      handleCancelValidation();
    } else {
      console.log('Missing parameters, setting loading to false');
      setIsLoading(false);
      setHasError(true);
    }
  }, [course_id, txnid]);

  if (isLoading) {
    return (
      <div className="d-flex justify-content-center align-items-center" style={{ minHeight: "60vh" }}>
        <div className="text-center">
          <div className="spinner-border text-warning mb-3" role="status" />
          <p className="text-muted">Processing your PayU payment cancellation...</p>
        </div>
      </div>
    );
  }

  if (hasError) {
    return (
      <div className="payment-error-container">
        <div className="error-content text-center">
          <Icon icon="mdi:alert-circle" className="text-danger mb-3" width="64" height="64" />
          <h3>Oops! Something went wrong.</h3>
          <p className="text-muted mb-4">We couldn't process your PayU payment cancellation. Please contact support.</p>
          <button className="btn btn-primary px-4 py-2" onClick={() => navigate('/')}>
            Back to Home
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="success-payment-container">
      <div className="success-payment-card">
        {/* Cancel Icon */}
        <div className="success-icon-wrapper mb-4">
          <Icon icon="mdi:close-circle" className="text-warning" width="64" height="64" />
        </div>

        {/* Cancel Message */}
        <h2 className="success-title text-warning">PayU Payment Cancelled</h2>
        <p className="success-subtitle">Your PayU payment transaction has been cancelled.</p>

        {/* Transaction Details */}
        <div className="transaction-details">
          <div className="detail-row">
            <span className="detail-label">Transaction ID</span>
            <span className="detail-value">
              #{txnid ? txnid.toString().toUpperCase().slice(0, 15) : "-"}
            </span>
          </div>
          <div className="detail-row">
            <span className="detail-label">Date</span>
            <span className="detail-value">
              {new Date().toLocaleDateString('en-IN', {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
              })}
            </span>
          </div>
          <div className="detail-row">
            <span className="detail-label">Status</span>
            <span className="detail-value status-cancelled">
              <Icon icon="mdi:close-circle" className="me-1" />
              Cancelled
            </span>
          </div>
          <div className="detail-row">
            <span className="detail-label">Payment Method</span>
            <span className="detail-value">
              PayU Gateway
            </span>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="action-buttons mt-4">
          <button 
            className="btn btn-primary btn-lg w-100 mb-3"
            onClick={retryPayment}
          >
            <Icon icon="mdi:refresh" className="me-2" />
            Try Payment Again
          </button>
          <button 
            className="btn btn-outline-secondary btn-lg w-100"
            onClick={redirectToCourse}
          >
            <Icon icon="mdi:arrow-left" className="me-2" />
            Back to Courses
          </button>
        </div>

        {/* Help Section */}
        <div className="help-section mt-4 p-3 bg-light rounded">
          <h6 className="mb-2">
            <Icon icon="mdi:help-circle" className="me-2" />
            Need Help?
          </h6>
          <p className="small text-muted mb-0">
            If you're experiencing issues with PayU payments, please contact our support team.
            No amount has been charged to your account.
          </p>
        </div>
      </div>
    </div>
  );
};

export default CancelPayUPayment;
