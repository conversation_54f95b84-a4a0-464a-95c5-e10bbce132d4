import React, { useState, useEffect, useCallback, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import defaultProfile from '../../../assets/images/profile/default-profile.png';
import { Icon } from '@iconify/react';
import './AllClassroom.css';
import { getTraineeClassrooms } from '../../../services/userService';
import debounce from 'lodash/debounce';
import { encodeData } from '../../../utils/encodeAndEncode'

function AllClassroom() {
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(true);
  const [hasMore, setHasMore] = useState(true);
  const [page, setPage] = useState(1);
  const [search, setSearch] = useState('');
  const [classrooms, setClassrooms] = useState([]);
  const [pagination, setPagination] = useState(null);
  const [loadingClassroom, setLoadingClassroom] = useState(null);
  const observer = useRef();
  const userId = JSON.parse(localStorage.getItem('user'))?.id;

  // Function to fetch classrooms
  const fetchClassrooms = async (pageNum, searchQuery, isNewSearch = false) => {
    try {
      setIsLoading(true);
      const response = await getTraineeClassrooms({
        user_id: userId,
        page: pageNum,
        limit: 10,
        search: searchQuery
      });

      if (response?.success && response?.data?.classrooms) {
        const newClassrooms = response.data.classrooms;
        const paginationInfo = response.data.pagination;

        if (isNewSearch) {
          setClassrooms(newClassrooms);
        } else {
          setClassrooms(prevClassrooms => [...prevClassrooms, ...newClassrooms]);
        }

        setPagination(paginationInfo);
        setHasMore(paginationInfo.currentPage < paginationInfo.totalPages);
      }
    } catch (error) {
      console.error('Error fetching classrooms:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Debounced search function
  const debouncedSearch = useCallback(
    debounce((searchQuery) => {
      setPage(1);
      fetchClassrooms(1, searchQuery, true);
    }, 500),
    []
  );

  // Handle search input change
  const handleSearchChange = (e) => {
    const searchQuery = e.target.value;
    setSearch(searchQuery);
    debouncedSearch(searchQuery);
  };

  // Last element ref callback for infinite scroll
  const lastClassroomElementRef = useCallback(node => {
    if (isLoading) return;
    if (observer.current) observer.current.disconnect();
    
    observer.current = new IntersectionObserver(entries => {
      if (entries[0].isIntersecting && hasMore) {
        setPage(prevPage => prevPage + 1);
      }
    });

    if (node) observer.current.observe(node);
  }, [isLoading, hasMore]);

  // Initial load and pagination
  useEffect(() => {
    fetchClassrooms(page, search, page === 1);
  }, [page]);

  // Format date helper function
  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    const options = { 
      year: 'numeric',
      month: 'short', 
      day: 'numeric'
    };
    return new Date(dateString).toLocaleDateString('en-US', options);
  };

  const handleClassroomClick = async (classroomId) => {
    try {
      setLoadingClassroom(classroomId);
      const encodedClassroomID = encodeData(classroomId);
      // Simulate loading for 2 seconds
      await new Promise(resolve => setTimeout(resolve, 2000));
      navigate(`/user/AllClassroom/classroomDetails/${encodeURIComponent(encodedClassroomID)}`);
    } catch (error) {
      console.error('Error navigating to classroom:', error);
    } finally {
      setLoadingClassroom(null);
    }
  };

  return (
    <div className="row">
      <div className="col-12">
        {/* Search Bar */}
        <div className="row mb-4 mt-2">
          <div className="col-12 col-md-4 col-lg-4">
            <div className="search-control">
              <input 
                type="text" 
                className="form-control search-input" 
                placeholder="Search classrooms..."
                value={search}
                onChange={handleSearchChange}
                aria-label="Search classrooms"
              />
            </div>
          </div>
          {pagination && (
            <div className="col-12 col-md-8 col-lg-8 text-end">
              <small className="text-muted">
                Showing {classrooms.length} of {pagination.totalRecords} classrooms
              </small>
            </div>
          )}
        </div>

        {/* Classrooms Grid */}
        <div className="row g-4">
<div className="row g-4">
  {Array.isArray(classrooms) && classrooms.map((classroom, index) => {
    // Safely extract values
    const cls_name = typeof classroom.cls_name === 'string' ? classroom.cls_name : 'Unnamed Classroom';
    const cls_desc = typeof classroom.cls_desc === 'string' ? classroom.cls_desc : 'No description available';
    const trainer_name = typeof classroom.trainer_name === 'string' ? classroom.trainer_name : 'Unknown Instructor';
    const total_users = typeof classroom.total_users === 'number' ? classroom.total_users : 0;

    return (
      <div
        key={classroom.id || index}
        className="col-md-6 col-lg-4"
        ref={index === classrooms.length - 1 ? lastClassroomElementRef : null}
      >
        <div
          className="card h-100 classroom-card"
          role="button"
          tabIndex={0}
        >
          <div className="classroom-image">
            <img
              src={classroom.banner_img || defaultProfile}
              className="card-img-top"
              alt={cls_name}
              onError={(e) => {
                e.target.src = defaultProfile;
              }}
            />
            <div className="image-overlay">
              <button
                className="enter-classroom-btn"
                onClick={() => handleClassroomClick(classroom.id)}
                disabled={loadingClassroom === classroom.id}
              >
                <>
                  Enter Classroom
                  {loadingClassroom === classroom.id ? (
                    <span className="loading-text">
                      <span className="dot">.</span>
                      <span className="dot">.</span>
                      <span className="dot">.</span>
                    </span>
                  ) : (
                    <Icon icon="mdi:arrow-right" width="20" height="20" />
                  )}
                </>
              </button>
            </div>
          </div>
          <div className="card-body">
            <h5 className="card-title mb-3">{cls_name}</h5>
            <p className="classroom-description mb-3">{cls_desc}</p>
            <div className="d-flex align-items-center mb-3">
              <img
                src={defaultProfile}
                className="rounded-circle me-2"
                alt={trainer_name}
                width="32"
                height="32"
              />
              <div>
                <small className="text-muted d-block">Instructor</small>
                <span className="fw-medium">{trainer_name}</span>
              </div>
            </div>
            <div className="d-flex flex-column flex-md-row justify-content-between align-items-center border-top pt-3">
              <div className="d-flex align-items-center justify-content-start justify-content-md-start w-100 w-md-auto mb-2 mb-md-0">
                <Icon icon="mdi:account-group" className="text-muted" width="20" height="20" />
                <small className="text-muted ms-1">{total_users} Students</small>
              </div>
              <div className="d-flex align-items-center justify-content-start justify-content-md-start w-100 w-md-auto">
                <Icon icon="mdi:calendar-clock" className="text-muted" width="20" height="20" />
                <small className="text-muted ms-1">
                  Joined: {formatDate(classroom.joined_at)}
                </small>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  })}
</div>

        </div>

        {/* Loading Indicator */}
        {isLoading && (
          <div className="text-center my-4">
            <div className="spinner-border text-primary" role="status">
              <span className="visually-hidden">Loading...</span>
            </div>
          </div>
        )}

        {/* No Results Message */}
        {!isLoading && (!classrooms || classrooms.length === 0) && (
          <div className="text-center my-4">
            <Icon icon="mdi:folder-open" width="48" height="48" className="text-muted mb-2" />
            <h5>No classrooms found</h5>
            <p className="text-muted">Try adjusting your search criteria</p>
          </div>
        )}
      </div>
    </div>
  );
}

export default AllClassroom;