import React, { createContext, useContext, useState, useEffect } from 'react';

const PermissionsContext = createContext();

export const PermissionsProvider = ({ children }) => {
  // Initialize state from localStorage if available
  const [permissions, setPermissions] = useState(() => {
    const storedPermissions = localStorage.getItem('permissions');
    return storedPermissions ? JSON.parse(storedPermissions) : {};
  });

  const setPermissionsFromAPI = (apiPermissions) => {
    // Convert array of permission objects to a key-value object
    const permissionsMap = apiPermissions.reduce((acc, permission) => {
      acc[permission.slug] = Boolean(permission.is_granted);
      return acc;
    }, {});
    
    // Update state and localStorage
    setPermissions(permissionsMap);
    localStorage.setItem('permissions', JSON.stringify(permissionsMap));
  };

  // Clear permissions when token is removed (logout)
  useEffect(() => {
    const handleStorageChange = (e) => {
      if (e.key === 'token' && !e.newValue) {
        setPermissions({});
        localStorage.removeItem('permissions');
      }
    };

    window.addEventListener('storage', handleStorageChange);
    return () => window.removeEventListener('storage', handleStorageChange);
  }, []);

  return (
    <PermissionsContext.Provider value={{ permissions, setPermissionsFromAPI }}>
      {children}
    </PermissionsContext.Provider>
  );
};

export const usePermissions = () => {
  const context = useContext(PermissionsContext);
  if (!context) {
    throw new Error('usePermissions must be used within a PermissionsProvider');
  }
  return context;
}; 