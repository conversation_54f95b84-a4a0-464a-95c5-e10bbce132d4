import React, { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom';
import { Icon } from '@iconify/react';
import { toast } from 'react-toastify';
import moment from 'moment-timezone';
import Nodata from '../../../components/common/NoData';
import Loader from '../../../components/common/Loader';
import { getAssignmentsForClassroom, addClassroomAssignment, deleteClassroomAssignment, updateClassroomAssignment } from '../../../services/adminService';
import { encodeData } from '../../../utils/encodeAndEncode';
import timeZones from '../../../utils/timeZone.json';
import { usePermissions } from '../../../context/PermissionsContext';
import './Classroom.css';
const DefaultTimeZone = process.env.REACT_APP_DEFAULT_TIMEZONE;

function ClassroomAssignment({decodedClassroomId}) {
  console.log('decodedClassroomId-------------',decodedClassroomId);

  const navigate = useNavigate();
  const { permissions } = usePermissions();
  const [showModal, setShowModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [editingAssignment, setEditingAssignment] = useState(null);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    dueDate: '',
    timeZone: DefaultTimeZone, // Using DefaultTimeZone instead of browser's timezone
    visibility: 'show' // Default value
  });
  const [timeZoneSearch, setTimeZoneSearch] = useState('');
  const [isTimeZoneDropdownOpen, setIsTimeZoneDropdownOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formErrors, setFormErrors] = useState({});

  // Character limits
  const MAX_NAME_LENGTH = 100;
  const MAX_DESCRIPTION_LENGTH = 500;

  // Visibility options
  const visibilityOptions = [
    { value: 'show', label: 'Show' },
    { value: 'hide', label: 'Hide' }
  ];

  // Check if form has errors or required fields are empty
  const hasFormErrors = () => {
    return Object.values(formErrors).some(error => error) ||
           !formData.name.trim() ||
           !formData.dueDate ||
           !formData.visibility;
  };

  const [assignments, setAssignments] = useState([]);
  const [loading, setLoading] = useState(true);
  const [pagination, setPagination] = useState({
    total: 0,
    page: 1,
    limit: 10,
    totalPages: 0
  });
  const [searchTerm, setSearchTerm] = useState('');

  // Delete modal states
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedAssignment, setSelectedAssignment] = useState(null);
  const [isDeleting, setIsDeleting] = useState(false);

  const fetchAssignments = async (page = 1, search = '') => {
    setLoading(true);
    try {
      const response = await getAssignmentsForClassroom({
        class_id: decodedClassroomId,
        page: page,
        limit: 10,
        search: search
      });

      console.log('getAssignments response', response);

      if (response.success) {
        setAssignments(response.data.assignments || []);
        setPagination(response.data.pagination || {});
      }
    } catch (error) {
      console.log('Error fetching assignments:', error);
      setAssignments([]);
    } finally {
      setLoading(false);
    }
  }

  useEffect(() => {
    fetchAssignments();
  }, [decodedClassroomId]);

  const handleSearch = (e) => {
    const value = e.target.value;
    setSearchTerm(value);
    fetchAssignments(1, value);
  };

  // Validation function
  const validateField = (name, value) => {
    const errors = {};

    switch (name) {
      case 'name':
        if (!value.trim()) {
          errors.name = 'Assignment name is required';
        } else if (value.length > MAX_NAME_LENGTH) {
          errors.name = `Assignment name cannot exceed ${MAX_NAME_LENGTH} characters`;
        }
        break;
      case 'description':
        if (value.length > MAX_DESCRIPTION_LENGTH) {
          errors.description = `Description cannot exceed ${MAX_DESCRIPTION_LENGTH} characters`;
        }
        break;
      case 'dueDate':
        if (!value) {
          errors.dueDate = 'Due date is required';
        } else {
          const selectedDate = new Date(value);
          const currentDate = new Date();
          if (selectedDate <= currentDate) {
            errors.dueDate = 'Due date must be in the future';
          }
        }
        break;
      case 'visibility':
        if (!value) {
          errors.visibility = 'Visibility is required';
        }
        break;
      default:
        break;
    }

    return errors;
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;

    // Update form data
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Real-time validation
    const fieldErrors = validateField(name, value);
    setFormErrors(prev => ({
      ...prev,
      [name]: fieldErrors[name] || null
    }));
  };

  const handleModalClose = () => {
    setShowModal(false);
    setIsSubmitting(false);
    // Reset form
    setFormData({
      name: '',
      description: '',
      dueDate: '',
      visibility: 'show'
    });
    // Reset form errors
    setFormErrors({});
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!permissions.classroom_assignment_create) {
      toast.warning("You don't have permission to create assignments");
      return;
    }

    // Validate all fields
    const nameErrors = validateField('name', formData.name);
    const descriptionErrors = validateField('description', formData.description);
    const dueDateErrors = validateField('dueDate', formData.dueDate);
    const visibilityErrors = validateField('visibility', formData.visibility);

    const allErrors = { ...nameErrors, ...descriptionErrors, ...dueDateErrors, ...visibilityErrors };

    if (Object.keys(allErrors).length > 0) {
      setFormErrors(allErrors);
      // Show first error in toast
      const firstError = Object.values(allErrors)[0];
      toast.error(firstError);
      return;
    }

    setIsSubmitting(true);

    try {
      // Convert the local date to UTC based on selected timezone
      const localDate = moment.tz(formData.dueDate, formData.timeZone);
      const utcDate = localDate.utc().format();


      const payload = {
        class_id: decodedClassroomId,
        title: formData.name.trim(),
        description: formData.description.trim() || '',
        due_date: utcDate, // Using the UTC converted date
        is_visible: formData.visibility === 'show' ? 1 : 0,
        total_marks: 0,
        time_zone: formData.timeZone // Adding the selected timezone
      };

      console.log('Submitting assignment:', payload);

      const response = await addClassroomAssignment(payload);

      console.log('Add assignment response:', response);

      if (response.success) {
        toast.success('Assignment created successfully!');
        handleModalClose();
        fetchAssignments(1, searchTerm);
      } else {
        toast.error(response.message || 'Failed to create assignment');
      }
    } catch (error) {
      console.error('Error creating assignment:', error);
      toast.error('An error occurred while creating the assignment');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Update assignment function
  const handleUpdateAssignment = async (formData) => {
    try {
      // Log form data for debugging
      for (let [key, value] of formData.entries()) {
        console.log(`${key}:`, value);
      }

      // Call the API to update the assignment
      const response = await updateClassroomAssignment(formData);
      console.log("Update assignment response:", response);

      // Check if response exists and has expected properties
      if (response) {
        // Refresh the assignments list with current search and pagination
        await fetchAssignments(1, searchTerm);
        setShowEditModal(false);
        toast.success(response.message || "Assignment updated successfully");
        return response;
      } else {
        // Handle error case
        const errorMsg = response?.message || "Failed to update assignment";
        toast.error(errorMsg);
        throw new Error(errorMsg);
      }
    } catch (error) {
      console.error("Error updating assignment:", error);
      toast.error(error.message || "An error occurred while updating the assignment");
      throw error; // Re-throw to allow the calling component to handle it
    }
  };

  // Handle edit assignment
  const handleEditAssignment = (assignment) => {
    if (!permissions.classroom_assignment_edit) {
      toast.warning("You don't have permission to edit assignments");
      return;
    }
    setEditingAssignment(assignment);
    
    // Convert UTC date to local date in the assignment's timezone
    const utcDate = moment.utc(assignment.due_date);
    const localDate = utcDate.tz(assignment.time_zone || DefaultTimeZone);
    
    setFormData({
      name: assignment.title,
      description: assignment.description || '',
      dueDate: localDate.format('YYYY-MM-DDTHH:mm'), // Format for datetime-local input
      timeZone: assignment.time_zone || DefaultTimeZone,
      visibility: assignment.is_visible === 1 ? 'show' : 'hide'
    });
    setFormErrors({});
    setShowEditModal(true);
  };

  // Handle edit form submission
  const handleEditSubmit = async (e) => {
    e.preventDefault();

    // Validate all fields
    const nameErrors = validateField('name', formData.name);
    const descriptionErrors = validateField('description', formData.description);
    const dueDateErrors = validateField('dueDate', formData.dueDate);
    const visibilityErrors = validateField('visibility', formData.visibility);

    const allErrors = { ...nameErrors, ...descriptionErrors, ...dueDateErrors, ...visibilityErrors };

    if (Object.keys(allErrors).length > 0) {
      setFormErrors(allErrors);
      // Show first error in toast
      const firstError = Object.values(allErrors)[0];
      toast.error(firstError);
      return;
    }

    setIsSubmitting(true);

    try {
      // Convert the local date to UTC based on selected timezone
      const localDate = moment.tz(formData.dueDate, formData.timeZone);
      const utcDate = localDate.utc().format();

      const payload = {
        assignment_id: editingAssignment.id,
        class_id: decodedClassroomId,
        title: formData.name.trim(),
        description: formData.description.trim() || '',
        due_date: utcDate,
        is_visible: formData.visibility === 'show' ? 1 : 0,
        total_marks: editingAssignment.total_marks || 0,
        time_zone: formData.timeZone
      };

      console.log('Updating assignment:', payload);

      const response = await updateClassroomAssignment(payload);

      console.log('Update assignment response:', response);

      if (response.success) {
        toast.success('Assignment updated successfully!');
        handleEditModalClose();
        fetchAssignments(1, searchTerm);
      } else {
        toast.error(response.message || 'Failed to update assignment');
      }
    } catch (error) {
      console.error('Error updating assignment:', error);
      toast.error('An error occurred while updating the assignment');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle edit modal close
  const handleEditModalClose = () => {
    setShowEditModal(false);
    setEditingAssignment(null);
    setIsSubmitting(false);
    // Reset form
    setFormData({
      name: '',
      description: '',
      dueDate: '',
      visibility: 'show'
    });
    // Reset form errors
    setFormErrors({});
  };

  // Handle delete assignment
  const handleDeleteAssignment = (assignment) => {
    if (!permissions.classroom_assignment_delete) {
      toast.warning("You don't have permission to delete assignments");
      return;
    }
    setSelectedAssignment(assignment);
    setShowDeleteModal(true);
  };

  // Handle delete confirmation
  const deleteClassAssignment = async () => {
    if (selectedAssignment) {
      setIsDeleting(true);
      try {
        // Call the API to delete the assignment
        const response = await deleteClassroomAssignment({ assignment_id: selectedAssignment.id });

        if (response.success) {
          // Close the delete confirmation popup
          setShowDeleteModal(false);
          setSelectedAssignment(null);

          // Refresh the assignments list with current search and pagination
          await fetchAssignments(1, searchTerm);
          toast.success("Assignment deleted successfully");
        } else {
          toast.error(response.message || 'Failed to delete assignment');
        }
      } catch (error) {
        console.error('Error deleting assignment:', error);
        toast.error('An error occurred while deleting the assignment');
      } finally {
        setIsDeleting(false);
      }
    }
  };

  // Handle delete modal close
  const handleDeleteModalClose = () => {
    setShowDeleteModal(false);
    setSelectedAssignment(null);
  };

  const HandleAssignmentDetails = (assignmentId) => {
    if (!permissions.classroom_assignment_manage) {
      toast.warning("You don't have permission to manage assignments");
      return;
    }
    // Encode both classroom_id and assignment_id
    const encodedClassroomId = encodeData(decodedClassroomId);
    const encodedAssignmentId = encodeData(assignmentId);
    
    navigate(`/admin/classrooms/classroom-dashboard/${encodedClassroomId}/assingment/${encodedAssignmentId}/ClassroomAssignmentDetails`);
    // Navigate with both encoded IDs
    // navigate(`/admin/ClassroomAssignmentDetails/${encodedClassroomId}/${encodedAssignmentId}`);
  }

  // Add this helper function at the top level of your component
  const getAssignmentStatus = (dueDate) => {
    const now = new Date();
    const due = new Date(dueDate);
    
    if (due < now) {
      return { status: 'ended', className: 'text-danger' };
    }
    return { status: 'ongoing', className: 'text-success' };
  };

  return (
    <>
      <div className="row mb-3">
        {/* search and add assignment button  */}
        <div className="col-12">
          <div className="row">
            <div className="col-12 col-md-6">
              <input
                type="text"
                className="form-control"
                placeholder="Search Assignment"
                style={{ width: '300px' }}
                value={searchTerm}
                onChange={handleSearch}
              />
            </div>
            <div className="col-12 col-md-6 d-flex justify-content-end" >
              <button 
                className="btn btn-primary" 
                style={{ width: '300px' }} 
                onClick={() => setShowModal(true)}
                disabled={!permissions.classroom_assignment_create}
                title={!permissions.classroom_assignment_create ? "You don't have permission to create assignments" : "Add Assignment"}
              >
                <Icon icon="fluent:add-24-regular" width="20" height="20" />
                Add Assignment
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Assignment Cards */}
      {loading ? (
        <div className="d-flex justify-content-center my-5">
          <Loader />
        </div>
      ) : assignments.length > 0 ? (
        assignments.map((assignment) => {
          const dueStatus = getAssignmentStatus(assignment.due_date);
          
          return (
            <div className="row mb-3" key={assignment.id}>
              <div className="card">
                <div className="card-body">
                  {/* title and icons */}
                  <div className="col-12 d-flex justify-content-between align-items-center mb-3">
                    <h5 className="card-title mb-0">{assignment.title}</h5>
                    <div className="d-flex gap-2 align-items-center">
                      <span 
                        style={{ 
                          padding: '6px 12px',
                          fontSize: '12px',
                          fontWeight: '500',
                          borderRadius: '4px',
                          backgroundColor: assignment.is_visible ? '#e6f4ea' : '#fff3e0',
                          color: assignment.is_visible ? '#1e8e3e' : '#f9a825',
                          border: `1px solid ${assignment.is_visible ? '#1e8e3e' : '#f9a825'}`
                        }}
                      >
                        {assignment.is_visible ? 'Visible to Trainees' : 'Hidden from Trainees'}
                      </span>
                      <span className={`assignment-status ${moment.utc(assignment.due_date).local().isBefore(moment()) ? 'ended' : 'ongoing'}`}>
  {moment.utc(assignment.due_date).local().isBefore(moment()) ? (
    'Assignment Ended'
  ) : (
    <div className="pulse-container">
      Assignment Ongoing
      <span className="status-dot" />
    </div>
  )}
</span>

                      <div className="d-flex gap-2">
                        <button
                          className="btn btn-outline-dark border border-dark btn-sm"
                          title={!permissions.classroom_assignment_edit ? "You don't have permission to edit assignments" : "Edit"}
                          onClick={() => handleEditAssignment(assignment)}
                          disabled={!permissions.classroom_assignment_edit}
                        >
                          <Icon icon="fluent:edit-24-regular" width="20" height="20" />
                        </button>
                        <button
                          className="btn btn-outline-danger border border-danger btn-sm"
                          title={!permissions.classroom_assignment_delete ? "You don't have permission to delete assignments" : "Delete"}
                          onClick={() => handleDeleteAssignment(assignment)}
                          disabled={!permissions.classroom_assignment_delete}
                        >
                          <Icon icon="fluent:delete-24-regular" width="20" height="20" />
                        </button>
                      </div>
                    </div>
                  </div>
                  <hr />

                  {/* description */}
                  <div className="col-12 mb-3">
                    <div className="d-flex align-items-start">
                      <Icon icon="fluent:text-description-24-regular" className="text-primary me-2 mt-1" width="20" height="20" />
                      <p className="mb-0" style={{ color: '#6c757d', fontSize: '14px' }}>{assignment.description}</p>
                    </div>
                  </div>

                  {/* card details */}
                  <div className="col-12 mb-3">
                    <div className="row">
                      <div className="col-md-6">
                        <div className="d-flex align-items-center mb-2">
                          <Icon icon="fluent:calendar-24-regular" className="text-primary me-2" width="20" height="20" />
                          <span>
  Due: {moment.utc(assignment.due_date).local().format('MMM DD, YYYY, hh:mm A')}
  <span className={moment.utc(assignment.due_date).local().isBefore(moment()) ? 'text-danger' : 'text-success'}>
    ({moment.utc(assignment.due_date).local().isBefore(moment()) ? 'Ended' : 'Ongoing'})
  </span>
</span>

                        </div>
                      </div>
                      <div className="col-md-6">
                        <div className="d-flex align-items-center mb-2">
                          <Icon icon="fluent:person-24-regular" className="text-primary me-2" width="20" height="20" />
                          <span>Created by: {assignment.creator_name}</span>
                        </div>
                      </div>
                    </div>
                    <div className="row">
                      <div className="col-md-6">
                        <div className="d-flex align-items-center">
                          <Icon icon="fluent:calendar-add-24-regular" className="text-primary me-2" width="20" height="20" />
                          <span>Assigned: {new Date(assignment.assigned_date).toLocaleDateString('en-US', {
                            year: 'numeric',
                            month: 'short',
                            day: '2-digit'
                          })}</span>
                        </div>
                      </div>
                      <div className="col-md-6">
                        <div className="d-flex align-items-center">
                          <Icon icon="fluent:trophy-24-regular" className="text-primary me-2" width="20" height="20" />
                          <span>Total Marks: {assignment.total_marks || 'Not set'}</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* buttons manage and trainees */}
                  <div className="col-12 d-flex justify-content-end">
                    <button 
                      className="btn btn-primary" 
                      style={{ width: '250px' }}
                      onClick={() => HandleAssignmentDetails(assignment.id)}
                      disabled={!permissions.classroom_assignment_manage}
                      title={!permissions.classroom_assignment_manage ? "You don't have permission to manage assignments" : "Manage Assignment"}
                    >
                      <Icon icon="fluent:settings-24-regular" className="me-2" width="20" height="20" />
                      Manage
                    </button>
                  </div>
                </div>
              </div>
            </div>
          );
        })
      ) : (
        <div className="d-flex justify-content-center my-5">
          <Nodata message="No assignments found" />
        </div>
      )}

      {/* Pagination */}
      {!loading && assignments.length > 0 && pagination.totalPages > 1 && (
        <div className="row mt-4">
          <div className="col-12">
            <div className="d-flex justify-content-between align-items-center">
              <div className="text-muted">
                Total {pagination.total} records
              </div>
              <nav>
                <ul className="pagination mb-0">
                  <li className={`page-item ${pagination.page === 1 ? 'disabled' : ''}`}>
                    <button
                      className="page-link"
                      onClick={() => fetchAssignments(pagination.page - 1, searchTerm)}
                      disabled={pagination.page === 1}
                    >
                      Previous
                    </button>
                  </li>

                  {[...Array(pagination.totalPages)].map((_, index) => {
                    const pageNumber = index + 1;
                    return (
                      <li key={pageNumber} className={`page-item ${pagination.page === pageNumber ? 'active' : ''}`}>
                        <button
                          className="page-link"
                          onClick={() => fetchAssignments(pageNumber, searchTerm)}
                        >
                          {pageNumber}
                        </button>
                      </li>
                    );
                  })}

                  <li className={`page-item ${pagination.page === pagination.totalPages ? 'disabled' : ''}`}>
                    <button
                      className="page-link"
                      onClick={() => fetchAssignments(pagination.page + 1, searchTerm)}
                      disabled={pagination.page === pagination.totalPages}
                    >
                      Next
                    </button>
                  </li>
                </ul>
              </nav>
            </div>
          </div>
        </div>
      )}

      {/* Add Assignment Modal */}
      {showModal && (
        <div className="modal show d-block fade" tabIndex="-1" style={{ backgroundColor: "rgba(0,0,0,0.5)" }}>
          <div className="modal-dialog modal-dialog-centered">
            <div className="modal-content" style={{ borderRadius: '12px', border: 'none' }}>
              <div className="modal-header" style={{ borderBottom: 'none', paddingBottom: '0' }}>
                <h5 className="modal-title fw-semibold" style={{ color: '#333', fontSize: '1.25rem' }}>Add Assignment</h5>
                <button
                  type="button"
                  className="btn-close"
                  onClick={handleModalClose}
                  style={{ fontSize: '0.875rem' }}
                ></button>
              </div>
              <div className="modal-body" style={{ paddingTop: '1rem' }}>
                <div className="mb-4">
                  <label className="form-label fw-semibold mb-2" style={{ color: '#333', fontSize: '0.875rem' }}>
                    Name <span className="text-danger">*</span>
                  </label>
                  <input
                    type="text"
                    className={`form-control ${formErrors.name ? 'is-invalid' : ''}`}
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    maxLength={MAX_NAME_LENGTH}
                    placeholder="Enter assignment name"
                    style={{
                      borderRadius: '8px',
                      border: formErrors.name ? '1px solid #dc3545' : '1px solid #e0e0e0',
                      padding: '12px 16px',
                      fontSize: '0.875rem',
                      backgroundColor: '#f8f9fa'
                    }}
                  />
                  {formErrors.name ? (
                    <div className="text-danger mt-1" style={{ fontSize: '0.75rem' }}>{formErrors.name}</div>
                  ) : (
                    <small className="text-muted mt-1 d-block" style={{ fontSize: '0.75rem' }}>
                      Maximum {MAX_NAME_LENGTH} characters allowed ({MAX_NAME_LENGTH - formData.name.length} remaining)
                    </small>
                  )}
                </div>

                <div className="mb-4">
                  <label className="form-label fw-semibold mb-2" style={{ color: '#333', fontSize: '0.875rem' }}>
                    Description
                  </label>
                  <textarea
                    className={`form-control ${formErrors.description ? 'is-invalid' : ''}`}
                    name="description"
                    value={formData.description}
                    onChange={handleInputChange}
                    maxLength={MAX_DESCRIPTION_LENGTH}
                    rows="4"
                    placeholder="Enter assignment description"
                    style={{
                      borderRadius: '8px',
                      border: formErrors.description ? '1px solid #dc3545' : '1px solid #e0e0e0',
                      padding: '12px 16px',
                      fontSize: '0.875rem',
                      backgroundColor: '#f8f9fa',
                      resize: 'vertical'
                    }}
                  />
                  {formErrors.description ? (
                    <div className="text-danger mt-1" style={{ fontSize: '0.75rem' }}>{formErrors.description}</div>
                  ) : (
                    <small className="text-muted mt-1 d-block" style={{ fontSize: '0.75rem' }}>
                      Maximum {MAX_DESCRIPTION_LENGTH} characters allowed ({MAX_DESCRIPTION_LENGTH - formData.description.length} remaining)
                    </small>
                  )}
                </div>

                <div className="mb-4">
                  <label className="form-label fw-semibold mb-2" style={{ color: '#333', fontSize: '0.875rem' }}>
                    Time Zone <span className="text-danger">*</span>
                  </label>
                  <div className="position-relative">
                    <div 
                      className={`form-select d-flex align-items-center justify-content-between ${formErrors.timeZone ? 'is-invalid' : ''}`}
                      onClick={() => setIsTimeZoneDropdownOpen(!isTimeZoneDropdownOpen)}
                      style={{
                        borderRadius: '8px',
                        border: formErrors.timeZone ? '1px solid #dc3545' : '1px solid #e0e0e0',
                        padding: '12px 16px',
                        fontSize: '0.875rem',
                        backgroundColor: '#fff',
                        cursor: 'pointer'
                      }}
                    >
                      <span>{formData.timeZone ? timeZones.find(zone => zone.value === formData.timeZone)?.label : 'Select Time Zone'}</span>
                      <Icon icon={isTimeZoneDropdownOpen ? "eva:arrow-up-fill" : "eva:arrow-down-fill"} />
                    </div>
                    {isTimeZoneDropdownOpen && (
                      <div 
                        className="position-absolute w-100 bg-white border rounded-3 mt-1 shadow-sm" 
                        style={{ 
                          maxHeight: '300px',
                          overflowY: 'auto',
                          zIndex: 1000
                        }}
                      >
                        <div className="p-2 sticky-top bg-white">
                          <div className="position-relative">
                            <input
                              type="text"
                              className="form-control"
                              placeholder="Search timezone..."
                              value={timeZoneSearch}
                              onChange={(e) => setTimeZoneSearch(e.target.value)}
                              onClick={(e) => e.stopPropagation()}
                              style={{
                                paddingLeft: '35px',
                                border: '1px solid #e0e0e0',
                                borderRadius: '6px',
                                fontSize: '0.875rem'
                              }}
                            />
                            <Icon 
                              icon="eva:search-outline"
                              style={{
                                position: 'absolute',
                                left: '12px',
                                top: '50%',
                                transform: 'translateY(-50%)',
                                color: '#6c757d'
                              }}
                            />
                          </div>
                        </div>
                        <div className="timezone-options">
                          {timeZones
                            .filter(zone => 
                              zone.label.toLowerCase().includes(timeZoneSearch.toLowerCase()) ||
                              zone.value.toLowerCase().includes(timeZoneSearch.toLowerCase())
                            )
                            .map((zone) => (
                              <div
                                key={zone.value}
                                className="timezone-option"
                                onClick={() => {
                                  setFormData(prev => ({ ...prev, timeZone: zone.value }));
                                  setIsTimeZoneDropdownOpen(false);
                                  setTimeZoneSearch('');
                                }}
                                style={{ 
                                  padding: '10px 16px',
                                  cursor: 'pointer',
                                  backgroundColor: formData.timeZone === zone.value ? '#f8f9fa' : 'transparent',
                                  transition: 'background-color 0.2s',
                                  fontSize: '0.875rem',
                                  color: '#333'
                                }}
                                onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#f8f9fa'}
                                onMouseLeave={(e) => e.currentTarget.style.backgroundColor = formData.timeZone === zone.value ? '#f8f9fa' : 'transparent'}
                              >
                                {zone.label}
                              </div>
                            ))
                          }
                        </div>
                      </div>
                    )}
                  </div>
                  {formErrors.timeZone && (
                    <div className="text-danger mt-1" style={{ fontSize: '0.75rem' }}>{formErrors.timeZone}</div>
                  )}
                </div>

                <div className="mb-4">
                  <label className="form-label fw-semibold mb-2" style={{ color: '#333', fontSize: '0.875rem' }}>
                    Due Date <span className="text-danger">*</span>
                  </label>
                  <input
                    type="datetime-local"
                    className={`form-control ${formErrors.dueDate ? 'is-invalid' : ''}`}
                    name="dueDate"
                    value={formData.dueDate}
                    onChange={handleInputChange}
                    min={new Date().toISOString().slice(0, 16)}
                    style={{
                      borderRadius: '8px',
                      border: formErrors.dueDate ? '1px solid #dc3545' : '1px solid #e0e0e0',
                      padding: '12px 16px',
                      fontSize: '0.875rem',
                      backgroundColor: '#f8f9fa'
                    }}
                  />
                  {formErrors.dueDate && (
                    <div className="text-danger mt-1" style={{ fontSize: '0.75rem' }}>{formErrors.dueDate}</div>
                  )}
                </div>

                <div className="mb-4">
                  <label className="form-label fw-semibold mb-2" style={{ color: '#333', fontSize: '0.875rem' }}>
                    Visibility <span className="text-danger">*</span>
                  </label>
                  <select
                    className={`form-select ${formErrors.visibility ? 'is-invalid' : ''}`}
                    name="visibility"
                    value={formData.visibility}
                    onChange={handleInputChange}
                    style={{
                      borderRadius: '8px',
                      border: formErrors.visibility ? '1px solid #dc3545' : '1px solid #e0e0e0',
                      padding: '12px 16px',
                      fontSize: '0.875rem',
                      backgroundColor: '#f8f9fa'
                    }}
                  >
                    {visibilityOptions.map(option => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                  {formErrors.visibility && (
                    <div className="text-danger mt-1" style={{ fontSize: '0.75rem' }}>{formErrors.visibility}</div>
                  )}
                </div>
              </div>
              <div className="modal-footer" style={{ borderTop: 'none', paddingTop: '0' }}>
                <div className="d-flex flex-column gap-2 w-100">
                  <button
                    type="button"
                    className="btn w-100"
                    onClick={handleModalClose}
                    disabled={isSubmitting}
                    style={{
                      border: '1px solid #dee2e6',
                      color: '#6c757d',
                      padding: '12px',
                      borderRadius: '8px',
                      fontSize: '0.875rem',
                      fontWeight: '500'
                    }}
                  >
                    Cancel
                  </button>
                  <button
                    type="button"
                    className="btn btn-primary w-100"
                    onClick={handleSubmit}
                    disabled={isSubmitting || hasFormErrors()}
                    style={{
                      padding: '12px',
                      borderRadius: '8px',
                      fontSize: '0.875rem',
                      fontWeight: '500',
                      borderColor: '#4f46e5'
                    }}
                  >
                    {isSubmitting ? (
                      <>
                        <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                        Creating...
                      </>
                    ) : (
                      'Save Assignment'
                    )}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Edit Assignment Modal */}
      {showEditModal && (
        <div className="modal show d-block fade" tabIndex="-1" style={{ backgroundColor: "rgba(0,0,0,0.5)" }}>
          <div className="modal-dialog modal-dialog-centered">
            <div className="modal-content" style={{ borderRadius: '12px', border: 'none' }}>
              <div className="modal-header" style={{ borderBottom: 'none', paddingBottom: '0' }}>
                <h5 className="modal-title fw-semibold" style={{ color: '#333', fontSize: '1.25rem' }}>Edit Assignment</h5>
                <button
                  type="button"
                  className="btn-close"
                  onClick={handleEditModalClose}
                  style={{ fontSize: '0.875rem' }}
                ></button>
              </div>
              <div className="modal-body" style={{ paddingTop: '1rem' }}>
                {/* Name field */}
                <div className="mb-4">
                  <label className="form-label fw-semibold mb-2" style={{ color: '#333', fontSize: '0.875rem' }}>
                    Name <span className="text-danger">*</span>
                  </label>
                  <input
                    type="text"
                    className={`form-control ${formErrors.name ? 'is-invalid' : ''}`}
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    maxLength={MAX_NAME_LENGTH}
                    placeholder="Enter assignment name"
                    style={{
                      borderRadius: '8px',
                      border: formErrors.name ? '1px solid #dc3545' : '1px solid #e0e0e0',
                      padding: '12px 16px',
                      fontSize: '0.875rem',
                      backgroundColor: '#f8f9fa'
                    }}
                  />
                  {formErrors.name ? (
                    <div className="text-danger mt-1" style={{ fontSize: '0.75rem' }}>{formErrors.name}</div>
                  ) : (
                    <small className="text-muted mt-1 d-block" style={{ fontSize: '0.75rem' }}>
                      Maximum {MAX_NAME_LENGTH} characters allowed ({MAX_NAME_LENGTH - formData.name.length} remaining)
                    </small>
                  )}
                </div>

                {/* Description field */}
                <div className="mb-4">
                  <label className="form-label fw-semibold mb-2" style={{ color: '#333', fontSize: '0.875rem' }}>
                    Description
                  </label>
                  <textarea
                    className={`form-control ${formErrors.description ? 'is-invalid' : ''}`}
                    name="description"
                    value={formData.description}
                    onChange={handleInputChange}
                    maxLength={MAX_DESCRIPTION_LENGTH}
                    rows="4"
                    placeholder="Enter assignment description"
                    style={{
                      borderRadius: '8px',
                      border: formErrors.description ? '1px solid #dc3545' : '1px solid #e0e0e0',
                      padding: '12px 16px',
                      fontSize: '0.875rem',
                      backgroundColor: '#f8f9fa',
                      resize: 'vertical'
                    }}
                  />
                  {formErrors.description ? (
                    <div className="text-danger mt-1" style={{ fontSize: '0.75rem' }}>{formErrors.description}</div>
                  ) : (
                    <small className="text-muted mt-1 d-block" style={{ fontSize: '0.75rem' }}>
                      Maximum {MAX_DESCRIPTION_LENGTH} characters allowed ({MAX_DESCRIPTION_LENGTH - formData.description.length} remaining)
                    </small>
                  )}
                </div>

                {/* Time Zone field */}
                <div className="mb-4">
                  <label className="form-label fw-semibold mb-2" style={{ color: '#333', fontSize: '0.875rem' }}>
                    Time Zone <span className="text-danger">*</span>
                  </label>
                  <div className="position-relative">
                    <div 
                      className={`form-select d-flex align-items-center justify-content-between ${formErrors.timeZone ? 'is-invalid' : ''}`}
                      onClick={() => setIsTimeZoneDropdownOpen(!isTimeZoneDropdownOpen)}
                      style={{
                        borderRadius: '8px',
                        border: formErrors.timeZone ? '1px solid #dc3545' : '1px solid #e0e0e0',
                        padding: '12px 16px',
                        fontSize: '0.875rem',
                        backgroundColor: '#fff',
                        cursor: 'pointer'
                      }}
                    >
                      <span>{formData.timeZone ? timeZones.find(zone => zone.value === formData.timeZone)?.label : 'Select Time Zone'}</span>
                      <Icon icon={isTimeZoneDropdownOpen ? "eva:arrow-up-fill" : "eva:arrow-down-fill"} />
                    </div>
                    {isTimeZoneDropdownOpen && (
                      <div 
                        className="position-absolute w-100 bg-white border rounded-3 mt-1 shadow-sm" 
                        style={{ 
                          maxHeight: '300px',
                          overflowY: 'auto',
                          zIndex: 1000
                        }}
                      >
                        <div className="p-2 sticky-top bg-white">
                          <div className="position-relative">
                            <input
                              type="text"
                              className="form-control"
                              placeholder="Search timezone..."
                              value={timeZoneSearch}
                              onChange={(e) => setTimeZoneSearch(e.target.value)}
                              onClick={(e) => e.stopPropagation()}
                              style={{
                                paddingLeft: '35px',
                                border: '1px solid #e0e0e0',
                                borderRadius: '6px',
                                fontSize: '0.875rem'
                              }}
                            />
                            <Icon 
                              icon="eva:search-outline"
                              style={{
                                position: 'absolute',
                                left: '12px',
                                top: '50%',
                                transform: 'translateY(-50%)',
                                color: '#6c757d'
                              }}
                            />
                          </div>
                        </div>
                        <div className="timezone-options">
                          {timeZones
                            .filter(zone => 
                              zone.label.toLowerCase().includes(timeZoneSearch.toLowerCase()) ||
                              zone.value.toLowerCase().includes(timeZoneSearch.toLowerCase())
                            )
                            .map((zone) => (
                              <div
                                key={zone.value}
                                className="timezone-option"
                                onClick={() => {
                                  setFormData(prev => ({ ...prev, timeZone: zone.value }));
                                  setIsTimeZoneDropdownOpen(false);
                                  setTimeZoneSearch('');
                                }}
                                style={{ 
                                  padding: '10px 16px',
                                  cursor: 'pointer',
                                  backgroundColor: formData.timeZone === zone.value ? '#f8f9fa' : 'transparent',
                                  transition: 'background-color 0.2s',
                                  fontSize: '0.875rem',
                                  color: '#333'
                                }}
                                onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#f8f9fa'}
                                onMouseLeave={(e) => e.currentTarget.style.backgroundColor = formData.timeZone === zone.value ? '#f8f9fa' : 'transparent'}
                              >
                                {zone.label}
                              </div>
                            ))
                          }
                        </div>
                      </div>
                    )}
                  </div>
                  {formErrors.timeZone && (
                    <div className="text-danger mt-1" style={{ fontSize: '0.75rem' }}>{formErrors.timeZone}</div>
                  )}
                </div>

                {/* Due Date field */}
                <div className="mb-4">
                  <label className="form-label fw-semibold mb-2" style={{ color: '#333', fontSize: '0.875rem' }}>
                    Due Date <span className="text-danger">*</span>
                  </label>
                  <input
                    type="datetime-local"
                    className={`form-control ${formErrors.dueDate ? 'is-invalid' : ''}`}
                    name="dueDate"
                    value={formData.dueDate}
                    onChange={handleInputChange}
                    min={moment().tz(formData.timeZone).format('YYYY-MM-DDTHH:mm')}
                    style={{
                      borderRadius: '8px',
                      border: formErrors.dueDate ? '1px solid #dc3545' : '1px solid #e0e0e0',
                      padding: '12px 16px',
                      fontSize: '0.875rem',
                      backgroundColor: '#f8f9fa'
                    }}
                  />
                  <small className="text-muted mt-1 d-block" style={{ fontSize: '0.75rem' }}>
                    Time shown in {timeZones.find(zone => zone.value === formData.timeZone)?.label || formData.timeZone}
                  </small>
                  {formErrors.dueDate && (
                    <div className="text-danger mt-1" style={{ fontSize: '0.75rem' }}>{formErrors.dueDate}</div>
                  )}
                </div>

                <div className="mb-4">
                  <label className="form-label fw-semibold mb-2" style={{ color: '#333', fontSize: '0.875rem' }}>
                    Visibility <span className="text-danger">*</span>
                  </label>
                  <select
                    className={`form-select ${formErrors.visibility ? 'is-invalid' : ''}`}
                    name="visibility"
                    value={formData.visibility}
                    onChange={handleInputChange}
                    style={{
                      borderRadius: '8px',
                      border: formErrors.visibility ? '1px solid #dc3545' : '1px solid #e0e0e0',
                      padding: '12px 16px',
                      fontSize: '0.875rem',
                      backgroundColor: '#f8f9fa'
                    }}
                  >
                    {visibilityOptions.map(option => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                  {formErrors.visibility && (
                    <div className="text-danger mt-1" style={{ fontSize: '0.75rem' }}>{formErrors.visibility}</div>
                  )}
                </div>
              </div>
              <div className="modal-footer" style={{ borderTop: 'none', paddingTop: '0' }}>
                <div className="d-flex flex-column gap-2 w-100">
                  <button
                    type="button"
                    className="btn w-100"
                    onClick={handleEditModalClose}
                    disabled={isSubmitting}
                    style={{
                      border: '1px solid #dee2e6',
                      color: '#6c757d',
                      padding: '12px',
                      borderRadius: '8px',
                      fontSize: '0.875rem',
                      fontWeight: '500'
                    }}
                  >
                    Cancel
                  </button>
                  <button
                    type="button"
                    className="btn btn-primary w-100"
                    onClick={handleEditSubmit}
                    disabled={isSubmitting || hasFormErrors()}
                    style={{
                      padding: '12px',
                      borderRadius: '8px',
                      fontSize: '0.875rem',
                      fontWeight: '500',
                      borderColor: '#4f46e5'
                    }}
                  >
                    {isSubmitting ? (
                      <>
                        <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                        Updating...
                      </>
                    ) : (
                      'Update Assignment'
                    )}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Delete Confirmation Modal */}
      {showDeleteModal && (
        <div className="modal show d-block fade" tabIndex="-1" style={{ backgroundColor: "rgba(0,0,0,0.5)" }}>
          <div className="modal-dialog modal-dialog-centered">
            <div className="modal-content" style={{ borderRadius: '12px', border: 'none' }}>
              <div className="modal-header" style={{ borderBottom: 'none', paddingBottom: '0' }}>
                <h5 className="modal-title fw-semibold text-danger" style={{ fontSize: '1.25rem' }}>Confirm Deletion</h5>
                <button
                  type="button"
                  className="btn-close"
                  onClick={handleDeleteModalClose}
                  style={{ fontSize: '0.875rem' }}
                ></button>
              </div>
              <div className="modal-body" style={{ paddingTop: '1rem' }}>
                <p style={{ color: '#333', fontSize: '0.875rem', marginBottom: '1rem' }}>
                  Are you sure you want to delete the assignment <strong>"{selectedAssignment?.title}"</strong>?
                </p>
                <p className="text-danger mb-0" style={{ fontSize: '0.875rem', fontWeight: '500' }}>
                  This action cannot be undone.
                </p>
              </div>
              <div className="modal-footer" style={{ borderTop: 'none', paddingTop: '0' }}>
                <div className="d-flex flex-column gap-2 w-100">
                  <button
                    type="button"
                    className="btn w-100"
                    onClick={handleDeleteModalClose}
                    disabled={isDeleting}
                    style={{
                      backgroundColor: '#f8f9fa',
                      border: '1px solid #dee2e6',
                      color: '#6c757d',
                      padding: '12px',
                      borderRadius: '8px',
                      fontSize: '0.875rem',
                      fontWeight: '500'
                    }}
                  >
                    Cancel
                  </button>
                  <button
                    type="button"
                    className="btn btn-danger w-100"
                    onClick={deleteClassAssignment}
                    disabled={isDeleting}
                    style={{
                      padding: '12px',
                      borderRadius: '8px',
                      fontSize: '0.875rem',
                      fontWeight: '500'
                    }}
                  >
                    {isDeleting ? (
                      <>
                        <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                        Deleting...
                      </>
                    ) : (
                      'Delete Assignment'
                    )}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

    </>
  );
}

export default ClassroomAssignment
