import React, { useState, useEffect, useRef } from 'react';
import { useParams, useSearchParams, useNavigate } from 'react-router-dom';
import { Icon } from '@iconify/react';
import { decodeData, encodeData } from '../../../utils/encodeAndEncode';
import { toast } from 'react-toastify';
import { getAllQuestionsForClassroomAssessmentForUser, submitClassroomAssessment } from '../../../services/userService';
import NoData from '../../../components/common/NoData';

function AssessmentQuiz() {
    const navigate = useNavigate();
    const { encodedAssessmentId } = useParams();
    const [searchParams] = useSearchParams();
    const encodedClassroomId = searchParams.get('classroomid');

    const assessment_id = decodeData(decodeURIComponent(encodedAssessmentId));
    const classroom_id = encodedClassroomId ? decodeData(decodeURIComponent(encodedClassroomId)) : null;

    const [assessmentData, setAssessmentData] = useState(null);
    const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
    const [selectedAnswer, setSelectedAnswer] = useState(null);
    const [progress, setProgress] = useState(0);
    const [answers, setAnswers] = useState({});
    const [submissionResult, setSubmissionResult] = useState(null);
    const [timeLeft, setTimeLeft] = useState(null);
    const [quizStarted, setQuizStarted] = useState(false);
    const [isSubmitting, setIsSubmitting] = useState(false);

    const isSubmittingRef = useRef(false);

    
    // Ref to store current answers for timer callback
    const answersRef = useRef({});

    // Keep ref in sync with answers state
    useEffect(() => {
        answersRef.current = answers;
    }, [answers]);

    useEffect(() => {
        const fetchQuestions = async () => {
            try {
                const payload = {
                    assessment_id: assessment_id,
                    class_id: classroom_id
                };

                const response = await getAllQuestionsForClassroomAssessmentForUser(payload);
                console.log("Assessment Questions Response:-------------------", response);
                if (response.success) {
                    setAssessmentData(response.data.assessment);
                    // Set initial timer value in seconds (null for untimed quizzes)
                    if (response.data.assessment.duration > 0) {
                        setTimeLeft(response.data.assessment.duration * 60);
                    } else {
                        setTimeLeft(null); // No timer for untimed quizzes
                    }
                    setQuizStarted(true);
                }
            } catch (error) {
                console.error("Error fetching assessment questions:", error);
            }
        };

        if (assessment_id && classroom_id) {
            fetchQuestions();
        }
    }, [assessment_id, classroom_id]);

    // Timer effect
    useEffect(() => {
        let timer;
        if (quizStarted && timeLeft !== null && timeLeft > 0 && !submissionResult) {
            timer = setInterval(() => {
                setTimeLeft((prevTime) => {
                    const newTime = prevTime - 1;
                    
                    // Show notifications only at 25 and 10 seconds
                    if (newTime === 25) {
                        toast.warning('⚠️ Only 25 seconds remaining!', {
                            toastId: 'timer-25',
                            position: "top-center",
                            autoClose: 3000,
                        });
                    } else if (newTime === 10) {
                        toast.error('⚠️ Only 10 seconds remaining!', {
                            toastId: 'timer-10',
                            position: "top-center",
                            autoClose: 3000,
                        });
                    }
                    
                    if (newTime <= 0) {
                        clearInterval(timer);
                        // Auto-submit when timer reaches 0
                        console.log("Timer expired - auto-submitting with answers:", answersRef.current);
                        handleSubmit(true);
                        return 0;
                    }
                    return newTime;
                });
            }, 1000);
        }

        return () => {
            if (timer) clearInterval(timer);
        };
    }, [quizStarted, submissionResult]); // Add submissionResult to dependencies

    const formatTime = (seconds) => {
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = seconds % 60;
        return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
    };

    const handleOptionSelect = (optionId) => {
        const currentQuestion = assessmentData.questions[currentQuestionIndex];
        
        setSelectedAnswer(optionId);
        setAnswers(prev => {
            const newAnswers = {
                ...prev,
                [currentQuestionIndex]: optionId
            };
            // Update ref with latest answers
            answersRef.current = newAnswers;
            console.log("Updated Answers:", newAnswers);
            return newAnswers;
        });
        
        // Calculate progress based on unique answered questions
        const answeredQuestionsCount = Object.keys(answers).length + 1; // +1 for current selection
        const progressPercentage = (answeredQuestionsCount / assessmentData.total_questions) * 100;
        setProgress(progressPercentage);
        
        console.log("Selected option for question:", {
            questionId: currentQuestion.id,
            optionId: optionId,
            questionIndex: currentQuestionIndex
        });
    };

    const handleNext = () => {
        if (currentQuestionIndex < (assessmentData?.questions?.length - 1)) {
            setCurrentQuestionIndex(prev => prev + 1);
            // Show previously selected answer if it exists
            setSelectedAnswer(answers[currentQuestionIndex + 1] || null);
        }
    };

    const handlePrevious = () => {
        if (currentQuestionIndex > 0) {
            setCurrentQuestionIndex(prev => prev - 1);
            // Show previously selected answer if it exists
            setSelectedAnswer(answers[currentQuestionIndex - 1] || null);
        }
    };

    const handleSubmit = async (isAutoSubmit = false) => {
        if (isSubmittingRef.current || submissionResult) {
            return;
        }
    
        isSubmittingRef.current = true;
        setIsSubmitting(true);
    
        const currentAnswers = isAutoSubmit ? answersRef.current : answers;
    
        const answersArray = Object.entries(currentAnswers).map(([questionIndex, optionId]) => ({
            question_id: assessmentData.questions[parseInt(questionIndex)].id,
            option: optionId.toString()
        }));
    
        const submissionPayload = {
            class_id: classroom_id,
            assessment_id: assessment_id,
            user_id: JSON.parse(localStorage.getItem('user'))?.id,
            quiz_answers: answersArray
        };
    
        try {
            const response = await submitClassroomAssessment(submissionPayload);
            if (response?.success) {
                setSubmissionResult(response.data);
                toast.success('Assessment submitted successfully!', {
                    toastId: 'assessment-submitted',
                });
            } else {
                toast.error('Failed to submit assessment. Please try again.');
            }
        } catch (error) {
            console.error("Error submitting assessment:", error);
            toast.error('Error submitting assessment. Please try again.');
        } finally {
            setIsSubmitting(false);
            isSubmittingRef.current = false; // optional if retry is ever allowed
        }
    };
    
    if (submissionResult) {
        return (
            <div className="container py-5">
                <div className="row justify-content-center">
                    <div className="col-lg-8">
                        <div className="card shadow-sm border-0">
                            <div className="card-body p-5">
                                <div className="text-center mb-4">
                                    <div className="bg-warning bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style={{ width: '80px', height: '80px' }}>
                                        <Icon 
                                            icon="mdi:information"
                                            className="text-warning fs-1"
                                            width="40" 
                                            height="40" 
                                        />
                                    </div>
                                    <h3 className="mb-5">Assessment Completed!</h3>
                                </div>

                                <div className="row justify-content-center">
                                    <div className="col-md-10">
                                        <div className="d-flex justify-content-between align-items-center py-3 border-bottom">
                                            <span className="text-muted">Total Questions:</span>
                                            <span className="fs-5">{submissionResult.total_questions}</span>
                                        </div>
                                        <div className="d-flex justify-content-between align-items-center py-3 border-bottom">
                                            <span className="text-muted">Attempted Questions:</span>
                                            <span className="fs-5">{submissionResult.total_attempted}</span>
                                        </div>
                                        <div className="d-flex justify-content-between align-items-center py-3 border-bottom">
                                            <span className="text-muted">Correct Answers:</span>
                                            <span className="fs-5">{submissionResult.total_correct}</span>
                                        </div>
                                        <div className="d-flex justify-content-between align-items-center py-3 border-bottom">
                                            <span className="text-muted">Your Score:</span>
                                            <span className="fs-5">{submissionResult.percentage}%</span>
                                        </div>
                                        <div className="d-flex justify-content-between align-items-center py-3 border-bottom">
                                            <span className="text-muted">Pass Percentage:</span>
                                            <span className="fs-5">{submissionResult.pass_percentage}%</span>
                                        </div>
                                        <div className="d-flex justify-content-between align-items-center py-3">
                                            <span className="text-muted">Status:</span>
                                            <span className={`fs-5 ${submissionResult.percentage >= submissionResult.pass_percentage ? 'text-success' : 'text-danger'}`}>
                                                {submissionResult.percentage >= submissionResult.pass_percentage ? 'Passed' : 'Failed'}
                                            </span>
                                        </div>
                                    </div>
                                </div>

                                <div className="mt-5 d-flex justify-content-center">
                                    <button 
                                        className="btn btn-primary d-flex align-items-center justify-content-center gap-2"
                                        style={{ minWidth: '200px' }}
                                        onClick={() => navigate(`/user/AllClassroom/classroomDetails/${encodeURIComponent(encodeData(classroom_id))}`)}
                                    >
                                        <Icon icon="mdi:arrow-left" width="20" height="20" />
                                        Back to Classroom
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        );
    }

    // Loading
    if (!assessmentData) {
        return (
            <div className="container py-5">
                <div className="row justify-content-center">
                    <div className="col-md-6">
                        <div className="text-center">
                            <div className="spinner-border text-primary" role="status">
                                <span className="visually-hidden">Loading...</span>
                            </div>
                            <p className="mt-3">Loading assessment...</p>
                        </div>
                    </div>
                </div>
            </div>
        );
    }

    // No Questions Added Yet
    if (!assessmentData?.questions?.length) {
        return (
            <div className="container py-5">
                <div className="row justify-content-center">
                    <div className="col-md-6">
                        <div className="card shadow-sm border-0">
                            <div className="card-body text-center p-5">
                                <div className="mb-4">
                                    <Icon 
                                        icon="mdi:file-document-outline" 
                                        className="text-muted" 
                                        width="80" 
                                        height="80" 
                                    />
                                </div>
                                <h4 className="text-muted mb-3">No Assessment Questions Available</h4>
                                <p className="text-muted mb-4">The instructor hasn't added any questions to this assessment yet.</p>
                                <button 
                                    className="btn btn-primary d-flex align-items-center gap-2 mx-auto"
                                    onClick={() => navigate(`/user/AllClassroom/classroomDetails/${encodeURIComponent(encodeData(classroom_id))}`)}
                                >
                                    <Icon icon="mdi:arrow-left" width="20" height="20" />
                                    Back to Classroom
                                </button>
                            </div>
                        </div>
                        <div className="card mt-4 border-0 bg-warning bg-opacity-10">
                            <div className="card-body p-4">
                                <div className="d-flex align-items-center gap-3">
                                    <Icon 
                                        icon="mdi:information" 
                                        className="text-warning"
                                        width="24" 
                                        height="24" 
                                    />
                                    <div>
                                        <h6 className="mb-1">Assessment Due Date</h6>
                                        <p className="mb-0 small">
                                            Due by: {new Date(assessmentData.due_date).toLocaleString('en-US', {
                                                year: 'numeric',
                                                month: 'short',
                                                day: '2-digit',
                                                hour: '2-digit',
                                                minute: '2-digit',
                                                hour12: true
                                            })}
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        );
    }

    const currentQuestion = assessmentData.questions[currentQuestionIndex];
    const isLastQuestion = currentQuestionIndex === assessmentData.questions.length - 1;

    return (
        <div className="container-fluid py-4">
            <div className="row d-flex justify-content-center align-items-center">
                <div className="col-lg-8 col-xl-7">
                    {/* Assessment Name */}
                    <div className="mb-4">
                        <h4 className="mb-0 text-center">{assessmentData.assessment_name}</h4>
                    </div>

                    {/* Timer and Question Count */}
                    <div className="d-flex flex-column flex-md-row justify-content-between align-items-start align-items-md-center mb-4">
                        <div className="d-flex align-items-center mb-3 mb-md-0">
                            <div className="bg-primary rounded-circle text-white p-2 me-2 d-flex align-items-center justify-content-center" style={{ width: '30px', height: '30px' }}>
                                <small>{currentQuestionIndex + 1}</small>
                            </div>
                            <span>Question {currentQuestionIndex + 1} of {assessmentData.total_questions}</span>
                        </div>
                        <div className="d-flex gap-2">
                            <span className="badge border border-primary text-primary bg-primary-subtle d-flex align-items-center gap-2 px-3 py-2 fw-medium fs-7">
                                <Icon icon="mdi:help-circle" width="14" height="14" />
                                {assessmentData.total_questions} Questions
                            </span>
                            {timeLeft !== null && (
                                <span className={`badge d-flex align-items-center gap-2 px-3 py-2 fw-medium fs-7 ${
                                    timeLeft <= 60 ? 'border border-danger text-danger bg-danger-subtle' : 'border border-info text-info bg-info-subtle'
                                }`}>
                                    <Icon icon="mdi:clock-outline" width="14" height="14" />
                                    {formatTime(timeLeft)}
                                </span>
                            )}
                            {timeLeft === null && (
                                <span className="badge border border-success text-success bg-success-subtle d-flex align-items-center gap-2 px-3 py-2 fw-medium fs-7">
                                    <Icon icon="mdi:clock-outline" width="14" height="14" />
                                    Untimed
                                </span>
                            )}
                        </div>
                    </div>

                    {/* Progress Bar */}
                    <div className="progress mb-4" style={{ height: '4px' }}>
                        <div 
                            className="progress-bar" 
                            role="progressbar" 
                            style={{ width: `${progress}%` }}
                            aria-valuenow={progress} 
                            aria-valuemin="0" 
                            aria-valuemax="100"
                        />
                    </div>

                    {/* Question */}
                    <div className="card">
                        <div className="card-body">
                            <h5 className="card-title mb-4">{currentQuestion?.question_name}</h5>
                            
                            <div className="d-flex flex-column gap-3">
                                {currentQuestion?.options?.map((option) => (
                                    <div 
                                        key={option.id}
                                        className={`form-check border rounded p-3 ${selectedAnswer === option.id ? 'border-primary bg-light' : ''} cursor-pointer`}
                                        onClick={() => handleOptionSelect(option.id)}
                                    >
                                        <input
                                            type="radio"
                                            className="form-check-input"
                                            checked={selectedAnswer === option.id}
                                            onChange={() => handleOptionSelect(option.id)}
                                        />
                                        <label className="form-check-label ms-2">
                                            {option.option_value}
                                        </label>
                                    </div>
                                ))}
                            </div>
                        </div>
                    </div>

                    {/* Buttons */}
                    <div className="d-flex justify-content-between mt-4">
                        <button 
                            className="btn btn-outline-primary w-120"
                            onClick={handlePrevious}
                            disabled={currentQuestionIndex === 0}
                        >
                            Previous
                        </button>
                        {isLastQuestion ? (
                            <button 
                                className="btn btn-primary w-120"
                                onClick={handleSubmit}
                                disabled={!selectedAnswer || isSubmitting}
                            >
                                {isSubmitting ? (
                                    <>
                                        <span className="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>
                                        Submitting...
                                    </>
                                ) : (
                                    'Submit'
                                )}
                            </button>
                        ) : (
                            <button 
                                className="btn btn-primary w-120"
                                onClick={handleNext}
                                disabled={!selectedAnswer}
                            >
                                Next
                            </button>
                        )}
                    </div>
                </div>
            </div>
        </div>
    );
}

export default AssessmentQuiz;  