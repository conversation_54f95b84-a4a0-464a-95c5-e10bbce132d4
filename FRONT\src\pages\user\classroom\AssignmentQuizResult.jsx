import React from 'react';
import { Icon } from '@iconify/react';
import { useNavigate } from 'react-router-dom';

function AssessmentQuizResult() {
  const navigate = useNavigate();

  const resultData = {
    score: 33.33,
    correctAnswers: 1,
    totalQuestions: 3,
    duration: 10,
    attempt: 3
  };

  const handleRedirect = () => {
    navigate('/user/classroom');
  };

  return (
      <div className="card border-0 shadow-sm">
        <div className="card-body p-4">
          {/* Header */}
          <div className="text-center mb-4">
            <h4 className="mb-1">Quiz Result</h4>
            <p className="text-muted mb-0">Here's how you performed</p>
          </div>

          {/* Score Circle */}
          <div className="d-flex justify-content-center mb-5">
            <div 
              className="rounded-circle border border-4 border-primary d-flex flex-column align-items-center justify-content-center"
              style={{ width: '150px', height: '150px' }}
            >
              <h3 className="mb-0">{resultData.score}%</h3>
              <span className="text-muted">Score</span>
            </div>
          </div>

          {/* Stats Grid */}
          <div className="row g-4 mb-4">
            {/* Correct Answers */}
            <div className="col-md-4">
              <div className="bg-light rounded p-4 text-center h-100">
                <div className="d-flex align-items-center justify-content-center mb-2">
                  <Icon 
                    icon="mdi:check-circle" 
                    className="text-success"
                    width="24" 
                    height="24" 
                  />
                </div>
                <h5 className="mb-1">Correct Answers</h5>
                <p className="mb-0 text-primary fw-medium">
                  {resultData.correctAnswers}/{resultData.totalQuestions}
                </p>
              </div>
            </div>

            {/* Duration */}
            <div className="col-md-4">
              <div className="bg-light rounded p-4 text-center h-100">
                <div className="d-flex align-items-center justify-content-center mb-2">
                  <Icon 
                    icon="mdi:clock-outline" 
                    className="text-warning"
                    width="24" 
                    height="24" 
                  />
                </div>
                <h5 className="mb-1">Duration</h5>
                <p className="mb-0 text-primary fw-medium">
                  {resultData.duration} min
                </p>
              </div>
            </div>

            {/* Attempt */}
            <div className="col-md-4">
              <div className="bg-light rounded p-4 text-center h-100">
                <div className="d-flex align-items-center justify-content-center mb-2">
                  <Icon 
                    icon="mdi:refresh" 
                    className="text-info"
                    width="24" 
                    height="24" 
                  />
                </div>
                <h5 className="mb-1">Attempt</h5>
                <p className="mb-0 text-primary fw-medium">
                  #{resultData.attempt}
                </p>
              </div>
            </div>
          </div>

          {/* Action Button */}
          <div className="d-flex justify-content-center">
            <button 
              className="btn btn-primary d-flex align-items-center gap-2 w-100 w-md-25"
              onClick={handleRedirect}
            >
              <Icon icon="mdi:refresh" width="20" height="20" />
                Redirect to Assessment
            </button>
          </div>
        </div>
      </div>
  );
}

export default AssessmentQuizResult; 