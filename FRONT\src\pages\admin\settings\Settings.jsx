import React, { useState, useEffect } from "react";
import { Icon } from "@iconify/react";
import { useSearchParams } from "react-router-dom";
import { toast } from "react-toastify";
import AboutOrganization from "./AboutOrganization";
import FAQ from "./FAQ";
import Query from "./Query";
import PaymentHistory from "./PaymentHistory";
import { usePermissions } from "../../../context/PermissionsContext";

// import "./Settings.css";

function Settings() {
  const { permissions } = usePermissions();
  const [searchParams] = useSearchParams();
  const [activeTab, setActiveTab] = useState("about-organization");

  useEffect(() => {
    // Get activeTab from URL query parameter
    const tabFromUrl = searchParams.get("activeTab");
    if (tabFromUrl) {
      setActiveTab(tabFromUrl);
    }
  }, [searchParams]);

  const handleTabChange = (tab) => {
    // Check permissions before changing tab
    switch (tab) {
      case 'about-organization':
        if (!permissions.settings_about_organization_view) {
          toast.warning("You don't have permission to view organization information");
          return;
        }
        break;
      case 'faq':
        if (!permissions.settings_faq_view) {
          toast.warning("You don't have permission to view FAQs");
          return;
        }
        break;
      case 'query':
        if (!permissions.settings_query_view) {
          toast.warning("You don't have permission to view queries");
          return;
        }
        break;
      case 'payment-history':
        if (!permissions.settings_payments_view) {
          toast.warning("You don't have permission to view payment history");
          return;
        }
        break;
      default:
        break;
    }
    setActiveTab(tab);
  };

  return (
    <div className="contianer">
      <div className="row">
        <div className="col-12">
          <div className="settings-container">
            {/* Settings Tabs */}
            <div className="settings-tabs-container">
              <div className="settings-tabs">
                <button
                  className={`settings-tab-item ${activeTab === 'about-organization' ? 'active' : ''}`}
                  onClick={() => handleTabChange('about-organization')}
                  disabled={!permissions.settings_about_organization_view}
                  title={!permissions.settings_about_organization_view ? "You don't have permission to view organization information" : ""}
                >
                  <Icon
                    icon="fluent:building-24-regular"
                    width="22"
                    height="22"
                    className="me-2"
                  />
                  About Organization
                </button>

                <button
                  className={`settings-tab-item ${activeTab === "faq" ? "active" : ""}`}
                  onClick={() => handleTabChange("faq")}
                  disabled={!permissions.settings_faq_view}
                  title={!permissions.settings_faq_view ? "You don't have permission to view FAQs" : ""}
                >
                  <Icon
                    icon="fluent:question-circle-24-regular"
                    width="22"
                    height="22"
                    className="me-2"
                  />
                  FAQ
                </button>

                <button
                  className={`settings-tab-item ${activeTab === "query" ? "active" : ""}`}
                  onClick={() => handleTabChange("query")}
                  disabled={!permissions.settings_query_view}
                  title={!permissions.settings_query_view ? "You don't have permission to view queries" : ""}
                >
                  <Icon
                    icon="fluent:chat-help-24-regular"
                    width="22"
                    height="22"
                    className="me-2"
                  />
                  Support Query
                </button>

                <button
                  className={`settings-tab-item ${activeTab === "payment-history" ? "active" : ""}`}
                  onClick={() => handleTabChange("payment-history")}
                  disabled={!permissions.settings_payments_view}
                  title={!permissions.settings_payments_view ? "You don't have permission to view payment history" : ""}
                >
                  <Icon
                    icon="fluent:payment-24-regular"
                    width="22"
                    height="22"
                    className="me-2"
                  />
                  Payment History
                </button>
              </div>
            </div>

            {/* Tab Content */}
            <div className="settings-content-container">
              {activeTab === "about-organization" && (
                <div className="settings-card">
                  <AboutOrganization />
                </div>
              )}

              {activeTab === "faq" && (
                <div className="settings-card">
                  <FAQ />
                </div>
              )}

              {activeTab === "query" && (
                <div className="settings-card">
                  <Query />
                </div>
              )}

              {activeTab === "payment-history" && (
                <div className="settings-card">
                  <PaymentHistory />
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default Settings;
