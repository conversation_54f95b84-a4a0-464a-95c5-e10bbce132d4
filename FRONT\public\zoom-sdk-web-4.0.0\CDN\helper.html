<!doctype html><html><head><meta charset="utf-8"><meta name="viewport" content="width=device-width,initial-scale=1"><title>Zoom Meetings SDK Web Helper</title><script defer="defer">(()=>{var e={3454:(e,r)=>{"use strict";function t(e){var r=document.createElement("div");r.id=e,"function"==typeof document.body.append?document.body.append(r):document.body.appendChild(r)}function n(e){var r=document.createElement("div");r.id=e,r.role="alert",r.ariaAtomic="true",r.ariaLive="assertive",r.class="sr-only",r.id=e,r.style="display: none","function"==typeof document.body.append?document.body.append(r):document.body.appendChild(r)}Object.defineProperty(r,"__esModule",{value:!0}),r.checkJssdkNode=function(){document.querySelector("#zmmtg-root")||t("zmmtg-root"),document.querySelector("#aria-notify-area")||n("aria-notify-area")},r.checkJssdkNodeHelper=function(){document.querySelector("#zmmtg-root-helper")||t("zmmtg-root-helper"),document.querySelector("#aria-notify-area")||n("aria-notify-area")}},860:(e,r,t)=>{"use strict";var n=t(4836);Object.defineProperty(r,"__esModule",{value:!0}),r.ZoomSDKErrorDict=r.I18nLoader=void 0,t(7476),t(6253),t(851),t(5115),t(110),t(9357);var o=n(t(861)),a=n(t(8698)),i=n(t(6690)),s=n(t(9728)),c=n(t(8416)),u=t(9480),d=t(7848);r.ZoomSDKErrorDict={"en-US":{"apac.errorcodes_another_running":"Another meeting running","apac.errorcodes_be_removed":"Be removed.","apac.errorcodes_denied_email":"Email is blocked by Administrator.","apac.errorcodes_panelist_tk":"require tk parameter to join as a panelist","apac.errorcodes_denied_register_with_panelist":"Denied register use panelist email.","apac.errorcodes_disconnect":"Meeting has been disconnected.","apac.errorcodes_email_require":"Email is missing to attend webinar.","apac.errorcodes_fail":"Fail to join the meeting.","apac.errorcodes_frequent_call":"You have reached the API limit for this call.","apac.errorcodes_frequent_join":"You be limit by zoom, need to check recaptcha.","apac.errorcodes_login_required":"Require login","apac.errorcodes_need_use_zoom_desktop_or_mobile":"Unable join from browser, please join using the Zoom desktop client or mobile app.","apac.errorcodes_not_exist":"Meeting does not exist.","apac.errorcodes_not_host":"You are not the meeting host.","apac.errorcodes_not_init":"Meeting not initialized.","apac.errorcodes_not_start":"Meeting has not started","apac.errorcodes_not_start_webinar":"Webinar has not started","apac.errorcodes_offline":"The service is temporarily offline.","apac.errorcodes_pac_api_disabled":"The accounts API Key is deactivated.","apac.errorcodes_pac_api_wrong":"The accounts API Key is not valid.","apac.errorcodes_pac_cant_host_other_mn":"Cannot not host another meeting concurrently.","apac.errorcodes_pac_host_not_found":"The meeting host is not found.","apac.errorcodes_pac_invalid_signature":"Signature is invalid.","apac.errorcodes_pac_mn_not_fount":"The meeting number is not found.","apac.errorcodes_pac_mn_wrong":"The meeting number is wrong.","apac.errorcodes_pac_no_permission":"No permission.","apac.errorcodes_pac_role_error":"Incorrect role.","apac.errorcodes_pac_signature_expired":"The signature has expired.","apac.errorcodes_re_connect":"Meeting is reconnecting.","apac.errorcodes_register":"This meeting requires registration.","apac.errorcodes_rwc_empty":"Could not get a response from the web server.","apac.errorcodes_rwc_error":"Could not connect to Web Server error.","apac.errorcodes_sign_webinar":"Dont' support sign in webinar","apac.errorcodes_success":"Successfully joined the meeting.","apac.errorcodes_tk_expired":"Token has expired.","apac.errorcodes_update":"You cant join, you need update lastest WebSDK version","apac.errorcodes_wasm_fail":"Download wasm files error, please check your network and firewall.","apac.errorcodes_web_has_in_progress":"Already has other meetings in progress.","apac.errorcodes_web_host_not_exit":"The meeting host does not exist.","apac.errorcodes_web_invalid_id":"Invalid meeting ID.","apac.errorcodes_web_not_allow_start_webinar_from_web":"Not allow to start webinar from web.","apac.errorcodes_web_not_support_registration_webinar":"Not support registration webinar","apac.errorcodes_web_not_support_tsp":"Not support start or join meeting from web, when you chose TSP as his audio in a meeting.","apac.errorcodes_web_not_support_webclient":"Not support start or join meeting from web.","apac.errorcodes_web_not_support_webinar_pac":"Not support webinar and personal audio conference","apac.errorcodes_web_require_email":"User email is required.","apac.errorcodes_web_should_support_webinar_with_latest_version":"The current sdk version doesn't support webinar, please upgrade to the latest version.","apac.errorcodes_meeting_websdk_version_need_update":"The current sdk version is no longer supported, please upgrade to the latest version.","apac.errorcodes_wrong_pass":"Meeting Passcode wrong.","apac.rmc.assistant_exist_warning":"You cannot control the meeting because another assistant is controlling this meeting.","apac.websdk_enforce_update_content":"Your app version needs to be {0} or higher to join this meeting. Please update to continue.","apac.invalid_parameter":"Invalid parameter","apac.errorcodes_host_inactive":"Meeting host is inactive.","apac.errorcodes_pac_not_allow_assistant":"Assistant join meeting been disabled","apac.errorcodes_participant_exist":"Participant exist","apac.errorcodes_reject_barriers":"Reject for information barriers","apac.dialog.meeting_capacity_reached":"Meeting capacity has been reached","apac.dialog.meeting_locked":"The meeting has been locked","apac.dialog.meeting_ended":"The meeting has been ended","apac.websdk_update_content":"Update MeetingSDK-Web to the latest version to optimize meeting experience","apac.errorcodes_need_token":"Join fail because you email no token","apac.dialog.server_error":"The server encountered an internal error and was unable to process your request.","apac.errorcodes_zak":"Token error","apac.errorcodes_zak2":"Token error","apac.errorcodes_host_email":"Please use host/alternative host to start the webinar.","apac.wc_cannot_join_content":"Your admin has restricted communication between certain groups and users.","apac.wc_cannot_join_content2":"You've already joined this meeting on another device.","apac.wc_cannot_join_title":"Unable to join this meeting","apac.errorcodes_not_allow_cross_join":"To join a meeting hosted by an external Zoom account, your SDK app has to be published on Zoom Marketplace. You can refer to Section 6.1 of Zoom's API License Terms of Use","apac.errorcodes_no_response_from_web":"No response from web, try again later."},"de-DE":{"apac.errorcodes_web_has_in_progress":"Hat bereits andere laufende Meetings.","apac.errorcodes_web_require_email":"E-Mail-Adresse von Benutzer erforderlich.","apac.errorcodes_web_not_support_webinar_pac":"Unterstützen Webinar und persönliche Audiokonferenz nicht","apac.errorcodes_web_host_not_exit":"Der Meeting-Host ist nicht vorhanden.","apac.errorcodes_web_not_support_webclient":"Start nicht unterstützt bzw. starten Sie das Meeting aus dem Web.","apac.errorcodes_web_should_support_webinar_with_latest_version":"Ihre aktuelle SDK-Version unterstützt keine Webinare. Aktualisieren Sie bitte auf die neueste Version.","apac.errorcodes_web_invalid_id":"Ungültige Meeting-ID.","apac.errorcodes_web_not_support_registration_webinar":"Registrierung für Webinar wird nicht unterstützt","apac.errorcodes_web_not_support_tsp":"Sie unterstützen nicht den Start oder die Teilnahme an einem Meeting aus dem Web, wenn Sie den Telefonieanbieter als seinen Audiobeitrag in einem Meeting gewählt haben.","apac.errorcodes_web_not_allow_start_webinar_from_web":"Das Starten eines Webinars vom Internet aus ist nicht zulässig.","apac.errorcodes_need_use_zoom_desktop_or_mobile":"Unfähig beitreten aus Browser, verbinden Sie bitte den Zoom-Desktop-Client oder mobile App.","apac.errorcodes_meeting_websdk_version_need_update":"Ihre aktuelle SDK-Version wird nicht mehr unterstützt. Aktualisieren Sie bitte auf die neueste Version."},"es-ES":{"apac.errorcodes_web_has_in_progress":"Ya posee otras reuniones en curso.","apac.errorcodes_web_require_email":"Se requiere correo electrónico del usuario.","apac.errorcodes_web_not_support_webinar_pac":"No soporta seminario web o conferencia de audio personal","apac.errorcodes_web_host_not_exit":"El anfitrión de la reunión no existe.","apac.errorcodes_web_not_support_webclient":"No se puede iniciar ni entrar a una reunión desde la web.","apac.errorcodes_web_should_support_webinar_with_latest_version":"La versión actual de sdk no es compatible con el seminario web, actualice a la última versión.","apac.errorcodes_web_invalid_id":"ID de reunión no válida.","apac.errorcodes_web_not_support_registration_webinar":"Seminario web que no es compatible con la inscripción","apac.errorcodes_web_not_support_tsp":"Cuando eligió el proveedor de servicios telefónicos como audio en una reunión, no se puede iniciar o unirse una reunión desde el sitio web.","apac.errorcodes_web_not_allow_start_webinar_from_web":"No se permite comenzar un seminario web desde la web.","apac.errorcodes_need_use_zoom_desktop_or_mobile":"No puede unirse desde el navegador, por favor unirse usando el cliente de escritorio Zoom o aplicación móvil.","apac.errorcodes_meeting_websdk_version_need_update":"La versión actual de sdk ya no es compatible actualice a la versión más reciente."},"fr-FR":{"apac.errorcodes_web_has_in_progress":"Il a déjà d’autres réunions en cours.","apac.errorcodes_web_require_email":"Le courriel de l’utilisateur est requis.","apac.errorcodes_web_not_support_webinar_pac":"Ne prend pas en charge le webinaire ni la conférence audio personnelle","apac.errorcodes_web_host_not_exit":"L’animateur de la réunion n’existe pas.","apac.errorcodes_web_not_support_webclient":"Ne supporte pas démarrer ou rejoindre une réunion à partir du web.","apac.errorcodes_web_should_support_webinar_with_latest_version":"La version actuelle du SDK ne prend pas en charge le webinaire, veuillez passer à la dernière version.","apac.errorcodes_web_invalid_id":"Nº de réunion non valide.","apac.errorcodes_web_not_support_registration_webinar":"Ne prend pas en charge le webinaire d’inscription","apac.errorcodes_web_not_support_tsp":"Ne peut pas démarrer ou rejoindre la réunion depuis Internet, lorsque vous sélectionnez le fournisseur de service en téléphonie comma sa sortie audio dans une réunion.","apac.errorcodes_web_not_allow_start_webinar_from_web":"Ne pas permettre de démarrer des webinaires à partir du Web.","apac.errorcodes_need_use_zoom_desktop_or_mobile":"Impossible de rejoindre le navigateur, s'il vous plaît rejoindre en utilisant le client de bureau Zoom ou une application mobile.","apac.errorcodes_meeting_websdk_version_need_update":"La version actuelle du SDK n’est plus prise en charge, veuillez passer à la dernière version."},"ja-JP":{"apac.errorcodes_web_has_in_progress":"すでに他のミーティングが始まっています。","apac.errorcodes_web_require_email":"ユーザーメールは必要です。","apac.errorcodes_web_not_support_webinar_pac":"ウェビナーとパーソナル音声会議がサポートされていません","apac.errorcodes_web_host_not_exit":"このミーティングのホストは存在しません。","apac.errorcodes_web_not_support_webclient":"ウェブからのミーティング開始/参加はサポートされません。","apac.errorcodes_web_should_support_webinar_with_latest_version":"現在のSDKバージョンはウェビナーをサポートしていません。最新バージョンにアップグレードしてください。","apac.errorcodes_web_invalid_id":"無効なミーティングIDです。","apac.errorcodes_web_not_support_registration_webinar":"登録ウェビナー非対応","apac.errorcodes_web_not_support_tsp":"ミーティングのオーディオにテレフォニーサービスプロバイダを選択した場合、ウェブからミーティングを開始したり、ミーティングに参加したりできません。","apac.errorcodes_web_not_allow_start_webinar_from_web":"ウェビナーをウェブから開始できません。","apac.errorcodes_need_use_zoom_desktop_or_mobile":"できませんが、ブラウザから参加し、ズームデスクトップクライアントやモバイルアプリを使用して参加してください。","apac.errorcodes_meeting_websdk_version_need_update":"現在の SDK バージョンはサポートされていません。最新バージョンにアップグレードしてください。"},"pt-PT":{"apac.errorcodes_web_has_in_progress":"Já há outras reuniões em andamento.","apac.errorcodes_web_require_email":"O e-mail do usuário é obrigatório.","apac.errorcodes_web_not_support_webinar_pac":"Não há suporte para webinar e audioconferência pessoal","apac.errorcodes_web_host_not_exit":"O anfitrião da reunião não existe.","apac.errorcodes_web_not_support_webclient":"Não há suporte para iniciar ou entrar na reunião pela web.","apac.errorcodes_web_should_support_webinar_with_latest_version":"A versão sdk atual não oferece suporte para webinar. Atualize para a versão mais recente.","apac.errorcodes_web_invalid_id":"ID da reunião inválido.","apac.errorcodes_web_not_support_registration_webinar":"O registro do webinar não é compatível","apac.errorcodes_web_not_support_tsp":"Não é compatível iniciar ou ingressar na reunião pela web ao escolher o áudio do provedor de serviços de telefonia em uma reunião.","apac.errorcodes_web_not_allow_start_webinar_from_web":"Não é permitido iniciar o webinar na web.","apac.errorcodes_need_use_zoom_desktop_or_mobile":"Incapaz juntar-se a partir do navegador, por favor, junte-se usando o cliente de desktop Zoom ou aplicativo móvel.","apac.errorcodes_meeting_websdk_version_need_update":"O serviço de validação de número ABN está temporariamente indisponível."},"ru-RU":{"apac.errorcodes_web_has_in_progress":"Уже участвует в других конференциях.","apac.errorcodes_web_require_email":"Требуется почта пользователя.","apac.errorcodes_web_not_support_webinar_pac":"Не поддерживает веб-семинар и персональную аудиоконференцию","apac.errorcodes_web_host_not_exit":"Организатор конференции не существует.","apac.errorcodes_web_not_support_webclient":"Запуск или вход в конференцию через сеть не поддерживается.","apac.errorcodes_web_should_support_webinar_with_latest_version":"Текущая версия пакета SDK не поддерживает вебинары. Выполните обновление до последней версии.","apac.errorcodes_web_invalid_id":"Неверный идентификатор конференции.","apac.errorcodes_web_not_support_registration_webinar":"Регистрация на вебинар не поддерживается","apac.errorcodes_web_not_support_tsp":"Если для аудиоконференции используется поставщик службы телефонии, такую конференцию невозможно начать или подключиться к ней через браузер.","apac.errorcodes_web_not_allow_start_webinar_from_web":"Запуск веб-семинара через Интернет запрещен.","apac.errorcodes_need_use_zoom_desktop_or_mobile":"Невозможно присоединиться из браузера, пожалуйста, присоединяйтесь с помощью настольного клиента Увеличить или мобильное приложение.","apac.errorcodes_meeting_websdk_version_need_update":"Текущая версия пакета SDK больше не поддерживается. Выполните обновление до последней версии."},"zh-CN":{"apac.errorcodes_web_has_in_progress":"已经有另一场会议正在进行。","apac.errorcodes_web_require_email":"必须提供用户电子邮件地址。","apac.errorcodes_web_not_support_webinar_pac":"非支持的网络研讨会和个人音频会议","apac.errorcodes_web_host_not_exit":"会议主持人不存在。","apac.errorcodes_web_not_support_webclient":"不支持从 Web 开始或加入会议。","apac.errorcodes_web_should_support_webinar_with_latest_version":"当前 sdk 版本不支持网络研讨会，请升级到最新版本。","apac.errorcodes_web_invalid_id":"无效的会议ID。","apac.errorcodes_web_not_support_registration_webinar":"不支持注册网络研讨会","apac.errorcodes_web_not_support_tsp":"如果选择电话语音服务提供商作为会议音频，则不支持通过网络开始或加入会议。","apac.errorcodes_web_not_allow_start_webinar_from_web":"不允许通过网络开始网络研讨会","apac.errorcodes_need_use_zoom_desktop_or_mobile":"从浏览器无法加入，请加入使用缩放桌面客户端或移动应用程序。","apac.errorcodes_meeting_websdk_version_need_update":"当前SDK版本不再受到支持，请升级到最新版本。"},"zh-TW":{"apac.errorcodes_web_has_in_progress":"已有其他會議正在進行中。","apac.errorcodes_web_require_email":"必須提供使用者電子郵件地址。","apac.errorcodes_web_not_support_webinar_pac":"不支援網路研討會和個人音訊會議","apac.errorcodes_web_host_not_exit":"會議主持人不存在。","apac.errorcodes_web_not_support_webclient":"不支援從 Web 開始或加入會議。","apac.errorcodes_web_should_support_webinar_with_latest_version":"目前的 SDK 版本不支援網路研討會，請升級至最新版本。","apac.errorcodes_web_invalid_id":"無效的會議 ID 。","apac.errorcodes_web_not_support_registration_webinar":"不支援註冊網路研討會","apac.errorcodes_web_not_support_tsp":"當您選擇電話語音服務供應商做為會議語音使用時，不支援經由網頁召開或加入會議。","apac.errorcodes_web_not_allow_start_webinar_from_web":"不允許從 Web 開始網路研討會。","apac.errorcodes_need_use_zoom_desktop_or_mobile":"從瀏覽器無法加入，請加入使用縮放桌面客戶端或移動應用程序。","apac.errorcodes_meeting_websdk_version_need_update":"目前的SDK版本已不再受到支援，請升級至最新版本。"},"ko-KR":{"apac.errorcodes_web_has_in_progress":"이미 다른 회의가 진행 중입니다.","apac.errorcodes_web_require_email":"사용자 이메일이 필요합니다.","apac.errorcodes_web_not_support_webinar_pac":"웨비나 및 개인 오디오 전화 회의를 지원하지 않음","apac.errorcodes_web_host_not_exit":"회의 호스트가 없습니다.","apac.errorcodes_web_not_support_webclient":"웹에서 회의를 시작하거나 참여하는 것은 지원하지 않습니다.","apac.errorcodes_web_should_support_webinar_with_latest_version":"현재 SDK 버전은 웨비나를 지원하지 않습니다. 최신 버전으로 업그레이드하십시오.","apac.errorcodes_web_invalid_id":"회의 ID가 잘못되었습니다.","apac.errorcodes_web_not_support_registration_webinar":"등록 웨비나를 지원하지 않음","apac.errorcodes_web_not_support_tsp":"회의에서 Telephony Service Provider를 오디오로 선택한 경우 웹에서 회의를 시작하거나 회의에 참가할 수 없습니다.","apac.errorcodes_web_not_allow_start_webinar_from_web":"웹에서 웨비나를 시작할 수 없습니다.","apac.errorcodes_need_use_zoom_desktop_or_mobile":"수 없습니다 브라우저에서 조인 줌 데스크톱 클라이언트 또는 모바일 앱을 사용하여 가입하시기 바랍니다.","apac.errorcodes_meeting_websdk_version_need_update":"현재 SDK 버전은 더 이상 지원되지 않습니다. 최신 버전으로 업그레이드하십시오."},"vi-VN":{"apac.errorcodes_web_has_in_progress":"Đã có cuộc họp khác đang diễn ra.","apac.errorcodes_web_require_email":"Email người dùng là bắt buộc.","apac.errorcodes_web_not_support_webinar_pac":"Không hỗ trợ hội thảovideo và hội nghị âm thanh cá nhân","apac.errorcodes_web_host_not_exit":"Người chủ trì cuộc họp không tồn tại.","apac.errorcodes_web_not_support_webclient":"Không hỗ trợ bắt đầu hoặc vào cuộc họp từ web.","apac.errorcodes_web_should_support_webinar_with_latest_version":"Phiên bản sdk hiện tại không hỗ trợ hội thảo trực tuyến, hãy nâng cấp lên phiên bản mới nhất.","apac.errorcodes_web_invalid_id":"ID cuộc họp không hợp lệ.","apac.errorcodes_web_not_support_registration_webinar":"Không hỗ trợ đăng ký hội thảo trực tuyến","apac.errorcodes_web_not_support_tsp":"Không hỗ trợ bắt đầu hoặc vào cuộc họp từ web, khi bạn chọn Nhà cung cấp dịch vụ điện thoại làm nguồn âm thanh trong cuộc họp.","apac.errorcodes_web_not_allow_start_webinar_from_web":"Không cho phép bắt đầu hội thảo trực tuyến từ web.","apac.errorcodes_need_use_zoom_desktop_or_mobile":"Không thể tham gia từ trình duyệt, hãy tham gia bằng cách sử dụng client desktop Phóng to hoặc ứng dụng di động.","apac.errorcodes_meeting_websdk_version_need_update":"Phiên bản sdk hiện tại không còn được hỗ trợ, vui lòng nâng cấp lên phiên bản mới nhất."},"it-IT":{"apac.errorcodes_web_has_in_progress":"Sta partecipando a un’altra riunione in corso.","apac.errorcodes_web_require_email":"È richiesta l’email dell’utente.","apac.errorcodes_web_not_support_webinar_pac":"Non supporta webinar e conferenza audio personale","apac.errorcodes_web_host_not_exit":"L’ospite della riunione non esiste.","apac.errorcodes_web_not_support_webclient":"Non supporta l’avvio o la partecipazione alla riunione dal Web.","apac.errorcodes_web_should_support_webinar_with_latest_version":"La versione sdk corrente non supporta i webinar. Effettua l’aggiornamento alla versione più recente.","apac.errorcodes_web_invalid_id":"ID riunione non valido.","apac.errorcodes_web_not_support_registration_webinar":"Non supporta webinar con iscrizione","apac.errorcodes_web_not_support_tsp":"In caso che hai selezionato il fornitore di servizio telefonico come l’audio in riunione, non supporta l’avvio o la partecipazione alla riunione dal Web.","apac.errorcodes_web_not_allow_start_webinar_from_web":"Non è consentito avviare il webinar dal Web.","apac.errorcodes_need_use_zoom_desktop_or_mobile":"Impossibile unirsi dal browser, si prega di unirsi con il client desktop Zoom o app mobile.","apac.errorcodes_meeting_websdk_version_need_update":"La versione sdk corrente non è più supportata. Aggiorna alla versione più recente."},"id-ID":{"apac.errorcodes_web_has_in_progress":"Sudah mengadakan pertemuan lain.","apac.errorcodes_web_require_email":"Email pengguna diperlukan.","apac.errorcodes_web_not_support_webinar_pac":"Tidak mendukung konferensi webinar dan audio pribadi","apac.errorcodes_web_host_not_exit":"Tuan rumah pertemuan tidak ada.","apac.errorcodes_web_not_support_webclient":"Tidak mendukung mulai atau bergabung dengan rapat dari web.","apac.errorcodes_web_should_support_webinar_with_latest_version":"Versi SDK saat ini tidak mendukung webinar, silakan tingkatkan ke versi terbaru.","apac.errorcodes_web_invalid_id":"ID pertemuan tidak valid.","apac.errorcodes_web_not_support_registration_webinar":"Tidak mendukung webinar pendaftaran","apac.errorcodes_web_not_support_tsp":"Tidak mendukung mulai atau bergabung dengan rapat dari web, saat Anda memilih TSP sebagai audionya dalam rapat.","apac.errorcodes_web_not_allow_start_webinar_from_web":"Tidak mengizinkan untuk memulai webinar dari web.","apac.errorcodes_need_use_zoom_desktop_or_mobile":"Tidak dapat bergabung dari browser, silakan bergabung menggunakan klien zoom desktop atau aplikasi seluler.","apac.errorcodes_meeting_websdk_version_need_update":"Versi sdk saat ini tidak lagi didukung. Tingkatkan ke versi terbaru."},"nl-NL":{"apac.errorcodes_web_has_in_progress":"Heeft al andere vergaderingen in uitvoering.","apac.errorcodes_web_require_email":"E -mail van gebruikers is vereist.","apac.errorcodes_web_not_support_webinar_pac":"Ondersteun niet webinar en persoonlijke audioconferentie","apac.errorcodes_web_host_not_exit":"De vergadergastheer bestaat niet.","apac.errorcodes_web_not_support_webclient":"Ondersteuning niet starten of deelnemen aan de vergadering van Web.","apac.errorcodes_web_should_support_webinar_with_latest_version":"De huidige SDK -versie ondersteunt Webinar niet, upgrade naar de nieuwste versie.","apac.errorcodes_web_invalid_id":"Ongeldige vergadering -ID.","apac.errorcodes_web_not_support_registration_webinar":"Ondersteun geen registratie -webinar","apac.errorcodes_web_not_support_tsp":"Start niet of deel aan de vergadering van Web, wanneer u TSP koos als zijn audio tijdens een vergadering.","apac.errorcodes_web_not_allow_start_webinar_from_web":"Niet toestaan ​​om webinar te starten vanaf het web.","apac.errorcodes_need_use_zoom_desktop_or_mobile":"Niet in staat om mee te doen vanuit de browser, doe mee met behulp van de Zoom Desktop -client of mobiele app.","apac.errorcodes_meeting_websdk_version_need_update":"De huidige SDK-versie wordt niet langer ondersteund. Voer een upgrade uit naar de nieuwste versie."},"tr-TR":{"apac.errorcodes_web_has_in_progress":"Zaten devam eden başka toplantılara sahip.","apac.errorcodes_web_require_email":"Kullanıcı e-postası gereklidir.","apac.errorcodes_web_not_support_webinar_pac":"Web'in ve kişisel ses konferansını desteklememek","apac.errorcodes_web_host_not_exit":"Toplantı ana bilgisayar mevcut değil.","apac.errorcodes_web_not_support_webclient":"Desteklemeyi denememek veya web'den toplantıya katılın.","apac.errorcodes_web_should_support_webinar_with_latest_version":"Mevcut SDK sürümü WebInar'ı desteklemiyor, lütfen en son sürüme yükseltin.","apac.errorcodes_web_invalid_id":"Geçersiz Toplantı Kimliği.","apac.errorcodes_web_not_support_registration_webinar":"Kayıt web seminerini desteklememek","apac.errorcodes_web_not_support_tsp":"TSP'yi bir toplantıda ses olarak seçtiğinizde, web'den toplantıyı başlatmayın veya katılmayın.","apac.errorcodes_web_not_allow_start_webinar_from_web":"Web'in web'den başlamasına izin vermeyin.","apac.errorcodes_need_use_zoom_desktop_or_mobile":"Tarayıcıdan katılamıyorsanız, lütfen Zoom Masaüstü İstemcisi veya Mobil Uygulamayı kullanarak katılın.","apac.errorcodes_meeting_websdk_version_need_update":"Mevcut sdk sürümü artık desteklenmiyor, lütfen en son sürüme yükseltin."},"pl-PL":{"apac.errorcodes_web_has_in_progress":"Ma już inne spotkania.","apac.errorcodes_web_require_email":"Wymagany jest e-mail użytkownika.","apac.errorcodes_web_not_support_webinar_pac":"Nie obsługuje seminarium i osobistej konferencji audio","apac.errorcodes_web_host_not_exit":"Gospodarz spotkania nie istnieje.","apac.errorcodes_web_not_support_webclient":"Nie obsługuje uruchomienia ani dołączania do spotkania z sieci.","apac.errorcodes_web_should_support_webinar_with_latest_version":"Aktualna wersja SDK nie obsługuje webinarium, proszę uaktualnić do najnowszej wersji.","apac.errorcodes_web_invalid_id":"Nieprawidłowy identyfikator spotkania.","apac.errorcodes_web_not_support_registration_webinar":"Nie wspieraj rejestracji webinarium internetowego","apac.errorcodes_web_not_support_tsp":"Nie obsługuje uruchomienia ani dołączania do spotkania z Internetu, gdy wybrałeś TSP jako jego audio na spotkaniu.","apac.errorcodes_web_not_allow_start_webinar_from_web":"Nie pozwól, aby rozpocząć sieć webinarną od sieci.","apac.errorcodes_need_use_zoom_desktop_or_mobile":"Nie można dołączyć z przeglądarki, dołącz do Klienta Klient Desktop lub Mobile App.","apac.errorcodes_meeting_websdk_version_need_update":"Bieżąca wersja sdk nie jest już obsługiwana; uaktualnij do najnowszej wersji."},"sv-SE":{"apac.errorcodes_another_running":"Ett annat möte pågår","apac.errorcodes_be_removed":"Bli borttagen.","apac.errorcodes_denied_email":"E-post är blockerad av administratören.","apac.errorcodes_panelist_tk":"kräver tk-parameter för att gå med som paneldeltagare","apac.errorcodes_denied_register_with_panelist":"Nekad registrering med paneldeltagare e-post.","apac.errorcodes_disconnect":"Mötet har kopplats bort.","apac.errorcodes_email_require":"E-post saknas för att delta i webbseminariet.","apac.errorcodes_fail":"Misslyckades med att ansluta till mötet.","apac.errorcodes_frequent_call":"Du har nått API-begränsningen för detta samtal.","apac.errorcodes_frequent_join":"Du är begränsad av Zoom, behöver kontrollera reCAPTCHA.","apac.errorcodes_login_required":"Inloggning krävs","apac.errorcodes_need_use_zoom_desktop_or_mobile":"Kan inte ansluta från webbläsare, vänligen använd Zoom desktop-klienten eller mobilappen.","apac.errorcodes_not_exist":"Mötet finns inte.","apac.errorcodes_not_host":"Du är inte mötesvärden.","apac.errorcodes_not_init":"Mötet är inte initierat.","apac.errorcodes_not_start":"Mötet har inte startat","apac.errorcodes_not_start_webinar":"Webbseminariet har inte startat","apac.errorcodes_offline":"Tjänsten är tillfälligt offline.","apac.errorcodes_pac_api_disabled":"Konto-API-nyckeln är inaktiverad.","apac.errorcodes_pac_api_wrong":"Konto-API-nyckeln är ogiltig.","apac.errorcodes_pac_cant_host_other_mn":"Kan inte vara värd för ett annat möte samtidigt.","apac.errorcodes_pac_host_not_found":"Mötesvärden hittas inte.","apac.errorcodes_pac_invalid_signature":"Signaturen är ogiltig.","apac.errorcodes_pac_mn_not_fount":"Mötesnumret hittas inte.","apac.errorcodes_pac_mn_wrong":"Mötesnumret är felaktigt.","apac.errorcodes_pac_no_permission":"Ingen behörighet.","apac.errorcodes_pac_role_error":"Felaktig roll.","apac.errorcodes_pac_signature_expired":"Signaturen har gått ut.","apac.errorcodes_re_connect":"Mötet återansluter.","apac.errorcodes_register":"Detta möte kräver registrering.","apac.errorcodes_rwc_empty":"Kunde inte få svar från webbservern.","apac.errorcodes_rwc_error":"Kunde inte ansluta till webbservern, fel.","apac.errorcodes_sign_webinar":"Stöder inte inloggning i webbseminarium","apac.errorcodes_success":"Lyckades ansluta till mötet.","apac.errorcodes_tk_expired":"Token har gått ut.","apac.errorcodes_update":"Du kan inte ansluta, du behöver uppdatera till senaste WebSDK-versionen","apac.errorcodes_wasm_fail":"Nedladdning av wasm-filer misslyckades, kontrollera ditt nätverk och brandvägg.","apac.errorcodes_web_has_in_progress":"Andra möten pågår redan.","apac.errorcodes_web_host_not_exit":"Mötesvärden finns inte.","apac.errorcodes_web_invalid_id":"Ogiltigt mötes-ID.","apac.errorcodes_web_not_allow_start_webinar_from_web":"Tillåter inte start av webbseminarium från webben.","apac.errorcodes_web_not_support_registration_webinar":"Stöder inte registrering av webbseminarium","apac.errorcodes_web_not_support_tsp":"Stöder inte start eller anslutning till möte från webben när du valt TSP som ljud i ett möte.","apac.errorcodes_web_not_support_webclient":"Stöder inte start eller anslutning till möte från webben.","apac.errorcodes_web_not_support_webinar_pac":"Stöder inte webbseminarium och personligt ljudkonferens","apac.errorcodes_web_require_email":"Användarens e-post krävs.","apac.errorcodes_web_should_support_webinar_with_latest_version":"Den nuvarande sdk-versionen stöder inte webbseminarium, vänligen uppgradera till den senaste versionen.","apac.errorcodes_meeting_websdk_version_need_update":"Den nuvarande sdk-versionen stöds inte längre, vänligen uppgradera till den senaste versionen.","apac.errorcodes_wrong_pass":"Fel möteslösenord.","apac.rmc.assistant_exist_warning":"Du kan inte kontrollera mötet eftersom en annan assistent kontrollerar detta möte.","apac.websdk_enforce_update_content":"Din appversion måste vara {0} eller högre för att delta i detta möte. Vänligen uppdatera för att fortsätta.","apac.invalid_parameter":"Ogiltig parameter","apac.errorcodes_host_inactive":"Mötesvärden är inaktiv.","apac.errorcodes_pac_not_allow_assistant":"Assistenten att ansluta till mötet har inaktiverats","apac.errorcodes_participant_exist":"Deltagare finns","apac.errorcodes_reject_barriers":"Avvisad på grund av informationsbarriärer","apac.dialog.meeting_capacity_reached":"Möteskapaciteten har nåtts","apac.dialog.meeting_locked":"Mötet är låst","apac.dialog.meeting_ended":"Mötet har avslutats","apac.websdk_update_content":"Uppdatera MeetingSDK-Web till den senaste versionen för att optimera mötesupplevelsen","apac.errorcodes_need_token":"Anslutningen misslyckades eftersom din e-post saknar token","apac.dialog.server_error":"Servern stötte på ett internt fel och kunde inte behandla din begäran.","apac.errorcodes_zak":"Token-fel","apac.errorcodes_zak2":"Token-fel","apac.errorcodes_host_email":"Vänligen använd värd/alternativ värd för att starta webbseminariet.","apac.wc_cannot_join_content":"Din administratör har begränsat kommunikationen mellan vissa grupper och användare.","apac.wc_cannot_join_content2":"Du har redan anslutit till detta möte på en annan enhet.","apac.wc_cannot_join_title":"Kan inte ansluta till detta möte","apac.errorcodes_not_allow_cross_join":"För att delta i ett möte som arrangeras av ett externt Zoom-konto måste din SDK-app publiceras på Zoom Marketplace. Du kan hänvisa till avsnitt 6.1 i Zooms API-licensvillkor","apac.errorcodes_no_response_from_web":"Inget svar från webben, försök igen senare."}},r.I18nLoader=(0,s.default)((function e(r){var t=this;(0,i.default)(this,e),(0,c.default)(this,"dict",{}),(0,c.default)(this,"currentLang",""),(0,c.default)(this,"userLangs",{}),(0,c.default)(this,"supportLangArray",["de-DE","es-ES","en-US","fr-FR","ja-JP","jp-JP","pt-PT","ru-RU","zh-CN","zh-TW","ko-KR","ko-KO","vi-VN","it-IT","pl-PL","tr-TR","id-ID","nl-NL","sv-SE"]),(0,c.default)(this,"loadAll",(function(){})),(0,c.default)(this,"getSupportLanguage",(function(){return t.supportLangArray})),(0,c.default)(this,"setSupportLanguage",(function(){console.warn("3.0.0 you can remove this api")})),(0,c.default)(this,"getCurrentLangList",(function(){return Object.keys(t.userLangs)})),(0,c.default)(this,"getLangJson",(function(e,r){return new Promise((function(n,o){var a;a=window.ActiveXObject?new ActiveXObject("Microsoft.XMLHTTP"):new XMLHttpRequest;var i=t;a.onreadystatechange=function(){if(4===this.readyState&&200===this.status)try{var t=JSON.parse(this.responseText);i.userLangs[r]=Object.assign({},i.userLangs[r],t),this.dict=t,n(t)}catch(r){console.warn("This link isn't a correct json file, load error, please check again,",e),o(r)}},a.open("GET",e,!0),a.send()}))})),(0,c.default)(this,"load",(function(e,r){var n=r,o=e,i="jp-JP is deprecated, use ja-JP instead since 4.0.0, will not accept jp-JP in 5.0.0.",s="ko-KO is deprecated, use ko-KR instead since 4.0.0, will not accept ko-KO in 5.0.0.";"ko-KO"===e?(o="ko-KR",console.warn(s)):"ja-JP"===e&&(o="ja-JP",console.warn(i)),"ko-KO"===r?(n="ko-KR",console.warn(s)):"jp-JP"===r&&(n="ja-JP",console.warn(i));var c="en-US";return o&&!n&&"string"==typeof o?c=o:n&&"string"==typeof n&&(c=n),t.currentLang=c,new Promise((function(e){if(o&&"object"===(0,a.default)(o))t.dict=o,e(o);else if("websdk"===d.PLATFORM_TYPE.WEB_SDK){if(t.userLangs[c])return t.dict=t.userLangs[c],void e(t.userLangs[c]);var r=(0,u.getZoomLangPath)();console.log("langDir",r);var n="".concat(r,"/").concat(c,".json");"ko-KR"===c||"ko-KO"===c?n="".concat(r,"/","ko-KO",".json"):"ja-JP"!==c&&"jp-JP"!==c||(n="".concat(r,"/","jp-JP",".json")),t.getLangJson(n,c).then((function(r){console.log("load success",n),t.dict=r,e(r)})).catch((function(){console.log("load error",n),e()}))}else console.log("load error",tmpLangJsonUrl),e()}))})),(0,c.default)(this,"reload",(function(){console.warn("3.0.0 you can remove this api")})),(0,c.default)(this,"getCurrentLang",(function(){return t.currentLang})),(0,c.default)(this,"setCurrentLang",(function(){console.warn("3.0.0 you can remove this api")})),(0,c.default)(this,"get",(function(){for(var e=arguments.length,r=new Array(e),n=0;n<e;n++)r[n]=arguments[n];var a=r[0],i=r.slice(1),s=t.print,c=t.dict,u="";return c&&c.hasOwnProperty(a)&&(u=t.dict[a]),1===r.length?u:s.apply(void 0,[u].concat((0,o.default)(i)))})),(0,c.default)(this,"print",(function(){for(var e=arguments.length,r=new Array(e),t=0;t<e;t++)r[t]=arguments[t];var n=r[0],o=r[1];if(r.length<2)return n;var a=Array.isArray(o)?o:r.slice(1);return n.replace(/\{(\d+)\}/g,(function(e,r){return void 0!==a[r]?a[r]:e}))})),"websdk"!==d.PLATFORM_TYPE.WEB_SDK&&this.load(r)}),[{key:"printf",value:function(e){for(var r=arguments.length,t=new Array(r>1?r-1:0),n=1;n<r;n++)t[n-1]=arguments[n];if(arguments.length<2)return e;var o=Array.isArray(t)?t:sliceFunc.call(t,1);return e.replace(/\{(\d+)\}/g,(function(e,r){return void 0!==o[parseInt(r,10)]?o[parseInt(r,10)]:e}))}},{key:"getAll",value:function(e){return e in this.userLangs?this.userLangs[e]:{}}},{key:"onLoad",value:function(e){var r=this;if(e&&"function"==typeof e)if(this.dict&&Object.keys(this.dict).length>0)e("success");else var t=setTimeout((function(){clearInterval(n),e("timeout")}),3e3),n=setInterval((function(){r.dict&&Object.keys(r.dict).length>0&&(clearInterval(n),clearTimeout(t),e("success"))}),100);else console.warn("callback is not a function",e)}}])},7848:(e,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.PLATFORM_TYPE=void 0,r.PLATFORM_TYPE={WEB_SDK:"websdk",WEB_CLIENT:"webclient"}},9480:(e,r,t)=>{"use strict";t(7476),t(5767),t(8837),t(4882),t(8351);var n=t(4836);Object.defineProperty(r,"__esModule",{value:!0}),r.getHelperLangPath=function(){return f},r.getZoomAudioPath=function(){return _},r.getZoomClientEnv=function(){return s},r.getZoomDomainList=void 0,r.getZoomImgPath=function(){return m},r.getZoomJSAVLib=function(){return l},r.getZoomJSLib=function(){return p},r.getZoomJsFolder=function(){return d+"/"},r.getZoomLangPath=function(){return f},r.getZoomMediaJSAVLib=function(){return u},r.getZoomMediaJSLib=function(){return c},r.getZoomVBPath=function(){return h},r.getZoomWasmLib=function(){return b},r.getZoomWorkPath=function(){return b},r.jsmediaVersion=void 0,r.setExternalLinkPage=function(e){},r.setZoomJSLib=y,r.setZoomMediaJSLib=function(e,r){u=r;var t=c=e;b=i({basePath:t,jsmediaWorkerPath:g},w(t)),h="".concat(t,"/vb-resource")},r.zoomJSLib=void 0;var o=n(t(8416));function a(e,r){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);r&&(n=n.filter((function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable}))),t.push.apply(t,n)}return t}function i(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{};r%2?a(Object(t),!0).forEach((function(r){(0,o.default)(e,r,t[r])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):a(Object(t)).forEach((function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))}))}return e}t(1876);var s="production",c="",u="",d="https://source.zoom.us/4.0.0/lib/ui",p=r.zoomJSLib="https://source.zoom.us/4.0.0/lib",l="/av",_="".concat("https://source.zoom.us/4.0.0/lib","/audio"),f="".concat("https://source.zoom.us/4.0.0/lib","/lang"),m="".concat("https://source.zoom.us/4.0.0/lib","/image"),h="".concat("https://source.zoom.us/4.0.0/lib","/av/vb-resource"),v={login:{"zoomdev.us":"devapps.zoomdev.us","zoomdev.com.cn":"devapps.zoomdev.us","dev-integration.zoomdev.us":"devapps.zoomdev.us","deva.zoomdev.us":"deva.zoomdev.us","local.zoom.us":"deva.zoomdev.us","devep.zoomdev.us":"devepapps.zoomdev.us","zoom.us":"applications.zoom.us","go.zoom.us":"goapplications.zoom.us",default:"applications.zoom.us"},webEndpoint:"zoom.us"};r.getZoomDomainList=function(){return v},r.jsmediaVersion=1501;var g="".concat(p).concat(l,"/js_media.min.js"),b={basePath:"".concat(p).concat(l),audioWorkerPath:"".concat(p).concat(l,"/js_audio_process.min.js"),audioWorkletPath:"".concat(p).concat(l,"/js_audio_worklet.min.js"),audioWorkletProcessPath:"".concat(p).concat(l,"/js_audio_worklet_process.min.js"),audioWorkletSIMDPath:"".concat(p).concat(l,"/js_audio_worklet_simd.min.js"),audioLevelWorkletPath:"".concat(p).concat(l,"/js_audio_level_worklet_process.min.js"),sharingAudioWorkletPath:"".concat(p).concat(l,"/js_sharing_audio_worklet.min.js"),audioWasm:"".concat(p).concat(l,"/audio.encode.wasm"),videoWorkerPath:"".concat(p).concat(l,"/video_s.min.js"),videoMtWorkerPath:"".concat(p).concat(l,"/video_m.min.js"),videoWasm:"".concat(p).concat(l,"/video.decode.wasm"),videoMtWasm:"".concat(p).concat(l,"/video.mt.wasm"),sharingWorkerPath:"".concat(p).concat(l,"/sharing_s.min.js"),sharingMtWorkerPath:"".concat(p).concat(l,"/sharing_m.min.js"),videoSIMDWorkerPath:"".concat(p).concat(l,"/video_simd.min.js"),videoSIMDWasm:"".concat(p).concat(l,"/video.simd.wasm"),sharingSIMDWorkerPath:"".concat(p).concat(l,"/sharing_simd.min.js"),videoMSIMDWasm:"".concat(p).concat(l,"/video.mtsimd.wasm"),sharingMSIMDWorkerPath:"".concat(p).concat(l,"/sharing_mtsimd.min.js"),videoMSIMDWorkerPath:"".concat(p).concat(l,"/video_mtsimd.min.js"),audioSIMDWorkletPath:"".concat(p).concat(l,"/audio_simd.min.js"),audioSIMDWasm:"".concat(p).concat(l,"/audio.simd.wasm"),audioSipWorkerPath:"".concat(p).concat(l,"/websipclient.min.js"),vsmiworkerpath:"".concat(p).concat(l,"/video_share_mtsimd.min.js"),netThreadPath:"".concat(p).concat(l,"/net_thread.min.js")};function w(e){return{audioWorkerPath:"".concat(e,"/js_audio_process.min.js"),audioWorkletPath:"".concat(e,"/js_audio_worklet.min.js"),audioWorkletProcessPath:"".concat(e,"/js_audio_worklet_process.min.js"),audioWorkletSIMDPath:"".concat(e,"/js_audio_worklet_simd.min.js"),audioLevelWorkletPath:"".concat(e,"/js_audio_level_worklet_process.min.js"),sharingAudioWorkletPath:"".concat(e,"/js_sharing_audio_worklet.min.js"),audioWasm:"".concat(e,"/audio.encode.wasm"),videoWorkerPath:"".concat(e,"/video_s.min.js"),videoMtWorkerPath:"".concat(e,"/video_m.min.js"),videoWasm:"".concat(e,"/video.decode.wasm"),videoMtWasm:"".concat(e,"/video.mt.wasm"),sharingWorkerPath:"".concat(e,"/sharing_s.min.js"),sharingMtWorkerPath:"".concat(e,"/sharing_m.min.js"),videoSIMDWorkerPath:"".concat(e,"/video_simd.min.js"),videoSIMDWasm:"".concat(e,"/video.simd.wasm"),sharingSIMDWorkerPath:"".concat(e,"/sharing_simd.min.js"),videoMSIMDWasm:"".concat(e,"/video.mtsimd.wasm"),sharingMSIMDWorkerPath:"".concat(e,"/sharing_mtsimd.min.js"),videoMSIMDWorkerPath:"".concat(e,"/video_mtsimd.min.js"),audioSIMDWorkletPath:"".concat(e,"/audio_simd.min.js"),audioSIMDWasm:"".concat(e,"/audio.simd.wasm"),audioSipWorkerPath:"".concat(e,"/websipclient.min.js"),vsmiworkerpath:"".concat(e,"/video_share_mtsimd.min.js"),netThreadPath:"".concat(e,"/net_thread.min.js")}}function y(e,t,n){r.zoomJSLib=p=e||p,l=t||l,d=function(e){var r=e.split("/");return r.pop(),r.join("/")}(e)+"/ui",n&&(r.jsmediaVersion=n),g="".concat(p).concat(l,"/js_media.min.js"),b=i({basePath:"".concat(p).concat(l),jsmediaWorkerPath:g},w("".concat(p).concat(l))),_="".concat(p,"/audio"),f="".concat(p,"/lang"),m="".concat(p,"/image"),h="".concat(p).concat(l,"/vb-resource")}y("https://source.zoom.us/4.0.0/lib","/av")},4826:(e,r,t)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.b64DecodeUnicode=function(e){return decodeURIComponent(atob(e).split("").map((function(e){return"%"+("00"+e.charCodeAt(0).toString(16)).slice(-2)})).join(""))},r.b64EncodeUnicode=function(e){return btoa(encodeURIComponent(e).replace(/%([0-9A-F]{2})/g,(function(e,r){return String.fromCharCode("0x"+r)})))},r.iText=function(e,r,t){if(void 0===window.i18n)return window.i18n=new n.I18nLoader,e||r;var o=void 0!==t?window.i18n.get(r,t):window.i18n.get(r);return o&&o!==r?s(o):e||r},r.informWebSDK=function(e,r){a()||i()?window.localStorage.setItem("zm-"+e,JSON.stringify(r)):"undefined"!=typeof BroadcastChannel&&new BroadcastChannel("zm-"+e).postMessage(r)},r.isIE=a,r.isSafari=i,r.loadCSS=function(e){var r=document.createElement("link");r.href=e,r.type="text/css",r.rel="stylesheet",r.media="screen,print","function"==typeof document.body.append?document.getElementsByTagName("head")[0].append(r):document.getElementsByTagName("head")[0].appendChild(r)},r.loadScript=function(e,r){var t=document.createElement("script");t.type="text/javascript",t.readyState?t.onreadystatechange=function(){"loaded"!==t.readyState&&"complete"!==t.readyState||(t.onreadystatechange=null,"function"==typeof r&&r())}:t.onload=function(){"function"==typeof r&&r()},t.src=e,"function"==typeof document.body.append?document.getElementsByTagName("head")[0].append(t):document.getElementsByTagName("head")[0].appendChild(t)},r.parseQuery=function(){return function(){for(var e=window.location.href,r=e.substr(e.indexOf("?")),t={},n=("?"===r[0]?r.substr(1):r).split("&"),o=0;o<n.length;o+=1){var a=n[o].split("=");t[decodeURIComponent(a[0])]=decodeURIComponent(a[1]||"")}return t}()},r.stringFormat=function(){String.prototype.format||(String.prototype.format=function(){var e=arguments;return this.replace(/{(\d+)}/g,(function(r,t){return void 0!==e[t]?e[t]:r}))})},r.testTool=void 0,r.unicodeToChar=s,r.xssFilter=function(e){var r=e;return(r=(r=(r=(r=(r=(r=r.toString()).replace(/</g,"&lt;")).replace(/>/g,"&gt;")).replace(/ /g,"&nbsp;")).replace(/"/g,"&quot;")).replace(/'/g,"&#39;")).replace(/&lt;\/br&gt;/g,"</br>")},t(2850),t(2773),t(8837),t(7732),t(9357),t(1876),t(1466),t(6253),t(6108),t(7476),t(75),t(9371),t(110);var n=t(860);String.prototype.includes||Object.defineProperty(String.prototype,"includes",{enumerable:!1,value:function(e,r){return"number"!=typeof r&&(r=0),!(r+e.length>this.length)&&-1!==this.indexOf(e,r)}}),Array.prototype.includes||Object.defineProperty(Array.prototype,"includes",{enumerable:!1,value:function(e){return this.filter((function(r){return r===e})).length>0}}),Array.prototype.intersect||Object.defineProperty(Array.prototype,"intersect",{enumerable:!1,value:function(){for(var e=new Array,r={},t=0;t<arguments.length;t++)for(var n=0;n<arguments[t].length;n++){var o=arguments[t][n];r[o]?(r[o]=r[o]+1,r[o]===arguments.length&&e.push(o)):r[o]=1}return e}}),Object.defineProperty(Array.prototype,"uniquelize",{enumerable:!1,value:function(){for(var e={},r=[],t=0,n=this.length;t<n;t++)e[this[t]]||(e[this[t]]=1,r.push(this[t]));return r}}),String.prototype.startsWith||Object.defineProperty(Array.prototype,"startsWith",{enumerable:!1,value:function(e,r){return r=r||0,this.indexOf(e,r)===r}}),Object.defineProperty(Array.prototype,"union",{enumerable:!1,value:function(){for(var e=new Array,r={},t=0;t<arguments.length;t++)for(var n=0;n<arguments[t].length;n++){var o=arguments[t][n];r[o]||(r[o]=1,e.push(o))}return e}}),Object.defineProperty(Array.prototype,"minus",{enumerable:!1,value:function(e){for(var r=new Array,t={},n=0;n<e.length;n++)t[e[n]]=1;for(var o=0;o<this.length;o++)t[this[o]]||(t[this[o]]=1,r.push(this[o]));return r}});var o=r.testTool={randomNum:function(e,r){switch(arguments.length){case 1:return parseInt(Math.random()*e+1,10);case 2:return parseInt(Math.random()*(r-e+1)+e,10);default:return 0}},generateEmail:function(){return o.randomNum(1e3,9999)+o.detectOS()+o.getBrowserInfo()[0].split(".")[0].replace("/","")+"@zoom.us"},detectOS:function(){var e=navigator.userAgent,r="Win32"===navigator.platform||"Windows"===navigator.platform,t="Mac68K"===navigator.platform||"MacPPC"===navigator.platform||"Macintosh"===navigator.platform||"MacIntel"===navigator.platform;if(t)return"Mac";if("X11"===navigator.platform&&!r&&!t)return"Unix";if(String(navigator.platform).indexOf("Linux")>-1)return"Linux";if(r){if(e.indexOf("Windows NT 5.0")>-1||e.indexOf("Windows 2000")>-1)return"Win2000";if(e.indexOf("Windows NT 5.1")>-1||e.indexOf("Windows XP")>-1)return"WinXP";if(e.indexOf("Windows NT 5.2")>-1||e.indexOf("Windows 2003")>-1)return"Win2003";if(e.indexOf("Windows NT 6.0")>-1||e.indexOf("Windows Vista")>-1)return"WinVista";if(e.indexOf("Windows NT 6.1")>-1||e.indexOf("Windows 7")>-1)return"Win7";if(e.indexOf("Windows NT 10")>-1||e.indexOf("Windows 10")>-1)return"Win10"}return"other"},isSafari:function(){return/^((?!chrome|android).)*safari/i.test(navigator.userAgent)},detectIE:function(){var e=window.navigator.userAgent,r=e.indexOf("MSIE ");if(r>0)return"IE"+parseInt(e.substring(r+5,e.indexOf(".",r)),10);if(e.indexOf("Trident/")>0){var t=e.indexOf("rv:");return"IE"+parseInt(e.substring(t+3,e.indexOf(".",t)),10)}var n=e.indexOf("Edge/");return n>0&&"Edge"+parseInt(e.substring(n+5,e.indexOf(".",n)),10)},getBrowserInfo:function(){var e=navigator.userAgent.toLowerCase();if(e.indexOf("firefox")>0)return e.match(/firefox\/[\d.]+/gi);if(e.indexOf("safari")>0&&e.indexOf("chrome")<0){var r,t="safari/unknow";return t=e.match(/version\/[\d.]+/gi),r=e.match(/safari\/[\d.]+/gi),t&&(t=t.toString().replace("version","safari")),r&&(t=r.toString().replace("version","safari")),t}return o.detectIE()||(e.indexOf("chrome")>0?e.match(/chrome\/[\d.]+/gi):"other")},getRandomInt:function(e){return Math.floor(Math.random()*Math.floor(e))},isMobileDevice:function(){return navigator.userAgent.match(/Android/i)||navigator.userAgent.match(/webOS/i)||navigator.userAgent.match(/iPhone/i)||navigator.userAgent.match(/iPod/i)||navigator.userAgent.match(/BlackBerry/i)||navigator.userAgent.match(/Windows Phone/i)},parseQuery:function(){return function(){for(var e=window.location.href,r=e.substr(e.indexOf("?")),t={},n=("?"===r[0]?r.substr(1):r).split("&"),o=0;o<n.length;o+=1){var a=n[o].split("=");t[decodeURIComponent(a[0])]=decodeURIComponent(a[1]||"")}return t}()},serialize:function(e){var r=["env","lang","mn","userName","passWord","role","signature","version","webEndpoint","rwcEndpoint","email"],t=Array.intersect(r,Object.keys(e)),n=[];r.forEach((function(r){t.includes(r)&&n.push([r,e[r]])})),Object.keys(e).sort().forEach((function(r){t.includes(r)||n.push([r,e[r]])}));var o=function(e){var r=[];for(var t in e)void 0!==e[t][1]&&r.push(encodeURIComponent(e[t][0])+"="+encodeURIComponent(e[t][1]));return r.join("&")}(n);return o},extractHostname:function(e){return(e.indexOf("//")>-1?e.split("/")[2]:e.split("/")[0]).split(":")[0].split("?")[0]},getDomainName:function(e){return e.substring(e.lastIndexOf(".",e.lastIndexOf(".")-1)+1)},setCookie:function(e,r){var t=new Date;t.setTime(t.getTime()+864e5);var n="expires="+t.toUTCString(),o=this.getDomainName(this.extractHostname(window.location.href));document.cookie=e+"="+r+";"+n+";Domain=."+o+";path=/"},getCookie:function(e){for(var r=e+"=",t=decodeURIComponent(document.cookie).split(";"),n=0;n<t.length;n+=1){for(var o=t[n];" "===o.charAt(0);)o=o.substring(1);if(0===o.indexOf(r))return o.substring(r.length,o.length)}return""}};function a(){var e=navigator.userAgent.toLowerCase();return!/opera|opr\/[\d]+/.test(e)&&/(msie|trident)/.test(e)}function i(){/^((?!chrome|android).)*safari/i.test(navigator.userAgent)}function s(e){return e?e.replace(/\\u[\dA-F]{4}/gi,(function(e){return String.fromCharCode(parseInt(e.replace(/\\u/g,""),16))})):e}},7541:(e,r,t)=>{"use strict";t(9357),t(5115),t(2773);var n=t(3454),o=t(9480),a=t(4826);t(802);var i=t(860);function s(e,r){"function"==typeof e.append?e.append(r):e.appendChild(r)}function c(e){var r=document.getElementById("loading-layer");r&&(e?r.classList.remove("hidden"):r.classList.add("hidden"))}var u={};u.i18n=(void 0===window.i18n&&(window.i18n=new i.I18nLoader),window.i18n),u.initMeetingSDKHelper=function(){var e=a.testTool.parseQuery(),r={type:e.type,title:"",url:(0,a.b64DecodeUnicode)(e.url)||"",width:parseInt(e.w||"500",10),height:parseInt(e.h||"400",10),lang:e.lang||"en-US",zoomJSLib:(0,a.b64DecodeUnicode)(e.zoomJSLib)||""};(0,o.setZoomJSLib)(r.zoomJSLib.replace("/lang","")),function(e){(0,n.checkJssdkNodeHelper)(),(0,a.stringFormat)(),void 0===window.i18n&&(window.i18n=load18n()),e&&e instanceof Array&&e.forEach((function(e){addOriginTrial(e)}))}(),window.i18n.load(r.lang).then((function(){if(console.log("i18n loaded"),e=r.url,/^(https?:\/\/)?([\w-]+\.)+[\w-]+(\/[\w- ./?%&=]*)?$/i.test(e)&&r.type){var e,t;(function(e){var r=document.createElement("div");r.classList.add("mini-layout");var t=document.createElement("div");t.classList.add("mini-layout-body");var n=document.createElement("div");n.classList.add("page-header");var o=document.createElement("div");o.textContent=(0,a.iText)("Confirm {0}","apac.helper.confirm_title").format(e.title);var i=document.createElement("div");i.classList.add("form");var c=document.createElement("div");c.classList.add("form-group");var u=document.createElement("div");u.classList.add("controls");var d=document.createElement("p"),p=(0,a.iText)("Confirm","apac.confirm");d.textContent=(0,a.iText)('Click "{0}" to continue. Please remain on this page before completing the process.',"apac.helper.confirm_desc").format(p);var l=document.createElement("button");l.textContent=p,l.classList.add("login-btn2"),l.classList.add("login-btn2-zoom"),l.id="zoom-websdk-confirm",s(r,t),s(t,n),s(n,o),s(t,i),s(i,c),s(c,u),s(u,d),s(u,l),s(document.getElementById("zmmtg-root-helper"),r)})(r=Object.assign(r,function(e){var r="",t="";switch(e){case"register":r=(0,a.iText)("Registration","apac.registration"),t=(0,a.iText)("Registering","apac.registering");break;case"sign":r=(0,a.iText)("Sign in","apac.sign_in"),t=(0,a.iText)("Signing in","apac.signing_in");break;case"captcha":r=(0,a.iText)("ReCAPTCHA","apac.recaptcha"),t=(0,a.iText)("ReCAPTCHA","apac.recaptcha");break;case"sms":r=(0,a.iText)("SMS verification","apac.sms_verification"),t=(0,a.iText)("SMS verification","apac.sms_verification");break;default:r="",t=""}return{title:r,text:t}}(r.type))),function(e){var r=document.createElement("div");r.id="loading-layer",r.classList.add("loading-layer"),r.classList.add("loading-layer--reset-webclient"),r.classList.add("hidden");var t=document.createElement("div");t.classList.add("loading-layer__text-field");var n=document.createElement("p");n.classList.add("loading-layer__title"),n.textContent="";var o=document.createElement("p");o.classList.add("loading-layer__text"),o.textContent=e;var a=document.createElement("span");a.classList.add("loading-layer__dot-ani"),s(r,t),s(t,n),s(t,o),s(o,a),s(document.getElementById("zmmtg-root-helper"),r)}(r.text),c(!1);var n,o="init";document.getElementById("zoom-websdk-confirm").addEventListener("click",(function(){c(!0),o="open",t=function(e,r,t,n,o){var a=window.screenX+(o.width-t)/2,i=window.screenY+(o.height-n)/2;return console.log("width=".concat(t,", height=").concat(n,", top=").concat(i,", left=").concat(a)),window.open(e,"zoom register","toolbar=no, location=no, directories=no, status=no, menubar=no, scrollbars=no, resizable=no, copyhistory=no, width=".concat(t,", height=").concat(n,", top=").concat(i,", left=").concat(a))}(r.url,0,r.width,r.height,{width:window.innerWidth,height:window.innerHeight}),n=setInterval((function(){t.closed&&"open"===o&&(clearInterval(n),c(!1),o="close")}),500),setTimeout((function(){t.closed||"open"!==o||(c(!1),o="close",t.close())}),6e5)})),window.addEventListener("message",(function(e){var n=e.data,o=n.type?n.type:n;["registerSuccess","joinSuccess","autoRegisterSuccess","manualRegisterSuccess","realNameSuccess","ssoLoginFail","ssoLoginSuccess","captchaFail","captchaSuccess","zoomLoginFail","zoomLoginSuccess"].includes(o)&&((0,a.informWebSDK)(r.type,n),t&&t.close(),window.close())}),!1),(0,a.informWebSDK)(r.type,{status:"doing"}),window.addEventListener("beforeunload",(function(){(0,a.informWebSDK)(r.type,{status:"close"})}))}else!function(){var e=document.createElement("div");e.classList.add("mini-layout");var r=document.createElement("p");r.textContent="Error, you link not verified",r.classList.add("mini-layout-error"),s(e,r);var t=document.getElementById("zmmtg-root-helper");"function"==typeof t.append?t.append(e):t.appendChild(e)}()}))},"undefined"!=typeof window&&(window.ZoomMtg=u),console.log("JSSDK_BUILD_WAY","var")},4963:e=>{e.exports=function(e){if("function"!=typeof e)throw TypeError(e+" is not a function!");return e}},7722:(e,r,t)=>{var n=t(6314)("unscopables"),o=Array.prototype;null==o[n]&&t(7728)(o,n,{}),e.exports=function(e){o[n][e]=!0}},6793:(e,r,t)=>{"use strict";var n=t(4496)(!0);e.exports=function(e,r,t){return r+(t?n(e,r).length:1)}},3328:e=>{e.exports=function(e,r,t,n){if(!(e instanceof r)||void 0!==n&&n in e)throw TypeError(t+": incorrect invocation!");return e}},7007:(e,r,t)=>{var n=t(5286);e.exports=function(e){if(!n(e))throw TypeError(e+" is not an object!");return e}},9315:(e,r,t)=>{var n=t(2110),o=t(875),a=t(2337);e.exports=function(e){return function(r,t,i){var s,c=n(r),u=o(c.length),d=a(i,u);if(e&&t!=t){for(;u>d;)if((s=c[d++])!=s)return!0}else for(;u>d;d++)if((e||d in c)&&c[d]===t)return e||d||0;return!e&&-1}}},50:(e,r,t)=>{var n=t(741),o=t(9797),a=t(508),i=t(875),s=t(6886);e.exports=function(e,r){var t=1==e,c=2==e,u=3==e,d=4==e,p=6==e,l=5==e||p,_=r||s;return function(r,s,f){for(var m,h,v=a(r),g=o(v),b=n(s,f,3),w=i(g.length),y=0,x=t?_(r,w):c?_(r,0):void 0;w>y;y++)if((l||y in g)&&(h=b(m=g[y],y,v),e))if(t)x[y]=h;else if(h)switch(e){case 3:return!0;case 5:return m;case 6:return y;case 2:x.push(m)}else if(d)return!1;return p?-1:u||d?d:x}}},2736:(e,r,t)=>{var n=t(5286),o=t(4302),a=t(6314)("species");e.exports=function(e){var r;return o(e)&&("function"!=typeof(r=e.constructor)||r!==Array&&!o(r.prototype)||(r=void 0),n(r)&&null===(r=r[a])&&(r=void 0)),void 0===r?Array:r}},6886:(e,r,t)=>{var n=t(2736);e.exports=function(e,r){return new(n(e))(r)}},1488:(e,r,t)=>{var n=t(2032),o=t(6314)("toStringTag"),a="Arguments"==n(function(){return arguments}());e.exports=function(e){var r,t,i;return void 0===e?"Undefined":null===e?"Null":"string"==typeof(t=function(e,r){try{return e[r]}catch(e){}}(r=Object(e),o))?t:a?n(r):"Object"==(i=n(r))&&"function"==typeof r.callee?"Arguments":i}},2032:e=>{var r={}.toString;e.exports=function(e){return r.call(e).slice(8,-1)}},5645:e=>{var r=e.exports={version:"2.6.12"};"number"==typeof __e&&(__e=r)},2811:(e,r,t)=>{"use strict";var n=t(9275),o=t(681);e.exports=function(e,r,t){r in e?n.f(e,r,o(0,t)):e[r]=t}},741:(e,r,t)=>{var n=t(4963);e.exports=function(e,r,t){if(n(e),void 0===r)return e;switch(t){case 1:return function(t){return e.call(r,t)};case 2:return function(t,n){return e.call(r,t,n)};case 3:return function(t,n,o){return e.call(r,t,n,o)}}return function(){return e.apply(r,arguments)}}},1355:e=>{e.exports=function(e){if(null==e)throw TypeError("Can't call method on  "+e);return e}},7057:(e,r,t)=>{e.exports=!t(4253)((function(){return 7!=Object.defineProperty({},"a",{get:function(){return 7}}).a}))},2457:(e,r,t)=>{var n=t(5286),o=t(3816).document,a=n(o)&&n(o.createElement);e.exports=function(e){return a?o.createElement(e):{}}},4430:e=>{e.exports="constructor,hasOwnProperty,isPrototypeOf,propertyIsEnumerable,toLocaleString,toString,valueOf".split(",")},5541:(e,r,t)=>{var n=t(7184),o=t(4548),a=t(4682);e.exports=function(e){var r=n(e),t=o.f;if(t)for(var i,s=t(e),c=a.f,u=0;s.length>u;)c.call(e,i=s[u++])&&r.push(i);return r}},2985:(e,r,t)=>{var n=t(3816),o=t(5645),a=t(7728),i=t(7234),s=t(741),c="prototype",u=function(e,r,t){var d,p,l,_,f=e&u.F,m=e&u.G,h=e&u.S,v=e&u.P,g=e&u.B,b=m?n:h?n[r]||(n[r]={}):(n[r]||{})[c],w=m?o:o[r]||(o[r]={}),y=w[c]||(w[c]={});for(d in m&&(t=r),t)l=((p=!f&&b&&void 0!==b[d])?b:t)[d],_=g&&p?s(l,n):v&&"function"==typeof l?s(Function.call,l):l,b&&i(b,d,l,e&u.U),w[d]!=l&&a(w,d,_),v&&y[d]!=l&&(y[d]=l)};n.core=o,u.F=1,u.G=2,u.S=4,u.P=8,u.B=16,u.W=32,u.U=64,u.R=128,e.exports=u},8852:(e,r,t)=>{var n=t(6314)("match");e.exports=function(e){var r=/./;try{"/./"[e](r)}catch(t){try{return r[n]=!1,!"/./"[e](r)}catch(e){}}return!0}},4253:e=>{e.exports=function(e){try{return!!e()}catch(e){return!0}}},8082:(e,r,t)=>{"use strict";t(8269);var n=t(7234),o=t(7728),a=t(4253),i=t(1355),s=t(6314),c=t(1165),u=s("species"),d=!a((function(){var e=/./;return e.exec=function(){var e=[];return e.groups={a:"7"},e},"7"!=="".replace(e,"$<a>")})),p=function(){var e=/(?:)/,r=e.exec;e.exec=function(){return r.apply(this,arguments)};var t="ab".split(e);return 2===t.length&&"a"===t[0]&&"b"===t[1]}();e.exports=function(e,r,t){var l=s(e),_=!a((function(){var r={};return r[l]=function(){return 7},7!=""[e](r)})),f=_?!a((function(){var r=!1,t=/a/;return t.exec=function(){return r=!0,null},"split"===e&&(t.constructor={},t.constructor[u]=function(){return t}),t[l](""),!r})):void 0;if(!_||!f||"replace"===e&&!d||"split"===e&&!p){var m=/./[l],h=t(i,l,""[e],(function(e,r,t,n,o){return r.exec===c?_&&!o?{done:!0,value:m.call(r,t,n)}:{done:!0,value:e.call(t,r,n)}:{done:!1}})),v=h[0],g=h[1];n(String.prototype,e,v),o(RegExp.prototype,l,2==r?function(e,r){return g.call(e,this,r)}:function(e){return g.call(e,this)})}}},3218:(e,r,t)=>{"use strict";var n=t(7007);e.exports=function(){var e=n(this),r="";return e.global&&(r+="g"),e.ignoreCase&&(r+="i"),e.multiline&&(r+="m"),e.unicode&&(r+="u"),e.sticky&&(r+="y"),r}},3531:(e,r,t)=>{var n=t(741),o=t(8851),a=t(6555),i=t(7007),s=t(875),c=t(9002),u={},d={},p=e.exports=function(e,r,t,p,l){var _,f,m,h,v=l?function(){return e}:c(e),g=n(t,p,r?2:1),b=0;if("function"!=typeof v)throw TypeError(e+" is not iterable!");if(a(v)){for(_=s(e.length);_>b;b++)if((h=r?g(i(f=e[b])[0],f[1]):g(e[b]))===u||h===d)return h}else for(m=v.call(e);!(f=m.next()).done;)if((h=o(m,g,f.value,r))===u||h===d)return h};p.BREAK=u,p.RETURN=d},18:(e,r,t)=>{e.exports=t(3825)("native-function-to-string",Function.toString)},3816:e=>{var r=e.exports="undefined"!=typeof window&&window.Math==Math?window:"undefined"!=typeof self&&self.Math==Math?self:Function("return this")();"number"==typeof __g&&(__g=r)},9181:e=>{var r={}.hasOwnProperty;e.exports=function(e,t){return r.call(e,t)}},7728:(e,r,t)=>{var n=t(9275),o=t(681);e.exports=t(7057)?function(e,r,t){return n.f(e,r,o(1,t))}:function(e,r,t){return e[r]=t,e}},639:(e,r,t)=>{var n=t(3816).document;e.exports=n&&n.documentElement},1734:(e,r,t)=>{e.exports=!t(7057)&&!t(4253)((function(){return 7!=Object.defineProperty(t(2457)("div"),"a",{get:function(){return 7}}).a}))},7242:e=>{e.exports=function(e,r,t){var n=void 0===t;switch(r.length){case 0:return n?e():e.call(t);case 1:return n?e(r[0]):e.call(t,r[0]);case 2:return n?e(r[0],r[1]):e.call(t,r[0],r[1]);case 3:return n?e(r[0],r[1],r[2]):e.call(t,r[0],r[1],r[2]);case 4:return n?e(r[0],r[1],r[2],r[3]):e.call(t,r[0],r[1],r[2],r[3])}return e.apply(t,r)}},9797:(e,r,t)=>{var n=t(2032);e.exports=Object("z").propertyIsEnumerable(0)?Object:function(e){return"String"==n(e)?e.split(""):Object(e)}},6555:(e,r,t)=>{var n=t(2803),o=t(6314)("iterator"),a=Array.prototype;e.exports=function(e){return void 0!==e&&(n.Array===e||a[o]===e)}},4302:(e,r,t)=>{var n=t(2032);e.exports=Array.isArray||function(e){return"Array"==n(e)}},5286:e=>{e.exports=function(e){return"object"==typeof e?null!==e:"function"==typeof e}},5364:(e,r,t)=>{var n=t(5286),o=t(2032),a=t(6314)("match");e.exports=function(e){var r;return n(e)&&(void 0!==(r=e[a])?!!r:"RegExp"==o(e))}},8851:(e,r,t)=>{var n=t(7007);e.exports=function(e,r,t,o){try{return o?r(n(t)[0],t[1]):r(t)}catch(r){var a=e.return;throw void 0!==a&&n(a.call(e)),r}}},9988:(e,r,t)=>{"use strict";var n=t(2503),o=t(681),a=t(2943),i={};t(7728)(i,t(6314)("iterator"),(function(){return this})),e.exports=function(e,r,t){e.prototype=n(i,{next:o(1,t)}),a(e,r+" Iterator")}},2923:(e,r,t)=>{"use strict";var n=t(4461),o=t(2985),a=t(7234),i=t(7728),s=t(2803),c=t(9988),u=t(2943),d=t(468),p=t(6314)("iterator"),l=!([].keys&&"next"in[].keys()),_="keys",f="values",m=function(){return this};e.exports=function(e,r,t,h,v,g,b){c(t,r,h);var w,y,x,k=function(e){if(!l&&e in O)return O[e];switch(e){case _:case f:return function(){return new t(this,e)}}return function(){return new t(this,e)}},S=r+" Iterator",j=v==f,P=!1,O=e.prototype,M=O[p]||O["@@iterator"]||v&&O[v],E=M||k(v),T=v?j?k("entries"):E:void 0,I="Array"==r&&O.entries||M;if(I&&(x=d(I.call(new e)))!==Object.prototype&&x.next&&(u(x,S,!0),n||"function"==typeof x[p]||i(x,p,m)),j&&M&&M.name!==f&&(P=!0,E=function(){return M.call(this)}),n&&!b||!l&&!P&&O[p]||i(O,p,E),s[r]=E,s[S]=m,v)if(w={values:j?E:k(f),keys:g?E:k(_),entries:T},b)for(y in w)y in O||a(O,y,w[y]);else o(o.P+o.F*(l||P),r,w);return w}},7462:(e,r,t)=>{var n=t(6314)("iterator"),o=!1;try{var a=[7][n]();a.return=function(){o=!0},Array.from(a,(function(){throw 2}))}catch(e){}e.exports=function(e,r){if(!r&&!o)return!1;var t=!1;try{var a=[7],i=a[n]();i.next=function(){return{done:t=!0}},a[n]=function(){return i},e(a)}catch(e){}return t}},5436:e=>{e.exports=function(e,r){return{value:r,done:!!e}}},2803:e=>{e.exports={}},4461:e=>{e.exports=!1},4728:(e,r,t)=>{var n=t(3953)("meta"),o=t(5286),a=t(9181),i=t(9275).f,s=0,c=Object.isExtensible||function(){return!0},u=!t(4253)((function(){return c(Object.preventExtensions({}))})),d=function(e){i(e,n,{value:{i:"O"+ ++s,w:{}}})},p=e.exports={KEY:n,NEED:!1,fastKey:function(e,r){if(!o(e))return"symbol"==typeof e?e:("string"==typeof e?"S":"P")+e;if(!a(e,n)){if(!c(e))return"F";if(!r)return"E";d(e)}return e[n].i},getWeak:function(e,r){if(!a(e,n)){if(!c(e))return!0;if(!r)return!1;d(e)}return e[n].w},onFreeze:function(e){return u&&p.NEED&&c(e)&&!a(e,n)&&d(e),e}}},4351:(e,r,t)=>{var n=t(3816),o=t(4193).set,a=n.MutationObserver||n.WebKitMutationObserver,i=n.process,s=n.Promise,c="process"==t(2032)(i);e.exports=function(){var e,r,t,u=function(){var n,o;for(c&&(n=i.domain)&&n.exit();e;){o=e.fn,e=e.next;try{o()}catch(n){throw e?t():r=void 0,n}}r=void 0,n&&n.enter()};if(c)t=function(){i.nextTick(u)};else if(!a||n.navigator&&n.navigator.standalone)if(s&&s.resolve){var d=s.resolve(void 0);t=function(){d.then(u)}}else t=function(){o.call(n,u)};else{var p=!0,l=document.createTextNode("");new a(u).observe(l,{characterData:!0}),t=function(){l.data=p=!p}}return function(n){var o={fn:n,next:void 0};r&&(r.next=o),e||(e=o,t()),r=o}}},3499:(e,r,t)=>{"use strict";var n=t(4963);function o(e){var r,t;this.promise=new e((function(e,n){if(void 0!==r||void 0!==t)throw TypeError("Bad Promise constructor");r=e,t=n})),this.resolve=n(r),this.reject=n(t)}e.exports.f=function(e){return new o(e)}},5345:(e,r,t)=>{"use strict";var n=t(7057),o=t(7184),a=t(4548),i=t(4682),s=t(508),c=t(9797),u=Object.assign;e.exports=!u||t(4253)((function(){var e={},r={},t=Symbol(),n="abcdefghijklmnopqrst";return e[t]=7,n.split("").forEach((function(e){r[e]=e})),7!=u({},e)[t]||Object.keys(u({},r)).join("")!=n}))?function(e,r){for(var t=s(e),u=arguments.length,d=1,p=a.f,l=i.f;u>d;)for(var _,f=c(arguments[d++]),m=p?o(f).concat(p(f)):o(f),h=m.length,v=0;h>v;)_=m[v++],n&&!l.call(f,_)||(t[_]=f[_]);return t}:u},2503:(e,r,t)=>{var n=t(7007),o=t(5588),a=t(4430),i=t(9335)("IE_PROTO"),s=function(){},c="prototype",u=function(){var e,r=t(2457)("iframe"),n=a.length;for(r.style.display="none",t(639).appendChild(r),r.src="javascript:",(e=r.contentWindow.document).open(),e.write("<script>document.F=Object<\/script>"),e.close(),u=e.F;n--;)delete u[c][a[n]];return u()};e.exports=Object.create||function(e,r){var t;return null!==e?(s[c]=n(e),t=new s,s[c]=null,t[i]=e):t=u(),void 0===r?t:o(t,r)}},9275:(e,r,t)=>{var n=t(7007),o=t(1734),a=t(1689),i=Object.defineProperty;r.f=t(7057)?Object.defineProperty:function(e,r,t){if(n(e),r=a(r,!0),n(t),o)try{return i(e,r,t)}catch(e){}if("get"in t||"set"in t)throw TypeError("Accessors not supported!");return"value"in t&&(e[r]=t.value),e}},5588:(e,r,t)=>{var n=t(9275),o=t(7007),a=t(7184);e.exports=t(7057)?Object.defineProperties:function(e,r){o(e);for(var t,i=a(r),s=i.length,c=0;s>c;)n.f(e,t=i[c++],r[t]);return e}},8693:(e,r,t)=>{var n=t(4682),o=t(681),a=t(2110),i=t(1689),s=t(9181),c=t(1734),u=Object.getOwnPropertyDescriptor;r.f=t(7057)?u:function(e,r){if(e=a(e),r=i(r,!0),c)try{return u(e,r)}catch(e){}if(s(e,r))return o(!n.f.call(e,r),e[r])}},9327:(e,r,t)=>{var n=t(2110),o=t(616).f,a={}.toString,i="object"==typeof window&&window&&Object.getOwnPropertyNames?Object.getOwnPropertyNames(window):[];e.exports.f=function(e){return i&&"[object Window]"==a.call(e)?function(e){try{return o(e)}catch(e){return i.slice()}}(e):o(n(e))}},616:(e,r,t)=>{var n=t(189),o=t(4430).concat("length","prototype");r.f=Object.getOwnPropertyNames||function(e){return n(e,o)}},4548:(e,r)=>{r.f=Object.getOwnPropertySymbols},468:(e,r,t)=>{var n=t(9181),o=t(508),a=t(9335)("IE_PROTO"),i=Object.prototype;e.exports=Object.getPrototypeOf||function(e){return e=o(e),n(e,a)?e[a]:"function"==typeof e.constructor&&e instanceof e.constructor?e.constructor.prototype:e instanceof Object?i:null}},189:(e,r,t)=>{var n=t(9181),o=t(2110),a=t(9315)(!1),i=t(9335)("IE_PROTO");e.exports=function(e,r){var t,s=o(e),c=0,u=[];for(t in s)t!=i&&n(s,t)&&u.push(t);for(;r.length>c;)n(s,t=r[c++])&&(~a(u,t)||u.push(t));return u}},7184:(e,r,t)=>{var n=t(189),o=t(4430);e.exports=Object.keys||function(e){return n(e,o)}},4682:(e,r)=>{r.f={}.propertyIsEnumerable},3160:(e,r,t)=>{var n=t(2985),o=t(5645),a=t(4253);e.exports=function(e,r){var t=(o.Object||{})[e]||Object[e],i={};i[e]=r(t),n(n.S+n.F*a((function(){t(1)})),"Object",i)}},1131:(e,r,t)=>{var n=t(7057),o=t(7184),a=t(2110),i=t(4682).f;e.exports=function(e){return function(r){for(var t,s=a(r),c=o(s),u=c.length,d=0,p=[];u>d;)t=c[d++],n&&!i.call(s,t)||p.push(e?[t,s[t]]:s[t]);return p}}},7643:(e,r,t)=>{var n=t(616),o=t(4548),a=t(7007),i=t(3816).Reflect;e.exports=i&&i.ownKeys||function(e){var r=n.f(a(e)),t=o.f;return t?r.concat(t(e)):r}},188:e=>{e.exports=function(e){try{return{e:!1,v:e()}}catch(e){return{e:!0,v:e}}}},94:(e,r,t)=>{var n=t(7007),o=t(5286),a=t(3499);e.exports=function(e,r){if(n(e),o(r)&&r.constructor===e)return r;var t=a.f(e);return(0,t.resolve)(r),t.promise}},681:e=>{e.exports=function(e,r){return{enumerable:!(1&e),configurable:!(2&e),writable:!(4&e),value:r}}},4408:(e,r,t)=>{var n=t(7234);e.exports=function(e,r,t){for(var o in r)n(e,o,r[o],t);return e}},7234:(e,r,t)=>{var n=t(3816),o=t(7728),a=t(9181),i=t(3953)("src"),s=t(18),c="toString",u=(""+s).split(c);t(5645).inspectSource=function(e){return s.call(e)},(e.exports=function(e,r,t,s){var c="function"==typeof t;c&&(a(t,"name")||o(t,"name",r)),e[r]!==t&&(c&&(a(t,i)||o(t,i,e[r]?""+e[r]:u.join(String(r)))),e===n?e[r]=t:s?e[r]?e[r]=t:o(e,r,t):(delete e[r],o(e,r,t)))})(Function.prototype,c,(function(){return"function"==typeof this&&this[i]||s.call(this)}))},7787:(e,r,t)=>{"use strict";var n=t(1488),o=RegExp.prototype.exec;e.exports=function(e,r){var t=e.exec;if("function"==typeof t){var a=t.call(e,r);if("object"!=typeof a)throw new TypeError("RegExp exec method returned something other than an Object or null");return a}if("RegExp"!==n(e))throw new TypeError("RegExp#exec called on incompatible receiver");return o.call(e,r)}},1165:(e,r,t)=>{"use strict";var n=t(3218),o=RegExp.prototype.exec,a=String.prototype.replace,i=o,s="lastIndex",c=function(){var e=/a/,r=/b*/g;return o.call(e,"a"),o.call(r,"a"),0!==e[s]||0!==r[s]}(),u=void 0!==/()??/.exec("")[1];(c||u)&&(i=function(e){var r,t,i,d,p=this;return u&&(t=new RegExp("^"+p.source+"$(?!\\s)",n.call(p))),c&&(r=p[s]),i=o.call(p,e),c&&i&&(p[s]=p.global?i.index+i[0].length:r),u&&i&&i.length>1&&a.call(i[0],t,(function(){for(d=1;d<arguments.length-2;d++)void 0===arguments[d]&&(i[d]=void 0)})),i}),e.exports=i},2974:(e,r,t)=>{"use strict";var n=t(3816),o=t(9275),a=t(7057),i=t(6314)("species");e.exports=function(e){var r=n[e];a&&r&&!r[i]&&o.f(r,i,{configurable:!0,get:function(){return this}})}},2943:(e,r,t)=>{var n=t(9275).f,o=t(9181),a=t(6314)("toStringTag");e.exports=function(e,r,t){e&&!o(e=t?e:e.prototype,a)&&n(e,a,{configurable:!0,value:r})}},9335:(e,r,t)=>{var n=t(3825)("keys"),o=t(3953);e.exports=function(e){return n[e]||(n[e]=o(e))}},3825:(e,r,t)=>{var n=t(5645),o=t(3816),a="__core-js_shared__",i=o[a]||(o[a]={});(e.exports=function(e,r){return i[e]||(i[e]=void 0!==r?r:{})})("versions",[]).push({version:n.version,mode:t(4461)?"pure":"global",copyright:"© 2020 Denis Pushkarev (zloirock.ru)"})},8364:(e,r,t)=>{var n=t(7007),o=t(4963),a=t(6314)("species");e.exports=function(e,r){var t,i=n(e).constructor;return void 0===i||null==(t=n(i)[a])?r:o(t)}},7717:(e,r,t)=>{"use strict";var n=t(4253);e.exports=function(e,r){return!!e&&n((function(){r?e.call(null,(function(){}),1):e.call(null)}))}},4496:(e,r,t)=>{var n=t(1467),o=t(1355);e.exports=function(e){return function(r,t){var a,i,s=String(o(r)),c=n(t),u=s.length;return c<0||c>=u?e?"":void 0:(a=s.charCodeAt(c))<55296||a>56319||c+1===u||(i=s.charCodeAt(c+1))<56320||i>57343?e?s.charAt(c):a:e?s.slice(c,c+2):i-56320+(a-55296<<10)+65536}}},2094:(e,r,t)=>{var n=t(5364),o=t(1355);e.exports=function(e,r,t){if(n(r))throw TypeError("String#"+t+" doesn't accept regex!");return String(o(e))}},4193:(e,r,t)=>{var n,o,a,i=t(741),s=t(7242),c=t(639),u=t(2457),d=t(3816),p=d.process,l=d.setImmediate,_=d.clearImmediate,f=d.MessageChannel,m=d.Dispatch,h=0,v={},g="onreadystatechange",b=function(){var e=+this;if(v.hasOwnProperty(e)){var r=v[e];delete v[e],r()}},w=function(e){b.call(e.data)};l&&_||(l=function(e){for(var r=[],t=1;arguments.length>t;)r.push(arguments[t++]);return v[++h]=function(){s("function"==typeof e?e:Function(e),r)},n(h),h},_=function(e){delete v[e]},"process"==t(2032)(p)?n=function(e){p.nextTick(i(b,e,1))}:m&&m.now?n=function(e){m.now(i(b,e,1))}:f?(a=(o=new f).port2,o.port1.onmessage=w,n=i(a.postMessage,a,1)):d.addEventListener&&"function"==typeof postMessage&&!d.importScripts?(n=function(e){d.postMessage(e+"","*")},d.addEventListener("message",w,!1)):n=g in u("script")?function(e){c.appendChild(u("script"))[g]=function(){c.removeChild(this),b.call(e)}}:function(e){setTimeout(i(b,e,1),0)}),e.exports={set:l,clear:_}},2337:(e,r,t)=>{var n=t(1467),o=Math.max,a=Math.min;e.exports=function(e,r){return(e=n(e))<0?o(e+r,0):a(e,r)}},1467:e=>{var r=Math.ceil,t=Math.floor;e.exports=function(e){return isNaN(e=+e)?0:(e>0?t:r)(e)}},2110:(e,r,t)=>{var n=t(9797),o=t(1355);e.exports=function(e){return n(o(e))}},875:(e,r,t)=>{var n=t(1467),o=Math.min;e.exports=function(e){return e>0?o(n(e),9007199254740991):0}},508:(e,r,t)=>{var n=t(1355);e.exports=function(e){return Object(n(e))}},1689:(e,r,t)=>{var n=t(5286);e.exports=function(e,r){if(!n(e))return e;var t,o;if(r&&"function"==typeof(t=e.toString)&&!n(o=t.call(e)))return o;if("function"==typeof(t=e.valueOf)&&!n(o=t.call(e)))return o;if(!r&&"function"==typeof(t=e.toString)&&!n(o=t.call(e)))return o;throw TypeError("Can't convert object to primitive value")}},3953:e=>{var r=0,t=Math.random();e.exports=function(e){return"Symbol(".concat(void 0===e?"":e,")_",(++r+t).toString(36))}},575:(e,r,t)=>{var n=t(3816).navigator;e.exports=n&&n.userAgent||""},6074:(e,r,t)=>{var n=t(3816),o=t(5645),a=t(4461),i=t(8787),s=t(9275).f;e.exports=function(e){var r=o.Symbol||(o.Symbol=a?{}:n.Symbol||{});"_"==e.charAt(0)||e in r||s(r,e,{value:i.f(e)})}},8787:(e,r,t)=>{r.f=t(6314)},6314:(e,r,t)=>{var n=t(3825)("wks"),o=t(3953),a=t(3816).Symbol,i="function"==typeof a;(e.exports=function(e){return n[e]||(n[e]=i&&a[e]||(i?a:o)("Symbol."+e))}).store=n},9002:(e,r,t)=>{var n=t(1488),o=t(6314)("iterator"),a=t(2803);e.exports=t(5645).getIteratorMethod=function(e){if(null!=e)return e[o]||e["@@iterator"]||a[n(e)]}},8837:(e,r,t)=>{"use strict";var n=t(2985),o=t(50)(2);n(n.P+n.F*!t(7717)([].filter,!0),"Array",{filter:function(e){return o(this,e,arguments[1])}})},2310:(e,r,t)=>{"use strict";var n=t(2985),o=t(50)(5),a="find",i=!0;a in[]&&Array(1)[a]((function(){i=!1})),n(n.P+n.F*i,"Array",{find:function(e){return o(this,e,arguments.length>1?arguments[1]:void 0)}}),t(7722)(a)},6997:(e,r,t)=>{"use strict";var n=t(7722),o=t(5436),a=t(2803),i=t(2110);e.exports=t(2923)(Array,"Array",(function(e,r){this._t=i(e),this._i=0,this._k=r}),(function(){var e=this._t,r=this._k,t=this._i++;return!e||t>=e.length?(this._t=void 0,o(1)):o(0,"keys"==r?t:"values"==r?e[t]:[t,e[t]])}),"values"),a.Arguments=a.Array,n("keys"),n("values"),n("entries")},9371:(e,r,t)=>{"use strict";var n=t(2985),o=t(50)(1);n(n.P+n.F*!t(7717)([].map,!0),"Array",{map:function(e){return o(this,e,arguments[1])}})},110:(e,r,t)=>{"use strict";var n=t(2985),o=t(639),a=t(2032),i=t(2337),s=t(875),c=[].slice;n(n.P+n.F*t(4253)((function(){o&&c.call(o)})),"Array",{slice:function(e,r){var t=s(this.length),n=a(this);if(r=void 0===r?t:r,"Array"==n)return c.call(this,e,r);for(var o=i(e,t),u=i(r,t),d=s(u-o),p=new Array(d),l=0;l<d;l++)p[l]="String"==n?this.charAt(o+l):this[o+l];return p}})},75:(e,r,t)=>{"use strict";var n=t(2985),o=t(4963),a=t(508),i=t(4253),s=[].sort,c=[1,2,3];n(n.P+n.F*(i((function(){c.sort(void 0)}))||!i((function(){c.sort(null)}))||!t(7717)(s)),"Array",{sort:function(e){return void 0===e?s.call(a(this)):s.call(a(this),o(e))}})},5115:(e,r,t)=>{var n=t(2985);n(n.S+n.F,"Object",{assign:t(5345)})},4882:(e,r,t)=>{var n=t(2110),o=t(8693).f;t(3160)("getOwnPropertyDescriptor",(function(){return function(e,r){return o(n(e),r)}}))},7476:(e,r,t)=>{var n=t(508),o=t(7184);t(3160)("keys",(function(){return function(e){return o(n(e))}}))},6253:(e,r,t)=>{"use strict";var n=t(1488),o={};o[t(6314)("toStringTag")]="z",o+""!="[object z]"&&t(7234)(Object.prototype,"toString",(function(){return"[object "+n(this)+"]"}),!0)},851:(e,r,t)=>{"use strict";var n,o,a,i,s=t(4461),c=t(3816),u=t(741),d=t(1488),p=t(2985),l=t(5286),_=t(4963),f=t(3328),m=t(3531),h=t(8364),v=t(4193).set,g=t(4351)(),b=t(3499),w=t(188),y=t(575),x=t(94),k="Promise",S=c.TypeError,j=c.process,P=j&&j.versions,O=P&&P.v8||"",M=c[k],E="process"==d(j),T=function(){},I=o=b.f,A=!!function(){try{var e=M.resolve(1),r=(e.constructor={})[t(6314)("species")]=function(e){e(T,T)};return(E||"function"==typeof PromiseRejectionEvent)&&e.then(T)instanceof r&&0!==O.indexOf("6.6")&&-1===y.indexOf("Chrome/66")}catch(e){}}(),z=function(e){var r;return!(!l(e)||"function"!=typeof(r=e.then))&&r},D=function(e,r){if(!e._n){e._n=!0;var t=e._c;g((function(){for(var n=e._v,o=1==e._s,a=0,i=function(r){var t,a,i,s=o?r.ok:r.fail,c=r.resolve,u=r.reject,d=r.domain;try{s?(o||(2==e._h&&N(e),e._h=1),!0===s?t=n:(d&&d.enter(),t=s(n),d&&(d.exit(),i=!0)),t===r.promise?u(S("Promise-chain cycle")):(a=z(t))?a.call(t,c,u):c(t)):u(n)}catch(e){d&&!i&&d.exit(),u(e)}};t.length>a;)i(t[a++]);e._c=[],e._n=!1,r&&!e._h&&W(e)}))}},W=function(e){v.call(c,(function(){var r,t,n,o=e._v,a=L(e);if(a&&(r=w((function(){E?j.emit("unhandledRejection",o,e):(t=c.onunhandledrejection)?t({promise:e,reason:o}):(n=c.console)&&n.error&&n.error("Unhandled promise rejection",o)})),e._h=E||L(e)?2:1),e._a=void 0,a&&r.e)throw r.v}))},L=function(e){return 1!==e._h&&0===(e._a||e._c).length},N=function(e){v.call(c,(function(){var r;E?j.emit("rejectionHandled",e):(r=c.onrejectionhandled)&&r({promise:e,reason:e._v})}))},C=function(e){var r=this;r._d||(r._d=!0,(r=r._w||r)._v=e,r._s=2,r._a||(r._a=r._c.slice()),D(r,!0))},K=function(e){var r,t=this;if(!t._d){t._d=!0,t=t._w||t;try{if(t===e)throw S("Promise can't be resolved itself");(r=z(e))?g((function(){var n={_w:t,_d:!1};try{r.call(e,u(K,n,1),u(C,n,1))}catch(e){C.call(n,e)}})):(t._v=e,t._s=1,D(t,!1))}catch(e){C.call({_w:t,_d:!1},e)}}};A||(M=function(e){f(this,M,k,"_h"),_(e),n.call(this);try{e(u(K,this,1),u(C,this,1))}catch(e){C.call(this,e)}},(n=function(e){this._c=[],this._a=void 0,this._s=0,this._d=!1,this._v=void 0,this._h=0,this._n=!1}).prototype=t(4408)(M.prototype,{then:function(e,r){var t=I(h(this,M));return t.ok="function"!=typeof e||e,t.fail="function"==typeof r&&r,t.domain=E?j.domain:void 0,this._c.push(t),this._a&&this._a.push(t),this._s&&D(this,!1),t.promise},catch:function(e){return this.then(void 0,e)}}),a=function(){var e=new n;this.promise=e,this.resolve=u(K,e,1),this.reject=u(C,e,1)},b.f=I=function(e){return e===M||e===i?new a(e):o(e)}),p(p.G+p.W+p.F*!A,{Promise:M}),t(2943)(M,k),t(2974)(k),i=t(5645)[k],p(p.S+p.F*!A,k,{reject:function(e){var r=I(this);return(0,r.reject)(e),r.promise}}),p(p.S+p.F*(s||!A),k,{resolve:function(e){return x(s&&this===i?M:this,e)}}),p(p.S+p.F*!(A&&t(7462)((function(e){M.all(e).catch(T)}))),k,{all:function(e){var r=this,t=I(r),n=t.resolve,o=t.reject,a=w((function(){var t=[],a=0,i=1;m(e,!1,(function(e){var s=a++,c=!1;t.push(void 0),i++,r.resolve(e).then((function(e){c||(c=!0,t[s]=e,--i||n(t))}),o)})),--i||n(t)}));return a.e&&o(a.v),t.promise},race:function(e){var r=this,t=I(r),n=t.reject,o=w((function(){m(e,!1,(function(e){r.resolve(e).then(t.resolve,n)}))}));return o.e&&n(o.v),t.promise}})},8269:(e,r,t)=>{"use strict";var n=t(1165);t(2985)({target:"RegExp",proto:!0,forced:n!==/./.exec},{exec:n})},6774:(e,r,t)=>{t(7057)&&"g"!=/./g.flags&&t(9275).f(RegExp.prototype,"flags",{configurable:!0,get:t(3218)})},1466:(e,r,t)=>{"use strict";var n=t(7007),o=t(875),a=t(6793),i=t(7787);t(8082)("match",1,(function(e,r,t,s){return[function(t){var n=e(this),o=null==t?void 0:t[r];return void 0!==o?o.call(t,n):new RegExp(t)[r](String(n))},function(e){var r=s(t,e,this);if(r.done)return r.value;var c=n(e),u=String(this);if(!c.global)return i(c,u);var d=c.unicode;c.lastIndex=0;for(var p,l=[],_=0;null!==(p=i(c,u));){var f=String(p[0]);l[_]=f,""===f&&(c.lastIndex=a(u,o(c.lastIndex),d)),_++}return 0===_?null:l}]}))},9357:(e,r,t)=>{"use strict";var n=t(7007),o=t(508),a=t(875),i=t(1467),s=t(6793),c=t(7787),u=Math.max,d=Math.min,p=Math.floor,l=/\$([$&`']|\d\d?|<[^>]*>)/g,_=/\$([$&`']|\d\d?)/g;t(8082)("replace",2,(function(e,r,t,f){return[function(n,o){var a=e(this),i=null==n?void 0:n[r];return void 0!==i?i.call(n,a,o):t.call(String(a),n,o)},function(e,r){var o=f(t,e,this,r);if(o.done)return o.value;var p=n(e),l=String(this),_="function"==typeof r;_||(r=String(r));var h=p.global;if(h){var v=p.unicode;p.lastIndex=0}for(var g=[];;){var b=c(p,l);if(null===b)break;if(g.push(b),!h)break;""===String(b[0])&&(p.lastIndex=s(l,a(p.lastIndex),v))}for(var w,y="",x=0,k=0;k<g.length;k++){b=g[k];for(var S=String(b[0]),j=u(d(i(b.index),l.length),0),P=[],O=1;O<b.length;O++)P.push(void 0===(w=b[O])?w:String(w));var M=b.groups;if(_){var E=[S].concat(P,j,l);void 0!==M&&E.push(M);var T=String(r.apply(void 0,E))}else T=m(S,l,j,P,M,r);j>=x&&(y+=l.slice(x,j)+T,x=j+S.length)}return y+l.slice(x)}];function m(e,r,n,a,i,s){var c=n+e.length,u=a.length,d=_;return void 0!==i&&(i=o(i),d=l),t.call(s,d,(function(t,o){var s;switch(o.charAt(0)){case"$":return"$";case"&":return e;case"`":return r.slice(0,n);case"'":return r.slice(c);case"<":s=i[o.slice(1,-1)];break;default:var d=+o;if(0===d)return t;if(d>u){var l=p(d/10);return 0===l?t:l<=u?void 0===a[l-1]?o.charAt(1):a[l-1]+o.charAt(1):t}s=a[d-1]}return void 0===s?"":s}))}}))},1876:(e,r,t)=>{"use strict";var n=t(5364),o=t(7007),a=t(8364),i=t(6793),s=t(875),c=t(7787),u=t(1165),d=t(4253),p=Math.min,l=[].push,_="split",f="length",m="lastIndex",h=4294967295,v=!d((function(){RegExp(h,"y")}));t(8082)("split",2,(function(e,r,t,d){var g;return g="c"=="abbc"[_](/(b)*/)[1]||4!="test"[_](/(?:)/,-1)[f]||2!="ab"[_](/(?:ab)*/)[f]||4!="."[_](/(.?)(.?)/)[f]||"."[_](/()()/)[f]>1||""[_](/.?/)[f]?function(e,r){var o=String(this);if(void 0===e&&0===r)return[];if(!n(e))return t.call(o,e,r);for(var a,i,s,c=[],d=(e.ignoreCase?"i":"")+(e.multiline?"m":"")+(e.unicode?"u":"")+(e.sticky?"y":""),p=0,_=void 0===r?h:r>>>0,v=new RegExp(e.source,d+"g");(a=u.call(v,o))&&!((i=v[m])>p&&(c.push(o.slice(p,a.index)),a[f]>1&&a.index<o[f]&&l.apply(c,a.slice(1)),s=a[0][f],p=i,c[f]>=_));)v[m]===a.index&&v[m]++;return p===o[f]?!s&&v.test("")||c.push(""):c.push(o.slice(p)),c[f]>_?c.slice(0,_):c}:"0"[_](void 0,0)[f]?function(e,r){return void 0===e&&0===r?[]:t.call(this,e,r)}:t,[function(t,n){var o=e(this),a=null==t?void 0:t[r];return void 0!==a?a.call(t,o,n):g.call(String(o),t,n)},function(e,r){var n=d(g,e,this,r,g!==t);if(n.done)return n.value;var u=o(e),l=String(this),_=a(u,RegExp),f=u.unicode,m=(u.ignoreCase?"i":"")+(u.multiline?"m":"")+(u.unicode?"u":"")+(v?"y":"g"),b=new _(v?u:"^(?:"+u.source+")",m),w=void 0===r?h:r>>>0;if(0===w)return[];if(0===l.length)return null===c(b,l)?[l]:[];for(var y=0,x=0,k=[];x<l.length;){b.lastIndex=v?x:0;var S,j=c(b,v?l:l.slice(x));if(null===j||(S=p(s(b.lastIndex+(v?0:x)),l.length))===y)x=i(l,x,f);else{if(k.push(l.slice(y,x)),k.length===w)return k;for(var P=1;P<=j.length-1;P++)if(k.push(j[P]),k.length===w)return k;x=y=S}}return k.push(l.slice(y)),k}]}))},6108:(e,r,t)=>{"use strict";t(6774);var n=t(7007),o=t(3218),a=t(7057),i="toString",s=/./[i],c=function(e){t(7234)(RegExp.prototype,i,e,!0)};t(4253)((function(){return"/a/b"!=s.call({source:"a",flags:"b"})}))?c((function(){var e=n(this);return"/".concat(e.source,"/","flags"in e?e.flags:!a&&e instanceof RegExp?o.call(e):void 0)})):s.name!=i&&c((function(){return s.call(this)}))},2850:(e,r,t)=>{"use strict";var n=t(2985),o=t(2094),a="includes";n(n.P+n.F*t(8852)(a),"String",{includes:function(e){return!!~o(this,e,a).indexOf(e,arguments.length>1?arguments[1]:void 0)}})},7732:(e,r,t)=>{"use strict";var n=t(2985),o=t(875),a=t(2094),i="startsWith",s=""[i];n(n.P+n.F*t(8852)(i),"String",{startsWith:function(e){var r=a(this,e,i),t=o(Math.min(arguments.length>1?arguments[1]:void 0,r.length)),n=String(e);return s?s.call(r,n,t):r.slice(t,t+n.length)===n}})},5767:(e,r,t)=>{"use strict";var n=t(3816),o=t(9181),a=t(7057),i=t(2985),s=t(7234),c=t(4728).KEY,u=t(4253),d=t(3825),p=t(2943),l=t(3953),_=t(6314),f=t(8787),m=t(6074),h=t(5541),v=t(4302),g=t(7007),b=t(5286),w=t(508),y=t(2110),x=t(1689),k=t(681),S=t(2503),j=t(9327),P=t(8693),O=t(4548),M=t(9275),E=t(7184),T=P.f,I=M.f,A=j.f,z=n.Symbol,D=n.JSON,W=D&&D.stringify,L="prototype",N=_("_hidden"),C=_("toPrimitive"),K={}.propertyIsEnumerable,R=d("symbol-registry"),F=d("symbols"),q=d("op-symbols"),J=Object[L],Z="function"==typeof z&&!!O.f,U=n.QObject,B=!U||!U[L]||!U[L].findChild,Y=a&&u((function(){return 7!=S(I({},"a",{get:function(){return I(this,"a",{value:7}).a}})).a}))?function(e,r,t){var n=T(J,r);n&&delete J[r],I(e,r,t),n&&e!==J&&I(J,r,n)}:I,V=function(e){var r=F[e]=S(z[L]);return r._k=e,r},H=Z&&"symbol"==typeof z.iterator?function(e){return"symbol"==typeof e}:function(e){return e instanceof z},$=function(e,r,t){return e===J&&$(q,r,t),g(e),r=x(r,!0),g(t),o(F,r)?(t.enumerable?(o(e,N)&&e[N][r]&&(e[N][r]=!1),t=S(t,{enumerable:k(0,!1)})):(o(e,N)||I(e,N,k(1,{})),e[N][r]=!0),Y(e,r,t)):I(e,r,t)},X=function(e,r){g(e);for(var t,n=h(r=y(r)),o=0,a=n.length;a>o;)$(e,t=n[o++],r[t]);return e},G=function(e){var r=K.call(this,e=x(e,!0));return!(this===J&&o(F,e)&&!o(q,e))&&(!(r||!o(this,e)||!o(F,e)||o(this,N)&&this[N][e])||r)},Q=function(e,r){if(e=y(e),r=x(r,!0),e!==J||!o(F,r)||o(q,r)){var t=T(e,r);return!t||!o(F,r)||o(e,N)&&e[N][r]||(t.enumerable=!0),t}},ee=function(e){for(var r,t=A(y(e)),n=[],a=0;t.length>a;)o(F,r=t[a++])||r==N||r==c||n.push(r);return n},re=function(e){for(var r,t=e===J,n=A(t?q:y(e)),a=[],i=0;n.length>i;)!o(F,r=n[i++])||t&&!o(J,r)||a.push(F[r]);return a};Z||(s((z=function(){if(this instanceof z)throw TypeError("Symbol is not a constructor!");var e=l(arguments.length>0?arguments[0]:void 0),r=function(t){this===J&&r.call(q,t),o(this,N)&&o(this[N],e)&&(this[N][e]=!1),Y(this,e,k(1,t))};return a&&B&&Y(J,e,{configurable:!0,set:r}),V(e)})[L],"toString",(function(){return this._k})),P.f=Q,M.f=$,t(616).f=j.f=ee,t(4682).f=G,O.f=re,a&&!t(4461)&&s(J,"propertyIsEnumerable",G,!0),f.f=function(e){return V(_(e))}),i(i.G+i.W+i.F*!Z,{Symbol:z});for(var te="hasInstance,isConcatSpreadable,iterator,match,replace,search,species,split,toPrimitive,toStringTag,unscopables".split(","),ne=0;te.length>ne;)_(te[ne++]);for(var oe=E(_.store),ae=0;oe.length>ae;)m(oe[ae++]);i(i.S+i.F*!Z,"Symbol",{for:function(e){return o(R,e+="")?R[e]:R[e]=z(e)},keyFor:function(e){if(!H(e))throw TypeError(e+" is not a symbol!");for(var r in R)if(R[r]===e)return r},useSetter:function(){B=!0},useSimple:function(){B=!1}}),i(i.S+i.F*!Z,"Object",{create:function(e,r){return void 0===r?S(e):X(S(e),r)},defineProperty:$,defineProperties:X,getOwnPropertyDescriptor:Q,getOwnPropertyNames:ee,getOwnPropertySymbols:re});var ie=u((function(){O.f(1)}));i(i.S+i.F*ie,"Object",{getOwnPropertySymbols:function(e){return O.f(w(e))}}),D&&i(i.S+i.F*(!Z||u((function(){var e=z();return"[null]"!=W([e])||"{}"!=W({a:e})||"{}"!=W(Object(e))}))),"JSON",{stringify:function(e){for(var r,t,n=[e],o=1;arguments.length>o;)n.push(arguments[o++]);if(t=r=n[1],(b(r)||void 0!==e)&&!H(e))return v(r)||(r=function(e,r){if("function"==typeof t&&(r=t.call(this,e,r)),!H(r))return r}),n[1]=r,W.apply(D,n)}}),z[L][C]||t(7728)(z[L],C,z[L].valueOf),p(z,"Symbol"),p(Math,"Math",!0),p(n.JSON,"JSON",!0)},2773:(e,r,t)=>{"use strict";var n=t(2985),o=t(9315)(!0);n(n.P,"Array",{includes:function(e){return o(this,e,arguments.length>1?arguments[1]:void 0)}}),t(7722)("includes")},8351:(e,r,t)=>{var n=t(2985),o=t(7643),a=t(2110),i=t(8693),s=t(2811);n(n.S,"Object",{getOwnPropertyDescriptors:function(e){for(var r,t,n=a(e),c=i.f,u=o(n),d={},p=0;u.length>p;)void 0!==(t=c(n,r=u[p++]))&&s(d,r,t);return d}})},6409:(e,r,t)=>{var n=t(2985),o=t(1131)(!1);n(n.S,"Object",{values:function(e){return o(e)}})},9665:(e,r,t)=>{t(6074)("asyncIterator")},1337:(e,r,t)=>{"use strict";t.d(r,{Z:()=>s});var n=t(8081),o=t.n(n),a=t(3645),i=t.n(a)()(o());i.push([e.id,'html,body{height:100%;width:100%;min-width:1100px;overflow:hidden;font-size:16px;padding:0;margin:0;font-family:"Open Sans";color:#4a4a4a}.main{height:100%;position:relative}#zmmtg-root-helper{width:100%;height:100%;position:fixed;top:0;left:0;bottom:0;right:0;margin:auto;background-color:#fff}.root-inner{width:100%;height:100%}.joinWindowBtn{height:46px !important;line-height:inherit !important;color:#fff !important;font-size:18px !important;text-align:center !important;min-width:200px !important;background-color:#2d8cff !important;border-color:#2d8cff !important}.mini-layout{display:-webkit-box;display:-ms-flexbox;display:flex;margin:auto;-webkit-box-pack:center;-ms-flex-pack:center;justify-content:center;margin-top:15%;padding:0 10% 0 10%;min-height:250px}.page-header{font-style:normal;font-weight:bold;font-size:24px;line-height:29px;-webkit-box-align:center;-ms-flex-align:center;align-items:center;color:#232333}.mini-layout-body p{margin-top:10px;font-size:14px;line-height:24px;color:#222230}#zoom-websdk-confirm{margin-top:20px}.login-btn2{display:block;border-radius:8px;line-height:40px;border:1px solid #babacc;background-color:#0e72ed;font-style:normal;font-weight:700;color:#fff;position:relative;min-width:100px;text-align:center}.login-btn2:hover{background-color:#e7f1fd;color:#295ebd;border-color:#cfe3fb;text-decoration:none}.login-btn2+.login-btn2{margin-top:8px}.form-group .login-btn2{margin:auto}.login-btn2:before{content:"";width:18px;height:18px;position:absolute;left:12px;top:11px;background-size:18px 18px;background-position:center center;background-repeat:no-repeat}.loading-layer{z-index:1000;background-color:#000;position:absolute;left:0px;right:0px;top:0px;bottom:0px;width:100%;height:100%}.loading-layer--reset-webclient{opacity:.9}.loading-layer__text-field{text-align:center;position:absolute;left:50%;top:50%;-webkit-transform:translate(-50%, -50%);-ms-transform:translate(-50%, -50%);transform:translate(-50%, -50%);color:#fff}.loading-layer__title{font-weight:bold;font-size:24px}.loading-layer__text{margin-top:24px;font-size:24px}.loading-layer__dot-ani{display:inline-block;height:12px;line-height:12px;overflow:hidden}.loading-layer__dot-ani::after{display:inline-table;white-space:pre;content:"\\a.\\a  ..\\a   ...";-webkit-animation:spin 2s steps(4) infinite;animation:spin 2s steps(4) infinite}@-webkit-keyframes spin{to{-webkit-transform:translateY(-48px);transform:translateY(-48px)}}@keyframes spin{to{-webkit-transform:translateY(-48px);transform:translateY(-48px)}}.hidden{display:none}',""]);const s=i},3645:e=>{"use strict";e.exports=function(e){var r=[];return r.toString=function(){return this.map((function(r){var t="",n=void 0!==r[5];return r[4]&&(t+="@supports (".concat(r[4],") {")),r[2]&&(t+="@media ".concat(r[2]," {")),n&&(t+="@layer".concat(r[5].length>0?" ".concat(r[5]):""," {")),t+=e(r),n&&(t+="}"),r[2]&&(t+="}"),r[4]&&(t+="}"),t})).join("")},r.i=function(e,t,n,o,a){"string"==typeof e&&(e=[[null,e,void 0]]);var i={};if(n)for(var s=0;s<this.length;s++){var c=this[s][0];null!=c&&(i[c]=!0)}for(var u=0;u<e.length;u++){var d=[].concat(e[u]);n&&i[d[0]]||(void 0!==a&&(void 0===d[5]||(d[1]="@layer".concat(d[5].length>0?" ".concat(d[5]):""," {").concat(d[1],"}")),d[5]=a),t&&(d[2]?(d[1]="@media ".concat(d[2]," {").concat(d[1],"}"),d[2]=t):d[2]=t),o&&(d[4]?(d[1]="@supports (".concat(d[4],") {").concat(d[1],"}"),d[4]=o):d[4]="".concat(o)),r.push(d))}},r}},8081:e=>{"use strict";e.exports=function(e){return e[1]}},802:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>v});var n=t(3379),o=t.n(n),a=t(7795),i=t.n(a),s=t(569),c=t.n(s),u=t(3565),d=t.n(u),p=t(9216),l=t.n(p),_=t(4589),f=t.n(_),m=t(1337),h={};h.styleTagTransform=f(),h.setAttributes=d(),h.insert=c().bind(null,"head"),h.domAPI=i(),h.insertStyleElement=l(),o()(m.Z,h);const v=m.Z&&m.Z.locals?m.Z.locals:void 0},3379:e=>{"use strict";var r=[];function t(e){for(var t=-1,n=0;n<r.length;n++)if(r[n].identifier===e){t=n;break}return t}function n(e,n){for(var a={},i=[],s=0;s<e.length;s++){var c=e[s],u=n.base?c[0]+n.base:c[0],d=a[u]||0,p="".concat(u," ").concat(d);a[u]=d+1;var l=t(p),_={css:c[1],media:c[2],sourceMap:c[3],supports:c[4],layer:c[5]};if(-1!==l)r[l].references++,r[l].updater(_);else{var f=o(_,n);n.byIndex=s,r.splice(s,0,{identifier:p,updater:f,references:1})}i.push(p)}return i}function o(e,r){var t=r.domAPI(r);return t.update(e),function(r){if(r){if(r.css===e.css&&r.media===e.media&&r.sourceMap===e.sourceMap&&r.supports===e.supports&&r.layer===e.layer)return;t.update(e=r)}else t.remove()}}e.exports=function(e,o){var a=n(e=e||[],o=o||{});return function(e){e=e||[];for(var i=0;i<a.length;i++){var s=t(a[i]);r[s].references--}for(var c=n(e,o),u=0;u<a.length;u++){var d=t(a[u]);0===r[d].references&&(r[d].updater(),r.splice(d,1))}a=c}}},569:e=>{"use strict";var r={};e.exports=function(e,t){var n=function(e){if(void 0===r[e]){var t=document.querySelector(e);if(window.HTMLIFrameElement&&t instanceof window.HTMLIFrameElement)try{t=t.contentDocument.head}catch(e){t=null}r[e]=t}return r[e]}(e);if(!n)throw new Error("Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.");n.appendChild(t)}},9216:e=>{"use strict";e.exports=function(e){var r=document.createElement("style");return e.setAttributes(r,e.attributes),e.insert(r,e.options),r}},3565:(e,r,t)=>{"use strict";e.exports=function(e){var r=t.nc;r&&e.setAttribute("nonce",r)}},7795:e=>{"use strict";e.exports=function(e){if("undefined"==typeof document)return{update:function(){},remove:function(){}};var r=e.insertStyleElement(e);return{update:function(t){!function(e,r,t){var n="";t.supports&&(n+="@supports (".concat(t.supports,") {")),t.media&&(n+="@media ".concat(t.media," {"));var o=void 0!==t.layer;o&&(n+="@layer".concat(t.layer.length>0?" ".concat(t.layer):""," {")),n+=t.css,o&&(n+="}"),t.media&&(n+="}"),t.supports&&(n+="}");var a=t.sourceMap;a&&"undefined"!=typeof btoa&&(n+="\n/*# sourceMappingURL=data:application/json;base64,".concat(btoa(unescape(encodeURIComponent(JSON.stringify(a))))," */")),r.styleTagTransform(n,e,r.options)}(r,e,t)},remove:function(){!function(e){if(null===e.parentNode)return!1;e.parentNode.removeChild(e)}(r)}}}},4589:e=>{"use strict";e.exports=function(e,r){if(r.styleSheet)r.styleSheet.cssText=e;else{for(;r.firstChild;)r.removeChild(r.firstChild);r.appendChild(document.createTextNode(e))}}},3897:e=>{e.exports=function(e,r){(null==r||r>e.length)&&(r=e.length);for(var t=0,n=Array(r);t<r;t++)n[t]=e[t];return n},e.exports.__esModule=!0,e.exports.default=e.exports},3405:(e,r,t)=>{var n=t(3897);e.exports=function(e){if(Array.isArray(e))return n(e)},e.exports.__esModule=!0,e.exports.default=e.exports},6690:e=>{e.exports=function(e,r){if(!(e instanceof r))throw new TypeError("Cannot call a class as a function")},e.exports.__esModule=!0,e.exports.default=e.exports},9728:(e,r,t)=>{var n=t(4062);function o(e,r){for(var t=0;t<r.length;t++){var o=r[t];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,n(o.key),o)}}e.exports=function(e,r,t){return r&&o(e.prototype,r),t&&o(e,t),Object.defineProperty(e,"prototype",{writable:!1}),e},e.exports.__esModule=!0,e.exports.default=e.exports},8416:(e,r,t)=>{var n=t(4062);e.exports=function(e,r,t){return(r=n(r))in e?Object.defineProperty(e,r,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[r]=t,e},e.exports.__esModule=!0,e.exports.default=e.exports},4836:e=>{e.exports=function(e){return e&&e.__esModule?e:{default:e}},e.exports.__esModule=!0,e.exports.default=e.exports},9498:e=>{e.exports=function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)},e.exports.__esModule=!0,e.exports.default=e.exports},2281:e=>{e.exports=function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},e.exports.__esModule=!0,e.exports.default=e.exports},861:(e,r,t)=>{var n=t(3405),o=t(9498),a=t(6116),i=t(2281);e.exports=function(e){return n(e)||o(e)||a(e)||i()},e.exports.__esModule=!0,e.exports.default=e.exports},5036:(e,r,t)=>{var n=t(8698).default;e.exports=function(e,r){if("object"!=n(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var o=t.call(e,r||"default");if("object"!=n(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(e)},e.exports.__esModule=!0,e.exports.default=e.exports},4062:(e,r,t)=>{var n=t(8698).default,o=t(5036);e.exports=function(e){var r=o(e,"string");return"symbol"==n(r)?r:r+""},e.exports.__esModule=!0,e.exports.default=e.exports},8698:e=>{function r(t){return e.exports=r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e.exports.__esModule=!0,e.exports.default=e.exports,r(t)}e.exports=r,e.exports.__esModule=!0,e.exports.default=e.exports},6116:(e,r,t)=>{var n=t(3897);e.exports=function(e,r){if(e){if("string"==typeof e)return n(e,r);var t={}.toString.call(e).slice(8,-1);return"Object"===t&&e.constructor&&(t=e.constructor.name),"Map"===t||"Set"===t?Array.from(e):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?n(e,r):void 0}},e.exports.__esModule=!0,e.exports.default=e.exports}},r={};function t(n){var o=r[n];if(void 0!==o)return o.exports;var a=r[n]={id:n,exports:{}};return e[n](a,a.exports,t),a.exports}t.n=e=>{var r=e&&e.__esModule?()=>e.default:()=>e;return t.d(r,{a:r}),r},t.d=(e,r)=>{for(var n in r)t.o(r,n)&&!t.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:r[n]})},t.o=(e,r)=>Object.prototype.hasOwnProperty.call(e,r),t.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},t.nc=void 0,t(5767),t(9665),t(7732),t(6997),t(2850),t(6409),t(2310),t(2773),t(7541)})()</script></head><body><div id="zmmtg-root-helper"></div><script>function documentReady(){ZoomMtg.initMeetingSDKHelper()}"loading"!==document.readyState?documentReady():document.addEventListener("DOMContentLoaded",documentReady)</script></body></html>