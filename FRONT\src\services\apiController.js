import axios from 'axios';

// Get the current hostname
const hostname = window.location.hostname;

// Determine the base URL based on environment
const BASE_URL = process.env.REACT_APP_API_URL || 
  (hostname === 'localhost' ? 'http://localhost:4000' : 'https://api.nxgenvarsity.com');

console.log('API Base URL:', BASE_URL);


console.log("BASE_URL frontend:",BASE_URL);
const api = axios.create({
  baseURL: BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add request interceptor with better error logging
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = token;
    }
    console.log('Making request to:', config.baseURL + config.url, {
      method: config.method,
      headers: config.headers
    });
    return config;
  },
  (error) => {
    console.error('Request interceptor error:', error);
    return Promise.reject(error);
  }
);

// Add response interceptor with better error handling
api.interceptors.response.use(
  (response) => {
    console.log('Response received:', {
      url: response.config.url,
      status: response.status,
      data: response.data
    });
    return response;
  },
  (error) => {
    if (error.code === 'ECONNABORTED') {
      console.error('Request timeout:', {
        url: error.config.url,
        method: error.config.method,
        timeout: error.config.timeout
      });
    } else if (!error.response) {
      console.error('Network error:', {
        url: error.config?.url,
        message: 'No response received from server',
        error: error.message
      });
    } else {
      console.error('API error:', {
        url: error.config.url,
        status: error.response.status,
        data: error.response.data
      });
    }
    return Promise.reject(error);
  }
);

class ApiController {
  static async get(endpoint, params = {}) {
    try {
      const response = await api.get(endpoint, { params });
      return response.data;
    } catch (error) {
      ApiController.handleError(error);
      throw error;
    }
  }

  static async post(endpoint, data = {}) {
    console.log("POST request to:",endpoint);
    console.log("Data being sent:", data);
    try {
      const response = await api.post(endpoint, data);
      console.log("POST request response:", response);
      return response.data;
    } catch (error) {
      console.error("POST request error:", error);
      ApiController.handleError(error);
      throw error;
    }
  }

  static async put(endpoint, data = {}) {
    try {
      const response = await api.put(endpoint, data);
      return response.data;
    } catch (error) {
      ApiController.handleError(error);
      throw error;
    }
  }

  static async patch(endpoint, data = {}) {
    try {
      const response = await api.patch(endpoint, data);
      return response.data;
    } catch (error) {
      ApiController.handleError(error);
      throw error;
    }
  }

  static async delete(endpoint) {
    try {
      const response = await api.delete(endpoint);
      return response.data;
    } catch (error) {
      ApiController.handleError(error);
      throw error;
    }
  }

  static async uploadFile(endpoint, formData, additionalData = {}, onProgress = null) {
    try {
      console.log('UPLOAD_FILE called with endpointd:',endpoint);
      console.log('Base URL:', api.defaults.baseURL);
      console.log('Full URL will be:', api.defaults.baseURL + endpoint);

      // Add any additional data to the existing FormData
      Object.keys(additionalData).forEach(key => {
        formData.append(key, additionalData[key]);
      });

      console.log('FormData contents before request:');
      for (let pair of formData.entries()) {
        if (pair[1] instanceof File) {
          console.log(`${pair[0]}: File(${pair[1].name}, ${pair[1].size} bytes, ${pair[1].type})`);
        } else {
          console.log(`${pair[0]}: ${pair[1]}`);
        }
      }

      const response = await api.post(endpoint, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        onUploadProgress: onProgress
          ? (progressEvent) => {
              const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
              onProgress(percentCompleted);
            }
          : undefined,
      });

      console.log('Upload successful, response:', response);
      return response.data;
    } catch (error) {
      console.error('Upload failed with error:', error);
      ApiController.handleError(error);
      throw error;
    }
  }

  static async uploadFileWithMethod(endpoint, formData, method = 'POST', additionalData = {}, onProgress = null) {
    try {
      // Add any additional data to the existing FormData
      Object.keys(additionalData).forEach(key => {
        formData.append(key, additionalData[key]);
      });

      // Choose the appropriate axios method
      const axiosMethod = method.toLowerCase() === 'put' ? api.put :
                         method.toLowerCase() === 'patch' ? api.patch :
                         api.post;

      const response = await axiosMethod(endpoint, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        onUploadProgress: onProgress
          ? (progressEvent) => {
              const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
              onProgress(percentCompleted);
            }
          : undefined,
      });

      return response.data;
    } catch (error) {
      ApiController.handleError(error);
      throw error;
    }
  }

  static async uploadMultipleFiles(endpoint, files, additionalData = {}, onProgress = null) {
    try {
      const formData = new FormData();

      files.forEach((file, index) => {
        formData.append(`files[${index}]`, file);
      });

      Object.keys(additionalData).forEach(key => {
        formData.append(key, additionalData[key]);
      });

      const response = await api.post(endpoint, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        onUploadProgress: onProgress
          ? (progressEvent) => {
              const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
              onProgress(percentCompleted);
            }
          : undefined,
      });

      return response.data;
    } catch (error) {
      ApiController.handleError(error);
      throw error;
    }
  }

  static handleError(error) {
    if (error.response) {
      console.error('Response Error:', error.response.data);
      console.error('Status:', error.response.status);
      console.error('Headers:', error.response.headers);
    } else if (error.request) {
      console.error('Request Error:', error.request);
    } else {
      console.error('Error:', error.message);
    }
  }
}

export const GET = ApiController.get;
export const POST = ApiController.post;
export const PUT = ApiController.put;
export const PATCH = ApiController.patch;
export const DELETE = ApiController.delete;
export const UPLOAD_FILE = ApiController.uploadFile;
export const UPLOAD_FILE_WITH_METHOD = ApiController.uploadFileWithMethod;
export const UPLOAD_MULTIPLE_FILES = ApiController.uploadMultipleFiles;

export default ApiController;
