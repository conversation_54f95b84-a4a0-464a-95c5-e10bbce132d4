const mysql = require('mysql2/promise');
const os = require('os');
require('dotenv').config(); 

let mysqlServerConnection;



const getIPAddress = () => {
  const networkInterfaces = os.networkInterfaces();
  for (const interfaceName in networkInterfaces) {
      const interfaces = networkInterfaces[interfaceName];
      for (const iface of interfaces) {
          if (iface.family === 'IPv4' && !iface.internal) {
             
              if (iface.address !== "*************"){
                  return 'local_server';
              }
              return iface.address;
          }
      }
  }
  return 'localhost'; // Fallback if no IP address is found
};

let ipconfig = getIPAddress();

if (process.env.IS_PROD === 'true' || ipconfig === '*************') {
  console.log("Production Database");
  mysqlServerConnection = mysql.createPool({
    host: process.env.PROD_DB_IP,
    user: process.env.PROD_DB_USER,
    password: process.env.PROD_DB_PASSWORD,
    // Connection pool configuration
    connectionLimit: 10,
    queueLimit: 0,
    acquireTimeout: 60000,
    timeout: 60000,
    reconnect: true,
    // Handle connection timeouts
    idleTimeout: 300000, // 5 minutes
    // Enable keep alive
    keepAliveInitialDelay: 0,
    enableKeepAlive: true,
    // Handle disconnections
    handleDisconnects: true
  });
} else {
  console.log("Development Database", process.env.IS_PROD);
  mysqlServerConnection = mysql.createPool({
    host: process.env.DEV_DB_IP,
    user: process.env.DEV_DB_USER,
    password: process.env.DEV_DB_PASSWORD,
    // Connection pool configuration
    connectionLimit: 10,
    queueLimit: 0,
    acquireTimeout: 60000,
    timeout: 60000,
    reconnect: true,
    // Handle connection timeouts
    idleTimeout: 300000, // 5 minutes
    // Enable keep alive
    keepAliveInitialDelay: 0,
    enableKeepAlive: true,
    // Handle disconnections
    handleDisconnects: true
  });
}

// mysqlServerConnection = mysql.createPool({
//   host: process.env.DB_IP,
//   user: process.env.DB_USER,
//   password: process.env.DB_PASSWORD
// });

// Add connection error handling
mysqlServerConnection.on('connection', function (connection) {
  console.log('Connected as id ' + connection.threadId);
});

mysqlServerConnection.on('error', function(err) {
  console.log('Database error', err);
  if(err.code === 'PROTOCOL_CONNECTION_LOST') {
    console.log('Database connection was closed.');
  }
  if(err.code === 'ER_CON_COUNT_ERROR') {
    console.log('Database has too many connections.');
  }
  if(err.code === 'ECONNREFUSED') {
    console.log('Database connection was refused.');
  }
  if(err.code === 'ECONNRESET') {
    console.log('Database connection was reset.');
  }
});

// Retry wrapper for database queries
const retryQuery = async (queryFn, maxRetries = 3) => {
  for (let i = 0; i < maxRetries; i++) {
    try {
      return await queryFn();
    } catch (error) {
      if ((error.code === 'ECONNRESET' || error.code === 'PROTOCOL_CONNECTION_LOST') && i < maxRetries - 1) {
        console.log(`Database connection error, retrying... (${i + 1}/${maxRetries})`);
        await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1))); // Exponential backoff
        continue;
      }
      throw error;
    }
  }
};

module.exports = { mysqlServerConnection, retryQuery };
