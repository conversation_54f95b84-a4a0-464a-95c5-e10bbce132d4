const { hashPassword, Validate, generateAccessToken, generateRefreshToken } = require('../../../tools/tools');
const { mysqlServerConnection } = require("../../../db/db");
const { loginValidation } = require('../../../middleware/Validator');
const { validationResult } = require('express-validator');
const bcrypt = require('bcrypt');
const { createOrganization } = require('../../db/utils/createOrganizationDb');
const admin = require('../../../helper/firbaseCongfig');
const maindb = process.env.MAIN_DB;
const webpush = require('web-push');


const getSuperAdminNotificationSettings = async (req, res) => {
    try {
        const userId = req.user.userId; // Get user ID from authenticated request
        const dbName = req.user.db_name; // Assuming DB name is attached to the user object
        console.log("userId:", userId, "dbName:", dbName);
        // Build the dynamic SQL query
        const getQuery = `
            SELECT *
            FROM ${dbName}.notification_settings
            WHERE user_id =?
        `;
        // Execute the get query
        const [result] = await mysqlServerConnection.query(getQuery, userId);
        // Check if any rows were found
        if (result.length === 0) {
            return res.status(404).json({
                success: false,
                data: { error_msg: 'No notification settings found for the specified user ID.' }
            });
        }
        // Return the notification settings
        return res.status(200).json({
            success: true,
            data: result[0]
        });
    } catch (error) {
        console.error('Error retrieving notification settings:', error);
        return res.status(500).json({
            success: false,
            data: { error_msg: 'Internal server error.' }
        });
    }
};

const updateSuperAdminNotificationSettings = async (req, res) => {
    try {
        const { mail_notification, invoice_mail, push_notification } = req.body; // Extract notification settings from request body
        const userId = req.user.userId; // Get user ID from authenticated request
        const dbName = req.user.db_name; // Assuming DB name is attached to the user object

        console.log("mail_notification:", mail_notification, "invoice_mail:", invoice_mail, "push_notification:", push_notification);

        // Initialize query parts and parameters
        let updateFields = [];
        let updateParams = [];

        // Check each setting and add to query if provided
        if (mail_notification !== undefined) {
            updateFields.push("mail_notification = ?");
            updateParams.push(mail_notification);
        }

        if (invoice_mail !== undefined) {
            updateFields.push("invoice_mail = ?");
            updateParams.push(invoice_mail);
        }

        if (push_notification !== undefined) {
            updateFields.push("push_notification = ?");
            updateParams.push(push_notification);
        }

        // If no fields are provided in the request body
        if (updateFields.length === 0) {
            return res.status(400).json({
                success: false,
                data: { error_msg: 'At least one field (mail_notification, invoice_mail, push_notification) is required.' }
            });
        }

        // Add userId to the updateParams array for the WHERE clause
        updateParams.push(userId);
        console.log("Please give me correct result",updateParams);
        // Build the dynamic SQL query
        const updateQuery = `
            UPDATE ${dbName}.notification_settings
            SET ${updateFields.join(", ")}
            WHERE user_id = ?
        `;

        // Execute the update query
        const [result] = await mysqlServerConnection.query(updateQuery, updateParams);

        // Check if any rows were affected (i.e., the settings were updated)
        if (result.affectedRows === 0) {
            return res.status(404).json({
                success: false,
                data: { error_msg: 'No notification settings found for the specified user ID.' }
            });
        }

        return res.status(200).json({
            success: true,
            data: { message: 'Settings updated.' }
        });
    } catch (error) {
        console.error('Error updating notification settings:', error);
        return res.status(500).json({
            success: false,
            data: { error_msg: 'Internal server error.' }
        });
    }
};

module.exports = { getSuperAdminNotificationSettings, updateSuperAdminNotificationSettings };
