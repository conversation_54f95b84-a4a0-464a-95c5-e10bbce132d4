import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Icon } from '@iconify/react';
import './CourseQuiz.css';
import { getCourseAssessment, submitAssessmentWithResult } from '../../../../services/userService';

function CourseQuiz({ assessmentId, moduleData, onAssessmentComplete }) {
  const navigate = useNavigate();
  console.log('moduleData', moduleData);
  const [currentQuestion, setCurrentQuestion] = useState(0);
  const [selectedAnswers, setSelectedAnswers] = useState({});
  const [showResults, setShowResults] = useState(false);
  const [timeLeft, setTimeLeft] = useState(300); // 5 minutes in seconds
  const [quizStarted, setQuizStarted] = useState(false);
  const [questions, setQuestions] = useState([]);
  const [quizData, setQuizData] = useState({
    duration: 5,
    pass_percentage: 60,
    total_questions: 0,
    is_attempted: 0,
    result: null
  });
  const [submissionResult, setSubmissionResult] = useState(null);

  useEffect(() => {
    getAssessmentQuestions();
    // Reset component state when assessment changes
    setCurrentQuestion(0);
    setSelectedAnswers({});
    setShowResults(false);
    setQuizStarted(false);
    setSubmissionResult(null);
  }, [moduleData.id]);

  const getAssessmentQuestions = async () => {
    try {
      const response = await getCourseAssessment({
        assessment_id: moduleData.id
      });

      if (response.success) {
        setQuestions(response.data);
        setQuizData({
          duration: response.duration || 5,
          pass_percentage: response.pass_percentage || 60,
          total_questions: response.total_questions || 0,
          is_attempted: response.is_attempted || 0,
          result: response.result || null
        });
        setTimeLeft(response.duration * 60 || 300); // Convert minutes to seconds
      }
      console.log('assessment questions', response);
    } catch (error) {
      console.log('error-----------------:', error);
    }
  };



  // Timer logic
  React.useEffect(() => {
    if (quizStarted && timeLeft > 0 && !showResults) {
      const timer = setInterval(() => {
        setTimeLeft((prev) => {
          if (prev <= 1) {
            clearInterval(timer);
            setShowResults(true);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
      return () => clearInterval(timer);
    }
  }, [quizStarted, timeLeft, showResults]);

  const formatTime = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const handleStartQuiz = () => {
    setCurrentQuestion(0);
    setSelectedAnswers({});
    setShowResults(false);
    setTimeLeft(quizData.duration * 60 || 300);
    setQuizStarted(true);
  };

  const handleOptionSelect = (questionId, optionId) => {
    setSelectedAnswers(prev => {
      const currentSelections = prev[questionId] || [];
      const updatedSelections = currentSelections.includes(optionId)
        ? currentSelections.filter(id => id !== optionId)
        : [...currentSelections, optionId];

      return {
        ...prev,
        [questionId]: updatedSelections
      };
    });
  };

  const handleNext = () => {
    if (currentQuestion < questions.length - 1) {
      setCurrentQuestion(currentQuestion + 1);
    }
  };

  const handlePrevious = () => {
    if (currentQuestion > 0) {
      setCurrentQuestion(currentQuestion - 1);
    }
  };

  const handleSubmit = async () => {
    try {
      const answers = questions.map(question => {
        const selectedOptionIds = selectedAnswers[question.id] || [];
        const correctOptions = question.options.filter(opt => opt.is_correct);

        return {
          question_id: question.id,
          selected_option_id: selectedOptionIds,
          correct_option_id: correctOptions.map(opt => opt.id),
          correct_value: correctOptions.map(opt => opt.content_value)
        };
      });

      const payload = {
        assessment_id: moduleData.id,
        answers: answers
      };

      console.log('Submitting assessment with payload:', payload);

      const response = await submitAssessmentWithResult(payload);
      console.log('Assessment submission response:', response);

      setSubmissionResult(response.result);
      setShowResults(true);

      // Call the completion callback if assessment is passed
      if (onAssessmentComplete && response.result) {
        const isPassed = response.result.pass_status === "pass";
        console.log('🎯 Assessment completed:', {
          assessmentId: moduleData.id,
          isPassed,
          percentage: response.result.percentage
        });

        // Call the parent component's callback
        onAssessmentComplete(moduleData.id, isPassed);
      }
    } catch (error) {
      console.error('Error submitting assessment:', error);
    }
  };

  const calculateScore = () => {
    let score = 0;
    questions.forEach(question => {
      const selectedOptionIds = selectedAnswers[question.id] || [];
      const correctOptions = question.options.filter(opt => opt.is_correct);
      if (selectedOptionIds.every(id => correctOptions.some(opt => opt.id === id))) {
        score++;
      }
    });
    return score;
  };

  const getScorePercentage = () => {
    return (calculateScore() / questions.length) * 100;
  };

  const renderQuestionContent = (question) => {
    switch (question.question_content_type) {
      case 'image':
        return (
          <div className="question-content">
            <div className="question-text">{question.question}</div>
            {question.question_media_url && (
              <div className="question-media-container">
                <img
                  src={question.question_media_url}
                  alt={question.question}
                  className="question-image"
                />
              </div>
            )}
          </div>
        );
      case 'audio':
        return (
          <div className="question-content">
            <div className="question-text">{question.question}</div>
            {question.question_media_url && (
              <div className="question-media-container">
                <audio controls className="question-audio">
                  <source src={question.question_media_url} type="audio/mpeg" />
                  Your browser does not support the audio element.
                </audio>
              </div>
            )}
          </div>
        );
      case 'text':
      default:
        return (
          <div className="question-content">
            <div className="question-text">{question.question}</div>
          </div>
        );
    }
  };

  const getOptionLetter = (index) => {
    return String.fromCharCode(65 + index); // 65 is ASCII for 'A'
  };

  const renderOptionContent = (option) => {
    switch (option.content_type) {
      case 'image':
        return (
          <div className="option-content-wrapper">
            <span className="option-text">{option.content_value}</span>
            <div className="option-media-container">
              {option.media_url && (
                <img
                  src={option.media_url}
                  alt={option.content_value}
                  className="option-image"
                />
              )}
            </div>
          </div>
        );
      case 'audio':
        return (
          <div className="option-content-wrapper">
            <span className="option-text">{option.content_value}</span>
            <div className="option-media-container">
              {option.media_url && (
                <audio controls className="option-audio">
                  <source src={option.media_url} type="audio/mpeg" />
                  Your browser does not support the audio element.
                </audio>
              )}
            </div>
          </div>
        );
      case 'text':
      default:
        return (
          <div className="option-content-wrapper">
            <span className="option-text">{option.content_value}</span>
          </div>
        );
    }
  };

  const renderResults = () => {
    if (!showResults || !submissionResult) return null;

    return (
      <div className="quiz-container">
        <div className="quiz-results">
          <h2>Quiz Results</h2>

          <div className="score-circle">
            <div className="score-percentage">{submissionResult.percentage}.0%</div>
            <div className="score-label">Score</div>
          </div>

          <div className="result-stats">
            <div className="stat-item">
              <Icon icon="mdi:help-circle-outline" className="stat-icon" />
              <span className="stat-text">Total Questions: {submissionResult.total_questions}</span>
            </div>

            <div className="stat-item">
              <Icon icon="mdi:checkbox-marked-circle-outline" className="stat-icon" />
              <span className="stat-text">Attempted Questions: {submissionResult.attempted_questions}</span>
            </div>

            <div className="stat-item">
              <Icon icon="mdi:check-circle" className="stat-icon" />
              <span className="stat-text">Passing Score: {quizData.pass_percentage}%</span>
            </div>

            <div className="stat-item correct">
              <Icon icon="mdi:check-circle" className="stat-icon" />
              <span className="stat-text">Correct Answers: {submissionResult.correct_answers}</span>
            </div>

            <div className="stat-item incorrect">
              <Icon icon="mdi:close-circle" className="stat-icon" />
              <span className="stat-text">Wrong Answers: {submissionResult.wrong_answers}</span>
            </div>
          </div>

          <div className={`pass-badge ${submissionResult.pass_status === "pass" ? "passed" : "failed"}`}>
            <Icon icon={submissionResult.pass_status === "pass" ? "mdi:check-circle" : "mdi:close-circle"} />
            <span>{submissionResult.pass_status === "pass" ? "Passed" : "Failed"}</span>
          </div>

          <div className="row d-flex justify-content-center mt-4">
            <div className="col-12 col-md-6">
              <button
                className="btn btn-warning mb-2"
                onClick={handleStartQuiz}
              >
                <Icon icon="mdi:refresh" className="btn-icon" />
                Start Again
              </button>
              {/* add the condition id i attempt is 1 show the result view button  */}
              <button
                className="btn btn-primary"
                onClick={handleReviewAssessment}

              >
                <Icon icon="mdi:eye" className="btn-icon" />
                Review Assessment Result
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  };

  const handleReviewAssessment = () => {
    console.log('moduleData', moduleData.id);
    navigate(`/user/courses/Result`, {
      state: {
        assessmentId: moduleData.id,
      }
    });
  };

  if (showResults && submissionResult) {
    return renderResults();
  }

  if (!quizStarted) {
    return (
      <div className="quiz-container p-2 p-md-5">
        <div className="quiz-intro p-0 px-md-5 py-md-2">
          <h1>Assessment</h1>

          {quizData.is_attempted === 1 && (
            <div className="previous-attempt-results">
              <h3 className="mb-4">Previous Attempt Results</h3>
              <div className="score-circle mb-4">
                <div className="score-percentage">{quizData.result?.percentage}%</div>
                <div className="score-label">Score</div>
              </div>

              <div className="result-stats">
                <div className="stat-item">
                  <Icon icon="mdi:help-circle-outline" className="stat-icon" />
                  <span className="stat-text">Total Questions: {quizData.result?.total_questions}</span>
                </div>

                <div className="stat-item">
                  <Icon icon="mdi:checkbox-marked-circle-outline" className="stat-icon" />
                  <span className="stat-text">Attempted Questions: {quizData.result?.attempted_questions}</span>
                </div>

                <div className="stat-item correct">
                  <Icon icon="mdi:check-circle" className="stat-icon" />
                  <span className="stat-text">Correct Answers: {quizData.result?.correct_answers}</span>
                </div>

                <div className="stat-item incorrect">
                  <Icon icon="mdi:close-circle" className="stat-icon" />
                  <span className="stat-text">Wrong Answers: {quizData.result?.wrong_answers}</span>
                </div>
              </div>

              <div className={`pass-badge ${quizData.result?.pass_status === "pass" ? "passed" : "failed"}`}>
                <Icon icon={quizData.result?.pass_status === "pass" ? "mdi:check-circle" : "mdi:close-circle"} />
                <span>{quizData.result?.pass_status === "pass" ? "Passed" : "Failed"}</span>
              </div>
            </div>
          )}

          <div className="quiz-info mt-4">
            <div className="info-item">
              <Icon icon="mdi:help-circle-outline" className="info-icon" />
              <span>Total Questions: {quizData.total_questions}</span>
            </div>
            <div className="info-item">
              <Icon icon="mdi:clock-outline" className="info-icon" />
              <span>Duration: {quizData.duration} minutes</span>
            </div>
            <div className="info-item">
              <Icon icon="mdi:trophy-outline" className="info-icon" />
              <span>Passing Score: {quizData.pass_percentage}%</span>
            </div>
          </div>

          <div className="instructions p-2 p-md-4">
            <h2>Instructions:</h2>
            <ul>
              <li>Read each question carefully before answering</li>
              <li>You can go back to previous questions</li>
              <li>Timer will start once you begin the quiz</li>
              <li>Questions may contain text, images, or audio content</li>
            </ul>
          </div>



          <div className="row d-flex justify-content-center">

            <div className="col-12 col-md-6">
 
              <button
                className={`btn  mb-2 ${quizData.is_attempted ? 'btn-warning' : 'btn-primary'}`}
                onClick={handleStartQuiz}
              >
                <Icon icon="mdi:play" className="btn-icon" />
                {quizData.is_attempted ? 'Start Again' : 'Start Quiz'}
              </button>
              {/* add the condition id i attempt is 1 show the result view button  */}
              
              {quizData.is_attempted === 1 && (
                <button
                  className="btn btn-primary"
                  onClick={handleReviewAssessment}
                >
                  <Icon icon="mdi:eye" className="btn-icon" />
                  Review Assessment Result
                </button>
              )} 

            </div>

          </div>

        </div>
      </div>
    );
  }

  const question = questions[currentQuestion];
  const isLastQuestion = currentQuestion === questions.length - 1;

  if (!question) {
    return <div>Loading...</div>;
  }

  return (
    <div className="quiz-container">
      <div className="quiz-header">
        <div className="question-progress">
          <span className="current-question">{currentQuestion + 1}</span>
          <span className="total-questions">/{questions.length}</span>
        </div>
        <div className="timer">
          <Icon icon="mdi:clock-outline" className="timer-icon" />
          {formatTime(timeLeft)}
        </div>
      </div>

      <div className="question-container">
        {renderQuestionContent(question)}

        <div className="options-container">
          <div className="options-label">Select your answer(s):</div>
          {question.options.map((option, index) => (
            <div
              key={option.id}
              className={`option ${(selectedAnswers[question.id] || []).includes(option.id) ? 'selected' : ''}`}
              onClick={() => handleOptionSelect(question.id, option.id)}
            >
              <span className="option-letter">{getOptionLetter(index)}</span>
              {renderOptionContent(option)}
            </div>
          ))}
        </div>

        <div className="navigation-buttons">
          <div className="nav-buttons-left">
            {currentQuestion > 0 && (
              <button className="nav-btn prev-btn" onClick={handlePrevious}>
                <Icon icon="mdi:chevron-left" />
                Previous
              </button>
            )}
          </div>
          <div className="nav-buttons-right">
            {!isLastQuestion ? (
              <button
                className="nav-btn next-btn"
                onClick={handleNext}
                disabled={!(selectedAnswers[question.id] || []).length}
              >
                Next
                <Icon icon="mdi:chevron-right" />
              </button>
            ) : (
              <button
                className="nav-btn submit-btn"
                onClick={handleSubmit}
                disabled={!(selectedAnswers[question.id] || []).length}
              >
                Submit
                <Icon icon="mdi:check" />
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

export default CourseQuiz;