import React, { useState, useEffect } from 'react';
import NoData from '../../../components/common/NoData';
import Loader from '../../../components/common/Loader';
import { Icon } from '@iconify/react';
import { getTraineeAnalytics } from '../../../services/traineeService';
import { toast } from 'react-toastify';

function TraineeAnalyticsCourseTab({ traineeId }) {
  const [loading, setLoading] = useState(false);
  const [searchLoading, setSearchLoading] = useState(false);
  const [courseData, setCourseData] = useState([]);
  const [expandedCourse, setExpandedCourse] = useState(null);
  
  // States for search and pagination
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [recordsPerPage, setRecordsPerPage] = useState(10);
  const [filteredData, setFilteredData] = useState([]);

  const fetchCourseData = async () => {
    try {
      setLoading(true);
      const response = await getTraineeAnalytics({ trainee_id: traineeId });
      console.log('Course Data---------------------:', response.data.course_completion);

      if (response.success) {
        const data = response.data.course_completion || [];
        setCourseData(data);
        setFilteredData(data);
      } else {
        toast.error(response.message || 'Failed to fetch course data');
      }
    } catch (error) {
      console.error('Error fetching course data:', error);
      toast.error('Something went wrong while fetching course data');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (traineeId) {
      fetchCourseData();
    }
  }, [traineeId]);

  // Handle search with debounce
  useEffect(() => {
    setSearchLoading(true);
    const searchTimeout = setTimeout(() => {
      const filtered = courseData.filter(course =>
        course.course_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        course.course_type.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setFilteredData(filtered);
      setCurrentPage(1);
      setSearchLoading(false);
    }, 300); // 300ms debounce

    return () => clearTimeout(searchTimeout);
  }, [searchTerm, courseData]);

  // Pagination calculations
  const indexOfLastRecord = currentPage * recordsPerPage;
  const indexOfFirstRecord = indexOfLastRecord - recordsPerPage;
  const currentRecords = filteredData.slice(indexOfFirstRecord, indexOfLastRecord);
  const totalPages = Math.ceil(filteredData.length / recordsPerPage);

  // Handlers
  const handleSearch = (e) => {
    setSearchTerm(e.target.value);
  };

  const handleRecordsPerPageChange = (e) => {
    setRecordsPerPage(Number(e.target.value));
    setCurrentPage(1);
  };

  const handlePrevPage = () => {
    setCurrentPage(prev => Math.max(prev - 1, 1));
  };

  const handleNextPage = () => {
    setCurrentPage(prev => Math.min(prev + 1, totalPages));
  };

  const toggleCourseExpand = (courseId) => {
    setExpandedCourse(expandedCourse === courseId ? null : courseId);
  };

  if (loading) {
    return (
      <div className="d-flex justify-content-center align-items-center" style={{ minHeight: '200px' }}>
        <Loader />
      </div>
    );
  }

  if (!courseData || courseData.length === 0) {
    return <NoData message="No courses found" />;
  }

  return (
    <div className="card p-2">
      <div className="row d-flex my-3">
        <div className="col-md-6 mt-2 d-flex justify-content-start align-items-center">
          <div className="position-relative" style={{ maxWidth: '300px', width: '100%' }}>
            <input
              type="search"
              className="form-control"
              placeholder="Search courses..."
              value={searchTerm}
              onChange={handleSearch}
            />
            {searchLoading && (
              <div className="position-absolute" style={{ right: '10px', top: '50%', transform: 'translateY(-50%)' }}>
                <div className="spinner-border spinner-border-sm text-primary" role="status">
                  <span className="visually-hidden">Searching...</span>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Table */}
      <div className="table-responsive">
        <table className="table table-borderless mb-0">
          <thead>
            <tr>
              <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500', width: '80px' }}>SL No.</th>
              <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500', width: '400px' }}>Course</th>
              <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500', width: '50px' }}>Type</th>
              <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500', width: '150px' }}>Progress</th>
              <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500', width: '120px' }}>Status</th>
              <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500', width: '100px' }}>Actions</th>
            </tr>
          </thead>
          <tbody>
            {searchLoading ? (
              <tr>
                <td colSpan="6" className="text-center py-4">
                  <Loader />
                </td>
              </tr>
            ) : currentRecords.length === 0 ? (
              <tr>
                <td colSpan="6" className="p-0">
                  <NoData message={searchTerm ? `No courses found for "${searchTerm}"` : "No courses available"} />
                </td>
              </tr>
            ) : (
              currentRecords.map((course, index) => (
                <React.Fragment key={course.course_id}>
                  <tr className="border-bottom">
                    <td className="py-3 align-middle">{indexOfFirstRecord + index + 1}</td>
                    <td className="py-3 align-middle">
                      <div className="d-flex align-items-center">
                        <img
                          src={course.banner_image}
                          alt={course.course_name}
                          className="rounded me-3"
                          style={{
                            height: "40px",
                            width: '60px',
                            objectFit: 'cover',
                            border: '1px solid #e9ecef'
                          }}
                          onError={(e) => {
                            e.target.onerror = null;
                            e.target.src = 'https://via.placeholder.com/60x40?text=No+Image';
                          }}
                        />
                        <div>
                          <div className="fw-semibold mb-1">{course.course_name}</div>
                          <div className="d-flex align-items-center flex-wrap gap-3">
                            <div className="d-flex align-items-center" title="Videos">
                              <Icon icon="fluent:video-24-regular" className="text-muted me-1" width="14" />
                              <span className="small text-muted">
                                <span className="me-1">Videos:</span>
                                <span className="fw-medium">{course.total_video}</span>
                              </span>
                            </div>
                            <div className="d-flex align-items-center" title="Documents">
                              <Icon icon="fluent:document-24-regular" className="text-muted me-1" width="14" />
                              <span className="small text-muted">
                                <span className="me-1">Documents:</span>
                                <span className="fw-medium">{course.total_document}</span>
                              </span>
                            </div>
                            <div className="d-flex align-items-center" title="Assessments">
                              <Icon icon="fluent:task-list-square-24-regular" className="text-muted me-1" width="14" />
                              <span className="small text-muted">
                                <span className="me-1">Assessments:</span>
                                <span className="fw-medium">{course.total_assessment}</span>
                              </span>
                            </div>
                            <div className="d-flex align-items-center" title="Surveys">
                              <Icon icon="fluent:form-24-regular" className="text-muted me-1" width="14" />
                              <span className="small text-muted">
                                <span className="me-1">Surveys:</span>
                                <span className="fw-medium">{course.total_survey}</span>
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="py-3 align-middle">{course.course_type}</td>
                    <td className="py-3 align-middle">
                      <div className="small mt-2 fw-medium">
                        {parseFloat(course.completion_percentage).toFixed(1)}%
                      </div>
                      <div className="progress" style={{ height: '8px', backgroundColor: '#e9ecef' }}>
                        <div
                          className={`progress-bar ${course.is_completed ? 'bg-success' : 'bg-primary'}`}
                          role="progressbar"
                          style={{
                            width: `${Math.min(course.completion_percentage, 100)}%`,
                            transition: 'width 0.3s ease'
                          }}
                          aria-valuenow={Math.min(course.completion_percentage, 100)}
                          aria-valuemin="0"
                          aria-valuemax="100"
                        />
                      </div>
                    </td>
                    <td className="py-3 align-middle">
                      <span className={`badge ${course.is_completed ? 'bg-success' : 'bg-warning'} px-3 py-2`}>
                        {course.is_completed ? 'Completed' : 'In Progress'}
                      </span>
                    </td>
                    <td className="py-3 align-middle">
                      <div className="d-flex gap-2">
                        <button
                          className="btn btn-outline-dark btn-sm border border-dark"
                          onClick={(e) => toggleCourseExpand(course.course_id)}
                          style={{ width: '36px', height: '36px' }}
                          title={course.assessment_results.length > 0 ? "View Assessments" : "No Assessments"}
                          disabled={course.assessment_results.length === 0}
                        >
                          <Icon
                            icon={expandedCourse === course.course_id ? 'fluent:chevron-up-24-regular' : 'fluent:task-list-square-24-regular'}
                            width="16"
                            height="16"
                            className={course.assessment_results.length === 0 ? 'text-muted' : ''}
                          />
                        </button>
                      </div>
                    </td>
                  </tr>
                  {expandedCourse === course.course_id && course.assessment_results.length > 0 && (
                    <tr>
                      <td colSpan="6" className="p-0">
                        <div className="bg-light p-3">
                          <h6 className="mb-3">Assessment Results</h6>
                          <div className="table-responsive">
                            <table className="table table-sm mb-0">
                              <thead>
                                <tr>
                                  <th>Assessment Name</th>
                                  <th>Type</th>
                                  <th>Last Attempt</th>
                                  <th>Score</th>
                                  <th>Status</th>
                                </tr>
                              </thead>
                              <tbody>
                                {course.assessment_results.map((assessment) => (
                                  <tr key={assessment.assessment_id}>
                                    <td>{assessment.assessment_name}</td>
                                    <td>
                                      <span className={`badge ${assessment.assessment_type === 'assessment' ? 'bg-info' : 'bg-secondary'}`}>
                                        {assessment.assessment_type}
                                      </span>
                                    </td>
                                    <td>{new Date(assessment.last_attempt_date).toLocaleDateString()}</td>
                                    <td>
                                      {assessment.assessment_type === 'assessment' ? (
                                        <>
                                          {assessment.correct_answers}/{assessment.total_questions}
                                          <span className="text-muted ms-2">({assessment.score_percentage}%)</span>
                                        </>
                                      ) : (
                                        '--'
                                      )}
                                    </td>
                                    <td>
                                      {assessment.assessment_type === 'assessment' && (
                                        <span className={`badge ${assessment.status === 'Passed' ? 'bg-success' : 'bg-danger'}`}>
                                          {assessment.status}
                                        </span>
                                      )}
                                      {assessment.assessment_type === 'survey' && (
                                        <span className="badge bg-success">Completed</span>
                                      )}
                                    </td>
                                  </tr>
                                ))}
                              </tbody>
                            </table>
                          </div>
                        </div>
                      </td>
                    </tr>
                  )}
                </React.Fragment>
              ))
            )}
          </tbody>
        </table>
      </div>

      <div className="row d-flex my-3">
        <div className="col-md-6 d-flex align-items-center gap-3">
          <select
            className="form-select"
            style={{ width: 'auto' }}
            value={recordsPerPage}
            onChange={handleRecordsPerPageChange}
            disabled={searchLoading}
          >
            <option value={10}>10 records per page</option>
            <option value={1}>25 records per page</option>
            <option value={50}>50 records per page</option>
          </select>
          <span className="text-muted">
            Total Records: {filteredData.length}
          </span>
        </div>
        <div className="col-md-6 d-flex justify-content-end align-items-center gap-3">
          <button
            className="btn btn-primary"
            style={{ width: '150px' }}
            onClick={handlePrevPage}
            disabled={currentPage === 1 || searchLoading}
          >
            Prev
          </button>
          <span>Page {currentPage} of {totalPages || 1}</span>
          <button
            className="btn btn-primary"
            style={{ width: '150px' }}
            onClick={handleNextPage}
            disabled={currentPage === totalPages || searchLoading}
          >
            Next
          </button>
        </div>
      </div>
    </div>
  );
}

export default TraineeAnalyticsCourseTab; 