/* Fi<PERSON> and <PERSON><PERSON> */
.filter-container {
  position: relative;
}

.filter-button {
  background-color: #fff;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  padding: 8px 16px;
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #333;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.filter-button:hover {
  background-color: #f8f9fa;
}

.filter-popup {
  position: absolute;
  top: calc(100% + 8px);
  right: 0;
  width: 300px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  overflow: hidden;
  animation: fadeIn 0.2s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.filter-popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.filter-popup-header h5 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.clear-all-btn {
  background: none;
  border: none;
  color: #3152e8;
  font-size: 14px;
  cursor: pointer;
}

.filter-section {
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
}

.filter-section h6 {
  margin: 0 0 10px 0;
  font-size: 14px;
  font-weight: 600;
  color: #555;
}

.filter-options {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.filter-option {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 6px;
  width: calc(50% - 4px);
}

.filter-option input[type="checkbox"],
.filter-option input[type="radio"] {
  accent-color: #3152e8;
  width: 16px;
  height: 16px;
}

.filter-option label {
  font-size: 14px;
  margin: 0;
  cursor: pointer;
  display: flex;
  align-items: center;
}

.rating-options .filter-option {
  width: auto;
  margin-right: 16px;
}

.star-icon {
  color: #ffc107;
  margin-left: 2px;
}

.filter-popup-footer {
  padding: 16px;
  display: flex;
  justify-content: flex-end;
}

.apply-filter-btn {
  background-color: #3152e8;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.apply-filter-btn:hover {
  background-color: #2a46cc;
}

/* Responsive styles */
@media (max-width: 576px) {
  .filter-popup {
    width: 280px;
  }

  .filter-popup:after {
    right: 80px;
  }

  .filter-option {
    width: 100%;
  }
}

/* Course Card Styles */
.course-card {
  height: 100%;
  border-radius: var(--radius-lg);
  overflow: hidden;
  transition: transform 0.3s ease;
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  box-shadow: var(--shadow-sm);
}

.course-image {
  position: relative;
  overflow: hidden;
  padding-top: 56.25%; /* 16:9 Aspect Ratio */
}

.course-image img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Override default card-body padding */
/* .card .card-body {
  padding: 0.75rem !important;
} */

.course-title {
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 0.95rem;
  line-height: 1.4;
}

.course-description {
  color: var(--text-secondary);
  font-size: 0.875rem;
  margin-bottom: 0.75rem;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  line-height: 1.5;
  height: 4.2em;
  opacity: 0.8;
}

.course-meta-info {
  margin-bottom: 0.75rem;
}

.meta-row {
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.meta-row:last-child {
  margin-bottom: 0;
}

.meta-icon {
  margin-right: 0.25rem;
  vertical-align: middle;
}

.star-icon {
  color: #ffc107;
  margin-right: 2px;
}

.course-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-top: 1px solid var(--border-color);
  padding-top: 0.75rem;
  margin-top: auto;
}

.course-price {
  font-weight: 600;
  font-size: 1rem;
  display: flex;
  align-items: center;
}

.free-price {
  color: #22c55e;
}

.paid-price {
  color: #4361ee;
}

/* ===================== */
/* ✅ WATCH BUTTON FIXES */
/* ===================== */

.watch-now-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.5rem 1.2rem;
  border-radius: 6px;
  font-size: 0.95rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
  text-align: center;
}

/* Free Button */
.watch-now-btn.free-btn {
  background-color: #22c55e;
  color: white;
}

.watch-now-btn.free-btn:hover {
  background-color: #1ea550;
  color: white;
}

/* Paid Button */
.watch-now-btn.paid-btn {
  background-color: #4361ee;
  color: white;
}

.watch-now-btn.paid-btn:hover {
  background-color: #2541df;
  color: white;
}

/* Button icon styles */
.watch-now-btn .btn-icon {
  font-size: 1.25rem;
  color: white !important;
  background: transparent !important;
  padding: 0 !important;
  border-radius: 0 !important;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Fix for Iconify SVG icons */
.watch-now-btn .btn-icon svg {
  background: transparent !important;
  fill: white !important;
  border-radius: 0 !important;
  padding: 0 !important;
}

/* Ensure icon stays white and clean on hover */
.watch-now-btn:hover .btn-icon,
.watch-now-btn:hover .btn-icon svg {
  color: white !important;
  fill: white !important;
  background: transparent !important;
}
