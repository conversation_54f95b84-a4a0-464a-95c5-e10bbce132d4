const { mysqlServerConnection } = require("../../../db/db");
const maindb = process.env.MAIN_DB;

const getRolesAndPermissions = async (req, res, next) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const offset = (page - 1) * limit;
    const search = req.query.search ? req.query.search.trim() : "";
    const status = req.query.status;

    // Main Query
    let query = `
            SELECT 
                r.id AS role_id, 
                r.name AS role_name, 
                r.is_active, 
                u.name AS created_by_name,
                u.email,
                u.profile_pic_url,
                GROUP_CONCAT(p.name) AS permissions 
            FROM ${req.user.db_name}.roles r
            LEFT JOIN ${req.user.db_name}.users u 
                ON u.id = r.created_by
            LEFT JOIN ${req.user.db_name}.role_permissions rp 
                ON r.id = rp.role_id
            LEFT JOIN ${maindb}.permissions p 
                ON rp.permission_id = p.id
            WHERE r.is_deleted = 0 AND r.name != 'trainee'
        `;

    // Add search condition
    if (search) {
      query += ` AND (r.name LIKE ? OR u.name LIKE ? OR u.email LIKE ?)`;
    }

    // Add status filter
    if (status == "true" || status == "false") {
      query += ` AND r.is_active = ?`;
    }

    // Finalize query
    query += ` GROUP BY r.id LIMIT ? OFFSET ?`;

    // Prepare parameters
    const params = [];
    if (search) {
      params.push(`%${search}%`, `%${search}%`, `%${search}%`);
    }
    if (status == "true" || status == "false") {
      params.push(status == "true" ? 1 : 0);
    }
    params.push(limit, offset);

    const [rolesWithPermissions] = await mysqlServerConnection.query(
      query,
      params
    );

    // Count query
    const [[{ totalCount }]] = await mysqlServerConnection.query(
      `
            SELECT COUNT(DISTINCT r.id) AS totalCount
            FROM ${req.user.db_name}.roles r
            LEFT JOIN ${req.user.db_name}.users u 
                ON u.id = r.created_by
            LEFT JOIN ${req.user.db_name}.role_permissions rp 
                ON r.id = rp.role_id
            LEFT JOIN ${maindb}.permissions p 
                ON rp.permission_id = p.id
            WHERE r.is_deleted = 0 AND r.name != 'trainee'
            ${
              search
                ? `AND (r.name LIKE ? OR u.name LIKE ? OR u.email LIKE ?)`
                : ""
            }
            ${status !== undefined ? `AND r.is_active = ?` : ""}
        `,
      [
        ...(search ? [`%${search}%`, `%${search}%`, `%${search}%`] : []),
        ...(status !== undefined ? [status === "true" ? 1 : 0] : []),
      ]
    );

    res.status(200).json({
      success: true,
      data: {
        rolesWithPermissions,
        pagination: {
          page,
          limit,
          totalPages: Math.ceil(totalCount / limit),
          totalCount,
        },
      },
    });
  } catch (error) {
    console.log("ERROR FETCHING ROLES", error.message);
    return next({
      statusCode: 500,
      message: error.message || "Internal Server Error",
    });
  }
};

const activateRole = async (req, res, next) => {
  try {
    const { role_id } = req.params;
    const [result] = await mysqlServerConnection.query(
      `UPDATE ${req.user.db_name}.roles SET is_active = 1 where id = ?`,
      [role_id]
    );
    console.log("Role Id ", role_id);
    res.status(200).json({
      success: true,
      message: "Role activated successfully",
    });
  } catch (error) {
    return next({
      statusCode: 500,
      message: error.message || "Internal Server Error",
    });
  }
};

const deactivateRole = async (req, res, next) => {
  try {
    const { role_id } = req.params;
    const [result] = await mysqlServerConnection.query(
      `UPDATE ${req.user.db_name}.roles SET is_active = 0 where id = ?`,
      [role_id]
    );
    console.log("Role:", role_id);
    if (result.affectedRows === 0) {
      return next({
        statusCode: 404,
        message: "Role not found or is aldready activated",
      });
    }
    res.status(200).json({
      success: true,
      message: "Role deactivated",
    });
  } catch (error) {
    return next({
      statusCode: 500,
      message: error.message || "Internal Server Error",
    });
  }
};

const deleteRole = async (req, res, next) => {
  try {
    const { role_id } = req.params;
    const [result] = await mysqlServerConnection.query(
      `UPDATE ${req.user.db_name}.roles SET is_deleted = 1 where id = ?`,
      [role_id]
    );

    if (result.affectedRows === 0) {
      return next({
        statusCode: 404,
        message: "Role not found or is already deleted",
      });
    }

    res.status(200).json({
      success: true,
      message: "Role deleted Successfully",
    });
  } catch (error) {
    return next({
      statusCode: 500,
      message: error.message || "Internal Server Error",
    });
  }
};

const createRoleWithPermissions = async (req, res) => {
  try {
    const { role_name, permission_ids, description, status } = req.body;

    const db_name = req.user.db_name;

    // Validate input
    if (
      role_name == "" ||
      !Array.isArray(permission_ids) ||
      permission_ids.length === 0
    ) {
      return res.status(400).json({
        success: false,
        error_msg:
          "Invalid input. Provide role_name, and non-empty permission_ids array.",
      });
    }

    // Start a transaction to ensure data consistency
    const connection = await mysqlServerConnection.getConnection();

    try {
      // Begin transaction
      await connection.beginTransaction();

      // Insert new role
      const [roleResult] = await connection.query(
        `INSERT INTO ${db_name}.roles 
                (name, description, is_active,created_by, role_type) 
                VALUES (?, ?, ?,?,?)`,
        [role_name, description || "", status, req.user.userId, "admin"]
      );

      const role_id = roleResult.insertId;

      // Insert role permissions
      const permissionQueries = permission_ids.map((permission_id) =>
        connection.query(
          `INSERT INTO ${db_name}.role_permissions 
                    (role_id, permission_id) 
                    VALUES (?, ?)`,
          [role_id, permission_id]
        )
      );

      // Execute all permission insertion queries
      await Promise.all(permissionQueries);

      // Commit transaction
      await connection.commit();

      return res.status(201).json({
        success: true,
        data: {
          role_id,
          role_name,
          message: "Role created successfully with specified permissions.",
        },
      });
    } catch (dbError) {
      // Rollback transaction in case of error
      await connection.rollback();
      console.error("Database error creating role:", dbError);
      return res.status(500).json({
        success: false,
        error_msg:
          "Failed to create role. Please check your input and try again.",
      });
    } finally {
      // Release the connection
      connection.release();
    }
  } catch (error) {
    console.error("Unexpected error in createRoleWithPermissions:", error);
    return res.status(500).json({
      success: false,
      error_msg: "Internal server error.",
    });
  }
};

const getAllPermissions = async (req, res) => {
  try {
    // Get permission levels for reference
    const [permissionLevels] = await mysqlServerConnection.query(`
            SELECT id, name, level_value
            FROM ${maindb}.permission_levels
            ORDER BY level_value
        `);

    const [permissions] = await mysqlServerConnection.query(`
            SELECT 
                p.id, 
                p.name, 
                p.description, 
                p.category,
                p.level_id,
                pl.name as level_name,
                pl.level_value
            FROM ${maindb}.permissions p
            LEFT JOIN ${maindb}.permission_levels pl ON p.level_id = pl.id
            ORDER BY p.category, p.id
        `);

    // Grouping permissions by category
    const categorizedPermissions = permissions.reduce((acc, p) => {
      const category = p.category || "Other";
      if (!acc[category]) {
        acc[category] = [];
      }
      acc[category].push({
        id: p.id,
        name: p.name,
        description: p.description,
        level_id: p.level_id,
        level_name: p.level_name,
        level_value: p.level_value,
      });
      return acc;
    }, {});

    return res.status(200).json({
      success: true,
      data: {
        levels: permissionLevels,
        permissions: categorizedPermissions,
      },
    });
  } catch (error) {
    console.error("Error fetching permissions:", error);
    return res.status(500).json({
      success: false,
      error_msg: "Failed to retrieve permissions.",
    });
  }
};

const getRolePermissionsById = async (req, res) => {
  try {
    const { role_id } = req.params;
    const db_name = req.user.db_name;

    console.log("Received role_id:", role_id);
    console.log("Database name:", db_name);

    // Validate role_id
    if (!role_id) {
      console.log("Error: Role ID is missing.");
      return res.status(400).json({
        success: false,
        error_msg: "Role ID is required.",
      });
    }

    // Query to fetch role details
    console.log("Fetching role details for role_id:", role_id);
    const [roleDetails] = await mysqlServerConnection.query(
      `
            SELECT 
                r.id, 
                r.name, 
                r.description, 
                r.is_active
            FROM ${db_name}.roles r
            WHERE r.id = ?`,
      [role_id]
    );

    // If role not found
    if (roleDetails.length === 0) {
      console.log("Error: Role not found for role_id:", role_id);
      return res.status(404).json({
        success: false,
        error_msg: "Role not found.",
      });
    }

    console.log("Fetched role details:", roleDetails);

    // Fetch permission levels
    console.log("Fetching permission levels...");
    const [permissionLevels] = await mysqlServerConnection.query(`
            SELECT 
                id, 
                name, 
                description, 
                level_value
            FROM ${maindb}.permission_levels
            ORDER BY level_value
        `);
    console.log("Fetched permission levels:", permissionLevels);

    // Query to fetch permissions for the role, including categories and level information
    console.log("Fetching permissions for role_id:", role_id);
    const [permissions] = await mysqlServerConnection.query(
      `
            SELECT 
                p.id, 
                p.name, 
                p.description,
                p.category,
                p.level_id,
                pl.name as level_name,
                pl.level_value
            FROM ${maindb}.permissions p
            JOIN ${db_name}.role_permissions rp ON p.id = rp.permission_id
            LEFT JOIN ${maindb}.permission_levels pl ON p.level_id = pl.id
            WHERE rp.role_id = ?
            ORDER BY p.category, p.id
        `,
      [role_id]
    );
    console.log("Fetched permissions:", permissions);

    // Grouping permissions by category
    console.log("Grouping permissions by category...");
    const categorizedPermissions = permissions.reduce((acc, p) => {
      const category = p.category || "Other"; // Default to 'Other' if no category
      if (!acc[category]) {
        acc[category] = [];
      }
      acc[category].push({
        id: p.id,
        name: p.name,
        description: p.description,
        level_id: p.level_id,
        level_name: p.level_name,
        level_value: p.level_value,
      });
      return acc;
    }, {});

    console.log("Categorized permissions:", categorizedPermissions);

    return res.status(200).json({
      success: true,
      data: {
        role: {
          id: roleDetails[0].id,
          name: roleDetails[0].name,
          description: roleDetails[0].description,
          is_active: roleDetails[0].is_active === 1,
        },
        levels: permissionLevels,
        permissions: categorizedPermissions,
      },
    });
  } catch (error) {
    console.error("Error fetching role permissions:", error);
    return res.status(500).json({
      success: false,
      error_msg: "Failed to retrieve role permissions.",
    });
  }
};




const updateRoleById = async (req, res) => {
  try {
    const { role_id } = req.params;
    const { name, description, is_active, permission_ids } = req.body;
    const db_name = req.user.db_name;
    // Validate role_id and input fields
    if (!role_id) {
      return res.status(400).json({
        success: false,
        error_msg: "Role ID is required.",
      });
    }

    // Prepare update query
    const updateFields = [];
    const updateValues = [];

    if (name) {
      updateFields.push("name = ?");
      updateValues.push(name);
    }
    if (description) {
      updateFields.push("description = ?");
      updateValues.push(description);
    }
    if (is_active !== undefined) {
      updateFields.push("is_active = ?");
      updateValues.push(is_active ? 1 : 0);
    }

    if (updateFields.length === 0) {
      return res.status(400).json({
        success: false,
        error_msg: "No fields to update.",
      });
    }

    // Execute the update query
    const query = `UPDATE ${db_name}.roles SET ${updateFields.join(
      ", "
    )} WHERE id = ?`;
    updateValues.push(role_id);

    const [result] = await mysqlServerConnection.query(query, updateValues);

    // Check if the role was updated
    if (result.affectedRows === 0) {
      return res.status(404).json({
        success: false,
        error_msg: "Role not found.",
      });
    }

    // Update permissions if provided
    if (permission_ids) {
      // Delete existing permissions
      await mysqlServerConnection.query(
        `DELETE FROM ${db_name}.role_permissions WHERE role_id = ?`,
        [role_id]
      );

      // Insert new permissions
      const permissionQueries = permission_ids.map((permission_id) =>
        mysqlServerConnection.query(
          `INSERT INTO ${db_name}.role_permissions (role_id, permission_id) VALUES (?, ?)`,
          [role_id, permission_id]
        )
      );

      // Execute all permission insertion queries
      await Promise.all(permissionQueries);
    }

    return res.status(200).json({
      success: true,
      message: "Role updated successfully.",
    });
  } catch (error) {
    console.error("Error updating role:", error);
    return res.status(500).json({
      success: false,
      error_msg: "Failed to update role.",
    });
  }
};

const getFullModuleHierarchyWithActionsd = async (req, res, next) => {
  try {
    const db_name = req.user?.db_name;
    if (!db_name) {
      throw new Error(
        "❌ db_name is missing from request. Please check login middleware."
      );
    }

    console.log("✅ Using DB:", db_name);

    // 1. Fetch all modules
    const [allModules] = await mysqlServerConnection.query(`
      SELECT 
        id AS module_id,
        parent_id,
        name,
        slug,
        icon,
        description,
        \`order\`,
        is_active
      FROM ${db_name}.permission_modules_with_new
    `);

    // 2. Fetch all actions linked to modules for all roles (include is_granted)
    const [allActions] = await mysqlServerConnection.query(`
      SELECT 
        hrp.id AS permission_id,
        hrp.role_id,
        hrp.module_id,
        hrp.action_id,
        hrp.is_granted,
        a.name AS action_name
      FROM ${db_name}.hierarchical_role_permissions hrp
      INNER JOIN ${db_name}.actions a ON a.id = hrp.action_id
      WHERE a.is_deleted = 0
    `);

    // 3. Fetch all roles except 'trainee'
    const [roles] = await mysqlServerConnection.query(`
      SELECT 
        id AS role_id,
        name AS role_name,
        description,
        role_type,
        is_active,
        created_by,
        createdAt,
        updatedAt
      FROM ${db_name}.roles
      WHERE name != 'trainee' AND is_deleted = 0
    `);

    // 4. Helper: map actions to a module for a role
    const mapActions = (roleId, moduleId) => {
      return allActions
        .filter((a) => a.role_id === roleId && a.module_id === moduleId)
        .map((a) => ({
          permission_id: a.permission_id,
          action_id: a.action_id,
          action_name: a.action_name,
          is_granted: a.is_granted, // keep as 0 or 1
        }));
    };

    // 5. Recursive builder for module tree
    const buildHierarchy = (roleId, parentId = null) => {
      return allModules
        .filter((mod) => mod.parent_id === parentId)
        .map((mod) => {
          const actions = mapActions(roleId, mod.module_id);
          const children = buildHierarchy(roleId, mod.module_id);
          return {
            module_id: mod.module_id,
            module_name: mod.name,
            slug: mod.slug,
            icon: mod.icon,
            description: mod.description,
            order: mod.order,
            is_active: mod.is_active,
            actions,
            children,
          };
        })
        .filter((mod) => mod.actions.length > 0 || mod.children.length > 0);
    };

    // 6. Build final result by roles
    const result = roles.map((role) => ({
      role_id: role.role_id,
      role_name: role.role_name,
      description: role.description,
      role_type: role.role_type,
      is_active: role.is_active,
      created_by: role.created_by,
      created_at: role.createdAt,
      updated_at: role.updatedAt,
      modules: buildHierarchy(role.role_id),
    }));

    return res.status(200).json({
      success: true,
      message:
        "All non-trainee roles with module hierarchy, actions, and permission status",
      data: result,
    });
  } catch (error) {
    console.error("❌ FULL ERROR:", error);
    return res.status(500).json({
      success: false,
      message: error.message || "Internal Server Error",
      stack: error.stack,
    });
  }
};

const createGetFullModuleHierarchyWithActions = async (req, res, next) => {
  try {
    const db_name = req.user?.db_name;
    if (!db_name) throw new Error("Missing db_name in request.");

    const { role_name, description, created_by, permissions } = req.body;

    if (
      !role_name ||
      !description ||
      !created_by ||
      !Array.isArray(permissions)
    ) {
      return res.status(400).json({
        success: false,
        message:
          "role_name, description, created_by, and permissions[] are required",
      });
    }

    // Step 1: Insert into roles table
    const [roleResult] = await mysqlServerConnection.query(
      `INSERT INTO ${db_name}.roles (
        name, description, created_by, is_deleted, is_active, role_type, createdAt, updatedAt
      ) VALUES (?, ?, ?, 0, 1, 'admin', NOW(), NOW())`,
      [role_name, description, created_by]
    );

    const newRoleId = roleResult.insertId;

    // Step 2: Prepare bulk insert for hierarchical_role_permissions
    const permissionValues = permissions
      .map(
        ({ module_id, action_id, is_granted }) =>
          `(${newRoleId}, ${module_id}, ${action_id}, ${
            is_granted || 0
          }, NOW())`
      )
      .join(",");

    if (!permissionValues) {
      return res
        .status(400)
        .json({ success: false, message: "Empty permissions array" });
    }

    const insertQuery = `
      INSERT INTO ${db_name}.hierarchical_role_permissions 
      (role_id, module_id, action_id, is_granted, created_at) 
      VALUES ${permissionValues}
    `;

    await mysqlServerConnection.query(insertQuery);

    return res.status(201).json({
      success: true,
      message: "Role and permissions saved successfully",
      new_role_id: newRoleId,
      total_permissions: permissions.length,
    });
  } catch (error) {
    console.error(
      "❌ Error in createGetFullModuleHierarchyWithActions:",
      error
    );
    return res.status(500).json({
      success: false,
      message: error.message,
      stack: error.stack,
    });
  }
};

const getPermissionsByRoleId = async (req, res, next) => {
  try {
    const db_name = req.user?.db_name;
    if (!db_name) {
      throw new Error(
        "❌ db_name is missing from request. Please check login middleware."
      );
    }

    console.log("✅ Using DB:", db_name);

    const roleId = 1; // Only fetch data for role_id = 1

    // 1. Fetch all modules
    const [allModules] = await mysqlServerConnection.query(`
      SELECT 
        id AS module_id,
        parent_id,
        name,
        slug,
        icon,
        description,
        \`order\`,
        is_active
      FROM ${db_name}.permission_modules_with_new
    `);

    // 2. Fetch all actions (but ignore is_granted — we override it to 0)
    const [allActions] = await mysqlServerConnection.query(
      `
      SELECT 
        hrp.id AS permission_id,
        hrp.role_id,
        hrp.module_id,
        hrp.action_id,
        a.name AS action_name
      FROM ${db_name}.hierarchical_role_permissions hrp
      INNER JOIN ${db_name}.actions a ON a.id = hrp.action_id
      WHERE hrp.role_id = ? AND a.is_deleted = 0
    `,
      [roleId]
    );

    // 3. Fetch role_id = 1 details
    const [roles] = await mysqlServerConnection.query(
      `
      SELECT 
        id AS role_id,
        name AS role_name,
        description,
        role_type,
        is_active,
        created_by,
        createdAt,
        updatedAt
      FROM ${db_name}.roles
      WHERE id = ? AND is_deleted = 0
    `,
      [roleId]
    );

    if (roles.length === 0) {
      return res.status(404).json({
        success: false,
        message: `Role ID ${roleId} not found`,
      });
    }

    // 4. Map actions per module (override is_granted = 0)
    const mapActions = (moduleId) => {
      return allActions
        .filter((a) => a.module_id === moduleId)
        .map((a) => ({
          permission_id: a.permission_id,
          action_id: a.action_id,
          action_name: a.action_name,
          is_granted: 0, // ❗ hardcoded to 0
        }));
    };

    // 5. Recursive hierarchy builder
    const buildHierarchy = (parentId = null) => {
      return allModules
        .filter((mod) => mod.parent_id === parentId)
        .map((mod) => {
          const actions = mapActions(mod.module_id);
          const children = buildHierarchy(mod.module_id);
          return {
            module_id: mod.module_id,
            module_name: mod.name,
            slug: mod.slug,
            icon: mod.icon,
            description: mod.description,
            order: mod.order,
            is_active: mod.is_active,
            actions,
            children,
          };
        })
        .filter((mod) => mod.actions.length > 0 || mod.children.length > 0);
    };

    // 6. Build response for role_id = 1
    const result = {
      role_id: roles[0].role_id,
      role_name: roles[0].role_name,
      description: roles[0].description,
      role_type: roles[0].role_type,
      is_active: roles[0].is_active,
      created_by: roles[0].created_by,
      created_at: roles[0].createdAt,
      updated_at: roles[0].updatedAt,
      modules: buildHierarchy(),
    };

    return res.status(200).json({
      success: true,
      message:
        "Module hierarchy with actions for role_id = 1 (all is_granted set to 0)",
      data: result,
    });
  } catch (error) {
    console.error("❌ FULL ERROR:", error);
    return res.status(500).json({
      success: false,
      message: error.message || "Internal Server Error",
      stack: error.stack,
    });
  }
};

const getFullModuleHierarchyWithActions = async (req, res) => {
  try {
    const db_name = req.user.db_name;

    // Step 1: Retrieve roles from the roles table
    const [roles] = await mysqlServerConnection.query(`
      SELECT id, name, description
      FROM ${db_name}.roles
      WHERE is_deleted = 0
    `);

    // Filter out roles with the name 'trainee'
    const filteredRoles = roles.filter(role => role.name !== 'trainee');

    if (filteredRoles.length === 0) {
      return res.status(404).json({
        success: false,
        message: "No roles found.",
      });
    }

    // Step 2: Retrieve parent permission modules from ${maindb}
    const [parentModules] = await mysqlServerConnection.query(`
      SELECT id, name, description, slug, icon
      FROM ${maindb}.permission_modules
      WHERE parent_id IS NULL
      ORDER BY \`order\`
    `);

    // Step 3: Retrieve all permission modules (including child modules and deeper levels)
    const [allModules] = await mysqlServerConnection.query(`
      SELECT id, name, description, slug, icon, parent_id
      FROM ${maindb}.permission_modules
      ORDER BY \`order\`
    `);

    // Step 4: Retrieve action mappings from permission_modules_and_actions_mapping
    const [actionMappings] = await mysqlServerConnection.query(`
      SELECT id, name, slug, module_id, action_id, description
      FROM ${maindb}.permission_modules_and_actions_mapping
      WHERE is_delete = 0
    `);

    // Step 5: Retrieve action details from permission_actions table
    const [actions] = await mysqlServerConnection.query(`
      SELECT id, name
      FROM ${maindb}.permission_actions
      WHERE is_deleted = 0
    `);

    // Step 6: Helper function to recursively fetch child modules and their actions
    const getModuleWithActions = (module, rolePermissions) => {
      const children = allModules.filter(child => child.parent_id === module.id);

      const actionsForModule = actionMappings
        .filter(mapping => mapping.module_id === module.id)
        .map(mapping => {
          const action = actions.find(action => action.id === mapping.action_id);
          const permission = rolePermissions.find(rp => rp.permission_id === mapping.id);
          return {
            id: mapping.id,
            name: mapping.name,
            action_name: action ? action.name : null, // ✅ Added action_name
            description: mapping.description,
            slug: mapping.slug,
            is_granted: permission ? permission.is_granted : 0
          };
        });

      const childrenWithActions = children.length > 0 ? children.map(child => getModuleWithActions(child, rolePermissions)) : [];

      return {
        ...module,
        actions: actionsForModule,
        children: childrenWithActions
      };
    };

    // Step 7: Organize parent modules with their respective children and actions, and include is_granted status
    const parentModulesWithChildren = parentModules.map(parentModule =>
      getModuleWithActions(parentModule, [])
    );

    // Step 8: For each role, fetch permissions individually
    const rolesWithPermissions = await Promise.all(
      filteredRoles.map(async (role) => {
        // Step 9: Retrieve role permissions for the specific role
        const [rolePermissions] = await mysqlServerConnection.query(`
          SELECT role_id, permission_id, is_granted
          FROM ${db_name}.role_permissions
          WHERE role_id = ${role.id}
        `);

        return {
          ...role,
          permissions: parentModulesWithChildren.map(parentModule =>
            getModuleWithActions(parentModule, rolePermissions)
          )
        };
      })
    );

    return res.status(200).json({
      success: true,
      message: "Roles and permissions retrieved successfully",
      data: rolesWithPermissions,
    });
  } catch (error) {
    console.error("Error fetching roles and permissions:", error);
    return res.status(500).json({
      success: false,
      message: "Failed to retrieve roles and permissions.",
    });
  }
};




const updateRolePermission = async (req, res) => {
  try {
    const { role_id, name, description, permissionsToUpdate } = req.body; // Extract role details and permissions to update

    // Validate input for role update
    if (!role_id || !name || !description) {
      return res.status(400).json({
        success: false,
        message: "Missing required fields for role update: role_id, name, or description.",
      });
    }

    // Step 1: Update the role details (name and description)
    const [roleUpdateResult] = await mysqlServerConnection.query(`
      UPDATE ${req.user.db_name}.roles
      SET name = ?, description = ?, updatedAt = NOW()
      WHERE id = ? AND is_deleted = 0
    `, [name, description, role_id]);

    // Check if the role was updated
    if (roleUpdateResult.affectedRows === 0) {
      return res.status(404).json({
        success: false,
        message: "Role not found or could not be updated.",
      });
    }

    // Step 2: Check if permissionsToUpdate exists and is an array
    if (permissionsToUpdate && Array.isArray(permissionsToUpdate) && permissionsToUpdate.length > 0) {
      // Validate input for permissions update
      for (const permission of permissionsToUpdate) {
        const { permission_id, is_granted } = permission;

        // Validate each permission object
        if (!permission_id || is_granted === undefined) {
          return res.status(400).json({
            success: false,
            message: "Each permission update must contain permission_id and is_granted.",
          });
        }

        // Update the permission (is_granted) in the role_permissions table
        const [permissionUpdateResult] = await mysqlServerConnection.query(`
          UPDATE ${req.user.db_name}.role_permissions
          SET is_granted = ?, updatedAt = NOW()
          WHERE role_id = ? AND permission_id = ?
        `, [is_granted, role_id, permission_id]);

        // Check if the permission update was successful
        if (permissionUpdateResult.affectedRows === 0) {
          console.warn(`No permission found for role_id: ${role_id} and permission_id: ${permission_id}`);
        }
      }
    }

    // Step 3: Send success response
    return res.status(200).json({
      success: true,
      message: "Role and permissions updated successfully.",
    });
  } catch (error) {
    console.error("Error updating role and permissions:", error);
    return res.status(500).json({
      success: false,
      message: "Failed to update role and permissions.",
    });
  }
};


const insertRoleAndPermissions = async (req, res) => {
  try {
    const { name, description } = req.body; // Extract role name and description

    // Validate input for role
    if (!name || !description) {
      return res.status(400).json({
        success: false,
        message: "Missing required fields: name or description.",
      });
    }

    // Step 1: Check if the role name already exists
    const [existingRole] = await mysqlServerConnection.query(`
      SELECT id FROM ${req.user.db_name}.roles WHERE name = ? AND is_deleted = 0
    `, [name]);

    if (existingRole.length > 0) {
      return res.status(400).json({
        success: false,
        message: `Role with name '${name}' already exists.`,
      });
    }

    // Step 2: Insert the new role into the roles table
    const [roleInsertResult] = await mysqlServerConnection.query(`
      INSERT INTO ${req.user.db_name}.roles (name, description, createdAt, updatedAt, is_deleted, is_active, role_type)
      VALUES (?, ?, NOW(), NOW(), 0, 1, 'admin')
    `, [name, description]); // Default values for is_deleted, is_active, and role_type

    // Step 3: Check if role was inserted successfully
    if (roleInsertResult.affectedRows === 0) {
      return res.status(500).json({
        success: false,
        message: "Failed to insert new role.",
      });
    }

    // Get the new role's id
    const newRoleId = roleInsertResult.insertId;

    // Step 4: Fetch all permission IDs from permission_modules_and_actions_mapping table
    const [permissionMappings] = await mysqlServerConnection.query(`
      SELECT id 
      FROM ${maindb}.permission_modules_and_actions_mapping
      WHERE is_delete = 0
    `);

    // If no permissions found, return an error
    if (permissionMappings.length === 0) {
      return res.status(404).json({
        success: false,
        message: "No permissions found in the permission_modules_and_actions_mapping table.",
      });
    }

    // Step 5: Insert new records into role_permissions table
    const rolePermissionsValues = permissionMappings.map(mapping => [
      newRoleId,  // role_id
      mapping.id, // permission_id
      0,          // is_granted (set to 0)
      new Date(), // createdAt
      new Date()  // updatedAt
    ]);

    // Insert all permissions for the new role
    await mysqlServerConnection.query(`
      INSERT INTO ${req.user.db_name}.role_permissions (role_id, permission_id, is_granted, createdAt, updatedAt)
      VALUES ?
    `, [rolePermissionsValues]);

    // Step 6: Send success response
    return res.status(200).json({
      success: true,
      message: "Role and permissions inserted successfully.",
      data: {
        role_id: newRoleId,
        name,
        description
      }
    });
  } catch (error) {
    console.error("Error inserting role and permissions:", error);
    return res.status(500).json({
      success: false,
      message: "Failed to insert role and permissions.",
    });
  }
};

const deleteRoleAndPermissions = async (req, res) => {
  console.log("deleteRoleAndPermissions", req.body);
  try {
    const { role_id } = req.body;
    const db_name = req.user.db_name; // Get the database name from the authenticated user

    // Step 1: Check if the role exists in the roles table
    const [role] = await mysqlServerConnection.query(
      `SELECT * FROM ${db_name}.roles WHERE id = ? AND is_deleted = 0`, [role_id]
    );

    if (role.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Role not found or already deleted.',
      });
    }

    // Step 2: Check if any users are assigned to the role (optional, based on requirement)
    const [userRoles] = await mysqlServerConnection.query(
      `SELECT * FROM ${db_name}.user_roles WHERE role_id = ?`, [role_id]
    );

    if (userRoles.length > 0) {
      return res.status(400).json({
        success: false,
        message: 'Role cannot be deleted as users are still assigned to it.',
      });
    }

    // Step 3: Delete all permissions for the role from role_permissions table
    await mysqlServerConnection.query(
      `DELETE FROM ${db_name}.role_permissions WHERE role_id = ?`, [role_id]
    );

    // Step 4: Soft delete the role (mark it as deleted) in the roles table
    await mysqlServerConnection.query(
      `DELETE FROM ${db_name}.roles WHERE id = ?`, [role_id]
    );

    return res.status(200).json({
      success: true,
      message: 'Role and its permissions are deleted successfully.',
    });

  } catch (error) {
    console.error("Error deleting role and permissions:", error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error.',
    });
  }
};










module.exports = {
  getRolesAndPermissions,
  activateRole,
  deactivateRole,
  deleteRole,
  createRoleWithPermissions,
  getAllPermissions,
  getRolePermissionsById,
  updateRoleById,
  createGetFullModuleHierarchyWithActions,
  getPermissionsByRoleId,

  getFullModuleHierarchyWithActions, 
  updateRolePermission,
  insertRoleAndPermissions,
  deleteRoleAndPermissions
};
