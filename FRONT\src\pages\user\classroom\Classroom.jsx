import React, { useState, useEffect } from 'react';
import { Icon } from '@iconify/react';
import { useParams, useSearchParams } from 'react-router-dom';
import './AllClassroom.css';
import LiveClass from './LiveClass';
import RecordedVideo from './RecordedVideo';
import Assignments from './Assignments';
import Assessments from './Assessments';
import Cummunity from './Cummunity';
import { getTraineeClassroomsDashboardData } from '../../../services/userService';
import { decodeData } from '../../../utils/encodeAndEncode';

function UserClassroom() {
  const { encodedClassroomID } = useParams();
  const [searchParams] = useSearchParams();
  const classroom_id = decodeData(decodeURIComponent(encodedClassroomID));
  const urlActiveTab = searchParams.get('active-tab');
  const normalizedUrlTab = urlActiveTab === 'assessments' ? 'assessment' : urlActiveTab;
  const [activeTab, setActiveTab] = useState(normalizedUrlTab || 'assignments');
  const [classroomData, setClassroomData] = useState(null);
  const [classroomName, setClassroomName] = useState(null);
  const [tabVisibility, setTabVisibility] = useState([]);
  const [loading, setLoading] = useState(true);

  // Check if all tabs are disabled
  const areAllTabsDisabled = tabVisibility.length > 0 &&
    tabVisibility.every(tab => tab.is_visible === "false");

  useEffect(() => {
    const fetchClassroomData = async () => {
      try {
        const response = await getTraineeClassroomsDashboardData({ class_id: classroom_id });
        console.log("Classroom Data ------------", response);

        // Handle different response structures
        let classroomData = null;
        if (response.data.status === 200 && response.data.data) {
          classroomData = response.data.data;
        } else if (response.data && !response.data.status) {
          // Direct data response
          classroomData = response.data;
        }

        if (classroomData) {
          setClassroomData(classroomData);
          setClassroomName(classroomData.cls_name);

          // Set tab visibility
          if (classroomData.is_visibility) {
            setTabVisibility(classroomData.is_visibility);

            // Only set default tab if no URL parameter is present
            if (!urlActiveTab) {
              // Set the first visible tab as active
              const firstVisibleTab = classroomData.is_visibility.find(tab => tab.is_visible === "true");
              if (firstVisibleTab) {
                // Map tab names to component tab names
                const tabMapping = {
                  'assignment': 'assignments',
                  'assessment': 'assessment',
                  'liveclass': 'live',
                  'recorded': 'recorded',
                  'community': 'community'
                };
                const mappedTabName = tabMapping[firstVisibleTab.tab_name] || 'assignments';
                setActiveTab(mappedTabName);
              }
            }
          }

          // Set group data
          if (classroomData.group) {
            // setGroupData({ // This line was removed as per the new_code, as groupData state was removed
            //   group_id: classroomData.group.group_id,
            //   group_name: classroomData.group.group_name
            // });
          }
        }

        setLoading(false);
      } catch (error) {
        console.error('Error fetching classroom data:', error);
        setLoading(false);
      }
    };

    fetchClassroomData();
  }, [encodedClassroomID, urlActiveTab]);

  const handleTabChange = (tab) => {
    setActiveTab(tab);
  };

  // Helper function to check if a tab is visible
  const isTabVisible = (tabName) => {
    // If tabVisibility is empty (not loaded yet), show all tabs by default
    if (tabVisibility.length === 0) {
      return true;
    }

    const tab = tabVisibility.find(t => t.tab_name === tabName);
    return tab ? tab.is_visible === "true" : false;
  };

  if (loading) {
    return (
      <div className="text-center p-4">Loading...</div>
    );
  }

  return (
    <div className="row">
      <div className="col-12">
        <div className="card-header bg-white border-bottom-0 d-flex justify-content-between align-items-center py-3">
          <h4 className="mb-0">{classroomData?.cls_name || "Classroom"}</h4>
        </div>

        {areAllTabsDisabled ? (
          <div className="card">
            <div className="card-body text-center py-5">
              <div className="mb-4">
                <Icon icon="fluent:presence-blocked-24-regular" className="text-muted" width="64" height="64" />
              </div>
              <h5 className="text-muted mb-2">No Active Tabs Available</h5>
              <p className="text-muted mb-0">
                The classroom features are currently disabled by the administrator.
                Please check back later or contact your instructor for more information.
              </p>
            </div>
          </div>
        ) : (
          <div className="card-body p-0">
            <div className="tab-header border-bottom">
              <ul className="nav nav-tabs">
                {/* Assignments Tab */}
                {isTabVisible('assignment') && (
                  <li className="nav-item">
                    <button
                      className={`nav-link d-flex align-items-center ${activeTab === 'assignments' ? 'active' : ''}`}
                      onClick={() => handleTabChange('assignments')}
                    >
                      <Icon icon="mdi:file-document" className="me-2" width="20" height="20" />
                      Assignments
                    </button>
                  </li>
                )}

                {/* Assessment Tab */}
                {isTabVisible('assessment') && (
                  <li className="nav-item">
                    <button
                      className={`nav-link d-flex align-items-center ${activeTab === 'assessment' ? 'active' : ''}`}
                      onClick={() => handleTabChange('assessment')}
                    >
                      <Icon icon="mdi:clipboard-text" className="me-2" width="20" height="20" />
                      Assessment
                    </button>
                  </li>
                )}

                {/* Live Classes Tab */}
                {isTabVisible('liveclass') && (
                  <li className="nav-item">
                    <button
                      className={`nav-link d-flex align-items-center ${activeTab === 'live' ? 'active' : ''}`}
                      onClick={() => handleTabChange('live')}
                    >
                      <Icon icon="mdi:video" className="me-2" width="20" height="20" />
                      Live Classes
                    </button>
                  </li>
                )}


                {/* Recorded Videos Tab */}
                {isTabVisible('recorded') && (
                  <li className="nav-item">
                    <button
                      className={`nav-link d-flex align-items-center ${activeTab === 'recorded' ? 'active' : ''}`}
                      onClick={() => handleTabChange('recorded')}
                    >
                      <Icon icon="mdi:play-circle" className="me-2" width="20" height="20" />
                      Resources & Videos
                    </button>
                  </li>
                )}

                {/* Community Tab */}
                {isTabVisible('community') && (
                  <li className="nav-item">
                    <button
                      className={`nav-link d-flex align-items-center ${activeTab === 'community' ? 'active' : ''}`}
                      onClick={() => handleTabChange('community')}
                    >
                      <Icon icon="mdi:chat" className="me-2" width="20" height="20" />
                      Community
                    </button>
                  </li>
                )}

              </ul>
            </div>

            {/* Tab Content */}
            {activeTab === 'assignments' && (
              <div className="assignments">
                <Assignments classroom_id={classroom_id} />
              </div>
            )}

            {activeTab === 'assessment' && (
              <div className="assessment">
                <Assessments classroom_id={classroom_id} />
              </div>
            )}

            {activeTab === 'live' && (
              <div className="live-class">
                <LiveClass classroom_id={classroom_id} />
              </div>
            )}

            {activeTab === 'recorded' && (
              <div className="recorded">
                <RecordedVideo classroom_id={classroom_id} />
              </div>
            )}

            {activeTab === 'community' && (
              <div className="community">
                <Cummunity classroom_id={classroom_id} group_id={classroomData?.group?.group_id} group_name={classroomData?.group?.group_name} />
              </div>
            )}


          </div>
        )}
      </div>
    </div>
  );
}

export default UserClassroom;
