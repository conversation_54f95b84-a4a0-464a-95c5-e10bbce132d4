const { mysqlServerConnection } = require("../../../db/db");
const maindb = process.env.MAIN_DB;

/**
 * Get all permission modules in hierarchical structure
 */
const getHierarchicalPermissions = async (req, res) => {
  try {
    // Get all permission modules - try organization DB first, then auth DB
    let modules, actions;
    const db_name = req.user?.db_name || "lms_tpi"; // Use user's org DB or default

    try {
      // Try to get from organization database first
      const [orgModules] = await mysqlServerConnection.query(`
                SELECT
                    pm.id,
                    pm.name,
                    pm.slug,
                    pm.parent_id,
                    pm.order,
                    pm.is_active,
                    pm.icon,
                    pm.description
                FROM ${db_name}.permission_modules pm
                WHERE pm.is_active = 1
                ORDER BY pm.parent_id, pm.order
            `);
      modules = orgModules;
    } catch (error) {
      // Fallback to auth database
      const [authModules] = await mysqlServerConnection.query(`
                SELECT
                    pm.id,
                    pm.name,
                    pm.slug,
                    pm.parent_id,
                    pm.order,
                    pm.is_active,
                    pm.icon,
                    pm.description
                FROM ${maindb}.permission_modules pm
                WHERE pm.is_active = 1
                ORDER BY pm.parent_id, pm.order
            `);
      modules = authModules;
    }

    try {
      // Try to get actions from organization database first
      const [orgActions] = await mysqlServerConnection.query(`
                SELECT id, name FROM ${db_name}.actions ORDER BY name
            `);
      actions = orgActions;
    } catch (error) {
      // Fallback to auth database
      const [authActions] = await mysqlServerConnection.query(`
                SELECT id, name FROM ${maindb}.actions ORDER BY name
            `);
      actions = authActions;
    }

    // Build hierarchical structure
    const hierarchicalModules = buildHierarchy(modules);

    return res.status(200).json({
      success: true,
      data: {
        modules: hierarchicalModules,
        actions: actions,
      },
    });
  } catch (error) {
    console.error("Error fetching hierarchical permissions:", error);
    return res.status(500).json({
      success: false,
      error_msg: "Failed to retrieve hierarchical permissions.",
    });
  }
};

/**
 * Get role permissions in hierarchical format
 */
const getRoleHierarchicalPermissions = async (req, res) => {
  try {
    const { role_id } = req.params;
    const db_name = req.user.db_name;

    // Get role details
    const [roleDetails] = await mysqlServerConnection.query(
      `
            SELECT id, name, description, is_active 
            FROM ${db_name}.roles 
            WHERE id = ?
        `,
      [role_id]
    );

    if (roleDetails.length === 0) {
      return res.status(404).json({
        success: false,
        error_msg: "Role not found",
      });
    }

    // Get all permission modules - try organization DB first, then auth DB
    let modules, actions;

    try {
      // Try to get from organization database first
      const [orgModules] = await mysqlServerConnection.query(`
            SELECT
                pm.id,
                pm.name,
                pm.slug,
                pm.parent_id,
                pm.order,
                pm.is_active,
                pm.icon,
                pm.description
            FROM ${db_name}.permission_modules pm
            WHERE pm.is_active = 1
            ORDER BY pm.parent_id, pm.order
        `);
      modules = orgModules;
    } catch (error) {
      // Fallback to auth database
      const [authModules] = await mysqlServerConnection.query(`
            SELECT
                pm.id,
                pm.name,
                pm.slug,
                pm.parent_id,
                pm.order,
                pm.is_active,
                pm.icon,
                pm.description
            FROM ${maindb}.permission_modules pm
            WHERE pm.is_active = 1
            ORDER BY pm.parent_id, pm.order
        `);
      modules = authModules;
    }

    try {
      // Try to get actions from organization database first
      const [orgActions] = await mysqlServerConnection.query(`
            SELECT id, name FROM ${db_name}.actions ORDER BY name
        `);
      actions = orgActions;
    } catch (error) {
      // Fallback to auth database
      const [authActions] = await mysqlServerConnection.query(`
            SELECT id, name FROM ${maindb}.actions ORDER BY name
        `);
      actions = authActions;
    }

    // Get role's hierarchical permissions
    const [rolePermissions] = await mysqlServerConnection.query(
      `
            SELECT 
                hrp.module_id,
                hrp.action_id
            FROM ${db_name}.hierarchical_role_permissions hrp
            WHERE hrp.role_id = ?
        `,
      [role_id]
    );

    // Build hierarchical structure with permissions
    const hierarchicalModules = buildHierarchy(modules, rolePermissions);

    return res.status(200).json({
      success: true,
      data: {
        role: {
          id: roleDetails[0].id,
          name: roleDetails[0].name,
          description: roleDetails[0].description,
          is_active: roleDetails[0].is_active === 1,
        },
        modules: hierarchicalModules,
        actions: actions,
      },
    });
  } catch (error) {
    console.error("Error fetching role hierarchical permissions:", error);
    return res.status(500).json({
      success: false,
      error_msg: "Failed to retrieve role hierarchical permissions.",
    });
  }
};

/**
 * Create role with hierarchical permissions
 */
const createRoleWithHierarchicalPermissions = async (req, res) => {
  try {
    const {
      role_name,
      permissions, // Array of {module_id, action_ids[]}
      description,
      status,
    } = req.body;

    const db_name = req.user.db_name;

    // Validate input
    if (!role_name || !Array.isArray(permissions) || permissions.length === 0) {
      return res.status(400).json({
        success: false,
        error_msg: "Invalid input. Provide role_name and permissions array.",
      });
    }

    // Start a transaction to ensure data consistency
    const connection = await mysqlServerConnection.getConnection();

    try {
      // Begin transaction
      await connection.beginTransaction();

      // Insert new role
      const [roleResult] = await connection.query(
        `INSERT INTO ${db_name}.roles 
                (name, description, is_active, created_by, role_type) 
                VALUES (?, ?, ?, ?, ?)`,
        [role_name, description || "", status, req.user.userId, "admin"]
      );

      const role_id = roleResult.insertId;

      // Insert hierarchical role permissions
      const permissionQueries = [];
      permissions.forEach((permission) => {
        permission.action_ids.forEach((action_id) => {
          permissionQueries.push(
            connection.query(
              `INSERT INTO ${db_name}.hierarchical_role_permissions 
                            (role_id, module_id, action_id) 
                            VALUES (?, ?, ?)`,
              [role_id, permission.module_id, action_id]
            )
          );
        });
      });

      // Execute all permission insertions
      await Promise.all(permissionQueries);

      // Commit transaction
      await connection.commit();

      return res.status(201).json({
        success: true,
        message: "Role created successfully with hierarchical permissions.",
        data: { role_id },
      });
    } catch (error) {
      // Rollback transaction on error
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }
  } catch (error) {
    console.error(
      "Unexpected error in createRoleWithHierarchicalPermissions:",
      error
    );
    return res.status(500).json({
      success: false,
      error_msg: "Internal server error.",
    });
  }
};

/**
 * Update role with hierarchical permissions
 */
const updateRoleWithHierarchicalPermissions = async (req, res) => {
  try {
    const { role_id } = req.params;
    const {
      name,
      permissions, // Array of {module_id, action_ids[]}
      description,
      is_active,
    } = req.body;

    const db_name = req.user.db_name;

    // Start a transaction to ensure data consistency
    const connection = await mysqlServerConnection.getConnection();

    try {
      // Begin transaction
      await connection.beginTransaction();

      // Update role details
      await connection.query(
        `UPDATE ${db_name}.roles 
                SET name = ?, description = ?, is_active = ?
                WHERE id = ?`,
        [name, description || "", is_active, role_id]
      );

      // Delete existing hierarchical permissions
      await connection.query(
        `DELETE FROM ${db_name}.hierarchical_role_permissions 
                WHERE role_id = ?`,
        [role_id]
      );

      // Insert new hierarchical role permissions
      if (permissions && permissions.length > 0) {
        const permissionQueries = [];
        permissions.forEach((permission) => {
          permission.action_ids.forEach((action_id) => {
            permissionQueries.push(
              connection.query(
                `INSERT INTO ${db_name}.hierarchical_role_permissions 
                                (role_id, module_id, action_id) 
                                VALUES (?, ?, ?)`,
                [role_id, permission.module_id, action_id]
              )
            );
          });
        });

        // Execute all permission insertions
        await Promise.all(permissionQueries);
      }

      // Commit transaction
      await connection.commit();

      return res.status(200).json({
        success: true,
        message: "Role updated successfully with hierarchical permissions.",
      });
    } catch (error) {
      // Rollback transaction on error
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }
  } catch (error) {
    console.error(
      "Unexpected error in updateRoleWithHierarchicalPermissions:",
      error
    );
    return res.status(500).json({
      success: false,
      error_msg: "Internal server error.",
    });
  }
};

/**
 * Helper function to build hierarchical structure
 */
function buildHierarchy(modules, rolePermissions = []) {
  const moduleMap = {};
  const rootModules = [];

  // Create a map of modules and initialize children array
  modules.forEach((module) => {
    moduleMap[module.id] = {
      ...module,
      children: [],
      permissions: {},
    };
  });

  // Add permission information if provided
  if (rolePermissions.length > 0) {
    rolePermissions.forEach((perm) => {
      if (moduleMap[perm.module_id]) {
        if (!moduleMap[perm.module_id].permissions[perm.action_id]) {
          moduleMap[perm.module_id].permissions[perm.action_id] = true;
        }
      }
    });
  }

  // Build the hierarchy
  modules.forEach((module) => {
    if (module.parent_id === null) {
      rootModules.push(moduleMap[module.id]);
    } else if (moduleMap[module.parent_id]) {
      moduleMap[module.parent_id].children.push(moduleMap[module.id]);
    }
  });

  return rootModules;
}

/**
 * Create a new permission module
 */
const createPermissionModule = async (req, res) => {
  try {
    const { name, slug, description, icon, parent_id, is_active } = req.body;
    const db_name = req.user?.db_name || "lms_tpi";

    // Validate required fields
    if (!name || !slug) {
      return res.status(400).json({
        success: false,
        error_msg: "Name and slug are required fields",
      });
    }

    // Check if slug already exists
    const [existingModule] = await mysqlServerConnection.query(
      `SELECT id FROM ${db_name}.permission_modules WHERE slug = ?`,
      [slug]
    );

    if (existingModule.length > 0) {
      return res.status(400).json({
        success: false,
        error_msg: `Module with slug '${slug}' already exists`,
      });
    }

    // Get the next order number for the parent
    let order = 1;
    const [maxOrder] = await mysqlServerConnection.query(
      `SELECT MAX(\`order\`) as max_order FROM ${db_name}.permission_modules WHERE parent_id ${
        parent_id ? "= ?" : "IS NULL"
      }`,
      parent_id ? [parent_id] : []
    );

    if (maxOrder[0].max_order) {
      order = maxOrder[0].max_order + 1;
    }

    // Insert the new module
    const [result] = await mysqlServerConnection.query(
      `INSERT INTO ${db_name}.permission_modules
       (name, slug, description, icon, parent_id, \`order\`, is_active)
       VALUES (?, ?, ?, ?, ?, ?, ?)`,
      [
        name,
        slug,
        description || null,
        icon || null,
        parent_id || null,
        order,
        is_active ? 1 : 0,
      ]
    );

    return res.status(201).json({
      success: true,
      data: {
        id: result.insertId,
        name,
        slug,
        description,
        icon,
        parent_id,
        order,
        is_active: is_active ? 1 : 0,
      },
      message: "Permission module created successfully",
    });
  } catch (error) {
    console.error("Error creating permission module:", error);
    return res.status(500).json({
      success: false,
      error_msg: "Internal server error while creating permission module",
      error: error.message,
    });
  }
};

/**
 * Get module actions (which actions are available for a specific module)
 */
const getModuleActions = async (req, res) => {
  try {
    const { module_id } = req.params;
    const db_name = req.user?.db_name || "lms_tpi";

    if (!module_id) {
      return res.status(400).json({
        success: false,
        error_msg: "Module ID is required",
      });
    }

    // Get all actions and check which ones are attached to this module
    const [allActions] = await mysqlServerConnection.query(
      `SELECT id, name, description FROM ${db_name}.actions ORDER BY name`
    );

    // Get actions currently attached to this module (from any role)
    const [attachedActions] = await mysqlServerConnection.query(
      `SELECT DISTINCT action_id FROM ${db_name}.hierarchical_role_permissions
       WHERE module_id = ?`,
      [module_id]
    );

    const attachedActionIds = attachedActions.map((a) => a.action_id);

    // Mark which actions are attached
    const actionsWithStatus = allActions.map((action) => ({
      ...action,
      isAttached: attachedActionIds.includes(action.id),
    }));

    return res.status(200).json({
      success: true,
      data: {
        module_id: parseInt(module_id),
        actions: actionsWithStatus,
      },
      message: "Module actions retrieved successfully",
    });
  } catch (error) {
    console.error("Error getting module actions:", error);
    return res.status(500).json({
      success: false,
      error_msg: "Internal server error while getting module actions",
      error: error.message,
    });
  }
};

/**
 * Save module actions (create a template of available actions for a module)
 * This creates a "module template" that defines which actions are relevant for this module
 */
const saveModuleActions = async (req, res) => {
  try {
    const { module_id, action_ids } = req.body;
    const db_name = req.user?.db_name || "lms_tpi";

    if (!module_id || !Array.isArray(action_ids)) {
      return res.status(400).json({
        success: false,
        error_msg: "Module ID and action_ids array are required",
      });
    }

    // For now, we'll create a simple mapping table to store which actions are available for each module
    // First, let's create the table if it doesn't exist
    await mysqlServerConnection.query(`
      CREATE TABLE IF NOT EXISTS ${db_name}.module_available_actions (
        module_id INT NOT NULL,
        action_id INT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY(module_id, action_id),
        FOREIGN KEY(module_id) REFERENCES ${db_name}.permission_modules(id) ON DELETE CASCADE,
        FOREIGN KEY(action_id) REFERENCES ${db_name}.actions(id) ON DELETE CASCADE
      )
    `);

    // Remove existing actions for this module
    await mysqlServerConnection.query(
      `DELETE FROM ${db_name}.module_available_actions WHERE module_id = ?`,
      [module_id]
    );

    // Insert new actions
    if (action_ids.length > 0) {
      const values = action_ids.map((action_id) => [module_id, action_id]);
      await mysqlServerConnection.query(
        `INSERT INTO ${db_name}.module_available_actions (module_id, action_id) VALUES ?`,
        [values]
      );
    }

    return res.status(200).json({
      success: true,
      data: {
        module_id: parseInt(module_id),
        action_ids: action_ids,
        count: action_ids.length,
      },
      message: `Successfully updated ${action_ids.length} actions for module`,
    });
  } catch (error) {
    console.error("Error saving module actions:", error);
    return res.status(500).json({
      success: false,
      error_msg: "Internal server error while saving module actions",
      error: error.message,
    });
  }
};

module.exports = {
  getHierarchicalPermissions,
  getRoleHierarchicalPermissions,
  createRoleWithHierarchicalPermissions,
  updateRoleWithHierarchicalPermissions,
  createPermissionModule,
  getModuleActions,
  saveModuleActions,
};
