import React, { useEffect, useState } from 'react'

import { Icon } from '@iconify/react';
import { coursePerformanceDetails } from '../../../services/adminService';
import { decodeData } from '../../../utils/encodeAndEncode';
import defaultProfile from '../../../assets/images/profile/default-profile.png'
import { useParams } from 'react-router-dom';

function CourseAnalyticsTraineeProgress() {
  const { courseId } = useParams();
  const decodedCourseId = decodeData(courseId);

  const [traineesData, setTraineesData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [search, setSearch] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    totalPages: 0,
    totalCount: 0
  });

  // Fetch trainee progress data
  useEffect(() => {
    fetchTraineeProgress();
  }, [decodedCourseId, search, statusFilter, pagination.page]);

  const fetchTraineeProgress = async () => {
    try {
      setLoading(true);
      const response = await coursePerformanceDetails({
        course_id: decodedCourseId,
        search: search,
        page: pagination.page,
        limit: pagination.limit,
        status: statusFilter
      });

      console.log('Trainee Progress Response:', response);

      if (response.success && response.data) {
        setTraineesData(response.data.users || []);
        setPagination(prev => ({
          ...prev,
          page: response.data.pagination?.current_page || 1,
          totalPages: response.data.pagination?.total_pages || 0,
          totalCount: response.data.pagination?.total_records || 0
        }));
      }
    } catch (error) {
      console.error('Error fetching trainee progress:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSearchChange = (e) => {
    setSearch(e.target.value);
    setPagination(prev => ({ ...prev, page: 1 })); // Reset to first page
  };

  const handleStatusFilterChange = (e) => {
    setStatusFilter(e.target.value);
    setPagination(prev => ({ ...prev, page: 1 })); // Reset to first page
  };

  const handlePageChange = (newPage) => {
    setPagination(prev => ({ ...prev, page: newPage }));
  };

  return (
    <>
      {/* Search and Filter */}
      <div className="row d-flex mb-3">
        <div className="col-md-6 mt-2 d-flex justify-content-start align-items-center">
          <input
            type="search"
            className="form-control"
            placeholder="Search trainees..."
            style={{ maxWidth: '300px' }}
            value={search}
            onChange={handleSearchChange}
          />
        </div>
        <div className="col-md-6 text-end mt-2 d-flex justify-content-end align-items-center gap-2">
          <select
            className="form-select"
            style={{ width: '180px' }}
            value={statusFilter}
            onChange={handleStatusFilterChange}
          >
            <option value="">All Progress</option>
            <option value="completed">Completed</option>
            <option value="in_progress">In Progress</option>
            <option value="not_started">Not Started</option>
          </select>
        </div>
      </div>

      {/* Table */}
      <div className="row mb-3">
        <div className="col-12">
          <div className="card">
            <div className="table-responsive">
              <table className="table table-borderless mb-0">
                <thead>
                  <tr>
                    <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500' }}>SL.No</th>
                    <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500' }}>User</th>
                    <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500' }}>Course Completion</th>
                  </tr>
                </thead>
                <tbody>
                  {loading ? (
                    <tr>
                      <td colSpan="3" className="text-center py-4">
                        <div className="spinner-border text-primary" role="status">
                          <span className="visually-hidden">Loading...</span>
                        </div>
                      </td>
                    </tr>
                  ) : traineesData.length === 0 ? (
                    <tr>
                      <td colSpan="3" className="text-center py-4 text-muted">
                        No trainees found
                      </td>
                    </tr>
                  ) : (
                    traineesData.map((trainee, index) => (
                      <tr key={trainee.user_id || index} className="border-bottom">
                        <td className="py-3 align-middle">
                          {(pagination.page - 1) * pagination.limit + index + 1}
                        </td>
                        <td className="py-3">
                          <div className="d-flex align-items-center">
                            <img
                              src={trainee.profile_pic_url || defaultProfile}
                              alt={trainee.user_name || 'User'}
                              className="rounded-circle me-3"
                              width="40"
                              height="40"
                              onError={(e) => {
                                e.target.src = defaultProfile;
                              }}
                            />
                            <div>
                              <div className="fw-medium">{trainee.user_name || 'N/A'}</div>
                              <div className="text-muted small">{trainee.email || 'N/A'}</div>
                            </div>
                          </div>
                        </td>
                        <td className="py-3 align-middle">
                          <div className="d-flex align-items-center">
                            <div className="progress flex-grow-1" style={{ height: '6px', width: '100px' }}>
                              <div
                                className="progress-bar bg-primary"
                                role="progressbar"
                                style={{ width: `${Number(trainee.overallCompletionPercentage || 0)}%` }}
                                aria-valuenow={Number(trainee.overallCompletionPercentage || 0)}
                                aria-valuemin="0"
                                aria-valuemax="100"
                              ></div>
                            </div>
                            <span className="ms-2">{Number(trainee.overallCompletionPercentage || 0).toFixed(2)}%</span>
                          </div>
                        </td>
                      </tr>
                    ))
                  )}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>

      {/* Pagination */}
      {!loading && traineesData.length > 0 && (
        <div className="row">
          <div className="col-md-6">
            <select
              className="form-select"
              style={{ width: 'auto' }}
              value={pagination.limit}
              onChange={(e) => setPagination(prev => ({ ...prev, limit: parseInt(e.target.value), page: 1 }))}
            >
              <option value={10}>10 records per page</option>
              <option value={25}>25 records per page</option>
              <option value={50}>50 records per page</option>
            </select>
          </div>
          <div className="col-md-6 d-flex justify-content-end align-items-center gap-3">
            <button
              className="btn btn-primary"
              style={{ width: '150px' }}
              disabled={pagination.page <= 1}
              onClick={() => handlePageChange(pagination.page - 1)}
            >
              Prev
            </button>
            <span>Page {pagination.page} of {pagination.totalPages}</span>
            <button
              className="btn btn-primary"
              style={{ width: '150px' }}
              disabled={pagination.page >= pagination.totalPages}
              onClick={() => handlePageChange(pagination.page + 1)}
            >
              Next
            </button>
          </div>
        </div>
      )}
    </>
  )
}

export default CourseAnalyticsTraineeProgress
