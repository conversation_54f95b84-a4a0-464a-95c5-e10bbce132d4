.permission-module {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  margin-bottom: 1rem;
}

.module-header {
  padding: 1rem;
  background-color: #f8f9fa;
  border-radius: 8px 8px 0 0;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.module-header:hover {
  background-color: #f0f0f0;
}

.module-header.active {
  background-color: #e9ecef;
  border-bottom: 1px solid #e0e0e0;
}

.module-name {
  font-weight: 500;
}

.module-permissions {
  background-color: #ffffff;
  border-radius: 0 0 8px 8px;
}

.permission-action {
  padding: 0.5rem 0;
  border-bottom: 1px solid #f0f0f0;
}

.permission-action:last-child {
  border-bottom: none;
}

.modules-list {
  max-height: calc(100vh - 10px);
  overflow-y: auto;
  padding-right: 1rem;
}

.modules-list::-webkit-scrollbar {
  width: 6px;
}

.modules-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.modules-list::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 3px;
}

.modules-list::-webkit-scrollbar-thumb:hover {
  background: #555;
}

/* Nested modules styling */
.module-permissions .permission-module {
  margin-left: 1.5rem;
  margin-top: 1rem;
  border-left: 2px solid #e0e0e0;
}

/* Checkbox styling */
.form-check-input {
  cursor: pointer;
}

.form-check-input:checked {
  background-color: #0d6efd;
  border-color: #0d6efd;
} 