import React, { useEffect, useState, useCallback } from 'react';
import { Icon } from '@iconify/react';
import { getClassroomResourcesForTrainees} from '../../../services/userService';
import { useParams } from 'react-router-dom';

function RecordedVideo({ classroom_id }) {
  const { classroomId } = useParams();
  const decodedClassroomId = classroom_id || classroomId;
  
  // State management
  const [resources, setResources] = useState([]);
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedResourceType, setSelectedResourceType] = useState('all');
  const [limit] = useState(12);
  const [showViewModal, setShowViewModal] = useState(false);
  const [selectedResource, setSelectedResource] = useState(null);

  // Fetch resources function
  const fetchResources = useCallback(async () => {
    try {
      setLoading(true);
      
      const payload = {
        id: null,
        page: currentPage,
        limit: limit,
        search: searchTerm,
        resource_type: selectedResourceType === 'all' ? null : selectedResourceType
      };

      console.log('Fetching resources with payload:', payload);
      console.log('Classroom ID:', decodedClassroomId);
      
      const response = await getClassroomResourcesForTrainees(payload);
      
      console.log('API Response:', response);

      if (response.success) {
        setResources(response.data);
        setTotalPages(response.pagination.totalPages);
        setTotalCount(response.pagination.total);
        console.log('Resources fetched successfully:', response.data);
        console.log('Pagination info:', response.pagination);
      } else {
        console.error('Failed to fetch resources:', response.message);
      }
    } catch (error) {
      console.error('Error fetching resources:', error);
    } finally {
      setLoading(false);
    }
  }, [decodedClassroomId, currentPage, limit, searchTerm, selectedResourceType]);

  // Fetch resources on component mount and when dependencies change
  useEffect(() => {
    if (decodedClassroomId) {
      fetchResources();
    }
  }, [decodedClassroomId, currentPage, searchTerm, selectedResourceType]);

  // Handle search
  const handleSearch = (e) => {
    setSearchTerm(e.target.value);
    setCurrentPage(1); // Reset to first page when searching
  };

  // Handle resource type filter
  const handleResourceTypeChange = (e) => {
    setSelectedResourceType(e.target.value);
    setCurrentPage(1); // Reset to first page when filtering
  };

  // Handle page change
  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  // Handle view resource
  const handleViewResource = (resource) => {
    setSelectedResource(resource);
    setShowViewModal(true);
  };

  // Close modal
  const closeModal = () => {
    setShowViewModal(false);
    setSelectedResource(null);
  };

  // Get resource icon based on type
  const getResourceIcon = (resourceType) => {
    switch (resourceType) {
      case 'image':
        return 'fluent:image-24-regular';
      case 'video':
        return 'fluent:video-24-regular';
      case 'audio':
        return 'fluent:audio-24-regular';
      case 'document':
        return 'fluent:document-24-regular';
      case 'url':
        return 'fluent:link-24-regular';
      case 'text':
        return 'fluent:text-24-regular';
      default:
        return 'fluent:document-24-regular';
    }
  };

  // Get resource type label
  const getResourceTypeLabel = (resourceType) => {
    return resourceType.charAt(0).toUpperCase() + resourceType.slice(1);
  };

  // Render media content based on resource type
  const renderMediaContent = (resource) => {
    if (!resource.media_url) return null;

    switch (resource.resource_type) {
      case 'image':
        return (
          <div className="text-center">
            <img
              src={resource.media_url}
              alt={resource.resource_name}
              className="img-fluid rounded"
              style={{ maxHeight: '400px', maxWidth: '100%' }}
            />
          </div>
        );
      
      case 'video':
        return (
          <div className="text-center">
            <video
              controls
              preload="metadata"
              className="img-fluid rounded border"
              style={{ 
                maxHeight: '400px', 
                maxWidth: '100%',
                width: '100%',
                height: 'auto'
              }}
              controlsList="nodownload"
            >
              <source src={resource.media_url} type="video/mp4" />
              <source src={resource.media_url} type="video/webm" />
              <source src={resource.media_url} type="video/ogg" />
              Your browser does not support the video tag.
            </video>
          </div>
        );
      
      case 'audio':
        return (
          <div className="text-center p-3">
            <audio 
              controls 
              preload="metadata"
              className="w-100"
              style={{ 
                maxWidth: '100%',
                height: '50px'
              }}
              controlsList="nodownload"
            >
              <source src={resource.media_url} type="audio/mpeg" />
              <source src={resource.media_url} type="audio/mp3" />
              <source src={resource.media_url} type="audio/wav" />
              <source src={resource.media_url} type="audio/ogg" />
              Your browser does not support the audio tag.
            </audio>
          </div>
        );
      
      case 'url':
        return (
          <div className="text-center">
            <a
              src={resource.media_url}
              className="border rounded"
              title={resource.resource_name}
            />
          </div>
        );
      
      case 'document':
        return (
          <div className="text-center">
            <iframe
              src={resource.media_url}
              className="border rounded"
              style={{
                width: '100%',
                height: '400px',
                maxHeight: '500px'
              }}
              title={resource.resource_name}
            />
          </div>
        );
      
      default:
        return (
          <div className="text-center">
            <a
              href={resource.media_url}
              target="_blank"
              rel="noopener noreferrer"
              className="btn btn-primary"
            >
              <Icon icon="fluent:open-24-regular" className="me-2" />
              Open Resource
            </a>
          </div>
        );
    }
  };
  
  return (
    <>
      <div className="row mb-4 mt-2">
        <div className="col-md-4">
          <div className="d-flex align-items-center gap-3">
            <div className="input-group" >
              <input
                type="text"
                className="form-control"
                placeholder="Search resources..."
                value={searchTerm}
                onChange={handleSearch}
              />
            </div>
            <select
              className="form-select"
              style={{ width: 'auto', minWidth: '150px' }}
              value={selectedResourceType}
              onChange={handleResourceTypeChange}
            >
              <option value="all">All Types</option>
              <option value="document">Documents</option>
              <option value="image">Images</option>
              <option value="video">Videos</option>
              <option value="audio">Audio</option>
              <option value="url">URLs</option>
              <option value="text">Text</option>
            </select>
          </div>
        </div>
        <div className="col-md-8 d-flex justify-content-end align-items-center">
          <span className="text-muted">
            Total: {totalCount} resources
          </span>
        </div>
      </div>

      {/* Resources Display */}
      <div className="row">
        {loading && currentPage === 1 ? (
          <div className="col-12 text-center py-5">
            <div className="spinner-border text-primary" role="status">
              <span className="visually-hidden">Loading...</span>
            </div>
          </div>
        ) : resources.length === 0 && !loading ? (
          <div className="col-12 text-center py-5">
            <Icon icon="fluent:document-24-regular" className="text-muted mb-3" width="48" height="48" />
            <h5>No Resources Found</h5>
            <p className="text-muted">
              {searchTerm ? 'No resources match your search criteria.' : 'No resources available for this classroom.'}
            </p>
          </div>
        ) : (
          resources.map((resource, index) => (
            <div
              key={resource.id}
              className="col-md-6 col-lg-4 mb-4"
            >
              <div className="card h-100 shadow-sm d-flex flex-column" style={{ minHeight: '280px' }}>
                <div className="card-header bg-transparent border-bottom-0 pb-0">
                  <div className="d-flex justify-content-between align-items-start">
                    <div className="d-flex align-items-center gap-2">
                      <Icon
                        icon={getResourceIcon(resource.resource_type)}
                        className="text-primary"
                        width="20"
                        height="20"
                      />
                      <span className="badge bg-primary-subtle text-primary px-2 py-1">
                        {getResourceTypeLabel(resource.resource_type)}
                      </span>
                    </div>
                  </div>
                </div>

                <div className="card-body d-flex flex-column flex-grow-1">
                  {/* Title with 3-line truncation */}
                  <h6 className="card-title mb-2 fw-semibold" style={{
                    display: '-webkit-box',
                    WebkitLineClamp: 3,
                    WebkitBoxOrient: 'vertical',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    lineHeight: '1.3',
                    maxHeight: '3.9em',
                    fontSize: '1rem',
                    minHeight: '1.3em'
                  }}>
                    {resource.resource_name}
                  </h6>

                  {/* Description with 1-line truncation */}
                  {resource.resource_description && (
                    <p className="card-text text-muted small mb-3" style={{
                      display: '-webkit-box',
                      WebkitLineClamp: 1,
                      WebkitBoxOrient: 'vertical',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      lineHeight: '1.4',
                      maxHeight: '1.4em',
                      fontSize: '0.875rem',
                      minHeight: '1.4em'
                    }}>
                      {resource.resource_description}
                    </p>
                  )}

                  {/* Separator Line */}
                  <hr className="my-3" style={{ opacity: 0.2 }} />

                  {/* Bottom Section - Always at bottom */}
                  <div className="mt-auto">
                    <div className="d-flex justify-content-between align-items-center mb-3">
                      <small className="text-muted fw-medium">
                        <Icon icon="fluent:calendar-24-regular" className="me-1" width="12" height="12" />
                        {new Date(resource.created_at).toLocaleDateString()}
                      </small>
                    </div>

                    {/* Always show View Resource button */}
                    <div className="d-grid">
                      <button
                        onClick={() => handleViewResource(resource)}
                        className="btn btn-primary btn-sm"
                        style={{ 
                          fontSize: '0.875rem',
                          padding: '0.5rem 1rem',
                          borderWidth: '1px'
                        }}
                      >
                        <Icon icon="fluent:open-24-regular" className="me-2" width="14" height="14" />
                        View Resource
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))
        )}
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="row mt-4">
          <div className="col-12 text-center">
            <nav aria-label="Page navigation">
              <ul className="pagination justify-content-end">
                <li className={`page-item ${currentPage === 1 ? 'disabled' : ''}`}>
                  <button
                    className="page-link"
                    onClick={() => handlePageChange(currentPage - 1)}
                    disabled={currentPage === 1}
                  >
                    Previous
                  </button>
                </li>
                {Array.from({ length: totalPages }, (_, i) => (
                  <li key={i} className={`page-item ${currentPage === i + 1 ? 'active' : ''}`}>
                    <button
                      className="page-link"
                      onClick={() => handlePageChange(i + 1)}
                    >
                      {i + 1}
                    </button>
                  </li>
                ))}
                <li className={`page-item ${currentPage === totalPages ? 'disabled' : ''}`}>
                  <button
                    className="page-link"
                    onClick={() => handlePageChange(currentPage + 1)}
                    disabled={currentPage === totalPages}
                  >
                    Next
                  </button>
                </li>
              </ul>
            </nav>
          </div>
        </div>
      )}

      {/* View Resource Modal */}
      {showViewModal && selectedResource && (
        <div className="modal fade show d-block" style={{ backgroundColor: 'rgba(0,0,0,0.5)' }}>
          <div className="modal-dialog modal-lg">
            <div className="modal-content">
              <div className="modal-header">
                <div className="d-flex align-items-center gap-2">
                  <Icon
                    icon={getResourceIcon(selectedResource.resource_type)}
                    className="text-primary"
                    width="24"
                    height="24"
                  />
                  View Resource
                </div>
                <button
                  type="button"
                  className="btn-close"
                  onClick={closeModal}
                ></button>
              </div>
              <div className="modal-body">
                {/* Resource Type Badge */}
                <div className="mb-3">
                  <span className="badge bg-primary-subtle text-primary px-3 py-2">
                    {getResourceTypeLabel(selectedResource.resource_type)} Resource
                  </span>
                </div>

                {/* Resource Title Input */}
                <div className="mb-3">
                  <label className="form-label fw-semibold">Title:</label>
                  <input
                    type="text"
                    className="form-control"
                    value={selectedResource.resource_name}
                    readOnly
                    style={{ backgroundColor: '#f8f9fa', border: '1px solid #dee2e6' }}
                  />
                </div>

                {/* Resource Description Input */}
                {selectedResource.resource_description && (
                  <div className="mb-4">
                    <label className="form-label fw-semibold">Description:</label>
                    <textarea
                      className="form-control"
                      rows="4"
                      value={selectedResource.resource_description}
                      readOnly
                      style={{ backgroundColor: '#f8f9fa', border: '1px solid #dee2e6' }}
                    ></textarea>
                  </div>
                )}

                {/* Media Content */}
                {selectedResource.media_url && (
                  <div className="mb-3">
                    <h6 className="fw-semibold mb-3">Content:</h6>
                    {renderMediaContent(selectedResource)}
                  </div>
                )}

                {/* Resource Details */}
                <div className="mt-4 pt-3 border-top">
                  <div className="row">
                    <div className="col-12 text-end">
                      {selectedResource.media_url && (
                        <a
                          href={selectedResource.media_url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="btn btn-primary btn-sm"
                        >
                          <Icon icon="fluent:open-24-regular" className="me-1" />
                          Open in New Tab
                        </a>
                      )}
                    </div>
                  </div>
                </div>
              </div>
              <div className="modal-footer">
                <button
                  type="button"
                  className="btn btn-secondary"
                  onClick={closeModal}
                >
                  Close
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
}

export default RecordedVideo;