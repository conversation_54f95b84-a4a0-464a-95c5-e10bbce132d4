:root {
  /* Base unit for spacing */
  --spacing-unit: 0.25rem;

  /* Padding Variables */
  --padding-xs: calc(var(--spacing-unit) * 1);    /* 0.25rem */
  --padding-sm: calc(var(--spacing-unit) * 2);    /* 0.5rem */
  --padding-md: calc(var(--spacing-unit) * 3);    /* 0.75rem */
  --padding-lg: calc(var(--spacing-unit) * 4);    /* 1rem */
  --padding-xl: calc(var(--spacing-unit) * 6);    /* 1.5rem */
  --padding-2xl: calc(var(--spacing-unit) * 8);   /* 2rem */
  
  /* Specific Component Padding */
  --padding-button: var(--padding-sm) var(--padding-lg);
  --padding-input: var(--padding-sm) var(--padding-md);
  --padding-card: var(--padding-lg);
  --padding-section: var(--padding-xl);
  --padding-container: var(--padding-2xl);
  
  /* Directional Padding */
  --padding-inline: var(--padding-md);
  --padding-block: var(--padding-sm);

  /* Colors */
  --error-color: #F04438;
  --error-bg: #FEF3F2;
  --error-border: #FDA29B;
  --primary-color: #4318FF;
  --primary-hover: #3311dd;
  --disabled-bg: #e9ecef;
  --disabled-color: #6c757d;
  --input-border: #E4E7EC;
  
  /* Transitions */
  --transition-fast: 0.15s ease-in-out;
  --transition-normal: 0.3s ease-in-out;
}

/* Utility Classes */
.p-xs { padding: var(--padding-xs); }
.p-sm { padding: var(--padding-sm); }
.p-md { padding: var(--padding-md); }
.p-lg { padding: var(--padding-lg); }
.p-xl { padding: var(--padding-xl); }
.p-2xl { padding: var(--padding-2xl); }

/* Directional Padding Utilities */
.px-xs { padding-left: var(--padding-xs); padding-right: var(--padding-xs); }
.px-sm { padding-left: var(--padding-sm); padding-right: var(--padding-sm); }
.px-md { padding-left: var(--padding-md); padding-right: var(--padding-md); }
.px-lg { padding-left: var(--padding-lg); padding-right: var(--padding-lg); }
.px-xl { padding-left: var(--padding-xl); padding-right: var(--padding-xl); }

.py-xs { padding-top: var(--padding-xs); padding-bottom: var(--padding-xs); }
.py-sm { padding-top: var(--padding-sm); padding-bottom: var(--padding-sm); }
.py-md { padding-top: var(--padding-md); padding-bottom: var(--padding-md); }
.py-lg { padding-top: var(--padding-lg); padding-bottom: var(--padding-lg); }
.py-xl { padding-top: var(--padding-xl); padding-bottom: var(--padding-xl); }

/* Form Group Styles */
.auth-form-group {
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 6px;
  min-height: 82px; 
  margin-bottom: 16px;
}

.auth-form-group label {
  color: #344054;
  font-size: 14px;
  font-weight: 500;
  line-height: 20px;
}

.auth-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

/* Form Input Styles */
.auth-input {
  width: 100%;
  height: 44px;
  padding: 10px 14px 10px 42px; /* Space for icon */
  border: 1px solid var(--input-border);
  border-radius: 8px;
  background: #FFFFFF;
  font-size: 16px;
  line-height: 24px;
  color: #101828;
  transition: all var(--transition-fast);
}

.auth-input::placeholder {
  color: #667085;
}

.auth-input-icon {
  position: absolute;
  left: 14px;
  color: #667085;
  font-size: 20px;
}

/* Error Styles */
.auth-input.error {
  border-color: #fda29b;
  box-shadow: 0 0 0 4px rgba(253, 162, 155, 0.1);
}

.error-message {
  position: absolute;
  top: calc(100% - 2px);
  left: 0;
  color: var(--error-color);
  font-size: 12px;
  line-height: 18px;
  margin-top: 2px;
}

/* Focus States */
.auth-input:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 4px rgba(67, 24, 255, 0.1);
  outline: none;
}

.auth-input.error:focus {
  border-color: var(--error-border);
  box-shadow: 0 0 0 4px rgba(253, 162, 155, 0.1);
}

/* Country Code Selector Styles */
.country-code-selector {
    min-width: 120px;
}

.country-code-select {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    min-width: 90px;
    height: 44px;
    padding: 10px 14px;
    border: 1px solid var(--input-border);
    border-radius: 8px;
    background: #FFFFFF;
    font-size: 16px;
    line-height: 24px;
    color: #101828;
    cursor: pointer;
    transition: all var(--transition-fast);
}

.country-code-select:hover {
    border-color: var(--primary-color);
}

.country-code-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 4px rgba(67, 24, 255, 0.1);
    outline: none;
}

.country-code-wrapper {
    position: relative;
}

.dropdown-icon {
    color: #667085;
    font-size: 16px;
    transition: transform 0.2s ease;
}

.country-code-button {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding: 0.375rem 0.75rem;
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5;
    color: #212529;
    background-color: #fff;
    border: 1px solid #ced4da;
    border-radius: 0.25rem;
    cursor: pointer;
}

.country-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    z-index: 1000;
    width: 300px;
    max-height: 300px;
    background-color: #fff;
    border: 1px solid #ced4da;
    border-radius: 0.25rem;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    margin-top: 0.25rem;
}

.country-search {
    position: relative;
    padding: 0.5rem;
    border-bottom: 1px solid #ced4da;
}

.country-search input {
    width: 100%;
    padding: 0.375rem 0.75rem;
    padding-left: 2rem;
    font-size: 1rem;
    border: 1px solid #ced4da;
    border-radius: 0.25rem;
}

.country-search .auth-input-icon {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
}

.country-list {
    max-height: 250px;
    overflow-y: auto;
}

.country-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.5rem 1rem;
    cursor: pointer;
}

.country-item:hover {
    background-color: #f8f9fa;
}

.country-code {
    color: #6c757d;
}

/* Custom scrollbar for the country list */
.country-list::-webkit-scrollbar {
    width: 6px;
}

.country-list::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.country-list::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 3px;
}

.country-list::-webkit-scrollbar-thumb:hover {
    background: #555;
}

.clickable-row {
  transition: background-color 0.2s ease;
}

.clickable-row:hover {
  background-color: #f8f9fa;
}


