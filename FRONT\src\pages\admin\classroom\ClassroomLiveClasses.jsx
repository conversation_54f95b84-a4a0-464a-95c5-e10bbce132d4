import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { Icon } from '@iconify/react';
import { toast } from 'react-toastify';
import { decodeData } from '../../../utils/encodeAndEncode';
import timeZones from '../../../utils/timeZone.json';
import {
  createZoomLiveClass,
  getZoomLiveClasses,
  updateZoomLiveClass,
  deleteZoomLiveClass,
  startZoomLiveClass,
  endZoomLiveClass
} from '../../../services/adminService';
import Loader from '../../../components/common/Loader';
import NoData from '../../../components/common/NoData';
import './Classroom.css';
import moment from 'moment-timezone';

const DefaultTimeZone = process.env.REACT_APP_DEFAULT_TIMEZONE || 'Asia/Kolkata';

function ClassroomLiveClasses() {
  const { classroomId: encodedClassroomId } = useParams();
  const classroomId = decodeData(encodedClassroomId);
  console.log('decodedClassroomId------------------------------', classroomId);
  const userId = JSON.parse(localStorage.getItem('user'))?.id;

  // State management
  const [liveClasses, setLiveClasses] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedClass, setSelectedClass] = useState(null);
  const [formData, setFormData] = useState({
    live_class_name: '',
    live_class_description: '',
    time_zone: DefaultTimeZone,
    class_date: '',
    start_time: '',
    duration: '30'
  });
  const [formErrors, setFormErrors] = useState({});
  const [submitting, setSubmitting] = useState(false);

  // Pagination and search state
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [totalPages, setTotalPages] = useState(1);
  const [totalRecords, setTotalRecords] = useState(0);

  // Timezone dropdown state
  const [timeZoneSearch, setTimeZoneSearch] = useState('');
  const [isTimeZoneDropdownOpen, setIsTimeZoneDropdownOpen] = useState(false);

  // Loading states for action buttons
  const [startingClassId, setStartingClassId] = useState(null);
  const [endingClassId, setEndingClassId] = useState(null);

  // Debounced search query
  const [debouncedSearchQuery, setDebouncedSearchQuery] = useState('');

  // Debounce search query to avoid too many API calls
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchQuery(searchQuery);
    }, 500); // 500ms delay

    return () => clearTimeout(timer);
  }, [searchQuery]);

  // Fetch live classes on component mount
  useEffect(() => {
    if (classroomId) {
      fetchLiveClasses();
    }
  }, [classroomId, currentPage, itemsPerPage, debouncedSearchQuery, statusFilter]);

  const fetchLiveClasses = async () => {
    try {
      setLoading(true);
      const response = await getZoomLiveClasses({
        classroom_id: classroomId,
        page: currentPage,
        limit: itemsPerPage,
        search: debouncedSearchQuery,
        status: statusFilter
      });
      console.log('Live Classes Response:++++++++++++++++++++++++++++++++++', response);
      if (response.data) {
        setLiveClasses(response.data.live_classes || response.data || []);
        setTotalPages(response.data.pagination?.totalPages || 1);
        setTotalRecords(response.data.pagination?.totalRecords || 0);
      }
    } catch (error) {
      console.error('Error fetching live classes:', error);
      toast.error('Failed to fetch live classes');
    } finally {
      setLoading(false);
    }
  };

  const validateForm = () => {
    const errors = {};

    if (!formData.live_class_name.trim()) {
      errors.live_class_name = 'Class name is required';
    }

    if (!formData.class_date) {
      errors.class_date = 'Class date is required';
    }

    if (!formData.start_time) {
      errors.start_time = 'Start time is required';
    }

    if (!formData.time_zone) {
      errors.time_zone = 'Time zone is required';
    }

    if (!formData.duration) {
      errors.duration = 'Duration is required';
    } else if (isNaN(formData.duration) || Number(formData.duration) < 1 || Number(formData.duration) > 480) {
      errors.duration = 'Duration must be between 1 and 480 minutes';
    }

    // Check if date and time are in the future
    if (formData.class_date && formData.start_time) {
      const selectedDateTime = new Date(`${formData.class_date}T${formData.start_time}`);
      const now = new Date();
      if (selectedDateTime <= now) {
        errors.start_time = 'Start time must be in the future';
      }
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Clear error when user starts typing
    if (formErrors[name]) {
      setFormErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const resetForm = () => {
    setFormData({
      live_class_name: '',
      live_class_description: '',
      time_zone: DefaultTimeZone,
      class_date: '',
      start_time: '',
      duration: '30'
    });
    setFormErrors({});
    setTimeZoneSearch('');
    setIsTimeZoneDropdownOpen(false);
  };

  const handleCreateClass = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      setSubmitting(true);

      // Convert local time to UTC based on selected timezone
      const localDateTime = `${formData.class_date}T${formData.start_time}`;
      const localMoment = moment.tz(localDateTime, formData.time_zone);
      const utcDateTime = localMoment.utc().format();


      const protocol = window.location.protocol;
      const hostname = window.location.hostname;
      const port = window.location.port;
      const fullURL = `${protocol}//${hostname}${port ? `:${port}` : ''}`;

      const payload = {
        classroom_id: classroomId,
        live_class_name: formData.live_class_name,
        live_class_description: formData.live_class_description,
        time_zone: formData.time_zone,
        class_date: formData.class_date,
        start_time: utcDateTime,
        duration: `${formData.duration} minutes`,
        sub_domain: fullURL,
        userId: userId
      };

      const response = await createZoomLiveClass(payload);
      console.log('Response==========================:', response);

      toast.success('Live class created successfully!');
      setShowCreateModal(false);
      resetForm();
      fetchLiveClasses();

      // if (response.data.success) {
      //   toast.success('Live class created successfully!');
      //   setShowCreateModal(false);
      //   resetForm();
      //   fetchLiveClasses();
      // } else {
      //   toast.error(response.data.message || 'Failed to create live class');
      //   console.error('response', response.data);
      // }
    } catch (error) {
      console.error('Error creating live class:', error);
      toast.error('Failed to create live class');
    } finally {
      setSubmitting(false);
    }
  };

  const handleEditClass = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      setSubmitting(true);

      // Convert local time to UTC based on selected timezone
      const localDateTime = `${formData.class_date}T${formData.start_time}`;
      const localMoment = moment.tz(localDateTime, formData.time_zone);
      const utcDateTime = localMoment.utc().format();

      const payload = {
        live_class_name: formData.live_class_name,
        live_class_description: formData.live_class_description,
        time_zone: formData.time_zone,
        class_date: formData.class_date,
        start_time: utcDateTime,
        duration: `${formData.duration} minutes`,
        userId: userId
      };

      const response = await updateZoomLiveClass(selectedClass.id, payload);

      // if (response.data.success) {
      toast.success('Live class updated successfully!');
      setShowEditModal(false);
      resetForm();
      setSelectedClass(null);
      fetchLiveClasses();
      // } else {
      // toast.error(response.data.message || 'Failed to update live class');
      // }
    } catch (error) {
      console.error('Error updating live class:', error);
      toast.error('Failed to update live class');
    } finally {
      setSubmitting(false);
    }
  };

  const handleDeleteClass = async () => {
    try {
      setSubmitting(true);
      const response = await deleteZoomLiveClass(selectedClass.id);
      toast.success('Live class deleted successfully!');
      setShowDeleteModal(false);
      setSelectedClass(null);
      fetchLiveClasses();
    } catch (error) {
      console.error('Error deleting live class:', error);
      toast.error('Failed to delete live class');
    } finally {
      setSubmitting(false);
    }
  };

  const handleStartClass = async (liveClass) => {
    try {
      setStartingClassId(liveClass.id);

      const protocol = window.location.protocol;
      const hostname = window.location.hostname;
      const port = window.location.port;
      const fullURL = `${protocol}//${hostname}${port ? `:${port}` : ''}`;
      // Add minimum loading time of 1.5 seconds
      const startTime = Date.now();
      const response = await startZoomLiveClass({ live_class_id: liveClass.id, sub_domain: fullURL });
      console.log('Response==========================:', response);
      const elapsedTime = Date.now() - startTime;

      // Ensure minimum 1.5 seconds loading time
      if (elapsedTime < 1500) {
        await new Promise(resolve => setTimeout(resolve, 1500 - elapsedTime));
      }

      toast.success('Live class started successfully!');
      // Open the host URL in the same tab
      window.location.href = response.data.host_url;
    } catch (error) {
      console.error('Error starting live class:', error);
      toast.error('Failed to start live class');
    } finally {
      setStartingClassId(null);
    }
  };

  const handleJoinClass = async (liveClass) => {
    try {

      const protocol = window.location.protocol;
      const hostname = window.location.hostname;
      const port = window.location.port;
      const fullURL = `${protocol}//${hostname}${port ? `:${port}` : ''}`;

      // For already started meetings, generate a fresh host URL
      const response = await startZoomLiveClass({ live_class_id: liveClass.id, sub_domain: fullURL });
      console.log('Response==========================:', response);

      // if (response.data.success) {
      toast.success(response.data.message || 'Joining meeting...');
      // Open the host URL in the same tab
      window.location.href = response.data.host_url;
      // } else {
      // toast.error(response.data.message || 'Failed to join meeting');
      // }
    } catch (error) {

    }
  };

  const handleEndClass = async (liveClass) => {
    try {
      setEndingClassId(liveClass.id);

      // Add minimum loading time of 1.5 seconds
      const startTime = Date.now();
      const response = await endZoomLiveClass({ live_class_id: liveClass.id });
      const elapsedTime = Date.now() - startTime;

      // Ensure minimum 1.5 seconds loading time
      if (elapsedTime < 1500) {
        await new Promise(resolve => setTimeout(resolve, 1500 - elapsedTime));
      }

      console.log('Response==========================:', response);
      toast.success('Live class ended successfully!');
      fetchLiveClasses();
    } catch (error) {
      console.error('Error ending live class:', error);
      toast.error('Failed to end live class');
    } finally {
      setEndingClassId(null);
    }
  };

  const openEditModal = (liveClass) => {
    setSelectedClass(liveClass);

    // Extract duration number from string (e.g., "60 minutes" -> "60")
    const durationMatch = liveClass.duration ? liveClass.duration.match(/(\d+)/) : null;
    const durationValue = durationMatch ? durationMatch[1] : '30';

    // Get the timezone from the live class or use default
    const timezone = liveClass.time_zone || DefaultTimeZone;

    // Convert UTC time to the selected timezone
    const utcMoment = moment.utc(liveClass.start_time);
    const localMoment = utcMoment.tz(timezone);

    const dateStr = localMoment.format('YYYY-MM-DD');
    const timeStr = localMoment.format('HH:mm');

    setFormData({
      live_class_name: liveClass.live_class_name || '',
      live_class_description: liveClass.live_class_description || '',
      time_zone: timezone,
      class_date: dateStr,
      start_time: timeStr,
      duration: durationValue
    });
    setTimeZoneSearch('');
    setIsTimeZoneDropdownOpen(false);
    setShowEditModal(true);
  };

  const openDeleteModal = (liveClass) => {
    setSelectedClass(liveClass);
    setShowDeleteModal(true);
  };

  // Search and pagination handlers
  const handleSearchChange = (e) => {
    setSearchQuery(e.target.value);
    setCurrentPage(1); // Reset to first page when search changes
  };

  const clearFilters = () => {
    setSearchQuery('');
    setStatusFilter('');
    setCurrentPage(1);
  };

  const handlePageChange = (newPage) => {
    setCurrentPage(newPage);
  };

  const handleLimitChange = (e) => {
    setItemsPerPage(parseInt(e.target.value));
    setCurrentPage(1); // Reset to first page when limit changes
  };

  const getStatusBadge = (liveClass) => {
    const commonStyles = {
      fontWeight: '600',
      borderWidth: '2px',
      padding: '6px 12px',
      borderRadius: '8px',
      fontSize: '0.875rem'
    };

    if (liveClass.is_ended === 1) {
      return (
        <span
          className="text-danger border border-danger"
          style={{ backgroundColor: '#fdeaea', ...commonStyles }}
        >
          Ended
        </span>
      );
    } else if (liveClass.is_started === 1) {
      return (
        <span
          className="text-success border border-success"
          style={{ backgroundColor: '#e6f4ea', ...commonStyles }}
        >
          Live
        </span>
      );
    } else {
      return (
        <span
          className="text-primary border border-primary"
          style={{ backgroundColor: '#e9f1ff', ...commonStyles }}
        >
          Scheduled
        </span>
      );
    }
  };



  const formatDateTime = (dateTimeString) => {
    const date = new Date(dateTimeString);
    return date.toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    });
  };

  if (loading) {
    return <Loader caption="Loading live classes..." />;
  }

  return (
    <div className="container-fluid p-4">
      {/* Search, Filter and Create button */}
      <div className="row mb-3">
        <div className="col-12 d-flex flex-column flex-md-row gap-3 align-items-start align-items-md-center">
          {/* Search Section - Left Side */}
          <div className="w-100 w-md-auto">
            <input
              type="search"
              className="form-control"
              placeholder="Search live classes..."
              style={{ minWidth: '200px', maxWidth: '300px' }}
              value={searchQuery}
              onChange={handleSearchChange}
            />
          </div>
          
          {/* Filter and Create Button Section - Right Side */}
          <div className="d-flex flex-column flex-md-row gap-2 ms-md-auto w-100 w-md-auto justify-content-end">
            <div className="d-flex gap-2 align-items-center ">
              <select
                className="form-select"
                style={{ minWidth: '150px', maxWidth: '200px' }}
                value={statusFilter}
                onChange={(e) => {
                  setStatusFilter(e.target.value);
                  setCurrentPage(1); // Reset to first page when filter changes
                }}
              >
                <option value="">All Status</option>
                <option value="scheduled">Scheduled</option>
                <option value="live">Live</option>
                <option value="ended">Ended</option>
              </select>
              {(searchQuery || statusFilter) && (
                <button
                  className="btn btn-outline-secondary btn-sm"
                  onClick={clearFilters}
                  title="Clear all filters"
                >
                  <Icon icon="mdi:close" width="16" height="16" />
                </button>
              )}
            </div>
            
            <button
              className="btn btn-primary d-flex align-items-center gap-2 w-auto"
              onClick={() => setShowCreateModal(true)}
            >
              <Icon icon="fluent:add-24-regular" width="20" height="20" />
              Create Live Class
            </button>
          </div>
        </div>
      </div>

      {/* Table */}
      <div className="row mb-3">
        <div className="col-12">
          <div className="card">
            <div className="table-responsive">
              {loading ? (
                <Loader
                  caption="Loading Live Classes..."
                  minLoadTime={500}
                  containerHeight="400px"
                />
              ) : (
                <table className="table table-borderless mb-0" style={{ minWidth: '800px' }}>
                  <thead>
                    <tr>
                      <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500', width: '6%' }}>SL No</th>
                      <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500', width: '16%' }}>Class Name</th>
                      <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500', width: '16%' }}>Description</th>
                      <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500', width: '15%' }}>Date & Time</th>
                      <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500', width: '10%' }}>Duration</th>
                      <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500', width: '12%' }}>Created By</th>
                      <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500', width: '14%' }}>Status</th>
                      <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500', width: '15%' }}>Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {liveClasses.length > 0 ? liveClasses.map((liveClass, index) => (
                      <tr key={liveClass.id} className="border-bottom">
                        <td className="py-3" style={{ maxWidth: '20px' }} >
                          {((currentPage - 1) * itemsPerPage) + index + 1}
                        </td>
                        <td className="py-3">
                          <div className="fw-medium text-truncate" style={{ width: '200px' }}>
                            {liveClass.live_class_name}

                          </div>
                        </td>
                        <td className="py-3">
                          <div className="text-muted small text-truncate" style={{ width: '200px' }}>
                            {liveClass.live_class_description || 'No description'}
                          </div>
                        </td>
                        <td className="py-3">
                          <div className="small">
                            <Icon icon="mdi:calendar" className="me-1" />
                            {moment.utc(liveClass.start_time).local().format('DD MMM YYYY, hh:mm A')}

                          </div>
                        </td>
                        <td className="py-3">
                          <div className="small">
                            <Icon icon="mdi:clock" className="me-1" />
                            {liveClass.duration}
                          </div>
                        </td>
                        <td className="py-3">
                          <div className="small">
                            <Icon icon="mdi:account" className="me-1" />
                            {liveClass.created_by_name || 'Unknown'}
                          </div>
                        </td>
                        <td className="py-3">
                          {getStatusBadge(liveClass)}
                        </td>
                        <td className="py-3">
                          <div className="d-flex gap-2">
                            {liveClass.is_ended === 1 ? (
                              <>
                                <span className="text-muted small d-flex align-items-center">Ended</span>
                                <button
                                  className="btn btn-outline-danger btn-sm border border-danger"
                                  style={{ width: '36px', height: '36px' }}
                                  onClick={() => openDeleteModal(liveClass)}
                                  title="Delete Class"
                                >
                                  <Icon icon="mdi:delete" width="16" height="16" />
                                </button>
                              </>
                            ) : liveClass.is_started === 1 ? (
                              <>
                                <button
                                  className="btn btn-outline-success btn-sm border border-success"
                                  style={{ width: '36px', height: '36px' }}
                                  onClick={() => handleJoinClass(liveClass)}
                                  title="Join Meeting"
                                >
                                  <Icon icon="mdi:video" width="16" height="16" />
                                </button>
                                <button
                                  className="btn btn-outline-danger btn-sm border border-danger"
                                  style={{ width: '36px', height: '36px' }}
                                  onClick={() => handleEndClass(liveClass)}
                                  title="End Meeting"
                                  disabled={endingClassId === liveClass.id}
                                >
                                  {endingClassId === liveClass.id ? (
                                    <span className="spinner-border spinner-border-sm" style={{ width: '16px', height: '16px' }}></span>
                                  ) : (
                                    <Icon icon="mdi:stop" width="16" height="16" />
                                  )}
                                </button>
                              </>
                            ) : (
                              <>
                                <button
                                  className="btn btn-outline-primary btn-sm border border-primary"
                                  style={{ width: '36px', height: '36px' }}
                                  onClick={() => handleStartClass(liveClass)}
                                  title="Start Meeting"
                                  disabled={startingClassId === liveClass.id}
                                >
                                  {startingClassId === liveClass.id ? (
                                    <span className="spinner-border spinner-border-sm" style={{ width: '16px', height: '16px' }}></span>
                                  ) : (
                                    <Icon icon="mdi:play" width="16" height="16" />
                                  )}
                                </button>
                                <button
                                  className="btn btn-outline-secondary btn-sm border border-secondary"
                                  style={{ width: '36px', height: '36px' }}
                                  onClick={() => openEditModal(liveClass)}
                                  title="Edit Class"
                                >
                                  <Icon icon="mdi:pencil" width="16" height="16" />
                                </button>
                                <button
                                  className="btn btn-outline-danger btn-sm border border-danger"
                                  style={{ width: '36px', height: '36px' }}
                                  onClick={() => openDeleteModal(liveClass)}
                                  title="Delete Class"
                                >
                                  <Icon icon="mdi:delete" width="16" height="16" />
                                </button>
                              </>
                            )}
                          </div>
                        </td>
                      </tr>
                    )) : (
                      <tr>
                        <td colSpan="8" className="text-center" style={{ height: '400px', verticalAlign: 'middle' }}>
                          <NoData
                            title="No Live Classes Found"
                            description="Create your first live class to get started with Zoom meetings."
                          />
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Record per page and pagination */}
      <div className="row">
        <div className="col-md-6 d-flex align-items-center gap-3">
          <select
            className="form-select"
            style={{ width: 'auto' }}
            value={itemsPerPage}
            onChange={handleLimitChange}
          >
            <option value={10}>10 records per page</option>
            <option value={25}>25 records per page</option>
            <option value={50}>50 records per page</option>
          </select>
          <span className="text-muted">
            Total Records: {totalRecords}
          </span>
        </div>
        <div className="col-md-6 d-flex justify-content-end align-items-center gap-3">
          <button
            className="btn btn-primary"
            style={{ width: '150px' }}
            onClick={() => handlePageChange(currentPage - 1)}
            disabled={currentPage === 1}
          >
            Prev
          </button>
          <span>Page {currentPage} of {totalPages}</span>
          <button
            className="btn btn-primary"
            style={{ width: '150px' }}
            onClick={() => handlePageChange(currentPage + 1)}
            disabled={currentPage === totalPages}
          >
            Next
          </button>
        </div>
      </div>

      {/* Create Modal */}
      {showCreateModal && (
        <div className="modal show d-block" style={{ backgroundColor: 'rgba(0,0,0,0.5)' }}>
          <div className="modal-dialog modal-lg">
            <div className="modal-content">
              <div className="modal-header">
                <h5 className="modal-title">Create Live Class</h5>
                <button
                  type="button"
                  className="btn-close"
                  onClick={() => {
                    setShowCreateModal(false);
                    resetForm();
                  }}
                ></button>
              </div>
              <form onSubmit={handleCreateClass}>
                <div className="modal-body">
                  <div className="row">
                    <div className="col-md-12 mb-3">
                      <label className="form-label">Class Name *</label>
                      <input
                        type="text"
                        className={`form-control ${formErrors.live_class_name ? 'is-invalid' : ''}`}
                        name="live_class_name"
                        value={formData.live_class_name}
                        onChange={handleInputChange}
                        placeholder="Enter class name"
                      />
                      {formErrors.live_class_name && (
                        <div className="invalid-feedback">{formErrors.live_class_name}</div>
                      )}
                    </div>

                    <div className="col-md-12 mb-3">
                      <label className="form-label">Description</label>
                      <textarea
                        className="form-control"
                        name="live_class_description"
                        value={formData.live_class_description}
                        onChange={handleInputChange}
                        rows="3"
                        placeholder="Enter class description (optional)"
                      ></textarea>
                    </div>

                    <div className="col-md-12 mb-3">
                      <label className="form-label">Time Zone <span className="text-danger">*</span></label>
                      <div className="position-relative">
                        <div
                          className={`form-select d-flex align-items-center justify-content-between ${formErrors.time_zone ? 'is-invalid' : ''}`}
                          onClick={() => setIsTimeZoneDropdownOpen(!isTimeZoneDropdownOpen)}
                          style={{
                            borderRadius: '8px',
                            border: formErrors.time_zone ? '1px solid #dc3545' : '1px solid #e0e0e0',
                            padding: '12px 16px',
                            fontSize: '0.875rem',
                            backgroundColor: '#fff',
                            cursor: 'pointer'
                          }}
                        >
                          <span>{formData.time_zone ? timeZones.find(zone => zone.value === formData.time_zone)?.label : 'Select Time Zone'}</span>
                          <Icon icon={isTimeZoneDropdownOpen ? "eva:arrow-up-fill" : "eva:arrow-down-fill"} />
                        </div>
                        {isTimeZoneDropdownOpen && (
                          <div
                            className="position-absolute w-100 bg-white border rounded-3 mt-1 shadow-sm"
                            style={{
                              maxHeight: '300px',
                              overflowY: 'auto',
                              zIndex: 1000
                            }}
                          >
                            <div className="p-2 sticky-top bg-white">
                              <div className="position-relative">
                                <input
                                  type="text"
                                  className="form-control"
                                  placeholder="Search timezone..."
                                  value={timeZoneSearch}
                                  onChange={(e) => setTimeZoneSearch(e.target.value)}
                                  onClick={(e) => e.stopPropagation()}
                                  style={{
                                    paddingLeft: '35px',
                                    border: '1px solid #e0e0e0',
                                    borderRadius: '6px',
                                    fontSize: '0.875rem'
                                  }}
                                />
                                <Icon
                                  icon="eva:search-outline"
                                  style={{
                                    position: 'absolute',
                                    left: '12px',
                                    top: '50%',
                                    transform: 'translateY(-50%)',
                                    color: '#6c757d'
                                  }}
                                />
                              </div>
                            </div>
                            <div className="timezone-options">
                              {timeZones
                                .filter(zone =>
                                  zone.label.toLowerCase().includes(timeZoneSearch.toLowerCase()) ||
                                  zone.value.toLowerCase().includes(timeZoneSearch.toLowerCase())
                                )
                                .map((zone) => (
                                  <div
                                    key={zone.value}
                                    className="timezone-option"
                                    onClick={() => {
                                      setFormData(prev => ({ ...prev, time_zone: zone.value }));
                                      setIsTimeZoneDropdownOpen(false);
                                      setTimeZoneSearch('');
                                    }}
                                    style={{
                                      padding: '10px 16px',
                                      cursor: 'pointer',
                                      backgroundColor: formData.time_zone === zone.value ? '#f8f9fa' : 'transparent',
                                      transition: 'background-color 0.2s',
                                      fontSize: '0.875rem',
                                      color: '#333'
                                    }}
                                    onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#f8f9fa'}
                                    onMouseLeave={(e) => e.currentTarget.style.backgroundColor = formData.time_zone === zone.value ? '#f8f9fa' : 'transparent'}
                                  >
                                    {zone.label}
                                  </div>
                                ))
                              }
                            </div>
                          </div>
                        )}
                      </div>
                      {formErrors.time_zone && (
                        <div className="invalid-feedback">{formErrors.time_zone}</div>
                      )}
                    </div>

                    <div className="col-md-6 mb-3">
                      <label className="form-label">Date *</label>
                      <input
                        type="date"
                        className={`form-control ${formErrors.class_date ? 'is-invalid' : ''}`}
                        name="class_date"
                        value={formData.class_date}
                        onChange={handleInputChange}
                        min={new Date().toISOString().split('T')[0]}
                      />
                      {formErrors.class_date && (
                        <div className="invalid-feedback">{formErrors.class_date}</div>
                      )}
                    </div>

                    <div className="col-md-6 mb-3">
                      <label className="form-label">Start Time *</label>
                      <input
                        type="time"
                        className={`form-control ${formErrors.start_time ? 'is-invalid' : ''}`}
                        name="start_time"
                        value={formData.start_time}
                        onChange={handleInputChange}
                      />
                      {formErrors.start_time && (
                        <div className="invalid-feedback">{formErrors.start_time}</div>
                      )}
                    </div>

                    <div className="col-md-12 mb-3">
                      <label className="form-label">Duration (minutes) <span className="text-danger">*</span></label>
                      <input
                        type="number"
                        className={`form-control ${formErrors.duration ? 'is-invalid' : ''}`}
                        name="duration"
                        value={formData.duration}
                        onChange={handleInputChange}
                        placeholder="Enter duration in minutes"
                        min="1"
                        max="480"
                      />
                      {formErrors.duration && (
                        <div className="invalid-feedback">{formErrors.duration}</div>
                      )}
                      <div className="form-text text-muted">
                        Enter the duration in minutes (1-480 minutes allowed)
                      </div>
                    </div>


                  </div>
                </div>
                <div className="modal-footer">
                  <button
                    type="button"
                    className="btn btn-secondary"
                    onClick={() => {
                      setShowCreateModal(false);
                      resetForm();
                    }}
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="btn btn-primary"
                    disabled={submitting}
                  >
                    {submitting ? (
                      <>
                        <span className="spinner-border spinner-border-sm me-2"></span>
                        Creating...
                      </>
                    ) : (
                      'Create Live Class'
                    )}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}

      {/* Edit Modal */}
      {showEditModal && (
        <div className="modal show d-block" style={{ backgroundColor: 'rgba(0,0,0,0.5)' }}>
          <div className="modal-dialog modal-lg">
            <div className="modal-content">
              <div className="modal-header">
                <h5 className="modal-title">Edit Live Class</h5>
                <button
                  type="button"
                  className="btn-close"
                  onClick={() => {
                    setShowEditModal(false);
                    resetForm();
                    setSelectedClass(null);
                  }}
                ></button>
              </div>
              <form onSubmit={handleEditClass}>
                <div className="modal-body">
                  {selectedClass?.is_started ? (
                    <div className="alert alert-warning">
                      <Icon icon="mdi:information" className="me-2" />
                      Meeting has started. Only description can be edited.
                    </div>
                  ) : null}

                  <div className="row">
                    <div className="col-md-12 mb-3">
                      <label className="form-label">Class Name *</label>
                      <input
                        type="text"
                        className={`form-control ${formErrors.live_class_name ? 'is-invalid' : ''}`}
                        name="live_class_name"
                        value={formData.live_class_name}
                        onChange={handleInputChange}
                        disabled={selectedClass?.is_started}
                        placeholder="Enter class name"
                      />
                      {formErrors.live_class_name && (
                        <div className="invalid-feedback">{formErrors.live_class_name}</div>
                      )}
                    </div>

                    <div className="col-md-12 mb-3">
                      <label className="form-label">Description</label>
                      <textarea
                        className="form-control"
                        name="live_class_description"
                        value={formData.live_class_description}
                        onChange={handleInputChange}
                        rows="3"
                        placeholder="Enter class description (optional)"
                      ></textarea>
                    </div>

                    <div className="col-md-12 mb-3">
                      <label className="form-label">Time Zone <span className="text-danger">*</span></label>
                      <div className="position-relative">
                        <div
                          className={`form-select d-flex align-items-center justify-content-between ${formErrors.time_zone ? 'is-invalid' : ''}`}
                          onClick={() => setIsTimeZoneDropdownOpen(!isTimeZoneDropdownOpen)}
                          style={{
                            borderRadius: '8px',
                            border: formErrors.time_zone ? '1px solid #dc3545' : '1px solid #e0e0e0',
                            padding: '12px 16px',
                            fontSize: '0.875rem',
                            backgroundColor: '#fff',
                            cursor: 'pointer'
                          }}
                        >
                          <span>{formData.time_zone ? timeZones.find(zone => zone.value === formData.time_zone)?.label : 'Select Time Zone'}</span>
                          <Icon icon={isTimeZoneDropdownOpen ? "eva:arrow-up-fill" : "eva:arrow-down-fill"} />
                        </div>
                        {isTimeZoneDropdownOpen && (
                          <div
                            className="position-absolute w-100 bg-white border rounded-3 mt-1 shadow-sm"
                            style={{
                              maxHeight: '300px',
                              overflowY: 'auto',
                              zIndex: 1000
                            }}
                          >
                            <div className="p-2 sticky-top bg-white">
                              <div className="position-relative">
                                <input
                                  type="text"
                                  className="form-control"
                                  placeholder="Search timezone..."
                                  value={timeZoneSearch}
                                  onChange={(e) => setTimeZoneSearch(e.target.value)}
                                  onClick={(e) => e.stopPropagation()}
                                  style={{
                                    paddingLeft: '35px',
                                    border: '1px solid #e0e0e0',
                                    borderRadius: '6px',
                                    fontSize: '0.875rem'
                                  }}
                                />
                                <Icon
                                  icon="eva:search-outline"
                                  style={{
                                    position: 'absolute',
                                    left: '12px',
                                    top: '50%',
                                    transform: 'translateY(-50%)',
                                    color: '#6c757d'
                                  }}
                                />
                              </div>
                            </div>
                            <div className="timezone-options">
                              {timeZones
                                .filter(zone =>
                                  zone.label.toLowerCase().includes(timeZoneSearch.toLowerCase()) ||
                                  zone.value.toLowerCase().includes(timeZoneSearch.toLowerCase())
                                )
                                .map((zone) => (
                                  <div
                                    key={zone.value}
                                    className="timezone-option"
                                    onClick={() => {
                                      setFormData(prev => ({ ...prev, time_zone: zone.value }));
                                      setIsTimeZoneDropdownOpen(false);
                                      setTimeZoneSearch('');
                                    }}
                                    style={{
                                      padding: '10px 16px',
                                      cursor: 'pointer',
                                      backgroundColor: formData.time_zone === zone.value ? '#f8f9fa' : 'transparent',
                                      transition: 'background-color 0.2s',
                                      fontSize: '0.875rem',
                                      color: '#333'
                                    }}
                                    onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#f8f9fa'}
                                    onMouseLeave={(e) => e.currentTarget.style.backgroundColor = formData.time_zone === zone.value ? '#f8f9fa' : 'transparent'}
                                  >
                                    {zone.label}
                                  </div>
                                ))
                              }
                            </div>
                          </div>
                        )}
                      </div>
                      {formErrors.time_zone && (
                        <div className="invalid-feedback">{formErrors.time_zone}</div>
                      )}
                    </div>


                    {!selectedClass?.is_started && (
                      <>
                        <div className="col-md-6 mb-3">
                          <label className="form-label">Date *</label>
                          <input
                            type="date"
                            className={`form-control ${formErrors.class_date ? 'is-invalid' : ''}`}
                            name="class_date"
                            value={formData.class_date}
                            onChange={handleInputChange}
                            min={new Date().toISOString().split('T')[0]}
                          />
                          {formErrors.class_date && (
                            <div className="invalid-feedback">{formErrors.class_date}</div>
                          )}
                        </div>

                        <div className="col-md-6 mb-3">
                          <label className="form-label">Start Time *</label>
                          <input
                            type="time"
                            className={`form-control ${formErrors.start_time ? 'is-invalid' : ''}`}
                            name="start_time"
                            value={formData.start_time}
                            onChange={handleInputChange}
                          />
                          {formErrors.start_time && (
                            <div className="invalid-feedback">{formErrors.start_time}</div>
                          )}
                        </div>

                        <div className="col-md-12 mb-3">
                          <label className="form-label">Duration (minutes) <span className="text-danger">*</span></label>
                          <input
                            type="number"
                            className={`form-control ${formErrors.duration ? 'is-invalid' : ''}`}
                            name="duration"
                            value={formData.duration}
                            onChange={handleInputChange}
                            placeholder="Enter duration in minutes"
                            min="1"
                            max="480"
                          />
                          {formErrors.duration && (
                            <div className="invalid-feedback">{formErrors.duration}</div>
                          )}
                          <div className="form-text text-muted">
                            Enter the duration in minutes (1-480 minutes allowed)
                          </div>
                        </div>


                      </>
                    )}
                  </div>
                </div>
                <div className="modal-footer">
                  <button
                    type="button"
                    className="btn btn-secondary"
                    onClick={() => {
                      setShowEditModal(false);
                      resetForm();
                      setSelectedClass(null);
                    }}
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="btn btn-primary"
                    disabled={submitting}
                  >
                    {submitting ? (
                      <>
                        <span className="spinner-border spinner-border-sm me-2"></span>
                        Updating...
                      </>
                    ) : (
                      'Update Live Class'
                    )}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}

      {/* Delete Modal */}
      {showDeleteModal && (
        <div className="modal show d-block" style={{ backgroundColor: 'rgba(0,0,0,0.5)' }}>
          <div className="modal-dialog">
            <div className="modal-content">
              <div className="modal-header">
                <h5 className="modal-title">Delete Live Class</h5>
                <button
                  type="button"
                  className="btn-close"
                  onClick={() => {
                    setShowDeleteModal(false);
                    setSelectedClass(null);
                  }}
                ></button>
              </div>
              <div className="modal-body">
                <div className="text-center">
                  <Icon icon="mdi:alert-circle" className="text-danger mb-3" style={{ fontSize: '48px' }} />
                  <h6>Are you sure you want to delete this live class?</h6>
                  <p className="text-muted mb-0">
                    <strong>{selectedClass?.live_class_name}</strong>
                  </p>
                  <p className="text-muted small">
                    This action cannot be undone. The Zoom meeting will also be deleted.
                  </p>
                </div>
              </div>
              <div className="modal-footer">
                <button
                  type="button"
                  className="btn btn-secondary"
                  onClick={() => {
                    setShowDeleteModal(false);
                    setSelectedClass(null);
                  }}
                >
                  Cancel
                </button>
                <button
                  type="button"
                  className="btn btn-danger"
                  onClick={handleDeleteClass}
                  disabled={submitting}
                >
                  {submitting ? (
                    <>
                      <span className="spinner-border spinner-border-sm me-2"></span>
                      Deleting...
                    </>
                  ) : (
                    'Delete Live Class'
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default ClassroomLiveClasses;
