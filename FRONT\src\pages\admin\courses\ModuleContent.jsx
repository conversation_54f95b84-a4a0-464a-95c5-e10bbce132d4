import './ModuleContent.css';

import { EditVideoContent, changeContentstatus, createAssessmentAP<PERSON>, createDocument, createVideoAP<PERSON>, deleteContentById, editAssessment, editDocument, getCourseModuleContent, updateContentOrder } from '../../../services/adminService';
import React, { useEffect, useState } from 'react';
import { decodeData, encodeData } from '../../../utils/encodeAndEncode';
import { useNavigate, useParams } from 'react-router-dom';
import { Reorder } from 'framer-motion';
import { toast } from 'react-toastify';
import { Icon } from '@iconify/react';
import Loader from '../../../components/admin/Loader';
import NoData from "../../../components/common/NoData";
import { usePermissions } from '../../../context/PermissionsContext';
const REACT_APP_BITMOVIN_PLAYER_KEY = process.env.REACT_APP_BITMOVIN_PLAYER_KEY;


function ModuleContent() {
    const navigate = useNavigate();
    const { courseId, moduleId } = useParams();
    const decodedCourseId = decodeData(courseId);
    const decodedModuleId = decodeData(moduleId);
    const { permissions } = usePermissions();

    console.log('decodedCourseId-------', decodedCourseId);
    console.log('decodedModuleId-------', decodedModuleId);

    // Group all useState declarations at the top
    const [loading, setLoading] = useState(true);
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [contents, setContents] = useState([]);
    const [showDeleteModal, setShowDeleteModal] = useState(false);
    const [showAddModal, setShowAddModal] = useState(false);
    const [contentToDelete, setContentToDelete] = useState(null);
    const [statusLoading, setStatusLoading] = useState(null);
    const [searchQuery, setSearchQuery] = useState('');
    const [draggedItem, setDraggedItem] = useState(null);
    const [isReordering, setIsReordering] = useState(false);
    const [selectedContentType, setSelectedContentType] = useState('video');
    const [isEditMode, setIsEditMode] = useState(false);
    const [editingContent, setEditingContent] = useState(null);
    const [useVideoUrl, setUseVideoUrl] = useState(false);
    const [isDragging, setIsDragging] = useState(false);
    const [isDraggingContent, setIsDraggingContent] = useState(false);
    const [classroomName, setClassroomName] = useState(null);
    const [errors, setErrors] = useState({
        videoName: '',
        videoDescription: '',
        videoUrl: '',
        videoFile: '',
        documentName: '',
        documentFile: '',
        assessmentName: '',
        assessmentDuration: '',
        assessmentPassingPercentage: '',
        assessmentRewardPoints: '',
        surveyName: '',
        surveyRewardPoints: ''
    });

    // Upload progress modal states
    const [showUploadModal, setShowUploadModal] = useState(false);
    const [uploadStatus, setUploadStatus] = useState('uploading'); // 'uploading', 'success', 'error'
    const [uploadProgress, setUploadProgress] = useState(0);

    const [videoForm, setVideoForm] = useState({
        name: '',
        description: '',
        videoUrl: '',
        videoDuration: '',
        videoFile: null,
        videoPreview: null
    });

    const [documentForm, setDocumentForm] = useState({
        name: '',
        description: '',
        documentFile: null,
        documentPreview: null
    });

    const [assessmentForm, setAssessmentForm] = useState({
        name: '',
        duration: '',
        passingPercentage: '',
        rewardPoints: ''
    });

    const [surveyForm, setSurveyForm] = useState({
        name: '',
        rewardPoints: ''
    });

    const [contentSummary, setContentSummary] = useState({
        videos: 0,
        documents: 0,
        assessments: 0,
        surveys: 0,
        totalDuration: '00:00:00'
    });

    // Filter contents based on search query
    const filteredContents = contents.filter(content =>
        content.title.toLowerCase().includes(searchQuery.toLowerCase())
    );

    // Debug logging
    console.log('🔍 Contents:', contents.length, contents.map(c => ({ id: c.id, title: c.title })));
    console.log('🔍 Filtered Contents:', filteredContents.length, filteredContents.map(c => ({ id: c.id, title: c.title })));

    useEffect(() => {
        fetchModuleContent();
    }, []);

    useEffect(() => {
        if (showAddModal) {
            setErrors({
                videoName: '',
                videoDescription: '',
                videoUrl: '',
                videoFile: '',
                documentName: '',
                documentFile: '',
                assessmentName: '',
                assessmentDuration: '',
                assessmentPassingPercentage: '',
                assessmentRewardPoints: '',
                surveyName: '',
                surveyRewardPoints: ''
            });
        }
    }, [showAddModal]);

    const fetchModuleContent = async () => {
        try {
            setLoading(true);
            const response = await getCourseModuleContent({ module_id: decodedModuleId });
            console.log('Module Content Response -------', response);
            setClassroomName(response.course_name);
            
            if (response.success) {
                const formattedContents = response.data.response.map(item => ({
                    id: item.id,
                    type: item.type,
                    title: item.title,
                    description: item.type === 'video' ? (item.description || 'No description available') : '',
                    url: item.url || item.attachment_detail,
                    duration: item.duration || 'N/A',
                    date: new Date(item.createdAt).toLocaleDateString('en-GB', {
                        day: '2-digit',
                        month: '2-digit', 
                        year: 'numeric'
                    }),
                    status: item.is_active ? 'active' : 'inactive',
                    serial_no: item.serial_no,
                    // Video specific fields
                    comments: item.total_comments || 0,
                    rating: 4.5, // Default rating since not in API
                    totalRatings: 128, // Default since not in API
                    // Assessment specific fields
                    totalQuestions: item.total_questions || 0,
                    timeLimit: item.duration ? `${item.duration} minutes` : 'No time limit',
                    passingPercentage: item.pass_percentage || 0,
                    earnPoint: item.earn_point || 0,
                    // Document specific fields
                    fileSize: item.total_size_mb ? `${item.total_size_mb} MB` : 'Unknown size',
                    fileType: item.url ? item.url.split('.').pop().toUpperCase() : 'FILE',
                    // Survey specific fields
                    estimatedTime: item.duration && item.duration > 0 ? `${item.duration} minutes` : 'No time limit',
                }));
                
                // Calculate content summary
                const summary = formattedContents.reduce((acc, item) => {
                    switch (item.type) {
                        case 'video':
                            acc.videos++;
                            break;
                        case 'document':
                            acc.documents++;
                            break;
                        case 'assessment':
                            acc.assessments++;
                            break;
                        case 'survey':
                            acc.surveys++;
                            break;
                    }
                    return acc;
                }, {
                    videos: 0,
                    documents: 0,
                    assessments: 0,
                    surveys: 0,
                    totalDuration: '00:00:00'
                });

                setContentSummary(summary);
                // Sort by serial_no
                formattedContents.sort((a, b) => a.serial_no - b.serial_no);
                setContents(formattedContents);
            } else {
                setContents([]);
                setContentSummary({
                    videos: 0,
                    documents: 0,
                    assessments: 0,
                    surveys: 0,
                    totalDuration: '00:00:00'
                });
                toast.error(response.message || 'Failed to fetch module content', {
                    position: 'top-center',
                    autoClose: 3000
                });
            }
        } catch (error) {
            console.log('error-------', error);
            setContents([]);
            setContentSummary({
                videos: 0,
                documents: 0,
                assessments: 0,
                surveys: 0,
                totalDuration: '00:00:00'
            });
            toast.error('Error fetching module content: ' + error.message, {
                position: 'top-center',
                autoClose: 3000
            });
        } finally {
            setLoading(false);
        }
    };

    // Framer Motion drag and drop handlers
    const handleReorderContents = async (newOrder) => {
        console.log('🔄 DRAG HANDLER CALLED!');
        console.log('🔄 Reordering contents:', newOrder.map(item => item.title));
        console.log('🔄 Original order:', contents.map(item => item.title));
        console.log('🔄 New order:', newOrder.map(item => item.title));

        // Check if order actually changed
        const orderChanged = newOrder.some((item, index) => item.id !== contents[index]?.id);
        if (!orderChanged) {
            console.log('🔄 No order change detected, skipping API call');
            return;
        }

        // Update local state immediately for smooth UI
        setContents(newOrder);
        setIsReordering(true);

        try {
            // Prepare ordered items for backend
            const orderedItems = newOrder.map(item => ({
                id: item.id,
                table_name: item.type === 'video' ? 'videos' :
                           item.type === 'document' ? 'documents' :
                           item.type === 'assessment' ? 'assessments' :
                           item.type === 'survey' ? 'assessments' : 'videos'
            }));

            console.log('📡 Sending reorder request to backend:', orderedItems);

            const response = await updateContentOrder({
                module_id: decodedModuleId,
                ordered_items: orderedItems
            });

            if (response?.success) {
                toast.success('Content reordered successfully!', {
                    position: 'top-center',
                    autoClose: 3000
                });
            } else {
                throw new Error(response?.message || 'Failed to reorder content');
            }
        } catch (error) {
            console.error('❌ Error reordering content:', error);
            toast.error('Failed to save order. Please try again.', {
                position: 'top-center',
                autoClose: 3000
            });
            // Revert to original order by refetching
            fetchModuleContent();
        } finally {
            setIsReordering(false);
        }
    };

    // Separate handlers for file upload drag and drop
    const handleFileDragOver = (e) => {
        e.preventDefault();
        e.stopPropagation();
        setIsDragging(true);
    };

    const handleFileDragLeave = (e) => {
        e.preventDefault();
        e.stopPropagation();
        setIsDragging(false);
    };

    const handleFileDrop = (e) => {
        e.preventDefault();
        e.stopPropagation();
        setIsDragging(false);
        
        const file = e.dataTransfer.files[0];
        if (selectedContentType === 'video') {
            handleVideoFile(file);
        } else if (selectedContentType === 'document') {
            handleDocumentFile(file);
        }
    };

    const handleContentClick = (content) => {
        if (isDraggingContent) return; // Prevent opening content while dragging

        // Encode all values once
        const encodedContentId = encodeData(content.id);
        const encodedCourseId = encodeData(decodedCourseId);
        const encodedModuleId = encodeData(decodedModuleId);
    
        // Predefined routes
        const videoUrl = `/admin/courses/module/${encodedCourseId}/content/${encodedModuleId}/video/${encodedContentId}`;
        const documentUrl = `/admin/courses/module/${encodedCourseId}/content/${encodedModuleId}/document/${encodedContentId}`;
        const assessmentUrl = `/admin/courses/module/${encodedCourseId}/content/${encodedModuleId}/quiz/${encodedContentId}`;
        const surveyUrl = `/admin/courses/module/${encodedCourseId}/content/${encodedModuleId}/survey/${encodedContentId}`;
    
        // Navigate based on content type
        switch (content.type) {
            case 'video':
                navigate(videoUrl);
                break;
            case 'document':
                navigate(documentUrl, {
                    state: {
                        documentName: content.title,
                        pdfUrl: content.url
                    }
                });
                break;
            case 'assessment':
                navigate(assessmentUrl);
                break;
            case 'survey':
                navigate(surveyUrl);
                break;
            default:
                break;
        }
    };
    

    const handleVideoFormChange = (e) => {
        const { name, value, type, files } = e.target;
        if (type === 'file' && files[0]) {
            handleVideoFile(files[0]);
        } else {
            setVideoForm(prev => ({
                ...prev,
                [name]: value
            }));
        }
    };

    const handleVideoFile = (file) => {
        if (file && file.type.startsWith('video/')) {
            if (file.size > 150 * 1024 * 1024) { // 150MB
                toast.error('File size should not exceed 150MB', {
                    position: 'top-center',
                    autoClose: 3000
                });
                return;
            }

            // Create video element to get duration
            const video = document.createElement('video');
            video.preload = 'metadata';

            video.onloadedmetadata = () => {
                // Convert duration to minutes:seconds format
                const minutes = Math.floor(video.duration / 60);
                const seconds = Math.floor(video.duration % 60);
                const formattedDuration = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
                
                setVideoForm(prev => ({
                    ...prev,
                    videoFile: file,
                    videoPreview: URL.createObjectURL(file),
                    videoDuration: formattedDuration
                }));
            };

            video.src = URL.createObjectURL(file);
        } else {
            alert('Please upload a valid video file');
        }
    };

    const handleRemoveVideo = () => {
        if (videoForm.videoPreview) {
            URL.revokeObjectURL(videoForm.videoPreview);
        }
        setVideoForm(prev => ({
            ...prev,
            videoFile: null,
            videoPreview: null,
            videoDuration: ''
        }));
    };

    const handleDocumentFormChange = (e) => {
        const { name, value, type, files } = e.target;
        if (type === 'file' && files[0]) {
            handleDocumentFile(files[0]);
        } else {
            setDocumentForm(prev => ({
                ...prev,
                [name]: value
            }));
        }
    };

    const handleDocumentFile = (file) => {
        if (file && file.type === 'application/pdf') {
            if (file.size > 10 * 1024 * 1024) { // 10MB
                toast.error('File size should not exceed 10MB', {
                    position: 'top-center',
                    autoClose: 3000
                });
                return;
            }
            setDocumentForm(prev => ({
                ...prev,
                documentFile: file,
                documentPreview: URL.createObjectURL(file)
            }));
        } else {
            toast.error('Please upload a PDF file only', {
                position: 'top-center',
                autoClose: 3000
            });
        }
    };

    const handleRemoveDocument = () => {
        if (documentForm.documentPreview) {
            URL.revokeObjectURL(documentForm.documentPreview);
        }
        setDocumentForm(prev => ({
            ...prev,
            documentFile: null,
            documentPreview: null
        }));
    };

    const handleAssessmentFormChange = (e) => {
        const { name, value } = e.target;
        setAssessmentForm(prev => ({
            ...prev,
            [name]: value
        }));
    };

    const handleSurveyFormChange = (e) => {
        const { name, value } = e.target;
        setSurveyForm(prev => ({
            ...prev,
            [name]: value
        }));
    };

    const validateForm = () => {
        const newErrors = { ...errors };
        let isValid = true;

        // Reset all errors first
        Object.keys(newErrors).forEach(key => newErrors[key] = '');

        switch (selectedContentType) {
            case 'video':
                if (!videoForm.name.trim()) {
                    newErrors.videoName = 'Video Name is required';
                    isValid = false;
                } else if (videoForm.name.trim().length > 100) {
                    newErrors.videoName = 'Video Name cannot exceed 100 characters';
                    isValid = false;
                }

                if (!videoForm.description.trim()) {
                    newErrors.videoDescription = 'Video Description is required';
                    isValid = false;
                } else if (videoForm.description.trim().length > 500) {
                    newErrors.videoDescription = 'Video Description cannot exceed 500 characters';
                    isValid = false;
                }

                if (useVideoUrl) {
                    if (!videoForm.videoUrl.trim()) {
                        newErrors.videoUrl = 'Video URL is required';
                        isValid = false;
                    }
                } else if (!videoForm.videoFile && !isEditMode) {
                    newErrors.videoFile = 'Please upload a video file';
                    isValid = false;
                }
                break;

            case 'document':
                if (!documentForm.name.trim()) {
                    newErrors.documentName = 'Document Name is required';
                    isValid = false;
                } else if (documentForm.name.trim().length > 100) {
                    newErrors.documentName = 'Document Name cannot exceed 100 characters';
                    isValid = false;
                }

                if (!documentForm.documentFile && !isEditMode) {
                    newErrors.documentFile = 'Please upload a document file';
                    isValid = false;
                }
                break;

            case 'assessment':
                if (!assessmentForm.name.trim()) {
                    newErrors.assessmentName = 'Assessment Name is required';
                    isValid = false;
                } else if (assessmentForm.name.trim().length > 100) {
                    newErrors.assessmentName = 'Assessment Name cannot exceed 100 characters';
                    isValid = false;
                }

                if (!assessmentForm.duration) {
                    newErrors.assessmentDuration = 'Duration is required';
                    isValid = false;
                } else if (parseInt(assessmentForm.duration) <= 0) {
                    newErrors.assessmentDuration = 'Duration must be greater than 0';
                    isValid = false;
                }

                if (!assessmentForm.passingPercentage) {
                    newErrors.assessmentPassingPercentage = 'Passing Percentage is required';
                    isValid = false;
                } else if (parseInt(assessmentForm.passingPercentage) < 0 || parseInt(assessmentForm.passingPercentage) > 100) {
                    newErrors.assessmentPassingPercentage = 'Passing Percentage must be between 0 and 100';
                    isValid = false;
                }
                break;

            case 'survey':
                if (!surveyForm.name.trim()) {
                    newErrors.surveyName = 'Survey Name is required';
                    isValid = false;
                } else if (surveyForm.name.trim().length > 100) {
                    newErrors.surveyName = 'Survey Name cannot exceed 100 characters';
                    isValid = false;
                }
                break;
        }

        setErrors(newErrors);
        return isValid;
    };

    const handleAddContent = async () => {
        if (!permissions.course_module_content_management_create) {
            toast.warning("You don't have permission to create content");
            return;
        }

        if (!validateForm()) {
            return;
        }
        try {
            setIsSubmitting(true);

            if (selectedContentType === 'video') {
                // Show upload modal for video uploads
                setShowUploadModal(true);
                setUploadStatus('uploading');
                setUploadProgress(0);
                if (!videoForm.name) {
                    toast.error('Please enter video name', {
                        position: 'top-center',
                        autoClose: 3000
                    });
                    setIsSubmitting(false);
                    return;
                }

                if (useVideoUrl && !videoForm.videoUrl) {
                    toast.error('Please enter video URL', {
                        position: 'top-center',
                        autoClose: 3000
                    });
                    setIsSubmitting(false);
                    return;
                }

                if (!useVideoUrl && !videoForm.videoFile) {
                    toast.error('Please upload a video file', {
                        position: 'top-center',
                        autoClose: 3000
                    });
                    setIsSubmitting(false);
                    return;
                }

                // Additional validation for file size
                if (!useVideoUrl && videoForm.videoFile && videoForm.videoFile.size > 150 * 1024 * 1024) {
                    toast.error('Video file size exceeds 150MB limit', {
                        position: 'top-center',
                        autoClose: 3000
                    });
                    setIsSubmitting(false);
                    return;
                }

                const videoData = new FormData();
                videoData.append("video_name", videoForm.name);
                videoData.append("video_desc", videoForm.description);
                videoData.append("module_id", decodedModuleId);
                videoData.append("course_id", decodedCourseId);
                videoData.append("duration", videoForm.videoDuration || '0');
                videoData.append("total_content", contents.length);
                videoData.append("upload_view_type", useVideoUrl ? 'src' : 'uploaded');

                if (useVideoUrl) {
                    videoData.append("src", videoForm.videoUrl);
                } else {
                    videoData.append("video", videoForm.videoFile);
                    videoData.append("attachment_file", videoForm.videoFile);
                }

                console.log("Sending video data:", {
                    name: videoForm.name,
                    description: videoForm.description,
                    duration: videoForm.videoDuration,
                    upload_type: useVideoUrl ? 'src' : 'uploaded',
                    url: videoForm.videoUrl,
                    fileSize: videoForm.videoFile ? `${(videoForm.videoFile.size / (1024 * 1024)).toFixed(2)}MB` : 'No file'
                });

                // Debug FormData contents
                console.log("FormData contents:");
                for (const [key, value] of videoData.entries()) {
                    if (value instanceof File) {
                        console.log(`${key}: File - ${value.name} (${(value.size / (1024 * 1024)).toFixed(2)}MB)`);
                    } else {
                        console.log(`${key}: ${value}`);
                    }
                }

                // Simulate upload progress
                const progressInterval = setInterval(() => {
                    setUploadProgress(prev => {
                        if (prev >= 90) {
                            clearInterval(progressInterval);
                            return 90;
                        }
                        return prev + 10;
                    });
                }, 1000);

                let response;
                if (isEditMode) {
                    // For edit mode, add the video ID
                    videoData.append("video_id", editingContent.id);
                    response = await EditVideoContent(videoData);
                } else {
                    response = await createVideoAPI(videoData);
                }

                // Clear the progress interval
                clearInterval(progressInterval);

                if (response.success) {
                    // Set upload status to success
                    setUploadStatus('success');
                    setUploadProgress(100);

                    toast.success(`Video ${isEditMode ? 'updated' : 'created'} successfully`, {
                        position: 'top-center',
                        autoClose: 3000
                    });

                    // Close the upload modal after a short delay
                    setTimeout(() => {
                        setShowUploadModal(false);
                        fetchModuleContent(); // Refresh the content list
                    }, 2000);
                } else {
                    setUploadStatus('error');
                    setTimeout(() => {
                        setShowUploadModal(false);
                    }, 2000);

                    toast.error(response.message || `Failed to ${isEditMode ? 'update' : 'create'} video`, {
                        position: 'top-center',
                        autoClose: 3000
                    });
                }
            } else if (selectedContentType === 'assessment' || selectedContentType === 'survey') {
                const payload = {
                    assessment_name: selectedContentType === 'assessment' ? assessmentForm.name : surveyForm.name,
                    assessment_id: editingContent?.id,
                    course_id: decodedCourseId,
                    assessment_type: selectedContentType,
                    module_id: decodedModuleId,
                    duration: selectedContentType === 'assessment' ? (parseInt(assessmentForm.duration) || 0) : 0,
                    earn_point: selectedContentType === 'assessment' ?
                        (parseInt(assessmentForm.rewardPoints) || 0) :
                        (parseInt(surveyForm.rewardPoints) || 0),
                    earn_point_applicable_after: 0,
                    pass_percentage: selectedContentType === 'assessment' ? (parseInt(assessmentForm.passingPercentage) || 0) : 0,
                    total_content: contents.length
                };

                console.log('payload-------', payload);

                let response;
                if (isEditMode) {
                    // For edit mode, add the assessment ID
                    payload.assessment_id = editingContent.id;
                    response = await editAssessment(payload);
                } else {
                    response = await createAssessmentAPI(payload);
                }

                if (response.success) {
                    toast.success(`${selectedContentType === 'assessment' ? 'Assessment' : 'Survey'} ${isEditMode ? 'updated' : 'created'} successfully`, {
                        position: 'top-center',
                        autoClose: 3000
                    });
                    fetchModuleContent(); // Refresh the content list
                } else {
                    toast.error(response.message || `Failed to ${isEditMode ? 'update' : 'create'} ${selectedContentType}`, {
                        position: 'top-center',
                        autoClose: 3000
                    });
                }
            } else if (selectedContentType === 'document') {
                // Validation: For create mode, file is required. For edit mode, file is optional
                if (!isEditMode && !documentForm.documentFile) {
                    toast.error('Please upload a document file', {
                        position: 'top-center',
                        autoClose: 3000
                    });
                    setIsSubmitting(false);
                    return;
                }

                // Validation: Document name is always required
                if (!documentForm.name || documentForm.name.trim() === '') {
                    toast.error('Please enter document name', {
                        position: 'top-center',
                        autoClose: 3000
                    });
                    setIsSubmitting(false);
                    return;
                }

                const formData = new FormData();
                let response;

                if (isEditMode) {
                    // For edit mode, use the expected field names for editDocument API
                    formData.append("doc_id", editingContent.id);
                    formData.append("doc_name", documentForm.name);

                    if (documentForm.documentFile) {
                        formData.append("doc_url", documentForm.documentFile);
                        formData.append("file_changed", "true");
                    } else {
                        // No new file uploaded, keep existing file
                        formData.append("doc_url", editingContent.url || '');
                        formData.append("file_changed", "false");
                    }

                    // Log form data contents for debugging
                    console.log('Edit Document Form data contents:');
                    for (const [key, value] of formData.entries()) {
                        console.log(key, typeof value === 'object' ? 'File object' : value);
                    }

                    response = await editDocument(formData);
                } else {
                    // For create mode, use the expected field names for createDocument API
                    formData.append("title", documentForm.name);
                    formData.append("attachment_file", documentForm.documentFile);
                    formData.append("module_id", decodedModuleId);
                    formData.append("course_id", decodedCourseId);
                    formData.append("total_content", contents.length);

                    response = await createDocument(formData);
                }

                if (response.success) {
                    toast.success(`Document ${isEditMode ? 'updated' : 'uploaded'} successfully`, {
                        position: 'top-center',
                        autoClose: 3000
                    });
                    fetchModuleContent(); // Refresh the content list
                } else {
                    toast.error(response.message || `Failed to ${isEditMode ? 'update' : 'upload'} document`, {
                        position: 'top-center',
                        autoClose: 3000
                    });
                }
            }
            // Reset forms and close modal
            setShowAddModal(false);
            setIsEditMode(false);
            setEditingContent(null);
            setVideoForm({
                name: '',
                description: '',
                videoUrl: '',
                videoDuration: '',
                videoFile: null,
                videoPreview: null
            });
            setDocumentForm({
                name: '',
                description: '',
                documentFile: null,
                documentPreview: null
            });
            setAssessmentForm({
                name: '',
                duration: '',
                passingPercentage: '',
                rewardPoints: ''
            });
            setSurveyForm({
                name: '',
                rewardPoints: ''
            });
            setUseVideoUrl(false);
        } catch (error) {
            console.error('Error creating content:', error);

            // Handle upload modal for video uploads
            if (selectedContentType === 'video') {
                setUploadStatus('error');
                setTimeout(() => {
                    setShowUploadModal(false);
                }, 2000);
            }

            // More specific error handling
            let errorMessage = 'Error creating content';
            if (error.response) {
                // Server responded with error status
                console.error('Server error response:', error.response.data);
                if (error.response.status === 413) {
                    errorMessage = 'File size too large. Please use a smaller video file (max 150MB).';
                } else if (error.response.status === 500) {
                    errorMessage = 'Server error occurred. Please check your file and try again.';
                } else if (error.response.data?.message) {
                    errorMessage = error.response.data.message;
                } else if (error.response.data?.data?.error_msg) {
                    errorMessage = error.response.data.data.error_msg;
                }
            } else if (error.request) {
                // Request was made but no response received
                console.error('No response received:', error.request);
                errorMessage = 'Network error. Please check your connection and try again.';
            } else {
                // Something else happened
                errorMessage = error.message || 'Unknown error occurred';
            }

            toast.error(errorMessage, {
                position: 'top-center',
                autoClose: 5000
            });
        } finally {
            setIsSubmitting(false);
        }
    };

    const handleEditClick = (e, content) => {
        e.stopPropagation();
        if (!permissions.course_module_content_management_edit) {
            toast.warning("You don't have permission to edit content");
            return;
        }
        setIsEditMode(true);
        setEditingContent(content);
        setSelectedContentType(content.type);

        // Pre-populate form data based on content type
        if (content.type === 'video') {
            setVideoForm({
                name: content.title,
                description: content.description,
                videoUrl: content.url || '',
                videoDuration: content.duration || '',
                videoFile: null,
                videoPreview: null
            });
            setUseVideoUrl(!!content.url);
        } else if (content.type === 'document') {
            setDocumentForm({
                name: content.title,
                description: content.description,
                documentFile: null,
                documentPreview: null
            });
        } else if (content.type === 'assessment') {
            setAssessmentForm({
                name: content.title,
                duration: content.timeLimit ? content.timeLimit.replace(' minutes', '') : '',
                passingPercentage: content.passingPercentage || '',
                rewardPoints: content.earnPoint || ''
            });
        } else if (content.type === 'survey') {
            setSurveyForm({
                name: content.title,
                rewardPoints: content.earnPoint || ''
            });
        }

        setShowAddModal(true);
    };

    const handleDeleteClick = (e, content) => {
        e.stopPropagation();
        if (!permissions.course_module_content_management_delete) {
            toast.warning("You don't have permission to delete content");
            return;
        }
        setContentToDelete(content);
        setShowDeleteModal(true);
    };

    const handleDeleteConfirm = async () => {
        try {
            setIsSubmitting(true);
            const response = await deleteContentById({
                content_id: contentToDelete.id,
                type: contentToDelete.type.toLowerCase()
            });

            if (response.success) {
                toast.success('Content deleted successfully', {
                    position: 'top-center',
                    autoClose: 3000
                });
                fetchModuleContent(); // Refresh the content list
            } else {
                toast.error(response.message || 'Failed to delete content', {
                    position: 'top-center',
                    autoClose: 3000
                });
            }
        } catch (error) {
            console.error('Error deleting content:', error);
            toast.error('Error deleting content: ' + error.message, {
                position: 'top-center',
                autoClose: 3000
            });
        } finally {
            setIsSubmitting(false);
            setShowDeleteModal(false);
            setContentToDelete(null);
        }
    };

    const handleStatusChange = async (e, content) => {
        e.stopPropagation();
        if (!permissions.course_module_content_management_status) {
            toast.warning("You don't have permission to change content status");
            return;
        }
        try {
            setStatusLoading(content.id); // Set loading state for this specific content

            const params = {
                module_id: decodedModuleId,
                content_id: content.id
            };

            const data = {
                is_active: content.status === 'active' ? false : true,
                content_type: content.type.toLowerCase()
            };

            const response = await changeContentstatus(params, data);

            if (response.success) {
                // Update local state instead of reloading
                setContents(prevContents => 
                    prevContents.map(item => 
                        item.id === content.id 
                            ? { ...item, status: content.status === 'active' ? 'inactive' : 'active' }
                            : item
                    )
                );

                toast.success(`Content ${content.status === 'active' ? 'deactivated' : 'activated'} successfully`, {
                    position: 'top-center',
                    autoClose: 3000
                });
            } else {
                toast.error(response.message || 'Failed to update content status', {
                    position: 'top-center',
                    autoClose: 3000
                });
            }
        } catch (error) {
            console.error('Error updating content status:', error);
            toast.error('Error updating content status: ' + error.message, {
                position: 'top-center',
                autoClose: 3000
            });
        } finally {
            setStatusLoading(null); // Clear loading state
        }
    };



    const resetAllForms = () => {
        setVideoForm({
            name: '',
            description: '',
            videoUrl: '',
            videoDuration: '',
            videoFile: null,
            videoPreview: null
        });
        setDocumentForm({
            name: '',
            description: '',
            documentFile: null,
            documentPreview: null
        });
        setAssessmentForm({
            name: '',
            duration: '',
            passingPercentage: '',
            rewardPoints: ''
        });
        setSurveyForm({
            name: '',
            rewardPoints: ''
        });
        setSelectedContentType('video');
        setUseVideoUrl(false);
        setErrors({
            videoName: '',
            videoDescription: '',
            videoUrl: '',
            videoFile: '',
            documentName: '',
            documentFile: '',
            assessmentName: '',
            assessmentDuration: '',
            assessmentPassingPercentage: '',
            assessmentRewardPoints: '',
            surveyName: '',
            surveyRewardPoints: ''
        });
    };

    const handleCloseModal = () => {
        setShowAddModal(false);
        setIsEditMode(false);
        setEditingContent(null);
        resetAllForms();
    };

    const renderVideoForm = () => (
        <>
            <div className="mb-3">
                <label className="form-label">Video Name *</label>
                <input
                    type="text"
                    className={`form-control ${errors.videoName ? 'is-invalid' : ''}`}
                    name="name"
                    value={videoForm.name}
                    onChange={(e) => {
                        handleVideoFormChange(e);
                        if (errors.videoName) {
                            setErrors({...errors, videoName: ''});
                        }
                    }}
                    placeholder="Enter video name"
                    maxLength={100}
                />
                {errors.videoName ? (
                    <div className="invalid-feedback">{errors.videoName}</div>
                ) : (
                    <small className="text-muted">Maximum 100 characters allowed ({100 - videoForm.name.length} remaining)</small>
                )}
            </div>

            <div className="mb-3">
                <label className="form-label">Video Description *</label>
                <textarea
                    className={`form-control ${errors.videoDescription ? 'is-invalid' : ''}`}
                    name="description"
                    value={videoForm.description}
                    onChange={(e) => {
                        handleVideoFormChange(e);
                        if (errors.videoDescription) {
                            setErrors({...errors, videoDescription: ''});
                        }
                    }}
                    rows="3"
                    placeholder="Enter video description"
                    maxLength={500}
                />
                {errors.videoDescription ? (
                    <div className="invalid-feedback">{errors.videoDescription}</div>
                ) : (
                    <small className="text-muted">Maximum 500 characters allowed ({500 - videoForm.description.length} remaining)</small>
                )}
            </div>

            <div className="mb-3">
                <div className="form-check">
                    <input
                        type="checkbox"
                        className="form-check-input"
                        id="useVideoUrl"
                        checked={useVideoUrl}
                        onChange={(e) => {
                            setUseVideoUrl(e.target.checked);
                            handleRemoveVideo();
                        }}
                    />
                    <label className="form-check-label" htmlFor="useVideoUrl">
                        Use Video URL instead of upload
                    </label>
                </div>
            </div>

            {useVideoUrl ? (
                    <div className="mb-3">
                    <label className="form-label">Video URL *</label>
                        <input
                            type="url"
                        className={`form-control ${errors.videoUrl ? 'is-invalid' : ''}`}
                            name="videoUrl"
                            value={videoForm.videoUrl}
                        onChange={(e) => {
                            handleVideoFormChange(e);
                            if (errors.videoUrl) {
                                setErrors({...errors, videoUrl: ''});
                            }
                        }}
                            placeholder="Enter video URL"
                        />
                    {errors.videoUrl && <div className="invalid-feedback">{errors.videoUrl}</div>}
                    </div>
            ) : (
                    <div className="mb-3">
                    <label className="form-label">Upload Video *</label>
                    {errors.videoFile && !videoForm.videoFile && (
                        <div className="alert alert-danger py-2">{errors.videoFile}</div>
                    )}
                        {!videoForm.videoPreview ? (
                            <div
                                className={`upload-area ${isDragging ? 'dragging' : ''}`}
                                onDragOver={handleFileDragOver}
                                onDragLeave={handleFileDragLeave}
                                onDrop={handleFileDrop}
                            >
                                <input
                                    type="file"
                                    accept="video/*"
                                    className="d-none"
                                    onChange={(e) => handleVideoFile(e.target.files[0])}
                                    id="videoUpload"
                                />
                                <label htmlFor="videoUpload" className="mb-0 w-100 h-100 d-flex flex-column align-items-center justify-content-center">
                                    <Icon icon="fluent:document-arrow-up-24-regular" width="48" height="48" className="mb-2" />
                                    <span>Drag & Drop or Click to Upload Video</span>
                                    <small className="text-muted">Maximum file size: 150MB</small>
                                </label>
                            </div>
                        ) : (
                            <div className="video-preview">
                                <video
                                    src={videoForm.videoPreview}
                                    controls
                                    className="w-100"
                                    style={{ maxHeight: '200px' }}
                                >
                                    Your browser does not support the video tag.
                                </video>
                                <button
                                    type="button"
                                    className="btn btn-outline-danger mt-2"
                                    onClick={handleRemoveVideo}
                                >
                                    Remove Video
                                </button>
                            </div>
                        )}
                    </div>
            )}
                    <div className="mb-3">
                        <label className="form-label">Video Duration</label>
                        <input
                            type="text"
                            className="form-control"
                            name="videoDuration"
                            value={videoForm.videoDuration}
                            onChange={handleVideoFormChange}
                            disabled={!useVideoUrl}
                            placeholder={useVideoUrl ? "Enter duration (e.g., 10:30)" : "Duration will be detected automatically"}
                        />
                        {useVideoUrl && (
                            <small className="text-muted">
                                Enter duration in MM:SS format (e.g., 05:30)
                            </small>
                        )}
                    </div>
        </>
    );

    const renderDocumentForm = () => (
        <>
            <div className="mb-3">
                <label className="form-label">Document Name *</label>
                <input
                    type="text"
                    className={`form-control ${errors.documentName ? 'is-invalid' : ''}`}
                    name="name"
                    value={documentForm.name}
                    onChange={(e) => {
                        handleDocumentFormChange(e);
                        if (errors.documentName) {
                            setErrors({...errors, documentName: ''});
                        }
                    }}
                    placeholder="Enter document name"
                    maxLength={100}
                />
                {errors.documentName ? (
                    <div className="invalid-feedback">{errors.documentName}</div>
                ) : (
                    <small className="text-muted">Maximum 100 characters allowed ({100 - documentForm.name.length} remaining)</small>
                )}
            </div>

            <div className="mb-3">
                <label className="form-label">Upload Document (PDF only) *</label>
                {errors.documentFile && !documentForm.documentFile && (
                    <div className="alert alert-danger py-2">{errors.documentFile}</div>
                )}
                <div
                    className={`upload-area ${isDragging ? 'dragging' : ''}`}
                    onDragOver={handleFileDragOver}
                    onDragLeave={handleFileDragLeave}
                    onDrop={handleFileDrop}
                    onClick={() => document.getElementById('documentUpload').click()}
                >
                    <input
                        type="file"
                        id="documentUpload"
                        className="d-none"
                        onChange={handleDocumentFormChange}
                        accept=".pdf"
                    />
                    <div className="text-center">
                        <Icon icon="fluent:document-pdf-24-regular" className="text-primary mb-2" width="48" height="48" />
                        <div className="upload-text">Upload New PDF Document</div>
                        <div className="upload-hint">Maximum file size: 10MB</div>
                    </div>
                </div>
            </div>

            {/* Document Preview Section */}
            {(documentForm.documentPreview || editingContent?.url) && (
                <div className="mb-3">
                    <label className="form-label">Current Document Preview</label>
                    <div className="card">
                        <div className="card-body p-2">
                            {documentForm.documentPreview && (
                                <div>
                                        <iframe
                                            src={documentForm.documentPreview}
                                            style={{ width: '100%', height: '400px', border: 'none' }}
                                            title="Document Preview"
                                        />
                                    <div className="d-flex justify-content-end mt-2">
                                        <button
                                            className="btn btn-link text-danger"
                                            onClick={handleRemoveDocument}
                                            type="button"
                                        >
                                            Remove Document
                                        </button>
                                    </div>
                                </div>
                            )}
                            
                            {editingContent?.url && !documentForm.documentPreview && (
                                <div>
                                    <iframe
                                        src={editingContent.url}
                                        style={{ width: '100%', height: '400px', border: 'none' }}
                                        title="Document Preview"
                                    />
                                    <div className="d-flex justify-content-end mt-2">
                                        <a 
                                            href={editingContent.url}
                                            target="_blank"
                                            rel="noopener noreferrer"
                                            className="btn btn-link text-primary"
                                            onClick={(e) => e.stopPropagation()}
                                        >
                                            Open in New Tab
                                        </a>
                                    </div>
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            )}
        </>
    );

    const renderAssessmentForm = () => (
        <>
            <div className="mb-3">
                <label className="form-label">Assessment Name *</label>
                <input
                    type="text"
                    className={`form-control ${errors.assessmentName ? 'is-invalid' : ''}`}
                    name="name"
                    value={assessmentForm.name}
                    onChange={(e) => {
                        handleAssessmentFormChange(e);
                        if (errors.assessmentName) {
                            setErrors({...errors, assessmentName: ''});
                        }
                    }}
                    placeholder="Enter assessment name"
                    maxLength={100}
                />
                {errors.assessmentName ? (
                    <div className="invalid-feedback">{errors.assessmentName}</div>
                ) : (
                    <small className="text-muted">Maximum 100 characters allowed ({100 - assessmentForm.name.length} remaining)</small>
                )}
            </div>

            <div className="mb-3">
                <label className="form-label">Duration (in minutes)</label>
                <input
                    type="number"
                    className={`form-control ${errors.assessmentDuration ? 'is-invalid' : ''}`}
                    name="duration"
                    value={assessmentForm.duration}
                    onChange={(e) => {
                        handleAssessmentFormChange(e);
                        if (errors.assessmentDuration) {
                            setErrors({...errors, assessmentDuration: ''});
                        }
                    }}
                    placeholder="Enter duration in minutes"
                    min="1"
                />
                {errors.assessmentDuration && <div className="invalid-feedback">{errors.assessmentDuration}</div>}
            </div>

            <div className="mb-3">
                <label className="form-label">Passing Percentage</label>
                <div className="d-flex align-items-center gap-2">
                    <div style={{ flex: 1, position: 'relative' }}>
                        <input
                            type="number"
                            className={`form-control ${errors.assessmentPassingPercentage ? 'is-invalid' : ''}`}
                            name="passingPercentage"
                            value={assessmentForm.passingPercentage}
                            onChange={(e) => {
                                handleAssessmentFormChange(e);
                                if (errors.assessmentPassingPercentage) {
                                    setErrors({...errors, assessmentPassingPercentage: ''});
                                }
                            }}
                            placeholder="Enter passing percentage"
                            min="0"
                            max="100"
                        />
                    </div>
                    <div className="border rounded px-2 py-1">
                        %
                    </div>
                </div>
                {errors.assessmentPassingPercentage && <div className="invalid-feedback">{errors.assessmentPassingPercentage}</div>}
            </div>

            <div className="mb-3">
                <label className="form-label">Reward Points</label>
                <input
                    type="number"
                    className={`form-control ${errors.assessmentRewardPoints ? 'is-invalid' : ''}`}
                    name="rewardPoints"
                    value={assessmentForm.rewardPoints}
                    onChange={(e) => {
                        handleAssessmentFormChange(e);
                        if (errors.assessmentRewardPoints) {
                            setErrors({...errors, assessmentRewardPoints: ''});
                        }
                    }}
                    placeholder="Enter reward points"
                    min="0"
                />
                {errors.assessmentRewardPoints && <div className="invalid-feedback">{errors.assessmentRewardPoints}</div>}
            </div>
        </>
    );

    const renderSurveyForm = () => (
        <>
            <div className="mb-3">
                <label className="form-label">Survey Name *</label>
                <input
                    type="text"
                    className={`form-control ${errors.surveyName ? 'is-invalid' : ''}`}
                    name="name"
                    value={surveyForm.name}
                    onChange={(e) => {
                        handleSurveyFormChange(e);
                        if (errors.surveyName) {
                            setErrors({...errors, surveyName: ''});
                        }
                    }}
                    placeholder="Enter survey name"
                    maxLength={100}
                />
                {errors.surveyName ? (
                    <div className="invalid-feedback">{errors.surveyName}</div>
                ) : (
                    <small className="text-muted">Maximum 100 characters allowed ({100 - surveyForm.name.length} remaining)</small>
                )}
            </div>

            <div className="mb-3">
                <label className="form-label">Reward Points</label>
                <input
                    type="number"
                    className={`form-control ${errors.surveyRewardPoints ? 'is-invalid' : ''}`}
                    name="rewardPoints"
                    value={surveyForm.rewardPoints}
                    onChange={(e) => {
                        handleSurveyFormChange(e);
                        if (errors.surveyRewardPoints) {
                            setErrors({...errors, surveyRewardPoints: ''});
                        }
                    }}
                    placeholder="Enter reward points"
                    min="0"
                />
                {errors.surveyRewardPoints && <div className="invalid-feedback">{errors.surveyRewardPoints}</div>}
            </div>
        </>
    );

    const renderContentMeta = (content) => {
        switch (content.type) {
            case 'video':
                return (
                    <div className="content-meta text-muted">
                        <div className="meta-item">
                            <Icon icon="fluent:comment-24-regular" width="16" height="16" />
                            <span>{content.comments} Comments</span>
                        </div>
                        <div className="meta-item">
                            <Icon icon="fluent:clock-24-regular" width="16" height="16" />
                            <span>{content.duration}</span>
                        </div>
                        <div className="meta-item">
                            <Icon icon="fluent:calendar-24-regular" width="16" height="16" />
                            <span>{content.date}</span>
                        </div>
                    </div>
                );
            case 'assessment':
                return (
                    <div className="content-meta text-muted">
                        <div className="meta-item">
                            <Icon icon="fluent:question-circle-24-regular" width="16" height="16" />
                            <span>{content.totalQuestions} Questions</span>
                        </div>
                        <div className="meta-item">
                            <Icon icon="fluent:timer-24-regular" width="16" height="16" />
                            <span>{content.timeLimit}</span>
                        </div>
                        <div className="meta-item">
                            <Icon icon="fluent:trophy-24-regular" width="16" height="16" />
                            <span>{content.passingPercentage}% Pass</span>
                        </div>
                        <div className="meta-item">
                            <Icon icon="fluent:star-24-regular" width="16" height="16" />
                            <span>{content.earnPoint} Points</span>
                        </div>
                        <div className="meta-item">
                            <Icon icon="fluent:calendar-24-regular" width="16" height="16" />
                            <span>{content.date}</span>
                        </div>
                    </div>
                );
            case 'survey':
                return (
                    <div className="content-meta text-muted">
                        <div className="meta-item">
                            <Icon icon="fluent:form-24-regular" width="16" height="16" />
                            <span>Survey</span>
                        </div>
                        <div className="meta-item">
                            <Icon icon="fluent:timer-24-regular" width="16" height="16" />
                            <span>{content.estimatedTime}</span>
                        </div>
                        <div className="meta-item">
                            <Icon icon="fluent:star-24-regular" width="16" height="16" />
                            <span>{content.earnPoint} Points</span>
                        </div>
                        <div className="meta-item">
                            <Icon icon="fluent:calendar-24-regular" width="16" height="16" />
                            <span>{content.date}</span>
                        </div>
                    </div>
                );
            case 'document':
                return (
                    <div className="content-meta text-muted">
                        <div className="meta-item">
                            <Icon icon="fluent:document-24-regular" width="16" height="16" />
                            <span>{content.fileType}</span>
                        </div>
                        <div className="meta-item">
                            <Icon icon="fluent:calendar-24-regular" width="16" height="16" />
                            <span>{content.date}</span>
                        </div>
                    </div>
                );
            default:
                return null;
        }
    };

    if (loading) {
        return <Loader />;
    }

    return (
        <>
            {/* Content Summary */}
            <div className="card mb-4">
                <div className="card-body">
                    <div className="row align-items-center">
                        <div className="col-12 col-md-auto mb-3 mb-md-0">
                            <div className="d-flex align-items-center">
                                <div className="profile-placeholder d-flex justify-content-center align-items-center bg-light rounded-circle me-3" style={{ width: '60px', height: '60px' }}>
                                    <Icon icon="fluent:book-24-regular" width="30" height="30" className="text-primary" />
                                </div>
                                <h5 className="mb-0">Module Content</h5>
                            </div>
                        </div>
                        <div className="col">
                            <div className="d-flex flex-wrap gap-3">
                                <div className="d-flex align-items-center">
                                    <Icon icon="fluent:video-24-regular" width="20" height="20" className="text-primary me-2" />
                                    <span>{contentSummary.videos} Videos</span>
                                </div>
                                <div className="d-flex align-items-center">
                                    <Icon icon="fluent:document-24-regular" width="20" height="20" className="text-primary me-2" />
                                    <span>{contentSummary.documents} Documents</span>
                                </div>
                                <div className="d-flex align-items-center">
                                    <Icon icon="fluent:quiz-new-24-regular" width="20" height="20" className="text-primary me-2" />
                                    <span>{contentSummary.assessments} Assessments</span>
                                </div>
                                <div className="d-flex align-items-center">
                                    <Icon icon="fluent:form-24-regular" width="20" height="20" className="text-primary me-2" />
                                    <span>{contentSummary.surveys} Surveys</span>
                                </div>
                                <div className="d-flex align-items-center">
                                    <Icon icon="fluent:clock-24-regular" width="20" height="20" className="text-primary me-2" />
                                    <span>{contentSummary.totalDuration}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {/* Header */}
            <div className="row mb-3">
                <div className="col-md-6 mt-2">
                   <div className="row">
                    <div className="col-md-6">
                    <div className="search-box">
                        <div className="position-relative">
                            <input
                                type="text"
                                className="form-control"
                                placeholder="Search by content title..."
                                value={searchQuery}
                                onChange={(e) => setSearchQuery(e.target.value)}
                            />
                         
                        </div>
                    </div>
                    </div>
                   </div>
                </div>
                <div className="col-md-6 text-end mt-2 d-flex justify-content-end align-items-center">
                    <button 
                        className="btn btn-primary text-nowrap" 
                        style={{ width: '150px' }}
                        onClick={() => {
                            if (!permissions.course_module_content_management_create) {
                                toast.warning("You don't have permission to create content");
                                return;
                            }
                            setShowAddModal(true);
                        }}
                        disabled={!permissions.course_module_content_management_create}
                        title={!permissions.course_module_content_management_create ? "You don't have permission to create content" : "Add Content"}
                    >
                        Add Content
                    </button>
                </div>
            </div>

            {/* Info Message */}
            <div className="info-bar mb-4">
                <Icon icon="fluent:info-24-regular" width="20" height="20" />
                <span>Click card to view content • Hold and drag to reorder ({filteredContents.length} items)</span>
            </div>

            {classroomName && (
                <div className="row mb-0">
                    <div className="col-12 mb-0">
                        <h6 className="text-muted mb-0">Course Name: {classroomName}</h6>
                    </div>
                </div>
            )}

            {/* Content Cards */}
            {filteredContents.length === 0 ? (
                searchQuery ? (
                    <div className="text-center mt-4">
                        <Icon icon="fluent:search-24-regular" width="48" height="48" className="text-muted mb-2" />
                        <p className="text-muted">No content found matching "{searchQuery}"</p>
                    </div>
                ) : (
                    <NoData message="No content found for this module. Click 'Add Content' to create content." />
                )
            ) : (
                <div className="position-relative">
                    {/* Loading overlay for reordering */}
                    {isReordering && (
                        <div className="position-absolute w-100 h-100 d-flex align-items-center justify-content-center"
                             style={{
                                 backgroundColor: 'rgba(255,255,255,0.8)',
                                 zIndex: 1000,
                                 borderRadius: '8px'
                             }}>
                            <div className="d-flex align-items-center gap-2">
                                <div className="spinner-border spinner-border-sm text-primary" role="status"></div>
                                <span className="text-primary fw-medium">Reordering content...</span>
                            </div>
                        </div>
                    )}

                    {/* Drag instruction */}
                    {filteredContents.length > 1 && (
                        <div className="d-flex justify-content-end align-items-center mb-3">
                            <small className="text-muted d-flex align-items-center gap-1">
                                <Icon icon="fluent:grip-vertical-20-filled" width="16" height="16" />
                                Drag to reorder content
                            </small>
                        </div>
                    )}

                    <Reorder.Group
                        axis="y"
                        values={filteredContents}
                        onReorder={handleReorderContents}
                        className="list-unstyled"
                        layoutScroll
                        style={{ overflowY: "visible" }}
                        onMouseDown={() => console.log('🖱️ Mouse down on Reorder.Group')}
                    >
                        {filteredContents.map((content, index) => (
                            <Reorder.Item
                                key={content.id}
                                value={content}
                                onDragStart={() => {
                                    console.log('🖱️ Drag start:', content.title);
                                    setDraggedItem(content.id);
                                    setIsDraggingContent(true);
                                }}
                                onDragEnd={() => {
                                    console.log('🖱️ Drag end:', content.title);
                                    setDraggedItem(null);
                                    // Add small delay to prevent click event from firing immediately after drag
                                    setTimeout(() => {
                                        setIsDraggingContent(false);
                                    }, 100);
                                }}
                                className="list-none mb-3"
                                style={{ cursor: 'grab' }}
                                onMouseDown={() => console.log('🖱️ Mouse down on item:', content.title)}
                                initial={{ opacity: 0, y: 20 }}
                                animate={{
                                    opacity: 1,
                                    y: 0,
                                    scale: draggedItem === content.id ? 1.02 : 1
                                }}
                                exit={{ opacity: 0, y: -20 }}
                                transition={{
                                    duration: 0.3,
                                    type: "spring",
                                    stiffness: 300,
                                    damping: 30
                                }}
                                whileDrag={{
                                    scale: 1.05,
                                    rotate: 2,
                                    zIndex: 50,
                                    boxShadow: "0 25px 50px -12px rgba(0, 123, 255, 0.25), 0 0 0 1px rgba(0, 123, 255, 0.1)",
                                    transition: { duration: 0.2 }
                                }}
                                whileHover={{
                                    scale: 1.01,
                                    transition: { duration: 0.2 }
                                }}
                                layout
                            >
                                <div className="row">
                                    <div className="col-12">
                                        <div
                                            className="content-card"
                                            onClick={() => handleContentClick(content)}
                                            style={{ cursor: 'pointer' }}
                                        >
                                            <div className="card position-relative">

                                                {/* Action Buttons */}
                                                <div className="row g-0">
                                                    <div className="col-12 p-2">
                                                        <div className="d-flex justify-content-end gap-2">
                                                        {/* Only show edit button for non-video content */}
                                                        {content.type !== 'video' && (
                                                            <button
                                                                className="btn btn-outline-dark btn-sm border border-dark"
                                                                style={{ width: "44px", height: "44px" }}
                                                                title={!permissions.course_module_content_management_edit ? "You don't have permission to edit content" : "Edit"}
                                                                onClick={(e) => handleEditClick(e, content)}
                                                                disabled={!permissions.course_module_content_management_edit}
                                                            >
                                                                <Icon icon="fluent:edit-24-regular" width="28" height="28" />
                                                            </button>
                                                        )}
                                                        <button
                                                            className="btn btn-outline-dark btn-sm border border-dark"
                                                            style={{ width: "44px", height: "44px" }}
                                                            title={!permissions.course_module_content_management_delete ? "You don't have permission to delete content" : "Delete"}
                                                            onClick={(e) => handleDeleteClick(e, content)}
                                                            disabled={!permissions.course_module_content_management_delete}
                                                        >
                                                            <Icon icon="fluent:delete-24-regular" width="28" height="28" />
                                                        </button>
                                                        <button
                                                            className={`btn ${content.status === "active" ? "btn-success" : "btn-outline-warning"} btn-sm border`}
                                                            style={{ width: "44px", height: "44px" }}
                                                            title={!permissions.course_module_content_management_status ? "You don't have permission to change status" : `${content.status === "active" ? "Deactivate" : "Activate"} Content`}
                                                            onClick={(e) => handleStatusChange(e, content)}
                                                            disabled={statusLoading === content.id || !permissions.course_module_content_management_status}
                                                        >
                                                            {statusLoading === content.id ? (
                                                                <span className="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                                            ) : (
                                                                <Icon
                                                                    icon={content.status === "active" ? "fluent:checkmark-circle-24-regular" : "fluent:dismiss-circle-24-regular"}
                                                                    width="28"
                                                                    height="28"
                                                                />
                                                            )}
                                                        </button>
                                                        </div>
                                                    </div>
                                                    <hr />
                                                </div>

                                                <span className="position-number">{index + 1}</span>

                                                {/* Content Body */}
                                                <div className="card-body p-1">
                                                    <div className="row g-0 align-items-center">
                                                        {/* Drag Handle - Left Center */}
                                                        <div className="col-auto me-2">
                                                            <div className="d-flex align-items-center justify-content-center" style={{
                                                                width: '24px',
                                                                height: '40px',
                                                                backgroundColor: '#f8f9fa',
                                                                border: '1px solid #dee2e6',
                                                                borderRadius: '6px',
                                                                cursor: 'grab'
                                                            }}>
                                                                <Icon icon="fluent:grip-vertical-20-filled" width="16" height="16" className="text-muted" />
                                                            </div>
                                                        </div>

                                                        {/* Content Preview */}
                                                        <div className="col-12 col-md-auto">
                                                            <div className="content-preview">
                                                                {content.type === 'document' ? (
                                                                    <div className="document-preview d-flex align-items-center justify-content-center" style={{ width: '120px', height: '120px', background: '#f8f9fa' }}>
                                                                        <Icon
                                                                            icon={
                                                                                content.fileType === 'PDF' ? "fluent:document-pdf-24-regular" :
                                                                                content.fileType === 'DOC' || content.fileType === 'DOCX' ? "fluent:document-text-24-regular" :
                                                                                "fluent:document-24-regular"
                                                                            }
                                                                            width="48"
                                                                            height="48"
                                                                            className="text-primary"
                                                                        />
                                                                    </div>
                                                                ) : content.type === 'video' ? (
                                                                    <video
                                                                        src={content.url}
                                                                        className="preview-video"
                                                                        controls={false}
                                                                        preload="metadata"
                                                                    >
                                                                        Your browser does not support the video tag.
                                                                    </video>
                                                                ) : (
                                                                    <div className="preview-placeholder">
                                                                        <Icon
                                                                            icon={content.type === 'assessment' ? "fluent:quiz-new-24-regular" : "fluent:form-24-regular"}
                                                                            width="32"
                                                                            height="32"
                                                                            className="text-muted"
                                                                        />
                                                                    </div>
                                                                )}
                                                            </div>
                                                        </div>

                                                        {/* Content Info */}
                                                        <div className="col">
                                                            <div className="content-info ps-md-3">
                                                                <div className="d-flex align-items-center gap-2 mb-2">
                                                                    {content.type === 'video' && (
                                                                        <>
                                                                            <Icon icon="fluent:video-24-regular" width="20" height="20" className="text-primary" />
                                                                            <span className="text-primary">Video</span>
                                                                        </>
                                                                    )}
                                                                    {content.type === 'survey' && (
                                                                        <>
                                                                            <Icon icon="fluent:form-24-regular" width="20" height="20" className="text-primary" />
                                                                            <span className="text-primary">Survey</span>
                                                                        </>
                                                                    )}
                                                                    {content.type === 'assessment' && (
                                                                        <>
                                                                            <Icon icon="fluent:quiz-new-24-regular" width="20" height="20" className="text-primary" />
                                                                            <span className="text-primary">Assessment</span>
                                                                        </>
                                                                    )}
                                                                    {content.type === 'document' && (
                                                                        <>
                                                                            <Icon icon="fluent:document-24-regular" width="20" height="20" className="text-primary" />
                                                                            <span className="text-primary">Document</span>
                                                                        </>
                                                                    )}
                                                                </div>
                                                                <h6 className="mb-2">{content.title}</h6>
                                                                {renderContentMeta(content)}
                                                                {content.type === 'video' && content.description && (
                                                                <p className="text-muted mb-0 content-description mt-2">
                                                                {content.description}
                                                            </p>
                                                                )}
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </Reorder.Item>
                        ))}
                    </Reorder.Group>
                </div>
            )}

            {/* Add Content Modal */}
            {showAddModal && (
                <div className="modal show d-block" style={{ backgroundColor: 'rgba(0,0,0,0.5)' }}>
                    <div className="modal-dialog">
                        <div className="modal-content">
                            <div className="modal-header">
                                <h5 className="modal-title">{isEditMode ? 'Edit Content' : 'Add New Content'}</h5>
                                <button
                                    type="button"
                                    className="btn-close"
                                    onClick={handleCloseModal}
                                ></button>
                            </div>
                            <div className="modal-body">
                                <div className="mb-4">
                                    <label className="form-label">Select Content Type</label>
                                    <select
                                        className="form-select"
                                        value={selectedContentType}
                                        onChange={(e) => setSelectedContentType(e.target.value)}
                                        disabled={isEditMode}
                                    >
                                        <option value="video">Video</option>
                                        <option value="document">Document</option>
                                        <option value="assessment">Assessment</option>
                                        <option value="survey">Survey</option>
                                    </select>
                                </div>

                                {selectedContentType === 'video' && renderVideoForm()}
                                {selectedContentType === 'document' && renderDocumentForm()}
                                {selectedContentType === 'assessment' && renderAssessmentForm()}
                                {selectedContentType === 'survey' && renderSurveyForm()}
                            </div>
                            <div className="modal-footer">
                                <button 
                                    type="button" 
                                    className="btn btn-secondary"
                                    onClick={handleCloseModal}
                                    disabled={isSubmitting}
                                >
                                    Cancel
                                </button>
                                <button 
                                    type="button" 
                                    className="btn btn-primary"
                                    onClick={handleAddContent}
                                    disabled={isSubmitting}
                                >
                                    {isSubmitting ? (
                                        <>
                                            <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                                            {isEditMode ?
                                                (selectedContentType === 'video' ? 'Updating Video...' : 'Updating Content...') :
                                                (selectedContentType === 'video' ? 'Uploading Video...' : 'Adding Content...')
                                            }
                                        </>
                                    ) : (
                                        isEditMode ? 'Update Content' : 'Add Content'
                                    )}
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            )}

            {/* Delete Confirmation Modal */}
            {showDeleteModal && (
                <div className="modal show d-block" style={{ backgroundColor: 'rgba(0,0,0,0.5)' }}>
                    <div className="modal-dialog">
                        <div className="modal-content">
                            <div className="modal-header">
                                <h5 className="modal-title">Confirm Delete</h5>
                                <button 
                                    type="button" 
                                    className="btn-close"
                                    onClick={() => {
                                        setShowDeleteModal(false);
                                        setContentToDelete(null);
                                    }}
                                ></button>
                            </div>
                            <div className="modal-body">
                                <p>Are you sure you want to delete this {contentToDelete?.type.toLowerCase()}?</p>
                                <p className="mb-0"><strong>Title:</strong> {contentToDelete?.title}</p>
                            </div>
                            <div className="modal-footer">
                                <button 
                                    type="button" 
                                    className="btn btn-secondary"
                                    onClick={() => {
                                        setShowDeleteModal(false);
                                        setContentToDelete(null);
                                    }}
                                    disabled={isSubmitting}
                                >
                                    Cancel
                                </button>
                                <button 
                                    type="button" 
                                    className="btn btn-danger"
                                    onClick={handleDeleteConfirm}
                                    disabled={isSubmitting}
                                >
                                    {isSubmitting ? (
                                        <>
                                            <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                                            Deleting...
                                        </>
                                    ) : (
                                        'Delete'
                                    )}
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            )}

            {/* Upload Progress Modal */}
            {showUploadModal && (
                <div className="modal show d-block fade" tabIndex="-1" style={{ backgroundColor: "rgba(0,0,0,0.8)" }}>
                    <div className="modal-dialog modal-dialog-centered">
                        <div className="modal-content">
                            <div className="modal-body text-center p-4">
                                {uploadStatus === 'uploading' && (
                                    <>
                                        <div className="mb-3">
                                            <Icon icon="fluent:cloud-arrow-up-24-regular" width="48" height="48" className="text-primary" />
                                        </div>
                                        <h5 className="mb-3">Uploading Video...</h5>
                                        <div className="progress mb-3" style={{ height: '20px' }}>
                                            <div
                                                className="progress-bar progress-bar-striped progress-bar-animated"
                                                role="progressbar"
                                                style={{ width: `${uploadProgress}%` }}
                                                aria-valuenow={uploadProgress}
                                                aria-valuemin="0"
                                                aria-valuemax="100"
                                            >
                                                {uploadProgress}%
                                            </div>
                                        </div>
                                        <p className="text-muted mb-0">Please wait while we process your video...</p>
                                    </>
                                )}

                                {uploadStatus === 'success' && (
                                    <>
                                        <div className="mb-3">
                                            <Icon icon="fluent:checkmark-circle-24-filled" width="48" height="48" className="text-success" />
                                        </div>
                                        <h5 className="text-success mb-3">Upload Successful!</h5>
                                        <p className="text-muted mb-0">Your video has been {isEditMode ? 'updated' : 'uploaded'} successfully.</p>
                                    </>
                                )}

                                {uploadStatus === 'error' && (
                                    <>
                                        <div className="mb-3">
                                            <Icon icon="fluent:error-circle-24-filled" width="48" height="48" className="text-danger" />
                                        </div>
                                        <h5 className="text-danger mb-3">Upload Failed</h5>
                                        <p className="text-muted mb-0">There was an error uploading your video. Please try again.</p>
                                    </>
                                )}
                            </div>
                        </div>
                    </div>
                </div>
            )}

        </>
    );
}

export default ModuleContent;
