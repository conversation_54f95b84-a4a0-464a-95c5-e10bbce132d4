const bcrypt = require('bcrypt');
const { validationResult } = require('express-validator');
const jwt = require('jsonwebtoken');
const { mysqlServerConnection } = require('../db/db');
const multer = require('multer');
require('dotenv').config();
const nodemailer = require('nodemailer');
const path = require('path');
const maindb = process.env.MAIN_DB;
const firebase = require('firebase/app');
const admin = require('../helper/firbaseCongfig');
const { io } = require('../app/server');



const SetNotification = async ({ dbName, userId, sendFrom = null, title, body, fileUrl = null, scheduleDateTime = null }) => {
    try {
      console.log(dbName, userId, sendFrom, title, body, fileUrl);
  
      // ✅ Use the same scheduleDateTime that was passed (convert to MySQL format)
      const mysqlDateTime = scheduleDateTime ? 
        new Date(scheduleDateTime).toISOString().slice(0, 19).replace('T', ' ') : 
        new Date().toISOString().slice(0, 19).replace('T', ' ');
  
      const query = `
        INSERT INTO ${dbName}.notifications 
        (user_id, send_from, title, body, file_url, is_deleted, is_read, createdAt)
        VALUES (?, ?, ?, ?, ?, FALSE, FALSE, ?);
      `;
  
      const values = [
        userId,
        sendFrom,
        title,
        body,
        fileUrl,
        mysqlDateTime // ✅ Store the same scheduleDateTime time
      ];
  
      await mysqlServerConnection.query(query, values);
  
      // ✅ Emit unread notifications to user
      const [notifications] = await mysqlServerConnection.query(
        `SELECT * FROM ${dbName}.notifications WHERE user_id = ? AND is_read = false`,
        [userId]
      );
  
      global._io.to(userId.toString()).emit('pushNotifications', { notifications });
  
      return true;
  
    } catch (err) {
      console.error('Error inserting notification or sending socket:', err);
    }
  };
  





const SendPushNotificaiton = async ( token, title, dec ) => {
    const messagePayload = {
        notification: {
            title: title,
            body: dec,
        },
        token: token,
    };
    console.log("send_notification")

    try {
        const response = await admin.messaging().send(messagePayload);
        return true
    } catch (error) {
        console.error('Error sending notification:', error);
        return false
    }
}



module.exports = {
    SetNotification,
    SendPushNotificaiton
}