const { mysqlServerConnection } = require('../../../db/db');

// Get all assignments data
const getAssignments = async (req, res) => {
  try {
    console.log("ASSIGNMENTS")
    // Get class_id from query, if not present, default it to null
    const { class_id } = req.query;
    const classIdParam = class_id === undefined ? null : class_id;  // If class_id is not passed, set it to null

    // SQL query to fetch assignment details along with user status and submission status
    const query = `
      SELECT 
        a.id,
        a.assignment_name,
        a.question_details,
        a.is_active,
        a.course_id,
        a.created_by,
        a.createdAt,
        a.updatedAt,
        a.class_id,
        au.status AS user_status,
        au.submit_assignment 
      FROM ${req.user.db_name}.assignments a
      LEFT JOIN ${req.user.db_name}.assignment_user au 
        ON au.assignment_id = a.id 
        AND au.user_id = ? 
        AND au.class_id = ?
      WHERE a.is_active = TRUE 
        AND (a.class_id = ? OR ? IS NULL)
    `;

    // Run the query with the current user's user_id and the class_id (null or passed value)
    const [assignments] = await mysqlServerConnection.query(query, [req.user.userId, classIdParam, classIdParam, classIdParam]);

    console.log("These are the assignments",assignments);

    // Format the response to include the assignment data
    const formattedAssignments = assignments.map((assignment) => ({
      id: assignment.id,
      assignment_name: assignment.assignment_name,
      question_details: assignment.question_details,
      is_active: assignment.is_active,
      course_id: assignment.course_id,
      created_by: assignment.created_by,
      user_status: assignment.user_status === 0 || assignment.user_status == null ? 0 : 1,  // Convert "pending" to 0, others to 1
      submit_assignment: assignment.submit_assignment || null,
      createdAt: assignment.createdAt,
      updatedAt: assignment.updatedAt,
      class_id: assignment.class_id
    }));

    // Send response
    return res.status(200).json({
      success: true,
      data: {
        assignments: formattedAssignments
      }
    });
  } catch (error) {
    console.error('Error retrieving assignments:', error);
    return res.status(500).json({
      success: false,
      data: {
        error_msg: 'Internal server error.'
      }
    });
  }
};


const submitAssignment = async (req, res) => {
  try {
    const { assignment_id} = req.body;
    const file = req.files["upload_file"] || null;

    console.log("SUBMIT ASSIGNMENT REQUEST",assignment_id);

    console.log("FILESS",req.files);
    const user_id = req.user.userId;
    const { class_id } = req.query;
    const classIdParam = class_id === undefined ? null : class_id;

    if (!user_id || !assignment_id || !file) {
      return res.status(400).json({
        success: false,
        data: { error_msg: 'User ID, Assignment ID, file, and class_id are required.' }
      });
    }

    // Check if the assignment already exists for the user in the specified class
    const checkQuery = `SELECT * FROM ${req.user.db_name}.assignment_user WHERE user_id = ? AND assignment_id = ? AND class_id = ?`;
    const [existingAssignment] = await mysqlServerConnection.query(checkQuery, [user_id, assignment_id, classIdParam]);

    if (existingAssignment.length > 0) {
      // If record exists, update it with the class_id
      const updateQuery = `
        UPDATE ${req.user.db_name}.assignment_user
        SET submit_assignment = ?, status = '1', updatedAt = CURRENT_TIMESTAMP
        WHERE user_id = ? AND assignment_id = ? AND class_id = ?`;
      const [updateResult] = await mysqlServerConnection.query(updateQuery, [file?req.files['upload_file'][0].path:null, user_id, assignment_id, classIdParam]);

      if (updateResult.affectedRows === 0) {
        return res.status(404).json({
          success: false,
          data: { error_msg: 'Failed to update assignment for the provided user, assignment ID, and class ID.' }
        });
      }

      return res.status(200).json({
        success: true,
        data: { message: 'Assignment updated successfully.' }
      });
    } else {
      // If record does not exist, insert a new one with the class_id
      const insertQuery = `
        INSERT INTO ${req.user.db_name}.assignment_user (user_id, assignment_id, submit_assignment, status, class_id, createdAt, updatedAt)
        VALUES (?, ?, ?, '1', ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)`;
      const [insertResult] = await mysqlServerConnection.query(insertQuery, [user_id, assignment_id,file?req.files["upload_file"][0].path:null, classIdParam]);

      if (insertResult.affectedRows === 0) {
        return res.status(500).json({
          success: false,
          data: { error_msg: 'Failed to insert new assignment with class ID.' }
        });
      }

      return res.status(200).json({
        success: true,
        data: { message: 'Assignment submitted successfully.' }
      });
    }
  } catch (error) {
    console.error('Error submitting assignment:', error.message);
    return res.status(500).json({
      success: false,
      data: { error_msg: 'Internal server error.' }
    });
  }
};





const getCompleteAndPendingAssignments = async (req, res) => {

  try {
    const { class_id } = req.params;
    if (!class_id) {
      return res.status(404).json({
        success: false,
        message: 'Please provide the class id'
      });
    }

    // Query to get total number of assignments where class_id matches
    const [totalAssignmentCount] = await mysqlServerConnection.query(`
        SELECT COUNT(*) as assignmentCount
        FROM ${req.user.db_name}.assignments
        WHERE class_id = ?
      `, [class_id]);

    // Query to get the completed assignments count
    const [completedAssignments] = await mysqlServerConnection.query(`
        SELECT COUNT(*) as completed_assignments
        FROM ${req.user.db_name}.assignment_user
        WHERE class_id = ? AND user_id = ? AND status = '1'
      `, [class_id, req.user.userId]);

    // Extracting counts from the results
    const total_assignments = totalAssignmentCount[0].assignmentCount;
    const completed_assignments = completedAssignments[0].completed_assignments;

    // Calculate pending assignments
    const pending_assignments = total_assignments - completed_assignments;

    // Return the result
    res.status(200).json({
      success: true,
      data: {
        total: total_assignments,
        completed: completed_assignments,
        pending: pending_assignments
      }
    });

  } catch (error) {
    res.status(500).json({
      success: false,
      message: error.message || 'Internal server error'
    });
  }
};



module.exports = {
  getAssignments,
  submitAssignment,
  getCompleteAndPendingAssignments,
};
