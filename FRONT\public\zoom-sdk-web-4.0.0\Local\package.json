{"$schema": "http://json.schemastore.org/coffeelint", "name": "websdk-local", "version": "4.0.0", "description": "Zoom sample app for MeetingSDK-Web client view", "main": "index.js", "scripts": {"preinstall": "npx force-resolutions || echo 1", "test": "echo \"Error: no test specified\" && exit 1", "start": "set NODE_ENV=development && set BABEL_ENV=development && node corp.js --corp", "https": "set NODE_ENV=development && set BABEL_ENV=development && node corp.js --corp --https"}, "keywords": ["zoom", "meeting", "client", "webclient", "WebSDK"], "author": "Zoom Communications, Inc.", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@zoom/meetingsdk": "4.0.0", "lodash": "^4.17.21", "react": "18.2.0", "react-dom": "18.2.0", "react-redux": "8.1.2", "redux": "4.2.1", "redux-thunk": "2.4.2"}, "devDependencies": {"@babel/cli": "^7.23.4", "@babel/core": "^7.23.5", "@babel/eslint-parser": "^7.23.3", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/preset-env": "^7.23.5", "@babel/preset-react": "^7.23.3", "@babel/preset-stage-0": "7.8.3", "@babel/preset-stage-1": "7.8.3", "@babel/runtime": "^7.23.5", "babel-loader": "^9.1.3", "babel-preset-airbnb": "^5.0.0", "css-loader": "^7.1.2", "eslint": "^8.55.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-prettier": "^8.10.0", "eslint-plugin-import": "^2.29.0", "eslint-plugin-jsx-a11y": "^6.8.0", "eslint-plugin-prettier": "^5.0.1", "eslint-plugin-react": "^7.33.1", "eslint-plugin-react-hooks": "^4.6.0", "file-loader": "6.2.0", "node-polyfill-webpack-plugin": "^4.1.0", "style-loader": "^4.0.0", "url-loader": "4.1.1", "webpack": "^5.89.0", "webpack-cli": "^6.0.1", "webpack-dev-server": "^5.0.2"}, "resolutions": {"browserify-sign": "4.2.2"}}