-- update users table
ALTER TABLE `<db>`.`users`
    ADD COLUMN employee_no VARCHAR(50) NULL,
    ADD COLUMN company_code VARCHAR(50) NULL,
    ADD COLUMN company_description VARCHAR(255) NULL,
    ADD COLUMN ic_no VARBINARY(255) NULL,
    ADD COLUMN fin_no VARBINARY(255) NULL,
    ADD COLUMN gender VARCHAR(10) NULL,
    ADD COLUMN date_of_birth DATE NULL,
    ADD COLUMN date_joined DATE NULL,
    ADD COLUMN pwm_rank VARCHAR(1000) NULL,
    ADD COLUMN job_title VARCHAR(1000) NULL,



-- update role_permissions
UPDATE `<db>`.`role_permissions`
SET
`role_id` = 1
WHERE `role_id` = 3;

-- alter the courses table add certificate_template_id column
ALTER TABLE `<db>`.`courses` ADD COLUMN `certificate_template_id` INT NULL;
-- add the foreign key constraint
ALTER TABLE `<db>`.`courses` 
ADD INDEX `certificatekey_1_idx` (`certificate_template_id` ASC) VISIBLE;
;
ALTER TABLE `<db>`.`courses` 
ADD CONSTRAINT `certificatekey_1`
  FOREIGN KEY (`certificate_template_id`)
  REFERENCES `<db>`.`certificate_templates` (`id`)
  ON DELETE SET NULL
  ON UPDATE CASCADE;


-- changes done on 07-04-2025

-- add class_id column to group_rooms table
ALTER TABLE `<db>`.`group_rooms` ADD COLUMN `class_id` INT DEFAULT NULL;
ALTER TABLE `<db>`.`group_rooms` 
ADD CONSTRAINT `class_id`
  FOREIGN KEY (`class_id`)
  REFERENCES `<db>`.`classroom` (`id`)
  ON DELETE SET NULL
  ON UPDATE CASCADE;


-- Create classroom_trainee table
CREATE TABLE IF NOT EXISTS `<db>`.`classroom_trainee` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `class_id` INT NOT NULL,
    `user_id` INT NOT NULL,
    `createdAt` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updatedAt` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (`class_id`) REFERENCES `<db>`.`classroom`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`user_id`) REFERENCES `<db>`.`users`(`id`) ON DELETE CASCADE
);

-- Create classroom_assessments table
CREATE TABLE IF NOT EXISTS `<db>`.`classroom_assessments` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `class_id` INT NOT NULL,
    `assessment_name` VARCHAR(255) NOT NULL,
    `duration` INT NOT NULL,
    `total_questions` INT NULL DEFAULT 0,
    `pass_percentage` INT NULL DEFAULT 100,
    `activate_time` DATETIME NULL,
    `deactivate_time` DATETIME NULL,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `is_active` INT NULL DEFAULT 0,
    `is_deleted` TINYINT(1) NULL DEFAULT 0,
    `is_time_based` TINYINT(1) NULL DEFAULT 0,
    FOREIGN KEY (`class_id`) REFERENCES `<db>`.`classroom`(`id`) ON DELETE CASCADE
);

-- Create question_bank table
CREATE TABLE IF NOT EXISTS `<db>`.`question_bank` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `question_name` TEXT NOT NULL,
    `question_type` ENUM('mcq') NOT NULL,
    `is_active` INT NULL DEFAULT 1,
    `is_deleted` INT NULL DEFAULT 0,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Alter table if it exists and also change column question_name datatype to TEXT instead of VARCHAR(250)
ALTER TABLE `<db>`.`question_bank` 
DROP COLUMN `correct_option`,
DROP COLUMN `options`,
CHANGE COLUMN `question_name` `question_name` TEXT NOT NULL;

-- Alter question_bank_options table if it exists
ALTER TABLE `<db>`.`question_bank_options` 
DROP COLUMN `option_text`,
ADD COLUMN `option_number` INT NOT NULL,
CHANGE COLUMN `option_value` `option_value` TEXT NOT NULL;

-- Create question_bank_options table
CREATE TABLE IF NOT EXISTS `<db>`.`question_bank_options` (
        id INT AUTO_INCREMENT PRIMARY KEY,
        question_id INT NOT NULL,
        option_number INT NOT NULL,
        option_value TEXT NOT NULL,
        is_correct INT DEFAULT 0,
        is_active INT DEFAULT 1,
        is_deleted INT DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (question_id) REFERENCES `<db>`.`question_bank`(`id`) ON DELETE CASCADE
);

-- Create classroom_assessment_questions table
CREATE TABLE IF NOT EXISTS `<db>`.`classroom_assessment_questions` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `class_id` INT NOT NULL,
  `assessment_id` INT NOT NULL,
  `question_id` INT NOT NULL,
  `is_active` INT DEFAULT 1,
  `is_deleted` INT DEFAULT 0,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (`class_id`) REFERENCES `<db>`.`classroom`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`assessment_id`) REFERENCES `<db>`.`classroom_assessments`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`question_id`) REFERENCES `<db>`.`question_bank`(`id`) ON DELETE CASCADE
);

-- Create classroom_assessment_results table
CREATE TABLE IF NOT EXISTS `<db>`.`classroom_assessment_results` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `user_id` INT NOT NULL,
  `class_id` INT NOT NULL,
  `assessment_id` INT NOT NULL,
  `total_questions` INT NOT NULL,
  `total_attempted` INT NOT NULL,
  `total_correct` INT NOT NULL,
  `percentage` DECIMAL(5,2) NOT NULL,
  `is_active` INT DEFAULT 1,
  `is_deleted` INT DEFAULT 0,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (`user_id`) REFERENCES `<db>`.`users`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`class_id`) REFERENCES `<db>`.`classroom`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`assessment_id`) REFERENCES `<db>`.`classroom_assessments`(`id`) ON DELETE CASCADE
);

-- Create classroom_question_assessment_answer table
CREATE TABLE IF NOT EXISTS `<db>`.`classroom_question_assessment_answer` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `user_id` INT NOT NULL,
  `class_id` INT NOT NULL,
  `assessment_id` INT NOT NULL,
  `question_id` INT NOT NULL,
  `answer` TEXT NOT NULL,
  `results_id` INT NULL,
  `is_correct` INT DEFAULT 0,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (`user_id`) REFERENCES `<db>`.`users`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`class_id`) REFERENCES `<db>`.`classroom`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`assessment_id`) REFERENCES `<db>`.`classroom_assessments`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`question_id`) REFERENCES `<db>`.`question_bank`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`results_id`) REFERENCES `<db>`.`classroom_assessment_results`(`id`) ON DELETE SET NULL ON UPDATE CASCADE
);



--ADDING IS_TIME_BASED COLUMNS FOR CLASSROOM ASSESSMENTS
ALTER TABLE `<db>`.`classroom_assessments` ADD COLUMN `is_time_based` TINYINT(1) DEFAULT 0;
ALTER TABLE `<db>`.`classroom_assessments` ADD COLUMN `activate_time` DATETIME NULL DEFAULT NULL;
ALTER TABLE `<db>`.`classroom_assessments` ADD COLUMN `deactivate_time` DATETIME NULL DEFAULT NULL;


-- 10-04-2025
-- Enable event scheduler
SET GLOBAL event_scheduler = ON;

-- Create trigger to automatically update is_active based on time range for time-based assessments
DROP TRIGGER IF EXISTS `<db>`.update_assessment_active_status;
DELIMITER //
CREATE TRIGGER `<db>`.update_assessment_active_status
BEFORE UPDATE ON `<db>`.`classroom_assessments`
FOR EACH ROW
BEGIN
    IF NEW.is_time_based = 1 AND NEW.activate_time IS NOT NULL AND NEW.deactivate_time IS NOT NULL THEN
        IF NOW() BETWEEN NEW.activate_time AND NEW.deactivate_time THEN
            SET NEW.is_active = 1;
        ELSE
            SET NEW.is_active = 0;
        END IF;
    END IF;
END//
DELIMITER ;

-- Create trigger to automatically update is_active on insert for time-based assessments
DROP TRIGGER IF EXISTS `<db>`.update_assessment_active_status_insert;
DELIMITER //
CREATE TRIGGER `<db>`.update_assessment_active_status_insert
BEFORE INSERT ON `<db>`.`classroom_assessments`
FOR EACH ROW
BEGIN
    IF NEW.is_time_based = 1 AND NEW.activate_time IS NOT NULL AND NEW.deactivate_time IS NOT NULL THEN
        IF NOW() BETWEEN NEW.activate_time AND NEW.deactivate_time THEN
            SET NEW.is_active = 1;
        ELSE
            SET NEW.is_active = 0;
        END IF;
    END IF;
END//
DELIMITER ;

-- Create an event to periodically check and update assessment active status, 
--- *** IMPORTANT *** execute it with database name like, lms_api.classroom_assessments
DROP EVENT IF EXISTS `<db>`.update_assessment_active_status_event;
DELIMITER //
CREATE EVENT `<db>`.update_assessment_active_status_event
ON SCHEDULE EVERY 2 SECOND
DO
BEGIN
    UPDATE `<db>`.`classroom_assessments`
    SET is_active = 1
    WHERE is_time_based = 1 
    AND activate_time IS NOT NULL
    AND deactivate_time IS NOT NULL
    AND NOW() BETWEEN activate_time AND deactivate_time
    AND is_active = 0;
    
    UPDATE `<db>`.`classroom_assessments`
    SET is_active = 0
    WHERE is_time_based = 1 
    AND (NOW() < activate_time OR NOW() > deactivate_time)
    AND is_active = 1;
END//
DELIMITER ;



-- Time: 10-04-2025 09:21PM changes
ALTER TABLE `<db>`.`classroom_assessments` 
ADD COLUMN `total_questions` INT NULL DEFAULT 0 AFTER `duration`,
ADD COLUMN `pass_percentage` INT NULL DEFAULT 100 AFTER `total_questions`;


-- Time: 10-04-2025 10:15PM - Create triggers for total_questions auto-update
-- Trigger for INSERT operations on classroom_assessment_questions
DROP TRIGGER IF EXISTS `<db>`.update_assessment_question_count_insert;
DELIMITER //
CREATE TRIGGER `<db>`.update_assessment_question_count_insert
AFTER INSERT ON `<db>`.classroom_assessment_questions
FOR EACH ROW
BEGIN
    UPDATE `<db>`.classroom_assessments 
    SET total_questions = total_questions + 1
    WHERE id = NEW.assessment_id AND class_id = NEW.class_id;
END//
DELIMITER ;

-- Trigger for DELETE operations on classroom_assessment_questions
DROP TRIGGER IF EXISTS `<db>`.update_assessment_question_count_delete;
DELIMITER //
CREATE TRIGGER `<db>`.update_assessment_question_count_delete
AFTER DELETE ON `<db>`.classroom_assessment_questions
FOR EACH ROW
BEGIN
    UPDATE `<db>`.classroom_assessments 
    SET total_questions = GREATEST(0, total_questions - 1)
    WHERE id = OLD.assessment_id AND class_id = OLD.class_id;
END//
DELIMITER ;

--- Add results_id column to classroom_question_assessment_answer
ALTER TABLE `<db>`.`classroom_question_assessment_answer` 
ADD COLUMN `results_id` INT NULL AFTER `is_correct`,
ADD INDEX `classroom_assessment_ky_fkid_idx` (`results_id` ASC) VISIBLE;
;
ALTER TABLE `<db>`.`classroom_question_assessment_answer` 
ADD CONSTRAINT `classroom_assessment_ky_fkid`
  FOREIGN KEY (`results_id`)
  REFERENCES `<db>`.`classroom_assessment_results` (`id`)
  ON DELETE SET NULL
  ON UPDATE CASCADE;

-- Add mobileno_code column to users table
ALTER TABLE `<db>`.`users` 
ADD COLUMN `mobileno_code` VARCHAR(10) NULL AFTER `mobile`;

-- adding subject, module and tags in question_bank
ALTER TABLE `<db>`.`question_bank` 
ADD COLUMN `subject` VARCHAR(100) NULL AFTER `question_type`,
ADD COLUMN `module` VARCHAR(100) NULL AFTER `subject`,
ADD COLUMN `tags` LONGTEXT NULL AFTER `module`;

--- adding question_tags table
CREATE TABLE IF NOT EXISTS `<db>`.`question_tags` (
    `id` INT NOT NULL AUTO_INCREMENT PRIMARY KEY,
    `tag_name` VARCHAR(100) NULL,
    `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP);



ALTER TABLE `<db>`.`replies_comments` 
DROP FOREIGN KEY `replies_comments_ibfk_1`,
DROP FOREIGN KEY `replies_comments_ibfk_2`;
ALTER TABLE `<db>`.`replies_comments` 
ADD CONSTRAINT `replies_comments_ibfk_1`
  FOREIGN KEY (`comment_id`)
  REFERENCES `<db>`.`messagebox` (`id`)
  ON DELETE CASCADE
  ON UPDATE CASCADE,
ADD CONSTRAINT `replies_comments_ibfk_2`
  FOREIGN KEY (`user_id`)
  REFERENCES `<db>`.`users` (`id`)
  ON DELETE CASCADE
  ON UPDATE CASCADE;


ALTER TABLE `<db>`.`videos` 
CHANGE COLUMN `video_title` `video_title` LONGTEXT NULL DEFAULT NULL ,
CHANGE COLUMN `video_description` `video_description` LONGTEXT NULL DEFAULT NULL ;



-- 03-05-2025

ALTER TABLE `<db>`.`live_class` 
MODIFY COLUMN `start_time` DATETIME DEFAULT NULL ,
MODIFY COLUMN `end_time` DATETIME DEFAULT NULL ;


-- adding changes for announcement (05-05-2025)


ALTER TABLE `announcements` 
ADD COLUMN `send_notification` TINYINT(1) DEFAULT 0,
ADD COLUMN `send_email` TINYINT(1) DEFAULT 0;


-- Add notification triggers for course approval and assessment activation

DROP TRIGGER IF EXISTS `<db>`.course_approval_notification;
DROP TRIGGER IF EXISTS `<db>`.assessment_activation_notification;

DELIMITER //

CREATE TRIGGER `<db>`.course_approval_notification
AFTER UPDATE ON `<db>`.courses
FOR EACH ROW
BEGIN
    -- Check if course was just approved and activated
    IF (OLD.is_approved = 0 AND NEW.is_approved = 1 AND NEW.is_active = 1) THEN
        -- Get all trainee users (role_id = 2)
        INSERT INTO `<db>`.notifications (user_id, send_from, title, body, is_deleted, is_read, createdAt, updatedAt)
        SELECT 
            u.id, 
            NEW.created_by, 
            'New Course Available', 
            CONCAT('Course "', NEW.course_name, '" is now available for enrollment'), 
            0, 
            0, 
            NOW(), 
            NOW()
        FROM `<db>`.users u
        JOIN `<db>`.user_roles ur ON u.id = ur.user_id
        WHERE ur.role_id = 2;
    END IF;
END//

CREATE TRIGGER `<db>`.assessment_activation_notification
AFTER UPDATE ON `<db>`.classroom_assessments
FOR EACH ROW
BEGIN
    DECLARE classroom_name VARCHAR(255);
    
    -- Check if assessment was just activated
    IF (OLD.is_active = 0 AND NEW.is_active = 1) THEN
        -- Get classroom name
        SELECT classroom.cls_name INTO classroom_name
        FROM `<db>`.classroom 
        WHERE classroom.id = NEW.class_id;
        
        -- Only proceed if we found a valid classroom
        IF classroom_name IS NOT NULL THEN
            -- Get all trainees enrolled in this specific classroom
            INSERT INTO `<db>`.notifications (user_id, send_from, title, body, is_deleted, is_read, createdAt, updatedAt)
            SELECT 
                classroom_trainee.user_id, 
                1, -- Admin as default sender
                'Assessment is Active now', 
                CONCAT('Assessment "', NEW.assessment_name, '" is now active in classroom "', classroom_name, '"'), 
                0, 
                0, 
                NOW(), 
                NOW()
            FROM `<db>`.classroom_trainee 
            JOIN `<db>`.user_roles ON classroom_trainee.user_id = user_roles.user_id
            WHERE classroom_trainee.class_id = NEW.class_id AND user_roles.role_id = 2;
        END IF;
    END IF;
END//

DELIMITER ;

-- 06-05-2025 Adding classroom_assignments table
CREATE TABLE IF NOT EXISTS `<db>`.`classroom_assignments` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `class_id` INT NOT NULL,
    `created_by` INT NOT NULL,
    `title` TEXT NOT NULL,
    `description` TEXT NULL,
    `attachment_url` VARCHAR(255) NULL,
    `due_date` DATETIME DEFAULT NULL,
    `assigned_date` DATETIME DEFAULT NULL,
    `createdAt` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updatedAt` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `is_deleted` BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (`class_id`) REFERENCES `<db>`.`classroom`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`created_by`) REFERENCES `<db>`.`users`(`id`) ON DELETE CASCADE
);

-- 07-05-2025 Adding live_class_logs table
CREATE TABLE IF NOT EXISTS `<db>`.`live_class_logs` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `class_id` INT NOT NULL,
    `user_id` INT NOT NULL,
    `live_class_id` INT NOT NULL,
    `login_time` DATETIME DEFAULT NULL,
    `logout_time` DATETIME DEFAULT NULL,
    `createdAt` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updatedAt` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (`class_id`) REFERENCES `<db>`.`classroom`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`user_id`) REFERENCES `<db>`.`users`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`live_class_id`) REFERENCES `<db>`.`live_class`(`id`) ON DELETE CASCADE
);



-- PERMISSIONS MANAGEMENT - REORGANIZED IN LOGICAL ORDER

-- 1. RENAME PERMISSIONS (All renames first)
-- Rename trainee management permissions
UPDATE auth.permissions 
SET `name` = 'can_create_trainee', 
    `description` = 'Permission to create a new trainee in the system'
WHERE `name` = 'can_create_enduser';

UPDATE auth.permissions 
SET `name` = 'can_edit_trainee', 
    `description` = 'Permission to edit existing trainee details'
WHERE `name` = 'can_edit_enduser';

UPDATE auth.permissions 
SET `name` = 'can_delete_trainee', 
    `description` = 'Permission to delete a trainee from the system'
WHERE `name` = 'can_delete_enduser';

UPDATE auth.permissions 
SET `name` = 'can_view_trainees', 
    `description` = 'Permission to view the list of trainees'
WHERE `name` = 'can_view_endusers';

UPDATE auth.permissions 
SET `name` = 'can_block_trainee', 
    `description` = 'Permission to block a trainee from accessing the platform'
WHERE `name` = 'can_block_enduser';

UPDATE auth.permissions 
SET `name` = 'can_activate_deactivate_trainee', 
    `description` = 'Permission to activate or deactivate a trainee in the system'
WHERE `name` = 'can_activate_deactivate_enduser';

-- Rename comment permissions
UPDATE auth.permissions 
SET `name` = 'can_view_course_comments', 
    `description` = 'Permission to view comments made on course modules'
WHERE `name` = 'can_view_comments';

UPDATE auth.permissions 
SET `name` = 'can_delete_course_comments', 
    `description` = 'Permission to delete comments made on course modules'
WHERE `name` = 'can_delete_comments';

UPDATE auth.permissions 
SET `name` = 'can_reply_on_course_comments', 
    `description` = 'Permission to reply to comments made on course modules'
WHERE `name` = 'can_reply_on_comments';

UPDATE auth.permissions 
SET `name` = 'can_block_course_comment', 
    `description` = 'Permission to block specific comments from appearing on course modules'
WHERE `name` = 'can_block_comment';

-- 2. UPDATE CATEGORIES (All category updates next)
-- User Management
UPDATE auth.permissions SET `category` = 'User Management' WHERE `name` IN (
  'can_create_user', 'can_edit_user', 'can_delete_user', 'can_view_users', 
  'can_block_user', 'can_activate_deactivate_user'
);

-- Trainee Management
UPDATE auth.permissions SET `category` = 'Trainee Management' WHERE `name` IN (
  'can_create_trainee', 'can_edit_trainee', 'can_delete_trainee', 'can_view_trainees', 
  'can_block_trainee', 'can_activate_deactivate_trainee'
);

-- Role Management
UPDATE auth.permissions SET `category` = 'Role Management' WHERE `name` IN (
  'can_create_role', 'can_edit_role', 'can_delete_role', 'can_view_role', 
  'can_assign_role', 'can_deactivate_role', 'can_activate_role', 
  'can_give_permission_create_role', 'can_give_permission_edit_role', 
  'can_give_permission_delete_role', 'can_give_permission_view_role'
);

-- Course Management (including Modules and Course Comments)
UPDATE auth.permissions SET `category` = 'Course Management' WHERE `name` IN (
  'can_create_course', 'can_edit_course', 'can_view_course', 'can_delete_course',
  'can_publish_course', 'can_unpublish_course', 'can_deactivate_course', 'can_approve_course',
  'can_update_course_materials', 'can_delete_course_materials', 'can_see_all_course',
  'can_announce_result_after_course', 'can_add_general_theme_to_course',
  'can_add_module', 'can_delete_module', 'can_edit_module_name', 'can_view_module', 'can_delete_empty_module',
  'can_view_course_comments', 'can_delete_course_comments', 'can_reply_on_course_comments', 
  'can_block_course_comment', 'can_comment_all_users', 'can_particular_user_not_comment'
);

-- Classroom Management (including Assessments)
UPDATE auth.permissions SET `category` = 'Classroom Management' WHERE `name` IN (
  'can_create_classroom', 'can_edit_classroom', 'can_view_classroom', 'can_delete_classroom', 
  'can_change_classroom_name', 'can_publicize_classroom', 'can_unpublicize_classroom', 
  'can_add_content_to_classroom', 'can_upload_videos_to_classroom', 'can_go_live_in_classroom',
  'can_give_replay_on_live_classroom', 'can_show_screen_in_live_classroom',
  'can_block_comment_in_live_classroom', 'can_block_user_in_classroom',
  'can_mute_all_users_in_classroom', 'can_unmute_user_in_classroom',
  'can_give_reply_on_classroom_comment',
  'can_create_assessment', 'can_update_assessment_before_publish', 'can_view_assessment',
  'can_delete_assessment', 'can_publish_assessment', 'can_unpublish_assessment',
  'can_update_assessment_after_publish', 'can_delete_after_publish', 'can_announce_assessment_result',
  'can_retake_assessment'
);

-- Certificate Management
UPDATE auth.permissions SET `category` = 'Certificate Management' WHERE `name` IN (
  'can_view_certificate', 'can_add_or_create_certificate', 'can_edit_certificate',
  'can_delete_certificate', 'can_view_all_uploaded_themes', 'can_upload_certificate_theme',
  'can_delete_certificate_theme', 'can_publish_certificate_theme', 'can_unpublish_certificate_theme'
);

-- 3. INSERT NEW PERMISSIONS (All inserts last, grouped by category)
-- Course Management - New permissions
INSERT INTO auth.permissions (`name`, `description`, `createdAt`, `updatedAt`, `category`) VALUES 
('can_view_course_analytics', 'Permission to view analytics data for courses', NOW(), NOW(), 'Course Management'),
('can_send_course_for_approval', 'Permission to send a course for approval', NOW(), NOW(), 'Course Management'),
('can_list_approval_requests', 'Permission to list courses pending approval', NOW(), NOW(), 'Course Management'),
('can_reject_course', 'Permission to reject a course approval request', NOW(), NOW(), 'Course Management'),
('can_activate_module', 'Permission to activate a module', NOW(), NOW(), 'Course Management'),
('can_deactivate_module', 'Permission to deactivate a module', NOW(), NOW(), 'Course Management'),
('can_add_module_content', 'Permission to add content to a module', NOW(), NOW(), 'Course Management'),
('can_edit_module_content', 'Permission to edit content in a module', NOW(), NOW(), 'Course Management'),
('can_activate_module_content', 'Permission to activate content in a module', NOW(), NOW(), 'Course Management'),
('can_deactivate_module_content', 'Permission to deactivate content in a module', NOW(), NOW(), 'Course Management'),
('can_view_module_rating', 'Permission to view ratings on a module', NOW(), NOW(), 'Course Management');

-- Classroom Management - New permissions
INSERT INTO auth.permissions (`name`, `description`, `createdAt`, `updatedAt`, `category`) VALUES 
-- Classroom Trainees
('can_view_classroom_trainees', 'Permission to view trainees in a classroom', NOW(), NOW(), 'Classroom Management'),
('can_add_classroom_trainees', 'Permission to add trainees to a classroom', NOW(), NOW(), 'Classroom Management'),
('can_edit_classroom_trainees', 'Permission to edit trainees in a classroom', NOW(), NOW(), 'Classroom Management'),
('can_delete_classroom_trainee', 'Permission to remove a trainee from a classroom', NOW(), NOW(), 'Classroom Management'),
-- Classroom Resources
('can_list_classroom_resources', 'Permission to list resources in a classroom', NOW(), NOW(), 'Classroom Management'),
('can_delete_classroom_resources', 'Permission to delete resources from a classroom', NOW(), NOW(), 'Classroom Management'),
-- Live Classes
('can_add_live_class', 'Permission to add a new live class', NOW(), NOW(), 'Classroom Management'),
('can_list_live_classes', 'Permission to list all live classes', NOW(), NOW(), 'Classroom Management'),
('can_update_live_class_status', 'Permission to update the status of a live class', NOW(), NOW(), 'Classroom Management'),
('can_delete_live_class', 'Permission to delete a live class', NOW(), NOW(), 'Classroom Management'),
('can_manage_live_class_participants', 'Permission to add or remove people from a live class', NOW(), NOW(), 'Classroom Management'),
-- Classroom Comments
('can_view_classroom_comments', 'Permission to view comments in classroom collaboration windows', NOW(), NOW(), 'Classroom Management'),
('can_delete_classroom_comments', 'Permission to delete comments in classroom collaboration windows', NOW(), NOW(), 'Classroom Management'),
('can_reply_on_classroom_comments', 'Permission to reply to comments in classroom collaboration windows', NOW(), NOW(), 'Classroom Management'),
('can_block_classroom_comment', 'Permission to block specific comments from appearing in classroom collaboration windows', NOW(), NOW(), 'Classroom Management'),
('can_moderate_classroom_comments', 'Permission to moderate comments in classroom collaboration windows', NOW(), NOW(), 'Classroom Management'),
-- Assessments
('can_list_assessment_questions', 'Permission to list questions for an assessment', NOW(), NOW(), 'Classroom Management'),
('can_add_assessment_questions', 'Permission to add questions to an assessment', NOW(), NOW(), 'Classroom Management'),
('can_list_assessment_results', 'Permission to view results of assessments', NOW(), NOW(), 'Classroom Management'),
('can_view_assessment_analytics', 'Permission to view analytics for assessments', NOW(), NOW(), 'Classroom Management'),
-- Question Bank
('can_list_question_bank', 'Permission to list questions in the question bank', NOW(), NOW(), 'Classroom Management'),
('can_add_question_to_bank', 'Permission to add a question to the question bank', NOW(), NOW(), 'Classroom Management'),
('can_edit_question_in_bank', 'Permission to edit a question in the question bank', NOW(), NOW(), 'Classroom Management'),
('can_delete_question_from_bank', 'Permission to delete a question from the question bank', NOW(), NOW(), 'Classroom Management'),
-- Assignments
('can_list_assignments', 'Permission to list assignments', NOW(), NOW(), 'Classroom Management'),
('can_create_assignment', 'Permission to create a new assignment', NOW(), NOW(), 'Classroom Management'),
('can_edit_assignment', 'Permission to edit an assignment', NOW(), NOW(), 'Classroom Management'),
('can_delete_assignment', 'Permission to delete an assignment', NOW(), NOW(), 'Classroom Management'),
('can_activate_assignment', 'Permission to activate an assignment', NOW(), NOW(), 'Classroom Management'),
('can_deactivate_assignment', 'Permission to deactivate an assignment', NOW(), NOW(), 'Classroom Management');

-- Announcement Management - New permissions
INSERT INTO auth.permissions (`name`, `description`, `createdAt`, `updatedAt`, `category`) VALUES 
('can_list_announcements', 'Permission to list announcements', NOW(), NOW(), 'Announcement Management'),
('can_add_announcement', 'Permission to add a new announcement', NOW(), NOW(), 'Announcement Management'),
('can_edit_announcement', 'Permission to edit an announcement', NOW(), NOW(), 'Announcement Management'),
('can_delete_announcement', 'Permission to delete an announcement', NOW(), NOW(), 'Announcement Management');


-- 3. CREATE PERMISSION LEVELS

-- Auth

-- First, create a permission_levels table to define the levels
CREATE TABLE IF NOT EXISTS auth.permission_levels (
  `id` INT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(50) NOT NULL,
  `description` VARCHAR(255) NOT NULL,
  `level_value` INT NOT NULL,
  `createdAt` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP,
  `updatedAt` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`)
);

-- Insert the permission levels
INSERT INTO auth.permission_levels (`name`, `description`, `level_value`) VALUES
('Basic', 'Basic level permissions with limited access', 1),
('Intermediate', 'Intermediate level permissions with moderate access', 2),
('Advanced', 'Advanced level permissions with extensive access', 3),
('Admin', 'Administrative level permissions with full access', 4);

-- Add a level column to the permissions table
ALTER TABLE auth.permissions 
ADD COLUMN `level_id` INT NULL DEFAULT NULL,
ADD CONSTRAINT `fk_permission_level` FOREIGN KEY (`level_id`) REFERENCES auth.permission_levels (`id`) ON DELETE SET NULL;

-- Assign levels to existing permissions

-- Basic level permissions (view-only and basic operations)
UPDATE auth.permissions SET `level_id` = (SELECT `id` FROM auth.permission_levels WHERE `name` = 'Basic') 
WHERE `name` IN (
  'can_view_course', 'can_view_classroom', 'can_view_assessment',
  'can_view_certificate', 'can_view_trainees', 'can_view_users',
  'can_view_role', 'can_view_module', 'can_view_classroom_trainees',
  'can_list_live_classes', 'can_list_assessment_questions',
  'can_list_assessment_results', 'can_list_question_bank',
  'can_list_assignments', 'can_list_announcements',
  'can_view_module_rating', 'can_view_assessment_analytics',
  'can_view_course_analytics', 'can_list_approval_requests',
  'can_list_classroom_resources'
);

-- Intermediate level permissions (create and edit operations)
UPDATE auth.permissions SET `level_id` = (SELECT `id` FROM auth.permission_levels WHERE `name` = 'Intermediate') 
WHERE `name` IN (
  'can_create_course', 'can_edit_course', 'can_update_course_materials',
  'can_create_classroom', 'can_edit_classroom', 'can_add_content_to_classroom',
  'can_upload_videos_to_classroom', 'can_create_assessment',
  'can_edit_assessment', 'can_add_assessment_questions',
  'can_add_module', 'can_edit_module_name', 'can_add_module_content',
  'can_edit_module_content', 'can_add_classroom_trainees',
  'can_edit_classroom_trainees', 'can_add_live_class',
  'can_add_question_to_bank', 'can_edit_question_in_bank',
  'can_create_assignment', 'can_edit_assignment',
  'can_add_announcement', 'can_edit_announcement',
  'can_add_or_create_certificate', 'can_edit_certificate',
  'can_upload_certificate_theme', 'can_send_course_for_approval'
);

-- Advanced level permissions (delete, publish, activate/deactivate operations)
UPDATE auth.permissions SET `level_id` = (SELECT `id` FROM auth.permission_levels WHERE `name` = 'Advanced') 
WHERE `name` IN (
  'can_delete_course', 'can_publish_course', 'can_unpublish_course',
  'can_deactivate_course', 'can_delete_course_materials',
  'can_delete_classroom', 'can_publicize_classroom', 'can_unpublicize_classroom',
  'can_delete_assessment', 'can_publish_assessment', 'can_unpublish_assessment',
  'can_delete_module', 'can_delete_empty_module', 'can_activate_module',
  'can_deactivate_module', 'can_activate_module_content',
  'can_deactivate_module_content', 'can_delete_classroom_trainee',
  'can_delete_classroom_resources', 'can_update_live_class_status',
  'can_delete_live_class', 'can_delete_question_from_bank',
  'can_delete_assignment', 'can_activate_assignment',
  'can_deactivate_assignment', 'can_delete_announcement',
  'can_delete_certificate', 'can_delete_certificate_theme',
  'can_publish_certificate_theme', 'can_unpublish_certificate_theme',
  'can_approve_course', 'can_reject_course', 'can_announce_assessment_result',
  'can_retake_assessment'
);

-- Admin level permissions (system management, user/role management)
UPDATE auth.permissions SET `level_id` = (SELECT `id` FROM auth.permission_levels WHERE `name` = 'Admin') 
WHERE `name` IN (
  'can_create_user', 'can_edit_user', 'can_delete_user', 'can_block_user',
  'can_activate_deactivate_user', 'can_create_trainee', 'can_edit_trainee',
  'can_delete_trainee', 'can_block_trainee', 'can_activate_deactivate_trainee',
  'can_create_role', 'can_edit_role', 'can_delete_role', 'can_assign_role',
  'can_deactivate_role', 'can_activate_role', 'can_give_permission_create_role',
  'can_give_permission_edit_role', 'can_give_permission_delete_role',
  'can_give_permission_view_role', 'can_go_live_in_classroom',
  'can_give_replay_on_live_classroom', 'can_show_screen_in_live_classroom',
  'can_block_comment_in_live_classroom', 'can_block_user_in_classroom',
  'can_mute_all_users_in_classroom', 'can_unmute_user_in_classroom',
  'can_give_reply_on_classroom_comment', 'can_manage_live_class_participants',
  'can_add_general_theme_to_course'
);

-- DATABASE STRUCTURE CHANGES


-- 13th May adding total_marks column to classroom_assignments table
ALTER TABLE `<db>`.`classroom_assignments` ADD COLUMN `total_marks` INT DEFAULT NOT NULL;

-- 13th May 2025 - Adding classroom_assignment_submissions table
CREATE TABLE IF NOT EXISTS `<db>`.`classroom_assignment_submissions` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `assignment_id` INT NOT NULL,
    `user_id` INT NOT NULL,
    `class_id` INT NOT NULL,
    `submission_url` VARCHAR(255) NULL,
    `marks_obtained` INT NULL,
    `assignment_evaluation_status` BOOLEAN DEFAULT FALSE,
    `submittedAt` DATETIME DEFAULT CURRENT_TIMESTAMP,
    `is_deleted` BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (`assignment_id`) REFERENCES `<db>`.`classroom_assignments`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`user_id`) REFERENCES `<db>`.`users`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`class_id`) REFERENCES `<db>`.`classroom`(`id`) ON DELETE CASCADE
);


-- 14th May 2025 - Adding assignment_feedback column to classroom_assignment_submissions table
ALTER TABLE `<db>`.`classroom_assignment_submissions` ADD COLUMN `assignment_feedback` TEXT DEFAULT NULL;
-- DATABASE STRUCTURE CHANGES (12-05-2025)

-- Add content_type columns to questions table
ALTER TABLE `<db>`.`questions` 
ADD COLUMN `question_content_type` ENUM('text', 'image', 'audio') NOT NULL DEFAULT 'text' AFTER `question`,
ADD COLUMN `question_media_url` VARCHAR(255) NULL AFTER `question_content_type`;

-- Create a new table for storing options with content types
CREATE TABLE IF NOT EXISTS `<db>`.`question_options` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `question_id` INT NOT NULL,
  `option_key` VARCHAR(50) NOT NULL,
  `content_type` ENUM('text', 'image', 'audio') NOT NULL DEFAULT 'text',
  `content_value` TEXT NOT NULL,
  `media_url` VARCHAR(255) NULL,
  `text_fallback` TEXT NULL,
  `is_correct` TINYINT(1) NOT NULL DEFAULT 0,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (`question_id`) REFERENCES `<db>`.`questions`(`id`) ON DELETE CASCADE
);

-- Migration script to populate the new question_options table with existing data
DELIMITER //

CREATE PROCEDURE `<db>`.`migrate_question_options`()
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE q_id INT;
    DECLARE q_options TEXT;
    DECLARE q_correct TEXT;
    
    -- Cursor to fetch all questions with their options
    DECLARE cur CURSOR FOR 
        SELECT id, options, correct_option 
        FROM `<db>`.`questions` 
        WHERE options IS NOT NULL;
    
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
    
    OPEN cur;
    
    read_loop: LOOP
        FETCH cur INTO q_id, q_options, q_correct;
        
        IF done THEN
            LEAVE read_loop;
        END IF;
        
        -- Skip if options is not valid JSON
        IF q_options IS NULL OR q_options = '' THEN
            ITERATE read_loop;
        END IF;
        
        -- Process each option manually without relying on JSON functions
        -- Extract option1
        IF q_options LIKE '%"option1":%' THEN
            SET @value = SUBSTRING_INDEX(SUBSTRING_INDEX(q_options, '"option1":"', -1), '"', 1);
            SET @is_correct = IF(q_correct LIKE CONCAT('%"', @value, '"%'), 1, 0);
            
            INSERT INTO `<db>`.`question_options` (
                question_id, option_key, content_type, content_value, text_fallback, is_correct
            ) VALUES (
                q_id, 'option1', 'text', @value, @value, @is_correct
            );
        END IF;
        
        -- Extract option2
        IF q_options LIKE '%"option2":%' THEN
            SET @value = SUBSTRING_INDEX(SUBSTRING_INDEX(q_options, '"option2":"', -1), '"', 1);
            SET @is_correct = IF(q_correct LIKE CONCAT('%"', @value, '"%'), 1, 0);
            
            INSERT INTO `<db>`.`question_options` (
                question_id, option_key, content_type, content_value, text_fallback, is_correct
            ) VALUES (
                q_id, 'option2', 'text', @value, @value, @is_correct
            );
        END IF;
        
        -- Extract option3
        IF q_options LIKE '%"option3":%' THEN
            SET @value = SUBSTRING_INDEX(SUBSTRING_INDEX(q_options, '"option3":"', -1), '"', 1);
            SET @is_correct = IF(q_correct LIKE CONCAT('%"', @value, '"%'), 1, 0);
            
            INSERT INTO `<db>`.`question_options` (
                question_id, option_key, content_type, content_value, text_fallback, is_correct
            ) VALUES (
                q_id, 'option3', 'text', @value, @value, @is_correct
            );
        END IF;
        
        -- Extract option4
        IF q_options LIKE '%"option4":%' THEN
            SET @value = SUBSTRING_INDEX(SUBSTRING_INDEX(q_options, '"option4":"', -1), '"', 1);
            SET @is_correct = IF(q_correct LIKE CONCAT('%"', @value, '"%'), 1, 0);
            
            INSERT INTO `<db>`.`question_options` (
                question_id, option_key, content_type, content_value, text_fallback, is_correct
            ) VALUES (
                q_id, 'option4', 'text', @value, @value, @is_correct
            );
        END IF;
        
        -- Extract option5 (if exists)
        IF q_options LIKE '%"option5":%' THEN
            SET @value = SUBSTRING_INDEX(SUBSTRING_INDEX(q_options, '"option5":"', -1), '"', 1);
            SET @is_correct = IF(q_correct LIKE CONCAT('%"', @value, '"%'), 1, 0);
            
            INSERT INTO `<db>`.`question_options` (
                question_id, option_key, content_type, content_value, text_fallback, is_correct
            ) VALUES (
                q_id, 'option5', 'text', @value, @value, @is_correct
            );
        END IF;
        
        -- Extract option6 (if exists)
        IF q_options LIKE '%"option6":%' THEN
            SET @value = SUBSTRING_INDEX(SUBSTRING_INDEX(q_options, '"option6":"', -1), '"', 1);
            SET @is_correct = IF(q_correct LIKE CONCAT('%"', @value, '"%'), 1, 0);
            
            INSERT INTO `<db>`.`question_options` (
                question_id, option_key, content_type, content_value, text_fallback, is_correct
            ) VALUES (
                q_id, 'option6', 'text', @value, @value, @is_correct
            );
        END IF;
    END LOOP;
    
    CLOSE cur;
END //

DELIMITER ;

-- Execute the migration procedure
CALL `<db>`.`migrate_question_options`();

-- Drop the procedure after migration
DROP PROCEDURE IF EXISTS `<db>`.`migrate_question_options`;



-- 15-02-2025(Adding pass percentage column in assessment table)
ALTER TABLE assessments ADD COLUMN pass_percentage INT DEFAULT 60;

-- Add is_passed column to assessment_users table
ALTER TABLE assessment_users ADD COLUMN is_passed INT DEFAULT 0;


-- 18-05-2025
ALTER TABLE auth.organization MODIFY COLUMN authorized_person_email VARCHAR(255) NOT NULL UNIQUE;


ALTER TABLE `<db>`.payments ADD COLUMN receipt_url VARCHAR(255) DEFAULT NULL;
ALTER TABLE `<db>`.payments ADD COLUMN receipt_pdf_path VARCHAR(255) DEFAULT NULL;


-- 23-05-2025
ALTER TABLE `<db>`.users
ADD COLUMN auth_provider VARCHAR(50) NOT NULL DEFAULT 'manual';



CREATE TABLE `<db>`.classroom_trainer (
 id INT AUTO_INCREMENT PRIMARY KEY,
 classroom_id INT NOT NULL,
 user_id INT NOT NULL,
 created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
 updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
 FOREIGN KEY (classroom_id) REFERENCES classroom(id),
 FOREIGN KEY (user_id) REFERENCES users(id)
);


--31-05-2025
CREATE TABLE IF NOT EXISTS `<db>`.classroom_assignments (
        id INT AUTO_INCREMENT PRIMARY KEY,
        class_id INT NOT NULL,
        total_marks INT NOT NULL,
        created_by INT NOT NULL,
        title TEXT NOT NULL,
        description TEXT NULL,
        attachment_url VARCHAR(255) NULL,
        due_date DATETIME DEFAULT NULL,
        assigned_date DATETIME DEFAULT NULL,
        createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        is_deleted BOOLEAN DEFAULT FALSE,
        FOREIGN KEY (class_id) REFERENCES `<db>`.classroom(id) ON DELETE CASCADE ON UPDATE CASCADE,
        FOREIGN KEY (created_by) REFERENCES `<db>`.users(id) ON DELETE CASCADE ON UPDATE CASCADE
);
 
CREATE TABLE IF NOT EXISTS `<db>`.classroom_assignment_questions (
            id INT AUTO_INCREMENT PRIMARY KEY,
            assignment_id INT NOT NULL,
            class_id INT NOT NULL,
            question_text TEXT NOT NULL,
            question_type VARCHAR(50) NOT NULL,
            response_type VARCHAR(50) NOT NULL,
            media_url VARCHAR(255) NULL,
            marks INT DEFAULT 0,
            question_order INT DEFAULT 1,
            createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (assignment_id) REFERENCES `<db>`.classroom_assignments(id) ON DELETE CASCADE ON UPDATE CASCADE,
            FOREIGN KEY (class_id) REFERENCES `<db>`.classroom(id) ON DELETE CASCADE ON UPDATE CASCADE
        );
 
 CREATE TABLE IF NOT EXISTS `<db>`.classroom_assignment_submissions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        assignment_id INT NOT NULL,
        user_id INT NOT NULL,
        class_id INT NOT NULL,
        question_id INT NOT NULL,
        submission_url VARCHAR(255) NULL,
        response_type VARCHAR(50) NULL,
        marks_obtained INT NULL,
        answer_text TEXT NULL,
        answer_status ENUM('pending', 'right', 'wrong') DEFAULT 'pending',
        media_url VARCHAR(255) NULL,
        is_completed TINYINT(1) DEFAULT 0,
        assignment_feedback TEXT DEFAULT NULL,
        assignment_evaluation_status BOOLEAN DEFAULT FALSE,
        submittedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
        is_deleted BOOLEAN DEFAULT FALSE,
        FOREIGN KEY (assignment_id) REFERENCES `<db>`.classroom_assignments(id) ON DELETE CASCADE,
        FOREIGN KEY (user_id) REFERENCES `<db>`.users(id) ON DELETE CASCADE,
        FOREIGN KEY (class_id) REFERENCES `<db>`.classroom(id) ON DELETE CASCADE,
        FOREIGN KEY (question_id) REFERENCES `<db>`.classroom_assignment_questions(id) ON DELETE CASCADE
        );

ALTER TABLE `<db>`.token_blacklist 
MODIFY COLUMN token VARCHAR(2000);

ALTER TABLE admin_lms.token_blacklist 
MODIFY COLUMN token TEXT;

ALTER TABLE `<db>`.classroom_assignment_submissions
ADD COLUMN answer_status ENUM('pending', 'right', 'wrong') DEFAULT 'pending';


--31-05-2024
ALTER TABLE `<db>`.users ADD COLUMN otp_code VARCHAR(10);
ALTER TABLE `<db>`.users ADD COLUMN otp_expiry DATETIME NULL;


-- Add video reference and timestamp columns to questions table for linking questions with video content
-- Date: 02-06-2025
ALTER TABLE `<db>`.`questions`
ADD COLUMN `ref_video_id` INT NULL AFTER `question_media_url`,
ADD COLUMN `video_timestamp` VARCHAR(10) NULL AFTER `ref_video_id`,
ADD CONSTRAINT `fk_questions_video` FOREIGN KEY (`ref_video_id`) REFERENCES `<db>`.`videos`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;


-- Date: 03-06-2025
ALTER TABLE `admin_lms`.`notifications` 
ADD COLUMN `course_id` INT NULL AFTER `updatedAt`,
ADD INDEX `course_id_fk_2_idx` (`course_id` ASC) VISIBLE;
;
ALTER TABLE `admin_lms`.`notifications` 
ADD CONSTRAINT `course_id_fk_2`
  FOREIGN KEY (`course_id`)
  REFERENCES `admin_lms`.`courses` (`id`)
  ON DELETE CASCADE
  ON UPDATE CASCADE;


DROP TRIGGER IF EXISTS course_approval_notification;
DELIMITER //
CREATE TRIGGER course_approval_notification
AFTER UPDATE ON `<db>`.`courses`
FOR EACH ROW
BEGIN
    -- Check if course was just approved and activated
    IF (OLD.is_approved = 0 AND NEW.is_approved = 1 AND NEW.is_active = 1) THEN
        -- Insert notification for all trainees (role_id = 2)
        INSERT INTO `<db>`.`notifications` (
            user_id,
            send_from,
            title,
            body,
            course_id,
            is_deleted,
            is_read,
            createdAt,
            updatedAt
        )
        SELECT 
            u.id,
            NEW.created_by,
            'New Course Available',
            CONCAT('Course "', NEW.course_name, '" is now available for enrollment'),
            NEW.id, 
            0,
            0,
            NOW(),
            NOW()
        FROM `<db>`.`users` u
        JOIN `<db>`.`user_roles` ur ON u.id = ur.user_id
        WHERE ur.role_id = 2;
    END IF;
END//
DELIMITER ;

-- 05-26-2025 ABID
ALTER TABLE `<db>`.courses
ADD COLUMN is_rejected TINYINT(1) default 0,
ADD COLUMN send_request TINYINT(1) DEFAULT 0 AFTER is_rejected,
ADD COLUMN rejected_note TEXT AFTER send_request;

SET SQL_SAFE_UPDATES = 0;

UPDATE `<db>`.courses
SET send_request = 1
WHERE send_request = 0 AND id > 0;


UPDATE `<db>`.courses
SET courses.is_rejected = 0
WHERE courses.is_approved = 1 and id > 0;

UPDATE courses AS c
JOIN users AS u ON c.created_by = u.name
SET c.created_by = u.id;

SET SQL_SAFE_UPDATES = 1;



ALTER TABLE `<db>`.ticket
ADD COLUMN subject TEXT AFTER sender_id;

-- 20-06-2025
ALTER TABLE `<db>`.classroom_assessments
MODIFY COLUMN is_active TINYINT(1);

-- 14-06-2025 - WhatsApp-like Group Chat Features
-- Add new columns to group_messages table for enhanced chat functionality

-- Add message_type column to support different types of messages (text, image, video, audio, document, link)
ALTER TABLE `<db>`.group_messages
ADD COLUMN message_type ENUM('text', 'image', 'video', 'audio', 'document', 'link') DEFAULT 'text' AFTER message,
ADD COLUMN media_url VARCHAR(500) NULL AFTER attachment,
ADD COLUMN media_metadata JSON NULL AFTER media_url,
ADD COLUMN emoji_reactions JSON DEFAULT ('{}') AFTER media_metadata,
ADD COLUMN reply_to_message_id INT NULL AFTER emoji_reactions,
ADD CONSTRAINT fk_group_messages_reply
FOREIGN KEY (reply_to_message_id) REFERENCES `<db>`.group_messages(id) ON DELETE SET NULL;


-- 15-06-2024 Abid
ALTER TABLE questions
MODIFY COLUMN question TEXT NOT NULL;


-- Step 1: Add the new column after `video_name`
ALTER TABLE videos
ADD COLUMN upload_view_type VARCHAR(50) AFTER video_name;

-- Step 2: Disable safe update mode temporarily
SET SQL_SAFE_UPDATES = 0;

-- Step 3: Update all rows to set default value
UPDATE videos 
SET upload_view_type = 'uploaded';

-- Step 4: Re-enable safe update mode
SET SQL_SAFE_UPDATES = 1;


ALTER TABLE videos
MODIFY COLUMN video_name TEXT;


ALTER TABLE notes
MODIFY COLUMN note LONGTEXT;


-- 16-06-2025
ALTER TABLE questions
ADD COLUMN is_deleted TINYINT(1) DEFAULT 0 AFTER collect_point;


 
-- 22-06-2025 ABid added
ALTER TABLE ticket
ADD COLUMN is_deleted TINYINT(1) DEFAULT 0;

ALTER TABLE ticket
ADD COLUMN response TEXT DEFAULT NULL;

ALTER TABLE orginfo
ADD COLUMN is_deleted TINYINT(1) NOT NULL DEFAULT 0;




-- Abid added 26-06-2025 
CREATE TABLE classroom_resources (
  id INT AUTO_INCREMENT PRIMARY KEY,

  classroom_id INT NOT NULL,
  updated_by INT NOT NULL,

  resource_name VARCHAR(255) NOT NULL,
  resource_description TEXT,

  resource_type ENUM('text', 'image', 'audio', 'video', 'url', 'document') NOT NULL,
  media_url TEXT NULL,

  pushed_at TIMESTAMP NULL DEFAULT NULL,

  is_active TINYINT(1) DEFAULT 1,
  is_deleted TINYINT(1) DEFAULT 0,

  seen TINYINT(1) DEFAULT 0,
  seen_at TIMESTAMP NULL DEFAULT NULL,

  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

  FOREIGN KEY (classroom_id) REFERENCES classroom(id),
  FOREIGN KEY (updated_by) REFERENCES users(id)
);

CREATE TABLE task_list (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    task TEXT NOT NULL,
    marked TINYINT(1) DEFAULT 0,         -- 0 = not done, 1 = done
    is_deleted TINYINT(1) DEFAULT 0,     -- 0 = active, 1 = deleted (soft delete)
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    CONSTRAINT fk_user FOREIGN KEY (user_id) REFERENCES users(id)
);


-- 02-07-2025 
ALTER TABLE course_approval
ADD COLUMN is_rejected TINYINT(1) DEFAULT 0,
ADD COLUMN rejected_note TEXT;


------------------------------- need to update in createORGDB

-- 02-07-2025  ABID 
-- 1. Add courseLanguage column
ALTER TABLE courses 
ADD COLUMN course_language VARCHAR(50) NULL AFTER course_category;

-- 2. Add currency column  
ALTER TABLE courses 
ADD COLUMN currency VARCHAR(10) DEFAULT 'SGD' AFTER course_price;

-- 3. Add discountPrice column
ALTER TABLE courses 
ADD COLUMN discount_price VARCHAR(25) NULL AFTER currency;

-- 4. Add tags column (JSON format for storing array)
ALTER TABLE courses 
ADD COLUMN tags JSON NULL AFTER discount_price;

-- 5. Add courseInfo column (JSON format for storing array)
ALTER TABLE courses 
ADD COLUMN course_info JSON NULL AFTER tags;





-- 06-07-2025
ALTER TABLE orginfo
ADD COLUMN is_active TINYINT(1) NOT NULL DEFAULT 1;


ALTER TABLE faqs
ADD COLUMN is_deleted TINYINT(1) NOT NULL DEFAULT 0,
ADD COLUMN is_active TINYINT(1) NOT NULL DEFAULT 1;


ALTER TABLE announcements
MODIFY COLUMN message TEXT;



-- 08-07-205 
-- ALTER TABLE classroom
ADD COLUMN tab_visibility JSON NOT NULL DEFAULT (
  JSON_ARRAY(
    JSON_OBJECT('tab_name', 'assignment', 'is_visible', 'true'),
    JSON_OBJECT('tab_name', 'assessment', 'is_visible', 'true'),
    JSON_OBJECT('tab_name', 'liveclass', 'is_visible', 'true'),
    JSON_OBJECT('tab_name', 'recorded', 'is_visible', 'true'),
    JSON_OBJECT('tab_name', 'community', 'is_visible', 'true')
  )
);



ALTER TABLE classroom_assignments
ADD COLUMN is_visible TINYINT(1) NOT NULL DEFAULT 1 AFTER class_id;


ALTER TABLE classroom_assessments
ADD COLUMN is_visible TINYINT(1) NOT NULL DEFAULT 1 AFTER class_id;



CREATE TABLE task_lists (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id INT NOT NULL,
  classroom_id INT,
  assessment_id INT,
  assignment_id INT,
  is_seen TINYINT(1) DEFAULT 0,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

  -- Foreign Key Constraints (optional but recommended)
  FOREIGN KEY (classroom_id) REFERENCES classroom(id),
  FOREIGN KEY (assessment_id) REFERENCES classroom_assessments(id),
  FOREIGN KEY (assignment_id) REFERENCES classroom_assignments(id)
);



ALTER TABLE classroom_assignments
ADD COLUMN time_zone VARCHAR(100) NULL
AFTER attachment_url;



ALTER TABLE classroom_assessments
ADD COLUMN time_zone VARCHAR(100) NULL
AFTER pass_percentage;





------------------------------------------------------------ issue 


DROP TRIGGER IF EXISTS update_assessment_active_status;

DELIMITER $$

CREATE TRIGGER update_assessment_active_status
BEFORE UPDATE ON classroom_assessments
FOR EACH ROW
BEGIN
  -- Only auto-set is_active if the value hasn't changed manually
  IF NEW.is_time_based = 1 
     AND NEW.activate_time IS NOT NULL 
     AND NEW.deactivate_time IS NOT NULL
     AND NEW.is_active = OLD.is_active THEN
     
    IF NOW() BETWEEN NEW.activate_time AND NEW.deactivate_time THEN
      SET NEW.is_active = 1;
    ELSE
      SET NEW.is_active = 0;
    END IF;

  END IF;
END$$

DELIMITER ;



-- The BEFORE UPDATE trigger (update_assessment_active_status) overwrites your value of is_active = 1 based on the current time and the values of is_time_based, activate_time, and deactivate_time.

-- So even if you force is_active = 1, the trigger may turn it back to 0.



-- DROP TRIGGER IF EXISTS update_assessment_active_status;

-- DELIMITER $$

-- CREATE TRIGGER update_assessment_active_status
-- BEFORE UPDATE ON classroom_assessments
-- FOR EACH ROW
-- BEGIN
--   -- 🛡️ Only auto-calculate is_active if user didn’t explicitly change it
--   IF NEW.is_time_based = 1 
--      AND NEW.activate_time IS NOT NULL 
--      AND NEW.deactivate_time IS NOT NULL 
--      AND NEW.is_active = OLD.is_active THEN

--     IF NOW() BETWEEN NEW.activate_time AND NEW.deactivate_time THEN
--       SET NEW.is_active = 1;
--     ELSE
--       SET NEW.is_active = 0;
--     END IF;

--   END IF;
-- END$$

-- DELIMITER ;







-- UPDATE announcements
-- SET time_zone = 'Asia/Kolkata';


-- SET SQL_SAFE_UPDATES = 0;

-- UPDATE announcements
-- SET time_zone = 'Asia/Kolkata';

-- SET SQL_SAFE_UPDATES = 1;










-- -- 16: 36 PM
-- ALTER TABLE live_class
-- ADD COLUMN time_zone VARCHAR(100) AFTER start_time;


-- ALTER TABLE live_class
-- ADD COLUMN zoom_meeting_id VARCHAR(50) AFTER live_class_url;



-- CREATE TABLE classroom_live_class (
--     id INT AUTO_INCREMENT PRIMARY KEY,

--     -- Foreign key to the classroom
--     classroom_id INT NOT NULL,

--     -- Live class metadata
--     live_class_name VARCHAR(60) NOT NULL,
--     live_class_description TEXT,

--     -- Timing
--     time_zone VARCHAR(50) NOT NULL,
--     class_date DATE NOT NULL,
--     start_time DATETIME NOT NULL,
--     end_time DATETIME NOT NULL,
--     duration VARCHAR(30),  -- e.g. "1 hour 30 minutes"

--     -- Status
--     status VARCHAR(20) DEFAULT 'scheduled',  -- scheduled, live, completed, cancelled
--     is_started TINYINT(1) DEFAULT 0,

--     -- Zoom or platform links
--     host_url TEXT,          -- Zoom's start_url (for instructor)
--     join_url TEXT,          -- Zoom's join_url (for trainees)
--     meeting_id VARCHAR(50), -- Zoom meeting ID

--     -- Optional recording field
--     recording_url TEXT,

--     -- Audit metadata
--     created_by INT NOT NULL,
--     is_deleted TINYINT(1) DEFAULT 0,
--     created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
--     updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

--     -- Foreign key constraint
--     FOREIGN KEY (classroom_id) REFERENCES classroom(id)
--     ON DELETE CASCADE ON UPDATE CASCADE
-- );







--------------------------------------------------------------------------- 18 <USER> <GROUP> -2025 Friday

ALTER TABLE `lmsr`.`announcements` 
CHANGE COLUMN `title` `title` TEXT NOT NULL ;

ALTER TABLE `lmsr`.`notifications` 
CHANGE COLUMN `title` `title` TEXT NULL DEFAULT NULL ;

-- update this in table 

ALTER TABLE classroom
MODIFY COLUMN cls_name VARCHAR(100) COLLATE utf8mb4_general_ci;

ALTER TABLE classroom
MODIFY COLUMN cls_desc VARCHAR(500) COLLATE utf8mb4_general_ci;

ALTER TABLE classroom
MODIFY COLUMN banner_img VARCHAR(255) COLLATE utf8mb4_general_ci;

ALTER TABLE classroom
MODIFY COLUMN collaboration_name VARCHAR(255) COLLATE utf8mb4_general_ci;



DELIMITER $$

-- Trigger for 'classroom_assessment_questions' table (after insert)
CREATE TRIGGER update_assessment_question_count_insert
AFTER INSERT ON classroom_assessment_questions
FOR EACH ROW
BEGIN
    UPDATE `lmsr`.classroom_assessments
    SET total_questions = total_questions + 1
    WHERE id = NEW.assessment_id AND class_id = NEW.class_id;
END$$

-- Trigger for 'classroom_assessment_questions' table (after delete)
CREATE TRIGGER update_assessment_question_count_delete
AFTER DELETE ON classroom_assessment_questions
FOR EACH ROW
BEGIN
    UPDATE `lmsr`.classroom_assessments
    SET total_questions = GREATEST(0, total_questions - 1)
    WHERE id = OLD.assessment_id AND class_id = OLD.class_id;
END$$

-- Trigger for 'classroom_assessments' table (before insert)
CREATE TRIGGER update_assessment_active_status_insert
BEFORE INSERT ON classroom_assessments
FOR EACH ROW
BEGIN
    IF NEW.is_time_based = 1 AND NEW.activate_time IS NOT NULL AND NEW.deactivate_time IS NOT NULL AND NEW.is_active = 0 THEN
        IF NOW() BETWEEN NEW.activate_time AND NEW.deactivate_time THEN
            SET NEW.is_active = 1;
        ELSE
            SET NEW.is_active = 0;
        END IF;
    END IF;
END$$

-- Trigger for 'classroom_assessments' table (after insert)
CREATE TRIGGER after_classroom_assessment_insert
AFTER INSERT ON classroom_assessments
FOR EACH ROW
BEGIN
    DECLARE done INT DEFAULT 0;
    DECLARE current_user_id INT;
    DECLARE classroom_name VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;
    DECLARE assessment_name_var VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;

    DECLARE user_cursor CURSOR FOR 
        SELECT user_id
        FROM classroom_trainee
        WHERE class_id = NEW.class_id;

    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = 1;

    SELECT cls_name INTO classroom_name
    FROM classroom
    WHERE id = NEW.class_id AND is_deleted = 0;

    SET assessment_name_var = IFNULL(NEW.assessment_name, 'No Name');

    OPEN user_cursor;

    read_loop: LOOP
        FETCH user_cursor INTO current_user_id;
        IF done THEN
            LEAVE read_loop;
        END IF;

        INSERT INTO notifications (
            user_id,
            send_from,
            title,
            body,
            is_deleted,
            is_read,
            createdAt,
            updatedAt,
            course_id,
            classroom_id
        )
        VALUES (
            current_user_id,
            'System',
            CONCAT('New Assessment Created: ', assessment_name_var),
            CONCAT('A new assessment named "', assessment_name_var, '" has been created for the classroom "', IFNULL(classroom_name, 'Unknown Classroom'), '".'),
            0,
            0,
            NOW(),
            NOW(),
            NULL,
            NEW.class_id
        );
    END LOOP;

    CLOSE user_cursor;
END$$

-- Trigger for 'classroom_assessments' table (before update)
CREATE TRIGGER update_assessment_active_status
BEFORE UPDATE ON classroom_assessments
FOR EACH ROW
BEGIN
    IF NEW.is_time_based = 1 AND NEW.activate_time IS NOT NULL AND NEW.deactivate_time IS NOT NULL AND NEW.is_active = OLD.is_active THEN
        IF NOW() BETWEEN NEW.activate_time AND NEW.deactivate_time THEN
            SET NEW.is_active = 1;
        ELSE
            SET NEW.is_active = 0;
        END IF;
    END IF;
END$$

-- Trigger for 'classroom_assessments' table (after update)
CREATE TRIGGER assessment_activation_notification
AFTER UPDATE ON classroom_assessments
FOR EACH ROW
BEGIN
    DECLARE classroom_name VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;
    DECLARE assessment_name_var VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;

    IF (OLD.is_active = 0 AND NEW.is_active = 1) THEN
        SELECT classroom.cls_name INTO classroom_name
        FROM classroom 
        WHERE classroom.id = NEW.class_id;

        SET assessment_name_var = IFNULL(NEW.assessment_name, 'Unnamed Assessment');

        IF classroom_name IS NOT NULL THEN
            INSERT INTO notifications (user_id, send_from, title, body, is_deleted, is_read, createdAt, updatedAt, course_id, classroom_id)
            SELECT 
                classroom_trainee.user_id, 
                'System', 
                CONCAT('Assessment is Active now: ', assessment_name_var), 
                CONCAT('Assessment "', assessment_name_var, '" is now active in classroom "', classroom_name, '"'),
                0, 
                0, 
                NOW(), 
                NOW(),
                NULL,
                NEW.class_id
            FROM classroom_trainee 
            JOIN user_roles ON classroom_trainee.user_id = user_roles.user_id
            WHERE classroom_trainee.class_id = NEW.class_id AND user_roles.role_id = 2;
        END IF;
    END IF;
END$$

-- Trigger for 'classroom_assignments' table (after insert)
CREATE TRIGGER after_classroom_assignment_insert
AFTER INSERT ON classroom_assignments
FOR EACH ROW
BEGIN
    DECLARE done INT DEFAULT 0;
    DECLARE current_user_id INT;
    DECLARE classroom_name VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;
    DECLARE assignment_title_var VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;

    DECLARE user_cursor CURSOR FOR 
        SELECT user_id
        FROM classroom_trainee
        WHERE class_id = NEW.class_id;

    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = 1;

    SELECT cls_name INTO classroom_name
    FROM classroom
    WHERE id = NEW.class_id AND is_deleted = 0;

    SET assignment_title_var = IFNULL(NEW.title, 'No Title');

    OPEN user_cursor;

    read_loop: LOOP
        FETCH user_cursor INTO current_user_id;
        IF done THEN
            LEAVE read_loop;
        END IF;

        INSERT INTO notifications (
            user_id,
            send_from,
            title,
            body,
            is_deleted,
            is_read,
            createdAt,
            updatedAt,
            course_id,
            classroom_id
        )
        VALUES (
            current_user_id,
            'System',
            CONCAT('New Assignment: ', assignment_title_var),
            CONCAT('A new assignment titled "', assignment_title_var, '" has been created for the classroom "', IFNULL(classroom_name, 'Unknown Classroom'), '".'),
            0,
            0,
            NOW(),
            NOW(),
            NULL,
            NEW.class_id
        );
    END LOOP;

    CLOSE user_cursor;
END$$

-- Trigger for 'classroom_live_class' table (after insert)
CREATE TRIGGER after_classroom_live_class_insert
AFTER INSERT ON classroom_live_class
FOR EACH ROW
BEGIN
    DECLARE done INT DEFAULT 0;
    DECLARE current_user_id INT;
    DECLARE live_class_name_var VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;

    DECLARE user_cursor CURSOR FOR 
        SELECT user_id
        FROM classroom_trainee
        WHERE class_id = NEW.classroom_id;

    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = 1;

    SET live_class_name_var = IFNULL(NEW.live_class_name, 'Live Class');

    OPEN user_cursor;

    read_loop: LOOP
        FETCH user_cursor INTO current_user_id;
        IF done THEN
            LEAVE read_loop;
        END IF;

        INSERT INTO notifications (
            user_id,
            send_from,
            title,
            body,
            is_deleted,
            is_read,
            createdAt,
            updatedAt,
            course_id,
            classroom_id
        )
        VALUES (
            current_user_id,
            'System',
            CONCAT('New Live Class: ', live_class_name_var),
            CONCAT('A new live class named "', live_class_name_var, '" has been created for the course.'),
            0,
            0,
            NOW(),
            NOW(),
            NULL,
            NEW.classroom_id
        );
    END LOOP;

    CLOSE user_cursor;
END$$

-- Trigger for 'classroom_resources' table (after insert)
CREATE TRIGGER after_classroom_resource_insert
AFTER INSERT ON classroom_resources
FOR EACH ROW
BEGIN
    DECLARE done INT DEFAULT 0;
    DECLARE current_user_id INT;
    DECLARE classroom_name VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;
    DECLARE resource_name_var VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;

    DECLARE user_cursor CURSOR FOR 
        SELECT user_id
        FROM classroom_trainee
        WHERE class_id = NEW.classroom_id;

    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = 1;

    SELECT cls_name INTO classroom_name
    FROM classroom
    WHERE id = NEW.classroom_id AND is_deleted = 0;

    SET resource_name_var = IFNULL(NEW.resource_name, 'No Resource Name');

    OPEN user_cursor;

    read_loop: LOOP
        FETCH user_cursor INTO current_user_id;
        IF done THEN
            LEAVE read_loop;
        END IF;

        INSERT INTO notifications (
            user_id,
            send_from,
            title,
            body,
            is_deleted,
            is_read,
            createdAt,
            updatedAt,
            course_id,
            classroom_id
        )
        VALUES (
            current_user_id,
            'System',
            CONCAT('New Resource: ', resource_name_var),
            CONCAT('A new resource named "', resource_name_var, '" has been added to the classroom "', IFNULL(classroom_name, 'Unknown Classroom'), '".'),
            0,
            0,
            NOW(),
            NOW(),
            NULL,
            NEW.classroom_id
        );
    END LOOP;

    CLOSE user_cursor;
END$$

-- Trigger for 'classroom_trainee' table (after insert)
CREATE TRIGGER after_classroom_trainee_insert
AFTER INSERT ON classroom_trainee
FOR EACH ROW
BEGIN
    DECLARE trainee_name VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;
    DECLARE classroom_name VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;
    DECLARE welcome_message VARCHAR(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;

    SELECT name INTO trainee_name
    FROM users
    WHERE id = NEW.user_id;

    SELECT cls_name INTO classroom_name
    FROM classroom
    WHERE id = NEW.class_id AND is_deleted = 0;

    SET welcome_message = CONCAT('🎉 Welcome ', trainee_name, '! You have been successfully added to the classroom "', classroom_name, '" 🎉. Enjoy your learning journey! 📚');

    INSERT INTO notifications (
        user_id,
        send_from,
        title,
        body,
        is_deleted,
        is_read,
        createdAt,
        updatedAt,
        course_id,
        classroom_id
    )
    VALUES (
        NEW.user_id,
        'System',
        CONCAT('🎉 Welcome ', trainee_name, ' to the Classroom! 🎉'),
        welcome_message,
        0,
        0,
        NOW(),
        NOW(),
        NULL,
        NEW.class_id
    );
END$$

-- Trigger for 'classroom_trainee' table (after delete)
CREATE TRIGGER after_classroom_trainee_delete
AFTER DELETE ON classroom_trainee
FOR EACH ROW
BEGIN
    DECLARE trainee_name VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;
    DECLARE classroom_name VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;
    DECLARE sorry_message VARCHAR(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;

    SELECT name INTO trainee_name
    FROM users
    WHERE id = OLD.user_id;

    SELECT cls_name INTO classroom_name
    FROM classroom
    WHERE id = OLD.class_id AND is_deleted = 0;

    SET sorry_message = CONCAT('Sorry ', trainee_name, ' 😔! You have been removed from the classroom "', classroom_name, '" 😞.');

    INSERT INTO notifications (
        user_id,
        send_from,
        title,
        body,
        is_deleted,
        is_read,
        createdAt,
        updatedAt,
        course_id,
        classroom_id
    )
    VALUES (
        OLD.user_id,
        'System',
        CONCAT('Sorry ', trainee_name, ' 😔!'),
        sorry_message,
        0,
        0,
        NOW(),
        NOW(),
        NULL,
        OLD.class_id
    );
END$$

-- Trigger for 'classroom_trainer' table (after insert)
CREATE TRIGGER after_classroom_trainer_insert
AFTER INSERT ON classroom_trainer
FOR EACH ROW
BEGIN
    DECLARE trainer_name VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;
    DECLARE classroom_name VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;
    DECLARE welcome_message VARCHAR(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;

    SELECT name INTO trainer_name
    FROM users
    WHERE id = NEW.user_id;

    SELECT cls_name INTO classroom_name
    FROM classroom
    WHERE id = NEW.classroom_id AND is_deleted = 0;

    SET welcome_message = CONCAT('🎉 Congratulations ', trainer_name, '! You have been successfully added as a trainer to the classroom "', classroom_name, '" 🎉. Make learning amazing! 💡');

    INSERT INTO notifications (
        user_id,
        send_from,
        title,
        body,
        is_deleted,
        is_read,
        createdAt,
        updatedAt,
        course_id,
        classroom_id
    )
    VALUES (
        NEW.user_id,
        'System',
        CONCAT('🎉 Congratulations ', trainer_name, ' as a Trainer! 🎉'),
        welcome_message,
        0,
        0,
        NOW(),
        NOW(),
        NULL,
        NEW.classroom_id
    );
END$$

-- Trigger for 'ticket' table (after update)
CREATE TRIGGER after_ticket_update
AFTER UPDATE ON ticket
FOR EACH ROW
BEGIN
    DECLARE sender_name VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;
    DECLARE status_message VARCHAR(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;

    IF OLD.status != NEW.status THEN
        SELECT name INTO sender_name
        FROM users
        WHERE id = NEW.sender_id;

        SET status_message = CONCAT('Your issue status is "', NEW.status, '". Please check your ticket.');

        INSERT INTO notifications (
            user_id,
            send_from,
            title,
            body,
            is_deleted,
            is_read,
            createdAt,
            updatedAt,
            course_id,
            classroom_id
        )
        VALUES (
            NEW.sender_id,
            'System',
            CONCAT('Your ticket status is ', NEW.status),
            status_message,
            0,
            0,
            NOW(),
            NOW(),
            NULL,
            NULL
        );
    END IF;
END$$

DELIMITER ;

-- Drop all the listed triggers
DROP TRIGGER IF EXISTS update_assessment_question_count_insert;
DROP TRIGGER IF EXISTS update_assessment_question_count_delete;
DROP TRIGGER IF EXISTS update_assessment_active_status_insert;
DROP TRIGGER IF EXISTS after_classroom_assessment_insert;
DROP TRIGGER IF EXISTS update_assessment_active_status;
DROP TRIGGER IF EXISTS assessment_activation_notification;
DROP TRIGGER IF EXISTS after_classroom_assignment_insert;
DROP TRIGGER IF EXISTS after_classroom_live_class_insert;
DROP TRIGGER IF EXISTS after_classroom_resource_insert;
DROP TRIGGER IF EXISTS after_classroom_trainee_insert;
DROP TRIGGER IF EXISTS after_classroom_trainee_delete;
DROP TRIGGER IF EXISTS after_classroom_trainer_insert;
DROP TRIGGER IF EXISTS after_ticket_update;
