.todo-lit-card {
  height: 550px !important;
  margin-top: 0.5rem !important;
}

.modern-todo-card {
  background-color: white;
  border-radius: 12px;
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.todo-title {
  font-weight: 700;
  margin-bottom: 0.5rem;
  color: #333;
  font-size: 1.1rem;
  display: flex;
  align-items: center;
  border-bottom: 1px solid #e0e0e0;
  padding-bottom: 0.5rem;
}

.todo-controls {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.search-container {
  position: relative;
}

.search-icon {
  position: absolute;
  left: var(--spacing-sm);
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-secondary);
  font-size: 1.25rem;
}

.search-container {
  position: relative;
}

.search-icon {
  position: absolute;
  left: 10px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 2;
}

.search-input {
  padding-left: 35px !important;
}

.add-form {
  margin-bottom: 0;
}

.todo-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  height: calc(420px - 60px);
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: var(--border-color) transparent;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  /* margin-top: 2px; */
}

.todo-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background: white;
  transition: all 0.2s ease;
}

.todo-item:hover {
  border-color: #4361ee;
  box-shadow: 0 2px 4px rgba(67, 97, 238, 0.1);
}

.todo-left {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  flex: 1;
  min-width: 0;
}

.checkbox-container {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  min-width: fit-content;
  cursor: pointer;
}

.checkbox-container input[type="checkbox"] {
  width: 18px;
  height: 18px;
  margin: 0;
  cursor: pointer;
}

.checkbox-label {
  font-size: 0.75rem;
  color: #666;
  user-select: none;
}

.task-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 1rem;
  padding-left: 1.75rem; /* Align with checkbox text */
}

.todo-text {
  font-size: 0.875rem;
  color: #333;
  line-height: 1.4;
  flex: 1;
  min-width: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.todo-text.completed {
  text-decoration: line-through;
  color: #666;
}

.todo-actions {
  display: flex;
  gap: 0.5rem;
}

.action-btn {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  background: white;
  color: #666;
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-btn:hover {
  border-color: #4361ee;
  color: #4361ee;
  background: rgba(67, 97, 238, 0.05);
}

.view-btn {
  color: #1a73e8;
  border-color: #1a73e8;
}

.view-btn:hover {
  background-color: #e3f2fd;
  border-color: #1557b0;
}

.edit-btn {
  color: #ff9800;
  border-color: #ff9800;
}

.edit-btn:hover {
  background-color: #fff3e0;
  border-color: #f57c00;
}

.delete-btn {
  color: #f44336;
  border-color: #f44336;
}

.delete-btn:hover {
  background-color: #ffebee;
  border-color: #d32f2f;
}

.edit-form {
  display: flex;
  gap: var(--spacing-sm);
  width: 100%;
}

.edit-input {
  flex: 1;
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--primary-color);
  border-radius: var(--radius-md);
  font-size: 0.875rem;
  color: var(--text-primary);
  background: var(--bg-primary);
}

.save-btn {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  border-radius: var(--radius-md);
  background: var(--primary-color);
  color: white;
  cursor: pointer;
  transition: all 0.2s ease;
}

.save-btn:hover {
  background: var(--primary-hover);
}

.no-results {
  text-align: center;
  padding: var(--spacing-lg);
  color: var(--text-secondary);
}

.no-results p {
  margin: 0;
  font-style: italic;
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 12px;
  width: 90%;
  /* max-width: 500px; */
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #e0e0e0;
}

.modal-title {
  margin: 0;
  font-size: 1rem;
  font-weight: 500;
  color: #333;
}

.modal-title input[type="checkbox"] {
  width: 18px;
  height: 18px;
  margin: 0;
  cursor: pointer;
}

.close-btn {
  background: none;
  border: none;
  cursor: pointer;
  color: #666;
  font-size: 20px;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.close-btn:hover {
  background-color: #f5f5f5;
}

.modal-body {
  padding: 1.5rem;
  color: #666;
  font-size: 14px;
}

.task-detail {
  margin-bottom: 1rem;
}

.task-detail:last-child {
  margin-bottom: 0;
}

.task-detail label {
  display: block;
  font-weight: 600;
  color: #333;
  margin-bottom: 0.5rem;
  font-size: 14px;
}

.task-detail p {
  margin: 0;
  color: #666;
  font-size: 14px;
  line-height: 1.5;
}

.priority {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.priority.high {
  background-color: #ffebee;
  color: #c62828;
}

.priority.medium {
  background-color: #fff3e0;
  color: #ef6c00;
}

.priority.low {
  background-color: #e8f5e9;
  color: #2e7d32;
}

/* Custom scrollbar */
.modern-todo-list::-webkit-scrollbar {
  width: 6px;
}

.modern-todo-list::-webkit-scrollbar-track {
  background: transparent;
}

.modern-todo-list::-webkit-scrollbar-thumb {
  background-color: #e0e0e0;
  border-radius: 3px;
}

.modern-todo-list::-webkit-scrollbar-thumb:hover {
  background-color: #c0c0c0;
}

.clone-btn {
  background: var(--primary-color);
  border: none;
}

.clone-btn:hover {
  background: var(--primary-hover);
} 









