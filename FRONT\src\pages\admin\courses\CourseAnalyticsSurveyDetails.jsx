import React, { useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';

import { Icon } from '@iconify/react';
import { decodeData } from '../../../utils/encodeAndEncode';
import { singleSurveyDetails } from '../../../services/adminService';
import { toast } from 'react-toastify';

function CourseAnalyticsSurveyDetails() {
  const { surveyId, courseId } = useParams();
  const navigate = useNavigate();
  const decodedCourseId = courseId ? decodeData(courseId) : null;
  const decodedSurveyId = surveyId ? decodeData(surveyId) : null;

  const [surveyDetails, setSurveyDetails] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchSurveyDetails();
  }, [decodedSurveyId, decodedCourseId]);

  const fetchSurveyDetails = async () => {
    try {
      setLoading(true);

      console.log('🔍 Fetching survey details for:', {
        surveyId: surveyId,
        decodedSurveyId: decodedSurveyId
      });

      const response = await singleSurveyDetails({
          assessment_id: decodedSurveyId,
        course_id: decodedCourseId
      });

      console.log('📋 Survey Details Response:', response);

      if (response.success) {
        // Handle both cases: data exists or is empty array
        const surveyData = response.data || [];
        const totalQuestions = surveyData.length;
        let totalAttempts = 0;
        let totalRating = 0;
        let ratingCount = 0;

        surveyData.forEach(question => {
          totalAttempts += question.responses ? question.responses.length : 0;
          // If responses contain ratings, calculate average
          if (question.responses) {
            question.responses.forEach(resp => {
              if (resp.answer && !isNaN(resp.answer)) {
                totalRating += parseFloat(resp.answer);
                ratingCount++;
              }
            });
          }
        });

        const averageRating = ratingCount > 0 ? (totalRating / ratingCount).toFixed(1) : 0;
        const completionRate = totalQuestions > 0 ? Math.round((totalAttempts / totalQuestions) * 100) : 0;

        setSurveyDetails({
          id: decodedSurveyId,
          name: 'Survey Details',
          questions: totalQuestions,
          attempts: totalAttempts,
          averageRating: averageRating,
          completionRate: completionRate,
          data: surveyData,
          message: response.message || null
        });
      } else {
        console.error('❌ Survey details API failed:', response);
        toast.error(response.message || 'Failed to load survey details', {
          position: 'top-center',
          autoClose: 3000
        });
      }
    } catch (error) {
      console.error('Error fetching survey details:', error);
      toast.error('Failed to fetch survey details', {
        position: 'top-center',
        autoClose: 3000
      });
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="d-flex justify-content-center align-items-center" style={{ minHeight: '400px' }}>
        <div className="spinner-border text-primary" role="status">
          <span className="visually-hidden">Loading...</span>
        </div>
      </div>
    );
  }

  if (!surveyDetails) {
    return (
      <div className="text-center py-4">
        <p>No survey details found</p>
        <button
          className="btn btn-primary"
          onClick={() => navigate(-1)}
        >
          Go Back
        </button>
      </div>
    );
  }

  return (
    <>
      {/* Assessment Overview */}
      <div className="row mb-4">
        <div className="col-12">
          <div className="card">
            <div className="card-body">
              <div className="row g-3">
                <div className="col-md-3">
                  <div className="border rounded p-3">
                    <div className="text-muted mb-1">Total Questions</div>
                    <div className="h5 mb-0">{surveyDetails.questions}</div>
                  </div>
                </div>
                <div className="col-md-3">
                  <div className="border rounded p-3">
                    <div className="text-muted mb-1">Total Attempts</div>
                    <div className="h5 mb-0">{surveyDetails.attempts}</div>
                  </div>
                </div>
                <div className="col-md-3">
                  <div className="border rounded p-3">
                    <div className="text-muted mb-1">Average Rating</div>
                    <div className="h5 mb-0">{surveyDetails.averageRating}/5.0</div>
                  </div>
                </div>
                <div className="col-md-3">
                  <div className="border rounded p-3">
                    <div className="text-muted mb-1">Completion Rate</div>
                    <div className="h5 mb-0">{surveyDetails.completionRate}%</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Survey Questions and Responses */}
      <div className="row">
        <div className="col-12">
          <div className="card">
            <div className="card-body">
              <h5 className="card-title mb-4">Survey Questions & Responses</h5>

              {surveyDetails.data && surveyDetails.data.length > 0 ? (
                surveyDetails.data.map((question, index) => (
                  <div key={index} className="mb-4 p-3 border rounded">
                    <h6 className="mb-3">
                      <span className="badge bg-primary me-2">{index + 1}</span>
                      {question.question}
                    </h6>

                    {question.responses && question.responses.length > 0 ? (
                      <div className="table-responsive">
                        <table className="table table-sm">
                          <thead>
                            <tr>
                              <th>Name</th>
                              <th>Email</th>
                              <th>Response</th>
                            </tr>
                          </thead>
                          <tbody>
                            {question.responses.map((response, respIndex) => (
                              <tr key={respIndex}>
                                <td>{response.name || 'N/A'}</td>
                                <td>{response.email || 'N/A'}</td>
                                <td>{response.answer || 'No response'}</td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    ) : (
                      <p className="text-muted">No responses yet</p>
                    )}
                  </div>
                ))
              ) : (
                <div className="text-center py-4">
                  <Icon icon="fluent:clipboard-text-24-regular" width="48" height="48" className="text-muted mb-3" />
                  <h6 className="text-muted">No Questions Found</h6>
                  <p className="text-muted mb-0">
                    {surveyDetails.message || 'This survey does not have any questions yet.'}
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </>
  );
}

export default CourseAnalyticsSurveyDetails;
