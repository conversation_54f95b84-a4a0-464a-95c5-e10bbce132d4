.notifications-container {
    padding: 20px;
    /* max-width: 1200px; */
    margin: 0 auto;
  }
  
  .notifications-header {
    margin-bottom: 24px;
  }
  
  .notifications-header h2 {
    font-size: 24px;
    color: #333;
    margin: 0;
  }
  
  .notifications-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }
  
  .notification-item {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    transition: all 0.2s ease;
    border: 1px solid #eee;
    cursor: pointer;
  }
  
  .notification-item:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
    background: #fafafa;
  }
  
  .notification-item:focus {
    outline: none;
    border-color: #4A6CF7;
    box-shadow: 0 0 0 3px rgba(74, 108, 247, 0.1);
  }
  
  .notification-item.unread {
    background: #f8f9ff;
    border-left: 4px solid #4A6CF7;
  }
  
  .notification-item.unread:hover {
    background: #f0f2ff;
  }
  
  .notification-content {
    width: 100%;
  }
  
  .notification-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 8px;
  }
  
  .notification-title h3 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin: 0;
  }
  
  .unread-badge {
    background: #4A6CF7;
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
  }
  
  .notification-body {
    color: #666;
    font-size: 14px;
    line-height: 1.5;
    margin: 0 0 12px 0;
  }
  
  .notification-meta {
    display: flex;
    align-items: center;
    gap: 16px;
  }
  
  .notification-date {
    display: flex;
    align-items: center;
    gap: 6px;
    color: #888;
    font-size: 13px;
  }
  
  .mark-all-read {
    display: flex;
    align-items: center;
    gap: 8px;
    background: none;
    border: none;
    color: #3152e8;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    padding: 8px 12px;
    border-radius: 4px;
    transition: background-color 0.2s;
  }
  
  .mark-all-read:hover {
    background-color: rgba(49, 82, 232, 0.05);
  }
  
  .notification-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
  }
  
  @media (max-width: 768px) {
    .notifications-container {
      padding: 16px;
    }
    
    .notifications-header {
      margin-bottom: 16px;
    }
    
    .notification-item {
      padding: 14px 16px;
    }
    
    .notification-icon {
      width: 36px;
      height: 36px;
    }
    
    .notification-title {
      font-size: 15px;
    }
    
    .notification-message {
      font-size: 13px;
    }
  }
  
  .search-control {
    position: relative;
    margin-bottom: 20px;
  }
  
  .search-input {
    padding: 12px 16px;
    border-radius: 8px;
    border: 1px solid #e0e0e0;
    background: white;
    width: 100%;
    font-size: 14px;
    transition: all 0.2s ease;
  }
  
  .search-input:focus {
    border-color: #4A6CF7;
    box-shadow: 0 0 0 3px rgba(74, 108, 247, 0.1);
    outline: none;
  }
  
  .search-input::placeholder {
    color: #999;
  }
  