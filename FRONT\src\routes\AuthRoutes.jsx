import React from 'react';
import { Routes, Route } from 'react-router-dom';
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';

// Import auth pages
import Login from '../pages/auth/Login';
import Register from '../pages/auth/Register';
import RegisterOTP from '../pages/auth/RegisterOTP';
import ForgotPassword from '../pages/auth/ForgotPassword';
import ForgotPasswordOTP from '../pages/auth/ForgotPasswordOTP';
import ResetPassword from '../pages/auth/ResetPassword';
import ActiveAccountPage from '../pages/auth/ActiveAccountPage';
import Error401 from '../pages/error/Error401';
import PageNotFound from '../pages/error/PageNotFound';



function AuthRoutes() {
  return (
    <>
      <Routes>
        <Route path="/" element={<Login />} />
        <Route path="/auth/login" element={<Login />} />
        <Route path="/auth/register" element={<Register />} />
        <Route path="/auth/forgot-password" element={<ForgotPassword />} />
        <Route path="/auth/forgot-password-otp" element={<ForgotPasswordOTP />} />
        <Route path="/auth/register-otp" element={<RegisterOTP />} />
        <Route path="/auth/reset-password" element={<ResetPassword />} />
        <Route path="/auth/active-account" element={<ActiveAccountPage />} />


        {/* Error Routes */}
        <Route path="/error/401" element={<Error401 />} />
        <Route path="*" element={<PageNotFound />} />
      </Routes>
      <ToastContainer />
    </>
  );
}

export default AuthRoutes;