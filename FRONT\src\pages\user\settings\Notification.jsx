import React, { useState, useEffect } from 'react';
import { Icon } from '@iconify/react';
import './Notification.css';
import { Form, Button, Spinner } from 'react-bootstrap';
import { toast } from 'react-toastify';
import { getNotificationSettings, updateNotificationSettings } from '../../../services/userService';

function Notification() {
  const [settings, setSettings] = useState({
    emailNotifications: false,
    pushNotifications: false,
    invoiceEmails: false,
  });

  const [isSaving, setIsSaving] = useState(false); // ✅ NEW: control spinner + disable

  const handleToggle = (key) => {
    setSettings((prev) => ({
      ...prev,
      [key]: !prev[key],
    }));
  };

  useEffect(() => {
    fetchNotificationSettings();
  }, []);

  const fetchNotificationSettings = async () => {
    try {
      const response = await getNotificationSettings();
      if (response?.success && response?.data) {
        const data = response.data;
        setSettings({
          emailNotifications: data.mail_notification === 1,
          pushNotifications: data.push_notification === 1,
          invoiceEmails: data.invoice_mail === 1,
        });
      }
    } catch (error) {
      console.error('Error fetching notification settings:', error);
      toast.error('Failed to load notification settings');
    }
  };

  const handleSave = async () => {
    setIsSaving(true); // Show spinner
    try {
      const payload = {
        mail_notification: settings.emailNotifications ? 1 : 0,
        push_notification: settings.pushNotifications ? 1 : 0,
        invoice_mail: settings.invoiceEmails ? 1 : 0,
      };

      // Simulate delay
      setTimeout(async () => {
        const response = await updateNotificationSettings(payload);
        if (response.success) {
          toast.success(response.data.message || 'Preferences updated successfully!');
        } else {
          toast.error(response.data.message || 'Failed to update preferences.');
        }
        setIsSaving(false); // Hide spinner
      }, 2000);
    } catch (error) {
      console.error('Error updating notification settings:', error);
      toast.error('An error occurred while updating settings.');
      setIsSaving(false); // Hide spinner
    }
  };

  return (
    <div className="notification-settings-container">
      <div className="notification-channels">
        <h4>Notification Channels</h4>
        <p className="text-muted">Choose how you want to receive notifications</p>

        <div className="notification-toggle-group">
          {/* Email */}
          <div className="notification-toggle-item">
            <div className="toggle-info">
              <div className="toggle-icon">
                <Icon icon="mdi:email-outline" width="20" height="20" />
              </div>
              <div className="toggle-details">
                <h5>Email Notifications</h5>
                <p>Receive notifications via email</p>
              </div>
            </div>
            <div className="toggle-switch">
              <Form.Check
                type="switch"
                id="email-switch"
                checked={settings.emailNotifications}
                onChange={() => handleToggle('emailNotifications')}
              />
            </div>
          </div>

          {/* Push */}
          <div className="notification-toggle-item">
            <div className="toggle-info">
              <div className="toggle-icon">
                <Icon icon="mdi:bell-outline" width="20" height="20" />
              </div>
              <div className="toggle-details">
                <h5>Push Notifications</h5>
                <p>Receive notifications in your browser</p>
              </div>
            </div>
            <div className="toggle-switch">
              <Form.Check
                type="switch"
                id="push-switch"
                checked={settings.pushNotifications}
                onChange={() => handleToggle('pushNotifications')}
              />
            </div>
          </div>

          {/* Invoice Email */}
          <div className="notification-toggle-item">
            <div className="toggle-info">
              <div className="toggle-icon">
                <Icon icon="mdi:file-document-outline" width="20" height="20" />
              </div>
              <div className="toggle-details">
                <h5>Invoice Emails</h5>
                <p>Receive invoice updates via email</p>
              </div>
            </div>
            <div className="toggle-switch">
              <Form.Check
                type="switch"
                id="invoice-switch"
                checked={settings.invoiceEmails}
                onChange={() => handleToggle('invoiceEmails')}
              />
            </div>
          </div>
        </div>

        {/* Save Button */}
        <div className="row mt-4 justify-content-end">
          <div className="col-12 col-md-3">
            <Button
              className="btn btn-primary w-100"
              onClick={handleSave}
              disabled={isSaving}
            >
              {isSaving ? (
                <>
                  <Spinner
                    as="span"
                    animation="border"
                    size="sm"
                    role="status"
                    aria-hidden="true"
                    className="me-2"
                  />
                  Saving...
                </>
              ) : (
                'Save Preferences'
              )}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}

export default Notification;
