const { mysqlServerConnection } = require("../../../db/db");
const { formatDateTimeForMySQL } = require("../../../utils/dateFormat");



const addClassroomAssessment = async (req, res, next) => {
  const { 
    class_id,
    assessment_name,
    activate_time = null,
    deactivate_time = null,
    duration,
    reward_points,
    is_time_based = 0,
    is_active,
    pass_percentage = 100,
    is_visible = 1,
    time_zone = null
  } = req.body;


  console.log('assessment response',req.body);
  
  const dbName = req.user.db_name;

  try {
    if (!class_id || !assessment_name || !duration) {
      return res.status(400).json({
        success: false,
        message: "Missing required fields. class_id, assessment_name, duration are required",
      });
    }

    const [classExists] = await mysqlServerConnection.query(
      `SELECT id FROM ${dbName}.classroom WHERE id = ? AND is_deleted = 0`,
      [class_id]
    );

    if (classExists.length === 0) {
      return res.status(404).json({
        success: false,
        message: "Classroom not found or has been deleted",
      });
    }

    const activeValue = is_active === 1 || is_active === true ? 1 : 0;
    const timeBasedValue = is_time_based === 1 || is_time_based === true ? 1 : 0;
    const visibleValue = is_visible === 1 || is_visible === true ? 1 : 0;

    const validPassPercentage = pass_percentage
      ? Math.min(Math.max(0, parseInt(pass_percentage)), 100)
      : 100;

    // Convert ISO datetime to MySQL format (YYYY-MM-DD HH:mm:ss)
    const mysqlActivateTime = activate_time ? activate_time.replace('T', ' ').replace('Z', '') : null;
    const mysqlDeactivateTime = deactivate_time ? deactivate_time.replace('T', ' ').replace('Z', '') : null;

    const queryValues = [
      class_id,
      assessment_name,
      mysqlActivateTime,
      mysqlDeactivateTime,
      duration,
      timeBasedValue,
      activeValue,
      validPassPercentage,
      time_zone,
      visibleValue
    ];

    const [assessmentResult] = await mysqlServerConnection.query(
      `INSERT INTO ${dbName}.classroom_assessments 
        (class_id, assessment_name, activate_time, deactivate_time, duration, is_time_based, is_active, pass_percentage, time_zone, is_visible) 
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      queryValues
    );

    const insertedAssessmentId = assessmentResult.insertId;

    try {
      const [trainees] = await mysqlServerConnection.query(
        `SELECT user_id FROM ${dbName}.classroom_trainee WHERE class_id = ?`,
        [class_id]
      );

      if (trainees.length > 0) {
        const now = new Date();
        const taskData = trainees.map(({ user_id }) => [
          user_id,
          class_id,
          insertedAssessmentId,
          null,
          0,
          now,
          now
        ]);

        await mysqlServerConnection.query(
          `INSERT INTO ${dbName}.task_lists 
            (user_id, classroom_id, assessment_id, assignment_id, is_seen, created_at, updated_at) 
          VALUES ?`,
          [taskData]
        );
      }
    } catch (taskErr) {
      console.error("⚠️ Failed to create tasks for trainees:", taskErr);
    }

    return res.status(201).json({
      status: 201,
      success: true,
      message: "Classroom assessment added and tasks assigned",
      data: {
        id: insertedAssessmentId,
        class_id,
        assessment_name,
        activate_time,
        deactivate_time,
        duration,
        is_time_based: timeBasedValue,
        is_active: activeValue,
        pass_percentage: validPassPercentage,
        time_zone,
        is_visible: visibleValue
      },
    });

  } catch (error) {
    console.error("❗ Error adding classroom assessment:", error);
    return res.status(500).json({
      success: false,
      message: "An error occurred while adding classroom assessment",
      error: error.message,
    });
  }
};



const updateClassroomAssessment = async (req, res, next) => {
  const {
    class_id,
    assessment_id,
    assessment_name,
    activate_time,
    deactivate_time,
    duration,
    reward_points,
    is_active,
    is_time_based,
    pass_percentage,
    is_visible,
    time_zone // ✅ Accept from body
  } = req.body;

  const dbName = req.user.db_name;

  console.log("UPDATE CLASSROOM ASSESSMENT", req.body);

  if (!class_id || !assessment_id || !assessment_name || !duration) {
    return res.status(400).json({
      success: false,
      message:
        "Missing required fields. class_id, assessment_id, assessment_name, duration are required",
    });
  }

  try {
    const [classExists] = await mysqlServerConnection.query(
      `SELECT id FROM ${dbName}.classroom WHERE id = ? AND is_deleted = 0`,
      [class_id]
    );

    if (classExists.length === 0) {
      return res.status(404).json({
        success: false,
        message: "Classroom not found or has been deleted",
      });
    }

    const [assessmentExists] = await mysqlServerConnection.query(
      `SELECT id FROM ${dbName}.classroom_assessments WHERE id = ? AND is_deleted = 0`,
      [assessment_id]
    );

    if (assessmentExists.length === 0) {
      return res.status(404).json({
        success: false,
        message: "Assessment not found or has been deleted",
      });
    }

    let updateQuery = `UPDATE ${dbName}.classroom_assessments SET assessment_name = ?, activate_time = ?, deactivate_time = ?, duration = ?`;
    let queryParams = [
      assessment_name,
      formatDateTimeForMySQL(activate_time),
      formatDateTimeForMySQL(deactivate_time),
      duration
    ];

    if (is_active !== undefined) {
      const activeValue = is_active === 1 || is_active === true ? 1 : 0;
      updateQuery += `, is_active = ?`;
      queryParams.push(activeValue);
    }

    if (is_time_based !== undefined) {
      const timeBasedValue = is_time_based === 1 || is_time_based === true ? 1 : 0;
      updateQuery += `, is_time_based = ?`;
      queryParams.push(timeBasedValue);
    }

    if (pass_percentage !== undefined) {
      const validPassPercentage = Math.min(Math.max(0, parseInt(pass_percentage)), 100);
      updateQuery += `, pass_percentage = ?`;
      queryParams.push(validPassPercentage);
    }

    if (is_visible !== undefined) {
      const visibleValue = is_visible === 1 || is_visible === true ? 1 : 0;
      updateQuery += `, is_visible = ?`;
      queryParams.push(visibleValue);
    }

    if (time_zone !== undefined) {
      updateQuery += `, time_zone = ?`; // ✅ Add to query
      queryParams.push(time_zone);
    }

    updateQuery += ` WHERE id = ?`;
    queryParams.push(assessment_id);

    console.log("🛠️ Executing Update Query:", updateQuery);
    console.log("📦 With Params:", queryParams);

    const [assessment] = await mysqlServerConnection.query(updateQuery, queryParams);

    return res.status(200).json({
      success: true,
      message: "Classroom assessment updated successfully",
      assessment,
    });

  } catch (error) {
    console.error("🔥 Error updating classroom assessment:", error);
    return res.status(500).json({
      success: false,
      message: "An error occurred while updating classroom assessment",
      error: error.message,
    });
  }
};



const deleteClassroomAssessment = async (req, res, next) => {
  const { class_id, assessment_id } = req.body;
  const dbName = req.user.db_name;

  try {
    if (!class_id || !assessment_id) {
      return res.status(400).json({
        success: false,
        message:
          "Missing required fields. class_id and assessment_id are required",
      });
    }

    const [assessmentExists] = await mysqlServerConnection.query(
      `SELECT id from ${dbName}.classroom_assessments WHERE id = ? and class_id = ? AND is_deleted = 0`,
      [assessment_id, class_id]
    );

    if (assessmentExists.length === 0) {
      return res.status(404).json({
        success: false,
        message: "Assessment not found or has been deleted",
      });
    }

    const [assessment] = await mysqlServerConnection.query(
      `UPDATE ${dbName}.classroom_assessments SET is_deleted = 1 WHERE id = ? AND class_id = ?`,
      [assessment_id, class_id]
    );

    return res.status(200).json({
      status: 200,
      success: true,
      message: "Classroom assessment deleted successfully",
    });
  } catch (error) {
    res.status(500).json({
      status: 500,
      success: false,
      message: "An error occurred while deleting classroom assessment",
      error: error.message,
    });
  }
};

const getClassroomAssessments = async (req, res, next) => {
  const { class_id, page, limit, search } = req.body;
  const offset = (page - 1) * limit;
  const dbName = req.user.db_name;
  // console.log("USER REQUEST OBJECT",req.user);

  try {
    const [classExists] = await mysqlServerConnection.query(
      `SELECT id from ${dbName}.classroom WHERE id = ? AND is_deleted = 0`,
      [class_id]
    );

    if (classExists.length === 0) {
      return res.status(404).json({
        success: false,
        message: "Classroom not found or has been deleted",
      });
    }

    let queryParams = [class_id];
    let countParams = [class_id];

    let baseQuery = `SELECT * FROM ${dbName}.classroom_assessments WHERE class_id = ? AND is_deleted = 0`;
    let countQuery = `SELECT COUNT(*) as total FROM ${dbName}.classroom_assessments WHERE class_id = ? AND is_deleted = 0`;

    if (search) {
      baseQuery += ` AND assessment_name LIKE ?`;
      countQuery += ` AND assessment_name LIKE ?`;
      queryParams.push(`%${search}%`);
      countParams.push(`%${search}%`);
    }

    baseQuery += ` ORDER BY created_at DESC`;
    baseQuery += ` LIMIT ? OFFSET ?`;
    queryParams.push(Number(limit), Number(offset));
    
    const [assessments] = await mysqlServerConnection.query(
      baseQuery,
      queryParams
    );
    const [totalResults] = await mysqlServerConnection.query(
      countQuery,
      countParams
    );
    const totalRecords = totalResults[0].total;

    // Enhance each assessment with additional information
    const assessmentData = await Promise.all(assessments.map(async (assessment) => {
      // Get total questions for this assessment
      const [questionCount] = await mysqlServerConnection.query(
        `SELECT COUNT(*) as total_questions 
         FROM ${dbName}.classroom_assessment_questions 
         WHERE assessment_id = ? AND class_id = ?`,
        [assessment.id, class_id]
      );

      // Get users who completed this assessment
      const [completedUsers] = await mysqlServerConnection.query(
        `SELECT car.user_id, u.name, car.total_questions, car.total_attempted, 
                car.total_correct, car.percentage, car.created_at as submission_time 
         FROM ${dbName}.classroom_assessment_results car 
         JOIN ${dbName}.users u ON car.user_id = u.id 
         WHERE car.assessment_id = ? AND car.class_id = ? AND car.is_deleted = 0`,
        [assessment.id, class_id]
      );

      return {
        ...assessment,
        total_questions: questionCount[0].total_questions,
        completed_users: completedUsers,
        completed_count: completedUsers.length
      };
    }));

    return res.status(200).json({
      success: true,
      message: "Classroom assessments retrieved successfully",
      assessments: assessmentData,
      pagination: {
        totalRecords,
        totalPages: Math.ceil(totalRecords / limit),
        page: Number(page),
        limit: Number(limit),
      },
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "An error occurred while retrieving classroom assessments",
      error: error.message,
    });
  }
};
















const addQuestionsToAssessment = async (req, res) => {
  const { assessment_id, questionIds, class_id } = req.body;
  const dbName = req.user.db_name;

  if (
    !assessment_id ||
    !questionIds ||
    !Array.isArray(questionIds) ||
    !class_id
  ) {
    return res.status(400).json({
      status: 400,
      success: false,
      message: "Missing required fields",
    });
  }

  try {
    const [classExists] = await mysqlServerConnection.query(
      `SELECT id from ${dbName}.classroom WHERE id = ? AND is_deleted = 0`,
      [class_id]
    );

    if (classExists.length === 0) {
      return res.status(404).json({
        status: 404,
        success: false,
        message: "Classroom not found or has been deleted",
      });
    }

    const [assessmentExists] = await mysqlServerConnection.query(
      `SELECT id from ${dbName}.classroom_assessments WHERE id = ? AND is_deleted = 0`,
      [assessment_id]
    );

    if (assessmentExists.length === 0) {
      return res.status(404).json({
        status: 404,
        success: false,
        message: "Assessment not found or has been deleted",
      });
    }

    // Process questions sequentially to avoid deadlocks
    const results = [];
    
    for (const questionId of questionIds) {
      console.log("Processing question:", questionId);
      try {
        // Add a small delay between queries to further reduce contention
        if (results.length > 0) {
          await new Promise(resolve => setTimeout(resolve, 10));
        }
        
        const [result] = await mysqlServerConnection.query(
          `INSERT INTO ${dbName}.classroom_assessment_questions (assessment_id, question_id, class_id) 
           VALUES (?, ?, ?)
           ON DUPLICATE KEY UPDATE question_id = VALUES(question_id)`,
          [assessment_id, questionId, class_id]
        );
        console.log("Question added successfully:", questionId);
        
        results.push({ questionId, inserted: true });
      } catch (err) {
        console.error('Error adding question to assessment:', err);
        results.push({ questionId, inserted: false, error: err.message });
      }
    }

    return res.status(200).json({
      status: 200,
      success: true,
      message: "Questions added to assessment successfully",
      results,
    });
  } catch (error) {
    res.status(500).json({
      status: 500,
      success: false,
      message: "An error occurred while adding questions to assessment",
      error: error.message,
    });
  }
};



// Function to remove a question from an assessment
const removeQuestionFromAssessment = async (req, res) => {
  const { question_id, assessment_id, class_id } = req.body;
  const dbName = req.user.db_name;

  if (!question_id || !assessment_id || !class_id) {
    return res.status(400).json({
      status: 400,
      success: false,
      message:
        "Missing required fields: Question id, Assessment id, Classroom id",
    });
  }

  try {
    const [result] = await mysqlServerConnection.query(
      `DELETE FROM ${dbName}.classroom_assessment_questions WHERE assessment_id = ? AND question_id = ? AND class_id = ?`,
      [assessment_id, question_id, class_id]
    );
    return res.status(200).json({
      status: 200,
      success: true,
      message: "Question removed from assessment successfully",
    });
  } catch (error) {
    res.status(500).json({
      status: 500,
      success: false,
      message: "An error occurred while removing question from assessment",
      error: error.message,
    });
  }
};

const getTraineeAssessmentResults = async (req, res) => {
  try {
    const { class_id, assessment_id, page = 1, limit = 10, search = '' } = req.body;
    const dbName = req.user.db_name;
    const offset = (page - 1) * limit;

    // Validate required fields
    if (!class_id || !assessment_id) {
      return res.status(400).json({
        status: 400,
        success: false,
        message: 'Missing required fields: class_id and assessment_id are required',
      });
    }

    // Check if classroom exists
    const [classExists] = await mysqlServerConnection.query(
      `SELECT id FROM ${dbName}.classroom WHERE id = ? AND is_deleted = 0`,
      [class_id]
    );

    if (classExists.length === 0) {
      return res.status(404).json({
        status: 404,
        success: false,
        message: 'Classroom not found or has been deleted',
      });
    }

    // Check if assessment exists
    const [assessmentExists] = await mysqlServerConnection.query(
      `SELECT id FROM ${dbName}.classroom_assessments WHERE id = ? AND is_deleted = 0`,
      [assessment_id]
    );

    if (assessmentExists.length === 0) {
      return res.status(404).json({
        status: 404,
        success: false,
        message: 'Assessment not found or has been deleted',
      });
    }

    // Get total count of results for pagination
    let countQuery = `
      SELECT COUNT(*) as total 
      FROM ${dbName}.classroom_assessment_results car
      JOIN ${dbName}.users u ON car.user_id = u.id
      WHERE car.class_id = ? AND car.assessment_id = ? AND car.is_deleted = 0`;
    
    let countParams = [class_id, assessment_id];
    
    if (search) {
      countQuery += ` AND (u.name LIKE ? OR u.username LIKE ? OR u.email LIKE ? OR u.id LIKE ?)`;
      countParams.push(`%${search}%`, `%${search}%`, `%${search}%`, `%${search}%`);
    }
    
    const [totalResults] = await mysqlServerConnection.query(countQuery, countParams);
    const totalRecords = totalResults[0].total;

    // Get assessment results with pagination and search
    let query = `
    SELECT 
      car.id,
      car.user_id,
      u.name as username,
      car.total_questions,
      car.total_attempted as questions_attempted,
      car.total_correct as correct_questions,
      car.percentage,
      ca.pass_percentage as passing_percentage,
      car.created_at as attempt_date
    FROM ${dbName}.classroom_assessment_results car
    JOIN ${dbName}.users u ON car.user_id = u.id
    JOIN ${dbName}.classroom_assessments ca ON car.assessment_id = ca.id
    WHERE car.class_id = ? AND car.assessment_id = ? AND car.is_deleted = 0`;

    // console.log('!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!++!!!!!!!-------------',query);
    
    let params = [class_id, assessment_id];
    
    if (search) {
      query += ` AND u.name LIKE ? AND u.id LIKE ?`;
      params.push(`%${search}%`, `%${search}%`);
    }
    
    query += ` ORDER BY car.created_at DESC LIMIT ? OFFSET ?`;
    params.push(Number(limit), offset);
    
    const [results] = await mysqlServerConnection.query(query, params);
    
    const totalPages = Math.ceil(totalRecords / limit);

    return res.status(200).json({
      status: 200,
      success: true,
      message: 'Assessment results retrieved successfully',
      data: {
        results,
        pagination: {
          totalRecords,
          totalPages,
          currentPage: Number(page),
          limit: Number(limit),
        },
      },
    });

  } catch (error) {
    return res.status(500).json({
      status: 500,
      success: false,
      message: 'An error occurred while retrieving assessment results',
      error: error.message,
    });
  }
};

const getAssessmentResultsforTrainee = async (req, res) => {
  try {
    const { user_id, class_id, assessment_id, results_id } = req.body;
    const dbName = req.user.db_name;

    // Validate required fields
    if (!user_id || !class_id || !assessment_id || !results_id) {
      return res.status(400).json({
        status: 400,
        success: false,
        message: 'Missing required fields: user_id, class_id, assessment_id, and results_id are required',
      });
    }

    // Check if user exists
    const [userExists] = await mysqlServerConnection.query(
      `SELECT id FROM ${dbName}.users WHERE id = ? AND is_deleted = 0`,
      [user_id]
    );

    if (userExists.length === 0) {
      return res.status(404).json({
        status: 404,
        success: false,
        message: 'User not found or has been deleted',
      });
    }

    // Check if classroom exists
    const [classExists] = await mysqlServerConnection.query(
      `SELECT id FROM ${dbName}.classroom WHERE id = ? AND is_deleted = 0`,
      [class_id]
    );

    if (classExists.length === 0) {
      return res.status(404).json({
        status: 404,
        success: false,
        message: 'Classroom not found or has been deleted',
      });
    }

    // Check if assessment exists
    const [assessmentExists] = await mysqlServerConnection.query(
      `SELECT id FROM ${dbName}.classroom_assessments WHERE id = ? AND is_deleted = 0`,
      [assessment_id]
    );

    if (assessmentExists.length === 0) {
      return res.status(404).json({
        status: 404,
        success: false,
        message: 'Assessment not found or has been deleted',
      });
    }

    // Check if user has attempted the assessment
    const [assessmentAttempted] = await mysqlServerConnection.query(
      `SELECT * FROM ${dbName}.classroom_assessment_results 
       WHERE user_id = ? AND class_id = ? AND assessment_id = ? AND is_deleted = 0 AND id = ?`,
      [user_id, class_id, assessment_id, results_id]
    );

    if (assessmentAttempted.length === 0) {
      return res.status(404).json({
        status: 404,
        success: false,
        message: 'No assessment attempt found for this user',
      });
    }

    // Get assessment summary
    const assessmentSummary = assessmentAttempted[0];

    // Get all questions for this assessment
    const [assessmentQuestions] = await mysqlServerConnection.query(
      `SELECT 
        caq.question_id,
        qb.question_name,
        qb.question_type
       FROM ${dbName}.classroom_assessment_questions caq
       JOIN ${dbName}.question_bank qb ON caq.question_id = qb.id
       WHERE caq.class_id = ? AND caq.assessment_id = ?`,
      [class_id, assessment_id]
    );

    // Get user's answers for this assessment
    const [userAnswers] = await mysqlServerConnection.query(
      `SELECT 
        question_id,
        answer,
        is_correct
       FROM ${dbName}.classroom_question_assessment_answer
       WHERE user_id = ? AND class_id = ? AND assessment_id = ? AND results_id = ?`,
      [user_id, class_id, assessment_id, results_id]
    );

    // Create a map of user answers for quick lookup
    const userAnswersMap = {};
    userAnswers.forEach(answer => {
      userAnswersMap[answer.question_id] = {
        answer: answer.answer,
        is_correct: answer.is_correct
      };
    });

    // Process each question with its options and user's answer
    const questionDetails = await Promise.all(
      assessmentQuestions.map(async (question) => {
        // Get options for this question
        const [options] = await mysqlServerConnection.query(
          `SELECT 
            id,
            option_number,
            option_value,
            is_correct
           FROM ${dbName}.question_bank_options
           WHERE question_id = ? AND is_deleted = 0
           ORDER BY option_number ASC`,
          [question.question_id]
        );

        // Find the correct option
        const correctOption = options.find(opt => opt.is_correct);
        
        // Check if user attempted this question
        const isAttempted = userAnswersMap.hasOwnProperty(question.question_id);
        
        // Find the user selected option if question was attempted
        let userSelectedOption = null;
        if (isAttempted) {
          try {
            // Parse the answer which might be stored as a string
            const answerValue = JSON.parse(userAnswersMap[question.question_id].answer);
            userSelectedOption = options.find(opt => opt.option_number === parseInt(answerValue) || 
                                               opt.id === parseInt(answerValue));
          } catch (e) {
            // If parsing fails, try direct comparison
            userSelectedOption = options.find(opt => opt.option_number === parseInt(userAnswersMap[question.question_id].answer) || 
                                             opt.id === parseInt(userAnswersMap[question.question_id].answer));
          }
        }

        return {
          question_id: question.question_id,
          question_name: question.question_name,
          question_type: question.question_type,
          is_attempted: isAttempted ? 1 : 0,
          options: options.map(opt => ({
            option_id: opt.id,
            option_number: opt.option_number,
            option_value: opt.option_value
          })),
          user_selected_option: userSelectedOption ? {
            option_id: userSelectedOption.id,
            option_number: userSelectedOption.option_number,
            option_value: userSelectedOption.option_value
          } : null,
          correct_option: correctOption ? {
            option_id: correctOption.id,
            option_number: correctOption.option_number,
            option_value: correctOption.option_value
          } : null,
          is_correct: isAttempted ? userAnswersMap[question.question_id].is_correct : 0
        };
      })
    );

    // Prepare the response
    return res.status(200).json({
      status: 200,
      success: true,
      message: 'Assessment results retrieved successfully',
      data: {
        summary: {
          total_questions: assessmentSummary.total_questions,
          total_attempted: assessmentSummary.total_attempted,
          total_correct: assessmentSummary.total_correct,
          percentage: assessmentSummary.percentage,
          pass_percentage: assessmentSummary.pass_percentage,
          result_status: assessmentSummary.percentage >= assessmentSummary.pass_percentage ? 'Pass' : 'Fail',
          attempt_date: assessmentSummary.created_at
        },
        questions: questionDetails
      }
    });
  } catch (error) {
    console.error('Error retrieving assessment results:', error);
    return res.status(500).json({
      status: 500,
      success: false,
      message: 'An error occurred while retrieving assessment results',
      error: error.message,
    });
  }
};


const copyClassroomAssessment = async (req, res, next) => {
  const { current_classroom_id, copy_classroom_ids, assessment_id } = req.body;
  const dbName = req.user.db_name;

  try {
    // Validate required parameters
    if (!current_classroom_id || !copy_classroom_ids || !assessment_id) {
      return res.status(400).json({
        success: false,
        message: "Missing required fields. current_classroom_id, copy_classroom_ids, and assessment_id are required",
      });
    }

    // Ensure copy_classroom_ids is an array
    const targetClassroomIds = Array.isArray(copy_classroom_ids) 
      ? copy_classroom_ids 
      : [copy_classroom_ids]; // Handle both array and single value

    if (targetClassroomIds.length === 0) {
      return res.status(400).json({
        success: false,
        message: "copy_classroom_ids must contain at least one classroom ID",
      });
    }

    // Check if source classroom exists
    const [sourceClassExists] = await mysqlServerConnection.query(
      `SELECT id, cls_name FROM ${dbName}.classroom WHERE id = ? AND is_deleted = 0`,
      [current_classroom_id]
    );

    if (sourceClassExists.length === 0) {
      return res.status(404).json({
        success: false,
        message: "Source classroom not found or has been deleted",
      });
    }

    const sourceClassName = sourceClassExists[0].cls_name;

    // Check if assessment exists
    const [assessmentExists] = await mysqlServerConnection.query(
      `SELECT * FROM ${dbName}.classroom_assessments WHERE id = ? AND class_id = ? AND is_deleted = 0`,
      [assessment_id, current_classroom_id]
    );

    if (assessmentExists.length === 0) {
      return res.status(404).json({
        success: false,
        message: "Assessment not found or has been deleted",
      });
    }

    const originalAssessment = assessmentExists[0];
    
    // Get all questions associated with the original assessment
    const [assessmentQuestions] = await mysqlServerConnection.query(
      `SELECT question_id FROM ${dbName}.classroom_assessment_questions 
       WHERE assessment_id = ? AND class_id = ?`,
      [assessment_id, current_classroom_id]
    );

    // Process each target classroom
    const copyResults = [];
    
    for (const targetClassroomId of targetClassroomIds) {
      try {
        // Check if target classroom exists
        const [targetClassExists] = await mysqlServerConnection.query(
          `SELECT id, cls_name FROM ${dbName}.classroom WHERE id = ? AND is_deleted = 0`,
          [targetClassroomId]
        );

        if (targetClassExists.length === 0) {
          copyResults.push({
            classroom_id: targetClassroomId,
            success: false,
            message: "Target classroom not found or has been deleted"
          });
          continue; // Skip to next classroom
        }

        const targetClassName = targetClassExists[0].class_name;
    // date and time
    const currentDate = new Date();
        const formattedDate = `${currentDate.getFullYear()}-${String(currentDate.getMonth() + 1).padStart(2, '0')}-${String(currentDate.getDate()).padStart(2, '0')}`;
        const formattedTime = `${String(currentDate.getHours()).padStart(2, '0')}:${String(currentDate.getMinutes()).padStart(2, '0')}:${String(currentDate.getSeconds()).padStart(2, '0')}`;
        
        // Create a new assessment name with the copy suffix
        const newAssessmentName = `${originalAssessment.assessment_name} (copy from ${sourceClassName}-${formattedDate}-${formattedTime})`;

        // Insert the new assessment
        const [newAssessment] = await mysqlServerConnection.query(
          `INSERT INTO ${dbName}.classroom_assessments 
          (class_id, assessment_name, activate_time, deactivate_time, duration, is_time_based, is_active, pass_percentage) 
          VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
          [
            targetClassroomId,
            newAssessmentName,
            originalAssessment.activate_time,
            originalAssessment.deactivate_time,
            originalAssessment.duration,
            originalAssessment.is_time_based,
            originalAssessment.is_active,
            originalAssessment.pass_percentage
          ]
        );

        const newAssessmentId = newAssessment.insertId;

        // Copy all questions to the new assessment
        let questionResults = [];
        
        if (assessmentQuestions.length > 0) {
          const questionInsertPromises = assessmentQuestions.map(async (question) => {
            try {
              await mysqlServerConnection.query(
                `INSERT INTO ${dbName}.classroom_assessment_questions (assessment_id, question_id, class_id) 
                VALUES (?, ?, ?)`,
                [newAssessmentId, question.question_id, targetClassroomId]
              );
              return { question_id: question.question_id, copied: true };
            } catch (err) {
              console.error('Error copying question to new assessment:', err);
              return { question_id: question.question_id, copied: false, error: err.message };
            }
          });

          questionResults = await Promise.all(questionInsertPromises);
        }

        copyResults.push({
          classroom_id: targetClassroomId,
          classroom_name: targetClassName,
          success: true,
          new_assessment_id: newAssessmentId,
          new_assessment_name: newAssessmentName,
          questions_copied: questionResults.filter(q => q.copied).length,
          total_questions: assessmentQuestions.length
        });
        
      } catch (error) {
        console.error(`Error copying to classroom ${targetClassroomId}:`, error);
        copyResults.push({
          classroom_id: targetClassroomId,
          success: false,
          message: `Error copying assessment: ${error.message}`
        });
      }
    }

    // Return overall results
    const successCount = copyResults.filter(result => result.success).length;
    
    return res.status(200).json({
      success: successCount > 0,
      message: `Assessment copied to ${successCount} out of ${targetClassroomIds.length} classrooms`,
      copy_results: copyResults
    });
    
  } catch (error) {
    console.error("Error in copyClassroomAssessment:", error);
    res.status(500).json({
      success: false,
      message: "An error occurred while copying classroom assessment",
      error: error.message,
    });
  }
};

const getTraineeClassroomAssessments = async (req, res, next) => {
  const { class_id, page = 1, limit = 10, search = "", startDate = null, endDate = null } = req.body;
  const offset = (page - 1) * limit;
  const dbName = req.user.db_name;

  console.log("📥 Request Body:", req.body);
  console.log("📌 DB Name:", dbName);
  console.log("🧪 class_id:", class_id);
  console.log("📄 Pagination — Page:", page, "Limit:", limit, "Offset:", offset);
  console.log("🔎 Filters — Search:-", search, "| Start:", startDate, "| End:", endDate);

  if (!class_id) {
    console.warn("❌ Missing class_id");
    return res.status(400).json({
      success: false,
      message: "class_id is required",
    });
  }

  try {
    const [classExists] = await mysqlServerConnection.query(
      `SELECT id from ${dbName}.classroom WHERE id = ? AND is_deleted = 0`,
      [class_id]
    );

    console.log("🏫 Classroom exists:", classExists.length > 0);

    if (classExists.length === 0) {
      return res.status(404).json({
        success: false,
        message: "Classroom not found or has been deleted",
      });
    }

    let queryParams = [class_id];
    let countParams = [class_id];

    let baseQuery = `
      SELECT 
        ca.id,
        ca.class_id,
        ca.is_visible,
        ca.assessment_name,
        ca.duration,
        ca.pass_percentage,
        ca.time_zone,
        CAST(ca.activate_time AS CHAR) as activate_time,
        CAST(ca.deactivate_time AS CHAR) as deactivate_time,
        CAST(ca.created_at AS CHAR) as created_at,
        CAST(ca.updated_at AS CHAR) as updated_at,
        ca.is_active,
        ca.is_deleted,
        ca.is_time_based,
        c.cls_name as classroom_name
      FROM ${dbName}.classroom_assessments ca
      JOIN ${dbName}.classroom c ON ca.class_id = c.id
      WHERE ca.class_id = ? AND ca.is_deleted = 0`;
    
    let countQuery = `
      SELECT COUNT(*) as total 
      FROM ${dbName}.classroom_assessments ca
      JOIN ${dbName}.classroom c ON ca.class_id = c.id
      WHERE ca.class_id = ? AND ca.is_deleted = 0`;

    if (search) {
      console.log("🔍 Applying search filter:", search);
      baseQuery += ` AND ca.assessment_name LIKE ?`;
      countQuery += ` AND ca.assessment_name LIKE ?`;
      queryParams.push(`%${search}%`);
      countParams.push(`%${search}%`);
    }

    if (startDate) {
      const formattedStart = new Date(startDate).toISOString().slice(0, 19).replace('T', ' ');
      console.log("📅 Applying startDate filter:", formattedStart);
      baseQuery += ` AND ca.created_at >= ?`;
      countQuery += ` AND ca.created_at >= ?`;
      queryParams.push(formattedStart);
      countParams.push(formattedStart);
    }

    if (endDate) {
      const formattedEnd = new Date(endDate).toISOString().slice(0, 19).replace('T', ' ');
      console.log("📅 Applying endDate filter:", formattedEnd);
      baseQuery += ` AND ca.created_at <= ?`;
      countQuery += ` AND ca.created_at <= ?`;
      queryParams.push(formattedEnd);
      countParams.push(formattedEnd);
    }

    baseQuery += ` ORDER BY ca.created_at DESC LIMIT ? OFFSET ?`;
    queryParams.push(Number(limit), Number(offset));

    console.log("📤 Executing baseQuery:", baseQuery);
    console.log("📦 queryParams:", queryParams);
    console.log("📤 Executing countQuery:", countQuery);
    console.log("📦 countParams:", countParams);

    const [assessments] = await mysqlServerConnection.query(baseQuery, queryParams);
    const [totalResults] = await mysqlServerConnection.query(countQuery, countParams);

    console.log("📊 Assessments Fetched:", assessments.length);
    console.log("📈 Total Records:", totalResults[0].total);

    const enhancedAssessments = await Promise.all(assessments.map(async (assessment) => {
      console.log(`🔁 Processing Assessment ID: ${assessment.id}`);
    
      const [questionCount] = await mysqlServerConnection.query(
        `SELECT COUNT(*) as total_questions 
         FROM ${dbName}.classroom_assessment_questions 
         WHERE assessment_id = ? AND class_id = ?`,
        [assessment.id, class_id]
      );
      console.log(`🧮 Questions in Assessment ${assessment.id}:`, questionCount[0].total_questions);
    
      const [completedUsersCount] = await mysqlServerConnection.query(
        `SELECT COUNT(DISTINCT user_id) as completed_count
         FROM ${dbName}.classroom_assessment_results 
         WHERE assessment_id = ? AND class_id = ? AND is_deleted = 0`,
        [assessment.id, class_id]
      );
      console.log(`👥 Users completed Assessment ${assessment.id}:`, completedUsersCount[0].completed_count);
    
      const enriched = {
        ...assessment,
        total_questions: questionCount[0].total_questions,
        completed_count: completedUsersCount[0].completed_count
      };
    
      console.log("📦 Final Enriched Assessment:", enriched);
      return enriched;
    }));
    

    console.log("✅ All assessments processed successfully");

    return res.status(200).json({
      success: true,
      message: "Classroom assessments retrieved successfully",
      assessments: enhancedAssessments,
      pagination: {
        totalRecords: totalResults[0].total,
        totalPages: Math.ceil(totalResults[0].total / limit),
        page: Number(page),
        limit: Number(limit),
      },
    });
  } catch (error) {
    console.error("❌ Error in getTraineeClassroomAssessments:", error);
    return res.status(500).json({
      success: false,
      message: "An error occurred while retrieving classroom assessments",
      error: error.message,
    });
  }
};











const getAssessmentQuestions = async (req, res, next) => {
  const { class_id, assessment_id, page = 1, limit = 10, search = "" } = req.body;
  const offset = (page - 1) * limit;
  const dbName = req.user.db_name;

  // Validate required parameters
  if (!class_id || !assessment_id) {
    return res.status(400).json({
      status: 400,
      success: false,
      message: "class_id and assessment_id are required",
    });
  }

  try {
    // Check if classroom exists
    const [classExists] = await mysqlServerConnection.query(
      `SELECT id from ${dbName}.classroom WHERE id = ? AND is_deleted = 0`,
      [class_id]
    );

    if (classExists.length === 0) {
      return res.status(404).json({
        success: false,
        message: "Classroom not found or has been deleted",
      });
    }

    // Check if assessment exists
    const [assessmentExists] = await mysqlServerConnection.query(
      `SELECT id from ${dbName}.classroom_assessments WHERE id = ? AND class_id = ? AND is_deleted = 0`,
      [assessment_id, class_id]
    );

    if (assessmentExists.length === 0) {
      return res.status(404).json({
        success: false,
        message: "Assessment not found or has been deleted",
      });
    }

    let queryParams = [assessment_id, class_id];
    let countParams = [assessment_id, class_id];

    let baseQuery = `
      SELECT caq.id as question_assessment_id, qb.id as question_id, qb.question_name, qb.question_type
      FROM ${dbName}.classroom_assessment_questions caq
      JOIN ${dbName}.question_bank qb ON caq.question_id = qb.id
      WHERE caq.assessment_id = ? AND caq.class_id = ? AND qb.is_deleted = 0`;
    
    let countQuery = `
      SELECT COUNT(*) as total
      FROM ${dbName}.classroom_assessment_questions caq
      JOIN ${dbName}.question_bank qb ON caq.question_id = qb.id
      WHERE caq.assessment_id = ? AND caq.class_id = ? AND qb.is_deleted = 0`;

    // Add search condition if provided
    if (search) {
      baseQuery += ` AND qb.question_name LIKE ?`;
      countQuery += ` AND qb.question_name LIKE ?`;
      queryParams.push(`%${search}%`);
      countParams.push(`%${search}%`);
    }

    // Add ordering and pagination
    baseQuery += ` ORDER BY caq.id ASC LIMIT ? OFFSET ?`;
    queryParams.push(Number(limit), Number(offset));

    // Execute queries
    const [questions] = await mysqlServerConnection.query(baseQuery, queryParams);
    const [totalResults] = await mysqlServerConnection.query(countQuery, countParams);
    const totalRecords = totalResults[0].total;

    // Fetch options for each question
    const questionsWithOptions = await Promise.all(questions.map(async (question) => {
      const [options] = await mysqlServerConnection.query(
        `SELECT id, option_value, is_correct
         FROM ${dbName}.question_bank_options
         WHERE question_id = ? AND is_deleted = 0
         ORDER BY id ASC`,
        [question.question_id]
      );

      return {
        ...question,
        options
      };
    }));

    return res.status(200).json({
      success: true,
      message: "Assessment questions retrieved successfully",
      questions: questionsWithOptions,
      pagination: {
        totalRecords,
        totalPages: Math.ceil(totalRecords / limit),
        page: Number(page),
        limit: Number(limit),
      },
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "An error occurred while retrieving assessment questions",
      error: error.message,
    });
  }
};

const getAvailableQuestionsForAssessment = async (req, res) => {
  const { class_id, assessment_id, page = 1, limit = 10, search = '' } = req.body;
  const dbName = req.user.db_name;
  const offset = (page - 1) * limit;

  if (!class_id || !assessment_id) {
    return res.status(400).json({
      status: 400,
      success: false,
      message: 'Missing required fields: class_id and assessment_id are required'
    });
  }

  try {
    // Check if classroom exists
    const [classExists] = await mysqlServerConnection.query(
      `SELECT id FROM ${dbName}.classroom WHERE id = ? AND is_deleted = 0`,
      [class_id]
    );

    if (classExists.length === 0) {
      return res.status(404).json({
        status: 404,
        success: false,
        message: 'Classroom not found or has been deleted'
      });
    }

    // Check if assessment exists
    const [assessmentExists] = await mysqlServerConnection.query(
      `SELECT id FROM ${dbName}.classroom_assessments WHERE id = ? AND is_deleted = 0`,
      [assessment_id]
    );

    if (assessmentExists.length === 0) {
      return res.status(404).json({
        status: 404,
        success: false,
        message: 'Assessment not found or has been deleted'
      });
    }

    // Build the query to get questions that are not in classroom_assessment_questions
    let query = `
      SELECT qb.id, qb.question_name, qb.question_type, qb.subject, qb.module, qb.tags, qb.is_active
      FROM ${dbName}.question_bank qb
      LEFT JOIN ${dbName}.classroom_assessment_questions caq ON qb.id = caq.question_id AND caq.class_id = ? AND caq.assessment_id = ?
      WHERE qb.is_deleted = 0 AND qb.is_active = 1 AND caq.id IS NULL`;

    const queryParams = [class_id, assessment_id];

    // Add search condition if provided
    if (search) {
      query += ` AND (qb.question_name LIKE ? OR qb.question_type LIKE ? OR qb.subject LIKE ? OR qb.module LIKE ? OR qb.tags LIKE ?)`;
      queryParams.push(`%${search}%`, `%${search}%`, `%${search}%`, `%${search}%`, `%${search}%`);
    }

    // Add ORDER BY clause
    query += ` ORDER BY qb.id ASC LIMIT ? OFFSET ?`;
    queryParams.push(limit, offset);

    // Get total count for pagination
    const [countResult] = await mysqlServerConnection.query(
      `SELECT COUNT(DISTINCT qb.id) as total
       FROM ${dbName}.question_bank qb
       LEFT JOIN ${dbName}.classroom_assessment_questions caq ON qb.id = caq.question_id AND caq.class_id = ? AND caq.assessment_id = ?
       WHERE qb.is_deleted = 0 AND qb.is_active = 1 AND caq.id IS NULL`,
      [class_id, assessment_id]
    );

    // Execute the main query
    const [questions] = await mysqlServerConnection.query(query, queryParams);

    // Fetch options for each question with is_correct flag
    const questionsWithOptions = await Promise.all(questions.map(async (question) => {
      const [options] = await mysqlServerConnection.query(
        `SELECT id, option_value, option_number, is_correct
         FROM ${dbName}.question_bank_options
         WHERE question_id = ? AND is_deleted = 0
         ORDER BY option_number ASC`,
        [question.id]
      );

      return {
        ...question,
        options
      };
    }));

    return res.status(200).json({
      status: 200,
      success: true,
      message: 'Available questions retrieved successfully',
      questions: questionsWithOptions,
      pagination: {
        totalRecords: countResult[0].total,
        totalPages: Math.ceil(countResult[0].total / limit),
        currentPage: Number(page),
        limit: Number(limit)
      }
    });
  } catch (error) {
    return res.status(500).json({
      status: 500,
      success: false,
      message: 'An error occurred while retrieving available questions',
      error: error.message
    });
  }
};

const getAssessmentAnalytics = async (req, res) => {
  try {
    const { class_id, assessment_id } = req.params;
    const dbName = req.user.db_name;

    if (!class_id || !assessment_id) {
      return res.status(400).json({
        status: 400,
        success: false,
        message: 'Class ID and Assessment ID are required',
      });
    }

    // 1. Get total number of trainees in the classroom
    const [totalTraineesResult] = await mysqlServerConnection.query(
      `SELECT COUNT(*) as total_trainees 
       FROM ${dbName}.classroom_trainee 
       WHERE class_id = ?`,
      [class_id]
    );
    const totalTrainees = totalTraineesResult[0].total_trainees;

    // 2. Get the passing percentage for this assessment
    const [assessmentResult] = await mysqlServerConnection.query(
      `SELECT pass_percentage 
       FROM ${dbName}.classroom_assessments 
       WHERE id = ? AND class_id = ?`,
      [assessment_id, class_id]
    );

    if (assessmentResult.length === 0) {
      return res.status(404).json({
        status: 404,
        success: false,
        message: 'Assessment not found',
      });
    }

    const passingPercentage = assessmentResult[0].pass_percentage;

    // 3. Get the highest score for each trainee who attempted the assessment
    const [traineeResultsQuery] = await mysqlServerConnection.query(
      `SELECT 
         ct.user_id,
         MAX(car.percentage) as highest_percentage
       FROM ${dbName}.classroom_trainee ct
       LEFT JOIN ${dbName}.classroom_assessment_results car ON ct.user_id = car.user_id 
         AND car.assessment_id = ? AND car.class_id = ? AND car.is_deleted = 0
       WHERE ct.class_id = ?
       GROUP BY ct.user_id`,
      [assessment_id, class_id, class_id]
    );

    // 4. Calculate how many passed and failed
    let passed = 0;
    let failed = 0;
    let notAttempted = 0;

    traineeResultsQuery.forEach(result => {
      if (result.highest_percentage === null) {
        notAttempted++;
      } else if (result.highest_percentage >= passingPercentage) {
        passed++;
      } else {
        failed++;
      }
    });

    return res.status(200).json({
      status: 200,
      success: true,
      message: 'Assessment analytics retrieved successfully',
      data: {
        total_trainees: totalTrainees,
        passed: passed,
        failed: failed,
        not_attempted: notAttempted,
        passing_percentage: passingPercentage
      },
    });

  } catch (error) {
    console.error('Error in getAssessmentAnalytics:', error);
    return res.status(500).json({
      status: 500,
      success: false,
      message: 'An error occurred while retrieving assessment analytics',
      error: error.message,
    });
  }
};

module.exports = {
  addClassroomAssessment,
  updateClassroomAssessment,
  deleteClassroomAssessment,
  getClassroomAssessments,
  addQuestionsToAssessment,
  removeQuestionFromAssessment,
  getTraineeAssessmentResults,
  getAssessmentResultsforTrainee,
  copyClassroomAssessment,
  getTraineeClassroomAssessments,
  getAssessmentQuestions,
  getAvailableQuestionsForAssessment,
  getAssessmentAnalytics
};
