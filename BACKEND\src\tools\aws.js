const fs = require("fs");
const {
  S3Client,
  PutObjectCommand,
  DeleteObjectCommand,
} = require("@aws-sdk/client-s3");

// Create an S3 client
const s3 = new S3Client({
  region: process.env.S3_AWS_REGION,
  credentials: {
    accessKeyId: process.env.S3_AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.S3_AWS_SECRET_ACCESS_KEY,
  },
});

/**
 * Upload a file to the specified S3 bucket
 * @param {string} filePath - The local path to the file being uploaded
 * @returns {Promise} - Resolves with the S3 upload response or rejects with an error
 */
const uploadImageToS3 = async (fileData) => {
  try {
    // Convert array back to Buffer if needed
    const fileContent = Array.isArray(fileData)
      ? Buffer.from(fileData)
      : fileData;
    console.log(
      "File content type:",
      typeof fileContent,
      "Length:",
      fileContent.length
    );

    const timeStamp = new Date().getTime();
    const key = `nexgenversity/images/${timeStamp}-image.jpg`;
    const params = {
      Bucket: process.env.S3_AWS_BUCKET_NAME,
      Key: key,
      Body: fileContent,
      ACL: "public-read",
      ContentType: "image/jpeg",
      ContentDisposition: "attachment",
    };

    // Upload the file to S3 with timeout
    const command = new PutObjectCommand(params);

    // Create a timeout promise for S3 upload
    const uploadTimeout = new Promise((_, reject) => {
      const timeoutDuration = Math.max(
        90000,
        90000 + (fileContent.length / (1024 * 1024)) * 10000
      ); // 90s + 10s per MB
      setTimeout(() => {
        reject(
          new Error(
            `S3 image upload timeout after ${timeoutDuration / 1000} seconds`
          )
        );
      }, timeoutDuration);
    });

    // Race between S3 upload and timeout
    const result = await Promise.race([s3.send(command), uploadTimeout]);

    // Return a properly formatted response with the path
    return {
      success: true,
      path: `https://${process.env.S3_AWS_BUCKET_NAME}.s3.amazonaws.com/${key}`,
    };
  } catch (error) {
    console.error("Error uploading image to S3:", error);
    throw error;
  }
};

const uploadVideoToS3 = async (fileData, name) => {
  try {
    // Convert array back to Buffer if needed
    const fileContent = Array.isArray(fileData)
      ? Buffer.from(fileData)
      : fileData;
    console.log(
      "Video file content type:",
      typeof fileContent,
      "Length:",
      fileContent.length
    );

    const timeStamp = new Date().getTime();
    // Sanitize filename
    const sanitizedName = name
      ? name
          .replace(/[^\x00-\x7F]/g, "")
          .replace(/\s+/g, "_")
          .replace(/[^a-zA-Z0-9_.-]/g, "")
      : "video";
    const key = `nexgenversity/videos/${timeStamp}-${sanitizedName}`;

    // Determine content type based on file extension
    let contentType = "video/mp4";
    if (name) {
      const extension = name.toLowerCase().split(".").pop();
      switch (extension) {
        case "mp4":
          contentType = "video/mp4";
          break;
        case "avi":
          contentType = "video/x-msvideo";
          break;
        case "mov":
          contentType = "video/quicktime";
          break;
        case "wmv":
          contentType = "video/x-ms-wmv";
          break;
        case "webm":
          contentType = "video/webm";
          break;
        default:
          contentType = "video/mp4";
      }
    }

    const params = {
      Bucket: process.env.S3_AWS_BUCKET_NAME,
      Key: key,
      Body: fileContent,
      ACL: "public-read",
      ContentType: contentType,
      ContentDisposition: "attachment",
    };

    // Upload the file to S3 with timeout
    const command = new PutObjectCommand(params);

    // Create a timeout promise for S3 upload
    const uploadTimeout = new Promise((_, reject) => {
      const timeoutDuration = Math.max(
        180000,
        180000 + (fileContent.length / (1024 * 1024)) * 20000
      ); // 3 minutes + 20s per MB
      setTimeout(() => {
        reject(
          new Error(`S3 upload timeout after ${timeoutDuration / 1000} seconds`)
        );
      }, timeoutDuration);
    });

    // Race between S3 upload and timeout
    const result = await Promise.race([s3.send(command), uploadTimeout]);

    console.log("Video upload result:", result);

    return {
      success: true,
      path: `https://${process.env.S3_AWS_BUCKET_NAME}.s3.${process.env.S3_AWS_REGION}.amazonaws.com/${key}`,
    };
  } catch (error) {
    console.error("Error uploading video file:", error);
    throw error;
  }
};
const uploadDocToS3 = async (fileData, fileName) => {
  try {
    // Convert array back to Buffer if needed
    const fileContent = Array.isArray(fileData)
      ? Buffer.from(fileData)
      : fileData;
    console.log(
      "Document file content type:",
      typeof fileContent,
      "Length:",
      fileContent.length
    );
    console.log("Original fileName:", fileName);

    const timeStamp = new Date().getTime();
    // Sanitize filename
    const sanitizedName = fileName
      ? fileName
          .replace(/[^\x00-\x7F]/g, "")
          .replace(/\s+/g, "_")
          .replace(/[^a-zA-Z0-9_.-]/g, "")
      : "document";
    const key = `nexgenversity/documents/${timeStamp}-${sanitizedName}`;

    // Determine content type and disposition based on file extension
    let contentType = "application/octet-stream";
    let contentDisposition = "attachment"; // Default to attachment for downloads

    if (fileName) {
      const extension = fileName.toLowerCase().split(".").pop();
      console.log("Detected file extension:", extension);
      switch (extension) {
        case "pdf":
          contentType = "application/pdf";
          contentDisposition = "inline"; // Allow PDF preview in browser
          console.log("Setting PDF to inline for preview");
          break;
        case "doc":
          contentType = "application/msword";
          break;
        case "docx":
          contentType =
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
          break;
        case "txt":
          contentType = "text/plain";
          contentDisposition = "inline"; // Allow text preview in browser
          break;
        default:
          contentType = "application/octet-stream";
      }
    }

    console.log("Final upload params:", {
      ContentType: contentType,
      ContentDisposition: contentDisposition,
      Key: key,
    });

    const params = {
      Bucket: process.env.S3_AWS_BUCKET_NAME,
      Key: key,
      Body: fileContent,
      ACL: "public-read",
      ContentType: contentType,
      ContentDisposition: contentDisposition,
    };

    // Upload the file to S3 with timeout
    const command = new PutObjectCommand(params);

    // Create a timeout promise for S3 upload
    const uploadTimeout = new Promise((_, reject) => {
      const timeoutDuration = Math.max(
        120000,
        120000 + (fileContent.length / (1024 * 1024)) * 15000
      ); // 2 minutes + 15s per MB
      setTimeout(() => {
        reject(
          new Error(
            `S3 document upload timeout after ${timeoutDuration / 1000} seconds`
          )
        );
      }, timeoutDuration);
    });

    // Race between S3 upload and timeout
    const result = await Promise.race([s3.send(command), uploadTimeout]);

    console.log("Document upload result:", result);

    return {
      success: true,
      path: `https://${process.env.S3_AWS_BUCKET_NAME}.s3.${process.env.S3_AWS_REGION}.amazonaws.com/${key}`,
    };
  } catch (error) {
    console.error("Error uploading document file:", error);
    throw error;
  }
};

const uploadDocxToS3 = async (filePath, fileName) => {
  try {
    const fileContent = filePath;
    const timeStamp = new Date().getTime();
    const key = `nexgenversity/images/${timeStamp}-${fileName}`;
    const params = {
      Bucket: process.env.S3_AWS_BUCKET_NAME,
      Key: key,
      Body: fileContent,
      ACL: "public-read",
      ContentType: "application/documents",
      ContentDisposition: "attachment", // Set the content disposition header
    };

    // Upload the file to S3
    const command = new PutObjectCommand(params); // Create a command object
    const result = await s3.send(command);
    console.log(result);
    return {
      result,
      path: `https://${process.env.S3_AWS_BUCKET_NAME}.s3.${process.env.S3_AWS_REGION}.amazonaws.com/${key}`,
    }; // Return the S3 response
  } catch (error) {
    console.error("Error uploading file:", error);
    throw error;
  }
};

const deleteFileFromS3 = async (fileUrl) => {
  try {
    // Extract the key from the URL
    // URL format: https://bucket-name.s3.region.amazonaws.com/key
    const urlParts = fileUrl.split(".amazonaws.com/");
    if (urlParts.length < 2) {
      throw new Error("Invalid S3 URL format");
    }

    const key = urlParts[1];

    const params = {
      Bucket: process.env.S3_AWS_BUCKET_NAME,
      Key: key,
    };

    const command = new DeleteObjectCommand(params);
    const result = await s3.send(command);
    console.log("File deleted from S3:", result);
    return result;
  } catch (error) {
    console.error("Error deleting file from S3:", error);
    throw error;
  }
};

const uploadAudioToS3 = async (fileData, name) => {
  try {
    // Convert array back to Buffer if needed
    const fileContent = Array.isArray(fileData)
      ? Buffer.from(fileData)
      : fileData;
    console.log(
      "Audio file content type:",
      typeof fileContent,
      "Length:",
      fileContent.length
    );

    const timeStamp = new Date().getTime();

    // Sanitize the filename - remove non-ASCII characters and replace spaces with underscores
    let sanitizedName = name
      ? name
          .replace(/[^\x00-\x7F]/g, "") // Remove non-ASCII characters
          .replace(/\s+/g, "_") // Replace spaces with underscores
          .replace(/[^a-zA-Z0-9_.-]/g, "") // Remove any other special characters
      : `audio_${timeStamp}.mp3`;

    // If sanitizedName is empty after cleaning, use a default name
    if (!sanitizedName || sanitizedName.trim() === "") {
      sanitizedName = `audio_${timeStamp}.mp3`;
    }

    const key = `nexgenversity/audio/${timeStamp}-${sanitizedName}`;
    const params = {
      Bucket: process.env.S3_AWS_BUCKET_NAME,
      Key: key,
      Body: fileContent,
      ACL: "public-read",
      ContentType: "audio/mpeg",
      ContentDisposition: "attachment",
    };

    // Upload the file to S3 with timeout
    const command = new PutObjectCommand(params);

    // Create a timeout promise for S3 upload
    const uploadTimeout = new Promise((_, reject) => {
      const timeoutDuration = Math.max(
        120000,
        120000 + (fileContent.length / (1024 * 1024)) * 15000
      ); // 2 minutes + 15s per MB
      setTimeout(() => {
        reject(
          new Error(
            `S3 audio upload timeout after ${timeoutDuration / 1000} seconds`
          )
        );
      }, timeoutDuration);
    });

    // Race between S3 upload and timeout
    const result = await Promise.race([s3.send(command), uploadTimeout]);

    console.log("Audio upload result:", result);

    return {
      success: true,
      path: `https://${process.env.S3_AWS_BUCKET_NAME}.s3.${process.env.S3_AWS_REGION}.amazonaws.com/${key}`,
    };
  } catch (error) {
    console.error("Error uploading audio file:", error);
    throw error;
  }
};

/**
 * Upload a Stripe receipt PDF to S3
 * @param {Buffer} pdfContent - The PDF content as a Buffer
 * @param {string} receiptId - The Stripe receipt ID or payment ID
 * @returns {Promise} - Resolves with the S3 upload response including the file path
 */
const uploadStripeReceiptToS3 = async (pdfContent, receiptId) => {
  try {
    if (!pdfContent) {
      throw new Error("PDF content is required");
    }

    // Sanitize the receipt ID
    const sanitizedReceiptId = receiptId
      ? receiptId
          .replace(/[^\x00-\x7F]/g, "") // Remove non-ASCII characters
          .replace(/\s+/g, "_") // Replace spaces with underscores
          .replace(/[^a-zA-Z0-9_.-]/g, "") // Remove any other special characters
      : `receipt_${new Date().getTime()}`;

    const timeStamp = new Date().getTime();
    const key = `nexgenversity/pdf/${timeStamp}-${sanitizedReceiptId}.pdf`;

    const params = {
      Bucket: process.env.S3_AWS_BUCKET_NAME,
      Key: key,
      Body: pdfContent,
      ACL: "public-read",
      ContentType: "application/pdf",
      ContentDisposition: "attachment", // Changed from 'inline' to 'attachment' to match working configuration
    };

    // Upload the file to S3
    const command = new PutObjectCommand(params);
    const result = await s3.send(command);

    return {
      success: true,
      path: `https://${process.env.S3_AWS_BUCKET_NAME}.s3.${process.env.S3_AWS_REGION}.amazonaws.com/${key}`,
      key: key,
    };
  } catch (error) {
    console.error("Error uploading Stripe receipt to S3:", error);
    throw error;
  }
};

module.exports = {
  s3,
  uploadImageToS3,
  uploadVideoToS3,
  uploadAudioToS3,
  uploadDocToS3,
  uploadDocxToS3,
  deleteFileFromS3,
  uploadStripeReceiptToS3,
};
