import React, { useState, useEffect } from 'react'
import { Icon } from '@iconify/react'
import { useNavigate } from 'react-router-dom'
import { getTaskLists, markTaskAsSeen } from '../../../services/userService'
import Loader from '../../../components/common/Loader'
import NoData from '../../../components/common/NoData'
import { encodeData } from '../../../utils/encodeAndEncode'

function TaskList() {
    const navigate = useNavigate();
    const [loading, setLoading] = useState(true);
    const [tasks, setTasks] = useState([]);
    const user_id = JSON.parse(localStorage.getItem('user'))?.id;
    const [pagination, setPagination] = useState({
        currentPage: 1,
        limit: 20,
        totalTasks: 0,
        hasMore: false
    });

    useEffect(() => {
        fetchTasks();
    }, []);

    const fetchTasks = async () => {
        try {
            setLoading(true);
            const payload = {
                user_id: user_id,
                page: pagination.currentPage,
                limit: pagination.limit
            };
            const response = await getTaskLists(payload);
            console.log("Task List response-------------", response);

            if (response.success) {
                setTasks(response.data);
                setPagination(response.pagination);
            }
        } catch (error) {
            console.error('Error fetching tasks:', error);
        } finally {
            setLoading(false);
        }
    };

    const handleMarkAsSeen = async (task) => {
        try {
            const payload = {
                user_id: user_id,
                classroom_id: task.classroom_id,
                task_id: task.task_id,
                [task.task_type === 'assessment' ? 'assessment_id' : 'assignment_id']: task.task_id
            };

            console.log("Marking task as seen with payload:", payload);
            const response = await markTaskAsSeen(payload);
            if (response.success) {
                // Update the local state to mark the task as seen
                setTasks(prevTasks => 
                    prevTasks.map(t => 
                        t.task_id === task.task_id ? { ...t, is_seen: true } : t
                    )
                );
            }
        } catch (error) {
            console.error('Error marking task as seen:', error);
        }
    };

    const formatDateTime = (dateString) => {
        if (!dateString) return null;
        return new Date(dateString).toLocaleString('en-US', {
            month: 'short',
            day: 'numeric',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
            hour12: true
        }).replace(/,/g, '');
    };

    const getTaskIcon = (taskType) => {
        switch (taskType) {
            case 'assignment':
                return <Icon icon="fluent:document-text-24-regular" className="text-primary" width="24" height="24" />;
            case 'assessment':
                return <Icon icon="fluent:quiz-new-24-regular" className="text-primary" width="24" height="24" />;
            default:
                return <Icon icon="fluent:task-list-24-regular" className="text-primary" width="24" height="24" />;
        }
    };

    const getTaskBadge = (taskType, isTimeBased) => {
        switch (taskType) {
            case 'assignment':
                return (
                    <span className="badge border border-primary text-primary bg-primary-subtle d-flex align-items-center gap-2 px-3 py-2 fw-medium">
                        <Icon icon="mdi:clipboard-text-outline" width="14" height="14" />
                        Assignment
                    </span>
                );
            case 'assessment':
                return (
                    <div className="d-flex align-items-center gap-2">
                          <small className="text-muted" style={{ fontSize: '11px' }}>
                            {isTimeBased ? '(Time base)' : '(Without time)'}
                        </small>
                        <span className="badge border border-primary text-primary bg-primary-subtle d-flex align-items-center gap-2 px-3 py-2 fw-medium">
                            <Icon icon="mdi:file-document-outline" width="14" height="14" />
                            Assessment
                        </span>
                      
                    </div>
                );
            default:
                return (
                    <span className="badge border border-primary text-primary bg-primary-subtle d-flex align-items-center gap-2 px-3 py-2 fw-medium">
                        <Icon icon="mdi:checkbox-blank-circle-outline" width="14" height="14" />
                        Task
                    </span>
                );
        }
    };

    const handleCardClick = async (task) => {
        // First mark the task as seen if it's not already seen
        if (!task.is_seen) {
            console.log("Task to be marked as seen-------------", task);
            await handleMarkAsSeen(task);
        }
        console.log("Task to be marked as seen-------------", task);
        
        // Then navigate to the classroom details
        const encodedClassroomId = encodeData(task.classroom_id);
        const activeTab = task.task_type === 'assessment' ? 'assessments' : 'assignments';
        navigate(`/user/AllClassroom/classroomDetails/${encodedClassroomId}?active-tab=${activeTab}`);
    };

    return (
        <>
            <style>
                {`
                    .task-list-card .card-body::-webkit-scrollbar {
                        width: 6px;
                    }
                    
                    .task-list-card .card-body::-webkit-scrollbar-track {
                        background: #f1f1f1;
                        border-radius: 3px;
                    }
                    
                    .task-list-card .card-body::-webkit-scrollbar-thumb {
                        background: #888;
                        border-radius: 3px;
                    }
                    
                    .task-list-card .card-body::-webkit-scrollbar-thumb:hover {
                        background: #555;
                    }

                    .unseen-task {
                        background-color: #e8f4ff !important;
                        border-left: 4px solid #0d6efd !important;
                    }
                `}
            </style>
            <div className="card shadow-sm mt-3 task-list-card" style={{ height: '535px' }}>
                <div className="card-header bg-white py-3">
                    <div className="d-flex justify-content-between align-items-center">
                        <h5 className="text-dark mb-0 d-flex align-items-center">
                            <Icon icon="fluent:task-list-square-24-regular" className="me-2 text-primary fs-4" />
                            Task List
                        </h5>
                    </div>
                </div>
                <div className="card-body p-3" style={{ 
                    height: 'calc(100% - 65px)', // 65px is approx header height
                    overflowY: 'auto',
                    scrollbarWidth: 'thin',
                    scrollbarColor: '#888 #f1f1f1'
                }}>
                    {loading ? (
                        <div className="p-4 text-center">
                            <Loader />
                        </div>
                    ) : tasks.length === 0 ? (
                        <div className="p-4">
                            <NoData caption="No tasks available" />
                        </div>
                    ) : (
                        <div className="row g-3">
                            {tasks.map((task) => (
                                <div key={task.task_id} className="col-12">
                                    <div 
                                        className={`card border shadow-sm cursor-pointer ${!task.is_seen ? 'unseen-task' : ''}`}
                                        onClick={() => handleCardClick(task)}
                                        style={{ 
                                            transition: 'transform 0.2s, box-shadow 0.2s',
                                            cursor: 'pointer'
                                        }}
                                        onMouseOver={(e) => {
                                            e.currentTarget.style.transform = 'translateY(-2px)';
                                            e.currentTarget.style.boxShadow = '0 .5rem 1rem rgba(0,0,0,.15)';
                                        }}
                                        onMouseOut={(e) => {
                                            e.currentTarget.style.transform = 'none';
                                            e.currentTarget.style.boxShadow = '';
                                        }}
                                    >
                                        <div className="card-body p-3">
                                            <div className="d-flex gap-3">
                                                <div className="task-icon">
                                                    {getTaskIcon(task.task_type)}
                                                </div>
                                                <div className="flex-grow-1">
                                                    <div className="d-flex justify-content-between align-items-start mb-1">
                                                        <div className="pe-3" style={{ minWidth: 0 }}>
                                                            <h6 className="mb-1 fw-semibold text-truncate" style={{ maxWidth: '150px' }}>
                                                                {task.title || task.assessment_name}
                                                                {!task.is_seen && (
                                                                    <span className="ms-2 badge bg-primary" style={{ fontSize: '0.7rem' }}>New</span>
                                                                )}
                                                            </h6>
                                                            {/* <div className="text-muted small mb-2 d-flex align-items-center">
                                                                <Icon icon="fluent:building-24-regular" className="me-1" width="14" height="14" />
                                                                Classroom ID: {task.classroom_id}
                                                            </div> */}
                                                        </div>
                                                        <div className="flex-shrink-0">
                                                            {task.task_type === 'assessment' ? (
                                                                <div className="d-flex align-items-center gap-2">
                                                                    <small className="text-muted" style={{ fontSize: '11px', whiteSpace: 'nowrap' }}>
                                                                        {task.is_time_based ? '(Time base)' : '(Without time)'}
                                                                    </small>
                                                                    <span className="badge border border-primary text-primary bg-primary-subtle d-flex align-items-center gap-2 px-3 py-2 fw-medium">
                                                                        <Icon icon="fluent:quiz-new-24-regular" width="14" height="14" />
                                                                        Assessment
                                                                    </span>
                                                                </div>
                                                            ) : (
                                                                <span className="badge border border-primary text-primary bg-primary-subtle d-flex align-items-center gap-2 px-3 py-2 fw-medium">
                                                                    <Icon icon="fluent:document-text-24-regular" width="14" height="14" />
                                                                    Assignment
                                                                </span>
                                                            )}
                                                        </div>
                                                    </div>
                                                    <div className="d-flex flex-wrap gap-3 text-muted small">
                                                        {task.assigned_date && (
                                                            <div className="d-flex align-items-center text-success">
                                                                <Icon icon="fluent:calendar-start-24-regular" className="me-1" width="14" height="14" />
                                                                Start: {formatDateTime(task.assigned_date)}
                                                            </div>
                                                        )}
                                                        {task.due_date && (
                                                            <div className="d-flex align-items-center text-danger">
                                                                <Icon icon="fluent:calendar-end-24-regular" className="me-1" width="14" height="14" />
                                                                Due: {formatDateTime(task.due_date)}
                                                            </div>
                                                        )}
                                                        {task.is_time_based && (
                                                            <>
                                                                <div className="d-flex align-items-center text-primary">
                                                                    <Icon icon="fluent:clock-24-regular" className="me-1" width="14" height="14" />
                                                                    Starts: {formatDateTime(task.activate_time)}
                                                                </div>
                                                                <div className="d-flex align-items-center text-danger">
                                                                    <Icon icon="fluent:clock-alarm-24-regular" className="me-1" width="14" height="14" />
                                                                    Ends: {formatDateTime(task.deactivate_time)}
                                                                </div>
                                                            </>
                                                        )}
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            ))}
                            {pagination.hasMore && (
                                <div className="col-12 text-center mb-2">
                                    <button 
                                        className="btn btn-outline-primary btn-sm"
                                        onClick={() => {
                                            setPagination(prev => ({
                                                ...prev,
                                                currentPage: prev.currentPage + 1
                                            }));
                                            fetchTasks();
                                        }}
                                    >
                                        <Icon icon="fluent:arrow-sync-24-regular" className="me-1" />
                                        Load More Tasks
                                    </button>
                                </div>
                            )}
                        </div>
                    )}
                </div>
            </div>
        </>
    )
}

export default TaskList
