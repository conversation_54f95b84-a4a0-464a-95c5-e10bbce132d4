// Import necessary modules and database connection
const { mysqlServerConnection } = require('../../../db/db');

const getNotes = async (req, res) => {
    try {
        const userId = req.user.userId; // Get user ID from the request (assuming user is authenticated and ID is attached to the request object)
        const videoId = req.query.video_id; // Get video ID from the query parameters

        if (!videoId) {
            return res.status(400).json({
                success: false,
                data: {
                    error_msg: 'Video ID is required.'
                }
            });
        }

        // Query to get notes from the Notes table for a specific user and video, and join with the Videos table to get video_name
        const [notes] = await mysqlServerConnection.query(
            `SELECT n.*, v.video_name 
             FROM ${req.user.db_name}.notes n
             JOIN ${req.user.db_name}.videos v ON n.video_id = v.id
             WHERE n.user_id = ? AND n.video_id = ?
             ORDER BY n.updatedAt DESC`,
            [userId, videoId]
        );

        // Check if any notes were found
        if (notes.length === 0) {
            return res.status(200).json({
                success: false,
                data: {
                    error_msg: 'No notes found for the specified video and user.'
                }
            });
        }

        // Format the notes response
        const formattedNotes = notes.map(note => ({
            id: note.id,
            video_id: note.video_id,
            user_id: note.user_id,
            video_name: note.video_name, // Include video_name in the response
            stream_time: note.stream_time,
            note: note.note,
            createdAt: note.createdAt,
            updatedAt: note.updatedAt
        }));

        // Send the response with the formatted notes
        return res.status(200).json({
            success: true,
            data: formattedNotes
        });
    } catch (error) {
        console.error('Error retrieving notes:', error);
        return res.status(500).json({
            success: false,
            data: {
                error_msg: 'Internal server error.'
            }
        });
    }
};



const addNote = async (req, res) => {
    try {
        const { video_id, stream_time, note, module_id } = req.body;
        const userId = req.user.userId;

        console.log("video_id:", video_id, "note:", note, "stream_time:", stream_time, "module_id:", module_id);

        // Validate required fields
        if (!video_id || !note || !module_id) {
            return res.status(400).json({
                success: false,
                data: { error_msg: 'Video ID, module ID, and note content are required.' }
            });
        }

        // Query to insert a new note into the Notes table
        const insertQuery = `
            INSERT INTO ${req.user.db_name}.notes (video_id, user_id, stream_time, note, module_id)
            VALUES (?, ?, ?, ?, ?)
        `;

        // ✅ FIX: Use nullish coalescing (??) instead of || to preserve 0 value
        await mysqlServerConnection.query(
            insertQuery,
            [video_id, userId, stream_time ?? null, note, module_id]
        );

        return res.status(200).json({
            success: true,
            data: { message: 'Note added successfully.' }
        });
    } catch (error) {
        console.error('Error adding note:', error);
        return res.status(500).json({
            success: false,
            data: { error_msg: 'Internal server error.' }
        });
    }
};


const getModuleNotes = async (req, res) => {
    try {
        const userId = req.user.userId; // Get user ID from the request (assuming user is authenticated and ID is attached to the request object)
        const moduleId = req.query.module_id; // Get module ID from the query parameters

        if (!moduleId) {
            return res.status(400).json({
                success: false,
                data: {
                    error_msg: 'Module ID is required.'
                }
            });
        }

        // Query to get notes from the Notes table for a specific user and module, and join with the Videos table to get video_name
        const [notes] = await mysqlServerConnection.query(
            `SELECT n.*, v.video_name 
             FROM ${req.user.db_name}.notes n
             JOIN ${req.user.db_name}.Videos v ON n.video_id = v.id
             WHERE n.user_id = ? AND n.module_id = ?
             ORDER BY n.updatedAt DESC`,
            [userId, moduleId]
        );

        // If no notes are found, return a success response with a message
        if (notes.length === 0) {
            return res.status(200).json({
                success: true,
                data: {
                    message: 'Empty Records', // Change to "Empty Records"
                    notes: [] // Return an empty array for notes
                }
            });
        }

        // Format the notes response
        const formattedNotes = notes.map(note => ({
            id: note.id,
            module_id: note.module_id,
            user_id: note.user_id,
            video_id: note.video_id,
            video_name: note.video_name, // Include video_name in the response
            stream_time: note.stream_time,
            note: note.note,
            createdAt: note.createdAt,
            updatedAt: note.updatedAt
        }));

        // Send the response with the formatted notes
        return res.status(200).json({
            success: true,
            data: formattedNotes
        });
    } catch (error) {
        console.error('Error retrieving module notes:', error);
        return res.status(500).json({
            success: false,
            data: {
                error_msg: 'Internal server error.'
            }
        });
    }
};

const deleteNotes = async (req, res) => {
    try {
        const userId = req.user.userId; // Get user ID from the request (assuming user is authenticated and ID is attached to the request object)
        const noteId = req.params.noteId; // Get note ID from the query parameters
        console.log("noteId:", noteId);
        console.log("userId:", userId);
        if (!noteId) {
            return res.status(400).json({
                success: false,
                data: {
                    error_msg: 'Note ID is required.'
                }
            });
        }

        // Query to delete notes from the Notes table for a specific user and note
        const deleteQuery = `
            DELETE FROM ${req.user.db_name}.notes
            WHERE user_id = ? AND id = ?
        `;

        // Execute the delete query
        const [result] = await mysqlServerConnection.query(deleteQuery, [userId, noteId]);

        // Check if any rows were affected (i.e., notes were deleted)
        if (result.affectedRows === 0) {
            return res.status(404).json({
                success: false,
                data: {
                    error_msg: 'No notes found for the specified note and user.'
                }
            });
        }

        return res.status(200).json({
            success: true,
            data: { message: 'Notes deleted successfully.' }
        });
    } catch (error) {
        console.error('Error deleting notes:', error);
        return res.status(500).json({
            success: false,
            data: { error_msg: 'Internal server error.' }
        });
    }
};

const updateNote = async (req, res) => {
    try {
        const userId = req.user.userId; // Get user ID from the request (assuming user is authenticated and ID is attached to the request object)
        const { noteId, note } = req.body; // Get note ID and new note content from the request body
        console.log("noteId---------------------------:", noteId);
        console.log("userId:", userId);
        console.log("note:", note);
        if (!noteId || !note) {
            return res.status(400).json({
                success: false,
                data: {
                    error_msg: 'Note ID and note content are required.'
                }
            });

        }
        // Query to update the note in the Notes table for a specific user and note
        const updateQuery = `

            UPDATE ${req.user.db_name}.notes
            SET note = ?, updatedAt = NOW()
            WHERE user_id = ? AND id = ?
        `;
        // Execute the update query
        const [result] = await mysqlServerConnection.query(updateQuery, [note, userId, noteId]);
        // Check if any rows were affected (i.e., note was updated)
        if (result.affectedRows === 0) {
            return res.status(404).json({
                success: false,
                data: {
                    error_msg: 'No notes found for the specified note and user.'
                }
            });
        }
        return res.status(200).json({
            success: true,
            data: { message: 'Note updated successfully.' }
        });
    } catch (error) {
        console.error('Error updating note:', error);
        return res.status(500).json({
            success: false,
            data: { error_msg: 'Internal server error.' }
        });
    }
};


const updateComment = async (req, res) => {
  try {
    const userId = req.user.userId; // Authenticated user
    const dbName = req.user.db_name;

    const { commentId, comment } = req.body;

    console.log("📝 Updating comment ID:", commentId);
    console.log("💬 New comment:", comment);
    console.log("👤 User ID:", userId);

    // Validate inputs
    if (!commentId || !comment?.trim()) {
      return res.status(400).json({
        success: false,
        data: { error_msg: "Comment ID and content are required." }
      });
    }

    // Query to update the comment
    const updateQuery = `
      UPDATE ${dbName}.messagebox
      SET comment = ?, updatedAt = NOW()
      WHERE id = ? AND user_id = ?
    `;

    const [result] = await mysqlServerConnection.query(updateQuery, [comment, commentId, userId]);

    if (result.affectedRows === 0) {
      return res.status(404).json({
        success: false,
        data: { error_msg: "No comment found or permission denied." }
      });
    }

    return res.status(200).json({
      success: true,
      data: { message: "Comment updated successfully." }
    });

  } catch (error) {
    console.error("❌ Error updating comment:", error);
    return res.status(500).json({
      success: false,
      data: { error_msg: "Internal server error." }
    });
  }
};


// API to get comments from the database
const getComments = async (req, res) => {
    try {
        const userId = req.user.userId; // Get user ID from the request (assuming user is authenticated and ID is attached to the request object)
        const videoId = req.query.video_id; // Get video ID from the query parameters
        // console.log("req:", videoId, userId);

        if (!videoId) {
            return res.status(400).json({
                success: false,
                data: {
                    error_msg: 'Video ID is required.'
                }
            });
        }

        // Query to get comments from the messagebox table for a specific user and video, and join with the Videos table to get video_name
        const [comments] = await mysqlServerConnection.query(
            `SELECT m.*, v.video_name 
             FROM ${req.user.db_name}.messagebox m
             JOIN ${req.user.db_name}.videos v ON m.video_id = v.id
             WHERE m.video_id = ?
             ORDER BY m.updatedAt DESC`,
            [videoId]
        );

        // Check if any comments were found
        if (comments.length === 0) {
            return res.status(404).json({
                success: false,
                data: {
                    error_msg: 'No comments found for the specified video and user.'
                }
            });
        }

        // Format the comments response
        const formattedComments = comments.map(comment => ({
            id: comment.id,
            video_id: comment.video_id,
            user_id: comment.user_id,
            video_name: comment.video_name, // Include video_name in the response
            stream_time: comment.stream_time,
            comment: comment.comment,
            createdAt: comment.createdAt,
            updatedAt: comment.updatedAt
        }));

        // Send the response with the formatted comments
        return res.status(200).json({
            success: true,
            data: formattedComments
        });
    } catch (error) {
        console.error('Error retrieving comments:', error);
        return res.status(500).json({
            success: false,
            data: {
                error_msg: 'Internal server error.'
            }
        });
    }
};

// API to add a comment to the database
const addComment = async (req, res) => {
    try {
        console.log("In addComment", req.body.stream_time);
        const { video_id, stream_time, comment, module_id } = req.body; // Extract necessary data from request body
           const userId = req.user.userId; // Assuming user is authenticated and user ID is attached to the request object
        const dbName = req.user.db_name; // Assuming DB name is also attached to the user object
        const mentor_id = 1;

        console.log("video_id----------:", video_id, "stream_time:", stream_time, "comment:", comment, "module_id:", module_id, "user_id:", userId);

        // Validate required fields
        if (!video_id || !comment) {
            return res.status(400).json({
                success: false,
                data: { error_msg: 'Video ID, mentor ID, and comment are required.' }
            });
        }

        // Query to insert a new comment into the messagebox table
        const insertQuery = `
            INSERT INTO ${dbName}.messagebox (video_id, user_id, module_id, mentor_id, stream_time, comment)
            VALUES (?, ?, ?, ?, ?, ?)
        `;

        // Execute the insert query
        await mysqlServerConnection.query(insertQuery, [video_id, userId, module_id || null, mentor_id, stream_time || null, comment]);

        return res.status(200).json({
            success: true,
            data: { message: 'Comment added successfully.' }
        });
    } catch (error) {
        console.error('Error adding comment:', error);
        return res.status(500).json({
            success: false,
            data: { error_msg: 'Internal server error.' }
        });
    }
};
const deleteComment = async (req, res) => {
    try {
        console.log("In deleteComment");
        const userId = req.user.userId;
        const commentId = req.params.commentId;
        if (!commentId) {
            return res.status(400).json({
                success: false,
                data: {
                    error_msg: 'Comment ID is required.'
                }
            });
        }

        // Query to delete the comment from the messagebox table
        const deleteCommentQuery = `
            DELETE FROM ${req.user.db_name}.messagebox
            WHERE user_id = ? AND id = ?
        `;

        // Execute the delete query for the comment
        const [result] = await mysqlServerConnection.query(deleteCommentQuery, [userId, commentId]);

        // Check if any rows were affected (i.e., comments were deleted)
        if (result.affectedRows === 0) {
            return res.status(404).json({
                success: false,
                data: {
                    error_msg: 'No comments found for the specified comment and user.'
                }
            });
        }

        return res.status(200).json({
            success: true,
            data: { message: 'Comment deleted successfully.' }
        });
    } catch (error) {
        console.error('Error deleting comment:', error);
        return res.status(500).json({
            success: false,
            data: { error_msg: 'Internal server error.' }
        });
    }
};

const updateLike = async (req, res) => {
    try {
        const userId = req.user.userId;
        const { commentId } = req.body;
        console.log("UserId:", userId, "CommentId:", commentId,"dbName:",req.user.db_name);

        // Check if the user has already liked the comment
        const [existingLike] = await mysqlServerConnection.query(
            `SELECT * FROM ${req.user.db_name}.likes WHERE user_id = ? AND comment_id = ?`,
            [userId, commentId]
        );

        let newLikesCount;
        if (existingLike.length > 0) {
            // User has already liked the comment, so remove the like
            await mysqlServerConnection.query(
                `DELETE FROM ${req.user.db_name}.likes WHERE user_id = ? AND comment_id = ?`,
                [userId, commentId]
            );
            newLikesCount = await mysqlServerConnection.query(
                `UPDATE ${req.user.db_name}.messagebox SET likes = likes - 1 WHERE id = ?`,
                [commentId]
            );
        } else {
            // User has not liked the comment, so add the like
            await mysqlServerConnection.query(
                `INSERT INTO ${req.user.db_name}.likes (user_id, comment_id) VALUES (?, ?)`,
                [userId, commentId]
            );
            newLikesCount = await mysqlServerConnection.query(
                `UPDATE ${req.user.db_name}.messagebox SET likes = likes + 1 WHERE id = ?`,
                [commentId]
            );
        }

        // Get the updated likes count
        const [updatedComment] = await mysqlServerConnection.query(
            `SELECT likes FROM ${req.user.db_name}.messagebox WHERE id = ?`,
            [commentId]
        );

        return res.status(200).json({
            success: true,
            data: {
                likes: updatedComment[0].likes
            }
        });
    } catch (error) {
        console.error('Error updating like:', error);
        return res.status(500).json({
            success: false,
            data: {
                error_msg: 'Internal server error.'
            }
        });
    }
};

const getModuleComments = async (req, res) => {
    try {
        const userId = req.user.userId; // Get user ID from the request (assuming user is authenticated and ID is attached to the request object)
        const moduleId = req.query.module_id; // Get module ID from the query parameters
        
        if (!moduleId) {
            return res.status(400).json({
                success: false,
                data: {
                    error_msg: 'Module ID is required.'
                }
            });
        }

        // Query to get comments from the messagebox table for a specific user and module
        const [comments] = await mysqlServerConnection.query(
            `SELECT * FROM ${req.user.db_name}.messagebox WHERE user_id = ? AND module_id = ? ORDER BY updatedAt DESC`,
            [userId, moduleId]
        );

        // Check if any comments were found
        if (comments.length === 0) {
            return res.status(404).json({
                success: false,
                data: {
                    error_msg: 'No comments found for the specified module and user.'
                }
            });
        }

        // Format the comments response
        const formattedComments = comments.map(comment => ({
            id: comment.id,
            module_id: comment.module_id,
            user_id: comment.user_id,
            stream_time: comment.stream_time,
            comment: comment.comment,
            createdAt: comment.createdAt,
            updatedAt: comment.updatedAt
        }));

        // Send the response with the formatted comments
        return res.status(200).json({
            success: true,
            data: formattedComments
        });
    } catch (error) {
        console.error('Error retrieving comments:', error);
        return res.status(500).json({
            success: false,
            data: {
                error_msg: 'Internal server error.'
            }
        });
    }
};

const addReplyComment = async (req, res) => {
    try {
        const { parent_comment_id, user_id, comment } = req.body; // Extract necessary data from request body
        const dbName = req.user.db_name; // Assuming the user is authenticated and the DB name is attached to the request object

        console.log("parent_K_comment_id:", parent_comment_id, "user_id:", user_id, "comment:", comment);

        // Validate required fields
        if (!parent_comment_id || !user_id || !comment) {
            return res.status(400).json({
                success: false,
                data: { error_msg: 'Parent comment ID, user ID, and comment content are required.' }
            });
        }

        // Query to insert a new reply comment into the replies_comments table
        const insertQuery = `
            INSERT INTO ${dbName}.replies_comments (comment_id, user_id, reply_comment)
            VALUES (?, ?, ?)
        `;

        // Execute the insert query
        await mysqlServerConnection.query(insertQuery, [parent_comment_id, user_id, comment]);

        console.log("Reply comment added successfully.", user_id);
        return res.status(200).json({
            success: true,
            data: { message: 'Reply comment added successfully.' }
        });
    } catch (error) {
        console.error('Error adding reply comment:', error);
        return res.status(500).json({
            success: false,
            data: { error_msg: 'Internal server error.' }
        });
    }
};

const editReplyComment = async (req, res) => {
    try {
      const { reply_id, comment } = req.body;
      const dbName = req.user.db_name;
  
      console.log("✏️ Edit Reply Request:", { reply_id, comment, dbName });
  
      if (!reply_id || !comment) {
        console.warn("⚠️ Missing required fields:", { reply_id, comment });
        return res.status(400).json({
          success: false,
          message: 'Reply ID and updated comment are required.'
        });
      }
  
      const updateQuery = `
        UPDATE ${dbName}.replies_comments 
        SET reply_comment = ?, updatedAt = CURRENT_TIMESTAMP 
        WHERE id = ?
      `;
  
      const [result] = await mysqlServerConnection.query(updateQuery, [comment, reply_id]);
  
      console.log("📦 Update Result:", result);
  
      if (result.affectedRows === 0) {
        console.warn("⚠️ Reply not found for update:", reply_id);
        return res.status(404).json({ success: false, message: 'Reply comment not found.' });
      }
  
      console.log("✅ Reply updated successfully:", reply_id);
      return res.status(200).json({
        success: true,
        message: 'Reply comment updated successfully.'
      });
  
    } catch (error) {
      console.error('❌ Error editing reply comment:', error);
      return res.status(500).json({
        success: false,
        message: 'Internal server error.'
      });
    }
  };
  
  const deleteReplyComment = async (req, res) => {
    try {
      const { reply_id } = req.body;
      console.log("reply_id----------------------:", reply_id);
      const dbName = req.user.db_name;
  
      console.log("🗑️ Delete Reply Request:", { reply_id, dbName });
  
      if (!reply_id) {
        console.warn("⚠️ Missing reply_id");
        return res.status(400).json({
          success: false,
          message: 'Reply ID is required.'
        });
      }
  
      const deleteQuery = `DELETE FROM ${dbName}.replies_comments WHERE id = ?`;
  
      const [result] = await mysqlServerConnection.query(deleteQuery, [reply_id]);
  
      console.log("📦 Delete Result:", result);
  
      if (result.affectedRows === 0) {
        console.warn("⚠️ Reply not found for deletion:", reply_id);
        return res.status(404).json({ success: false, message: 'Reply comment not found.' });
      }
  
      console.log("✅ Reply deleted successfully:", reply_id);
      return res.status(200).json({
        success: true,
        message: 'Reply comment deleted successfully.'
      });
  
    } catch (error) {
      console.error('❌ Error deleting reply comment:', error);
      return res.status(500).json({
        success: false,
        message: 'Internal server error.'
      });
    }
  };
  
  
  

const getRepliesByCommentId = async (req, res) => {
    try {
        const { comment_id } = req.body; // Extract comment_id from request body
        const dbName = req.user.db_name; // Assuming the user is authenticated and the DB name is attached to the request object


        // Validate required field
        if (!comment_id) {
            return res.status(400).json({
                success: false,
                data: { error_msg: 'Comment ID is required.' }
            });
        }

        // Query to fetch replies based on comment_id
        const selectQuery = `
            SELECT replies_comments.*, users.name FROM ${dbName}.replies_comments JOIN ${dbName}.users ON replies_comments.user_id = users.id
            WHERE comment_id = ?
        `;

        // Execute the select query
        const [rows] = await mysqlServerConnection.query(selectQuery, [comment_id]);

        console.log("Get replies by comment ID:\n",rows);
        // If no replies found
        if (rows.length === 0) {
            return res.status(200).json({
                success: false,
                data: { message: 'No replies found for the given comment ID.' }
            });
        }

        // Success response with fetched data
        return res.status(200).json({
            success: true,
            data: rows
        });
    } catch (error) {
        console.error('Error fetching replies:', error);
        return res.status(500).json({
            success: false,
            data: { error_msg: 'Internal server error.' }
        });
    }
};


// const addCourseReview = async (req, res) => {
//     try {
//         const { course_id, video_id, class_rating, mentor_rating = 0, review_text } = req.body;
//         const userId = req.user.userId;

//         // Validate required fields
//         if (!course_id || !class_rating) {
//             return res.status(400).json({
//                 success: false,
//                 data: { error_msg: 'Rating is required.' }
//             });
//         }

//         // Check if review already exists
//         const checkQuery = `
//             SELECT * FROM ${req.user.db_name}.course_reviews 
//             WHERE user_id = ? AND course_id = ? AND video_id = ?
//         `;
//         const [existingReview] = await mysqlServerConnection.query(checkQuery, [
//             userId,
//             course_id,
//             video_id || null
//         ]);

//         if (existingReview.length > 0) {
//             // Update existing review
//             const updateQuery = `
//                 UPDATE ${req.user.db_name}.course_reviews 
//                 SET class_rating = ?, mentor_rating = ?, review_text = ? 
//                 WHERE user_id = ? AND course_id = ? AND video_id = ?
//             `;
//             await mysqlServerConnection.query(updateQuery, [
//                 class_rating,
//                 mentor_rating,
//                 review_text || null,
//                 userId,
//                 course_id,
//                 video_id || null
//             ]);
//         } else {
//             // Insert new review
//             const insertQuery = `
//                 INSERT INTO ${req.user.db_name}.course_reviews 
//                 (user_id, course_id, video_id, class_rating, mentor_rating, review_text) 
//                 VALUES (?, ?, ?, ?, ?, ?)
//             `;
//             await mysqlServerConnection.query(insertQuery, [
//                 userId,
//                 course_id,
//                 video_id || null,
//                 class_rating,
//                 mentor_rating,
//                 review_text || null
//             ]);
//         }

//         // Recalculate average rating
//         const [currentAvgRating] = await mysqlServerConnection.query(
//             `SELECT AVG(class_rating) as course_rating FROM ${req.user.db_name}.course_reviews WHERE course_id = ?`,
//             [course_id]
//         );

//         const total_rating = Number(currentAvgRating[0].course_rating).toFixed(1);

//         // 🔧 Try updating course total_rating, catch any trigger-related errors
//         try {
//             console.log("Updating course total_rating to:", total_rating);
//             await mysqlServerConnection.query(
//                 `UPDATE ${req.user.db_name}.courses SET total_rating = ? WHERE id = ?`,
//                 [total_rating, course_id]
//             );
//         } catch (err) {
//             console.warn("⚠️ Skipped total_rating update due to trigger definer issue:", err.sqlMessage);
//         }

//         return res.status(200).json({
//             success: true,
//             data: { message: 'Course review added successfully.' }
//         });
//     } catch (error) {
//         console.error('Error adding/updating course review:', error);
//         return res.status(500).json({
//             success: false,
//             data: { error_msg: 'Internal server error.' }
//         });
//     }
// };

const addCourseReview = async (req, res) => {
    try {
        const { course_id, video_id, review_text, class_rating } = req.body;
        console.log("course_id:", course_id, "video_id:", video_id, "review_text:", review_text, "class_rating:", class_rating);
        const userId = req.user.userId;

        // Basic validation
        if (!course_id || !review_text) {
            return res.status(400).json({
                success: false,
                data: { error_msg: 'Course ID and review text are required.' }
            });
        }

        // Use class_rating from request or default to 5
        const ratingValue = class_rating || 5;
        const mentorRating = 0; // Set mentor rating to 0 by default

        // Check if review already exists
        const checkQuery = `
            SELECT id FROM ${req.user.db_name}.course_reviews 
            WHERE user_id = ? AND course_id = ? AND video_id = ?
        `;
        const [existingReview] = await mysqlServerConnection.query(checkQuery, [
            userId,
            course_id,
            video_id || null
        ]);

        if (existingReview.length > 0) {
            // Update existing review
            const updateQuery = `
                UPDATE ${req.user.db_name}.course_reviews 
                SET class_rating = ?, mentor_rating = ?, review_text = ? 
                WHERE user_id = ? AND course_id = ? AND video_id = ?
            `;
            await mysqlServerConnection.query(updateQuery, [
                ratingValue,
                mentorRating,
                review_text,
                userId,
                course_id,
                video_id || null
            ]);
        } else {
            // Insert new review
            const insertQuery = `
                INSERT INTO ${req.user.db_name}.course_reviews 
                (user_id, course_id, video_id, class_rating, mentor_rating, review_text) 
                VALUES (?, ?, ?, ?, ?, ?)
            `;
            await mysqlServerConnection.query(insertQuery, [
                userId,
                course_id,
                video_id || null,
                ratingValue,
                mentorRating,
                review_text
            ]);
        }

        return res.status(200).json({
            success: true,
            data: { message: 'Review saved successfully.' }
        });

    } catch (error) {
        console.error('Error saving review:', error);
        return res.status(500).json({
            success: false,
            data: { error_msg: 'Internal server error.' }
        });
    }
};

const editCourseReview = async (req, res) => {
    try {
      const { id, review_text, class_rating } = req.body;
      const dbName = req.user.db_name;
  
      if (!id || !review_text) {
        return res.status(400).json({
          success: false,
          message: 'Review ID and review text are required.'
        });
      }
  
      const updateQuery = `
        UPDATE ${dbName}.course_reviews
        SET class_rating = ?, review_text = ?, updatedAt = CURRENT_TIMESTAMP
        WHERE id = ?
      `;
  
      const [result] = await mysqlServerConnection.query(updateQuery, [
        class_rating || 5,
        review_text,
        id
      ]);
  
      if (result.affectedRows === 0) {
        return res.status(404).json({
          success: false,
          message: 'Review not found.'
        });
      }
  
      return res.status(200).json({
        success: true,
        message: 'Review updated successfully.'
      });
  
    } catch (error) {
      console.error('❌ Error updating review:', error);
      return res.status(500).json({
        success: false,
        message: 'Internal server error.'
      });
    }
  };


  



const getCourseReviews = async (req, res) => {
    try {
        const courseId = req.query.course_id;
        // Validate required fields
        if (!courseId) {
            return res.status(400).json({
                success: false,
                data: {
                    error_msg: 'Course ID is required.'
                }
            });
        }

        // SQL query to fetch reviews along with user details
        const query = `
            SELECT 
                reviews.id ,
                reviews.course_id,
                reviews.class_rating,
                reviews.review_text,
                reviews.createdAt ,
                reviews.updatedAt,
                users.id AS user_id,
                users.name AS user_name,
                users.email AS user_email,
                users.profile_pic_url AS user_profile_pic
            FROM ${req.user.db_name}.course_reviews AS reviews
            INNER JOIN ${req.user.db_name}.users AS users
            ON reviews.user_id = users.id
            WHERE reviews.course_id = ?
            ORDER BY reviews.updatedAt DESC
        `;
        const queryParams = [courseId];

        // Execute the query to get reviews with user details
        const [reviews] = await mysqlServerConnection.query(query, queryParams);

        // Format the reviews with user details
        const formattedReviews = reviews.map(review => ({
            review_id: review.id,
            course_id: review.course_id,
            user_id: review.user_id,
            class_rating: review.class_rating,
            review_text: review.review_text,
            review_createdAt: review.createdAt,
            review_updatedAt: review.updatedAt,
            user: {
                id: review.user_id,
                name: review.user_name,
                email: review.user_email,
                profile_picture: review.user_profile_pic
            }
        }));


        // Send the response with the formatted reviews
        return res.status(200).json({
            success: true,
            data: formattedReviews
        });
    } catch (error) {
        console.error('Error retrieving course reviews:', error);
        return res.status(500).json({
            success: false,
            data: {
                error_msg: 'Internal server error.'
            }
        });
    }
};


// controller/courseReviews.js
const deleteReview = async (req, res) => {
  /*─────────────────────────────*
   * 1. Extract request details  *
   *─────────────────────────────*/
  const reviewId = req.params.review_id;      // URL param
  const userId   = req.user?.id || req.user?.userId;  // depends on your JWT payload
  const dbName   = req.user?.db_name;

  console.log('🗑️  DELETE /course_reviews');
  console.log('• reviewId :', reviewId);
  console.log('• userId   :', userId);
  console.log('• dbName   :', dbName);

  try {
    /*─────────────────────────────*
     * 2. Basic validation         *
     *─────────────────────────────*/
    if (!reviewId) {
      console.warn('⚠️  reviewId missing in params');
      return res.status(400).json({
        success: false,
        data: { error_msg: 'Review ID is required.' }
      });
    }

    /*─────────────────────────────*
     * 3. Delete query             *
     *─────────────────────────────*/
    const sql = `
      DELETE FROM ${dbName}.course_reviews
      WHERE id = ? AND user_id = ?
    `;
    console.log('🔎 SQL:', sql.trim());

    const [result] = await mysqlServerConnection.query(sql, [reviewId, userId]);
    console.log('📊 MySQL result:', result);

    /*─────────────────────────────*
     * 4. Row-count check          *
     *─────────────────────────────*/
    if (result.affectedRows === 0) {
      console.warn('❌ No review found or permission denied.');
      return res.status(404).json({
        success: false,
        data: { error_msg: 'Review not found.' }
      });
    }

    /*─────────────────────────────*
     * 5. Success response         *
     *─────────────────────────────*/
    console.log('✅ Review deleted successfully.');
    return res.status(200).json({
      success: true,
      data: { message: 'Review deleted successfully.' }
    });

  } catch (err) {
    /*─────────────────────────────*
     * 6. Error handling           *
     *─────────────────────────────*/
    console.error('💥 Error deleting review:', err);
    return res.status(500).json({
      success: false,
      data: { error_msg: 'Internal server error.' }
    });
  }
};



const getAttachments = async (req, res) => {

    try {
        const { video_id } = req.params;
        if (!video_id) {
            res.status(401).json({
                message: "Please provide video ID"
            })
        }

        const [results] = await mysqlServerConnection.query(`SELECT * from ${req.user.db_name}.video_details where video_id = ?`, [video_id]);

        console.log(results)

        if (results.length === 0) {
            return res.status(200).json({
                success: false,
                data: {
                    error_msg: 'No attachments found for the specified video.'
                }
            });
        }

        return res.status(200).json({
            success: true,
            data: results,
        })



    } catch (error) {
        res.status(500).json({
            message: error.mesage || 'Internal server error'
        })
    }


}

const updateReplyComment = async (req, res) => {
    try {
        const userId = req.user.userId;
        const { replyCommentId, replyComment } = req.body;
        if (!replyCommentId || !replyComment) {
            return res.status(400).json({
                success: false,
                data: {
                    error_msg: 'Reply comment ID and reply comment content are required.'
                }
            });
        }

        console.log("replyCommentId:", replyCommentId, "replyComment:", replyComment, "userId:", userId);

        const updateQuery = `
            UPDATE ${req.user.db_name}.replies_comments
            SET reply_comment = ?, updatedAt = NOW()
            WHERE id = ? AND user_id = ?
        `;

        const [result] = await mysqlServerConnection.query(updateQuery, [replyComment, replyCommentId, userId]);

        console.log("result:", result);


        if (result.affectedRows === 0) {
            return res.status(404).json({
                success: false,

                data: {
                    error_msg: 'No reply comment found for the specified ID and user.'
                }
            });
        }

        return res.status(200).json({
            success: true,
            data: { message: 'Reply comment updated successfully.' }
        });
    }
    catch (error) {
        console.error('Error updating reply comment:', error);
        return res.status(500).json({
            success: false,
            data: { error_msg: 'Internal server error.' }
        });
    }
};






module.exports = {
    getNotes,
    addNote,
    getComments,
    addComment,
    deleteComment,
    updateLike,
    addCourseReview,
    getCourseReviews,
    deleteReview,
    getModuleNotes,
    getModuleComments,
    deleteNotes,
    addReplyComment,
    getRepliesByCommentId,
    getAttachments,
    updateNote,
    updateComment,

    deleteReplyComment,
    editReplyComment,
    editCourseReview
};
