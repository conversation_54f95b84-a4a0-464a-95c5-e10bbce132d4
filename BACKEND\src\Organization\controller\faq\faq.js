
const { response } = require('express');
const bcrypt = require('bcrypt');
const { mysqlServerConnection } = require('../../../db/db');
const createdb = require('../../../superAdmin/db/utils/createOrganizationDb');
const { Validate, hashPassword, generateAccessToken, generateRefreshToken } = require('../../../tools/tools');
const { body, check } = require('express-validator');
const fs = require('fs');
const path = require('path');
const { supportTeamMail } = require('../../../tools/emailtemplates');
const { v4: uuidv4 } = require('uuid');
const { format } = require('date-fns');

const MaindbName = process.env.MAIN_DB;



const CreateFaqs = async (req, res) => {
    try {
        const result = req.body;
        console.log(result, "++++++++++++++++++++++++")
        if (Object.keys(result).length == 0) {
            return res.status(422).json({
                success: false,
                data: {
                    error_msg: 'Following fields are required',
                    response: {
                        classroom_id: "",
                        course_id: "",
                        module_sequence: "",
                        module_name: "",
                    }
                }
            });
        }
        else {
            await mysqlServerConnection.query(`INSERT INTO ${req.user.db_name}.faqs (
            user_id,
            question,
            answer
            ) VALUES (?,?,?)`,
                [
                    result.classroom_id,
                    result.course_id,
                    result.module_sequence,
                    result.module_name,
                    result.course_category
                ]);

            res.status(201).json({
                success: true, data: {
                    message: 'Successfully created',
                    response: JSON.parse(result)

                }
            });
        }
    } catch (error) {
        console.error('Error creating organization:', error);
        res.status(500).json({ success: false, data: { error_msg: 'Internal server error.' } });
    }
};


const getFaqs = async (req, res) => {
  try {

    console.log("FAQ API Triggered");

    const [faq] = await mysqlServerConnection.query(
      `SELECT id, question, answer FROM ${req.user.db_name}.faqs WHERE is_deleted = 0 AND is_active = 1`
    ); 

    return res.status(200).json({
      success: true,
      data: {
        response: faq || [],
        message: 'FAQ data fetched successfully.',
      },
    });
  } catch (error) {
    console.error('Error fetching FAQs:', error);
    return res.status(500).json({
      success: false,
      data: { error_msg: 'Internal server error.' },
    });
  }
};


const raiseTicket = async (req, res) => {
    try {

        const result = req.body;
        console.log(result, "++++++++++++++++++++++++");

        const {subject, description, } = req.body;
        const ticket_id = uuidv4(1);

        if (!subject || !description) {
            return res.status(400).json({
                success: false,
                message: 'Subject and description are required.'
            });
        } else {


            // Insert ticket into the database
            const [ticket] = await mysqlServerConnection.query(
                `INSERT INTO ${req.user.db_name}.ticket (
                subject,
                    description,
                    sender_id,
                    ticket_id,
                    status
                ) VALUES (?,?,?,?,?)`,
                [
                    result.subject,
                    result.description,
                    fileUrl,  // Use fileUrl which is either the file path or null
                    req.user.userId,
                    ticket_id,
                    "Pending"
                ]
            );

            // Format the date
            const date = format(new Date(), 'MMM dd');
            const [user] = await mysqlServerConnection.query(`SELECT name FROM ${req.user.db_name}.users WHERE id=?`, [req.user.userId]);

            console.log(user);
            // Send an email notification
            supportTeamMail(req.user.username, ticket_id.slice(0, 6), date, result.description, user[0].name);

            // Respond with success
            res.status(201).json({
                success: true,
                data: {
                    message: 'Successfully raised the ticket',
                }
            });
        }
    } catch (error) {
        console.error('Error creating ticket:', error);
        res.status(500).json({ success: false, data: { error_msg: 'Internal server error.' } });
    }
};



const getVideoByCourseAndId = async (req, res) => {
    try {
      const { course_id, video_id } = req.body;
      console.log(course_id, video_id, "++++++++++++++++++++++++");
  
      if (!course_id || !video_id) {
        return res.status(422).json({
          success: false,
          data: {
            error_msg: 'course_id and video_id are required',
            response: { course_id: '', video_id: '' }
          }
        });
      }
  
      const [rows] = await mysqlServerConnection.query(
        `SELECT 
           id AS video_id,
           video_url
         FROM videos
         WHERE course_id = ? AND id = ? AND is_deleted = 0 AND is_active = 1`,
        [course_id, video_id]
      );
  
      if (rows.length === 0) {
        return res.status(404).json({
          success: false,
          data: {
            error_msg: 'Video not found',
            response: { course_id, video_id }
          }
        });
      }
  
      const video = rows[0];
  
      res.status(200).json({
        success: true,
        data: {
          message: 'Video fetched successfully',
          course_id,
          video_id: video.video_id,
          video_url: video.video_url
        }
      });
    } catch (error) {
      console.error('Error fetching video:', error);
      res.status(500).json({
        success: false,
        data: {
          error_msg: 'Internal server error.'
        }
      });
    }
  };
  

//   const raiseTicketV2 = async (req, res) => {
//     try {
//         const { subject, description } = req.body;
//         console.log(subject, description, "++++++++++++++++++++++++");
//         const file = req.files ? req.files.upload_file : null; // Check if files exist
  
//         if (!subject || !description) {
//             return res.status(422).json({
//                 success: false,
//                 data: {
//                     error_msg: 'Subject and description are required',
//                     response: { subject: '', description: '' }
//                 }
//             });
//         }
  
//         const ticket_id = uuidv4();
  
//       await mysqlServerConnection.query(
//         `INSERT INTO ${req.user.db_name}.ticket (
//           sender_id,
//           assigned_to_id,
//           subject,
//           description,
//           file_url,
//           status,
//           ticket_id
//         ) VALUES (?, ?, ?, ?, ?, ?)`,
//         [
//           req.user.userId,         // sender_id
//           null,                    // assigned_to_id (null for now)
//           subject, // description with subject
//           description,
//           null,                    // file_url
//           'Pending',               // status
//           ticket_id                // ticket_id
//         ]
//       );
  
//         res.status(201).json({
//             success: true,
//             data: {
//                 message: 'Ticket created successfully',
//                 ticket_id: ticket_id.slice(0, 8)
//             }
//         });
//     } catch (error) {
//         console.error('Error raising ticket:', error);
//         res.status(500).json({
//             success: false,
//             data: {
//                 error_msg: 'Internal server error.'
//             }
//         });
//     }
// };

const raiseTicketV2 = async (req, res) => {
  try {
    const { subject, description } = req.body;
    const sender_id = req.user.userId;
    const db_name = req.user.db_name;

    if (!subject || !description) {
      return res.status(422).json({
        success: false,
        data: {
          error_msg: 'Subject and description are required',
          response: { subject: '', description: '' }
        }
      });
    }

    // ✅ Generate 6-char unique ticket ID
const ticket_id = `RTI-${Math.floor(100000 + Math.random() * 900000)}`;

    await mysqlServerConnection.query(
      `INSERT INTO ${db_name}.ticket (
        sender_id,
        assigned_to_id,
        subject,
        description,
        file_url,
        status,
        ticket_id,
        createdAt,
        updatedAt
      ) VALUES (?, NULL, ?, ?, NULL, 'Pending', ?, NOW(), NOW())`,
      [sender_id, subject, description, ticket_id]
    );

    res.status(201).json({
      success: true,
      data: {
        message: 'Ticket created successfully',
        ticket_id: ticket_id
      }
    });

  } catch (error) {
    console.error('Error raising ticket:', error);
    res.status(500).json({
      success: false,
      data: {
        error_msg: 'Internal server error.'
      }
    });
  }
};


const userAllRaiseTicket = async (req, res) => {
  try {
    const dbName = req.user.db_name;
    const userId = req.user.userId;

    const page = parseInt(req.body.page, 10) || 1;
    const limit = parseInt(req.body.limit, 10) || 10;
    const searchTerm = req.body.search?.trim() || '';
    const offset = (page - 1) * limit;

    const searchCondition = searchTerm ? 'AND description LIKE ?' : '';
    const searchValue = `%${searchTerm}%`;

    // ✅ Main query including is_deleted = 0
    const query = `
      SELECT ticket_id, createdAt, description, status, subject, response 
      FROM ${dbName}.ticket
      WHERE sender_id = ? AND is_deleted = 0 ${searchCondition}
      ORDER BY createdAt DESC
      LIMIT ? OFFSET ?
    `;
    const queryParams = searchTerm
      ? [userId, searchValue, limit, offset]
      : [userId, limit, offset];

    const [tickets] = await mysqlServerConnection.query(query, queryParams);

    // ✅ Count query including is_deleted = 0
    const countQuery = `
      SELECT COUNT(*) AS count
      FROM ${dbName}.ticket
      WHERE sender_id = ? AND is_deleted = 0 ${searchCondition}
    `;
    const countParams = searchTerm
      ? [userId, searchValue]
      : [userId];

    const [[{ count }]] = await mysqlServerConnection.query(countQuery, countParams);
    const totalPages = Math.ceil(count / limit);

    return res.status(200).json({
      message: 'Successfully retrieved tickets',
      success: true,
      data: {
        tickets,
        pagination: {
          page,
          limit,
          totalPages,
          totalCount: count,
        },
      },
    });
  } catch (error) {
    console.error('Error retrieving tickets:', error);
    return res.status(500).json({
      success: false,
      data: { error_msg: 'Internal server error.' },
    });
  }
};


const DeleteTicket = async (req, res) => {
  try {
    const { ticket_id } = req.params;

    console.log('Ticket ID',ticket_id);

    if (!ticket_id) {
      return res.status(404).json({
        success: false,
        data: {
          error_msg: 'Ticket is required',
          response: { ticket_id: '' }
        }
      });
    }

    // also check if the ticket exists
    const [ticketExists] = await mysqlServerConnection.query(
      `SELECT * FROM ${req.user.db_name}.ticket WHERE ticket_id = ? AND is_deleted = 0`,
      [ticket_id]
    );

    if (ticketExists.length === 0) {
      return res.status(404).json({
        success: false,
        data: {
          error_msg: 'Ticket not found or already deleted',
          response: { ticket_id }
        }
      });
    }

    await mysqlServerConnection.query(
      `UPDATE ${req.user.db_name}.ticket SET is_deleted = 1 WHERE ticket_id = ?`,
      [ticket_id]
    );

    res.status(200).json({
      success: true,
      data: {
        message: 'Ticket deleted successfully',
      }
    });

  } catch (error) {
    console.error('Error deleting ticket:', error);
    res.status(500).json({
      success: false,
      data: { error_msg: 'Internal server error.' },
    });
  }

}





module.exports = {
    getFaqs,
    raiseTicket,
    userAllRaiseTicket,
    raiseTicketV2,
    getVideoByCourseAndId,
    DeleteTicket
};