/* Classroom specific styles */
.classroom-image {
  position: relative;
  overflow: hidden;
}

.classroom-image img {
  width: 100%;
  height: 200px;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.classroom-image:hover .image-overlay {
  opacity: 1;
}

.classroom-image:hover img {
  transform: scale(1.05);
}

.classroom-description {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  color: #6c757d;
  font-size: 0.9rem;
  line-height: 1.5;
  height: 4.5em;
}

.card-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 0.5rem;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Loading and No Results styles */
.spinner-border {
  width: 2rem;
  height: 2rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .classroom-image {
    height: 160px;
  }

  .card-body {
    padding: 1rem;
  }

  .classroom-description {
    -webkit-line-clamp: 2;
    height: 3em;
  }
}

.main-card {
  border: none;
  box-shadow: 0 0 20px rgba(0,0,0,0.05);
  border-radius: 12px;
}

.custom-card {
  border: none;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
  border-radius: 10px;
  transition: all 0.3s ease;
}

.hover-shadow:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 16px rgba(0,0,0,0.1);
}

.date-badge {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 1rem;
  transition: all 0.3s ease;
}

.custom-card:hover .date-badge {
  background-color: #e7f1ff;
  color: #0d6efd;
}

.video-thumbnail {
  position: relative;
  overflow: hidden;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
}

.play-button {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 60px;
  height: 60px;
  background-color: rgba(255,255,255,0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.play-button:hover {
  background-color: #fff;
  transform: translate(-50%, -50%) scale(1.1);
}

.video-duration {
  position: absolute;
  bottom: 10px;
  right: 10px;
  background-color: rgba(0,0,0,0.7);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.875rem;
}

.custom-select {
  border-radius: 6px;
  border-color: #dee2e6;
  padding: 0.375rem 2.25rem 0.375rem 0.75rem;
}

.stats-card {
  background-color: #fff;
}

.progress {
  background-color: #e9ecef;
  border-radius: 10px;
  overflow: hidden;
}

.progress-bar {
  border-radius: 10px;
}

.stats-item {
  transition: all 0.3s ease;
}

.stats-item:hover {
  background-color: #f8f9fa;
}

.assessment-icon {
  transition: all 0.3s ease;
}

.custom-card:hover .assessment-icon {
  transform: scale(1.1);
}

.overview-item {
  transition: all 0.3s ease;
}

.overview-item:hover {
  transform: translateY(-2px);
}

.chat-messages {
  background-color: #f8f9fa;
}

.message-content {
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
  transition: all 0.3s ease;
}

.message-content:hover {
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.chat-input {
  background-color: #fff;
}

.chat-input .form-control {
  border-radius: 20px;
  padding-left: 1rem;
  padding-right: 1rem;
  border-color: #dee2e6;
}

.chat-input .form-control:focus {
  box-shadow: none;
  border-color: #0d6efd;
}

.chat-input .btn {
  border-radius: 20px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .overview-item {
    padding: 1rem !important;
  }
  
  .chat-messages {
    height: 400px !important;
  }
} 

.card {
  box-shadow: rgba(0, 0, 0, 0.1) 0px 4px 6px -1px, rgba(0, 0, 0, 0.06) 0px 2px 4px -1px;
  border: 2px solid #e0e0e0;
}

.search-control {
  position: relative;
  margin-bottom: 1.5rem;
}

.search-input {
  padding-left: 2.5rem;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="11" cy="11" r="8"></circle><line x1="21" y1="21" x2="16.65" y2="16.65"></line></svg>');
  background-repeat: no-repeat;
  background-position: 12px center;
  background-size: 16px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.search-input:focus {
  box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.15);
  border-color: #86b7fe;
}

.classroom-card {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  cursor: pointer;
  border: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.classroom-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.btn.loading {
  background-color: #e9ecef;
  border-color: #e9ecef;
  color: #6c757d;
  cursor: not-allowed;
  opacity: 0.8;
}

.btn.loading:hover {
  background-color: #e9ecef;
  border-color: #e9ecef;
  transform: none;
}
.classroom-overlay .btn {
  opacity: 0;
  transform: translateY(10px);
  transition: all 0.3s ease;
  padding: 0.8rem 1.5rem;
  font-size: 0.85rem;
  /* border-radius: 30px; */
  min-width: auto;
  width: fit-content;
}

/* Show the button only on card hover */
.classroom-card:hover .classroom-overlay .btn {
  opacity: 1;
  transform: translateY(0);
}

.classroom-overlay .btn.loading {
  position: relative;
  opacity: 0.8;
  cursor: not-allowed;
  transition: all 0.3s ease;
}

.classroom-overlay .btn {
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 8px 16px;
  border-radius: 4px;
  font-weight: 500;
}

.classroom-overlay .btn:not(.loading):hover {
  transform: translateY(-2px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.enter-classroom-btn {
  background: var(--primary-color);
  color: white;
  border: none;
  padding: 8px 20px;
  border-radius: 6px;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  gap: 6px;
  transition: all 0.3s ease;
  cursor: pointer;
  min-width: 160px;
  justify-content: center;
  transform: translateY(20px);
  opacity: 0;
}

.classroom-image:hover .enter-classroom-btn {
  transform: translateY(0);
  opacity: 1;
}

.enter-classroom-btn:hover:not(:disabled) {
  background: var(--primary-color);
  transform: scale(1.05) !important;
  box-shadow: 0 2px 8px rgba(74, 144, 226, 0.2);
}

.enter-classroom-btn:disabled {
  background: var(--primary-color);
  opacity: 0.85;
  cursor: not-allowed;
  color: white;
}

.loading-text {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  color: white;
  margin-left: 4px;
}

.loading-text .dot {
  animation: loadingDots 1.4s infinite;
  opacity: 0;
  margin: 0;
  font-size: 20px;
  line-height: 10px;
  position: relative;
  top: -2px;
}

@keyframes loadingDots {
  0% { opacity: 0; transform: translateY(0px); }
  50% { opacity: 1; transform: translateY(-2px); }
  100% { opacity: 0; transform: translateY(0px); }
}
