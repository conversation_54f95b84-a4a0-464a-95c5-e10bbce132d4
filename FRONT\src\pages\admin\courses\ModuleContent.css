.content-card {
    transition: all 0.3s ease;
}

.content-card.dragging {
    opacity: 0.5;
    transform: scale(1.02);
    box-shadow: 0 0 20px rgba(13, 110, 253, 0.2);
}

.content-card:hover {
    cursor: pointer;
}

.content-card .card {
    position: relative;
    overflow: visible; /* Allow drag handle to show outside card */
}

.drag-handle {
    cursor: grab;
    padding: 8px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.drag-handle:hover {
    background-color: #e9ecef !important;
    border-color: #0d6efd !important;
    transform: scale(1.05);
}

.drag-handle:active {
    cursor: grabbing;
    transform: scale(0.95);
}

.info-bar {
    background-color: #e7f1ff;
    border-radius: 4px;
    padding: 8px 16px;
    margin-bottom: 16px;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    color: #0d6efd;
}

.content-stats {
    display: flex;
    gap: 16px;
    font-size: 14px;
    color: #6c757d;
}

.content-stat {
    display: flex;
    align-items: center;
    gap: 4px;
}

.position-number {
    position: absolute;
    left: -12px;
    top: 50%;
    transform: translateY(-50%);
    width: 24px;
    height: 24px;
    background-color: #e7f1ff;
    color: #0d6efd;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 500;
    font-size: 14px;
    border: 2px solid #fff;
}

.content-preview {
    width: 180px;
    flex-shrink: 0;
}

.content-preview iframe {
    width: 100%;
    height: 100px;
    border: none;
    border-radius: 4px;
}

.content-preview img {
    width: 100%;
    height: 100px;
    object-fit: cover;
    border-radius: 4px;
}

.content-description {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    font-size: 14px;
    line-height: 1.4;
}

.action-button {
    width: 32px;
    height: 32px;
    padding: 0;
}

.content-meta {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex-wrap: wrap;
}

.meta-item {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    white-space: nowrap;
}

.content-meta .icon {
    opacity: 0.7;
}

.upload-area {
    border: 2px dashed #dee2e6;
    border-radius: 8px;
    padding: 2rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.upload-area:hover, 
.upload-area.dragging {
    border-color: #0d6efd;
    background-color: #f8f9fa;
}

.upload-text {
    color: #0d6efd;
    font-size: 1rem;
    margin-bottom: 0.5rem;
}

.upload-hint {
    color: #6c757d;
    font-size: 0.875rem;
}

.preview-video {
    width: 160px;
    height: 90px;
    object-fit: cover;
    border-radius: 4px;
}

.preview-placeholder {
    width: 160px;
    height: 90px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f8f9fa;
    border-radius: 4px;
}

.content-card.potential-drop-target {
    border: 2px solid transparent;
    margin: -2px;
}

.content-card.drop-target-hover {
    border: 2px solid #0d6efd;
    box-shadow: 0 0 15px rgba(13, 110, 253, 0.3);
    transform: scale(1.01);
    margin: -2px;
}

@media (max-width: 768px) {
    .content-meta {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .meta-item {
        width: 100%;
        justify-content: flex-start;
    }

    .content-preview {
        width: 100% !important;
        margin-bottom: 1rem;
    }

    .preview-video,
    .document-preview {
        width: 100% !important;
        height: auto !important;
        min-height: 200px;
    }

    .content-card .card-body {
        padding: 1rem !important;
    }

    .content-info {
        width: 100%;
        padding-left: 0 !important;
        margin-top: 1rem;
    }

    .drag-handle {
        display: none;
    }
}

/* Framer Motion Drag and Drop Styles */
.content-card {
    will-change: transform, opacity;
    transform-origin: center;
}

/* Smooth transitions for all content cards */
.content-card .card {
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Enhanced drag handle styling */
.drag-handle {
    cursor: grab;
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
}

.drag-handle:active {
    cursor: grabbing;
}

/* Loading overlay for reordering */
.reorder-loading-overlay {
    backdrop-filter: blur(2px);
    -webkit-backdrop-filter: blur(2px);
}

/* Drag instruction styling */
.drag-instruction {
    opacity: 0.7;
    transition: opacity 0.3s ease;
}

.drag-instruction:hover {
    opacity: 1;
}
