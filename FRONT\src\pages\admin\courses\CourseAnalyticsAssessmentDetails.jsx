import React, { useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';

import { Icon } from '@iconify/react';
import { getAssessmentDetails } from '../../../services/adminService';
import { decodeData } from '../../../utils/encodeAndEncode';

function CourseAnalyticsAssessmentDetails() {
  const { assessmentId, courseId } = useParams();
  const decodedCourseId = courseId ? decodeData(courseId) : null;
  const decodedAssessmentId = assessmentId ? decodeData(assessmentId) : null;
  const navigate = useNavigate();

  const [assessmentDetails, setAssessmentDetails] = useState(null);
  const [recentAttempts, setRecentAttempts] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchAssessmentDetails();
  }, [decodedAssessmentId, decodedCourseId]);

  const fetchAssessmentDetails = async () => {
    try {
      setLoading(true);
        const response = await getAssessmentDetails(decodedAssessmentId, decodedCourseId);

      console.log('Assessment Details Response:', response);

      if (response.success && response.data) {
        setAssessmentDetails(response.data.assessment);
        setRecentAttempts(response.data.recent_attempts || []);
      }
    } catch (error) {
      console.error('Error fetching assessment details:', error);
    } finally {
      setLoading(false);
    } 

  };

  if (loading) {
    return (
      <div className="d-flex justify-content-center align-items-center" style={{ minHeight: '400px' }}>
        <div className="spinner-border text-primary" role="status">
          <span className="visually-hidden">Loading...</span>
        </div>
      </div>
    );
  }

  if (!assessmentDetails) {
    return (
      <div className="text-center py-5">
        <div className="text-muted">Assessment not found</div>
        <button className="btn btn-primary mt-3" onClick={() => navigate(-1)}>
          Go Back
        </button>
      </div>
    );
  }

  return (
    <>

      {/* Assessment Overview */}
      <div className="row mb-4">
        <div className="col-12">
          <div className="card">
            <div className="card-body">
              <div className="row g-3">
                <div className="col-md-3">
                  <div className="border rounded p-3">
                    <div className="text-muted mb-1">Total Questions</div>
                    <div className="h5 mb-0">{assessmentDetails.total_questions || 0}</div>
                  </div>
                </div>
                <div className="col-md-3">
                  <div className="border rounded p-3">
                    <div className="text-muted mb-1">Total Attempts</div>
                    <div className="h5 mb-0">{assessmentDetails.total_attempts || 0}</div>
                  </div>
                </div>
                <div className="col-md-3">
                  <div className="border rounded p-3">
                    <div className="text-muted mb-1">Average Score</div>
                    <div className="h5 mb-0">{assessmentDetails.average_score || 0}%</div>
                  </div>
                </div>
                <div className="col-md-3">
                  <div className="border rounded p-3">
                    <div className="text-muted mb-1">Pass Rate</div>
                    <div className="h5 mb-0">{assessmentDetails.pass_rate || 0}%</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Assessment Information */}
      <div className="row mb-4">
        <div className="col-md-6">
          <div className="card">
            <div className="card-body">
              <h5 className="card-title mb-3">Assessment Information</h5>
              <div className="row g-3">
                <div className="col-6">
                  <div className="text-muted">Duration</div>
                  <div className="fw-medium">{assessmentDetails.duration ? `${assessmentDetails.duration} minutes` : 'Not specified'}</div>
                </div>
                <div className="col-6">
                  <div className="text-muted">Pass Percentage</div>
                  <div className="fw-medium">{assessmentDetails.pass_percentage || 0}%</div>
                </div>
                <div className="col-6">
                  <div className="text-muted">Points</div>
                  <div className="fw-medium">{assessmentDetails.earn_point || 0}</div>
                </div>
                <div className="col-6">
                  <div className="text-muted">Status</div>
                  <div className="fw-medium">
                    <span className={`badge ${assessmentDetails.is_active ? 'bg-success' : 'bg-secondary'}`}>
                      {assessmentDetails.is_active ? 'Active' : 'Inactive'}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="col-md-6">
          <div className="card">
            <div className="card-body">
              <h5 className="card-title mb-3">Course Information</h5>
              <div className="text-muted">Course Name</div>
              <div className="fw-medium">{assessmentDetails.course_name || 'N/A'}</div>
              <div className="text-muted mt-3">Created Date</div>
              <div className="fw-medium">
                {assessmentDetails.createdAt
                  ? new Date(assessmentDetails.createdAt).toLocaleDateString()
                  : 'N/A'
                }
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Recent Attempts */}
      <div className="row">
        <div className="col-12">
          <div className="card">
            <div className="card-body">
              <h5 className="card-title mb-4">Recent Attempts</h5>
              {recentAttempts.length === 0 ? (
                <div className="text-center py-4 text-muted">
                  No attempts found for this assessment
                </div>
              ) : (
                <div className="table-responsive">
                  <table className="table table-borderless mb-0">
                    <thead>
                      <tr>
                        <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500' }}>User</th>
                        <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500' }}>Score</th>
                        <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500' }}>Status</th>
                        <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500' }}>Last Attempt</th>
                      </tr>
                    </thead>
                    <tbody>
                      {recentAttempts.map((attempt, index) => (
                        <tr key={index} className="border-bottom">
                          <td className="py-3 align-middle">
                            <div className="d-flex align-items-center gap-2">
                              <div className="bg-primary rounded-circle d-flex align-items-center justify-content-center"
                                   style={{ width: '32px', height: '32px', fontSize: '14px', color: 'white' }}>
                                {attempt.user_name?.charAt(0)?.toUpperCase() || 'U'}
                              </div>
                              <div>
                                <div className="fw-medium">{attempt.user_name || 'Unknown User'}</div>
                                <div className="text-muted small">{attempt.email}</div>
                              </div>
                            </div>
                          </td>
                          <td className="py-3 align-middle">
                            <div className="fw-medium">{attempt.score_percentage}%</div>
                            <div className="text-muted small">
                              {attempt.correct_answers}/{attempt.total_questions} correct
                            </div>
                          </td>
                          <td className="py-3 align-middle">
                            <span className={`badge ${attempt.passed ? 'bg-success' : 'bg-danger'}`}>
                              {attempt.passed ? 'Passed' : 'Failed'}
                            </span>
                          </td>
                          <td className="py-3 align-middle">
                            <div className="text-muted">
                              {attempt.last_attempt_date
                                ? new Date(attempt.last_attempt_date).toLocaleDateString()
                                : 'N/A'
                              }
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </>
  );
}

export default CourseAnalyticsAssessmentDetails;
