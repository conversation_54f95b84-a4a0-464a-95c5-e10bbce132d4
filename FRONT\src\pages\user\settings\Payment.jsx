import React, { useState, useEffect, useMemo } from 'react';
import { Table, Form, InputGroup, Button } from 'react-bootstrap';
import { Icon } from '@iconify/react';
import NoData from '../../../components/common/NoData';
import { getPaymentHistory } from '../../../services/userService';

function Payment() {
  const [searchTerm, setSearchTerm] = useState('');
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(10);
  const [allPaymentHistory, setAllPaymentHistory] = useState([]);

  useEffect(() => {
    fetchPaymentHistory();
  }, []); // Fetch all data once

  const fetchPaymentHistory = async () => {
    try {
      const response = await getPaymentHistory();
      console.log('Payment History response', response);
      if (response.success) {
        setAllPaymentHistory(response.data);
      }
    } catch (error) {
      console.error('Error fetching payment history:', error);
    }
  };

  // Filter and paginate data on the frontend
  const { filteredData, paginatedData, totalPages, totalCount } = useMemo(() => {
    // Filter data based on search term
    const filtered = allPaymentHistory.filter(item => {
      const searchLower = searchTerm.toLowerCase();
      return (
        item.payment_uid?.toLowerCase().includes(searchLower) ||
        item.course_name?.toLowerCase().includes(searchLower) ||
        item.amount?.toString().includes(searchLower) ||
        item.payment_method?.toLowerCase().includes(searchLower) ||
        item.status?.toLowerCase().includes(searchLower) ||
        new Date(item.created_at).toLocaleDateString().toLowerCase().includes(searchLower)
      );
    });

    // Calculate pagination
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginated = filtered.slice(startIndex, endIndex);
    const totalPagesCalc = Math.ceil(filtered.length / limit);

    return {
      filteredData: filtered,
      paginatedData: paginated,
      totalPages: totalPagesCalc,
      totalCount: filtered.length
    };
  }, [allPaymentHistory, searchTerm, page, limit]);

  // Reset to first page when search term or limit changes
  useEffect(() => {
    setPage(1);
  }, [searchTerm, limit]);

  const getStatusBadge = (status) => {
    switch (status.toLowerCase()) {
      case 'completed':
        return <span className="badge bg-success">Completed</span>;
      case 'pending':
        return <span className="badge bg-warning text-dark">Pending</span>;
      case 'failed':
        return <span className="badge bg-danger">Failed</span>;
      default:
        return <span className="badge bg-secondary">Unknown</span>;
    }
  };

  const truncateTransactionId = (payment_uid) => {
    return payment_uid.substring(0, 11);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString();
  };

  return (
    <>
      <div className="row py-3">
        <div className="col-12 mb-3">
          <div className="d-flex flex-wrap justify-content-between align-items-center">
            <h4 className="mb-2 mb-md-0">Payment History</h4>
          </div>
        </div>

        <div className="col-12">
          <div className="d-flex justify-content-between align-items-center mb-3 flex-wrap">
            <InputGroup className="w-auto">
              <Form.Control
                placeholder="Search payments..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="rounded-3"
              />
            </InputGroup>
            <div className="d-flex align-items-center gap-2">
              <span className="d-none d-md-flex">Size:</span>
              <Form.Select
                size="sm"
                value={limit}
                onChange={(e) => {
                  setLimit(Number(e.target.value));
                  setPage(1); // Reset to first page when changing limit
                }}
                className="rounded-3"
              >
                {[10, 50, 100].map((num) => (
                  <option key={num} value={num}>{num}</option>
                ))}
              </Form.Select>
            </div>
          </div>

          <div style={{ overflowX: 'auto' }}>
            <Table striped bordered hover responsive className="table-hover" style={{ minWidth: '900px' }}>
              <thead className="table-primary">
                <tr>
                  <th style={{ whiteSpace: 'nowrap', paddingInline: '10px' }}>SL No.</th>
                  <th style={{ whiteSpace: 'nowrap', paddingInline: '10px' }}>Transaction ID</th>
                  <th style={{ whiteSpace: 'nowrap', paddingInline: '10px' }}>Course Name</th>
                  <th style={{ whiteSpace: 'nowrap', paddingInline: '10px' }}>Amount</th>
                  <th style={{ whiteSpace: 'nowrap', paddingInline: '10px' }}>Payment Method</th>
                  <th style={{ whiteSpace: 'nowrap', paddingInline: '10px' }}>Status</th>
                  <th style={{ whiteSpace: 'nowrap', paddingInline: '10px' }}>Payment Date</th>
                  <th style={{ whiteSpace: 'nowrap', paddingInline: '10px' }} className="text-center">Action</th>
                </tr>
              </thead>
              <tbody>
                {paginatedData.length > 0 ? (
                  paginatedData.map((item, index) => (
                    <tr key={item.id}>
                      <td className="text-center">{((page - 1) * limit) + index + 1}</td>
                      <td>{truncateTransactionId(item.payment_uid)}</td>
                      <td style={{ verticalAlign: 'middle' }}>
                        <div className="truncate-2-line" title={item.course_name}>
                          {item.course_name}
                        </div>
                      </td>
                      <td>${item.amount}</td>
                      <td style={{ textTransform: 'capitalize' }}>{item.payment_method}</td>
                      <td>{getStatusBadge(item.status)}</td>
                      <td>{formatDate(item.created_at)}</td>
                      <td className="d-flex justify-content-center align-items-center gap-2">
                        <Button
                          variant="outline-primary"
                          size="sm"
                          className="rounded-3"
                          disabled={!item.receipt_url}
                          style={{
                            cursor: !item.receipt_url ? 'not-allowed' : 'pointer',
                            pointerEvents: !item.receipt_url ? 'auto' : 'auto',
                            width: '36px',
                            height: '36px',
                            padding: '4px',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center'
                          }}
                          href={item.receipt_url || '#'}
                          target={item.receipt_url ? "_blank" : undefined}
                          onClick={!item.receipt_url ? (e) => e.preventDefault() : undefined}
                          title="Download Receipt PDF"
                        >
                          <Icon icon="mdi:file-pdf-box" width="20" height="20" />
                        </Button>
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan="8" className="text-center">No payment records found.</td>
                  </tr>
                )}
              </tbody>
            </Table>
          </div>

          <div className="d-flex justify-content-center justify-content-md-between align-items-center flex-wrap gap-2 mt-3">
            <div>Showing {paginatedData.length > 0 ? ((page - 1) * limit) + 1 : 0} to {Math.min(page * limit, totalCount)} of {totalCount} entries</div>
            <div className="d-flex align-items-center gap-2" style={{ whiteSpace: 'nowrap' }}>
              <Button variant="outline-secondary" size="sm" disabled={page === 1} onClick={() => setPage(page - 1)}>Prev</Button>
              <span>Page {page} of {totalPages}</span>
              <Button variant="outline-secondary" size="sm" disabled={page === totalPages} onClick={() => setPage(page + 1)}>Next</Button>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}

export default Payment;
