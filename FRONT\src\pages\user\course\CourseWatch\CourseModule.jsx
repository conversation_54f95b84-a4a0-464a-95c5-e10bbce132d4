import React, { useState, useEffect, useCallback, memo } from 'react';
import { Icon } from '@iconify/react';
import './CourseModule.css';
import { getCourseCompletePercentage } from '../../../../services/userService';

// Memoize the progress bar component
const ProgressBar = memo(({ progress }) => (
  <div className="progress-bar-container" role="progressbar" 
    aria-valuenow={progress} aria-valuemin="0" aria-valuemax="100"
    style={{ 
      height: '8px', 
      backgroundColor: '#e9ecef',
      borderRadius: '4px',
      overflow: 'hidden',
      margin: '8px 0'
    }}>
    <div
      className="progress-bar"
      style={{ 
        width: `${progress}%`,
        height: '100%',
        backgroundColor: '#0d6efd',
        transition: 'width 0.6s ease'
      }}
    ></div>
  </div>
));

function CourseModule({ courseModules, courseId, onContentClick, selectedContent, onProgressUpdate }) {
  const [expandedModule, setExpandedModule] = useState(() => {
    const saved = localStorage.getItem(`module-expanded-${courseId}`);
    return saved ? JSON.parse(saved) : null;
  });
  const [courseProgress, setCourseProgress] = useState(0);
  const [isLoading, setIsLoading] = useState(true);

  // Memoize the progress update function
  const updateProgress = useCallback(async () => {
    if (!courseId) return;
    
    try {
      const response = await getCourseCompletePercentage(courseId);
      if (response?.data?.overallCompletionPercentage) {
        const progress = parseFloat(response.data.overallCompletionPercentage);
        setCourseProgress(prev => {
          // Only update if progress has changed
          if (prev !== progress) {
            if (onProgressUpdate) {
              onProgressUpdate(progress);
            }
            return progress;
          }
          return prev;
        });
      }
    } catch (error) {
      console.error('Error updating course progress:', error);
    }
  }, [courseId, onProgressUpdate]);

  useEffect(() => {
    if (selectedContent && courseModules) {
      const moduleWithContent = courseModules.find(module => 
        module.content?.some(item => item.id === selectedContent.id)
      );
      if (moduleWithContent) {
        setExpandedModule(moduleWithContent.id);
      }
    }
  }, [selectedContent, courseModules]);

  // Initial progress fetch
  useEffect(() => {
    if (courseId) {
      setIsLoading(true);
      updateProgress().finally(() => setIsLoading(false));
    }
  }, [courseId, updateProgress]);

  // Handle progress updates
  useEffect(() => {
    if (courseModules) {
      updateProgress();
    }
  }, [courseModules, updateProgress]);

  useEffect(() => {
    if (expandedModule !== null) {
      localStorage.setItem(`module-expanded-${courseId}`, JSON.stringify(expandedModule));
    }
  }, [expandedModule, courseId]);

  async function fetchCourseProgress() {
    try {
      setIsLoading(true);
      const response = await getCourseCompletePercentage(courseId);
      console.log('📊 Initial course progress:', response);
      
      if (response?.data?.overallCompletionPercentage) {
        const progress = parseFloat(response.data.overallCompletionPercentage);
        setCourseProgress(progress);
        
        // Call progress update callback if provided
        if (onProgressUpdate) {
          onProgressUpdate(progress);
        }
      }
    } catch (error) {
      console.error('Error fetching course progress:', error);
    } finally {
      setIsLoading(false);
    }
  }

  const getContentIcon = (type) => {
    switch (type?.toLowerCase()) {
      case 'video':
        return 'mdi:play-circle-outline';
      case 'assessment':
        return 'mdi:help-circle-outline';
      case 'document':
        return 'mdi:file-document-outline';
      case 'survey':
        return 'mdi:clipboard-text-outline';
      default:
        return 'mdi:circle-outline';
    }
  };

  const toggleModule = (moduleId) => {
    setExpandedModule(expandedModule === moduleId ? null : moduleId);
  };

  const calculateModuleProgress = (module) => {
    if (!Array.isArray(module.content) || module.content.length === 0) return 0;
    const completedItems = module.content.filter(item => item.completed === 1).length;
    return (completedItems / module.content.length) * 100;
  };

  const handleKeyPress = (e, action) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      action();
    }
  };

  if (isLoading) {
    return (
      <div className="course-content" aria-busy="true">
        <h2>Course Content</h2>
        <div className="text-center p-4">
          <div className="spinner-border text-primary" role="status">
            <span className="visually-hidden">Loading course progress...</span>
          </div>
        </div>
      </div>
    );
  }
 
  return (
    <div className="course-content" role="navigation" aria-label="Course modules">
      <h2>Course Content</h2>
      <div className="progress-info">
        <span className='font-weight-bold font-size-16' style={{ fontWeight: 'bold', fontSize: '16px' }}>
          {courseProgress.toFixed(2)}% completed
        </span>
        <ProgressBar progress={courseProgress} />
      </div>

      <div className="modules-list">
        {Array.isArray(courseModules) && courseModules.map((module, index) => {
          const moduleProgress = calculateModuleProgress(module);
          return (
            <div key={module.id} className="module-item">
              <div
                className={`module-header ${expandedModule === module.id ? 'expanded' : ''}`}
                onClick={() => toggleModule(module.id)}
                onKeyPress={(e) => handleKeyPress(e, () => toggleModule(module.id))}
                role="button"
                tabIndex={0}
                aria-expanded={expandedModule === module.id}
                aria-controls={`module-content-${module.id}`}
              >
                <div className="module-title">
                  <p className='font-size-14' style={{ fontSize: '24px' }}>Module {index + 1}</p>
                  <p className='font-size-14'>{module.module_name}</p>
                  <div className="module-progress" 
                    role="progressbar" 
                    aria-valuenow={moduleProgress} 
                    aria-valuemin="0" 
                    aria-valuemax="100">
                    <div className="progress-bar-mini">
                      {/* <div className="progress-fill" style={{ width: `${moduleProgress}%` }}></div> */}
                    </div>
                    {/* <span className="progress-text">{Math.round(moduleProgress)}%</span> */}
                  </div>
                </div>
                <Icon
                  icon={expandedModule === module.id ? 'mdi:chevron-up' : 'mdi:chevron-down'}
                  width="24"
                  height="24"
                  aria-hidden="true"
                />
              </div>

              {expandedModule === module.id && (
                <div 
                  id={`module-content-${module.id}`}
                  className="module-content"
                  role="region"
                  aria-label={`Content for ${module.module_name}`}
                >
                  {Array.isArray(module.content) && module.content.map((item) => (
                    <div 
                      key={`${item.id}-${item.serial_no}`} 
                      className={`content-item ${selectedContent?.id === item.id ? 'active' : ''}`}
                      onClick={() => onContentClick(item)}
                      onKeyPress={(e) => handleKeyPress(e, () => onContentClick(item))}
                      role="button"
                      tabIndex={0}
                      aria-label={`${item.title} - ${item.type} ${item.duration ? `(${item.duration})` : ''}`}
                      aria-current={selectedContent?.id === item.id ? 'true' : 'false'}
                    >
                      <div className="content-left">
                        <div className="content-icon">
                          <Icon 
                            icon={getContentIcon(item.type)} 
                            width="20" 
                            height="20"
                            aria-hidden="true"
                          />
                        </div>
                        <div className="content-details">
                          <span className="content-title">{item.title}</span>
                          <span className="content-type">
                            {item.type} {item.duration ? `(${item.duration})` : ''}
                          </span>
                        </div>
                      </div>
                      <div className="content-status">
                        {item.type !== 'document' && (
                          <Icon 
                            icon={item.completed === 1 ? "mdi:check-circle" : "mdi:circle-outline"} 
                            className={item.completed === 1 ? "completed" : "pending"} 
                            width="24" 
                            height="24"
                            aria-label={item.completed === 1 ? "Completed" : "Not completed"}
                          />
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          );
        })}
      </div>
    </div>
  );
}

export default CourseModule;