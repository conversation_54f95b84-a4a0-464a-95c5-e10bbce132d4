import prospectLogo from '../assets/images/logo/propspectLogo.png';
import creatorFoundationLogo from '../assets/images/logo/creatorFoundationLogo.png';
import nxgenvartifyLogo from '../assets/images/logo/nxgenvartifyLogo.png';

export const getLogoByDomainAndAlt = () => {
  const currentDomain = process.env.REACT_APP_DOMAIN_URL?.replace(/['"]/g, '') || '';
  console.log('currentDomain--------------------', currentDomain)
  
  if (currentDomain === 'lms.tpi.sg') {
    return {
      logo: prospectLogo,
      alt: 'Prospect LMS Logo'
    };
  } else if (currentDomain === 'lms.creatorfoundation.in') {
    console.log('creatorFoundationLogo--------------------', creatorFoundationLogo)
    return {
      logo: creatorFoundationLogo,
      alt: 'Creator Foundation LMS Logo'
    };
  } else if (currentDomain === 'lms.nxgenvarsity.com') {
    console.log('nxgenvartifyLogo--------------------', nxgenvartifyLogo)
    return {
      logo: nxgenvartifyLogo,
      alt: 'Nxgen Varsity LMS Logo'
    };
  } else {
    // Default logo for other domains - using prospect logo
    console.log('prospectLogo--------------------', prospectLogo)
    return {
      logo: prospectLogo,
      alt: 'Prospect LMS Logo'
    };
  }
}; 