const { mysqlServerConnection } = require('./db');

// Database health check function
const checkDatabaseHealth = async () => {
  try {
    const connection = await mysqlServerConnection.getConnection();
    await connection.query('SELECT 1');
    connection.release();
    console.log('✅ Database connection is healthy');
    return true;
  } catch (error) {
    console.log('❌ Database connection failed:', error.message);
    return false;
  }
};

// Run health check every 30 seconds
const startHealthCheck = () => {
  setInterval(async () => {
    await checkDatabaseHealth();
  }, 30000); // 30 seconds
};

module.exports = { checkDatabaseHealth, startHealthCheck };
