import React, { useState, useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import { processPayUPayment } from '../../../services/userService';

function OrderDetailsWithPayu() {
  const location = useLocation();
  const navigate = useNavigate();
  const courseData = location.state;

  const [isCheckoutLoading, setIsCheckoutLoading] = useState(false);

  useEffect(() => {
    // Validate and log the received data
    console.log('OrderDetailsWithPayu - Received State:', location.state);
    
    if (!location.state || !location.state.course_id) {
      console.error('No course data or course ID found');
    }

    // Log all the course data
    console.log('Order Details PayU - Course Data:', {
      course_id: courseData?.course_id,
      course_name: courseData?.course_name,
      course_price: courseData?.course_price,
      banner_image: courseData?.banner_image,
      course_type: courseData?.course_type,
      course_desc: courseData?.course_desc,
      discountCode: courseData?.discountCode,
      discountValue: courseData?.discountValue,
      points: courseData?.points
    });
  }, [location.state, courseData]);

  

  const subtotal = Number(courseData?.course_price) || 0;
  const discount = Number(courseData?.discountValue) || 0;
  const total = Math.max(0, subtotal - discount);

  const handlePayUCheckout = async () => {
    if (total <= 0) {
      toast.error('Total must be greater than ₹0');
      return;
    }

    setIsCheckoutLoading(true);
    try {
      const response = await processPayUPayment({
        amount: total * 100, // Convert to paisa (smallest currency unit)
        currency: 'INR',
        origin: window.location.origin,
        courseName: courseData.course_name,
        points: courseData.points,
        course_id: courseData.course_id,
      });

      console.log('PayU Checkout Response-----------------:', response);

    } catch (error) {
      console.error('PayU Checkout Error:', error);
      toast.error('Failed to process payment.');
      setIsCheckoutLoading(false);
    }
  };

  if (!courseData || !courseData.course_id) {
    return (
      <div className="d-flex justify-content-center align-items-center" style={{ minHeight: '60vh' }}>
        <div className="text-center">
          <h3>No order data available</h3>
          <p>Please select a course to purchase</p>
          <button 
            className="btn btn-primary mt-3"
            onClick={() => navigate(-1)}
          >
            Go Back
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="container py-4">
      <div className="row">
        <div className="col-12">
          <div className="d-flex align-items-center mb-4">
            <button 
              className="btn btn-outline-secondary me-3"
              onClick={() => navigate(-1)}
            >
              <i className="fas fa-arrow-left"></i> Back
            </button>
            <h2 className="mb-0">Order Details - PayU Payment</h2>
          </div>
        </div>
      </div>

      <div className="card shadow-sm rounded-4">
        <div className="card-body">
          <div className="row g-4">
            <div className="col-md-4">
              <img
                src={courseData.banner_image}
                alt={courseData.course_name}
                className="img-fluid rounded border"
                style={{ height: '200px', width: '100%', objectFit: 'cover' }}
                onError={(e) => {
                  e.target.onerror = null;
                  e.target.src = '/placeholder-course-image.jpg'; // Add a fallback image
                }}
              />
              <div className="mt-3">
                <span className="badge bg-primary">{courseData.course_type}</span>
              </div>
            </div>
            
            <div className="col-md-8">
              <h4 className="fw-light mb-3">{courseData.course_name}</h4>
              <p className="text-muted mb-4">{courseData.course_desc}</p>

              <div className="payment-section">
                <div className="card bg-light">
                  <div className="card-body">
                    <h5 className="card-title mb-3">
                      <i className="fas fa-credit-card me-2"></i>
                      Payment Details
                    </h5>

                    <div className="price-breakdown">
                      <div className="d-flex justify-content-between mb-2">
                        <span>Course Price</span>
                        <span className="fw-medium">₹{subtotal.toFixed(2)}</span>
                      </div>

                      {discount > 0 && (
                        <div className="d-flex justify-content-between mb-2 text-success">
                          <span>
                            <i className="fas fa-tag me-1"></i>
                            Discount ({courseData.discountCode})
                          </span>
                          <span className="fw-medium">-₹{discount.toFixed(2)}</span>
                        </div>
                      )}

                      {courseData.points > 0 && (
                        <div className="d-flex justify-content-between mb-2 text-info">
                          <span>
                            <i className="fas fa-coins me-1"></i>
                            Points Used
                          </span>
                          <span className="fw-medium">{courseData.points} points</span>
                        </div>
                      )}

                      <hr className="my-3" />
                      
                      <div className="d-flex justify-content-between mb-3">
                        <span className="fs-5 fw-bold">Total Amount</span>
                        <span className="fs-4 fw-bold text-primary">₹{total.toFixed(2)}</span>
                      </div>

                      <div className="payment-method-info mb-3">
                        <div className="d-flex align-items-center">
                          <img 
                            src="https://www.payu.in/images/payu-logo.png" 
                            alt="PayU" 
                            style={{ height: '30px' }}
                            className="me-2"
                          />
                          <span className="text-muted">Secure payment powered by PayU</span>
                        </div>
                      </div>

                      <button
                        className="btn btn-success btn-lg w-100"
                        onClick={handlePayUCheckout}
                        disabled={isCheckoutLoading || total <= 0}
                      >
                        {isCheckoutLoading ? (
                          <>
                            <span className="spinner-border spinner-border-sm me-2"></span>
                            Processing Payment...
                          </>
                        ) : (
                          <>
                            <i className="fas fa-shield-alt me-2"></i>
                            Pay ₹{total.toFixed(2)} with PayU
                          </>
                        )}
                      </button>

                      <div className="mt-3 text-center">
                        <small className="text-muted">
                          <i className="fas fa-lock me-1"></i>
                          Your payment information is secure and encrypted
                        </small>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Payment Security Info */}
      <div className="row mt-4">
        <div className="col-12">
          <div className="card border-0 bg-light">
            <div className="card-body">
              <div className="row text-center">
                <div className="col-md-4">
                  <i className="fas fa-shield-alt text-success fs-3 mb-2"></i>
                  <h6>Secure Payment</h6>
                  <small className="text-muted">256-bit SSL encryption</small>
                </div>
                <div className="col-md-4">
                  <i className="fas fa-credit-card text-primary fs-3 mb-2"></i>
                  <h6>Multiple Payment Options</h6>
                  <small className="text-muted">Cards, Net Banking, UPI</small>
                </div>
                <div className="col-md-4">
                  <i className="fas fa-headset text-info fs-3 mb-2"></i>
                  <h6>24/7 Support</h6>
                  <small className="text-muted">Customer support available</small>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default OrderDetailsWithPayu;
