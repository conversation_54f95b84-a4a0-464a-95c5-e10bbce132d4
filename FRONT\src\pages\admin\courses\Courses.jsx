import React, { useState, Suspense, lazy } from 'react';
import { Icon } from '@iconify/react';
import { usePermissions } from '../../../context/PermissionsContext';

// Lazy load components
const AllCourses = lazy(() => import('./AllCourses'));
const Approved = lazy(() => import('./Approved'));
const Pending = lazy(() => import('./Pending'));
const WaitingForApproval = lazy(() => import('./WaitingForApproval'));
const Rejected = lazy(() => import('./Rejected'));


function Courses() {
  const [activeTab, setActiveTab] = useState('all');
  const { permissions } = usePermissions();

  console.log("permissions#######################:", permissions);

  // Loading fallback component
  const LoadingFallback = () => (
    <div className="text-center p-4">
      <div className="spinner-border text-primary" role="status">
        <span className="visually-hidden">Loading...</span>
      </div>
    </div>
  );

  return (
   <>
    <div className="row">
      <div className="col-12 p-2">
        <div className="card border-0">
          {/* Tabs Navigation */}
          <div className="tab-header border-bottom mb-0">
            <ul className="nav nav-tabs">
              <li className="nav-item">
                <button
                  className={`nav-link d-flex align-items-center ${activeTab === 'all' ? 'active' : ''}`}
                  onClick={() => setActiveTab('all')}
                >
                  <Icon 
                    icon="fluent:library-24-regular" 
                    width="20" 
                    height="20" 
                    className="me-2"
                  />
                  All Courses
                </button>
              </li>
              <li className="nav-item">
                <button
                  className={`nav-link d-flex align-items-center ${activeTab === 'approved' ? 'active' : ''}`}
                  onClick={() => setActiveTab('approved')}
                >
                  <Icon 
                    icon="fluent:checkmark-circle-24-regular" 
                    width="20" 
                    height="20" 
                    className="me-2"
                  />
                  Approved
                </button>
              </li>
              <li className="nav-item">
                <button
                  className={`nav-link d-flex align-items-center ${activeTab === 'pending' ? 'active' : ''}`}
                  onClick={() => setActiveTab('pending')}
                >
                  <Icon 
                    icon="fluent:clock-24-regular" 
                    width="20" 
                    height="20" 
                    className="me-2"
                  /> 
                  Pending
                </button>
              </li>
              <li className="nav-item">
                <button
                  className={`nav-link d-flex align-items-center ${activeTab === 'waiting' ? 'active' : ''}`}
                  onClick={() => setActiveTab('waiting')}
                >
                  <Icon 
                    icon="fluent:timer-24-regular" 
                    width="20" 
                    height="20" 
                    className="me-2"
                  />
                  Waiting for Approval
                </button>
              </li>
              <li className="nav-item">
                <button
                  className={`nav-link d-flex align-items-center ${activeTab === 'rejected' ? 'active' : ''}`}
                  onClick={() => setActiveTab('rejected')}
                >
                  <Icon 
                    icon="fluent:dismiss-circle-24-regular" 
                    width="20" 
                    height="20" 
                    className="me-2"
                  />
                  Rejected
                </button>
              </li>
            </ul>
          </div>

          {/* Tab Content */}
          <div className="tab-content">
            <Suspense fallback={<LoadingFallback />}>
              {activeTab === 'all' && (
                <div className="tab-pane p-0 active border-0">
                  <div className="card-body p-0 border-0">
                    <AllCourses />
                  </div>
                </div>
              )}

              {activeTab === 'approved' && (
                <div className="tab-pane p-0 active">
                  <div className="card-body p-0">
                    <Approved />
                  </div>
                </div>
              )}

              {activeTab === 'pending' && (
                <div className="tab-pane p-0 active">
                  <div className="card-body p-0">
                    <Pending />
                  </div>
                </div>
              )}

              {activeTab === 'waiting' && (
                <div className="tab-pane p-0 active">
                  <div className="card-body p-0">
                    <WaitingForApproval />
                  </div>
                </div>
              )}

              {activeTab === 'rejected' && (
                <div className="tab-pane p-0 active">
                  <div className="card-body p-0">
                    <Rejected />
                  </div>
                </div>
              )}
            </Suspense>
          </div>
        </div>
      </div>
    </div>
   </>
  );
}

export default Courses;
