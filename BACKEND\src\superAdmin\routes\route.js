const router = require('express').Router(); 
const jwt = require('jsonwebtoken');

const { createUser, createRoles, createPermission, loginUser, showPermissions, showRoles, editRoles, deleteRole, addOrganization, SendNotificaiton, createBulkPermissions, getOrganizations, updateOrganization, activateOrganization, deactivateOrganization, deleteOrganization, getOrganizationGrowth, getOrganizationRequests, updateRequestStatus, getSuperAdminProfile, updateSuperAdminProfile, updateSuperAdminPasswordAPI } = require('../controller/accounts/Users');
const { registerValidation, loginValidation, authenticateJWT, authorizePermissions } = require('../../middleware/Validator');
const { upload } = require('../../tools/tools');
const { getSuperAdminNotifications, markSuperAdminNotificationAsRead, markAllSuperAdminNotificationsAsRead } = require('../controller/notifications/notification');
const { getSuperAdminNotificationSettings, updateSuperAdminNotificationSettings } = require('../controller/notification_settings/notification_settings');

// router.post('/create_organization', create_organization.create_organization);
router.post('/create_user', createUser);

router.post('/create_roles', createRoles);
router.get('/showroles', showRoles);
router.put('/editroles', editRoles);
router.post('/deleteroles', deleteRole);

router.post('/create_permission', createPermission);
router.get("/showpermission",showPermissions);
router.post('/login', loginValidation(),loginUser);

// get Profile API Route
router.get('/getsuperadminprofile', authenticateJWT, getSuperAdminProfile);
router.post('/updatesuperadminprofile', authenticateJWT,upload.fields([{ name: 'profile_pic' }]), updateSuperAdminProfile);

// Settings Update Password API
router.put('/updatesuperadminpassword', authenticateJWT, updateSuperAdminPasswordAPI);

// Super Admin Settings Notification
router.get("/get_superadmin_notification_settings", authenticateJWT, getSuperAdminNotificationSettings);
router.put("/update_superadmin_notification_settings", authenticateJWT, updateSuperAdminNotificationSettings);


// Notifications
router.get('/getsuperadminnotifications', authenticateJWT, getSuperAdminNotifications);
router.put('/mark_superadmin_notification_as_read', authenticateJWT, markSuperAdminNotificationAsRead);
router.put('/mark_superadmin_all_notifications_as_read', authenticateJWT, markAllSuperAdminNotificationsAsRead);


// router.post('/create_organization',authenticateJWT,//authorizePermissions("can_create_organization"), addOrganization);
// router.post('/create_organization',authenticateJWT, addOrganization);
// API Route
router.post('/create_organization', authenticateJWT, upload.single('org_image'), addOrganization);
router.get('/get_organizations', authenticateJWT, getOrganizations);
// router.put('/update_organization', authenticateJWT, updateOrganization);
// API Route for updating organization
router.put('/update_organization', authenticateJWT, upload.single('org_image'), updateOrganization);
router.put('/organization/activate/:org_id', authenticateJWT, activateOrganization);
router.put('/organization/deactivate/:org_id', authenticateJWT, deactivateOrganization);
router.put(`/delete_organization/:org_id`,authenticateJWT,deleteOrganization);
// Organizations Growth
router.get('/get_organization_growth',authenticateJWT, getOrganizationGrowth);
// Organization Total Requests
router.get('/get_organization_requests', authenticateJWT, getOrganizationRequests);
// Organization Status 
router.put('/update-status/:request_id', authenticateJWT, updateRequestStatus);

router.post('/test_send_notification',SendNotificaiton);
router.post("/create_bulk_permissions",createBulkPermissions);

module.exports = router;