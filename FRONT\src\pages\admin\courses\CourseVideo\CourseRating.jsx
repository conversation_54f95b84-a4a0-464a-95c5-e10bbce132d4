import React, { useState, useEffect } from 'react';
import { Icon } from '@iconify/react';
import './CourseRating.css';
import DefaultImage from '../../../../assets/images/profile/default-profile.png';
import { getCourseReviews } from '../../../../services/userService';
import { toast } from 'react-toastify';

function CourseRating({ contentData }) {
  const [ratings, setRatings] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  const videoId = contentData?.id;
  const courseId = contentData?.course_id;

  console.log('CourseRating - Initial Props:', {
    contentData,
    videoId,
    courseId,
    type: {
      videoId: typeof videoId,
      courseId: typeof courseId
    }
  });

  useEffect(() => {
    let isMounted = true;

    const fetchReviews = async () => {
      try {
        if (!videoId || !courseId) {
          console.log('Missing required IDs for fetch:', { videoId, courseId });
          setError('Missing required video or course ID');
          setIsLoading(false);
          return;
        }

        console.log('Fetching reviews with IDs:', {
          courseId,
          videoId,
          courseIdType: typeof courseId,
          videoIdType: typeof videoId
        });

        const response = await getCourseReviews(courseId, videoId);
        console.log('API Response:', response);

        if (!isMounted) return;

        if (response?.success) {
          setRatings(response.data || []);
          setError(null);
        } else {
          console.error('API returned unsuccessful response:', response);
          setError('Failed to fetch reviews');
          setRatings([]);
        }
      } catch (error) {
        console.error('Error in fetchReviews:', error);
        if (isMounted) {
          setError(error.message || 'Failed to load reviews');
          setRatings([]);
        }
      } finally {
        if (isMounted) {
          setIsLoading(false);
        }
      }
    };

    setIsLoading(true);
    fetchReviews();

    return () => {
      isMounted = false;
    };
  }, [videoId, courseId]);

  const renderStars = (rating) => {
    return Array.from({ length: 5 }, (_, index) => (
      <span
        key={index}
        className={`star ${index < rating ? 'active' : ''}`}
      >
        <Icon icon="mdi:star" width="24" height="24" />
      </span>
    ));
  };

  if (isLoading) {
    return (
      <div className="text-center p-4">
        <div className="spinner-border text-primary" role="status">
          <span className="visually-hidden">Loading reviews...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="alert alert-danger m-3" role="alert">
        {error}
      </div>
    );
  }

  if (!videoId || !courseId) {
    return (
      <div className="alert alert-warning m-3" role="alert">
        Missing required video or course information
      </div>
    );
  }

  return (
    <div className="course-rating-container p-0">
      <div className="ratings-list">
        {Array.isArray(ratings) && ratings.length > 0 ? (
          ratings.map(rating => {
            console.log('Rendering rating item:', rating);
            return (
              <div key={rating.review_id || `rating-${Math.random()}`} className="rating-item">
                <div className="rating-item-header">
                  <div className="rating-author">
                    <img 
                      src={rating.user?.profile_picture || DefaultImage} 
                      alt="avatar" 
                      className="author-avatar" 
                    />
                    <span className="author-name">{rating.user?.name || 'Anonymous'}</span>
                  </div>
                  <span className="rating-date">
                    {rating.review_createdAt ? new Date(rating.review_createdAt).toLocaleDateString() : 'No date'}
                  </span>
                </div>
                <div className="rating-stars-display">
                  {renderStars(rating.class_rating || 0)}
                </div>
                {rating.review_text && <p className="rating-text">{rating.review_text}</p>}
              </div>
            );
          })
        ) : (
          <div className="empty-ratings">
            <p>No ratings available for this content.</p>
          </div>
        )}
      </div>
    </div>
  );
}

export default CourseRating;