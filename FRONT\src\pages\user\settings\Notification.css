.notification-settings-container {
  padding: 0;
}

.section-header {
  margin-bottom: 24px;
}

.section-header h3 {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.section-header p {
  font-size: 14px;
  color: #666;
  margin: 0;
}

.notification-channels,
.notification-types {
}

.notification-channels h4,
.notification-types h4 {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.notification-toggle-group {
  margin-top: 16px;
}

.notification-toggle-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
  border-bottom: 1px solid #eee;
}

.notification-toggle-item:last-child {
  border-bottom: none;
}

.toggle-info {
  display: flex;
  align-items: flex-start;
  flex: 1;
}

.toggle-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background-color: rgba(49, 82, 232, 0.1);
  border-radius: 8px;
  margin-right: 16px;
  color: #3152e8;
}

.toggle-details h5 {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.toggle-details p {
  font-size: 14px;
  color: #666;
  margin: 0;
}

/* Custom toggle switch */
.custom-switch {
  position: relative;
  width: 50px;
  height: 26px;
  background-color: #e0e0e0;
  border-radius: 13px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.custom-switch.active {
  background-color: #3152e8;
}

.switch-slider {
  position: absolute;
  top: 3px;
  left: 3px;
  width: 20px;
  height: 20px;
  background-color: white;
  border-radius: 50%;
  transition: transform 0.3s;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.custom-switch.active .switch-slider {
  transform: translateX(24px);
}

.form-actions {
  margin-top: 24px;
  display: flex;
  justify-content: flex-end;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .notification-channels,
  .notification-types {
    padding: 16px;
  }
  
  .toggle-icon {
    width: 32px;
    height: 32px;
    margin-right: 12px;
  }
  
  .toggle-details h5 {
    font-size: 15px;
  }
  
  .toggle-details p {
    font-size: 13px;
  }
}
