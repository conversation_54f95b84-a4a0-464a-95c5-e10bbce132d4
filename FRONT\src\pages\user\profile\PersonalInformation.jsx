import React, { useState, useRef, useEffect } from 'react';
import { Mo<PERSON>, Button, Form, Row, Col } from 'react-bootstrap';
import DefaultProfile from '../../../assets/images/profile/default-profile.png';
import { Icon } from '@iconify/react';
import countryData from '../../../utils/countory.json';
import { validateName, validatePhone, validateDate, validateGender, validateState, validateProfession, validateQualification, validateBio, validateForm } from '../../../utils/validation';
import { getProfile, editProfile, updateProfilePic } from '../../../services/userService';
import { toast } from 'react-toastify';

function PersonalInformation() {

    // Initialize all state
    const [profileData, setProfileData] = useState(null);
    const [formData, setFormData] = useState({
        fullName: '',
        email: '',
        phoneNumber: '',
        dateOfBirth: '',
        dateJoined: '',
        gender: '',
        country: '',
        state: '',
        zipCode: '',
        profession: '',
        academicQualification: '',
        address: '',
        bio: ''
    });

    const [editFormData, setEditFormData] = useState({
        fullName: '',
        email: '',
        phoneNumber: '',
        dateOfBirth: '',
        dateJoined: '',
        gender: '',
        country: '',
        state: '',
        zipCode: '',
        profession: '',
        academicQualification: '',
        address: '',
        bio: ''
    });

    // UI state
    const [showEditModal, setShowEditModal] = useState(false);
    const [showCountryDropdown, setShowCountryDropdown] = useState(false);
    const [showCountryFieldDropdown, setShowCountryFieldDropdown] = useState(false);
    const [showImageModal, setShowImageModal] = useState(false);

    // Image handling state
    const [profileImage, setProfileImage] = useState(null);
    const [previewImage, setPreviewImage] = useState(null);
    const [selectedImage, setSelectedImage] = useState(null);

    // Form state
    const [errors, setErrors] = useState({});
    const [isLoading, setIsLoading] = useState(false);
    const [selectedCountryCode, setSelectedCountryCode] = useState('+374');
    const [searchQuery, setSearchQuery] = useState('');
    const [countrySearchQuery, setCountrySearchQuery] = useState('');

    // Refs
    const dropdownRef = useRef(null);
    const countryDropdownRef = useRef(null);

    useEffect(() => {
        fetchProfile();
    }, []);

    const fetchProfile = async () => {
        try {
            const response = await getProfile();
            console.log('Profile response:', response);
            if (response.success && response.data) {
                const profile = response.data;

                // Update both formData and editFormData with the API response
                const updatedData = {
                    fullName: profile.name || '',
                    email: profile.email || '',
                    phoneNumber: profile.mobile || '',
                    dateOfBirth: profile.date_of_birth?.split('T')[0] || '',
                    dateJoined: profile.date_joined?.split('T')[0] || '',
                    gender: profile.gender?.charAt(0).toUpperCase() + profile.gender?.slice(1) || '',
                    country: profile.country || '',
                    state: profile.state || '',
                    zipCode: profile.zipcode || '',
                    profession: profile.profession || '',
                    academicQualification: profile.qualification || '',
                    address: profile.address || '',
                    bio: profile.bio || ''
                };

                setFormData(updatedData);
                setEditFormData(updatedData);
                setSelectedCountryCode(profile.mobileno_code || '+374');

                // Set profile image if available
                if (profile.profile_pic_url) {
                    setProfileImage(profile.profile_pic_url);
                    setSelectedImage(profile.profile_pic_url);
                    setPreviewImage(profile.profile_pic_url);
                }
            }
        } catch (error) {
            console.error('Error fetching profile:', error);
            toast.error('Failed to fetch profile data');
        }
    };

    // Filter countries based on search query
    const filteredCountries = countryData.countoryCode.filter(country =>
        country.country.toLowerCase().includes(searchQuery.toLowerCase()) ||
        country.code.toLowerCase().includes(searchQuery.toLowerCase())
    );

    const filteredCountriesForField = countryData.countoryCode.filter(country =>
        country.country.toLowerCase().includes(countrySearchQuery.toLowerCase())
    );

    // Handle country selection
    const handleCountrySelect = (country) => {
        setSelectedCountryCode(country.code);
        setEditFormData(prevData => ({
            ...prevData,
            country: country.country.split(' ')[0]
        }));
        setShowCountryDropdown(false);
        setSearchQuery('');
    };

    // Handle Click Outside for Country Dropdowns
    useEffect(() => {
        const handleClickOutside = (event) => {
            if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
                setShowCountryDropdown(false);
            }
            if (countryDropdownRef.current && !countryDropdownRef.current.contains(event.target)) {
                setShowCountryFieldDropdown(false);
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, []);

    // Format date for API (keep as YYYY-MM-DD for MySQL)
    const formatDateForAPI = (date) => {
        if (!date) return '';
        return date; // Already in YYYY-MM-DD format from the input
    };

    // Validation rules
    const validationRules = {
        fullName: validateName,
        phoneNumber: validatePhone,
        dateOfBirth: (value) => {
            const dateErrors = [];
            if (!value) {
                dateErrors.push('Date of birth is required');
            } else {
                // Convert YYYY-MM-DD to DD-MM-YYYY for validation
                const [year, month, day] = value.split('-');
                const formattedDate = `${day}-${month}-${year}`;

                // Check if date is valid
                const isValidDate = /^(0[1-9]|[12][0-9]|3[01])-(0[1-9]|1[012])-\d{4}$/.test(formattedDate);
                if (!isValidDate) {
                    dateErrors.push('Date must be in DD-MM-YYYY format (e.g., 02-05-2025)');
                }

                // Check age range
                const birthDate = new Date(year, month - 1, day);
                const today = new Date();
                const age = today.getFullYear() - birthDate.getFullYear();

                if (age < 10) {
                    dateErrors.push('You must be at least 10 years old');
                }
                if (age > 100) {
                    dateErrors.push('Age cannot be more than 100 years');
                }
            }
            return { errors: dateErrors };
        },
        gender: validateGender,
        state: validateState,
        profession: validateProfession,
        academicQualification: validateQualification,
        bio: validateBio
    };

    // Handle opening edit modal
    const handleEditClick = () => {
        setEditFormData({ ...formData });
        // Ensure profile image persists when opening edit modal
        setSelectedImage(profileImage);
        setPreviewImage(profileImage);
        setShowEditModal(true);
    };

    // Handle closing the edit modal
    const handleCloseModal = () => {
        setShowEditModal(false);
        setSearchQuery('');
        setCountrySearchQuery('');
        setShowCountryDropdown(false);
        setShowCountryFieldDropdown(false);
        // Reset form data to original state
        setEditFormData({ ...formData });
        setErrors({});
    };

    // Handle form field changes in the edit modal
    const handleEditChange = (e) => {
        const { id, value } = e.target;
        setEditFormData(prevData => ({
            ...prevData,
            [id]: value
        }));
        // Clear error for the changed field
        if (errors[id]) {
            setErrors(prev => ({
                ...prev,
                [id]: undefined
            }));
        }
    };

    // Handle profile image change
    const handleImageChange = (e) => {
        const file = e.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onloadend = () => {
                setSelectedImage(reader.result);
                setPreviewImage(reader.result);
            };
            reader.readAsDataURL(file);
        }
    };

    const handleChange = (e) => {
        const { id, value } = e.target;
        setFormData(prevData => ({
            ...prevData,
            [id]: value
        }));
    };

    const handleSubmit = (e) => {
        e.preventDefault();
        console.log('Form submitted:', formData);
        // Here you would typically send the data to your backend
    };

    // Handle image selection
    const handleImageSelect = (e) => {
        const file = e.target.files[0];
        if (file) {
            // Check if file is an image
            if (!file.type.startsWith('image/')) {
                toast.error('Please select an image file');
                return;
            }

            // Check file size (limit to 5MB)
            if (file.size > 5 * 1024 * 1024) {
                toast.error('Image size should be less than 5MB');
                return;
            }

            setSelectedImage(file);
            const reader = new FileReader();
            reader.onloadend = () => {
                setPreviewImage(reader.result);
            };
            reader.readAsDataURL(file);
        }
    };

    // Handle image upload
    const handleImageUpload = async () => {
        if (!selectedImage) return;

        try {

            console.log('Selected image:', selectedImage);
            const formData = new FormData();
            formData.append('profileImage', selectedImage);

            // Call the API to upload image
            const response = await updateProfilePic(formData);

            if (response.success) {
                toast.success('Profile image updated successfully!');
                // Refresh profile data to get new image
                await fetchProfile();
                handleImageModalClose();
            } else {
                toast.error(response.message || 'Failed to update profile image');
            }
        } catch (error) {
            console.error('Error uploading image:', error);
            toast.error('Failed to upload image. Please try again.');
        }
    };

    // Handle opening image modal
    const handleImageModalOpen = () => {
        setShowImageModal(true);
        // Don't reset the image states here to maintain current image
        setPreviewImage(profileImage); // Show current profile image in modal
    };

    // Handle closing image modal
    const handleImageModalClose = () => {
        setShowImageModal(false);
        // Don't reset selected image if upload was not completed
        if (!selectedImage) {
            setPreviewImage(null);
        }
    };

    // Handle saving the edited profile data
    const handleSaveChanges = async (e) => {
        e.preventDefault();

        const validation = validateForm(editFormData, validationRules);
        if (!validation.isValid) {
            setErrors(validation.errors);
            return;
        }
        console.log('Validation:', validation);

        setIsLoading(true);

        try {
            // Prepare the data for API with the exact structure required
            const updateData = {
                profile_pic_url: profileImage || '',
                is_profile_pic_changed: false,
                name: editFormData.fullName || '',
                mobileno_code: selectedCountryCode,
                mobile: editFormData.phoneNumber || '',
                bio: editFormData.bio || '',
                date_of_birth: formatDateForAPI(editFormData.dateOfBirth) || '',
                gender: (editFormData.gender || '').toLowerCase(),
                country: editFormData.country || '',
                state: editFormData.state || '',
                zipcode: editFormData.zipCode || '',
                address: editFormData.address || '',
                language: 'en',
                profession: editFormData.profession || '',
                qualification: editFormData.academicQualification || '',
                experience: ''
            };

            console.log('Data being sent to API:', updateData);

            // Call the update API
            const response = await editProfile(updateData);
            console.log('API Response:', response);

            if (response.success) {
                // Update the local state with new data
                setFormData({ ...editFormData });
                toast.success('Profile updated successfully!');
                setShowEditModal(false);

                // Refresh profile data
                await fetchProfile();
            } else {
                toast.error(response.message || 'Failed to update profile');
            }
        } catch (error) {
            console.error('Error updating profile:', error);
            toast.error('Failed to update profile. Please try again.');
        } finally {
            setIsLoading(false);
            setErrors({});
        }
    };

    // Handle country selection for both dropdowns
    const handleCountryCodeSelect = (country) => {
        setSelectedCountryCode(country.code);
        setShowCountryDropdown(false);
        setSearchQuery('');
    };

    const handleCountryFieldSelect = (country) => {
        // Remove country code from country name (e.g., "India (+91)" -> "India")
        const countryName = country.country.split(' (')[0];

        setEditFormData(prevData => ({
            ...prevData,
            country: countryName
        }));
        setShowCountryFieldDropdown(false);
        setCountrySearchQuery('');
    };

    return (
        <div className="profile-container">
            <div className="row">
                {/* Left Column - My Profile */}
                <div className="col-md-5 mb-4">
                    <div className="profile-card">
                        <div className="profile-card-header">
                            <h5>My Profile</h5>
                        </div>
                        <div className="profile-card-body">
                            <div className="text-center mb-4">
                                <div className="profile-image-container">
                                    <img
                                        src={profileImage || DefaultProfile}
                                        alt="Profile"
                                        className="rounded-circle profile-image"
                                        style={{ width: '150px', height: '150px', objectFit: 'cover' }}
                                    />
                                </div>
                                <div className="mt-3">
                                    <button
                                        type="button"
                                        className="btn primary-btn d-flex align-items-center justify-content-center gap-2 mx-auto w-50"
                                        onClick={handleImageModalOpen}
                                    >
                                        <Icon icon="fluent:cloud-arrow-up-24-regular" />
                                        Upload <span className="d-none d-md-inline">Image</span>
                                    </button>
                                </div>
                            </div>

                            <div className="profile-field-group mb-2">
                                <label className="profile-label">Full Name</label>
                                <div className="profile-field">
                                    <input
                                        type="text"
                                        className="form-control"
                                        id="fullName"
                                        value={formData.fullName}
                                        readOnly
                                    />
                                </div>
                            </div>

                            <div className="profile-field-group mb-2">
                                <label className="profile-label">Email</label>
                                <div className="profile-field">
                                    <input
                                        type="text"
                                        className="form-control"
                                        id="email"
                                        value={formData.email}
                                        readOnly
                                    />
                                    <div className="text-muted small">Email cannot be edited</div>
                                </div>
                            </div>

                            <div className="profile-field-group mb-2">
                                <label className="profile-label">Phone Number</label>
                                <div className="profile-field">
                                    <input
                                        type="text"
                                        className="form-control"
                                        id="phoneNumber"
                                        value={`${selectedCountryCode} ${formData.phoneNumber}`}
                                        readOnly
                                    />
                                    <div className="text-muted small">Number cannot be edited</div>
                                </div>
                            </div>

                            {/* <div className="profile-field-group mb-2">
                                <label className="profile-label">Joined Date</label>
                                <div className="profile-field">
                                    <input
                                        type="text"
                                        className="form-control"
                                        id="subscriptionDate"
                                        value={formData.dateJoined}
                                        readOnly
                                    />
                                </div>
                                <div className="text-muted small">Date of Joined cannot be edited</div>
                            </div> */}
                        </div>
                    </div>
                </div>

                {/* Right Column - More Information */}
                <div className="col-md-7">
                    <div className="profile-card">
                        <div className="profile-card-header d-flex justify-content-between align-items-center">
                            <h5>More Information</h5>
                            <button
                                className="btn primary-btn w-25 d-flex align-items-center justify-content-center gap-2"
                                onClick={handleEditClick}
                            >
                                Edit
                                <Icon icon="fluent:edit-24-regular" />
                            </button>
                        </div>
                        <div className="profile-card-body">
                            <div className="row">
                                {/* Gender */}
                                <div className="col-md-6 mb-3">
                                    <div className="profile-field-group mb-2">
                                        <label className="profile-label">Gender</label>
                                        <div className="profile-field">
                                            <input
                                                type="text"
                                                className="form-control"
                                                id="gender"
                                                value={formData.gender}
                                                readOnly
                                            />
                                        </div>
                                    </div>
                                </div>

                                {/* Date of birth */}
                                <div className="col-md-6 mb-3">
                                    <div className="profile-field-group mb-2">
                                        <label className="profile-label">Date of Birth</label>
                                        <div className="profile-field">
                                            <input
                                                type="text"
                                                className="form-control"
                                                id="dateOfBirth"
                                                value={formData.dateOfBirth}
                                                readOnly
                                            />
                                        </div>
                                    </div>
                                </div>

                                <div className="col-md-6 mb-3">
                                    <div className="profile-field-group mb-2">
                                        <label className="profile-label">Country</label>
                                        <div className="profile-field">
                                            <input
                                                type="text"
                                                className="form-control"
                                                id="country"
                                                value={formData.country}
                                                readOnly
                                            />
                                        </div>
                                    </div>
                                </div>

                                <div className="col-md-6 mb-3">
                                    <div className="profile-field-group mb-2">
                                        <label className="profile-label">State</label>
                                        <div className="profile-field">
                                            <input
                                                type="text"
                                                className="form-control"
                                                id="state"
                                                value={formData.state}
                                                readOnly
                                            />
                                        </div>
                                    </div>
                                </div>

                                <div className="col-md-6 mb-3">
                                    <div className="profile-field-group mb-2">
                                        <label className="profile-label">Zip Code</label>
                                        <div className="profile-field">
                                            <input
                                                type="text"
                                                className="form-control"
                                                id="zipCode"
                                                value={formData.zipCode}
                                                readOnly
                                            />
                                        </div>
                                    </div>
                                </div>

                                <div className="col-md-6 mb-3">
                                    <div className="profile-field-group">
                                        <label className="profile-label">Profession</label>
                                        <div className="profile-field">
                                            <input
                                                type="text"
                                                className="form-control"
                                                id="profession"
                                                value={formData.profession}
                                                readOnly
                                            />
                                        </div>
                                    </div>
                                </div>

                                <div className="col-md-6 mb-3">
                                    <div className="profile-field-group mb-2">
                                        <label className="profile-label">Academic Qualification</label>
                                        <div className="profile-field">
                                            <input
                                                type="text"
                                                className="form-control"
                                                id="academicQualification"
                                                value={formData.academicQualification}
                                                readOnly
                                            />
                                        </div>
                                    </div>
                                </div>

                                <div className="col-12 mb-3">
                                    <div className="profile-field-group mb-2">
                                        <label className="profile-label">Address</label>
                                        <div className="profile-field">
                                            <textarea
                                                className="form-control"
                                                id="address"
                                                value={formData.address}
                                                readOnly
                                                rows="3"
                                            ></textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {/* Image Upload Modal */}
            <Modal
                show={showImageModal}
                onHide={handleImageModalClose}
                centered
                className="image-upload-modal"
            >
                <Modal.Header closeButton>
                    <Modal.Title>Upload Profile Picture</Modal.Title>
                </Modal.Header>
                <Modal.Body>
                    <div className="text-center">
                        <div className="preview-container mb-4">
                            {previewImage ? (
                                <img
                                    src={previewImage}
                                    alt="Preview"
                                    className="preview-image rounded-circle"
                                    style={{ width: '200px', height: '200px', objectFit: 'cover' }}
                                />
                            ) : profileImage ? (
                                <img
                                    src={profileImage}
                                    alt="Current Profile"
                                    className="preview-image rounded-circle"
                                    style={{ width: '200px', height: '200px', objectFit: 'cover' }}
                                />
                            ) : (
                                <div
                                    className="upload-placeholder rounded-circle"
                                    style={{
                                        width: '200px',
                                        height: '200px',
                                        backgroundColor: '#f8f9fa',
                                        display: 'flex',
                                        flexDirection: 'column',
                                        alignItems: 'center',
                                        justifyContent: 'center'
                                    }}
                                >
                                    <Icon
                                        icon="bi:image"
                                        style={{ fontSize: '48px', color: '#6c757d' }}
                                    />
                                    <p className="mt-2 mb-0 text-muted">No image selected</p>
                                </div>
                            )}
                        </div>
                        <input
                            type="file"
                            accept="image/*"
                            onChange={handleImageSelect}
                            style={{ display: 'none' }}
                            id="imageInput"
                        />
                        <Button
                            variant="primary"
                            className="d-flex align-items-center justify-content-center gap-2 mx-auto"
                            style={{ width: 'fit-content' }}
                            onClick={() => document.getElementById('imageInput').click()}
                        >
                            <Icon icon="fluent:cloud-arrow-up-24-regular" />
                            Choose Image
                        </Button>
                    </div>
                </Modal.Body>
                <Modal.Footer>
                    <Button
                        variant="secondary"
                        onClick={handleImageModalClose}
                    >
                        Cancel
                    </Button>
                    <Button
                        variant="primary"
                        onClick={handleImageUpload}
                        disabled={!selectedImage}
                        className="d-flex align-items-center gap-2"
                    >
                        {selectedImage ? (
                            <>
                                <Icon icon="fluent:cloud-arrow-up-24-regular" />
                                Upload Image
                            </>
                        ) : 'Select an Image'}
                    </Button>
                </Modal.Footer>
            </Modal>

            {/* Edit Profile Modal */}
            <Modal
                show={showEditModal}
                onHide={handleCloseModal}
                size="xl"
                centered
            >
                <Modal.Header closeButton>
                    <Modal.Title>Edit Profile</Modal.Title>
                </Modal.Header>
                <Modal.Body>
                    <Form onSubmit={handleSaveChanges}>
                        <Row>
                            {/* Full Name */}
                            <Col xs={12} md={12} className="mb-3">
                                <Form.Group>
                                    <Form.Label>Full Name <span className="text-danger">*</span></Form.Label>
                                    <Form.Control
                                        type="text"
                                        id="fullName"
                                        value={editFormData.fullName}
                                        onChange={handleEditChange}
                                        placeholder="Enter your full name"
                                        className={errors.fullName ? 'is-invalid' : ''}
                                    />
                                    {errors.fullName && <div className="invalid-feedback">{errors.fullName[0]}</div>}
                                </Form.Group>
                            </Col>

                            {/* Email - Read Only */}
                            <Col xs={12} md={12} className="mb-3">
                                <Form.Group>
                                    <Form.Label>Email  <span className="text-danger">*</span></Form.Label>
                                    <Form.Control
                                        type="email"
                                        value={editFormData.email}
                                        disabled
                                        readOnly
                                    />
                                    <small className="text-muted">Email cannot be edited</small>
                                </Form.Group>
                            </Col>

                            <Form.Group>
                                <Form.Label>Phone Number</Form.Label>
                                <div className="d-flex align-items-start flex-column flex-md-row gap-2">
                                    {/* Country Code Selector */}
                                    <div className="country-code-selector" ref={dropdownRef}>
                                        <button
                                            type="button"
                                            className="country-code-button"
                                            onClick={() => setShowCountryDropdown(!showCountryDropdown)}
                                        >
                                            {selectedCountryCode}
                                            <Icon icon="mdi:chevron-down" />
                                        </button>

                                        {showCountryDropdown && (
                                            <div className="country-dropdown">
                                                <div className="country-search">
                                                    <input
                                                        type="text"
                                                        placeholder="Search country..."
                                                        value={searchQuery}
                                                        onChange={(e) => setSearchQuery(e.target.value)}
                                                    />
                                                </div>
                                                <div className="country-list">
                                                    {filteredCountries.map((country, index) => (
                                                        <div
                                                            key={index}
                                                            className="country-item"
                                                            onClick={() => handleCountryCodeSelect(country)}
                                                        >
                                                            <span>{country.country}</span>
                                                            <span>{country.code}</span>
                                                        </div>
                                                    ))}
                                                </div>
                                            </div>
                                        )}
                                    </div>

                                    {/* Phone Input with Error */}
                                    <div className="flex-grow-1 position-relative w-100">
                                        <Form.Control
                                            type="tel"
                                            id="phoneNumber"
                                            value={editFormData.phoneNumber}
                                            onChange={handleEditChange}
                                            placeholder="Enter phone number"
                                            className={errors.phoneNumber ? 'is-invalid' : ''}
                                        />
                                        {errors.phoneNumber && (
                                            <div className="invalid-feedback d-block">
                                                Phone number must be 5–15 digits
                                            </div>
                                        )}
                                    </div>
                                </div>
                            </Form.Group>


                            {/* Date of Birth */}
                            <Col xs={12} md={12} className="mb-3">
                                <Form.Group>
                                    <Form.Label>Date of Birth  <span className="text-danger">*</span></Form.Label>
                                    <Form.Control
                                        type="date"
                                        id="dateOfBirth"
                                        value={editFormData.dateOfBirth}
                                        onChange={handleEditChange}
                                        className={errors.dateOfBirth ? 'is-invalid' : ''}
                                    />
                                    {errors.dateOfBirth && <div className="invalid-feedback">{errors.dateOfBirth[0]}</div>}
                                </Form.Group>
                            </Col>

                            {/* Gender */}
                            <Col xs={12} md={12} className="mb-3">
                                <Form.Group>
                                    <Form.Label>Gender  <span className="text-danger">*</span></Form.Label>
                                    <Form.Select
                                        id="gender"
                                        value={editFormData.gender}
                                        onChange={handleEditChange}
                                        className={errors.gender ? 'is-invalid' : ''}
                                    >
                                        <option value="">Select gender</option>
                                        <option value="Male">Male</option>
                                        <option value="Female">Female</option>
                                        <option value="Other">Other</option>
                                    </Form.Select>
                                    {errors.gender && <div className="invalid-feedback">{errors.gender[0]}</div>}
                                </Form.Group>
                            </Col>

                            {/* Country */}
                            <Col xs={12} md={12} className="mb-3">
                                <Form.Group>
                                    <Form.Label>Country  <span className="text-danger">*</span></Form.Label>
                                    <div className="country-select-container">
                                        <div className="position-relative" ref={countryDropdownRef}>
                                            <Form.Control
                                                type="text"
                                                value={editFormData.country}
                                                readOnly
                                                onClick={() => setShowCountryFieldDropdown(true)}
                                                placeholder="Select country"
                                            />
                                            {showCountryFieldDropdown && (
                                                <div className="country-dropdown">
                                                    <div className="country-search">
                                                        <Icon icon="mdi:magnify" className="auth-input-icon" />
                                                        <input
                                                            type="text"
                                                            placeholder="Search countries..."
                                                            value={countrySearchQuery}
                                                            onChange={(e) => setCountrySearchQuery(e.target.value)}
                                                            className="auth-input"
                                                        />
                                                    </div>
                                                    <div className="country-list">
                                                        {filteredCountriesForField.map((country, index) => (
                                                            <div
                                                                key={index}
                                                                className="country-item"
                                                                onClick={() => handleCountryFieldSelect(country)}
                                                            >
                                                                <span>{country.country}</span>
                                                            </div>
                                                        ))}
                                                    </div>
                                                </div>
                                            )}
                                        </div>
                                    </div>
                                </Form.Group>
                            </Col>

                            {/* State */}
                            <Col xs={12} md={12} className="mb-3">
                                <Form.Group>
                                    <Form.Label>State</Form.Label>
                                    <Form.Control
                                        type="text"
                                        id="state"
                                        value={editFormData.state}
                                        onChange={handleEditChange}
                                        placeholder="Enter your state"
                                        className={errors.state ? 'is-invalid' : ''}
                                    />
                                    {errors.state && <div className="invalid-feedback">{errors.state[0]}</div>}
                                </Form.Group>
                            </Col>

                            {/* Zip Code */}
                            <Col xs={12} md={12} className="mb-3">
                                <Form.Group>
                                    <Form.Label>Zip Code</Form.Label>
                                    <Form.Control
                                        type="text"
                                        id="zipCode"
                                        value={editFormData.zipCode}
                                        onChange={handleEditChange}
                                        placeholder="Enter zip code"
                                    />
                                </Form.Group>
                            </Col>

                            {/* Profession */}
                            <Col xs={12} md={12} className="mb-3">
                                <Form.Group>
                                    <Form.Label>Profession</Form.Label>
                                    <Form.Control
                                        type="text"
                                        id="profession"
                                        value={editFormData.profession}
                                        onChange={handleEditChange}
                                        placeholder="Enter your profession"
                                        className={errors.profession ? 'is-invalid' : ''}
                                    />
                                    {errors.profession && <div className="invalid-feedback">{errors.profession[0]}</div>}
                                </Form.Group>
                            </Col>

                            {/* Academic Qualification */}
                            <Col xs={12} md={12} className="mb-3">
                                <Form.Group>
                                    <Form.Label>Academic Qualification</Form.Label>
                                    <Form.Control
                                        type="text"
                                        id="academicQualification"
                                        value={editFormData.academicQualification}
                                        onChange={handleEditChange}
                                        placeholder="Enter your qualification"
                                        className={errors.academicQualification ? 'is-invalid' : ''}
                                    />
                                    {errors.academicQualification && <div className="invalid-feedback">{errors.academicQualification[0]}</div>}
                                </Form.Group>
                            </Col>

                            {/* Address */}
                            <Col xs={12} className="mb-3">
                                <Form.Group>
                                    <Form.Label>Address</Form.Label>
                                    <Form.Control
                                        as="textarea"
                                        rows={3}
                                        id="address"
                                        value={editFormData.address}
                                        onChange={handleEditChange}
                                        placeholder="Enter your address"
                                    />
                                </Form.Group>
                            </Col>

                            {/* Bio */}
                            <Col xs={12} className="mb-3" style={{ display: 'none' }}>
                                <Form.Group>
                                    <Form.Label>Bio</Form.Label>
                                    <Form.Control
                                        as="textarea"
                                        rows={3}
                                        id="bio"
                                        value={editFormData.bio}
                                        onChange={handleEditChange}
                                        placeholder="Tell us about yourself..."
                                        className={errors.bio ? 'is-invalid' : ''}
                                    />
                                    {errors.bio && <div className="invalid-feedback">{errors.bio[0]}</div>}
                                </Form.Group>
                            </Col>
                        </Row>
                    </Form>
                </Modal.Body>
                <Modal.Footer>
                    <Button className="btn secondary-btn" onClick={handleCloseModal} disabled={isLoading}>
                        Cancel
                    </Button>
                    <Button
                        className="btn primary-btn"
                        onClick={handleSaveChanges}
                        disabled={isLoading}
                    >
                        {isLoading ? (
                            <>
                                <div className="spinner"></div>
                                Saving...
                            </>
                        ) : (
                            'Save Changes'
                        )}
                    </Button>
                </Modal.Footer>
            </Modal>


        </div>
    );
}

export default PersonalInformation;