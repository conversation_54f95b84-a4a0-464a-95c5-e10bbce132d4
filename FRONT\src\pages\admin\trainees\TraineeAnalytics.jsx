import React, { useState, useEffect } from 'react'
import { useParams } from 'react-router-dom'
import { decodeData } from '../../../utils/encodeAndEncode'
import { Icon } from '@iconify/react';
import { getTraineeAnalytics } from '../../../services/traineeService';
import { toast } from 'react-toastify';
import Loader from '../../../components/common/Loader';
import TraineeAnalyticsCourseTab from './TraineeAnalyticsCourseTab';
import TraineeAnalyticsClassroomTab from './TraineeAnalyticsClassroomTab';
import TraineeAnalyticsTicketsTab from './TraineeAnalyticsTicketsTab';
import TraineeAnalyticsPaymentsTab from './TraineeAnalyticsPaymentsTab';
import TraineeAnalyticsCertificatesTab from './TraineeAnalyticsCertificatesTab';

function TraineeAnalytics() {
  const { traineeId } = useParams();
  const [activeTab, setActiveTab] = useState('courses');
  let decodedId;
  
  try {
    decodedId = decodeData(traineeId);
  } catch (error) {
    console.error('Error decoding trainee ID:', error);
    decodedId = 'Invalid ID';
  }

  const [loading, setLoading] = useState(false);
  const [traineeData, setTraineeData] = useState(null);
  const [dashboardStats, setDashboardStats] = useState({
    active_classrooms: 0,
    active_courses: 0,
    free_courses: 0,
    paid_courses: 0,
    completed_courses: 0,
    certificates: 0,
    pending_tickets: 0,
    resolved_tickets: 0
  });

  // Add state for tab data
  const [tabData, setTabData] = useState({
    courses: [],
    classrooms: [],
    tickets: [],
    payments: [],
    certificates: []
  });

  const fetchAnalyticsData = async () => {
    try {
      setLoading(true);
      const response = await getTraineeAnalytics({trainee_id: decodedId});
      console.log('Trainee Analytics Data:', response);

      if (response.success) {
        setTraineeData(response.data.trainee);
        setDashboardStats(response.data.dashboard);
        // Set tab data from response
        setTabData({
          courses: response.data.courses || [],
          classrooms: response.data.classrooms || [],
          tickets: response.data.tickets || [],
          payments: response.data.payments || [],
          certificates: response.data.certificates || []
        });
      } else {
        toast.error(response.message || 'Failed to fetch analytics data');
      }
    } catch (error) {
        console.error('Error fetching trainee analytics:', error);
        toast.error('Something went wrong while fetching analytics data');
    } finally {
        setLoading(false);
    }
  };

  useEffect(() => {
    if (decodedId) {
      fetchAnalyticsData();
    }
  }, [decodedId]);

  const tabs = [
    {
      id: 'courses',
      label: 'Courses',
      icon: 'material-symbols:book-outline'
    },
    {
      id: 'classroom',
      label: 'Classroom',
      icon: 'material-symbols:grid-view'
    },
    {
      id: 'tickets',
      label: 'Tickets',
      icon: 'material-symbols:confirmation-number-outline'
    },
    {
      id: 'payments',
      label: 'Payments',
      icon: 'material-symbols:payments-outline'
    },
    {
      id: 'certificates',
      label: 'Certificates',
      icon: 'material-symbols:verified-outline'
    }
  ];

  const getDashboardCards = () => {
    return [
      {
        id: 1,
        title: "Active Classrooms",
        value: dashboardStats.active_classrooms || 0,
        icon: "material-symbols:grid-view",
        color: "success"
      },
      {
        id: 2,
        title: "Active Courses",
        value: dashboardStats.active_courses || 0,
        icon: "material-symbols:book-outline",
        color: "success"
      },
      {
        id: 3,
        title: "Free Courses",
        value: dashboardStats.free_courses || 0,
        icon: "material-symbols:star-outline",
        color: "warning"
      },
      {
        id: 4,
        title: "Paid Courses",
        value: dashboardStats.paid_courses || 0,
        icon: "material-symbols:credit-card",
        color: "info"
      },
      {
        id: 5,
        title: "Completed Courses",
        value: dashboardStats.completed_courses || 0,
        icon: "material-symbols:check-circle-outline",
        color: "danger"
      },
      {
        id: 6,
        title: "Certificates",
        value: dashboardStats.certificates || 0,
        icon: "material-symbols:verified-outline",
        color: "success"
      },
      {
        id: 7,
        title: "Pending Tickets",
        value: dashboardStats.pending_tickets || 0,
        icon: "material-symbols:support-agent",
        color: "warning"
      },
      {
        id: 8,
        title: "Resolved Tickets",
        value: dashboardStats.resolved_tickets || 0,
        icon: "material-symbols:check-circle",
        color: "success"
      }
    ];
  };

  const dashboardCards = getDashboardCards();

  if (loading) {
    return (
      <div className="d-flex justify-content-center align-items-center" style={{ minHeight: '400px' }}>
        <Loader />
      </div>
    );
  }

  // Render tab content based on active tab
  const renderTabContent = () => {
    switch (activeTab) {
      case 'courses':
        return <TraineeAnalyticsCourseTab traineeId={decodedId} />;
      case 'classroom':
        return <TraineeAnalyticsClassroomTab traineeId={decodedId} />;
      case 'tickets':
        return <TraineeAnalyticsTicketsTab traineeId={decodedId} />;
      case 'payments':
        return <TraineeAnalyticsPaymentsTab traineeId={decodedId} />;
      case 'certificates':
        return <TraineeAnalyticsCertificatesTab traineeId={decodedId} />;
      default:
        return null;
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'Not Available';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <div className="container-fluid p-4">
      {/* Trainee Information Card */}
      {traineeData && (
        <div className="card mb-4">
          <div className="card-body">
            <div className="row">
              {/* Profile Picture Column */}
              <div className="col-md-3 text-center">
                <div className="position-relative mb-3">
                  {traineeData.profile_pic_url ? (
                    <img
                      src={traineeData.profile_pic_url}
                      alt={traineeData.name}
                      className="rounded-circle"
                      style={{
                        width: '150px',
                        height: '150px',
                        objectFit: 'cover',
                        border: '4px solid #fff',
                        boxShadow: '0 2px 10px rgba(0,0,0,0.1)'
                      }}
                    />
                  ) : (
                    <div
                      className="rounded-circle d-flex align-items-center justify-content-center bg-primary text-white"
                      style={{
                        width: '150px',
                        height: '150px',
                        fontSize: '48px',
                        margin: '0 auto',
                        border: '4px solid #fff',
                        boxShadow: '0 2px 10px rgba(0,0,0,0.1)'
                      }}
                    >
                      {traineeData.name.charAt(0).toUpperCase()}
                    </div>
                  )}
               
                </div>
                <h5 className="mb-1">{traineeData.name}</h5>
              </div>

              {/* Personal Information Column */}
              <div className="col-md-4">
                <h6 className="text-muted mb-3">Personal Information</h6>
                <div className="mb-2">
                  <div className="d-flex align-items-center mb-2">
                    <Icon icon="fluent:mail-24-regular" className="text-muted me-2" width="20" />
                    <span>{traineeData.email}</span>
                  </div>
                  <div className="d-flex align-items-center mb-2">
                    <Icon icon="fluent:phone-24-regular" className="text-muted me-2" width="20" />
                    <span>{traineeData.mobileno_code} {traineeData.mobile}</span>
                  </div>
                  <div className="d-flex align-items-center mb-2">
                    <Icon icon="fluent:calendar-24-regular" className="text-muted me-2" width="20" />
                    <span>Born {formatDate(traineeData.date_of_birth)}</span>
                  </div>
                  <div className="d-flex align-items-center mb-2">
                    <Icon icon="fluent:person-24-regular" className="text-muted me-2" width="20" />
                    <span className="text-capitalize">{traineeData.gender || 'Not Specified'}</span>
                  </div>
                </div>
              </div>

              {/* Professional Information Column */}
              <div className="col-md-5">
                <h6 className="text-muted mb-3">Professional Information</h6>
                <div className="mb-2">
                  <div className="d-flex align-items-center mb-2">
                    <Icon icon="fluent:book-24-regular" className="text-muted me-2" width="20" />
                    <span>Qualification: {traineeData.qualification || 'Not Specified'}</span>
                  </div>
                  <div className="d-flex align-items-center mb-2">
                    <Icon icon="fluent:briefcase-24-regular" className="text-muted me-2" width="20" />
                    <span>Profession: {traineeData.profession || 'Not Specified'}</span>
                  </div>
                  <div className="d-flex align-items-center mb-2">
                    <Icon icon="fluent:location-24-regular" className="text-muted me-2" width="20" />
                    <span>{traineeData.state}, {traineeData.country} - {traineeData.zipcode}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      <div className="row g-4">
        {dashboardCards.map((item) => (
          <div key={item.id} className="col-sm-6 col-md-4 col-lg-3">
            <div className={`card border-0 shadow-sm`}>
              <div className="card-body">
                <div className="d-flex align-items-center mb-2">
                  <div className={`bg-${item.color}-subtle p-2 rounded-3 me-2`}>
                    <Icon icon={item.icon} width="24" height="24" className={`text-${item.color}`} />
                  </div>
                  <span className="text-muted">{item.title}</span>
                </div>
                <h3 className="mb-0">{item.value}</h3>
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="card border-0 mt-4">
        {/* Tabs Navigation */}
        <div className="tab-header border-bottom">
          <ul className="nav nav-tabs px-3">
            {tabs.map(tab => (
              <li className="nav-item" key={tab.id}>
                <button
                  className={`nav-link ${activeTab === tab.id ? 'active' : ''} px-4`}
                  onClick={() => setActiveTab(tab.id)}
                >
                  <Icon icon={tab.icon} width="20" height="20" className="me-2" />
                  {tab.label}
                </button>
              </li>
            ))}
          </ul>
        </div>

        {/* Tab Content */}
        <div className="tab-content p-4">
          {renderTabContent()}
        </div>
      </div>
    </div>
  );
}

export default TraineeAnalytics;
