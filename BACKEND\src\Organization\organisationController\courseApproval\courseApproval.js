const { mysqlServerConnection } = require('../../../db/db');

const sendApprovalcourse = async (req, res, next) => {
    try {
      const { user_id, course_id } = req.body;
      const db_name = req.user.db_name;
      console.log(user_id, course_id, "++++++++++++++++++++++++");
  
      console.log("Request received:", { user_id, course_id, db_name });
  
      // Check if course exists
      const [courseRows] = await mysqlServerConnection.query(
        `SELECT send_request FROM ${db_name}.courses WHERE id = ?`,
        [course_id]
      );
  
      if (courseRows.length === 0) {
        console.log("Course not found for ID:", course_id);
        return res.status(404).json({
          success: false,
          message: "Course not found",
        });
      }
  
      const currentSendRequest = courseRows[0].send_request;
  
    //   if (currentSendRequest === 1) {
    //     // If already sent, toggle back to 0 (cancel)
    //     await mysqlServerConnection.query(
    //       `UPDATE ${db_name}.courses 
    //          SET send_request = 0,
    //          is_approved = 0,
    //          is_rejected = 0,
    //          updatedAt = NOW()
    //        WHERE id = ?`,
    //       [course_id]
    //     );
  
    //     console.log("Approval request canceled for course ID:", course_id);
    //     return res.status(200).json({
    //       success: true,
    //       message: "Approval request is canceled successfully",
    //     });
    //   }
  
      // Otherwise, send the approval request (set send_request = 1)
      await mysqlServerConnection.query(
        `UPDATE ${db_name}.courses 
         SET send_request = 1, 
             created_by = ?, 
             is_rejected = 0,
             rejected_note = NULL,
             updatedAt = NOW()
         WHERE id = ?`,
        [user_id, course_id]
      );
      
  
      console.log("Approval request sent for course ID:", course_id, "by user:", user_id);
      return res.status(200).json({
        success: true,
        message: "Approval request sent successfully",
      });
    } catch (error) {
      console.error("Error in sendApprovalcourse:", error);
      return next({
        statusCode: 500,
        message: "Internal server error",
      });
    }
  };
  
const cancelApprovalCourseRequest = async (req, res, next) => {
  try {
    const { course_id } = req.body;
    const db_name = req.user.db_name;

    console.log("🔁 Cancel Approval Request Initiated");
    console.log("➡️ DB:", db_name);
    console.log("➡️ Course ID:", course_id);

    // Step 1: Check if the course exists
    const [courseRows] = await mysqlServerConnection.query(
      `SELECT send_request, is_approved, is_rejected FROM ${db_name}.courses WHERE id = ?`,
      [course_id]
    );

    console.log("🗂️ Course Lookup Result:", courseRows);

    if (courseRows.length === 0) {
      console.warn("⚠️ Course not found for ID:", course_id);
      return res.status(404).json({
        success: false,
        message: "Course not found",
      });
    }

    const { send_request, is_approved, is_rejected } = courseRows[0];

    console.log(`📌 Current Status - send_request: ${send_request}, is_approved: ${is_approved}, is_rejected: ${is_rejected}`);

    // if (send_request == 0) {
    //   console.warn("⚠️ Approval request has not been sent yet. Nothing to cancel.");
    //   return res.status(400).json({
    //     success: false,
    //     message: "Approval request has not been sent yet",
    //   });
    // }

    // Step 2: Cancel the approval request
    const [updateResult] = await mysqlServerConnection.query(
      `UPDATE ${db_name}.courses
       SET send_request = 0,
           is_approved = 0,
           is_rejected = 0,
           rejected_note = NULL,
           updatedAt = NOW()
       WHERE id = ?`,
      [course_id]
    );

    console.log("✅ Update Result:", updateResult);

    console.log("🟢 Approval request successfully canceled for course ID:", course_id);

    return res.status(200).json({
      success: true,
      message: "Approval request canceled successfully",
    });
  } catch (error) {
    console.error("❌ Error in cancelApprovalCourseRequest:", error);
    return next({
      statusCode: 500,
      message: "Internal server error",
    });
  }
};




const approveCourse = async (req, res, next) => {
    const { course_id } = req.params;
    console.log("=== Starting Course Approval ==-=");
    console.log("Course ID:", course_id);
    console.log("User ID:", req.user?.userId);
    console.log("Database:", req.user?.db_name);

    try {
        if (!course_id) {
            console.error("Error: Course ID is missing");
            return next({ statusCode: 404, message: 'Course ID is missing' });
        }

        console.log("Updating course approval status...");
        const [updateResult] = await mysqlServerConnection.query(
            `UPDATE ${req.user.db_name}.courses SET is_approved = 1 WHERE id = ?`,
            [course_id]
        );

        console.log("Update result:", {
            affectedRows: updateResult.affectedRows,
            changedRows: updateResult.changedRows,
            message: updateResult.message
        });

        if (updateResult.affectedRows === 0) {
            console.error("Error: Course not found or already approved");
            return next({ statusCode: 404, message: 'Course not found or already approved' });
        }

        console.log("Recording approval in course_approval table...");
        const [insertResult] = await mysqlServerConnection.query(
            `INSERT INTO ${req.user.db_name}.course_approval (user_id, course_id, is_approval) VALUES (?, ?, ?)`,
            [req.user.userId, course_id, 1]
        );

        console.log("=== Course Approval Successful ===");
        console.log("Approval ID:", insertResult.insertId);
        console.log("Approved by User ID:", req.user.userId);
        console.log("Approval Time:", new Date().toISOString());

        res.status(201).json({
            success: true,
            message: 'Course approved successfully',
            approvalId: insertResult.insertId
        });

    } catch (error) {
        console.error("=== Course Approval Error ===");
        console.error("Error:", error.message);
        console.error("Stack:", error.stack);
        console.error("Request Body:", req.body);
        console.error("Request Params:", req.params);
        console.error("User Info:", {
            userId: req.user?.userId,
            userRole: req.user?.role
        });
        
        return next({ 
            statusCode: 500, 
            message: error.message || 'Internal Server Error' 
        });
    }
};



const getCoursesBasedOnType = async (req,res,next)=>{

    try {
        const {course_type} = req.body;
        if(!course_type){
            return next({statusCode: 404,message:'Please provide the type of course you are searching for'})
        }

        const [courses] = await mysqlServerConnection.query(`SELECT * from ${req.user.db_name}.courses where course_type = ?`,[course_type]);
        res.status(200).json({
            success: true,
            data: courses
        })
    } catch (error) {
        return next({statusCode:500,message: error.message ||'Internal Server Error'})
    }
}


const rejectCourse = async (req, res, next) => {
    const { course_id } = req.params;
    try {
        if (!course_id) {
            return next({ statusCode: 404, message: 'Course ID is missing' });
        }
        const [updateResult] = await mysqlServerConnection.query(
            `UPDATE ${req.user.db_name}.courses SET is_approved = 0 WHERE id = ?`,
            [course_id]
        );

        if (updateResult.affectedRows === 0) {
            return next({ statusCode: 404, message: 'Course not found or already rejected' });
        }

        const [existingApproval] = await mysqlServerConnection.query(
            `SELECT * FROM ${req.user.db_name}.course_approval WHERE course_id = ?`,
            [course_id]
        );
        if (existingApproval.length > 0) {
            await mysqlServerConnection.query(
                `UPDATE ${req.user.db_name}.course_approval SET is_approval = 0 WHERE course_id = ?`,
                [course_id]
            );
        } 
        else {
            await mysqlServerConnection.query(
                `INSERT INTO ${req.user.db_name}.course_approval (user_id, course_id, is_approval) VALUES (?, ?, ?)`,
                [req.user.userId, course_id, 0]
            );
        }
        res.status(201).json({
            success: true,
            message: 'Course rejected successfully',
        });

    } catch (error) {
        return next({ statusCode: 500, message: error.message || 'Internal Server Error' });
    }
};

const getAllPendingCourses = async (req, res, next) => {
    try {
        const page = parseInt(req.query.page) || 1;
        // Limit the page size to reasonable values (5, 10, 25, 50)
        let limit = parseInt(req.query.limit) || 10;
        limit = [5, 10, 25, 50].includes(limit) ? limit : 10;
        
        const search = req.query.search || null;
        const trainer = req.query.trainer || null;
        const course_category = req.query.course_category || null;

        console.log("trainer",trainer);
        console.log("course_category",course_category);

        const offset = (page - 1) * limit;
        let query = `SELECT * FROM ${req.user.db_name}.courses WHERE is_approved = false`;
        let countQuery = `SELECT COUNT(*) AS totalCount FROM ${req.user.db_name}.courses WHERE is_approved = 0`;

        // Add search filtering
        if (search){
            console.log("SEARCH QUERY IS THERE",search);
            query += ` AND course_name LIKE ?`;
            countQuery += ` AND course_name LIKE ?`;
        }

        // Add trainer filtering
        if (trainer !== 'null'){
            // console.log("TRAINER QUERY IS THERE",typeof(trainer));
            query += ` AND created_by = ?`;
            countQuery += ` AND created_by = ?`;
        }

        if(course_category != 'null'){
            // console.log("THIS IS COURSE CATEGORY",course_category);
            query += ` AND course_category = ?`;
            countQuery += ` AND course_category = ?`;
        }

        // Add pagination to the query
        query += ` LIMIT ? OFFSET ?`;

        // Prepare parameters for the queries
        const searchParam = [];
        const countSearchParam = [];

        if (search) {
            searchParam.push(`%${search}%`);
            countSearchParam.push(`%${search}%`);
        }
        if (trainer != 'null') {
            searchParam.push(trainer);
            countSearchParam.push(trainer);
        }
        if (course_category != 'null') {
            searchParam.push(course_category);
            countSearchParam.push(course_category);
        }
        
        // Add pagination parameters
        searchParam.push(limit, offset);

        // Execute the query with search, trainer, and pagination parameters
        // console.log(query);
        const [pendingCourses] = await mysqlServerConnection.query(query, searchParam);
        console.log(searchParam);
       

        // Execute the count query to get the total count for pagination metadata
        const [[{ totalCount }]] = await mysqlServerConnection.query(countQuery, countSearchParam);

        // Respond with the courses and pagination info
        res.status(200).json({
            success: true,
            data: {
                pendingCourses,
                pagination: {
                    page: page,
                    limit: limit,
                    totalPages: Math.ceil(totalCount / limit),
                    totalCount: totalCount
                }
            }
        });
    } catch (error) {
        console.log(error);
        return next({ statusCode: 500, message: error.message || 'Internal Server Error' });
    }
};


const getParticularUserDetails = async (req,res,next)=>{
    try {
        const {role_id} = req.params;
        const [data] = await mysqlServerConnection.query(`
        SELECT u.id,u.name from ${req.user.db_name}.users u 
        JOIN ${req.user.db_name}.user_roles ur on ur.user_id = u.id
        where ur.role_id = ${role_id};
    `);

    res.status(200).json({
        success:true,
        data: data,
    })
    } catch (error) {
        return next({statusCode:500,message: error.message || 'Internal Server Error'})
    }
}

// Get approved courses with pagination, search, and filtering
const getApprovedCourses = async (req, res, next) => {
    try {
        const page = parseInt(req.query.page) || 1;
        // Limit the page size to reasonable values (5, 10, 25, 50)
        let limit = parseInt(req.query.limit) || 10;
        limit = [5, 10, 25, 50].includes(limit) ? limit : 10;
        
        const search = req.query.search || null;
        const trainer = req.query.trainer || null;
        const course_category = req.query.course_category || null;

        console.log("trainer", trainer);
        console.log("course_category", course_category);

        const offset = (page - 1) * limit;
        let query = `SELECT * FROM ${req.user.db_name}.courses WHERE is_approved = 1 AND is_deleted = 0`;
        let countQuery = `SELECT COUNT(*) AS totalCount FROM ${req.user.db_name}.courses WHERE is_approved = 1 AND is_deleted = 0`;

        // Add search filtering
        if (search) {
            console.log("SEARCH QUERY IS THERE", search);
            query += ` AND course_name LIKE ?`;
            countQuery += ` AND course_name LIKE ?`;
        }

        // Add trainer filtering
        if (trainer !== 'null') {
            query += ` AND created_by = ?`;
            countQuery += ` AND created_by = ?`;
        }

        if (course_category != 'null') {
            query += ` AND course_category = ?`;
            countQuery += ` AND course_category = ?`;
        }

        // Add pagination to the query
        query += ` LIMIT ? OFFSET ?`;

        // Prepare parameters for the queries
        const searchParam = [];
        const countSearchParam = [];

        if (search) {
            searchParam.push(`%${search}%`);
            countSearchParam.push(`%${search}%`);
        }
        if (trainer != 'null') {
            searchParam.push(trainer);
            countSearchParam.push(trainer);
        }
        if (course_category != 'null') {
            searchParam.push(course_category);
            countSearchParam.push(course_category);
        }
        
        // Add pagination parameters
        searchParam.push(limit, offset);

        // Execute the query with search, trainer, and pagination parameters
        const [approvedCourses] = await mysqlServerConnection.query(query, searchParam);
        console.log(searchParam);
       
        // Execute the count query to get the total count for pagination metadata
        const [[{ totalCount }]] = await mysqlServerConnection.query(countQuery, countSearchParam);

        // Respond with the courses and pagination info
        res.status(200).json({
            success: true,
            data: {
                approvedCourses,
                pagination: {
                    page: page,
                    limit: limit,
                    totalPages: Math.ceil(totalCount / limit),
                    totalCount: totalCount
                }
            }
        });
    } catch (error) {
        console.error('Error fetching approved courses:', error);
        return next({ statusCode: 500, message: error.message || 'Internal Server Error' });
    }
};

module.exports = {
    approveCourse,
    getCoursesBasedOnType,
    rejectCourse,
    getAllPendingCourses,
    getParticularUserDetails,
    getApprovedCourses,
    sendApprovalcourse,
    cancelApprovalCourseRequest
};
