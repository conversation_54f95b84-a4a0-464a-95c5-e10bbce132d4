const crypto = require('crypto');
const { mysqlServerConnection } = require('../../../db/db');

const PAY_U_MERCHANT_KEY = process.env.PAY_U_MERCHANT_KEY;
const PAY_U_MERCHANT_SALT = process.env.PAY_U_MERCHANT_SALT;
const PAYU_BASE_URL = process.env.PAYU_BASE_URL;

// Enhanced environment variable logging
console.log('=== PayU Environment Variables Check ===');
console.log('PAY_U_MERCHANT_KEY:', PAY_U_MERCHANT_KEY ? `${PAY_U_MERCHANT_KEY.substring(0, 8)}...` : 'NOT SET');
console.log('PAY_U_MERCHANT_SALT:', PAY_U_MERCHANT_SALT ? `${PAY_U_MERCHANT_SALT.substring(0, 8)}...` : 'NOT SET');
console.log('PAYU_BASE_URL:', PAYU_BASE_URL || 'NOT SET');
console.log('========================================');

// Generate hash for PayU
const generateHash = (data) => {
    console.log('=== Generating PayU Hash ===');
    console.log('Hash Input Data:', {
        key: data.key,
        txnid: data.txnid,
        amount: data.amount,
        productinfo: data.productinfo,
        firstname: data.firstname,
        email: data.email
    });
    
    const hashString = `${PAY_U_MERCHANT_KEY}|${data.txnid}|${data.amount}|${data.productinfo}|${data.firstname}|${data.email}|||||||||||${PAY_U_MERCHANT_SALT}`;
    console.log('Hash String (masked):', hashString.replace(PAY_U_MERCHANT_SALT, '***SALT***'));
    
    const hash = crypto.createHash('sha512').update(hashString).digest('hex');
    console.log('Generated Hash:', hash);
    console.log('============================');
    return hash;
};

// Generate unique transaction ID
const generateTxnId = () => {
    const txnid = 'TXN' + Date.now() + Math.floor(Math.random() * 1000);
    console.log('Generated Transaction ID:', txnid);
    return txnid;
};

const processPayUPayment = async (req, res) => {
    console.log('=== PayU Payment Processing Started ===');
    console.log('Request Body:', JSON.stringify(req.body, null, 2));
    console.log('User Info:', {
        userId: req.user?.userId,
        db_name: req.user?.db_name,
        email: req.user?.email
    });
    
    try {
        const { amount, currency, origin, courseName, points, course_id } = req.body;
        const user = req.user;

        console.log('Extracted Data:', {
            amount,
            currency,
            origin,
            courseName,
            points,
            course_id
        });

        // Validate required fields
        console.log('=== Validating Required Fields ===');
        const requiredFields = { amount, currency, origin, courseName, course_id };
        const missingFields = Object.keys(requiredFields).filter(key => !requiredFields[key]);
        
        if (missingFields.length > 0) {
            console.log('Missing Required Fields:', missingFields);
            return res.status(400).json({
                success: false,
                data: { error_msg: 'Missing required fields', missing: missingFields }
            });
        }
        console.log('All required fields are present');

        // Check if the course is already purchased
        console.log('=== Checking Course Purchase Status ===');
        const courseCheckQuery = `SELECT * FROM ${user.db_name}.mycourses WHERE course_id = ? AND user_id = ? LIMIT 1`;
        console.log('Course Check Query:', courseCheckQuery);
        console.log('Query Parameters:', [course_id, user.userId]);
        
        const [isCourseExistRows] = await mysqlServerConnection.query(courseCheckQuery, [course_id, user.userId]);
        console.log('Course Check Result:', isCourseExistRows);

        if (isCourseExistRows.length > 0) {
            console.log('Course already purchased by user');
            return res.status(409).json({
                success: false,
                data: { error_msg: 'Course already purchased' }
            });
        }
        console.log('Course not previously purchased');

        // Get user profile for payment details
        console.log('=== Fetching User Profile ===');
        const userQuery = `SELECT name, email, mobile FROM ${user.db_name}.users WHERE id = ? LIMIT 1`;
        console.log('User Query:', userQuery);
        console.log('User Query Parameters:', [user.userId]);
        
        const [userRows] = await mysqlServerConnection.query(userQuery, [user.userId]);
        console.log('User Profile Result:', userRows);

        if (userRows.length === 0) {
            console.log('User not found in database');
            return res.status(404).json({
                success: false,
                data: { error_msg: 'User not found' }
            });
        }

        const userProfile = userRows[0];
        console.log('User Profile Retrieved:', {
            name: userProfile.name,
            email: userProfile.email,
            mobile: userProfile.mobile
        });

        const txnid = generateTxnId();

        // Convert amount from cents to INR (assuming amount comes in cents)
        const amtInINR = (amount / 100).toFixed(2);
        console.log('Amount Conversion:', {
            originalAmount: amount,
            convertedAmount: amtInINR,
            currency: 'INR'
        });

        // Prepare PayU payment data
        console.log('=== Preparing PayU Payment Data ===');
        const paymentData = {
            key: PAY_U_MERCHANT_KEY,
            txnid: txnid,
            amount: amtInINR,
            productinfo: courseName,
            firstname: userProfile.name || 'User',
            email: userProfile.email,
            phone: userProfile.mobile || '**********',
            surl: `${origin}/user/courses/successPayUPayment?course_id=${course_id}&txnid=${txnid}`,
            furl: `${origin}/user/courses/cancelPayUPayment?course_id=${course_id}&txnid=${txnid}`,
            service_provider: 'payu_paisa'
        };

        console.log('Payment Data Prepared:', {
            ...paymentData,
            key: paymentData.key ? `${paymentData.key.substring(0, 8)}...` : 'NOT SET'
        });

        // Generate hash
        console.log('=== Generating Payment Hash ===');
        paymentData.hash = generateHash(paymentData);

        // Insert payment record into the database
        console.log('=== Inserting Payment Record ===');
        const insertQuery = `INSERT INTO ${user.db_name}.payments (user_id, amount, currency, payment_method, payment_uid, course_name, discount_point, status)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)`;
        const insertParams = [user.userId, amtInINR, 'INR', 'payu', txnid, courseName, points || 0, 'pending'];
        
        console.log('Payment Insert Query:', insertQuery);
        console.log('Payment Insert Parameters:', insertParams);
        
        await mysqlServerConnection.query(insertQuery, insertParams);
        console.log('Payment record inserted successfully');

        console.log('=== PayU Payment Processing Completed Successfully ===');
        console.log('Final Payment Data:', {
            ...paymentData,
            key: paymentData.key ? `${paymentData.key.substring(0, 8)}...` : 'NOT SET',
            hash: paymentData.hash ? `${paymentData.hash.substring(0, 16)}...` : 'NOT SET'
        });

        // Return payment data for frontend to submit form
        return res.status(200).json({
            success: true,
            paymentData: paymentData,
            payuUrl: PAYU_BASE_URL
        });

    } catch (error) {
        console.log('=== PayU Payment Processing Error ===');
        console.error('Error Details:', {
            message: error.message,
            stack: error.stack,
            code: error.code,
            errno: error.errno,
            sqlMessage: error.sqlMessage
        });
        console.log('Request Body that caused error:', JSON.stringify(req.body, null, 2));
        console.log('User Info at error:', {
            userId: req.user?.userId,
            db_name: req.user?.db_name
        });
        
        return res.status(500).json({
            success: false,
            error_msg: "PayU payment processing failed",
            details: error.message,
        });
    }
};

const successPayUPayment = async (req, res) => {
    console.log('=== PayU Success Callback Started ===');
    console.log('Query Parameters:', JSON.stringify(req.query, null, 2));
    console.log('User Info:', {
        userId: req.user?.userId,
        db_name: req.user?.db_name
    });
    
    try {
        const db_name = req.user.db_name;
        const user_id = req.user.userId;
        const course_id = req.query.course_id;
        const txnid = req.query.txnid;
        console.log('Extracted Success Validation Data:', {
            db_name,
            user_id,
            course_id,
            txnid
        });

        // Validate required parameters
        if (!course_id || !txnid) {
            console.log('Missing required parameters');
            return res.status(400).json({
                success: false,
                data: { error_msg: 'Missing course_id or transaction ID' }
            });
        }
        console.log('Required parameters validated - proceeding with payment verification');

        // Get payment record
        console.log('=== Fetching Payment Record ===');
        const paymentQuery = `SELECT * FROM ${db_name}.payments WHERE user_id = ? AND payment_uid = ?`;
        console.log('Payment Query:', paymentQuery);
        console.log('Payment Query Parameters:', [user_id, txnid]);
        
        const [payment] = await mysqlServerConnection.query(paymentQuery, [user_id, txnid]);
        console.log('Payment Record Result:', payment);

        if (payment.length === 0) {
            console.log('Payment record not found');
            return res.status(404).json({
                success: false,
                data: { error_msg: 'Payment record not found' }
            });
        }
        console.log('Payment record found:', payment[0]);

        // Get course details
        console.log('=== Fetching Course Details ===');
        const courseQuery = `SELECT * FROM ${db_name}.courses WHERE id = ? LIMIT 1`;
        console.log('Course Query:', courseQuery);
        console.log('Course Query Parameters:', [course_id]);
        
        const [courseRows] = await mysqlServerConnection.query(courseQuery, [course_id]);
        console.log('Course Details Result:', courseRows);

        if (courseRows.length === 0) {
            console.log('Course not found');
            return res.status(404).json({
                success: false,
                data: { error_msg: 'Course not found' }
            });
        }
        console.log('Course found:', courseRows[0]);

        // Check if course already enrolled to prevent duplicates
        console.log('=== Checking Existing Enrollment ===');
        const enrollmentCheckQuery = `SELECT * FROM ${db_name}.mycourses WHERE user_id = ? AND course_id = ? LIMIT 1`;
        const [existingEnrollment] = await mysqlServerConnection.query(enrollmentCheckQuery, [user_id, course_id]);
        
        if (existingEnrollment.length === 0) {
            // Add course to user's courses (using actual mycourses table columns)
            console.log('=== Adding Course to User Courses ===');
            const mycoursesQuery = `INSERT INTO ${db_name}.mycourses (user_id, course_id) VALUES (?, ?)`;
            const mycoursesParams = [user_id, course_id];
            
            console.log('MyCourses Insert Query:', mycoursesQuery);
            console.log('MyCourses Insert Parameters:', mycoursesParams);
            
            await mysqlServerConnection.query(mycoursesQuery, mycoursesParams);
            console.log('Course added to user courses successfully');
        } else {
            console.log('Course already enrolled, skipping enrollment');
        }

        // Update payment status to completed
        console.log('=== Updating Payment Status ===');
        const updatePaymentQuery = `UPDATE ${db_name}.payments SET status = 'completed' WHERE user_id = ? AND payment_uid = ?`;
        const updatePaymentParams = [user_id, txnid];

        console.log('Payment Update Query:', updatePaymentQuery);
        console.log('Payment Update Parameters:', updatePaymentParams);

        await mysqlServerConnection.query(updatePaymentQuery, updatePaymentParams);
        console.log('Payment status updated to completed');

        const course = courseRows[0];
        console.log('=== PayU Success Validation Completed ===');
        console.log('Final Success Response:', {
            payment_id: txnid,
            price: course.course_price,
            transaction_id: txnid,
            course_name: course.course_name
        });

        return res.status(200).json({
            success: true,
            data: {
                payment_id: txnid,
                price: course.course_price,
                transaction_id: txnid,
                course_name: course.course_name,
                message: 'Payment verified and course enrolled successfully!'
            }
        });

    } catch (error) {
        console.log('=== PayU Success Callback Error ===');
        console.error('Error Details:', {
            message: error.message,
            stack: error.stack,
            code: error.code,
            errno: error.errno,
            sqlMessage: error.sqlMessage
        });
        console.log('Query Parameters that caused error:', JSON.stringify(req.query, null, 2));
        console.log('User Info at error:', {
            userId: req.user?.userId,
            db_name: req.user?.db_name
        });
        
        return res.status(500).json({
            success: false,
            error_msg: "PayU payment verification failed",
            details: error.message,
        });
    }
};

const cancelPayUPayment = async (req, res) => {
    console.log('=== PayU Cancel Callback Started ===');
    console.log('Query Parameters:', JSON.stringify(req.query, null, 2));
    console.log('User Info:', {
        userId: req.user?.userId,
        db_name: req.user?.db_name
    });
    
    try {
        const db_name = req.user.db_name;
        const user_id = req.user.userId;
        const txnid = req.query.txnid;

        console.log('Extracted Cancel Data:', {
            db_name,
            user_id,
            txnid
        });

        // Update payment status to failed (since 'cancelled' is not in ENUM)
        console.log('=== Updating Payment Status to Failed ===');
        const cancelQuery = `UPDATE ${db_name}.payments SET status = 'failed' WHERE user_id = ? AND payment_uid = ?`;
        console.log('Cancel Update Query:', cancelQuery);
        console.log('Cancel Update Parameters:', [user_id, txnid]);
        
        await mysqlServerConnection.query(cancelQuery, [user_id, txnid]);
        console.log('Payment status updated to failed successfully');

        console.log('=== PayU Cancel Callback Completed ===');
        console.log('Final Cancel Response:', {
            transaction_id: txnid,
            message: 'Payment cancelled'
        });

        return res.status(200).json({
            success: true,
            data: {
                transaction_id: txnid,
                message: 'Payment cancelled'
            }
        });

    } catch (error) {
        console.log('=== PayU Cancel Callback Error ===');
        console.error('Error Details:', {
            message: error.message,
            stack: error.stack,
            code: error.code,
            errno: error.errno,
            sqlMessage: error.sqlMessage
        });
        console.log('Query Parameters that caused error:', JSON.stringify(req.query, null, 2));
        console.log('User Info at error:', {
            userId: req.user?.userId,
            db_name: req.user?.db_name
        });
        
        return res.status(500).json({
            success: false,
            error_msg: "Payment cancellation processing failed",
            details: error.message,
        });
    }
};


// Test PayU connectivity
const testPayUConnectivity = async (req, res) => {
    try {
        const testUrls = [
            'https://test.payu.in/_payment',
            'https://secure.payu.in/_payment',
            'https://sandboxsecure.payu.in/_payment'
        ];

        const results = [];

        for (const url of testUrls) {
            try {
                const response = await fetch(url, {
                    method: 'HEAD',
                    timeout: 5000
                });
                results.push({
                    url,
                    status: response.status,
                    accessible: response.ok
                });
            } catch (error) {
                results.push({
                    url,
                    status: 'ERROR',
                    accessible: false,
                    error: error.message
                });
            }
        }

        return res.status(200).json({
            success: true,
            currentUrl: PAYU_BASE_URL,
            testResults: results
        });
    } catch (error) {
        return res.status(500).json({
            success: false,
            error: error.message
        });
    }
};

module.exports = {
    processPayUPayment,
    successPayUPayment,
    cancelPayUPayment,
    testPayUConnectivity
};
