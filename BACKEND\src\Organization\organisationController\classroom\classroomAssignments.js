const { mysqlServerConnection } = require("../../../db/db");
const { formatDateTimeForMySQL } = require("../../../utils/dateFormat");
const { uploadDocToS3,deleteFileFromS3 } = require("../../../tools/aws");
const { uploadImageToS3, uploadAudioToS3 } = require("../../../tools/aws");


const createClassroomAssignment = async (req, res, next) => {
  try {
    const {
      class_id,
      title,
      description,
      due_date,
      total_marks,
      is_visible,
      time_zone
    } = req.body;

    console.log("CREATE CLASSROOM ASSIGNMENT",req.body);

    const dbName = req.user.db_name;
    const created_by = req.user.userId;

    // ✅ Basic validation
    if (!class_id || !title) {
      return res.status(400).json({
        success: false,
        message: "Missing required fields. class_id and title are required",
      });
    }

    // ✅ Check if classroom exists
    const [classExists] = await mysqlServerConnection.query(
      `SELECT id FROM ${dbName}.classroom WHERE id = ? AND is_deleted = 0`,
      [class_id]
    );

    if (classExists.length === 0) {
      return res.status(404).json({
        success: false,
        message: "Classroom not found or has been deleted",
      });
    }

    // ✅ Upload file to S3 (if present)
    let attachment_url = null;
    if (req.file && req.file.buffer && req.file.originalname) {
      try {
        const fileData = await uploadDocToS3(req.file.buffer, req.file.originalname);
        attachment_url = fileData.path;
      } catch (uploadErr) {
        console.error("❌ File upload failed:", uploadErr);
        return res.status(500).json({
          success: false,
          message: "File upload failed",
        });
      }
    }

    // ✅ Prepare other values
    const parsedTotalMarks =
      total_marks === '' || total_marks === null || total_marks === undefined
        ? null
        : parseInt(total_marks, 10);

    // Convert UTC ISO string to MySQL datetime format (YYYY-MM-DD HH:mm:ss)
    const formattedDueDate = due_date ? new Date(due_date).toISOString().slice(0, 19).replace('T', ' ') : null;
    const assignedDate = formatDateTimeForMySQL(new Date());
    const visibleValue = is_visible === 0 ? 0 : 1;

    // ✅ Insert into classroom_assignments
    const [result] = await mysqlServerConnection.query(
      `INSERT INTO ${dbName}.classroom_assignments 
        (class_id, is_visible, created_by, title, description, attachment_url, due_date, assigned_date, createdAt, updatedAt, is_deleted, total_marks, time_zone) 
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 0, ?, ?)`,
      [
        class_id,
        visibleValue,
        created_by,
        title,
        description || null,
        attachment_url,
        formattedDueDate,
        assignedDate,
        parsedTotalMarks,
        time_zone
      ]
    );

    if (result.affectedRows === 0) {
      return res.status(500).json({
        success: false,
        message: "Failed to create assignment",
      });
    }

    const assignmentId = result.insertId;

    // ✅ Fetch trainees in this classroom
    try {
      const [trainees] = await mysqlServerConnection.query(
        `SELECT user_id FROM ${dbName}.classroom_trainee WHERE class_id = ?`,
        [class_id]
      );

      if (trainees.length > 0) {
        const now = new Date();
        const taskData = trainees.map(({ user_id }) => [
          user_id,
          class_id,
          null,            // assessment_id
          assignmentId,    // assignment_id
          0,               // is_seen
          now,
          now
        ]);

        await mysqlServerConnection.query(
          `INSERT INTO ${dbName}.task_lists 
            (user_id, classroom_id, assessment_id, assignment_id, is_seen, created_at, updated_at)
           VALUES ?`,
          [taskData]
        );
      }
    } catch (taskErr) {
      console.error("⚠️ Error creating tasks for trainees:", taskErr);
    }

    // ✅ Final response
    return res.status(201).json({
      success: true,
      message: "Assignment created successfully and tasks added",
      data: {
        id: assignmentId,
        class_id,
        created_by,
        title,
        description,
        attachment_url,
        due_date: formattedDueDate,
        assigned_date: assignedDate,
        total_marks: parsedTotalMarks,
        is_visible: visibleValue
      }
    });

  } catch (error) {
    console.error("🔥 Error in createClassroomAssignment:", error);
    return next({
      statusCode: 500,
      message: error.message || "Internal Server Error",
    });
  }
};



const updateClassroomAssignment = async (req, res, next) => {
  try {
    const {
      class_id,
      title,
      description,
      due_date,
      assignment_id,
      total_marks,
      is_visible,
      time_zone
    } = req.body;

    const dbName = req.user.db_name;

    if (!assignment_id) {
      return res.status(400).json({
        success: false,
        message: "Assignment ID is required",
      });
    }

    // Check if assignment exists
    const [assignmentExists] = await mysqlServerConnection.query(
      `SELECT * FROM ${dbName}.classroom_assignments WHERE id = ? AND is_deleted = 0`,
      [assignment_id]
    );

    if (assignmentExists.length === 0) {
      return res.status(404).json({
        success: false,
        message: "Assignment not found or has been deleted",
      });
    }

    const existingAssignment = assignmentExists[0];

    // Optional classroom existence check
    if (class_id) {
      const [classExists] = await mysqlServerConnection.query(
        `SELECT id FROM ${dbName}.classroom WHERE id = ? AND is_deleted = 0`,
        [class_id]
      );

      if (classExists.length === 0) {
        return res.status(404).json({
          success: false,
          message: "Classroom not found or has been deleted",
        });
      }
    }

    // File upload logic
    let attachment_url = existingAssignment.attachment_url;
    if (req.file && req.file.buffer && req.file.originalname) {
      try {
        const fileData = await uploadDocToS3(req.file.buffer, req.file.originalname);
        attachment_url = fileData.path;
      } catch (error) {
        return res.status(500).json({
          success: false,
          message: "Error uploading attachment file",
        });
      }
    }

    // Data processing
    const formattedDueDate = due_date ? new Date(due_date).toISOString().slice(0, 19).replace('T', ' ') : existingAssignment.due_date;
    const parsedTotalMarks =
      total_marks === '' || total_marks === null || total_marks === undefined
        ? existingAssignment.total_marks
        : parseInt(total_marks, 10);

    const parsedIsVisible =
      is_visible === undefined || is_visible === null
        ? existingAssignment.is_visible
        : parseInt(is_visible, 10);

    // Perform update
    const [result] = await mysqlServerConnection.query(
      `UPDATE ${dbName}.classroom_assignments 
      SET 
        class_id = ?, 
        title = ?, 
        description = ?, 
        attachment_url = ?, 
        due_date = ?, 
        total_marks = ?, 
        is_visible = ?,  
        time_zone = ?,
        updatedAt = CURRENT_TIMESTAMP 
      WHERE id = ? AND is_deleted = 0`,
      [
        class_id || existingAssignment.class_id,
        title || existingAssignment.title,
        description !== undefined ? description : existingAssignment.description,
        attachment_url,
        formattedDueDate,
        parsedTotalMarks,
        parsedIsVisible,
        time_zone,
        assignment_id,
      ]
    );

    if (result.affectedRows > 0) {
      return res.status(200).json({
        success: true,
        message: "Assignment updated successfully",
        data: {
          id: parseInt(assignment_id),
          class_id: class_id || existingAssignment.class_id,
          title: title || existingAssignment.title,
          description: description !== undefined ? description : existingAssignment.description,
          attachment_url,
          due_date: formattedDueDate,
          total_marks: parsedTotalMarks,
          is_visible: parsedIsVisible,
        },
      });
    } else {
      return res.status(500).json({
        success: false,
        message: "Failed to update assignment",
      });
    }
  } catch (error) {
    console.error("Error updating assignment:", error);
    return next({
      statusCode: 500,
      message: error.message || "Internal Server Error",
    });
  }
};



const getClassroomAssignments = async (req, res, next) => {
  try {
    const { class_id, page = 1, limit = 10, search = "", startDate, endDate } = req.body;
    const dbName = req.user.db_name;
    const offset = (page - 1) * limit;

    console.log("📥 Request Body:", req.body);
    console.log("🔍 DB Name:", dbName);

    // Validate required fields
    if (!class_id) {
      console.warn("❌ class_id missing in request");
      return res.status(400).json({
        success: false,
        message: "class_id is required",
      });
    }

    // Check if classroom exists
    const [classExists] = await mysqlServerConnection.query(
      `SELECT id FROM ${dbName}.classroom WHERE id = ? AND is_deleted = 0`,
      [class_id]
    );

    console.log("✅ Classroom Exists:", classExists.length > 0);

    if (classExists.length === 0) {
      return res.status(404).json({
        success: false,
        message: "Classroom not found or has been deleted",
      });
    }

    // Build the query with filters
    let query = `
      SELECT 
        ca.id, 
        ca.class_id,
        ca.title,
        ca.description,
        ca.attachment_url,
        DATE_FORMAT(ca.due_date, '%Y-%m-%d %H:%i:%s') as due_date,
        DATE_FORMAT(ca.assigned_date, '%Y-%m-%d %H:%i:%s') as assigned_date,
        ca.total_marks,
        ca.is_visible,
        ca.time_zone,
        ca.created_by,
        DATE_FORMAT(ca.createdAt, '%Y-%m-%d %H:%i:%s') as createdAt,
        DATE_FORMAT(ca.updatedAt, '%Y-%m-%d %H:%i:%s') as updatedAt,
        u.name as creator_name,
        u.email as creator_email,
        c.cls_name as classroom_name
      FROM ${dbName}.classroom_assignments ca
      JOIN ${dbName}.users u ON ca.created_by = u.id
      JOIN ${dbName}.classroom c ON ca.class_id = c.id
      WHERE ca.class_id = ? AND ca.is_deleted = 0
    `;

    const queryParams = [class_id];

    // Add search filter if provided
    if (search) {
      console.log("🔎 Applying search filter:", search);
      query += ` AND (ca.title LIKE ? OR ca.description LIKE ?)`;
      queryParams.push(`%${search}%`, `%${search}%`);
    }

    // Add date filters if provided
    if (startDate) {
      const formattedStart = formatDateTimeForMySQL(new Date(startDate));
      console.log("📅 Start Date Filter:", formattedStart);
      query += ` AND ca.assigned_date >= ?`;
      queryParams.push(formattedStart);
    }

    if (endDate) {
      const formattedEnd = formatDateTimeForMySQL(new Date(endDate));
      console.log("📅 End Date Filter:", formattedEnd);
      query += ` AND ca.assigned_date <= ?`;
      queryParams.push(formattedEnd);
    }

    // Add count query for pagination
    const countQuery = query.replace(
      "SELECT \n        ca.id,\n        ca.class_id,\n        ca.title,\n        ca.description,\n        ca.attachment_url,\n        ca.due_date,\n        ca.assigned_date,\n        ca.total_marks,\n        ca.is_visible,\n        ca.time_zone,\n        ca.created_by,\n        ca.createdAt,\n        ca.updatedAt,\n        u.name as creator_name,\n        u.email as creator_email,\n        c.cls_name as classroom_name",
      "SELECT COUNT(*) as total"
    );

    console.log("🧮 Running count query...");
    const [countResult] = await mysqlServerConnection.query(countQuery, queryParams);
    const totalCount = countResult[0].total;
    const totalPages = Math.ceil(totalCount / limit);
    console.log("📊 Total Assignments:", totalCount, "| Total Pages:", totalPages);

    // Add order by and pagination
    query += ` ORDER BY ca.createdAt DESC LIMIT ? OFFSET ?`;
    queryParams.push(parseInt(limit), offset);
    console.log("📤 Final Query with Params:", query, queryParams);

    // Execute the main query
    const [assignments] = await mysqlServerConnection.query(query, queryParams);
    console.log("✅ Assignments Fetched:", assignments.length);

    // Format the dates - preserve the UTC time from database
    const formattedAssignments = assignments.map(assignment => {
      // Log raw dates from database
      console.log("Raw DB dates:", {
        due_date: assignment.due_date,
        assigned_date: assignment.assigned_date,
        createdAt: assignment.createdAt,
        updatedAt: assignment.updatedAt
      });

      return {
        ...assignment,
        // Return dates exactly as they are in the database
        due_date: assignment.due_date,
        assigned_date: assignment.assigned_date,
        createdAt: assignment.createdAt,
        updatedAt: assignment.updatedAt
      };
    });

    console.log("📝 Final Formatted Assignments:", formattedAssignments);

    return res.status(200).json({
      success: true,
      message: "Assignments retrieved successfully",
      data: {
        assignments: formattedAssignments,
        pagination: {
          total: totalCount,
          page: parseInt(page),
          limit: parseInt(limit),
          totalPages,
        },
      },
    });
  } catch (error) {
    console.error("❌ Error retrieving assignments:", error);
    return next({
      statusCode: 500,
      message: error.message || "Internal Server Error",
    });
  }
};












































const deleteClassroomAssignment = async (req, res, next) => {
  try {
    const { assignment_id } = req.body;
    const dbName = req.user.db_name;

    // Validate required fields
    if (!assignment_id) {
      return res.status(400).json({
        success: false,
        message: "Assignment ID is required",
      });
    }

    // Check if assignment exists and get attachment URL
    const [assignment] = await mysqlServerConnection.query(
      `SELECT id, attachment_url FROM ${dbName}.classroom_assignments WHERE id = ? AND is_deleted = 0`,
      [assignment_id]
    );

    if (assignment.length === 0) {
      return res.status(404).json({
        success: false,
        message: "Assignment not found or has already been deleted",
      });
    }

    // Delete attachment from S3 if it exists
    if (assignment[0].attachment_url) {
      try {
        await deleteFileFromS3(assignment[0].attachment_url);
        console.log(`Successfully deleted attachment from S3: ${assignment[0].attachment_url}`);
      } catch (s3Error) {
        console.error(`Error deleting attachment from S3: ${s3Error.message}`);
        // Continue with deletion even if S3 deletion fails
      }
    }

    // Soft delete the assignment (set is_deleted to true)
    const [result] = await mysqlServerConnection.query(
      `UPDATE ${dbName}.classroom_assignments SET is_deleted = 1, updatedAt = CURRENT_TIMESTAMP WHERE id = ?`,
      [assignment_id]
    );

    if (result.affectedRows > 0) {
      return res.status(200).json({
        success: true,
        message: "Assignment deleted successfully",
      });
    } else {
      return res.status(500).json({
        success: false,
        message: "Failed to delete assignment",
      });
    }
  } catch (error) {
    console.error("Error deleting assignment:", error);
    return next({
      statusCode: 500,
      message: error.message || "Internal Server Error",
    });
  }
};



const getClassroomAssignmentById = async (req, res, next) => {

  try {
    const { assignment_id, class_id } = req.body;
    const dbName = req.user.db_name;

    // Validate required fields
    if (!assignment_id || !class_id) {
      return res.status(400).json({
        success: false,
        message: "Assignment ID and Class ID are required",
      });
    }

    // Get assignment details
    const [assignment] = await mysqlServerConnection.query(
      `SELECT 
        ca.id,
        ca.class_id,
        ca.title,
        ca.description,
        ca.attachment_url,
        DATE_FORMAT(ca.due_date, '%Y-%m-%d %H:%i:%s') as due_date,
        DATE_FORMAT(ca.assigned_date, '%Y-%m-%d %H:%i:%s') as assigned_date,
        ca.total_marks,
        ca.created_by,
        DATE_FORMAT(ca.createdAt, '%Y-%m-%d %H:%i:%s') as createdAt,
        DATE_FORMAT(ca.updatedAt, '%Y-%m-%d %H:%i:%s') as updatedAt,
        u.name as creator_name,
        u.email as creator_email,
        c.cls_name as classroom_name,
        c.banner_img as classroom_banner
      FROM ${dbName}.classroom_assignments ca
      JOIN ${dbName}.users u ON ca.created_by = u.id
      JOIN ${dbName}.classroom c ON ca.class_id = c.id
      WHERE ca.id = ? AND ca.class_id = ? AND ca.is_deleted = 0`,
      [assignment_id, class_id]
    );

    if (assignment.length === 0) {
      return res.status(404).json({
        success: false,
        message: "Assignment not found or has been deleted",
      });
    }

    // Get assignment questions
    const [questions] = await mysqlServerConnection.query(
      `SELECT 
        id,
        question_text,
        response_type,
        marks,
        question_order
      FROM ${dbName}.classroom_assignment_questions
      WHERE assignment_id = ? AND class_id = ?
      ORDER BY question_order ASC`,
      [assignment_id, class_id]
    );

    // Format dates - preserve UTC times from database
    const assignmentData = {
      ...assignment[0],
      due_date: assignment[0].due_date,
      assigned_date: assignment[0].assigned_date,
      createdAt: assignment[0].createdAt,
      updatedAt: assignment[0].updatedAt
    };

    // Calculate total marks from questions (if available)
    let questionTotalMarks = 0;
    if (questions.length > 0) {
      questionTotalMarks = questions.reduce((sum, question) => sum + (question.marks || 0), 0);
    }

    return res.status(200).json({
      success: true,
      message: "Assignment retrieved successfully",
      data: {
        assignment: assignmentData,
        questions: questions,
      }
    });
  } catch (error) {
    console.error("Error retrieving assignment:", error);
    return next({
      statusCode: 500,
      message: error.message || "Internal Server Error",
    });
  }
};






const submitClassroomAssignment = async (req, res, next) => {
  try {
    const { assignment_id, class_id } = req.body;
    const dbName = req.user.db_name;
    const user_id = req.user.userId;

    console.log("SUBMIT CLASSROOM ASSIGNMENT",req.body);

    // Validate required fields
    if (!assignment_id || !class_id) {
      return res.status(400).json({
        success: false,
        message: "Assignment ID and Class ID are required",
      });
    }

    // Check if assignment exists and is not past due date
    const [assignmentExists] = await mysqlServerConnection.query(
      `SELECT id, due_date, title FROM ${dbName}.classroom_assignments 
       WHERE id = ? AND class_id = ? AND is_deleted = 0`,
      [assignment_id, class_id]
    );

    if (assignmentExists.length === 0) {
      return res.status(404).json({
        success: false,
        message: "Assignment not found or has been deleted",
      });
    }

    // Check if the assignment is past due date
    const assignment = assignmentExists[0];
    const currentDate = new Date();
    const dueDate = new Date(assignment.due_date);

    if (dueDate < currentDate) {
      return res.status(400).json({
        success: false,
        message: "Assignment submission deadline has passed",
      });
    }


    // Check if user has already submitted this assignment
    const [existingSubmission] = await mysqlServerConnection.query(
      `SELECT id, submission_url FROM ${dbName}.classroom_assignment_submissions 
       WHERE assignment_id = ? AND user_id = ? AND class_id = ? AND is_deleted = 0`,
      [assignment_id, user_id, class_id]
    );

    let submission_url = null;
    
    // Handle file upload if a file is provided
    if (req.file) {
      // Delete previous submission file if it exists
      if (existingSubmission.length > 0 && existingSubmission[0].submission_url) {
        try {
          await deleteFileFromS3(existingSubmission[0].submission_url);
          console.log(`Successfully deleted previous submission from S3: ${existingSubmission[0].submission_url}`);
        } catch (s3Error) {
          console.error(`Error deleting previous submission from S3: ${s3Error.message}`);
          // Continue with upload even if S3 deletion fails
        }
      }

      // Upload new file to S3
      try {
        const fileBuffer = req.file.buffer;
        const fileName = `${user_id}_${assignment_id}_${Date.now()}_${req.file.originalname}`;
        
        const uploadResult = await uploadDocToS3(fileBuffer, fileName);
        submission_url = uploadResult.path;
      } catch (uploadError) {
        console.error("Error uploading file to S3:", uploadError);
        return res.status(500).json({
          success: false,
          message: "Failed to upload assignment file",
        });
      }
    } else if (existingSubmission.length === 0) {
      // If no file is provided and this is a new submission
      return res.status(400).json({
        success: false,
        message: "Assignment file is required for submission",
      });
    }

    // Create or update submission record
    if (existingSubmission.length > 0) {
      // Update existing submission
      const [updateResult] = await mysqlServerConnection.query(
        `UPDATE ${dbName}.classroom_assignment_submissions 
         SET submission_url = ?, submittedAt = CURRENT_TIMESTAMP, 
         assignment_evaluation_status = false, marks_obtained = NULL
         WHERE id = ?`,
        [submission_url || existingSubmission[0].submission_url, existingSubmission[0].id]
      );

      if (updateResult.affectedRows > 0) {
        return res.status(200).json({
          success: true,
          message: "Assignment resubmitted successfully",
          data: {
            submission_id: existingSubmission[0].id,
            assignment_id,
            class_id,
            user_id,
            submission_url: submission_url || existingSubmission[0].submission_url,
            submitted_at: new Date()
          }
        });
      } else {
        return res.status(500).json({
          success: false,
          message: "Failed to update assignment submission",
        });
      }
    } else {
      // Create new submission
      const [insertResult] = await mysqlServerConnection.query(
        `INSERT INTO ${dbName}.classroom_assignment_submissions
         (assignment_id, user_id, class_id, submission_url, assignment_evaluation_status, is_deleted)
         VALUES (?, ?, ?, ?, false, false)`,
        [assignment_id, user_id, class_id, submission_url]
      );

      if (insertResult.affectedRows > 0) {
        return res.status(201).json({
          success: true,
          message: "Assignment submitted successfully",
          data: {
            submission_id: insertResult.insertId,
            assignment_id,
            class_id,
            user_id,
            submission_url,
            submitted_at: new Date()
          }
        });
      } else {
        return res.status(500).json({
          success: false,
          message: "Failed to create assignment submission",
        });
      }
    }
  } catch (error) {
    console.error("Error submitting assignment:", error);
    return next({
      statusCode: 500,
      message: error.message || "Internal Server Error",
    });
  }
};
 

const getUserClassroomAssignments = async (req, res, next) => {
  console.log("🚀 USER CLASSROOM ASSIGNMENTS Request Body-:", req.body);

  try {
    let { class_id, user_id, page = 1, limit = 10, search = "" } = req.body;
    const dbName = req.user.db_name;

    console.log("✅ DB Name:", dbName);
    console.log("📌 Params - class_id:", class_id, "| user_id:", user_id, "| page:", page, "| limit:", limit);

    const offset = (page - 1) * limit;

    // Step 1: Check if user is trainee in this classroom
    const [traineeRows] = await mysqlServerConnection.query(
      `SELECT id FROM ${dbName}.classroom_trainee 
       WHERE class_id = ? AND user_id = ?`,
      [class_id, user_id]
    );

    if (traineeRows.length === 0) {
      return res.status(403).json({
        success: false,
        message: "User is not assigned to this classroom",
      });
    }

    // Step 2: Count total
    let countQuery = `
      SELECT COUNT(*) as total 
      FROM ${dbName}.classroom_assignments 
      WHERE class_id = ? AND is_deleted = 0 AND is_visible = 1
    `;
    let countParams = [class_id];

    if (search && search.trim() !== "") {
      countQuery += ` AND title LIKE ?`;
      countParams.push(`%${search.trim()}%`);
    }

    const [countResult] = await mysqlServerConnection.query(countQuery, countParams);
    const totalRecords = countResult[0].total;
    const totalPages = Math.ceil(totalRecords / limit);

    if (totalRecords === 0) {
      return res.status(200).json({
        success: true,
        message: "No assignments found",
        data: {
          assignments: [],
          pagination: {
            totalRecords,
            totalPages,
            currentPage: Number(page),
            limit: Number(limit),
          }
        }
      });
    }

    // Step 3: Fetch paginated assignments (raw due_date)
    let assignmentQuery = `
      SELECT 
        id,
        class_id,
        title,
        description,
        attachment_url,
        DATE_FORMAT(due_date, '%Y-%m-%d %H:%i:%s') as due_date,
        DATE_FORMAT(assigned_date, '%Y-%m-%d %H:%i:%s') as assigned_date,
        total_marks,
        is_visible,
        time_zone,
        created_by,
        DATE_FORMAT(createdAt, '%Y-%m-%d %H:%i:%s') as createdAt,
        DATE_FORMAT(updatedAt, '%Y-%m-%d %H:%i:%s') as updatedAt
      FROM ${dbName}.classroom_assignments 
      WHERE class_id = ? AND is_deleted = 0 AND is_visible = 1
    `;
    let assignmentParams = [class_id];

    if (search && search.trim() !== "") {
      assignmentQuery += ` AND title LIKE ?`;
      assignmentParams.push(`%${search.trim()}%`);
    }

    assignmentQuery += ` ORDER BY createdAt DESC LIMIT ? OFFSET ?`;
    assignmentParams.push(Number(limit), offset);

    const [assignments] = await mysqlServerConnection.query(assignmentQuery, assignmentParams);

    // Step 4: Attach question stats and status
    for (const assignment of assignments) {
      const [questionStats] = await mysqlServerConnection.query(
        `
        SELECT 
          total.total_questions,
          IFNULL(attended.attended_questions, 0) AS attended_questions,
          (total.total_questions - IFNULL(attended.attended_questions, 0)) AS remaining_questions
        FROM (
          SELECT COUNT(*) AS total_questions
          FROM ${dbName}.classroom_assignment_questions
          WHERE assignment_id = ?
        ) AS total
        LEFT JOIN (
          SELECT COUNT(DISTINCT question_id) AS attended_questions
          FROM ${dbName}.classroom_assignment_submissions
          WHERE assignment_id = ? AND user_id = ? AND is_deleted = 0
        ) AS attended ON 1 = 1
        `,
        [assignment.id, assignment.id, user_id]
      );

      assignment.total_questions = questionStats[0].total_questions;
      assignment.attended_questions = questionStats[0].attended_questions;
      assignment.remaining_questions = questionStats[0].remaining_questions;

      const isCompleted = assignment.total_questions > 0 &&
        assignment.attended_questions === assignment.total_questions;

      const currentTime = new Date();
      // Since due_date is now a string, parse it correctly
      const dueDate = new Date(assignment.due_date.replace(' ', 'T') + 'Z');
      const isOverdue = dueDate && currentTime > dueDate;

      if (isCompleted) {
        assignment.status = isOverdue ? "overdue-completed" : "completed";
      } else {
        assignment.status = isOverdue ? "overdue-pending" : "pending";
      }
    }

    return res.status(200).json({
      success: true,
      message: "Assignments fetched successfully",
      data: {
        assignments,
        pagination: {
          totalRecords,
          totalPages,
          currentPage: Number(page),
          limit: Number(limit),
        }
      }
    });

  } catch (error) {
    console.error("🔥 Error in getUserClassroomAssignments:", error);
    return next({
      statusCode: 500,
      message: error.message || "Internal Server Error",
    });
  }
};









const gradeClassroomAssignment = async (req,res,next)=>{
  try {
    const { user_id, class_id, assignment_id, marks_obtained, feedback } = req.body;
    const dbName = req.user.db_name;
    const grader_id = req.user.userId;

    // Validate required fields
    if (!user_id || !class_id || !assignment_id || marks_obtained === undefined) {
      return res.status(400).json({
        success: false,
        message: "User ID, Class ID, Assignment ID, and Marks are required",
      });
    }

    // Check if the assignment exists
    const [assignmentExists] = await mysqlServerConnection.query(
      `SELECT id, total_marks FROM ${dbName}.classroom_assignments 
       WHERE id = ? AND class_id = ? AND is_deleted = 0`,
      [assignment_id, class_id]
    );

    if (assignmentExists.length === 0) {
      return res.status(404).json({
        success: false,
        message: "Assignment not found or has been deleted",
      });
    }

    const totalMarks = assignmentExists[0].total_marks;

    // Validate marks are within range
    if (marks_obtained < 0 || marks_obtained > totalMarks) {
      return res.status(400).json({
        success: false,
        message: `Marks must be between 0 and ${totalMarks}`,
      });
    }

    // Check if submission exists
    const [submissionExists] = await mysqlServerConnection.query(
      `SELECT id FROM ${dbName}.classroom_assignment_submissions 
       WHERE assignment_id = ? AND user_id = ? AND class_id = ? AND is_deleted = 0`,
      [assignment_id, user_id, class_id]
    );

    if (submissionExists.length === 0) {
      return res.status(404).json({
        success: false,
        message: "Submission not found",
      });
    }

    const submission_id = submissionExists[0].id;

    // Update the submission with grades and feedback
    const [updateResult] = await mysqlServerConnection.query(
     `UPDATE ${dbName}.classroom_assignment_submissions 
      SET marks_obtained = ?, 
      assignment_evaluation_status = true,
      assignment_feedback = ?
      WHERE id = ?`,
      [marks_obtained, feedback || null, submission_id]
    );

    if (updateResult.affectedRows > 0) {
      // Get updated submission details
      const [updatedSubmission] = await mysqlServerConnection.query(
        `SELECT 
          cas.*, 
          ca.title as assignment_title,
          ca.total_marks,
          u.name as student_name,
          u.email as student_email
         FROM ${dbName}.classroom_assignment_submissions cas
         JOIN ${dbName}.classroom_assignments ca ON cas.assignment_id = ca.id
         JOIN ${dbName}.users u ON cas.user_id = u.id
         WHERE cas.id = ?`,
        [submission_id]
      );

      return res.status(200).json({
        success: true,
        message: "Assignment graded successfully",
        data: updatedSubmission[0]
      });
    } else {
      return res.status(500).json({
        success: false,
        message: "Failed to grade assignment",
      });
    }
  } catch (error) {
    console.error("Error grading assignment:", error);
    return next({
      statusCode: 500,
      message: error.message || "Internal Server Error",
    });
  }
};



const addClassroomAssignmentQuestion = async (req, res, next) => {
  try {
    const {
      assignment_id,
      class_id,
      question_type,
      question_text,
      response_type,
      question_order,
      marks
    } = req.body;

    const dbName = req.user.db_name;
    const media = req.file;

    console.log("assignment_id:", assignment_id);
    console.log("class_id:", class_id);
    console.log("question_type:", question_type);
    console.log("question_text:", question_text);
    console.log("response_type:", response_type);
    console.log("question_order:", question_order);
    console.log("media:", media);

    // Validate required fields
    if (!assignment_id || !class_id || !question_text || !response_type || !question_type) {
      return res.status(400).json({
        success: false,
        message: "Missing required fields. Please provide assignment_id, class_id, question_text, response_type, and question_type.",
      });
    }

    // Check if assignment exists
    const [assignmentExists] = await mysqlServerConnection.query(
      `SELECT id FROM ${dbName}.classroom_assignments 
       WHERE id = ? AND class_id = ? AND is_deleted = 0`,
      [assignment_id, class_id]
    );

    if (assignmentExists.length === 0) {
      return res.status(404).json({
        success: false,
        message: "Assignment not found or has been deleted",
      });
    }

    // Determine next question order
    let nextQuestionOrder = question_order || 1;
    if (!question_order) {
      const [highestOrder] = await mysqlServerConnection.query(
        `SELECT MAX(question_order) as max_order 
         FROM ${dbName}.classroom_assignment_questions 
         WHERE assignment_id = ? AND class_id = ?`,
        [assignment_id, class_id]
      );
      if (highestOrder[0].max_order) {
        nextQuestionOrder = highestOrder[0].max_order + 1;
      }
    }

    // Handle media upload based on question_type
    let mediaUrl = null;

    if (question_type === "image") {
      if (!media) {
        return res.status(400).json({ success: false, message: "Image file is required" });
      }
      const result = await uploadImageToS3(media.buffer);
      mediaUrl = result.path;
    }

    if (question_type === "audio") {
      if (!media) {
        return res.status(400).json({ success: false, message: "Audio file is required" });
      }
      const result = await uploadAudioToS3(media.buffer);
      mediaUrl = result.path;
    }

    // Insert the question into DB
    const [result] = await mysqlServerConnection.query(
      `INSERT INTO ${dbName}.classroom_assignment_questions 
       (assignment_id, class_id, question_text, response_type, marks, question_order, media_url, question_type) 
       VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        assignment_id,
        class_id,
        question_text,
        response_type,
        marks || 0,
        nextQuestionOrder,
        mediaUrl,
        question_type
      ]
    );

    if (result.affectedRows > 0) {
      return res.status(201).json({
        success: true,
        message: "Question added successfully",
        data: {
          id: result.insertId,
          assignment_id,
          class_id,
          question_text,
          response_type,
          question_type,
          question_order: nextQuestionOrder,
          media_url: mediaUrl,
          marks: marks || 0
        }
      });
    } else {
      return res.status(500).json({
        success: false,
        message: "Failed to add question",
      });
    }

  } catch (error) {
    console.error("Error adding assignment question:", error);
    return next({
      statusCode: 500,
      message: error.message || "Internal Server Error",
    });
  }
};


const updateClassroomAssignmentQuestion = async (req, res, next) => {
  console.log("------------------------------------------------------------------------------");
  try {
    const {
      question_id,
      assignment_id,
      class_id,
      question_text,
      response_type,
      marks,
      question_order,
      question_type,
      existing_media_url
    } = req.body;

    const dbName = req.user.db_name;
    const media = req.file;

    console.log("🚀 Update Request Received:");
    console.log("question_id:", question_id);
    console.log("assignment_id:", assignment_id);
    console.log("class_id:", class_id);
    console.log("question_text:", question_text);
    console.log("response_type:", response_type);
    console.log("marks:", marks);
    console.log("question_order:", question_order);
    console.log("question_type:", question_type);
    console.log("existing_media_url:", existing_media_url);
    console.log("media file:", media ? media.originalname : "No new media uploaded");

    // Validate required fields
    if (!question_id || !assignment_id || !class_id) {
      console.error("❌ Missing required fields.");
      return res.status(400).json({
        success: false,
        message: "Question ID, Assignment ID, and Class ID are required",
      });
    }

    // Check if question exists
    const [questionExists] = await mysqlServerConnection.query(
      `SELECT * FROM ${dbName}.classroom_assignment_questions 
       WHERE id = ? AND assignment_id = ? AND class_id = ?`,
      [question_id, assignment_id, class_id]
    );

    if (questionExists.length === 0) {
      console.error("❌ Question does not exist.");
      return res.status(404).json({
        success: false,
        message: "Question not found or does not belong to this assignment",
      });
    }

    const existingQuestion = questionExists[0];
    // console.log("✅ Existing question found:", existingQuestion);

    // Upload media if new file is provided
    let mediaUrl = existing_media_url || existingQuestion.media_url;
    if (media) {
      try {
        if (question_type === 'image') {
          console.log("📤 Uploading image to S3...");
          const uploaded = await uploadImageToS3(media.buffer, media.originalname);
          mediaUrl = uploaded.path;
          console.log("✅ Image uploaded to:", mediaUrl);
        } else if (question_type === 'audio') {
          console.log("📤 Uploading audio to S3...");
          const uploaded = await uploadAudioToS3(media.buffer, media.originalname);
          mediaUrl = uploaded.path;
          console.log("✅ Audio uploaded to:", mediaUrl);
        } else {
          console.warn("⚠️ Unknown question_type for media upload:", question_type);
        }
      } catch (uploadErr) {
        console.error("❌ Error uploading media:", uploadErr);
        return res.status(500).json({
          success: false,
          message: "Failed to upload new media file",
        });
      }
    }

    // Update query
    console.log("📦 Updating question in DB...");
    const [result] = await mysqlServerConnection.query(
      `UPDATE ${dbName}.classroom_assignment_questions 
       SET 
         question_text = ?, 
         response_type = ?, 
         marks = ?, 
         question_order = ?, 
         question_type = ?, 
         media_url = ?, 
         updatedAt = CURRENT_TIMESTAMP
       WHERE id = ?`,
      [
        question_text || existingQuestion.question_text,
        response_type || existingQuestion.response_type,
        marks !== undefined ? marks : existingQuestion.marks,
        question_order || existingQuestion.question_order,
        question_type || existingQuestion.question_type,
        mediaUrl,
        question_id
      ]
    );

    console.log("📊 Update result:", result);

    if (result.affectedRows > 0) {
      const [updatedQuestion] = await mysqlServerConnection.query(
        `SELECT * FROM ${dbName}.classroom_assignment_questions WHERE id = ?`,
        [question_id]
      );

      console.log("✅ Updated question:", updatedQuestion[0]);

      return res.status(200).json({
        success: true,
        message: "Question updated successfully",
        data: updatedQuestion[0],
      });
    } else {
      console.error("❌ Update failed. No rows affected.");
      return res.status(500).json({
        success: false,
        message: "Failed to update question",
      });
    }

  } catch (error) {
    console.error("❌ Exception during update:", error);
    return next({
      statusCode: 500,
      message: error.message || "Internal Server Error",
    });
  }
};


const deleteClassroomAssignmentQuestion = async (req, res, next) => {
  try {
    const { question_id, assignment_id, class_id } = req.body;
    console.log("DELETE CLASSROOM ASSIGNMENT QUESTION", req.body);
    const dbName = req.user.db_name;
    
    // Validate required fields
    if (!question_id || !assignment_id || !class_id) {
      return res.status(400).json({
        success: false,
        message: "Question ID, Assignment ID, and Class ID are required",
      });
    }

    // Check if question exists and belongs to the assignment
    const [questionExists] = await mysqlServerConnection.query(
      `SELECT id FROM ${dbName}.classroom_assignment_questions 
       WHERE id = ? AND assignment_id = ? AND class_id = ?`,
      [question_id, assignment_id, class_id]
    );

    if (questionExists.length === 0) {
      return res.status(404).json({
        success: false,
        message: "Question not found or does not belong to this assignment",
      });
    }

    // Delete the question (hard delete since there's no is_deleted field)
    const [result] = await mysqlServerConnection.query(
      `DELETE FROM ${dbName}.classroom_assignment_questions WHERE id = ?`,
      [question_id]
    );

    if (result.affectedRows > 0) {
      // First initialize the variable
      await mysqlServerConnection.query(`SET @rank := 0`);
      
      // Then update the question orders in a separate query
      await mysqlServerConnection.query(
        `UPDATE ${dbName}.classroom_assignment_questions
         SET question_order = (@rank := @rank + 1)
         WHERE assignment_id = ? AND class_id = ?
         ORDER BY question_order ASC`,
        [assignment_id, class_id]
      );

      return res.status(200).json({
        success: true,
        message: "Question deleted successfully",
      });
    } else {
      return res.status(500).json({
        success: false,
        message: "Failed to delete question",
      });
    }
  } catch (error) {
    console.error("Error deleting assignment question:", error);
    return next({
      statusCode: 500,
      message: error.message || "Internal Server Error",
    });
  }
};

const getClassroomAssignmentQuestions = async (req, res, next) => {
  try {
    const {
      assignment_id,
      class_id,
      page = 1,
      limit = 10,
      search = ""
    } = req.body;

    const dbName = req.user.db_name;
    const offset = (page - 1) * limit;

    // Validate required fields
    if (!assignment_id || !class_id) {
      return res.status(400).json({
        success: false,
        message: "Assignment ID and Class ID are required",
      });
    }

    // Check if assignment exists
    const [assignmentExists] = await mysqlServerConnection.query(
      `SELECT id, title FROM ${dbName}.classroom_assignments 
       WHERE id = ? AND class_id = ? AND is_deleted = 0`,
      [assignment_id, class_id]
    );

    if (assignmentExists.length === 0) {
      return res.status(404).json({
        success: false,
        message: "Assignment not found or has been deleted",
      });
    }

    // Base query
    let countQuery = `
      SELECT COUNT(*) as total 
      FROM ${dbName}.classroom_assignment_questions
      WHERE assignment_id = ? AND class_id = ?
    `;

    let questionsQuery = `
      SELECT 
        id,
        assignment_id,
        class_id,
        question_text,
        response_type,
        question_type,
        marks,
        question_order,
        media_url,
        createdAt,
        updatedAt
      FROM ${dbName}.classroom_assignment_questions
      WHERE assignment_id = ? AND class_id = ?
    `;

    const params = [assignment_id, class_id];

    // Apply search filter
    if (search && search.trim() !== "") {
      countQuery += ` AND question_text LIKE ?`;
      questionsQuery += ` AND question_text LIKE ?`;
      const searchPattern = `%${search}%`;
      params.push(searchPattern);
    }

    // Final query with pagination
    questionsQuery += ` ORDER BY question_order ASC LIMIT ? OFFSET ?`;
    const fullParams = [...params, Number(limit), offset];

    // Execute queries
    const [countResult] = await mysqlServerConnection.query(countQuery, params);
    const totalRecords = countResult[0].total;
    const totalPages = Math.ceil(totalRecords / limit);

    const [questions] = await mysqlServerConnection.query(questionsQuery, fullParams);

    // Total marks calculation
    const totalMarks = questions.reduce((sum, q) => sum + (q.marks || 0), 0);

    return res.status(200).json({
      success: true,
      message: "Questions retrieved successfully",
      data: {
        assignment_id,
        class_id,
        assignment_title: assignmentExists[0].title,
        questions,
        total_marks: totalMarks,
        pagination: {
          totalRecords,
          totalPages,
          currentPage: Number(page),
          limit: Number(limit)
        }
      }
    });

  } catch (error) {
    console.error("Error fetching assignment questions:", error);
    return next({
      statusCode: 500,
      message: error.message || "Internal Server Error",
    });
  }
};


const getUserClassroomAssignmentsQuestions = async (req, res, next) => {
  try {
    const { classroom_id, assignment_id, user_id } = req.body;
    const dbName = req.user.db_name;

    console.log("📥 Input:", { classroom_id, assignment_id, user_id, dbName });

    // ✅ Check if the assignment belongs to the classroom and get due_date
// ✅ Updated to prevent automatic JS Date conversion
const [assignmentRows] = await mysqlServerConnection.query(
  `SELECT id, 
          DATE_FORMAT(due_date, '%Y-%m-%dT%H:%i:%sZ') as due_date 
   FROM ${dbName}.classroom_assignments 
   WHERE id = ? AND class_id = ? AND is_deleted = 0`,
  [assignment_id, classroom_id]
);


    console.log("📌 Assignment Check Result:", assignmentRows);

    if (assignmentRows.length === 0) {
      console.warn("⚠️ Assignment not found or mismatched class.");
      return res.status(404).json({
        success: false,
        message: "Assignment not found or does not belong to the classroom",
      });
    }

    const assignmentDueDate = assignmentRows[0].due_date;

    // ✅ Fetch all questions
    const [questions] = await mysqlServerConnection.query(
      `SELECT 
        id,
        assignment_id,
        class_id,
        question_type,
        question_text,
        response_type,
        media_url,
        marks,
        question_order,
        createdAt,
        updatedAt
      FROM ${dbName}.classroom_assignment_questions
      WHERE assignment_id = ? AND class_id = ?`,
      [assignment_id, classroom_id]
    );

    console.log("📚 Questions Fetched:", questions.length);

    // ✅ Fetch submissions
    const [submissions] = await mysqlServerConnection.query(
      `SELECT 
        question_id,
        response_type AS submitted_response_type,
        answer_text,
        media_url AS submission_media_url,
        answer_status,
        is_completed
      FROM ${dbName}.classroom_assignment_submissions
      WHERE assignment_id = ? AND class_id = ? AND user_id = ? AND is_deleted = 0`,
      [assignment_id, classroom_id, user_id]
    );

    console.log("📝 Submissions Fetched:", submissions.length);

    // ✅ Create map of submissions
    const submissionMap = {};
    submissions.forEach(sub => {
      submissionMap[sub.question_id] = sub;
    });

    console.log("📌 Submission Map Created");

    // ✅ Attach submission to each question
    const questionsWithSubmissions = questions.map(q => {
      const submission = submissionMap[q.id] || {};
      return {
        ...q,
        submitted_response_type: submission.submitted_response_type || null,
        answer_text: submission.answer_text || null,
        submission_media_url: submission.submission_media_url || null,
        answer_status: submission.answer_status ?? 'pending',
        is_completed: submission.is_completed || 0
      };
    });

    console.log("✅ Final Questions with Submissions:", questionsWithSubmissions.length);

    // ✅ Send response with due_date at top level
    return res.status(200).json({
      success: true,
      message: 'Questions fetched successfully',
      due_date: assignmentDueDate,
      data: questionsWithSubmissions
    });

  } catch (error) {
    console.error("❌ Error fetching questions with submissions:", error);
    return next({
      statusCode: 500,
      message: error.message || "Internal Server Error",
    });
  }
};




const submitUserClassroomAssignmentResponse = async (req, res, next) => {
  try {
    const {
      classroom_id,
      assignment_id,
      question_id,
      user_id,
      response_type,
      answer_text = ''
    } = req.body;

    const dbName = req.user.db_name;
    const file = req.file;

    console.log("Request Body:", req.body);
    console.log("Database Name:", dbName);
    console.log("File (if any):", file);

    // Validate classroom
    const [classroomRows] = await mysqlServerConnection.query(
      `SELECT id FROM ${dbName}.classroom WHERE id = ? AND is_deleted = 0`,
      [classroom_id]
    );
    console.log("Classroom Rows:", classroomRows);
    if (classroomRows.length === 0) {
      return res.status(404).json({ success: false, message: 'Classroom not found or deleted' });
    }

    // Validate assignment
    const [assignmentRows] = await mysqlServerConnection.query(
      `SELECT id FROM ${dbName}.classroom_assignments WHERE id = ? AND class_id = ? AND is_deleted = 0`,
      [assignment_id, classroom_id]
    );
    console.log("Assignment Rows:", assignmentRows);
    if (assignmentRows.length === 0) {
      return res.status(404).json({ success: false, message: 'Assignment not found for classroom' });
    }

    // Validate question
    const [questionRows] = await mysqlServerConnection.query(
      `SELECT id FROM ${dbName}.classroom_assignment_questions WHERE id = ? AND assignment_id = ? AND class_id = ?`,
      [question_id, assignment_id, classroom_id]
    );
    console.log("Question Rows:", questionRows);
    if (questionRows.length === 0) {
      return res.status(404).json({ success: false, message: 'Question not found for assignment/classroom' });
    }

    // Validate user
    const [userRows] = await mysqlServerConnection.query(
      `SELECT id FROM ${dbName}.users WHERE id = ? AND is_deleted = 0`,
      [user_id]
    );
    console.log("User Rows:", userRows);
    if (userRows.length === 0) {
      return res.status(404).json({ success: false, message: 'User not found or deleted' });
    }

    // Upload to S3 if media response
    let media_url = null;

    if (response_type === 'image_response' || response_type === 'audio_response') {
      console.log("Media response detected. Response Type:", response_type);
      if (!file) {
        return res.status(400).json({
          success: false,
          message: 'Media file is required for this response type'
        });
      }

      let uploadResult;
      if (response_type === 'image_response') {
        console.log("Uploading image to S3...");
        uploadResult = await uploadImageToS3(file.buffer);
      } else {
        console.log("Uploading audio to S3...");
        uploadResult = await uploadAudioToS3(file.buffer);
      }

      console.log("Upload Result:", uploadResult);

      if (!uploadResult || !uploadResult.path) {
        return res.status(500).json({ success: false, message: 'Failed to upload media to S3' });
      }

      console.log("Media URL--------------------:", uploadResult.path);

      media_url = uploadResult.path;
    }

    // Insert submission
    console.log("Inserting response into classroom_assignment_submissions...");
    const [result] = await mysqlServerConnection.query(
      `INSERT INTO ${dbName}.classroom_assignment_submissions (
          assignment_id, user_id, class_id, question_id, response_type,
          answer_text, media_url, is_completed, submittedAt, is_deleted
        ) VALUES (?, ?, ?, ?, ?, ?, ?, 1, NOW(), 0)`,
      [
        assignment_id,
        user_id,
        classroom_id,
        question_id,
        response_type,
        response_type === 'text_response' ? answer_text : '',
        media_url
      ]
    );

    console.log("Insert Result:", result);

    return res.status(201).json({
      success: true,
      message: 'Response submitted successfully',
      submission_id: result.insertId
    });

  } catch (error) {
    console.error('Error submitting assignment response:', error);
    return next({
      statusCode: 500,
      message: error.message || 'Internal Server Error'
    });
  }
};


// const getClassroomAssignmentSubmissions = async (req, res, next) => {
//   try {
//     const { class_id, assignment_id, page = 1, limit = 10, search = "" } = req.body;
//     const dbName = req.user.db_name;
//     const offset = (page - 1) * limit;

//     // Validate required fields
//     if (!class_id || !assignment_id) {
//       return res.status(400).json({
//         success: false,
//         message: "Class ID and Assignment ID are required",
//       });
//     }

//     // Check if assignment exists
//     const [assignmentExists] = await mysqlServerConnection.query(
//       `SELECT id, title, total_marks FROM ${dbName}.classroom_assignments 
//        WHERE id = ? AND class_id = ? AND is_deleted = 0`,
//       [assignment_id, class_id]
//     );

//     if (assignmentExists.length === 0) {
//       return res.status(404).json({
//         success: false,
//         message: "Assignment not found or has been deleted",
//       });
//     }

//     const assignment = assignmentExists[0];

//     // Build the query with search condition
//     let countQuery = `
//       SELECT COUNT(*) as total 
//       FROM ${dbName}.classroom_assignment_submissions cas
//       JOIN ${dbName}.users u ON cas.user_id = u.id
//       WHERE cas.assignment_id = ? 
//         AND cas.class_id = ? 
//         AND cas.is_deleted = 0
//     `;
    
//     let submissionsQuery = `
//       SELECT 
//         cas.id, 
//         cas.user_id,
//         cas.submission_url,
//         cas.marks_obtained,
//         cas.assignment_evaluation_status,
//         cas.submittedAt,
//         cas.assignment_feedback,
//         u.name as student_name,
//         u.email as student_email,
//         ca.total_marks,
//         CASE 
//           WHEN cas.assignment_evaluation_status = 1 THEN 'Graded'
//           ELSE 'Pending' 
//         END as grading_status
//       FROM ${dbName}.classroom_assignment_submissions cas
//       JOIN ${dbName}.users u ON cas.user_id = u.id
//       JOIN ${dbName}.classroom_assignments ca ON cas.assignment_id = ca.id
//       WHERE cas.assignment_id = ? 
//         AND cas.class_id = ? 
//         AND cas.is_deleted = 0
//     `;

//     const params = [assignment_id, class_id];

//     // Add search condition if provided
//     if (search && search.trim() !== "") {
//       countQuery += ` AND (u.name LIKE ? OR u.email LIKE ?)`;
//       submissionsQuery += ` AND (u.name LIKE ? OR u.email LIKE ?)`;
      
//       const searchPattern = `%${search}%`;
//       params.push(searchPattern, searchPattern);
//     }

//     // Add sorting and pagination
//     submissionsQuery += ` ORDER BY cas.submittedAt DESC LIMIT ? OFFSET ?`;
//     const fullParams = [...params, Number(limit), offset];

//     // Execute count query
//     const [countResult] = await mysqlServerConnection.query(countQuery, params);
//     const totalRecords = countResult[0].total;
//     const totalPages = Math.ceil(totalRecords / limit);

//     // Execute submissions query
//     const [submissions] = await mysqlServerConnection.query(submissionsQuery, fullParams);

//     // Get class enrollment count for context
//     const [enrollmentCount] = await mysqlServerConnection.query(
//       `SELECT COUNT(*) as total_students
//        FROM ${dbName}.classroom_trainee
//        WHERE class_id = ?`,
//       [class_id]
//     );

//     const totalStudents = enrollmentCount[0].total_students;
//     const submissionRate = totalStudents > 0 ? 
//       Math.round((totalRecords / totalStudents) * 100) : 0;

//     // Calculate average score for submitted assignments
//     let averageScore = 0;
//     let gradedCount = 0;

//     submissions.forEach(submission => {
//       if (submission.assignment_evaluation_status && submission.marks_obtained !== null) {
//         averageScore += submission.marks_obtained;
//         gradedCount++;
//       }
      
//       // Format date for better readability
//       submission.submittedAt = submission.submittedAt ? 
//         new Date(submission.submittedAt).toISOString() : null;
//     });

//     averageScore = gradedCount > 0 ? (averageScore / gradedCount).toFixed(2) : 0;

//     // Send response
//     return res.status(200).json({
//       success: true,
//       message: "Assignment submissions retrieved successfully",
//       data: {
//         assignment: {
//           id: assignment.id,
//           title: assignment.title,
//           total_marks: assignment.total_marks
//         },
//         submissions,
//         stats: {
//           total_submissions: totalRecords,
//           total_students: totalStudents,
//           submission_rate: `${submissionRate}%`,
//           average_score: averageScore,
//           total_graded: gradedCount
//         },
//         pagination: {
//           totalRecords,
//           totalPages,
//           currentPage: Number(page),
//           limit: Number(limit)
//         }
//       }
//     });
//   } catch (error) {
//     console.error("Error fetching assignment submissions:", error);
//     return next({
//       statusCode: 500,
//       message: error.message || "Internal Server Error",
//     });
//   }
// }


const getClassroomAssignmentSubmissions = async (req, res, next) => {
  try {
    const { assignment_id, page = 1, limit = 10, search = "" } = req.body;
    const dbName = req.user.db_name;

    if (!assignment_id) {
      return res.status(400).json({
        success: false,
        message: "Assignment ID is required",
      });
    }

    // ✅ Step 0: Get assignment and class info
    const [assignmentResult] = await mysqlServerConnection.query(
      `SELECT id, class_id, title, total_marks FROM ${dbName}.classroom_assignments 
       WHERE id = ? AND is_deleted = 0`,
      [assignment_id]
    );

    if (assignmentResult.length === 0) {
      return res.status(404).json({
        success: false,
        message: "Assignment not found or has been deleted",
      });
    }

    const assignment = assignmentResult[0];
    const class_id = assignment.class_id;

    // ✅ Step 1: Check if the classroom exists
    const [classroomRows] = await mysqlServerConnection.query(
      `SELECT id FROM ${dbName}.classroom 
       WHERE id = ? AND is_deleted = 0`,
      [class_id]
    );

    if (classroomRows.length === 0) {
      return res.status(404).json({
        success: false,
        message: "Classroom not found or has been deleted",
      });
    }

    // ✅ Step 2: Get total number of questions in the assignment
    const [questionCountResult] = await mysqlServerConnection.query(
      `SELECT COUNT(*) AS total_questions 
       FROM ${dbName}.classroom_assignment_questions 
       WHERE assignment_id = ? AND class_id = ?`,
      [assignment_id, class_id]
    );

    const totalQuestions = questionCountResult[0].total_questions || 0;

    // ✅ Step 3: Fetch all submission details
    const [rawSubmissions] = await mysqlServerConnection.query(
      `SELECT
        u.id AS user_id,
        u.name AS user_name,
        u.email,
        cas.question_id,
        caq.question_type,
        caq.question_text,
        caq.media_url AS question_media_url,
        cas.response_type,
        cas.answer_text,
        cas.media_url AS submission_media_url,
        cas.answer_status,
        cas.submittedAt
      FROM ${dbName}.classroom_assignment_submissions cas
      JOIN ${dbName}.users u ON u.id = cas.user_id
      JOIN ${dbName}.classroom_assignment_questions caq ON caq.id = cas.question_id
      WHERE cas.assignment_id = ? AND cas.is_deleted = 0
      ORDER BY u.id, cas.question_id`,
      [assignment_id]
    );

    // ✅ Step 4: Filter by search
    const filtered = rawSubmissions.filter(entry =>
      entry.user_name.toLowerCase().includes(search.toLowerCase()) ||
      entry.email.toLowerCase().includes(search.toLowerCase())
    );

    // ✅ Step 5: Group submissions by user and calculate question stats
    const groupedMap = {};

    filtered.forEach(row => {
      const {
        user_id, user_name, email,
        question_id, question_type, question_text, question_media_url,
        response_type, answer_text, submission_media_url, submittedAt, answer_status
      } = row;

      if (!groupedMap[user_id]) {
        groupedMap[user_id] = {
          user_id,
          user_name,
          email,
          answers: [],
          attended_question_ids: new Set()
        };
      }

      groupedMap[user_id].answers.push({
        question_id,
        question_type,
        question_text,
        question_media_url,
        response_type,
        answer_text,
        submission_media_url,
        submittedAt,
        answer_status
      });

      groupedMap[user_id].attended_question_ids.add(question_id);
    });

    const groupedList = Object.values(groupedMap).map(user => {
      const attended = user.attended_question_ids.size;
      return {
        user_id: user.user_id,
        user_name: user.user_name,
        email: user.email,
        total_questions: totalQuestions,
        attended_questions: attended,
        remaining_questions: totalQuestions - attended,
        answers: user.answers
      };
    });

    // ✅ Step 6: Apply pagination
    const totalRecords = groupedList.length;
    const totalPages = Math.ceil(totalRecords / limit);
    const offset = (page - 1) * limit;
    const paginated = groupedList.slice(offset, offset + limit);

    // ✅ Step 7: Final response
    // console.log("📦 Final Paginated Submission Data:", JSON.stringify(paginated, null, 2));

    return res.status(200).json({
      success: true,
      message: "Submission details fetched successfully",
      assignment: {
        id: assignment.id,
        title: assignment.title,
        total_marks: assignment.total_marks
      },
      pagination: {
        currentPage: Number(page),
        limit: Number(limit),
        totalPages,
        totalRecords
      },
      data: paginated
    });

  } catch (error) {
    console.error("🔥 Error in getAssignmentSubmissionDetails:", error);
    return next({
      statusCode: 500,
      message: error.message || "Internal Server Error"
    });
  }
};

const userAssignmentStatus = async (req, res, next) => {
  try {
    const { class_id, assignment_id, question_id, user_id, answer_status } = req.body;
    const dbName = req.user.db_name;
    // console.log("🚀 ~ userAssignmentStatus ~ req.body:", req.body);

    // Validate required fields
    if (!class_id || !assignment_id || !question_id || !user_id || !answer_status) {
      return res.status(400).json({
        success: false,
        message: "All fields (class_id, assignment_id, question_id, user_id, answer_status) are required",
      });
    }

    // Validate allowed statuses
    const validStatuses = ['pending', 'right', 'wrong'];
    if (!validStatuses.includes(answer_status)) {
      return res.status(400).json({
        success: false,
        message: "Invalid answer_status value. Must be 'pending', 'right', or 'wrong'",
      });
    }

    // Update the status in the database
    const [updateResult] = await mysqlServerConnection.query(
      `UPDATE ${dbName}.classroom_assignment_submissions
       SET answer_status = ?
       WHERE class_id = ? AND assignment_id = ? AND question_id = ? AND user_id = ?`,
      [answer_status, class_id, assignment_id, question_id, user_id]
    );

    if (updateResult.affectedRows === 0) {
      return res.status(404).json({
        success: false,
        message: "Answer not found for the given parameters",
      });
      
    }

    // console.log("🚀 ~ userAssignmentStatus ~ updateResult:", updateResult);

    return res.status(200).json({
      success: true,
      message: `Answer status updated.`,
    });
  } catch (error) {
    console.error("Error updating answer status:", error);
    return res.status(500).json({
      success: false,
      message: "Internal server error",
    });
  }
};



module.exports = {
  createClassroomAssignment,
  updateClassroomAssignment,
  deleteClassroomAssignment,
  getClassroomAssignments,
  getClassroomAssignmentById,
  submitClassroomAssignment,
  getUserClassroomAssignments,
  gradeClassroomAssignment,
  getClassroomAssignmentSubmissions,
  addClassroomAssignmentQuestion,
  updateClassroomAssignmentQuestion,
  deleteClassroomAssignmentQuestion,
  getClassroomAssignmentQuestions,
  getUserClassroomAssignmentsQuestions,
  submitUserClassroomAssignmentResponse,
  userAssignmentStatus
};