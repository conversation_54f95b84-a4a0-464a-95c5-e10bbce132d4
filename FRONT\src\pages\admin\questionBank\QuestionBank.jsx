import React, { useState, useEffect } from 'react'
import { Icon } from '@iconify/react';
import { Modal } from 'react-bootstrap';
import { toast } from 'react-toastify';
import { getTags , createQuestionBank , getQuestionBank, updateQuestionBank, deleteQuestionBank, getQuestionBankById , deleteQuestionTag } from '../../../services/adminService';
import { usePermissions } from '../../../context/PermissionsContext';

function QuestionBank() {
  const { permissions } = usePermissions();
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  // Data states
  const [questions, setQuestions] = useState([]);
  const [loading, setLoading] = useState(false);
  const [availableTags, setAvailableTags] = useState([]);

  // Modal states
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  // Form states
  const [formData, setFormData] = useState({
    question: '',
    questionType: 'MCQ',
    categories: [],
    options: [
      { text: '', isCorrect: false },
      { text: '', isCorrect: false },
      { text: '', isCorrect: false },
      { text: '', isCorrect: false }
    ]
  });

  const [editFormData, setEditFormData] = useState({
    id: '',
    question: '',
    questionType: 'MCQ',
    categories: [],
    options: []
  });

  // Category input state
  const [categoryInput, setCategoryInput] = useState('');
  const [showCategoryDropdown, setShowCategoryDropdown] = useState(false);

  // Form errors
  const [formErrors, setFormErrors] = useState({});

  // Selected question for delete
  const [selectedQuestion, setSelectedQuestion] = useState(null);

  // Tag deletion states
  const [showDeleteTagModal, setShowDeleteTagModal] = useState(false);
  const [selectedTagToDelete, setSelectedTagToDelete] = useState(null);
  const [isDeletingTag, setIsDeletingTag] = useState(false);

  // Modal handlers
  const handleModalClose = () => {
    setShowAddModal(false);
    setFormErrors({});
    setFormData({
      question: '',
      questionType: 'MCQ',
      categories: [],
      options: [
        { text: '', isCorrect: false },
        { text: '', isCorrect: false },
        { text: '', isCorrect: false },
        { text: '', isCorrect: false }
      ]
    });
    setCategoryInput('');
    setShowCategoryDropdown(false);
  };

  const handleEditModalClose = () => {
    setShowEditModal(false);
    setFormErrors({});
    setEditFormData({
      id: '',
      question: '',
      questionType: 'MCQ',
      categories: [],
      options: []
    });
    setCategoryInput('');
    setShowCategoryDropdown(false);
  };

  // Form input handlers
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    // Clear error when user types
    if (formErrors[name]) {
      setFormErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  };

  // Category handlers
  const handleCategoryKeyDown = (e) => {
    if (e.key === 'Enter' && categoryInput.trim()) {
      e.preventDefault();
      if (!formData.categories.includes(categoryInput.trim())) {
        setFormData(prev => ({
          ...prev,
          categories: [...prev.categories, categoryInput.trim()]
        }));
      }
      setCategoryInput('');
    }
  };

  const removeCategory = (categoryToRemove) => {
    setFormData(prev => ({
      ...prev,
      categories: prev.categories.filter(cat => cat !== categoryToRemove)
    }));
  };

  // Select category from dropdown
  const selectCategory = (categoryName) => {
    if (!formData.categories.includes(categoryName)) {
      setFormData(prev => ({
        ...prev,
        categories: [...prev.categories, categoryName]
      }));
    }
    setCategoryInput('');
    setShowCategoryDropdown(false);
  };

  // Add new category
  const addNewCategory = () => {
    if (categoryInput.trim() && !formData.categories.includes(categoryInput.trim())) {
      setFormData(prev => ({
        ...prev,
        categories: [...prev.categories, categoryInput.trim()]
      }));
    }
    setCategoryInput('');
    setShowCategoryDropdown(false);
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (!event.target.closest('.position-relative')) {
        setShowCategoryDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Option handlers
  const handleOptionChange = (index, value) => {
    setFormData(prev => ({
      ...prev,
      options: prev.options.map((option, idx) =>
        idx === index ? { ...option, text: value } : option
      )
    }));
  };

  const handleCorrectAnswerChange = (index) => {
    setFormData(prev => ({
      ...prev,
      options: prev.options.map((option, idx) => ({
        ...option,
        isCorrect: idx === index
      }))
    }));
  };

  const addOption = () => {
    setFormData(prev => ({
      ...prev,
      options: [...prev.options, { text: '', isCorrect: false }]
    }));
  };

  const removeOption = (index) => {
    if (formData.options.length <= 2) {
      toast.warn('At least 2 options are required');
      return;
    }

    setFormData(prev => {
      const newOptions = prev.options.filter((_, idx) => idx !== index);
      // If we removed the correct answer, clear all correct flags
      const removedCorrect = prev.options[index].isCorrect;
      if (removedCorrect) {
        return {
          ...prev,
          options: newOptions.map(opt => ({ ...opt, isCorrect: false }))
        };
      }
      return {
        ...prev,
        options: newOptions
      };
    });
  };

  // Validation
  const validateForm = () => {
    const errors = {};

    if (!formData.question.trim()) {
      errors.question = 'Question is required';
    }

    if (formData.categories.length === 0) {
      errors.categories = 'At least one category is required';
    }

    const filledOptions = formData.options.filter(opt => opt.text.trim());
    if (filledOptions.length < 2) {
      errors.options = 'At least 2 options are required';
    }

    const hasCorrectAnswer = formData.options.some(opt => opt.isCorrect && opt.text.trim());
    if (!hasCorrectAnswer) {
      errors.correctAnswer = 'Please select a correct answer';
    }

    return errors;
  };

  // Submit handler
  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!permissions.question_bank_management_create) {
      toast.warning("You don't have permission to create questions");
      return;
    }

    const errors = validateForm();
    if (Object.keys(errors).length > 0) {
      setFormErrors(errors);
      return;
    }

    setIsSubmitting(true);

    try {
      // Transform form data to API format
      const apiData = {
        question_name: formData.question,
        question_type: formData.questionType.toLowerCase(),
        options: formData.options
          .filter(option => option.text.trim()) // Only include filled options
          .map((option, index) => ({
            option_number: index + 1,
            option_value: option.text.trim(),
            is_correct: option.isCorrect
          })),
        subject: "", // Empty as per existing pattern
        modules: "", // Empty as per existing pattern
        tags: formData.categories // Pass the selected categories as tags
      };

      console.log('Question data to save:', apiData);

      const response = await createQuestionBank(apiData);

      if (response.success) {
        toast.success(response.message || 'Question added successfully!');
        handleModalClose();
        fetchQuestions(currentPage, searchTerm); // Refresh questions list
        fetchTags(); // Refresh tags to get newly created tags
      } else {
        toast.error(response.message || 'Failed to add question');
      }

    } catch (error) {
      console.error('Error adding question:', error);
      toast.error('Failed to add question. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Fetch questions from API
  const fetchQuestions = async (page = 1, search = '') => {
    setLoading(true);
    try {
      const payload = {
        page: page,
        limit: itemsPerPage,
        search: search
      };

      const response = await getQuestionBank(payload);
      console.log('Questions API Response:', response);

      if (response.success) {
        // Handle different response structures
        let questionsData = [];
        if (response.data && response.data.questions) {
          questionsData = response.data.questions;
        } else if (response.questions) {
          questionsData = response.questions;
        } else if (Array.isArray(response.data)) {
          questionsData = response.data;
        }

        setQuestions(questionsData);
      } else {
        toast.error(response.message || "Failed to fetch questions");
      }
    } catch (error) {
      console.error("Error fetching questions:", error);
      toast.error("An error occurred while fetching questions");
    } finally {
      setLoading(false);
    }
  };

  // Fetch available tags from API
  const fetchTags = async () => {
    try {
      const response = await getTags();
      console.log('Tags API Response:', response);

      if (response.success && response.data) {
        setAvailableTags(response.data);
      } else {
        console.error("Failed to fetch tags:", response);
      }
    } catch (error) {
      console.error("Error fetching tags:", error);
    }
  };

  // Initialize data on component mount
  useEffect(() => {
    fetchQuestions(1, '');
    fetchTags();
  }, [itemsPerPage]);

  // Handle search
  const handleSearch = (e) => {
    const value = e.target.value;
    setSearchTerm(value);
    setCurrentPage(1);
    fetchQuestions(1, value);
  };

  // Handle edit question
  const handleEditQuestion = (question) => {
    if (!permissions.question_bank_management_edit) {
      toast.warning("You don't have permission to edit questions");
      return;
    }
    // Transform API data to form format
    setEditFormData({
      id: question.id,
      question: question.question_name,
      questionType: question.question_type.toUpperCase(),
      categories: question.tags || [],
      options: question.options.map(opt => ({
        text: opt.option_value,
        isCorrect: opt.is_correct
      }))
    });
    setShowEditModal(true);
  };

  // Handle delete question
  const handleDeleteQuestion = (question) => {
    if (!permissions.question_bank_management_delete) {
      toast.warning("You don't have permission to delete questions");
      return;
    }
    setSelectedQuestion(question);
    setShowDeleteModal(true);
  };

  // Confirm delete question
  const confirmDeleteQuestion = async () => {
    if (!selectedQuestion) return;

    setIsDeleting(true);
    try {
      const response = await deleteQuestionBank({ question_id: selectedQuestion.id });

      if (response.success) {
        toast.success(response.message || 'Question deleted successfully');
        setShowDeleteModal(false);
        setSelectedQuestion(null);
        fetchQuestions(currentPage, searchTerm);
      } else {
        toast.error(response.message || 'Failed to delete question');
      }
    } catch (error) {
      console.error('Error deleting question:', error);
      toast.error('Failed to delete question. Please try again.');
    } finally {
      setIsDeleting(false);
    }
  };

  // Handle edit submit
  const handleEditSubmit = async (e) => {
    e.preventDefault();

    // Validate edit form data
    const errors = validateEditForm();
    if (Object.keys(errors).length > 0) {
      setFormErrors(errors);
      return;
    }

    setIsEditing(true);

    try {
      // Transform edit form data to API format
      const apiData = {
        question_id: editFormData.id,
        question_name: editFormData.question,
        question_type: editFormData.questionType.toLowerCase(),
        options: editFormData.options
          .filter(option => option.text.trim()) // Only include filled options
          .map((option, index) => ({
            option_number: index + 1,
            option_value: option.text.trim(),
            is_correct: option.isCorrect
          })),
        subject: "", // Empty as per existing pattern
        modules: "", // Empty as per existing pattern
        tags: editFormData.categories, // Pass the selected categories as tags
        is_active: true
      };

      console.log('Edit question data to save:', apiData);

      const response = await updateQuestionBank(apiData);

      if (response.success) {
        toast.success(response.message || 'Question updated successfully!');
        handleEditModalClose();
        fetchQuestions(currentPage, searchTerm); // Refresh questions list
        fetchTags(); // Refresh tags to get newly created tags
      } else {
        toast.error(response.message || 'Failed to update question');
      }

    } catch (error) {
      console.error('Error updating question:', error);
      toast.error('Failed to update question. Please try again.');
    } finally {
      setIsEditing(false);
    }
  };

  // Validate edit form
  const validateEditForm = () => {
    const errors = {};

    if (!editFormData.question.trim()) {
      errors.question = 'Question is required';
    }

    if (editFormData.categories.length === 0) {
      errors.categories = 'At least one category is required';
    }

    const filledOptions = editFormData.options.filter(opt => opt.text.trim());
    if (filledOptions.length < 2) {
      errors.options = 'At least 2 options are required';
    }

    const hasCorrectAnswer = editFormData.options.some(opt => opt.isCorrect && opt.text.trim());
    if (!hasCorrectAnswer) {
      errors.correctAnswer = 'Please select a correct answer';
    }

    return errors;
  };

  // Handle tag deletion
  const handleDeleteTag = (tag) => {
    setSelectedTagToDelete(tag);
    setShowDeleteTagModal(true);
  };

  // Confirm tag deletion
  const confirmDeleteTag = async () => {
    if (!selectedTagToDelete) return;

    setIsDeletingTag(true);
    try {
      const response = await deleteQuestionTag({ id: selectedTagToDelete.id });

      if (response.success) {
        toast.success(response.message || `Tag "${selectedTagToDelete.tag_name}" deleted successfully`);
        setShowDeleteTagModal(false);
        setSelectedTagToDelete(null);

        // Refresh both tags and questions since questions might be affected
        fetchTags();
        fetchQuestions(currentPage, searchTerm);
      } else {
        toast.error(response.message || 'Failed to delete tag');
      }
    } catch (error) {
      console.error('Error deleting tag:', error);
      toast.error('Failed to delete tag. Please try again.');
    } finally {
      setIsDeletingTag(false);
    }
  };

  return (
    <>
      {/* search and add question */}
      <div className="row mb-3">
        <div className="col-12">
          <div className="row">
            <div className="col-12 col-md-6">
              <input
                type="text"
                className="form-control"
                placeholder="Search questions..."
                style={{ maxWidth: '300px' }}
                value={searchTerm}
                onChange={handleSearch}
              />
            </div>
            <div className="col-12 col-md-6 d-flex justify-content-end">
              <button
                className="btn btn-primary btn-sm d-flex align-items-center gap-1"
                style={{ width: '250px' }}
                onClick={() => {
                  if (!permissions.question_bank_management_create) {
                    toast.warning("You don't have permission to create questions");
                    return;
                  }
                  setShowAddModal(true);
                }}
                disabled={!permissions.question_bank_management_create}
              >
                <Icon icon="fluent:add-24-regular" width="16" height="16" />
                Add Question
              </button>
            </div>
          </div>
        </div>
      </div> 

      {/* table */}
      <div className="row mb-3">
        <div className="col-12">
          <div className="card">
            {loading ? (
              <div className="text-center my-5">
                <div className="spinner-border" role="status">
                  <span className="visually-hidden">Loading...</span>
                </div>
              </div>
            ) : (
              <div className="table-responsive">
                <table className="table table-borderless mb-0">
                  <thead>
                    <tr>
                      <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500', width: '80px' }}>SL NO.</th>
                      <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500', width: '300px' }}>Question</th>
                      <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500', width: '100px' }}>Type</th>
                      <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500', width: '250px' }}>Options</th>
                      <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500', width: '150px' }}>Tags</th>
                      <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500', width: '120px' }}>Created At</th>
                      <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500', width: '100px' }}>Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {questions.length === 0 ? (
                      <tr>
                        <td colSpan="7" className="text-center py-5">
                          <div className="text-muted">
                            <Icon icon="fluent:document-search-24-regular" width="48" height="48" className="mb-3" />
                            <p>No questions found</p>
                          </div>
                        </td>
                      </tr>
                    ) : (
                      questions.map((question, index) => (
                        <tr key={question.id} className="border-bottom">
                          <td className="py-3 align-middle">{(currentPage - 1) * itemsPerPage + index + 1}</td>
                          <td className="py-3 align-middle">{question.question_name}</td>
                          <td className="py-3 align-middle">
                            <span className="badge bg-primary">{question.question_type?.toUpperCase()}</span>
                          </td>
                          <td className="py-3 align-middle">
                            <div className="d-flex flex-column gap-1">
                              {question.options?.slice(0, 3).map((option, idx) => (
                                <div
                                  key={idx}
                                  className={`small p-2 rounded ${option.is_correct ? 'bg-success-subtle' : 'bg-light'}`}
                                >
                                  {option.option_value}
                                  {option.is_correct && (
                                    <Icon icon="fluent:checkmark-circle-24-filled" className="ms-2 text-success" width="16" height="16" />
                                  )}
                                </div>
                              ))}
                              {question.options?.length > 3 && (
                                <small className="text-muted">+{question.options.length - 3} more</small>
                              )}
                            </div>
                          </td>
                          <td className="py-3 align-middle">
                            <div className="d-flex flex-wrap gap-1">
                              {question.tags?.slice(0, 2).map((tag, idx) => (
                                <span key={idx} className="badge bg-info">{tag}</span>
                              ))}
                              {question.tags?.length > 2 && (
                                <span className="badge bg-secondary">+{question.tags.length - 2}</span>
                              )}
                            </div>
                          </td>
                          <td className="py-3 align-middle">
                            {new Date(question.created_at).toLocaleDateString()}
                          </td>
                          <td className="py-3 align-middle">
                            <div className="d-flex gap-2">
                              <button
                                className="btn btn-outline-dark btn-sm border border-dark"
                                style={{ width: '36px', height: '36px' }}
                                title={permissions.question_bank_management_edit ? "Edit" : "You don't have permission to edit"}
                                onClick={() => handleEditQuestion(question)}
                                disabled={!permissions.question_bank_management_edit}
                              >
                                <Icon icon="fluent:edit-24-regular" width="16" height="16" />
                              </button>
                              <button
                                className="btn btn-outline-danger btn-sm border border-danger"
                                style={{ width: '36px', height: '36px' }}
                                title={permissions.question_bank_management_delete ? "Delete" : "You don't have permission to delete"}
                                onClick={() => handleDeleteQuestion(question)}
                                disabled={!permissions.question_bank_management_delete}
                              >
                                <Icon icon="fluent:delete-24-regular" width="16" height="16" />
                              </button>
                            </div>
                          </td>
                        </tr>
                      ))
                    )}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* record per page , total record , pagination */}
      <div className="row">
        <div className="col-md-6 d-flex align-items-center gap-2">
          <select
            className="form-select"
            style={{ width: 'auto' }}
            value={itemsPerPage}
            onChange={(e) => setItemsPerPage(Number(e.target.value))}
          >
            <option value={10}>10 records per page</option>
            <option value={25}>25 records per page</option>
            <option value={50}>50 records per page</option>
          </select>
          <span className="text-muted">
            Total Records: {questions.length}
          </span>
        </div>
        <div className="col-md-6 d-flex justify-content-end align-items-center gap-3">
          <button
            className="btn btn-primary"
            style={{ width: '150px' }}
            onClick={() => {
              const newPage = Math.max(currentPage - 1, 1);
              setCurrentPage(newPage);
              fetchQuestions(newPage, searchTerm);
            }}
            disabled={currentPage === 1 || loading}
          >
            Prev
          </button>
          <span>Page {currentPage} of {Math.max(1, Math.ceil(questions.length / itemsPerPage))}</span>
          <button
            className="btn btn-primary"
            style={{ width: '150px' }}
            onClick={() => {
              const totalPages = Math.ceil(questions.length / itemsPerPage);
              const newPage = Math.min(currentPage + 1, totalPages);
              setCurrentPage(newPage);
              fetchQuestions(newPage, searchTerm);
            }}
            disabled={currentPage >= Math.ceil(questions.length / itemsPerPage) || loading}
          >
            Next
          </button>
        </div>
      </div>

      {/* Add Question Modal */}
      <Modal show={showAddModal} onHide={handleModalClose} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>Add New Question</Modal.Title>
        </Modal.Header>
        <Modal.Body style={{ maxHeight: '70vh', overflowY: 'auto', paddingBottom: '0' }}>
          {!permissions.question_bank_management_create ? (
            <div className="alert alert-warning">
              <Icon icon="mdi:warning" className="me-2" />
              You don't have permission to create new questions.
            </div>
          ) : (
            <form onSubmit={handleSubmit}>
              {/* Question Input */}
              <div className="mb-3">
                <label className="form-label">Enter Question <span className="text-danger">*</span></label>
                <textarea
                  className={`form-control ${formErrors.question ? 'is-invalid' : ''}`}
                  name="question"
                  value={formData.question}
                  onChange={handleInputChange}
                  placeholder="Enter your question here..."
                  rows="3"
                />
                {formErrors.question && (
                  <div className="invalid-feedback">{formErrors.question}</div>
                )}
              </div>

              {/* Question Type */}
              <div className="mb-3">
                <label className="form-label">Question Type</label>
                <select
                  className="form-select"
                  name="questionType"
                  value={formData.questionType}
                  onChange={handleInputChange}
                  disabled
                >
                  <option value="MCQ">Multiple Choice Question (MCQ)</option>
                </select>
                <div className="form-text text-muted">
                  Currently only MCQ type is supported
                </div>
              </div>

              {/* Categories */}
              <div className="mb-3">
                <label className="form-label">Categories <span className="text-danger">*</span></label>

                {/* Dropdown for available tags */}
                <div className="position-relative">
                  <input
                    type="text"
                    className={`form-control ${formErrors.categories ? 'is-invalid' : ''}`}
                    value={categoryInput}
                    onChange={(e) => setCategoryInput(e.target.value)}
                    onKeyDown={handleCategoryKeyDown}
                    placeholder="Type to search categories or add new..."
                    onFocus={() => setShowCategoryDropdown(true)}
                  />

                  {/* Dropdown with available tags */}
                  {showCategoryDropdown && (
                    <div className="dropdown-menu show w-100" style={{ maxHeight: '200px', overflowY: 'auto', zIndex: 1050 }}>
                      {/* Filtered available tags */}
                      {availableTags
                        .filter(tag =>
                          tag.tag_name.toLowerCase().includes(categoryInput.toLowerCase()) &&
                          !formData.categories.includes(tag.tag_name)
                        )
                        .map(tag => (
                          <div
                            key={tag.id}
                            className="dropdown-item d-flex justify-content-between align-items-center"
                            style={{ cursor: 'pointer', padding: '8px 12px' }}
                          >
                            <span
                              onClick={() => selectCategory(tag.tag_name)}
                              style={{ flex: 1 }}
                            >
                              {tag.tag_name}
                            </span>
                            <button
                              type="button"
                              className="btn btn-sm text-danger"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleDeleteTag(tag);
                              }}
                              title="Delete tag permanently"
                              style={{
                                width: '20px',
                                height: '20px',
                                padding: 0,
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                border: 'none',
                                background: 'transparent',
                                fontSize: '16px',
                                fontWeight: 'bold'
                              }}
                            >
                              ×
                            </button>
                          </div>
                        ))
                      }

                      {/* Add new category option */}
                      {categoryInput.trim() &&
                       !availableTags.some(tag => tag.tag_name.toLowerCase() === categoryInput.toLowerCase()) && (
                        <div
                          className="dropdown-item text-primary"
                          onClick={() => addNewCategory()}
                          style={{ cursor: 'pointer' }}
                        >
                          <Icon icon="fluent:add-24-regular" width="16" height="16" className="me-2" />
                          Add "{categoryInput.trim()}"
                        </div>
                      )}

                      {availableTags.length === 0 && (
                        <div className="dropdown-item disabled">No categories available</div>
                      )}
                    </div>
                  )}
                </div>

                {formErrors.categories && (
                  <div className="invalid-feedback d-block">{formErrors.categories}</div>
                )}

                {/* Display selected categories */}
                {formData.categories.length > 0 && (
                  <div className="mt-2">
                    <div className="d-flex flex-wrap gap-2">
                      {formData.categories.map((category, index) => (
                        <span key={index} className="badge bg-info d-flex align-items-center gap-1">
                          {category}
                          <button
                            type="button"
                            className="btn-close btn-close-white"
                            style={{ fontSize: '0.6em' }}
                            onClick={() => removeCategory(category)}
                            aria-label="Remove category"
                          ></button>
                        </span>
                      ))}
                    </div>
                  </div>
                )}
              </div>

              {/* Options */}
              <div className="mb-3">
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '12px' }}>
                  <label className="form-label" style={{ margin: 0 }}>Options <span className="text-danger">*</span></label>
                  <button
                    type="button"
                    className="btn btn-primary btn-sm"
                    onClick={addOption}
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: '4px',
                      padding: '6px 12px',
                      fontSize: '14px',
                      width: '150px',
                    }}
                  >
                    <Icon icon="fluent:add-24-regular" width="16" height="16" />
                    Add Option
                  </button>
                </div>

                {formErrors.options && (
                  <div className="text-danger small mb-2">{formErrors.options}</div>
                )}
                {formErrors.correctAnswer && (
                  <div className="text-danger small mb-2">{formErrors.correctAnswer}</div>
                )}

                {formData.options.map((option, index) => (
                  <div key={index} style={{ marginBottom: '12px' }}>
                    <div style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: '8px',
                      width: '100%'
                    }}>
                      <input
                        type="radio"
                        name="correctAnswer"
                        checked={option.isCorrect}
                        onChange={() => handleCorrectAnswerChange(index)}
                        title="Mark as correct answer"
                        style={{
                          margin: 0,
                          flexShrink: 0,
                          width: '16px',
                          height: '16px'
                        }}
                      />
                      <input
                        type="text"
                        className="form-control"
                        value={option.text}
                        onChange={(e) => handleOptionChange(index, e.target.value)}
                        placeholder={`Option ${index + 1}`}
                        style={{
                          flex: 1,
                          minWidth: 0
                        }}
                      />
                      {formData.options.length > 2 && (
                        <button
                          type="button"
                          className="btn btn-outline-danger btn-sm"
                          onClick={() => removeOption(index)}
                          title="Remove option"
                          style={{
                            flexShrink: 0,
                            width: '36px',
                            height: '36px',
                            padding: 0,
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center'
                          }}
                        >
                          <Icon icon="fluent:delete-24-regular" width="16" height="16" />
                        </button>
                      )}
                    </div>
                  </div>
                ))}

                <div className="form-text text-muted">
                  Select the radio button to mark the correct answer. Minimum 2 options required.
                </div>
              </div>
            </form>
          )}
        </Modal.Body>
        <Modal.Footer className="d-flex justify-content-between" style={{ borderTop: '1px solid #dee2e6' }}>
          <button
            type="button"
            className="btn btn-secondary"
            onClick={handleModalClose}
            disabled={isSubmitting}
            style={{ width: '100%' }}
          >
            Cancel
          </button>
          <button
            type="button"
            className="btn btn-primary"
            onClick={handleSubmit}
            disabled={isSubmitting}
            style={{ width: '100%' }}
          >
            {isSubmitting ? (
              <>
                <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                Adding...
              </>
            ) : (
              'Add Question'
            )}
          </button>
        </Modal.Footer>
      </Modal>

      {/* Edit Question Modal */}
      <Modal show={showEditModal} onHide={handleEditModalClose} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>Edit Question</Modal.Title>
        </Modal.Header>
        <Modal.Body style={{ maxHeight: '70vh', overflowY: 'auto', paddingBottom: '0' }}>
          {!permissions.question_bank_management_edit ? (
            <div className="alert alert-warning">
              <Icon icon="mdi:warning" className="me-2" />
              You don't have permission to edit questions.
            </div>
          ) : (
            <form onSubmit={handleEditSubmit}>
              {/* Question Input */}
              <div className="mb-3">
                <label className="form-label">Enter Question <span className="text-danger">*</span></label>
                <textarea
                  className={`form-control ${formErrors.question ? 'is-invalid' : ''}`}
                  name="question"
                  value={editFormData.question}
                  onChange={(e) => setEditFormData(prev => ({ ...prev, question: e.target.value }))}
                  placeholder="Enter your question here..."
                  rows="3"
                />
                {formErrors.question && (
                  <div className="invalid-feedback">{formErrors.question}</div>
                )}
              </div>

              {/* Question Type */}
              <div className="mb-3">
                <label className="form-label">Question Type</label>
                <select
                  className="form-select"
                  name="questionType"
                  value={editFormData.questionType}
                  onChange={(e) => setEditFormData(prev => ({ ...prev, questionType: e.target.value }))}
                  disabled
                >
                  <option value="MCQ">Multiple Choice Question (MCQ)</option>
                </select>
                <div className="form-text text-muted">
                  Question type cannot be changed after creation
                </div>
              </div>

              {/* Categories for Edit */}
              <div className="mb-3">
                <label className="form-label">Categories <span className="text-danger">*</span></label>

                {/* Display selected categories */}
                {editFormData.categories.length > 0 && (
                  <div className="mb-2">
                    <div className="d-flex flex-wrap gap-2">
                      {editFormData.categories.map((category, index) => (
                        <span key={index} className="badge bg-info d-flex align-items-center gap-1">
                          {category}
                          <button
                            type="button"
                            className="btn-close btn-close-white"
                            style={{ fontSize: '0.6em' }}
                            onClick={() => {
                              setEditFormData(prev => ({
                                ...prev,
                                categories: prev.categories.filter(cat => cat !== category)
                              }));
                            }}
                            aria-label="Remove category"
                          ></button>
                        </span>
                      ))}
                    </div>
                  </div>
                )}

                {/* Add more categories */}
                <div className="position-relative">
                  <input
                    type="text"
                    className={`form-control ${formErrors.categories ? 'is-invalid' : ''}`}
                    value={categoryInput}
                    onChange={(e) => setCategoryInput(e.target.value)}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' && categoryInput.trim()) {
                        e.preventDefault();
                        if (!editFormData.categories.includes(categoryInput.trim())) {
                          setEditFormData(prev => ({
                            ...prev,
                            categories: [...prev.categories, categoryInput.trim()]
                          }));
                        }
                        setCategoryInput('');
                      }
                    }}
                    placeholder="Type to add more categories..."
                    onFocus={() => setShowCategoryDropdown(true)}
                  />

                  {/* Dropdown for edit modal */}
                  {showCategoryDropdown && (
                    <div className="dropdown-menu show w-100" style={{ maxHeight: '200px', overflowY: 'auto', zIndex: 1050 }}>
                      {availableTags
                        .filter(tag =>
                          tag.tag_name.toLowerCase().includes(categoryInput.toLowerCase()) &&
                          !editFormData.categories.includes(tag.tag_name)
                        )
                        .map(tag => (
                          <div
                            key={tag.id}
                            className="dropdown-item d-flex justify-content-between align-items-center"
                            style={{ cursor: 'pointer', padding: '8px 12px' }}
                          >
                            <span
                              onClick={() => {
                                if (!editFormData.categories.includes(tag.tag_name)) {
                                  setEditFormData(prev => ({
                                    ...prev,
                                    categories: [...prev.categories, tag.tag_name]
                                  }));
                                }
                                setCategoryInput('');
                                setShowCategoryDropdown(false);
                              }}
                              style={{ flex: 1 }}
                            >
                              {tag.tag_name}
                            </span>
                            <button
                              type="button"
                              className="btn btn-sm text-danger"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleDeleteTag(tag);
                              }}
                              title="Delete tag permanently"
                              style={{
                                width: '20px',
                                height: '20px',
                                padding: 0,
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                border: 'none',
                                background: 'transparent',
                                fontSize: '16px',
                                fontWeight: 'bold'
                              }}
                            >
                              ×
                            </button>
                          </div>
                        ))
                      }
                    </div>
                  )}
                </div>

                {formErrors.categories && (
                  <div className="invalid-feedback d-block">{formErrors.categories}</div>
                )}
              </div>

              {/* Options for Edit */}
              <div className="mb-3">
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '12px' }}>
                  <label className="form-label" style={{ margin: 0 }}>Options <span className="text-danger">*</span></label>
                  <button
                    type="button"
                    className="btn btn-primary btn-sm"
                    onClick={() => {
                      setEditFormData(prev => ({
                        ...prev,
                        options: [...prev.options, { text: '', isCorrect: false }]
                      }));
                    }}
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: '4px',
                      padding: '6px 12px',
                      fontSize: '14px',
                      width: '150px',
                    }}
                  >
                    <Icon icon="fluent:add-24-regular" width="16" height="16" />
                    Add Option
                  </button>
                </div>

                {formErrors.options && (
                  <div className="text-danger small mb-2">{formErrors.options}</div>
                )}
                {formErrors.correctAnswer && (
                  <div className="text-danger small mb-2">{formErrors.correctAnswer}</div>
                )}

                {editFormData.options.map((option, index) => (
                  <div key={index} style={{ marginBottom: '12px' }}>
                    <div style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: '8px',
                      width: '100%'
                    }}>
                      <input
                        type="radio"
                        name="editCorrectAnswer"
                        checked={option.isCorrect}
                        onChange={() => {
                          setEditFormData(prev => ({
                            ...prev,
                            options: prev.options.map((opt, idx) => ({
                              ...opt,
                              isCorrect: idx === index
                            }))
                          }));
                        }}
                        title="Mark as correct answer"
                        style={{
                          margin: 0,
                          flexShrink: 0,
                          width: '16px',
                          height: '16px'
                        }}
                      />
                      <input
                        type="text"
                        className="form-control"
                        value={option.text}
                        onChange={(e) => {
                          setEditFormData(prev => ({
                            ...prev,
                            options: prev.options.map((opt, idx) =>
                              idx === index ? { ...opt, text: e.target.value } : opt
                            )
                          }));
                        }}
                        placeholder={`Option ${index + 1}`}
                        style={{
                          flex: 1,
                          minWidth: 0
                        }}
                      />
                      {editFormData.options.length > 2 && (
                        <button
                          type="button"
                          className="btn btn-outline-danger btn-sm"
                          onClick={() => {
                            if (editFormData.options.length > 2) {
                              setEditFormData(prev => ({
                                ...prev,
                                options: prev.options.filter((_, idx) => idx !== index)
                              }));
                            }
                          }}
                          title="Remove option"
                          style={{
                            flexShrink: 0,
                            width: '36px',
                            height: '36px',
                            padding: 0,
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center'
                          }}
                        >
                          <Icon icon="fluent:delete-24-regular" width="16" height="16" />
                        </button>
                      )}
                    </div>
                  </div>
                ))}

                <div className="form-text text-muted">
                  Select the radio button to mark the correct answer. Minimum 2 options required.
                </div>
              </div>
            </form>
          )}
        </Modal.Body>
        <Modal.Footer className="d-flex justify-content-between" style={{ borderTop: '1px solid #dee2e6' }}>
          <button
            type="button"
            className="btn btn-secondary"
            onClick={handleEditModalClose}
            disabled={isEditing}
            style={{ width: '100%' }}
          >
            Cancel
          </button>
          <button
            type="button"
            className="btn btn-primary"
            onClick={handleEditSubmit}
            disabled={isEditing || !permissions.question_bank_management_edit}
            style={{ width: '100%' }}
          >
            {isEditing ? (
              <>
                <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                Updating...
              </>
            ) : (
              'Update Question'
            )}
          </button>
        </Modal.Footer>
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal show={showDeleteModal} onHide={() => setShowDeleteModal(false)} centered>
        <Modal.Header closeButton>
          <Modal.Title className="text-danger">
            <Icon icon="fluent:warning-24-regular" className="me-2" />
            Confirm Deletion
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {!permissions.question_bank_management_delete ? (
            <div className="alert alert-warning">
              <Icon icon="mdi:warning" className="me-2" />
              You don't have permission to delete questions.
            </div>
          ) : selectedQuestion && (
            <div>
              <p>Are you sure you want to delete the question:</p>
              <p className="fw-bold">"{selectedQuestion.question_name}"</p>
              <p className="text-danger mb-0">This action cannot be undone.</p>
            </div>
          )}
        </Modal.Body>
        <Modal.Footer>
          <button
            type="button"
            className="btn btn-secondary"
            onClick={() => {
              setShowDeleteModal(false);
              setSelectedQuestion(null);
            }}
            disabled={isDeleting}
          >
            Cancel
          </button>
          <button
            type="button"
            className="btn btn-danger"
            onClick={confirmDeleteQuestion}
            disabled={isDeleting || !permissions.question_bank_management_delete}
          >
            {isDeleting ? (
              <>
                <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                Deleting...
              </>
            ) : (
              'Delete Question'
            )}
          </button>
        </Modal.Footer>
      </Modal>

      {/* Delete Tag Confirmation Modal */}
      <Modal show={showDeleteTagModal} onHide={() => setShowDeleteTagModal(false)} centered>
        <Modal.Header closeButton>
          <Modal.Title className="text-danger">
            <Icon icon="fluent:warning-24-regular" className="me-2" />
            Delete Tag Permanently
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {selectedTagToDelete && (
            <div>
              <p>Are you sure you want to permanently delete the tag:</p>
              <p className="fw-bold text-primary">"{selectedTagToDelete.tag_name}"</p>
              <div className="alert alert-warning">
                <Icon icon="fluent:warning-24-filled" className="me-2" />
                <strong>Warning:</strong> This action will:
                <ul className="mb-0 mt-2">
                  <li>Delete this tag permanently from the system</li>
                  <li>Remove this tag from all existing questions</li>
                  <li>This action cannot be undone</li>
                </ul>
              </div>
            </div>
          )}
        </Modal.Body>
        <Modal.Footer>
          <button
            type="button"
            className="btn btn-secondary"
            onClick={() => {
              setShowDeleteTagModal(false);
              setSelectedTagToDelete(null);
            }}
            disabled={isDeletingTag}
          >
            Cancel
          </button>
          <button
            type="button"
            className="btn btn-danger"
            onClick={confirmDeleteTag}
            disabled={isDeletingTag}
          >
            {isDeletingTag ? (
              <>
                <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                Deleting...
              </>
            ) : (
              <>
                <Icon icon="fluent:delete-24-regular" className="me-2" />
                Delete Permanently
              </>
            )}
          </button>
        </Modal.Footer>
      </Modal>
    </>
  )
}

export default QuestionBank
