import { GET, POST, PUT, DELETE, UPLOAD_FILE } from './apiController';

const endpoint = "/mainOrg";

// Get trainee details with pagination, search, and status filter
export const getTraineeDetails = async ({ page, limit, status, search }) => {
  return GET(
    endpoint + `/trainee_details?page=${page}&limit=${limit}&status=${status}&search=${search}`
  );
};

// Toggle trainee active/inactive status
export const activeInactiveTrainee = (user_id, status) => {
  return PUT(endpoint + `/deactivate_user/${user_id}`, { status });
};

// Fetch individual trainee details for editing
export const fetchUserDetails = (user_id) => {
  return GET(endpoint + `/get_trainee_details/${user_id}`);
};

// Delete a trainee
export const deleteTrainee = async (user_id) => {
  return DELETE(endpoint + `/delete_user/${user_id}`);
};

// Edit trainee details
export const editTraineeDetails = (trainee_id, data) => {
  return UPLOAD_FILE(endpoint + `/edit_trainee_details/${trainee_id}`, data);
};

// Create new organization user (trainee)
export const createNewOrganisationUser = (data) => {
  return UPLOAD_FILE(endpoint + `/orgUser`, data);
};

// Bulk upload trainees from Excel file
export const BulkUploadUserDetails = (data) => {
  return POST(endpoint + `/user_bulk_upload`, data);
};

// Download sample Excel template
export const getSample = () => {
  return GET(endpoint + `/sample_download`, {
    responseType: "arraybuffer",
  });
};

// Get organization users (alternative endpoint)
export const createOrgUsers = (data) => {
  return GET(endpoint + `/orgUser`, data);
};


// /trainee_analytics 
export const getTraineeAnalytics = (data) => {
  return POST(endpoint + `/trainee_analytics`, data);
};
