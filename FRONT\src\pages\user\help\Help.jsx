import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Icon } from '@iconify/react';
import './Help.css';

function Help() {
    const navigate = useNavigate();
    return (
        <div className="row">
            <div className="col-12">
                <div className="help-container">
                    {/* Header Section with Gradient Background */}
                    <div className="header-section rounded-4 mb-4 p-4"
                        style={{
                            background: 'linear-gradient(to right, #e991e5, #8dc9f5)',
                            minHeight: '200px',
                            display: 'flex',
                            flexDirection: 'column',
                            justifyContent: 'center'
                        }}>
                        <h1 className="text-white mb-2">Help Center</h1>
                        <p className="text-white mb-0 fs-5">Hi, all your problem solutions are here</p>
                    </div>

                    {/* Help Options Grid */}
                    <div className="row g-4">
                        {/* FAQ Section */}
                        <div className="col-12 col-md-6">
                            <div className="card h-100  shadow-sm">
                                <div className="card-body text-center p-4">
                                    <div className="mb-4">
                                        <Icon
                                            icon="fluent:chat-help-24-regular"
                                            width="64"
                                            height="64"
                                            className="text-primary"
                                        />
                                    </div>
                                    <h3 className="card-title mb-3 fs-5">Frequently Asked Questions</h3>
                                    <p className="card-text text-muted mb-4">
                                        If you have questions, our FAQ section can help! Click on "FAQs" to browse
                                        through common questions and find solutions to your problems. If you
                                        don't find the answer you're looking for, free to raise a ticket and our
                                        support team will assist you.
                                    </p>

                                    <button
                                        onClick={() => navigate('/user/help/faq')}
                                        className="btn btn-primary px-4 responsive-btn"
                                    >
                                        <Icon icon="fluent:book-question-mark-24-regular" className="me-2" />
                                        Browse FAQs
                                    </button>
                                </div>
                            </div>
                        </div>

                        {/* Ticket Section */}
                        <div className="col-12 col-md-6">
                            <div className="card h-100  shadow-sm">
                                <div className="card-body text-center p-4">
                                    <div className="mb-4">
                                        <Icon
                                            icon="fluent:ticket-24-regular"
                                            width="64"
                                            height="64"
                                            className="text-primary"
                                        />
                                    </div>
                                    <h3 className="card-title mb-3 fs-5">Ticket</h3>
                                    <p className="card-text text-muted mb-4">
                                        We're here to assist you! If you need help, simply click the "Raise a Ticket"
                                        button to report a problem or ask for assistance. Once your ticket is submitted,
                                        you can click the "Ticket" button to check the status and follow up on your
                                        request.
                                    </p>
                                    <button
                                        onClick={() => navigate('/user/help/ticket')}
                                        className="btn btn-primary px-4 responsive-btn">
                                        <Icon icon="fluent:ticket-add-24-regular" className="me-2" />
                                        Raise a Ticket
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}

// Add CSS styles
const styles = `

`;

// Add style tag
const styleSheet = document.createElement("style");
styleSheet.innerText = styles;
document.head.appendChild(styleSheet);

export default Help;