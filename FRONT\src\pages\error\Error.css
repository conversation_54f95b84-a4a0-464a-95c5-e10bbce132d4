.error-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  padding: 20px;
  font-family: 'Inter', sans-serif;
}

.error-content {
  text-align: center;
  max-width: 600px;
  margin-right: 50px;
}

.error-number {
  font-size: 120px;
  font-weight: 700;
  color: #2563eb;
  line-height: 1;
  margin-bottom: 24px;
  animation: float 6s ease-in-out infinite;
}

.error-title {
  font-size: 32px;
  color: #1f2937;
  margin-bottom: 16px;
}

.error-description {
  font-size: 16px;
  color: #6b7280;
  margin-bottom: 32px;
  line-height: 1.6;
}

.error-actions {
  display: flex;
  gap: 16px;
  justify-content: center;
}

.btn-home, .btn-back {
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
}

.btn-home {
  background-color: #2563eb;
  color: white;
}

.btn-home:hover {
  background-color: #1d4ed8;
  transform: translateY(-2px);
}

.btn-back {
  background-color: #f3f4f6;
  color: #4b5563;
}

.btn-back:hover {
  background-color: #e5e7eb;
  transform: translateY(-2px);
}

.home-icon, .back-icon {
  font-size: 20px;
}

/* Ghost Animation */
.error-animation {
  position: relative;
  width: 200px;
  height: 200px;
}

.error-ghost {
  position: relative;
  width: 100%;
  height: 100%;
  animation: float 6s ease-in-out infinite;
}

.ghost-body {
  position: relative;
  width: 100px;
  height: 150px;
  background: #2563eb;
  border-radius: 50px 50px 0 0;
  animation: float 6s ease-in-out infinite;
}

.ghost-eyes {
  position: absolute;
  top: 40px;
  display: flex;
  justify-content: center;
  width: 100%;
  gap: 20px;
}

.eye-left, .eye-right {
  width: 12px;
  height: 12px;
  background: white;
  border-radius: 50%;
  animation: blink 3s ease-in-out infinite;
}

.ghost-bottom {
  position: absolute;
  bottom: 0;
  width: 100%;
  height: 20px;
  display: flex;
}

.ghost-bottom div {
  flex: 1;
  background: #2563eb;
  border-radius: 0 0 50% 50%;
  margin: 0 2px;
}

.ghost-shadow {
  position: absolute;
  bottom: -20px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 15px;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 50%;
  animation: shadow 6s ease-in-out infinite;
}

/* Lock Animation for 401 */
.error-lock {
  position: relative;
  width: 100px;
  height: 150px;
  margin: 0 auto;
  animation: float 6s ease-in-out infinite;
}

.lock-body {
  position: relative;
  width: 80px;
  height: 100px;
  background: #2563eb;
  border-radius: 10px;
  margin: 40px auto 0;
}

.lock-hole {
  position: absolute;
  top: 40%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 25px;
  height: 25px;
  background: #f8f9fa;
  border-radius: 50%;
}

.lock-base {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 5px;
  background: #1d4ed8;
  border-radius: 3px;
}

.lock-hook {
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40px;
  height: 45px;
  animation: unlocked 5s ease-in-out infinite;
  transform-origin: bottom center;
}

.hook-body {
  position: absolute;
  width: 40px;
  height: 40px;
  border: 8px solid #2563eb;
  border-radius: 20px 20px 0 0;
}

.hook-base {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 10px;
  height: 15px;
  background: #2563eb;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes shadow {
  0%, 100% {
    transform: translateX(-50%) scale(1);
    opacity: 0.6;
  }
  50% {
    transform: translateX(-50%) scale(0.8);
    opacity: 0.3;
  }
}

@keyframes blink {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(0.8);
  }
}

@keyframes unlocked {
  0%, 50% {
    transform: translateX(-50%) rotate(0);
  }
  60%, 70% {
    transform: translateX(-50%) rotate(-20deg);
  }
  80%, 90% {
    transform: translateX(-50%) rotate(0);
  }
}

@media (max-width: 768px) {
  .error-page {
    flex-direction: column;
    text-align: center;
  }

  .error-content {
    margin-right: 0;
    margin-bottom: 50px;
    padding: 0 20px;
  }

  .error-number {
    font-size: 100px;
  }

  .error-title {
    font-size: 28px;
  }

  .error-actions {
    flex-direction: column;
    align-items: stretch;
  }

  .btn-home, .btn-back {
    justify-content: center;
  }
} 