const { mysqlServerConnection } = require("../../../db/db");
const axios = require("axios");
const crypto = require("crypto");
const { LogsHandler } = require("../../../tools/tools");

// Zoom OAuth Credentials
const ZOOM_ACCOUNT_ID = process.env.ZOOM_ACCOUNT_ID;
const ZOOM_CLIENT_ID = process.env.ZOOM_CLIENT_ID;
const ZOOM_CLIENT_SECRET = process.env.ZOOM_CLIENT_SECRET;

// Zoom SDK Credentials
const ZOOM_SDK_CLIENT_ID = process.env.Zoom_SDK_CLIENT_ID;
const ZOOM_SDK_CLIENT_SECRET = process.env.ZOOM_SDK_CLIENT_SECRET;

// Zoom API Base URL
const ZOOM_API_BASE_URL = "https://api.zoom.us/v2";

/**
 * Generate Zoom Server-to-Server OAuth Access Token
 */
const getZoomAccessToken = async () => {
    try {
        const credentials = Buffer.from(
            `${ZOOM_CLIENT_ID}:${ZOOM_CLIENT_SECRET}`
        ).toString("base64");

        const response = await axios.post(
            "https://zoom.us/oauth/token",
            `grant_type=account_credentials&account_id=${ZOOM_ACCOUNT_ID}`,
            {
                headers: {
                    Authorization: `Basic ${credentials}`,
                    "Content-Type": "application/x-www-form-urlencoded",
                },
            }
        );

        return response.data.access_token;
    } catch (error) {
        console.error(
            "Error getting Zoom access token:",
            error.response?.data || error.message
        );
        throw new Error("Failed to obtain Zoom access token");
    }
};

/**
 * Get or update Zoom account profile with user's name
 */
const resetZoomProfile = async (accessToken, userName) => {
    try {
        await axios.patch(
            `${ZOOM_API_BASE_URL}/users/me`,
            {
                first_name: userName.split(" ")[0] || userName,
                last_name: userName.split(" ").slice(1).join(" ") || "",
            },
            {
                headers: {
                    Authorization: `Bearer ${accessToken}`,
                    "Content-Type": "application/json",
                },
            }
        );
    } catch (error) {
        console.error(
            "Error updating Zoom profile:",
            error.response?.data || error.message
        );
        // Don't throw error here as it's not critical for meeting creation
    }
};

/**
 * Verify if a Zoom meeting exists
 */
const verifyZoomMeeting = async (meetingId) => {
    try {
        const accessToken = await getZoomAccessToken();
        const response = await axios.get(
            `${ZOOM_API_BASE_URL}/meetings/${meetingId}`,
            {
                headers: {
                    Authorization: `Bearer ${accessToken}`,
                    "Content-Type": "application/json",
                },
            }
        );
        console.log("[verifyZoomMeeting] Meeting exists:", meetingId, {
            status: response.data.status,
            password: response.data.password || "No password",
            encrypted_password:
                response.data.encrypted_password || "No encrypted password",
        });
        return { exists: true, meeting: response.data };
    } catch (error) {
        console.error(
            "[verifyZoomMeeting] Meeting verification failed:",
            meetingId,
            error.response?.data || error.message
        );
        return { exists: false, error: error.response?.data || error.message };
    }
};

/**
 * Generate Zoom Meeting SDK Signature (Updated for SDK v4.0.0 - JWT format)
 */
const generateZoomSDKSignature = (meetingNumber, role) => {
    try {
        const iat = Math.round(new Date().getTime() / 1000) - 30;
        const exp = iat + 60 * 60 * 2; // 2 hours

        const oHeader = { alg: "HS256", typ: "JWT" };

        // Updated payload structure for SDK v4.0.0
        const oPayload = {
            iss: ZOOM_SDK_CLIENT_ID,
            exp: exp,
            iat: iat,
            aud: "zoom",
            appKey: ZOOM_SDK_CLIENT_ID,
            tokenExp: exp,
            alg: "HS256",
            // Add meeting-specific claims
            mn: meetingNumber.toString(),
            role: parseInt(role),
        };

        const jwt = require("jsonwebtoken");
        const sJWT = jwt.sign(oPayload, ZOOM_SDK_CLIENT_SECRET, {
            header: oHeader,
        });

        console.log("[generateZoomSDKSignature] Generated JWT signature for:", {
            meetingNumber,
            role,
            iat,
            exp,
            payload: oPayload,
            sdkKey: ZOOM_SDK_CLIENT_ID,
        });

        return {
            signature: sJWT,
            sdkKey: ZOOM_SDK_CLIENT_ID,
        };
    } catch (error) {
        console.error("Error generating Zoom SDK signature:", error);
        throw new Error("Failed to generate Zoom SDK signature");
    }
};

/**
 * Create a new live class with Zoom meeting
 */
const createLiveClass = async (req, res) => {
    console.log(
        "[createLiveClass] called with body:",
        req.body,
        "user:",
        req.user
    );
    try {
        const {
            classroom_id,
            live_class_name,
            live_class_description,
            time_zone,
            class_date,
            start_time,
            duration,
            sub_domain,
            userId
        } = req.body;
        console.log("[createLiveClass] called with bod888888888888888888888888888888888888888888y:", req.body);

        const dbName = req.user.db_name;
        const userName = req.user.name || "LMS User";

        // Validate required fields
        if (
            !classroom_id ||
            !live_class_name ||
            !class_date ||
            !start_time ||
            !duration
        ) {
            console.warn("[createLiveClass] Validation failed:", {
                classroom_id,
                live_class_name,
                class_date,
                start_time,
                duration,
            });
            return res.status(400).json({
                success: false,
                message:
                    "Missing required fields: classroom_id, live_class_name, class_date, start_time, duration",
            });
        }

        // Get Zoom access token
        const accessToken = await getZoomAccessToken();
        console.log("[createLiveClass] Zoom access token received");

        // Update Zoom profile with user's name
        try {
            await resetZoomProfile(accessToken, userName);
            console.log("[createLiveClass] Zoom profile updated for user:", userName);
        } catch (err) {
            console.warn(
                "[createLiveClass] Zoom profile update failed:",
                err.message
            );
        }

        // Create Zoom meeting
        const meetingData = {
            topic: `${live_class_name} - Host: ${userName}`,
            type: 2, // Scheduled meeting (more reliable than instant)
            start_time: new Date(start_time).toISOString(),
            duration: parseInt(duration.replace(/\D/g, "")) || 60, // Extract number from duration string
            timezone: time_zone || "UTC",
            password: "", // Disable password for easier SDK integration
            settings: {
                host_video: true,
                participant_video: true,
                mute_upon_entry: true,
                waiting_room: false,
                approval_type: 0,
                join_before_host: true,
                auto_recording: "none",
                use_pmi: false,
                meeting_authentication: false,
                password_requirement: false,
                // Add these settings for trainee video visibility
                allow_multiple_devices: true,
                show_share_button: true,
                allow_live_streaming: false,
                request_permission_to_unmute_participants: false,
                // This is the key setting for participant video viewing
                participant_video_recording: true,
                // Allow participants to see each other
                co_host: false,
                polling: false,
                attendee_on_hold: false
            },
        };
        console.log(
            "[createLiveClass] Creating Zoom meeting with data:",
            meetingData
        );

        const zoomResponse = await axios.post(
            `${ZOOM_API_BASE_URL}/users/me/meetings`,
            meetingData,
            {
                headers: {
                    Authorization: `Bearer ${accessToken}`,
                    "Content-Type": "application/json",
                },
            }
        );
        console.log("[createLiveClass] Zoom meeting created:", zoomResponse.data);

        const meeting = zoomResponse.data;

        // Format start_time for MySQL DATETIME format
        const formattedStartTime = new Date(start_time)
            .toISOString()
            .slice(0, 19)
            .replace("T", " ");
        console.log(
            "[createLiveClass] Formatted start_time for MySQL:",
            formattedStartTime
        );

        // Insert into database with temporary URLs (will update after insert)
        const [result] = await mysqlServerConnection.query(
            `INSERT INTO ${dbName}.classroom_live_class
       (classroom_id, live_class_name, live_class_description, time_zone, class_date,
        start_time, duration, status, is_started, host_url, join_url, meeting_id,
        created_by, created_at, updated_at)
       VALUES (?, ?, ?, ?, ?, ?, ?, 'scheduled', 0, '', '', ?, ?, NOW(), NOW())`,
            [
                classroom_id,
                live_class_name,
                live_class_description || null,
                time_zone || "UTC",
                class_date,
                formattedStartTime,
                duration,
                meeting.id.toString(),
                userId,
            ]
        );
        console.log(
            "[createLiveClass] Inserted into DB, insertId:",
            result.insertId
        );

        // Generate SDK signatures for host and attendee
        const hostSignature = generateZoomSDKSignature(meeting.id, 1); // Role 1 = Host
        const attendeeSignature = generateZoomSDKSignature(meeting.id, 0); // Role 0 = Attendee
        console.log("[createLiveClass] SDK signatures generated");

        // Create host URL with SDK parameters
        const hostUrl =
            `${sub_domain}/zoom-meeting?` +
            `meetingNumber=${meeting.id}&role=1&signature=${hostSignature.signature}&` +
            `sdkKey=${ZOOM_SDK_CLIENT_ID}&userName=${encodeURIComponent(
                userName
            )}&liveClassId=${result.insertId}`;

        // Create join URL with SDK parameters
        const joinUrl =
            `${sub_domain}/zoom-meeting?` +
            `meetingNumber=${meeting.id}&role=0&signature=${attendeeSignature.signature}&` +
            `sdkKey=${ZOOM_SDK_CLIENT_ID}&liveClassId=${result.insertId}`;

        // Update the record with the correct URLs
        await mysqlServerConnection.query(
            `UPDATE ${dbName}.classroom_live_class SET host_url = ?, join_url = ? WHERE id = ?`,
            [hostUrl, joinUrl, result.insertId]
        );
        console.log("[createLiveClass] Updated DB with URLs for:", result.insertId);

        res.status(201).json({
            success: true,
            message: "Live class created successfully",
            data: {
                id: result.insertId,
                meeting_id: meeting.id,
                host_url: hostUrl,
                join_url: joinUrl,
                zoom_start_url: meeting.start_url,
                zoom_join_url: meeting.join_url,
            },
        });
        console.log("[createLiveClass] success:", {
            id: result.insertId,
            meeting_id: meeting.id,
        });
    } catch (error) {
        console.error("[createLiveClass] Error:", error);
        res.status(500).json({
            success: false,
            message: "Failed to create live class",
            error: error.message,
        });
    }
};

/**
 * Get all live classes for a classroom
 */
const getLiveClasses = async (req, res) => {
    console.log(
        "[getLiveClasses] called with body:------------------",
        req.body,
        "user:",
        req.user
    );
    try {
        const { classroom_id, page = 1, limit = 10, search = "", status = "" } = req.body;
        const dbName = req.user.db_name;
        
        if (!classroom_id) {
            console.warn("[getLiveClasses] classroom_id missing");
            return res.status(400).json({
                success: false,
                message: "classroom_id is required",
            });
        }

        // Calculate offset for pagination
        const offset = (page - 1) * limit;
        
        // Build search and filter conditions
        let searchCondition = "";
        let searchParams = [];
        
        if (search && search.trim() !== "") {
            searchCondition = `AND (lc.live_class_name LIKE ? OR lc.live_class_description LIKE ? OR u.name LIKE ?)`;
            const searchTerm = `%${search.trim()}%`;
            searchParams = [searchTerm, searchTerm, searchTerm];
        }

        // Add status filter condition
        if (status && status.trim() !== "") {
            let statusCondition = "";
            switch (status.toLowerCase()) {
                case "scheduled":
                    statusCondition = "AND lc.is_started = 0 AND lc.is_ended = 0";
                    break;
                case "live":
                    statusCondition = "AND lc.is_started = 1 AND lc.is_ended = 0";
                    break;
                case "ended":
                    statusCondition = "AND lc.is_ended = 1";
                    break;
                default:
                    // No filter applied
                    break;
            }
            searchCondition += statusCondition;
        }

        // Get total count for pagination
        const [countResult] = await mysqlServerConnection.query(
            `SELECT COUNT(*) as total
             FROM ${dbName}.classroom_live_class lc
             LEFT JOIN ${dbName}.users u ON lc.created_by = u.id
             WHERE lc.classroom_id = ? AND lc.is_deleted = 0 ${searchCondition}`,
            [classroom_id, ...searchParams]
        );
        
        const totalRecords = countResult[0].total;
        const totalPages = Math.ceil(totalRecords / limit);

        // Get paginated results
        const [liveClasses] = await mysqlServerConnection.query(
            `SELECT 
     lc.id,
     lc.classroom_id,
     lc.live_class_name,
     lc.live_class_description,
     lc.time_zone,
     lc.class_date,
     DATE_FORMAT(lc.start_time, '%Y-%m-%d %H:%i:%s') as start_time,
     lc.duration,
     lc.is_started,
     lc.is_ended,
     lc.meeting_id,
     lc.created_by,
     lc.created_at,
     lc.updated_at,
     u.name as created_by_name
   FROM ${dbName}.classroom_live_class lc
   LEFT JOIN ${dbName}.users u ON lc.created_by = u.id
   WHERE lc.classroom_id = ? AND lc.is_deleted = 0 ${searchCondition}
   ORDER BY lc.start_time DESC
   LIMIT ? OFFSET ?`,
            [classroom_id, ...searchParams, parseInt(limit), offset]
        );

        // console.log('[getLiveClasses] DB result:', liveClasses);
        res.status(200).json({
            success: true,
            data: {
                live_classes: liveClasses,
                pagination: {
                    currentPage: parseInt(page),
                    totalPages: totalPages,
                    totalRecords: totalRecords,
                    limit: parseInt(limit),
                    hasNextPage: page < totalPages,
                    hasPrevPage: page > 1
                }
            },
        });
        console.log("[getLiveClasses] success:", { 
            count: liveClasses.length, 
            totalRecords, 
            totalPages, 
            currentPage: page 
        });
    } catch (error) {
        console.error("[getLiveClasses] Error:", error);
        res.status(500).json({
            success: false,
            message: "Failed to fetch live classes",
            error: error.message,
        });
    }
};

/**
 * Update a live class
 */
const updateLiveClass = async (req, res) => {
    console.log(
        "[updateLiveClass] called with params:",
        req.params,
        "body:",
        req.body,
        "user:",
        req.user
    );
    try {
        const { live_class_id } = req.params;
        const {
            live_class_name,
            live_class_description,
            time_zone,
            class_date,
            start_time,
            duration,
        } = req.body;
        const dbName = req.user.db_name;
        const userName = req.user.name || "LMS User";
        // Get existing live class
        const [existingClass] = await mysqlServerConnection.query(
            `SELECT * FROM ${dbName}.classroom_live_class WHERE id = ? AND is_deleted = 0`,
            [live_class_id]
        );
        console.log("[updateLiveClass] Existing class:", existingClass);
        if (existingClass.length === 0) {
            console.warn("[updateLiveClass] Live class not found:", live_class_id);
            return res.status(404).json({
                success: false,
                message: "Live class not found",
            });
        }
        const liveClass = existingClass[0];
        // If meeting is already started, only allow description updates
        if (liveClass.is_started) {
            if (live_class_description !== undefined) {
                await mysqlServerConnection.query(
                    `UPDATE ${dbName}.classroom_live_class
           SET live_class_description = ?, updated_at = NOW()
           WHERE id = ?`,
                    [live_class_description, live_class_id]
                );
                console.log(
                    "[updateLiveClass] Updated description for started class:",
                    live_class_id
                );
            }
            return res.status(200).json({
                success: true,
                message: "Live class description updated successfully",
            });
        }
        // Update Zoom meeting if timing details changed
        if (start_time || duration || live_class_name) {
            const accessToken = await getZoomAccessToken();
            console.log("[updateLiveClass] Zoom access token received");
            await resetZoomProfile(accessToken, userName);
            const updateData = {};
            if (live_class_name)
                updateData.topic = `${live_class_name} - Host: ${userName}`;
            if (start_time)
                updateData.start_time = new Date(start_time).toISOString();
            if (duration)
                updateData.duration = parseInt(duration.replace(/\D/g, "")) || 60;
            if (time_zone) updateData.timezone = time_zone;
            console.log("[updateLiveClass] Updating Zoom meeting with:", updateData);
            await axios.patch(
                `${ZOOM_API_BASE_URL}/meetings/${liveClass.meeting_id}`,
                updateData,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`,
                        "Content-Type": "application/json",
                    },
                }
            );
            console.log("[updateLiveClass] Zoom meeting updated");
        }

        // Update database
        const updateFields = [];
        const updateValues = [];
        if (live_class_name) {
            updateFields.push("live_class_name = ?");
            updateValues.push(live_class_name);
        }
        if (live_class_description !== undefined) {
            updateFields.push("live_class_description = ?");
            updateValues.push(live_class_description);
        }
        if (time_zone) {
            updateFields.push("time_zone = ?");
            updateValues.push(time_zone);
        }
        if (class_date) {
            updateFields.push("class_date = ?");
            updateValues.push(class_date);
        }
        if (start_time) {
            // Format start_time for MySQL DATETIME format
            const formattedStartTime = new Date(start_time)
                .toISOString()
                .slice(0, 19)
                .replace("T", " ");
            console.log(
                "[updateLiveClass] Formatted start_time for MySQL:",
                formattedStartTime
            );
            updateFields.push("start_time = ?");
            updateValues.push(formattedStartTime);
        }
        if (duration) {
            updateFields.push("duration = ?");
            updateValues.push(duration);
        }
        if (updateFields.length > 0) {
            updateFields.push("updated_at = NOW()");
            updateValues.push(live_class_id);
            await mysqlServerConnection.query(
                `UPDATE ${dbName}.classroom_live_class SET ${updateFields.join(
                    ", "
                )} WHERE id = ?`,
                updateValues
            );
            console.log("[updateLiveClass] DB updated fields:", updateFields);
        }
        res.status(200).json({
            success: true,
            message: "Live class updated successfully",
        });
        console.log("[updateLiveClass] success:", {
            live_class_id: req.params.live_class_id,
        });
    } catch (error) {
        console.error("[updateLiveClass] Error:", error);
        res.status(500).json({
            success: false,
            message: "Failed to update live class",
            error: error.message,
        });
    }
};

/**
 * Delete a live class
 */
const deleteLiveClass = async (req, res) => {
    console.log(
        "[deleteLiveClass] called with params:",
        req.params,
        "user:",
        req.user
    );
    try {
        const { live_class_id } = req.params;
        const dbName = req.user.db_name;
        // Get existing live class
        const [existingClass] = await mysqlServerConnection.query(
            `SELECT * FROM ${dbName}.classroom_live_class WHERE id = ? AND is_deleted = 0`,
            [live_class_id]
        );
        console.log("[deleteLiveClass] Existing class:", existingClass);
        if (existingClass.length === 0) {
            console.warn("[deleteLiveClass] Live class not found:", live_class_id);
            return res.status(404).json({
                success: false,
                message: "Live class not found",
            });
        }
        const liveClass = existingClass[0];
        // Cannot delete if meeting is currently active
        if (liveClass.is_started && liveClass.status === "started") {
            console.warn(
                "[deleteLiveClass] Cannot delete active meeting:",
                live_class_id
            );
            return res.status(400).json({
                success: false,
                message:
                    "Cannot delete an active meeting. Please end the meeting first.",
            });
        }
        // Delete Zoom meeting
        try {
            const accessToken = await getZoomAccessToken();
            await axios.delete(
                `${ZOOM_API_BASE_URL}/meetings/${liveClass.meeting_id}`,
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`,
                    },
                }
            );
            console.log(
                "[deleteLiveClass] Zoom meeting deleted:",
                liveClass.meeting_id
            );
        } catch (zoomError) {
            console.warn(
                "[deleteLiveClass] Zoom meeting deletion failed:",
                zoomError.message
            );
        }
        // Soft delete from database
        await mysqlServerConnection.query(
            `UPDATE ${dbName}.classroom_live_class SET is_deleted = 1, updated_at = NOW() WHERE id = ?`,
            [live_class_id]
        );
        console.log("[deleteLiveClass] Marked as deleted in DB:", live_class_id);
        res.status(200).json({
            success: true,
            message: "Live class deleted successfully",
        });
        console.log("[deleteLiveClass] success:", {
            live_class_id: req.params.live_class_id,
        });
    } catch (error) {
        console.error("[deleteLiveClass] Error:", error);
        res.status(500).json({
            success: false,
            message: "Failed to delete live class",
            error: error.message,
        });
    }
};

/**
 * Start a live class meeting
 */
const startLiveClass = async (req, res) => {
    console.log("[startLiveClass] called with body:", req.body);
    try {
        const { live_class_id, sub_domain } = req.body;
        const dbName = req.user.db_name;
        const userName = req.user.name || "LMS User";
        if (!live_class_id) {
            console.warn("[startLiveClass] live_class_id missing");
            return res.status(400).json({
                success: false,
                message: "live_class_id is required",
            });
        }
        // Get existing live class
        const [existingClass] = await mysqlServerConnection.query(
            `SELECT * FROM ${dbName}.classroom_live_class WHERE id = ? AND is_deleted = 0`,
            [live_class_id]
        );
        console.log("[startLiveClass] Existing class:", existingClass);
        if (existingClass.length === 0) {
            console.warn("[startLiveClass] Live class not found:", live_class_id);
            return res.status(404).json({
                success: false,
                message: "Live class not found",
            });
        }
        const liveClass = existingClass[0];

        let message = "Live class started successfully";

        if (liveClass.is_started) {
            console.log(
                "[startLiveClass] Meeting already started, generating fresh host URL:",
                live_class_id
            );
            message = "Generating fresh meeting URL";
        } else {
            // Update meeting status to started only if not already started
            await mysqlServerConnection.query(
                `UPDATE ${dbName}.classroom_live_class
         SET is_started = 1, status = 'started', updated_at = NOW()
         WHERE id = ?`,
                [live_class_id]
            );
            console.log("[startLiveClass] Updated DB to started:", live_class_id);
        }

        // Verify meeting exists in Zoom before generating join URL
        const meetingVerification = await verifyZoomMeeting(liveClass.meeting_id);
        if (!meetingVerification.exists) {
            console.error(
                "[startLiveClass] Meeting does not exist in Zoom:",
                liveClass.meeting_id
            );
            return res.status(400).json({
                success: false,
                message: "Meeting not found in Zoom. Please recreate the meeting.",
                error: meetingVerification.error,
            });
        }

        console.log(
            "[startLiveClass] Meeting verified in Zoom:",
            liveClass.meeting_id,
            meetingVerification.meeting.status
        );

        // Always generate fresh SDK signature for host (whether starting or rejoining)
        const hostSignature = generateZoomSDKSignature(liveClass.meeting_id, 1);
        const meetingPassword = meetingVerification.meeting.password || "";
        const hostUrl =
            `${sub_domain}/zoom-meeting?` +
            `meetingNumber=${liveClass.meeting_id}&role=1&signature=${hostSignature.signature}&` +
            `sdkKey=${ZOOM_SDK_CLIENT_ID}&userName=${encodeURIComponent(
                userName
            )}&password=${encodeURIComponent(
                meetingPassword
            )}&liveClassId=${live_class_id}`;
        res.status(200).json({
            success: true,
            message: message,
            data: {
                host_url: hostUrl,
                meeting_id: liveClass.meeting_id,
                status: "started",
                zoom_meeting_status: meetingVerification.meeting.status,
                meeting_password: meetingPassword,
            },
        });
        console.log("[startLiveClass] success:", {
            live_class_id: req.body.live_class_id,
        });
    } catch (error) {
        console.error("[startLiveClass] Error:", error);
        res.status(500).json({
            success: false,
            message: "Failed to start live class",
            error: error.message,
        });
    }
};

/**
 * End a live class meeting
 */
const endLiveClass = async (req, res) => {
    console.log("[endLiveClass] called with body:", req.body);
    try {
        const { live_class_id } = req.body;
        const dbName = req.user.db_name;
        if (!live_class_id) {
            console.warn("[endLiveClass] live_class_id missing");
            return res.status(400).json({
                success: false,
                message: "live_class_id is required",
            });
        }
        // Get existing live class
        const [existingClass] = await mysqlServerConnection.query(
            `SELECT * FROM ${dbName}.classroom_live_class WHERE id = ? AND is_deleted = 0`,
            [live_class_id]
        );
        console.log("[endLiveClass] Existing class:", existingClass);
        if (existingClass.length === 0) {
            console.warn("[endLiveClass] Live class not found:", live_class_id);
            return res.status(404).json({
                success: false,
                message: "Live class not found",
            });
        }
        const liveClass = existingClass[0];
        // Update meeting status to ended
        await mysqlServerConnection.query(
            `UPDATE ${dbName}.classroom_live_class
       SET status = 'ended',
       is_started = 0,
       is_ended = 1, updated_at = NOW()
       WHERE id = ?`,
            [live_class_id]
        );
        console.log("[endLiveClass] Updated DB to ended:", live_class_id);
        // Try to end Zoom meeting
        let zoomEndResult = { success: false, message: "Not attempted" };
        try {
            const accessToken = await getZoomAccessToken();
            console.log(
                "[endLiveClass] Attempting to end Zoom meeting:",
                liveClass.meeting_id
            );

            await axios.put(
                `${ZOOM_API_BASE_URL}/meetings/${liveClass.meeting_id}/status`,
                {
                    action: "end",
                },
                {
                    headers: {
                        Authorization: `Bearer ${accessToken}`,
                        "Content-Type": "application/json",
                    },
                }
            );

            console.log(
                "[endLiveClass] Zoom meeting ended successfully:",
                liveClass.meeting_id
            );
            zoomEndResult = { success: true, message: "Meeting ended successfully" };

            // Verify the meeting was actually ended
            try {
                const verificationResult = await verifyZoomMeeting(
                    liveClass.meeting_id
                );
                if (verificationResult.exists) {
                    console.log(
                        "[endLiveClass] Meeting status after end:",
                        verificationResult.meeting.status
                    );
                    zoomEndResult.final_status = verificationResult.meeting.status;
                }
            } catch (verifyError) {
                console.log(
                    "[endLiveClass] Could not verify meeting status after ending:",
                    verifyError.message
                );
            }
        } catch (zoomError) {
            console.error("[endLiveClass] Zoom meeting end failed:", {
                meetingId: liveClass.meeting_id,
                error: zoomError.message,
                status: zoomError.response?.status,
                statusText: zoomError.response?.statusText,
                data: zoomError.response?.data,
            });

            // Check if meeting is already ended or doesn't exist
            if (zoomError.response?.status === 404) {
                console.log(
                    "[endLiveClass] Meeting not found on Zoom (already ended or deleted)"
                );
                zoomEndResult = {
                    success: true,
                    message: "Meeting already ended or not found",
                };
            } else if (zoomError.response?.status === 400) {
                console.log(
                    "[endLiveClass] Meeting cannot be ended (may already be ended)"
                );
                zoomEndResult = { success: true, message: "Meeting already ended" };
            } else {
                zoomEndResult = {
                    success: false,
                    message: `Failed to end Zoom meeting: ${zoomError.message}`,
                };
            }
        }
        res.status(200).json({
            success: true,
            message: "Live class ended successfully",
            data: {
                status: "ended",
                zoom_end_result: zoomEndResult,
            },
        });
        console.log("[endLiveClass] success:", {
            live_class_id: req.body.live_class_id,
        });
    } catch (error) {
        console.error("[endLiveClass] Error:", error);
        res.status(500).json({
            success: false,
            message: "Failed to end live class",
            error: error.message,
        });
    }
};

/**
 * Generate join token for trainee
 */
const generateJoinToken = async (req, res) => {
    console.log(
        "[generateJoinToken] called with body:",
        req.body,
        "user:",
        req.user
    );
    try {
        const { live_class_id, sub_domain } = req.body;
        const dbName = req.user.db_name;
        const userName = req.user.name || "Trainee";
        if (!live_class_id) {
            console.warn("[generateJoinToken] live_class_id missing");
            return res.status(400).json({
                success: false,
                message: "live_class_id is required",
            });
        }
        // Get existing live class
        const [existingClass] = await mysqlServerConnection.query(
            `SELECT * FROM ${dbName}.classroom_live_class WHERE id = ? AND is_deleted = 0`,
            [live_class_id]
        );
        console.log("[generateJoinToken] Existing class:", existingClass);
        if (existingClass.length === 0) {
            console.warn("[generateJoinToken] Live class not found:", live_class_id);
            return res.status(404).json({
                success: false,
                message: "Live class not found",
            });
        }
        const liveClass = existingClass[0];
        if (!liveClass.is_started) {
            console.warn("[generateJoinToken] Meeting not started:", live_class_id);
            return res.status(400).json({
                success: false,
                message: "Meeting has not started yet",
            });
        }
        if (liveClass.status === "ended") {
            console.warn("[generateJoinToken] Meeting already ended:", live_class_id);
            return res.status(400).json({
                success: false,
                message: "Meeting has already ended",
            });
        }
        // Verify meeting exists in Zoom before generating join URL
        const meetingVerification = await verifyZoomMeeting(liveClass.meeting_id);
        if (!meetingVerification.exists) {
            console.error(
                "[generateJoinToken] Meeting does not exist in Zoom:",
                liveClass.meeting_id
            );
            return res.status(400).json({
                success: false,
                message: "Meeting not found in Zoom. Please contact the instructor.",
                error: meetingVerification.error,
            });
        }

        console.log(
            "[generateJoinToken] Meeting verified in Zoom:",
            liveClass.meeting_id,
            meetingVerification.meeting.status
        );

        // Generate fresh SDK signature for attendee
        const attendeeSignature = generateZoomSDKSignature(liveClass.meeting_id, 0);
        const meetingPassword = meetingVerification.meeting.password || "";
        const joinUrl =
            `${sub_domain}/zoom-meeting?` +
            `meetingNumber=${liveClass.meeting_id}&role=0&signature=${attendeeSignature.signature}&` +
            `sdkKey=${ZOOM_SDK_CLIENT_ID}&userName=${encodeURIComponent(
                userName
            )}&password=${encodeURIComponent(
                meetingPassword
            )}&liveClassId=${live_class_id}`;
        console.log("[generateJoinToken] Join URL generated:", joinUrl);
        res.status(200).json({
            success: true,
            data: {
                join_url: joinUrl,
                meeting_id: liveClass.meeting_id,
                meeting_name: liveClass.live_class_name,
                status: liveClass.status,
                zoom_meeting_status: meetingVerification.meeting.status,
                meeting_password: meetingPassword,
            },
        });
        console.log("[generateJoinToken] success:", {
            live_class_id: req.body.live_class_id,
        });
    } catch (error) {
        console.error("[generateJoinToken] Error:", error);
        res.status(500).json({
            success: false,
            message: "Failed to generate join token",
            error: error.message,
        });
    }
};

/**
 * Debug endpoint to verify meeting and signature
 */
const debugMeeting = async (req, res) => {
    try {
        const { meeting_id } = req.params;
        console.log("[debugMeeting] Debugging meeting:", meeting_id);

        // Verify meeting exists
        const meetingVerification = await verifyZoomMeeting(meeting_id);

        // Generate test signatures
        const hostSignature = generateZoomSDKSignature(meeting_id, 1);
        const attendeeSignature = generateZoomSDKSignature(meeting_id, 0);

        res.status(200).json({
            success: true,
            data: {
                meeting_id,
                meeting_exists: meetingVerification.exists,
                meeting_data: meetingVerification.meeting || null,
                meeting_error: meetingVerification.error || null,
                host_signature: hostSignature,
                attendee_signature: attendeeSignature,
                sdk_client_id: ZOOM_SDK_CLIENT_ID,
            },
        });
    } catch (error) {
        console.error("[debugMeeting] Error:", error);
        res.status(500).json({
            success: false,
            message: "Debug failed",
            error: error.message,
        });
    }
};

module.exports = {
    createLiveClass,
    getLiveClasses,
    updateLiveClass,
    deleteLiveClass,
    startLiveClass,
    endLiveClass,
    generateJoinToken,
    getZoomAccessToken,
    generateZoomSDKSignature,
    debugMeeting,
};
