import React, { useEffect, useState } from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import { endZoomLiveClass } from '../../services/adminService';

// Zoom SDK Error Boundary
class ZoomErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false };
  }
  static getDerivedStateFromError() { return { hasError: true }; }
  componentDidCatch(error, info) {
    console.log('🔵 Zoom SDK Error caught by boundary:', error, info);
  }
  render() {
    if (this.state.hasError) return null;
    return this.props.children;
  }
}

const ZoomMeeting = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();

  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [initializationStarted, setInitializationStarted] = useState(false);

  const meetingNumber = searchParams.get('meetingNumber');
  const role = searchParams.get('role');
  const signature = searchParams.get('signature');
  const sdkKey = searchParams.get('sdkKey');
  const userName = searchParams.get('userName') || (role === '1' ? 'Host' : 'Attendee');
  const meetingPassword = searchParams.get('password') || '';
  const liveClassId = searchParams.get('liveClassId'); // Get live class ID for API calls

  useEffect(() => {
    if (!meetingNumber || !signature || !sdkKey || role === null) {
      setError('Missing required meeting parameters');
      setLoading(false);
      return;
    }

    fetch('https://source.zoom.us/ping', { mode: 'no-cors' }).catch(() => {});

    return () => {
      // Clear Zoom-rendered DOM on cleanup
      try {
        const zoomRoot = document.getElementById('zoom-root');
        if (zoomRoot) zoomRoot.innerHTML = '';
      } catch (e) {
        console.warn('Zoom cleanup error:', e);
      }
    };
  }, [meetingNumber, signature, sdkKey, role]);

  useEffect(() => {
    if (!initializationStarted && !error) {
      setInitializationStarted(true);

      const timeoutId = setTimeout(() => {
        if (loading) {
          setError('Meeting initialization timed out. Please try again.');
          setLoading(false);
        }
      }, 60000);

      initializeZoomMeeting().finally(() => clearTimeout(timeoutId));
    }
  }, [initializationStarted, loading, error]);

  const loadZoomSDK = async () => {
    if (window.ZoomMtg) return window.ZoomMtg;

    const scripts = [
      'https://source.zoom.us/4.0.0/lib/vendor/react.min.js',
      'https://source.zoom.us/4.0.0/lib/vendor/react-dom.min.js',
      'https://source.zoom.us/4.0.0/lib/vendor/redux.min.js',
      'https://source.zoom.us/4.0.0/lib/vendor/redux-thunk.min.js',
      'https://source.zoom.us/4.0.0/zoom-meeting-4.0.0.min.js'
    ];

    await Promise.all(scripts.map(src => new Promise((res, rej) => {
      if (document.querySelector(`script[src="${src}"]`)) return res();
      const s = document.createElement('script');
      s.src = src;
      s.async = false;
      s.onload = res;
      s.onerror = () => rej(new Error(`Failed to load: ${src}`));
      document.head.appendChild(s);
    })));

    return new Promise((resolve, reject) => {
      let attempts = 0;
      (function check() {
        if (window.ZoomMtg) return resolve(window.ZoomMtg);
        if (++attempts > 20) return reject(new Error('ZoomMtg not available'));
        setTimeout(check, 100);
      })();
    });
  };

  const initializeZoomMeeting = async () => {
    try {
      const ZoomMtg = await loadZoomSDK();

      ZoomMtg.setZoomJSLib('https://source.zoom.us/4.0.0/lib', '/av');
      ZoomMtg.preLoadWasm();
      ZoomMtg.prepareWebSDK();

      ZoomMtg.i18n.load('en-US');
      ZoomMtg.i18n.onLoad(() => {
        // Determine redirect URL based on user role
        const redirectUrl = role === '1'
          ? `${window.location.origin}/admin/classrooms`
          : `${window.location.origin}/user/AllClassroom`;

        console.log('Setting Zoom leaveUrl based on role:', role, '-> redirectUrl:', redirectUrl);

        ZoomMtg.init({
          leaveUrl: redirectUrl,
          disableCORP: !window.crossOriginIsolated,
          // Tell SDK which DOM node to control
          success: () => joinMeeting(ZoomMtg),
          error: (e) => {
            setError('Failed to initialize Zoom SDK: ' + (e.errorMessage || e.message));
            setLoading(false);
          }
        });
      });
    } catch (err) {
      setError('Zoom SDK init error: ' + err.message);
      setLoading(false);
    }
  };

  const joinMeeting = (ZoomMtg) => {
    let userLeftMeeting = false;

    // Listen for when user leaves meeting (this fires when "Leave Meeting" is clicked)
    ZoomMtg.inMeetingServiceListener('onUserLeave', (data) => {
      console.log('User leave event:', data);
      userLeftMeeting = true;
      console.log('User left meeting (meeting continues for others)');
      // The leaveUrl will handle the redirect automatically
    });

    // Listen for when meeting is ended by host (this fires when "End Meeting for All" is clicked)
    ZoomMtg.inMeetingServiceListener('onMeetingEnded', (data) => {
      console.log('Meeting ended by host event:', data);
      if (!userLeftMeeting) {
        console.log('Host ended meeting for all participants');
        handleMeetingEnd();
      }
    });

    // Add event listeners for meeting end events
    ZoomMtg.inMeetingServiceListener('onMeetingStatus', (data) => {
      console.log('Meeting status changed:', data);

      // Only call API if user didn't just leave the meeting
      if (data.meetingStatus === 3 && !userLeftMeeting) { // 3 = Meeting ended for all
        console.log('Meeting ended for all participants detected via Zoom SDK');
        handleMeetingEnd();
      } else if (data.meetingStatus === 3 && userLeftMeeting) {
        console.log('Meeting status changed due to user leaving - not calling API');
      }
    });

    ZoomMtg.join({
      meetingNumber: meetingNumber.replace(/\D/g, ''),
      signature,
      userName,
      userEmail: '',
      passWord: meetingPassword,
      tk: '',
      success: () => {
        setLoading(false);
        toast.success('Successfully joined the meeting!');
      },
      error: (e) => {
        setError('Failed to join meeting: ' + (e.errorMessage || 'Unknown error'));
        setLoading(false);
      }
    });
  };

  const handleGoBack = () => {
    const redirectPath = role === '1' ? '/admin/classrooms' : '/user/AllClassroom';
    navigate(redirectPath);
  };

  // Function to handle meeting end via API
  const handleMeetingEnd = async () => {
    if (!liveClassId) {
      console.warn('No live class ID available for ending meeting');
      return;
    }

    try {
      console.log('Meeting ended via Zoom controls, calling API to update database...');
      const response = await endZoomLiveClass({ live_class_id: liveClassId });
      console.log('Meeting end API response:', response);
      toast.success('Meeting ended successfully!');

      // Redirect to appropriate page after successful API call
      const redirectPath = role === '1' ? '/admin/classrooms' : '/user/AllClassroom';
      setTimeout(() => {
        navigate(redirectPath);
      }, 1000); // Small delay to show the success message

    } catch (error) {
      console.error('Error ending meeting via API:', error);
      // Don't show error toast as meeting is already ended on Zoom side
      // Still redirect to appropriate page even if API call fails
      const redirectPath = role === '1' ? '/admin/classrooms' : '/user/AllClassroom';
      setTimeout(() => {
        navigate(redirectPath);
      }, 1000);
    }
  };

  if (error) {
    return (
      <div className="container-fluid vh-100 d-flex align-items-center justify-content-center">
        <div className="text-center">
          <i className="fas fa-exclamation-triangle text-danger mb-4" style={{ fontSize: '48px' }} />
          <h4 className="text-danger mb-3">Meeting Error</h4>
          <p className="text-muted mb-4">{error}</p>
          <button className="btn btn-primary" onClick={handleGoBack}>Go Back</button>
        </div>
      </div>
    );
  }

  // React does not render anything; Zoom lives in #zoom-root
  return null;
};

export default function ZoomMeetingWithErrorBoundary() {
  return (
    <ZoomErrorBoundary>
      <ZoomMeeting />
    </ZoomErrorBoundary>
  );
}
