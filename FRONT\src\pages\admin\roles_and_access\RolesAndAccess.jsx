import React, { useState } from 'react';
import { Icon } from '@iconify/react';
import Users from './Users';
import Permission from './permission';
import { usePermissions } from '../../../context/PermissionsContext';
import { toast } from 'react-toastify';

function RolesAndAccess() {
  const { permissions } = usePermissions();
  const [activeTab, setActiveTab] = useState('users');

  const handleTabChange = (tabId) => {
    if (tabId === 'permissions' && !permissions.roles_and_access_user_view) {
      toast.warning("You don't have permission to view roles and permissions");
      return;
    }
    setActiveTab(tabId);
  };

  const tabs = [
    {
      id: 'users',
      label: 'Users',
      icon: 'fluent:people-24-regular',
      component: Users
    },
    {
      id: 'permissions',
      label: 'Roles & Permissions',
      icon: 'fluent:shield-lock-24-regular',
      component: Permission,
      disabled: !permissions.roles_and_access_permissions_view
    }
  ];

  return (
    <div className="container-fluid p-0">
      <div className="card border-0">
        {/* Tabs Navigation */}
        <div className="tab-header border-bottom">
          <ul className="nav nav-tabs">
            {tabs.map(tab => (
              <li className="nav-item" key={tab.id}>
                <button
                  className={`nav-link ${activeTab === tab.id ? 'active' : ''}`}
                  onClick={() => handleTabChange(tab.id)}
                  disabled={tab.disabled}
                  style={tab.disabled ? { cursor: 'not-allowed', opacity: 0.6 } : {}}
                >
                  <Icon icon={tab.icon} width="20" height="20" />
                  {tab.label}
                </button>
              </li>
            ))}
          </ul>
        </div>

        {/* Tab Content */}
        <div className="tab-content">
          {tabs.map(tab => (
            <div
              key={tab.id}
              className={`tab-pane ${activeTab === tab.id ? 'active' : ''}`}
            >
              {(!tab.disabled || tab.id === 'users') && <tab.component />}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

export default RolesAndAccess;
