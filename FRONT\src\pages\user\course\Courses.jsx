import React, { useState } from 'react';
import { Icon } from '@iconify/react';
import CourseTab from './CourseTab';
import MyCourseTab from './MyCourseTab';

function UserCourses() {
  
  const [activeTab, setActiveTab] = useState('my-courses');

  return (
    <div className="row">

      
      <div className="col-12 p-2.filter-popup">
        <div className="card border-0">
          {/* Tabs Navigation */}
          <div className="tab-header border-bottom mb-0">
            <ul className="nav nav-tabs">
              <li className="nav-item">
              <button
                  className={`nav-link d-flex align-items-center ${activeTab === 'my-courses' ? 'active' : ''}`}
                  onClick={() => setActiveTab('my-courses')}
                >
                  <Icon 
                    icon="fluent:library-24-regular" 
                    width="20" 
                    height="20" 
                    className="me-2"
                  />
                  My Courses
                </button>
              </li>
              <li className="nav-item">
             

                <button
                  className={`nav-link d-flex align-items-center ${activeTab === 'courses' ? 'active' : ''}`}
                  onClick={() => setActiveTab('courses')}
                >
                  <Icon 
                    icon="fluent:book-24-regular" 
                    width="20" 
                    height="20" 
                    className="me-2"
                  />
                  Courses
                </button>
              </li>
            </ul>
          </div>

          {/* Tab Content */}
          <div className="tab-content">
            <div className={`tab-pane p-0 ${activeTab === 'courses' ? 'active' : ''}`}>
              <div className="card-body p-0">
                <CourseTab />
              </div>
            </div>

            <div className={`tab-pane p-0 ${activeTab === 'my-courses' ? 'active' : ''}`}>
              <div className="card-body p-0">
                <MyCourseTab />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default UserCourses;
