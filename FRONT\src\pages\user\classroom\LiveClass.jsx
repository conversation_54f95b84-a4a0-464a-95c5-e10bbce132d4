import React, { useState, useEffect } from 'react';
import { Icon } from '@iconify/react';
import { toast } from 'react-toastify';
import { getTraineeLiveClasses, generateTraineeJoinToken } from '../../../services/userService';
import Loader from '../../../components/common/Loader';
import NoData from '../../../components/common/NoData';

function LiveClass({ classroom_id }) {
    const [liveClasses, setLiveClasses] = useState([]);
    const [loading, setLoading] = useState(true);
    const [joiningMeeting, setJoiningMeeting] = useState(null);
    
    // Search and filter state
    const [searchQuery, setSearchQuery] = useState('');
    const [statusFilter, setStatusFilter] = useState('');
    const [debouncedSearchQuery, setDebouncedSearchQuery] = useState('');
    
    // Pagination state
    const [currentPage, setCurrentPage] = useState(1);
    const [itemsPerPage, setItemsPerPage] = useState(9); // Initial 9 items
    const [totalPages, setTotalPages] = useState(1);
    const [totalRecords, setTotalRecords] = useState(0);
    const [hasMore, setHasMore] = useState(true);
    const [isInitialLoad, setIsInitialLoad] = useState(true);

    // Debounce search query to avoid too many API calls
    useEffect(() => {
        const timer = setTimeout(() => {
            setDebouncedSearchQuery(searchQuery);
        }, 500); // 500ms delay

        return () => clearTimeout(timer);
    }, [searchQuery]);

    useEffect(() => {
        if (classroom_id) {
            fetchLiveClasses();
        }
    }, [classroom_id, currentPage, itemsPerPage, debouncedSearchQuery, statusFilter]);

    const fetchLiveClasses = async () => {
        try {
            setLoading(true);
            const response = await getTraineeLiveClasses({ 
                classroom_id,
                page: currentPage,
                limit: itemsPerPage,
                search: debouncedSearchQuery,
                status: statusFilter
            });
            console.log('Live Classes Response:++++++++++++++++++++++++++++++++++', response);
            
            if (response.data) {
                const newLiveClasses = response.data.live_classes || response.data || [];
                
                // For pagination, replace data; for infinite scroll, append data
                if (currentPage === 1) {
                    setLiveClasses(newLiveClasses);
                    setIsInitialLoad(false);
                } else {
                    setLiveClasses(prev => [...prev, ...newLiveClasses]);
                }
                
                setTotalPages(response.data.pagination?.totalPages || 1);
                setTotalRecords(response.data.pagination?.totalRecords || 0);
                setHasMore(response.data.pagination?.hasNextPage || false);
            }
        } catch (error) {
            console.error('Error fetching live classes:', error);
            toast.error('Failed to fetch live classes');
        } finally {
            setLoading(false);
        }
    };

    const handleJoinMeeting = async (liveClass) => {
        try {
            setJoiningMeeting(liveClass.id);
const protocol = window.location.protocol;
const hostname = window.location.hostname;
const port = window.location.port;
const fullURL = `${protocol}//${hostname}${port ? `:${port}` : ''}`;    

            const response = await generateTraineeJoinToken({ live_class_id: liveClass.id , sub_domain : fullURL   });

            // Open the join URL in the same tab
            window.location.href = response.data.join_url;
            toast.success('Joining meeting...');
        } catch (error) {
            console.error('Error joining meeting:', error);
            toast.error('Failed to join meeting');
        } finally {
            setJoiningMeeting(null);
        }
    };

    const getStatusBadge = (liveClass) => {
        const commonStyles = {
            fontWeight: '600',
            borderWidth: '2px',
            padding: '6px 12px',
            borderRadius: '8px',
            fontSize: '0.875rem'
        };

        if (liveClass.is_ended === 1) {
            return (
                <span
                    className="text-danger border border-danger"
                    style={{ backgroundColor: '#fdeaea', ...commonStyles }}
                >
                    Ended
                </span>
            );
        } else if (liveClass.is_started === 1) {
            return (
                <span
                    className="text-success border border-success"
                    style={{ backgroundColor: '#e6f4ea', ...commonStyles }}
                >
                    Live
                </span>
            );
        } else {
            return (
                <span
                    className="text-primary border border-primary"
                    style={{ backgroundColor: '#e9f1ff', ...commonStyles }}
                >
                    Scheduled
                </span>
            );
        }
    };

    const formatDateTime = (dateTimeString) => {
        const date = new Date(dateTimeString);
        return date.toLocaleString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
            hour12: true
        });
    };

    const handleSearchChange = (e) => {
        setSearchQuery(e.target.value);
        setCurrentPage(1); // Reset to first page when search changes
        setItemsPerPage(9); // Reset to initial 9 items
        setIsInitialLoad(true);
    };

    const clearFilters = () => {
        setSearchQuery('');
        setStatusFilter('');
        setCurrentPage(1);
        setItemsPerPage(9); // Reset to initial 9 items
        setIsInitialLoad(true);
    };

    const handleLoadMore = () => {
        if (hasMore && !loading) {
            // After initial 9 items, load 12 items per page
            if (isInitialLoad && currentPage === 1) {
                setItemsPerPage(12);
            }
            setCurrentPage(prev => prev + 1);
        }
    };

    const isUpcoming = (startTime) => {
        return new Date(startTime) > new Date();
    };

    if (loading) {
        return <Loader caption="Loading live classes..." />;
    }

    return (
        <div className="container-fluid p-0">
{/* Search and Filter Section */}
<div className="row mb-4 mt-2">
  <div className="col-md-4">
    <div className="d-flex align-items-center gap-3">
      <div className="input-group">
        <input
          type="text"
          className="form-control"
          placeholder="Search live classes..."
          value={searchQuery}
          onChange={handleSearchChange}
        />
      </div>
      <select
        className="form-select"
        style={{ width: 'auto', minWidth: '150px' }}
        value={statusFilter}
        onChange={(e) => {
          setStatusFilter(e.target.value);
          setCurrentPage(1); // Reset to first page when filter changes
          setItemsPerPage(9); // Reset to initial 9 items
          setIsInitialLoad(true);
        }}
      >
        <option value="">All Status</option>
        <option value="scheduled">Scheduled</option>
        <option value="live">Live</option>
        <option value="ended">Ended</option>
      </select>
    </div>
  </div>
  <div className="col-md-8 d-flex justify-content-end align-items-center">
    <span className="text-muted">
      Total: {totalRecords} live classes
    </span>
  </div>
</div>



            {/* Live Classes Summary */}
            {liveClasses.length > 0 && (
                <div className="row my-4">
                    <div className="col-12">
                        <div className="card bg-light">
                            <div className="card-body">
                                <h6 className="card-title mb-3">
                                    <Icon icon="mdi:information" className="me-2" />
                                    Live Classes Summary
                                </h6>
                                <div className="row text-center">
                                    <div className="col-6 col-md-3" >
                                        <div className="h5 mb-1 text-primary">
                                            {liveClasses.filter(lc => lc.is_started === 1 && lc.is_ended === 0).length}
                                        </div>
                                        <small className="text-muted">Live Now</small>
                                    </div>
                                    <div className="col-6 col-md-3" >
                                        <div className="h5 mb-1 text-info">
                                            {liveClasses.filter(lc => lc.is_started === 0 && lc.is_ended === 0).length}
                                        </div>
                                        <small className="text-muted">Scheduled</small>
                                    </div>
                                    <div className="col-6 col-md-3" >
                                        <div className="h5 mb-1 text-secondary">
                                            {liveClasses.filter(lc => lc.is_ended === 1).length}
                                        </div>
                                        <small className="text-muted">Ended</small>
                                    </div>
                                    <div className="col-6 col-md-3" >
                                        <div className="h5 mb-1 text-dark">
                                            {totalRecords}
                                        </div>
                                        <small className="text-muted">Total</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            )}

            {/* Live Classes List */}
            {liveClasses.length === 0 ? (
                <div className="col-12 text-center py-5">
                    <Icon icon="fluent:video-24-regular" className="text-muted mb-3" width="48" height="48" />
                    <h5>No Live Classes Found</h5>
                    <p className="text-muted">
                        {searchQuery ? 'No live classes match your search criteria.' : 'No live classes available for this classroom.'}
                    </p>
                </div>
            ) : (
                <div className="row">
                    {liveClasses.map((liveClass) => (
                        <div key={liveClass.id} className="col-12 col-md-6 col-lg-4 mb-4">
                            <div className="card h-100 rounded-4 bg-white">
                                <div
                                    className="card-body d-flex flex-column p-4"
                                    style={{
                                        minHeight: '220px',
                                        maxHeight: '260px',
                                        overflow: 'hidden',
                                        display: 'flex',
                                        flexDirection: 'column',
                                        paddingBottom: 0
                                    }}
                                >
                                    <div className="d-flex justify-content-between align-items-start mb-2" style={{ minHeight: '40px' }}>
                                        <h6
                                            className="card-title mb-0 fw-bold fs-5"
                                            style={{
                                                maxWidth: '70%',
                                                whiteSpace: 'nowrap',
                                                overflow: 'hidden',
                                                textOverflow: 'ellipsis',
                                                wordBreak: 'break-word',
                                                letterSpacing: '0.5px'
                                            }}
                                            title={liveClass.live_class_name}
                                        >
                                            {liveClass.live_class_name}
                                        </h6>
                                        <span className="ms-2 align-self-center" style={{ fontSize: '0.85rem' }}>
                                            {getStatusBadge(liveClass)}
                                        </span>
                                    </div>

                                    {liveClass.live_class_description && (
                                        <p
                                            className="card-text text-muted small mb-2 px-2 py-1 bg-light rounded-2"
                                            style={{
                                                wordBreak: 'break-word',
                                                marginBottom: 0,
                                                display: '-webkit-box',
                                                WebkitLineClamp: 3,
                                                WebkitBoxOrient: 'vertical',
                                                overflow: 'hidden',
                                                textOverflow: 'ellipsis'
                                            }}
                                            title={liveClass.live_class_description}
                                        >
                                            {liveClass.live_class_description}
                                        </p>
                                    )}

                                    {/* Details always at the bottom */}
                                    <div className="mt-auto pt-2">
                                        <small className="text-muted d-block mb-1">
                                            <Icon icon="mdi:calendar" className="me-1" />
                                            {formatDateTime(liveClass.start_time)}
                                        </small>
                                        <small className="text-muted d-block mb-1">
                                            <Icon icon="mdi:clock" className="me-1" />
                                            Duration: {liveClass.duration}
                                        </small>
                                        {liveClass.created_by_name && (
                                            <small className="text-muted d-block mb-1">
                                                <Icon icon="mdi:account" className="me-1" />
                                                Instructor: {liveClass.created_by_name}
                                            </small>
                                        )}
                                    </div>
                                </div>

                                <div className="card-footer bg-transparent border-top text-center">
                                    {liveClass.status === 'ended' || liveClass.is_ended === 1 ? (
                                        <span className="text-danger small d-flex align-items-center justify-content-center gap-1 fw-semibold">
                                            <Icon icon="mdi:close-circle" className="me-1" />
                                            Meeting has ended
                                        </span>
                                    ) : liveClass.is_started ? (
                                        <button
                                            className="btn btn-success w-100 fw-semibold"
                                            onClick={() => handleJoinMeeting(liveClass)}
                                            disabled={joiningMeeting === liveClass.id}
                                        >
                                            {joiningMeeting === liveClass.id ? (
                                                <>
                                                    <span className="spinner-border spinner-border-sm me-2"></span>
                                                    Joining...
                                                </>
                                            ) : (
                                                <>
                                                    <Icon icon="mdi:video" className="me-2" />
                                                    Join Meeting
                                                </>
                                            )}
                                        </button>
                                    ) : isUpcoming(liveClass.start_time) ? (
                                        <span className="text-muted small d-flex align-items-center justify-content-center gap-1">
                                            <Icon icon="mdi:clock-outline" className="me-1" />
                                            Meeting not started yet
                                        </span>
                                    ) : (
                                        <span className="text-warning small d-flex align-items-center justify-content-center gap-1">
                                            <Icon icon="mdi:alert" className="me-1" />
                                            Meeting should have started
                                        </span>
                                    )}
                                </div>
                            </div>
                        </div>
                    ))}
                </div>
            )}

            {/* Load More Button */}
            {hasMore && liveClasses.length > 0 && (
                <div className="row mt-4">
                    <div className="col-12 text-center">
                        <button
                            className="btn btn-outline-primary"
                            onClick={handleLoadMore}
                            disabled={loading}
                        >
                            {loading ? (
                                <>
                                    <span className="spinner-border spinner-border-sm me-2"></span>
                                    Loading...
                                </>
                            ) : (
                                <>
                                    <Icon icon="mdi:plus" className="me-2" />
                                    Load More Live Classes
                                </>
                            )}
                        </button>
                        <div className="mt-2">
                            <small className="text-muted">
                                Showing {liveClasses.length} of {totalRecords} live classes
                            </small>
                        </div>
                    </div>
                </div>
            )}

            {/* No More Data Message */}
            {!hasMore && liveClasses.length > 0 && (
                <div className="row mt-4">
                    <div className="col-12 text-center">
                        <p className="text-muted">
                            <Icon icon="mdi:check-circle" className="me-2" />
                            You've seen all live classes
                        </p>
                    </div>
                </div>
            )}


        </div>
    );
}

export default LiveClass;
