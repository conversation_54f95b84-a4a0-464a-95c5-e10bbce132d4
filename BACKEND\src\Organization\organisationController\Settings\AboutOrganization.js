const { mysqlServerConnection } = require('../../../db/db');

const getAboutOrganization = async (req, res) => {
  console.log("📡 [API] getAboutOrganization called");

  try {
    const { page = 1, limit = 10, search = "" } = req.body;
    const dbName = req.user.db_name;
    const offset = (page - 1) * limit;

    console.log("🏢 Database:", dbName);
    console.log("📄 Page:", page, "Limit:", limit, "Search:", search);

    // Build search condition
    let searchCondition = "";
    let queryParams = [];

    if (search && search.trim() !== "") {
      searchCondition = "AND (name LIKE ? OR description LIKE ?)";
      queryParams.push(`%${search}%`, `%${search}%`);
    }

    // Get total count for pagination
    const countQuery = `
      SELECT COUNT(*) as total
      FROM ${dbName}.orginfo
      WHERE is_deleted = 0 ${searchCondition}
    `;
    const [countResult] = await mysqlServerConnection.query(countQuery, queryParams);
    const totalRecords = countResult[0].total;

    // Get paginated data
    const dataQuery = `
      SELECT id, name, description, is_active, createdAt, updatedAt
      FROM ${dbName}.orginfo
      WHERE is_deleted = 0 ${searchCondition}
      ORDER BY createdAt DESC
      LIMIT ? OFFSET ?
    `;
    const dataParams = [...queryParams, limit, offset];
    const [result] = await mysqlServerConnection.query(dataQuery, dataParams);

    console.log("✅ Found", result.length, "organization info records");

    return res.status(200).json({
      success: true,
      status: 200,
      data: {
        records: result,
        pagination: {
          currentPage: page,
          totalPages: Math.ceil(totalRecords / limit),
          totalRecords: totalRecords,
          recordsPerPage: limit
        }
      },
      message: result.length > 0 ? "Organization information fetched successfully" : "No organization information found"
    });

  } catch (err) {
    console.error("❌ Error in getAboutOrganization:", err);
    return res.status(500).json({
      success: false,
      status: 500,
      message: "Server error while fetching organization information",
      error: err.message
    });
  }
};

const addAboutOrganization = async (req, res) => {
  console.log("📡 [API] addAboutOrganization called");

  try {
    const { name, description } = req.body;
    const dbName = req.user.db_name;
    const created_by = req.user.id;

    console.log("📝 Adding organization info:", { name, description, created_by });

    // Validate required fields
    if (!name || !description) {
      return res.status(400).json({
        success: false,
        status: 400,
        message: "Name and description are required fields"
      });
    }


    // Insert new organization info
    const [result] = await mysqlServerConnection.query(
      `INSERT INTO ${dbName}.orginfo (created_by, name, description, is_active, is_deleted, createdAt, updatedAt)
       VALUES (?, ?, ?, 1, 0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)`,
      [created_by, name, description]
    );

    console.log("✅ Organization info added successfully with ID:", result.insertId);

    return res.status(201).json({
      success: true,
      status: 201,
      data: {
        id: result.insertId,
        name,
        description,
        is_active: true
      },
      message: "Organization information added successfully"
    });

  } catch (err) {
    console.error("❌ Error in addAboutOrganization:", err);
    return res.status(500).json({
      success: false,
      status: 500,
      message: "Server error while adding organization information",
      error: err.message
    });
  }
};

const editAboutOrganization = async (req, res) => {
  console.log("📡 [API] editAboutOrganization called");

  try {
    const { id, name, description } = req.body;
    const dbName = req.user.db_name;

    console.log("✏️ Editing organization info:", { id, name, description });

    // Validate required fields
    if (!id || !name || !description) {
      return res.status(400).json({
        success: false,
        status: 400,
        message: "ID, name and description are required fields"
      });
    }

    // Check if record exists and is not deleted
    const [existingRecord] = await mysqlServerConnection.query(
      `SELECT id, name FROM ${dbName}.orginfo WHERE id = ? AND is_deleted = 0`,
      [id]
    );

    if (existingRecord.length === 0) {
      return res.status(404).json({
        success: false,
        status: 404,
        message: "Organization information not found or has been deleted"
      });
    }


    // Update the record
    const [result] = await mysqlServerConnection.query(
      `UPDATE ${dbName}.orginfo
       SET name = ?, description = ?, updatedAt = CURRENT_TIMESTAMP
       WHERE id = ? AND is_deleted = 0`,
      [name, description, id]
    );

    if (result.affectedRows === 0) {
      return res.status(404).json({
        success: false,
        status: 404,
        message: "Organization information not found or could not be updated"
      });
    }

    console.log("✅ Organization info updated successfully");

    return res.status(200).json({
      success: true,
      status: 200,
      data: {
        id,
        name,
        description
      },
      message: "Organization information updated successfully"
    });

  } catch (err) {
    console.error("❌ Error in editAboutOrganization:", err);
    return res.status(500).json({
      success: false,
      status: 500,
      message: "Server error while updating organization information",
      error: err.message
    });
  }
};

const statusAboutOrganization = async (req, res) => {
  console.log("📡 [API] statusAboutOrganization called");

  try {
    const { id } = req.params;
    const { status } = req.body;
    const dbName = req.user.db_name;

    console.log("🔄 Updating organization info status:", { id, status });

    // Validate required fields
    if (!id || status === undefined) {
      return res.status(400).json({
        success: false,
        status: 400,
        message: "ID and status are required fields"
      });
    }

    // Validate status value (should be boolean or 0/1)
    const isActiveStatus = status === true || status === 1 || status === "1";

    // Check if record exists and is not deleted
    const [existingRecord] = await mysqlServerConnection.query(
      `SELECT id, name, is_active FROM ${dbName}.orginfo WHERE id = ? AND is_deleted = 0`,
      [id]
    );

    if (existingRecord.length === 0) {
      return res.status(404).json({
        success: false,
        status: 404,
        message: "Organization information not found or has been deleted"
      });
    }

    // Update the status
    const [result] = await mysqlServerConnection.query(
      `UPDATE ${dbName}.orginfo
       SET is_active = ?, updatedAt = CURRENT_TIMESTAMP
       WHERE id = ? AND is_deleted = 0`,
      [isActiveStatus ? 1 : 0, id]
    );

    if (result.affectedRows === 0) {
      return res.status(404).json({
        success: false,
        status: 404,
        message: "Organization information not found or could not be updated"
      });
    }

    const statusMessage = isActiveStatus ? "activated" : "deactivated";
    console.log(`✅ Organization info ${statusMessage} successfully`);

    return res.status(200).json({
      success: true,
      status: 200,
      data: {
        id,
        is_active: isActiveStatus
      },
      message: `Organization information has been ${statusMessage} successfully`
    });

  } catch (err) {
    console.error("❌ Error in statusAboutOrganization:", err);
    return res.status(500).json({
      success: false,
      status: 500,
      message: "Server error while updating organization information status",
      error: err.message
    });
  }
};

const deleteAboutOrganization = async (req, res) => {
  console.log("📡 [API] deleteAboutOrganization called");

  try {
    const { id } = req.params || req.body;
    const dbName = req.user.db_name;

    console.log("🗑️ Deleting organization info with ID:", id);

    // Validate required fields
    if (!id) {
      return res.status(400).json({
        success: false,
        status: 400,
        message: "ID is required"
      });
    }

    // Check if record exists and is not already deleted
    const [existingRecord] = await mysqlServerConnection.query(
      `SELECT id, name FROM ${dbName}.orginfo WHERE id = ? AND is_deleted = 0`,
      [id]
    );

    if (existingRecord.length === 0) {
      return res.status(404).json({
        success: false,
        status: 404,
        message: "Organization information not found or has already been deleted"
      });
    }

    // Soft delete the record
    const [result] = await mysqlServerConnection.query(
      `UPDATE ${dbName}.orginfo
       SET is_deleted = 1, updatedAt = CURRENT_TIMESTAMP
       WHERE id = ? AND is_deleted = 0`,
      [id]
    );

    if (result.affectedRows === 0) {
      return res.status(404).json({
        success: false,
        status: 404,
        message: "Organization information not found or could not be deleted"
      });
    }

    console.log("✅ Organization info deleted successfully");

    return res.status(200).json({
      success: true,
      status: 200,
      message: "Organization information deleted successfully"
    });

  } catch (err) {
    console.error("❌ Error in deleteAboutOrganization:", err);
    return res.status(500).json({
      success: false,
      status: 500,
      message: "Server error while deleting organization information",
      error: err.message
    });
  }
};

module.exports = {
  getAboutOrganization,
  addAboutOrganization,
  editAboutOrganization,
  statusAboutOrganization,
  deleteAboutOrganization,
};
