const { json } = require("body-parser");
const { mysqlServerConnection } = require("../../../db/db");
const CustomError = require("../../../middleware/error");
const { deleteFileIfExists } = require("../../../tools/tools");
const XLSX = require("xlsx");
const fs = require("fs");
const path = require("path");
const {
  uploadDocToS3,
  uploadVideoToS3,
  uploadImageToS3,
  uploadAudioToS3,
} = require("../../../tools/aws");

const currentPath = "../../../app";

// Not in user
const getAllCourses = async (req, res) => {
  try {
    const dbName = req.user.db_name;
    const userId = req.user.id;
    const courseId = req.query.courseId || null;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const search = req.query.search || null;
    const statusFilter = (req.query.status || "all").toLowerCase();

    // ------------------------------
    // 1. Fetch specific course by ID
    // ------------------------------
    if (courseId) {
      const courseQuery = `
        SELECT 
          c.*,
          c.course_subcategory,
          c.rejected_note               AS course_rejected_note,
          ct.template_url,
          ct.image_url                  AS certificate_image_url,
          ca.is_rejected                AS approval_is_rejected,
          ca.rejected_note              AS approval_rejected_note
        FROM ${dbName}.courses AS c
        LEFT JOIN ${dbName}.certificate_templates AS ct 
          ON c.certificate_template_id = ct.id
        LEFT JOIN ${dbName}.course_approval AS ca 
          ON c.id = ca.course_id 
          AND ca.user_id = ?
        WHERE c.id = ?
          AND c.is_deleted = FALSE
      `;
      const [courseRows] = await mysqlServerConnection.query(courseQuery, [
        userId,
        courseId,
      ]);

      if (courseRows.length === 0) {
        return res.status(404).json({
          success: false,
          message: "Course not found",
        });
      }

      const courseData = courseRows[0];

      // Determine course_status from `courses` columns
      let courseStatus = "pending";
      if (courseData.is_approved === 1) courseStatus = "approved";
      else if (courseData.is_rejected === 1) courseStatus = "rejected";
      else if (courseData.send_request === 1)
        courseStatus = "waiting_for_response";

      // Parse JSON‐stringified `course_subcategory`
      let includesArray = [];
      if (courseData.course_subcategory) {
        try {
          includesArray = JSON.parse(courseData.course_subcategory);
        } catch {
          includesArray = [];
        }
      }

      return res.status(200).json({
        success: true,
        data: {
          ...courseData,
          course_subcategory: includesArray,
          // Return the note from `courses.rejected_note`
          rejected_note: courseData.course_rejected_note || null,
          course_status: courseStatus,
          // has_approval_request is true if any of those flags in `courses` indicate a request or decision
          has_approval_request:
            courseData.send_request === 1 ||
            courseData.is_rejected === 1 ||
            courseData.is_approved === 1,
        },
      });
    }

    // -------------------------------------------------
    // 2. Pagination & search for “all courses” (list view)
    // -------------------------------------------------
    const offset = (page - 1) * limit;

    let query = `
      SELECT 
        c.id,
        c.created_by,
        c.banner_image,
        c.course_name,
        c.course_desc,
        c.course_type,
        c.is_active,
        c.is_approved,
        c.is_rejected,
        c.send_request,
        c.course_subcategory,
        c.rejected_note                  AS course_rejected_note,
        ca.is_rejected                   AS approval_is_rejected,
        ca.rejected_note                 AS approval_rejected_note
      FROM ${dbName}.courses AS c
      LEFT JOIN ${dbName}.course_approval AS ca 
        ON c.id = ca.course_id 
        AND ca.user_id = ?
      WHERE c.is_deleted = FALSE
    `;
    const queryParams = [userId];

    // 2.a. Apply text‐search if provided
    if (search) {
      query += ` AND c.course_name LIKE ? `;
      queryParams.push(`%${search}%`);
    }

    // 2.b. Apply status filter if not "all"
    if (statusFilter !== "all") {
      switch (statusFilter) {
        case "pending":
          query += ` AND c.is_approved = 0 AND c.is_rejected = 0 AND c.send_request = 0 `;
          break;
        case "rejected":
          query += ` AND c.is_rejected = 1 `;
          break;
        case "waiting_for_response":
          query += ` AND c.send_request = 1 AND c.is_rejected = 0 AND c.is_approved = 0 `;
          break;
        case "approved":
          query += ` AND c.is_approved = 1 `;
          break;
        default:
          // unknown status → no extra filter
          break;
      }
    }

    // 2.c. Add “latest first” ordering, then LIMIT/OFFSET
    query += ` ORDER BY c.createdAt DESC LIMIT ? OFFSET ? `;
    queryParams.push(limit, offset);

    const [courses] = await mysqlServerConnection.query(query, queryParams);

    // -------------------------------
    // 3. Get total count for pagination
    // -------------------------------
    let countQuery = `
      SELECT COUNT(*) AS totalCourses
      FROM ${dbName}.courses AS c
      WHERE c.is_deleted = FALSE
    `;
    const countParams = [];

    if (search) {
      countQuery += ` AND c.course_name LIKE ? `;
      countParams.push(`%${search}%`);
    }

    if (statusFilter !== "all") {
      switch (statusFilter) {
        case "pending":
          countQuery += ` AND c.is_approved = 0 AND c.is_rejected = 0 AND c.send_request = 0 `;
          break;
        case "rejected":
          countQuery += ` AND c.is_rejected = 1 `;
          break;
        case "waiting_for_response":
          countQuery += ` AND c.send_request = 1 AND c.is_rejected = 0 AND c.is_approved = 0 `;
          break;
        case "approved":
          countQuery += ` AND c.is_approved = 1 `;
          break;
        default:
          // no extra filter
          break;
      }
    }

    const [coursesCount] = await mysqlServerConnection.query(
      countQuery,
      countParams
    );

    // ------------------------------------------------------------
    // 4. Format each course: parse `course_subcategory` + set status
    // ------------------------------------------------------------
    const formattedCourses = courses.map((course) => {
      let courseStatus = "pending";
      if (course.is_approved === 1) courseStatus = "approved";
      else if (course.is_rejected === 1) courseStatus = "rejected";
      else if (course.send_request === 1) courseStatus = "waiting_for_response";

      let includesArray = [];
      if (course.course_subcategory) {
        try {
          includesArray = JSON.parse(course.course_subcategory);
        } catch {
          includesArray = [];
        }
      }

      return {
        ...course,
        course_subcategory: includesArray,
        rejected_note: course.course_rejected_note || null,
        course_status: courseStatus,
        has_approval_request:
          course.send_request === 1 ||
          course.is_rejected === 1 ||
          course.is_approved === 1,
      };
    });

    // -----------------------
    // 5. Return final payload
    // -----------------------
    return res.status(200).json({
      success: true,
      data: {
        courses: formattedCourses,
        pagination: {
          page: page,
          limit: limit,
          totalPages: Math.ceil(coursesCount[0].totalCourses / limit),
          totalCount: coursesCount[0].totalCourses,
        },
      },
    });
  } catch (error) {
    console.error("Error fetching courses:", error);
    return res.status(500).json({
      success: false,
      message: "Failed to fetch courses",
      error: error.message,
    });
  }
};


// Get all course 
const getAllCoursesAPI = async (req, res) => {
  try {
    const dbName = req.user.db_name;

    // Get all courses
    const sql = `
      SELECT
        c.id,
        c.created_by,
        c.banner_image,
        c.course_name,
        c.course_desc,
        c.course_price,
        c.course_type,
        c.course_category,
        c.course_subcategory,
        c.certificate_template_id,
        c.validity,
        c.total_rating,
        c.subscribers,
        c.is_active,
        c.is_approved,
        c.send_request,
        c.is_rejected,
        c.rejected_note,
        c.createdAt,
        c.updatedAt
      FROM ${dbName}.courses AS c
      WHERE c.is_deleted = FALSE
      ORDER BY c.createdAt DESC
    `;

    const [rows] = await mysqlServerConnection.query(sql);

    const all = [];
    const waiting_for_approve = [];
    const approved = [];
    const rejected = [];
    const pending = [];

    rows.forEach((raw) => {
      let parsedSubcats = [];
      if (raw.course_subcategory) {
        try {
          parsedSubcats = JSON.parse(raw.course_subcategory);
        } catch {
          parsedSubcats = [];
        }
      }

      let courseStatus = "pending";
      if (raw.is_approved === 1) {
        courseStatus = "approved";
      } else if (raw.is_rejected === 1) {
        courseStatus = "rejected";
      } else if (raw.send_request === 1) {
        courseStatus = "waiting_for_approval";
      }

      const courseObj = {
        id: raw.id,
        created_by: raw.created_by,
        banner_image: raw.banner_image,
        course_name: raw.course_name,
        course_desc: raw.course_desc,
        course_price: raw.course_price,
        course_type: raw.course_type,
        course_category: raw.course_category,
        course_subcategory: parsedSubcats,
        certificate_template_id: raw.certificate_template_id,
        validity: raw.validity,
        total_rating: raw.total_rating,
        subscribers: raw.subscribers,
        is_active: raw.is_active,
        is_approved: raw.is_approved,
        send_request: raw.send_request,
        is_rejected: raw.is_rejected,
        rejected_note: raw.rejected_note,
        createdAt: raw.createdAt,
        updatedAt: raw.updatedAt,
        course_status: courseStatus,
        course_summary: {} // placeholder, will be filled later
      };

      all.push(courseObj);

      if (raw.is_approved === 1) {
        approved.push(courseObj);
      } else if (raw.is_rejected === 1) {
        rejected.push(courseObj);
      } else if (raw.send_request === 1) {
        waiting_for_approve.push(courseObj);
      } else if (
        raw.send_request === 0 &&
        raw.is_approved === 0 &&
        raw.is_rejected === 0
      ) {
        pending.push(courseObj);
      }
    });

    // Fetch content summary (videos, docs, assessments, etc.)
    const summarySQL = `
      SELECT
        c.id AS course_id,
        IFNULL(v.video_count, 0) AS total_videos,
        IFNULL(doc.doc_count, 0) AS total_documents,
        IFNULL(a.assessment_count, 0) AS total_assessments,
        IFNULL(s.survey_count, 0) AS total_surveys,
        SEC_TO_TIME(IFNULL(v.total_duration, 0)) AS total_duration,
        IFNULL(mc.purchase_count, 0) AS total_purchased,
        ROUND(IFNULL(cr.avg_rating, 0), 1) AS average_rating
      FROM ${dbName}.courses c
      LEFT JOIN (
        SELECT course_id, COUNT(*) AS video_count, SUM(TIME_TO_SEC(video_duration)) AS total_duration
        FROM ${dbName}.videos
        WHERE is_deleted = 0 AND is_active = 1
        GROUP BY course_id
      ) v ON v.course_id = c.id
      LEFT JOIN (
        SELECT course_id, COUNT(*) AS doc_count
        FROM ${dbName}.documents
        WHERE is_deleted = 0 AND is_active = 1
        GROUP BY course_id
      ) doc ON doc.course_id = c.id
      LEFT JOIN (
        SELECT course_id, COUNT(*) AS assessment_count
        FROM ${dbName}.assessments
        WHERE is_deleted = 0 AND is_active = 1 AND assessment_type = 'assessment'
        GROUP BY course_id
      ) a ON a.course_id = c.id
      LEFT JOIN (
        SELECT course_id, COUNT(*) AS survey_count
        FROM ${dbName}.assessments
        WHERE is_deleted = 0 AND is_active = 1 AND assessment_type = 'survey'
        GROUP BY course_id
      ) s ON s.course_id = c.id
      LEFT JOIN (
        SELECT course_id, COUNT(*) AS purchase_count
        FROM ${dbName}.mycourses
        GROUP BY course_id
      ) mc ON mc.course_id = c.id
      LEFT JOIN (
        SELECT course_id, AVG((class_rating + mentor_rating)/2) AS avg_rating
        FROM ${dbName}.course_reviews
        GROUP BY course_id
      ) cr ON cr.course_id = c.id
      WHERE c.is_deleted = 0
    `;

    const [summary] = await mysqlServerConnection.query(summarySQL);

    // Map summary to course id
    const summaryMap = {};
    summary.forEach((s) => {
      summaryMap[s.course_id] = {
        total_videos: s.total_videos,
        total_documents: s.total_documents,
        total_assessments: s.total_assessments,
        total_surveys: s.total_surveys,
        total_duration: s.total_duration,
        total_purchased: s.total_purchased,
        average_rating: s.average_rating
      };
    });

    // Attach to each course
    all.forEach((course) => {
      course.course_summary = summaryMap[course.id] || {
        total_videos: 0,
        total_documents: 0,
        total_assessments: 0,
        total_surveys: 0,
        total_duration: "00:00:00",
        total_purchased: 0,
        average_rating: 0
      };
    });

    return res.status(200).json({
      success: true,
      message: "Fetched course approval data",
      data: {
        all,
        approved,
        rejected,
        waiting_for_approve,
        pending
      }
    });
  } catch (error) {
    console.error("Error in getAllCoursesAPI:", error);
    return res.status(500).json({
      success: false,
      message: "Failed to fetch courses",
      error: error.message
    });
  }
};


// Delete course API
const deleteCourse = async (req, res, next) => {
  try {
    const { course_id } = req.params;
    const dbName = req.user.db_name;

    console.log("🗑️ Deleting course with ID:", course_id);

    const [result] = await mysqlServerConnection.query(
      `UPDATE ${dbName}.courses SET is_deleted = TRUE WHERE id = ?`,
      [course_id]
    );

    console.log("✅ Delete Result:", result);

    if (result.affectedRows === 0) {
      return res.status(404).json({
        success: false,
        message: "Course not found or already deleted.",
      });
    }

    return res.status(200).json({
      success: true,
      message: "Course deleted successfully.",
    });
  } catch (error) {
    console.error("❌ Error deleting course:", error);
    return next({
      statusCode: 500,
      message: error.message || "Internal Server Error",
    });
  }
};


const getAssignmentsByModule = async (req, res, next) => {
  try {
    const { module_id } = req.params;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const offset = (page - 1) * limit;

    console.log("assignments", module_id);

    // Fetch assignments with student count, filtered by module_id and paginated
    const [assignments] = await mysqlServerConnection.query(
      `SELECT a.*, COUNT(au.user_id) AS student_count 
             FROM ${req.user.db_name}.assignments a
             LEFT JOIN ${req.user.db_name}.assignment_user au ON a.id = au.assignment_id
             WHERE a.module_id = ? AND is_deleted = false
             GROUP BY a.id
             LIMIT ? OFFSET ?`,
      [module_id, limit, offset]
    );

    // Count total assignments to calculate total pages
    const [[{ total }]] = await mysqlServerConnection.query(
      `SELECT COUNT(*) as total FROM ${req.user.db_name}.assignments WHERE module_id = ?`,
      [module_id]
    );

    // If no assignments are found
    if (!assignments || assignments.length === 0) {
      return next({ statusCode: 404, message: "No assignments found" });
    }

    // Calculate total pages
    const totalPages = Math.ceil(total / limit);
    console.log("assignments", assignments);

    // Send paginated response
    res.status(200).json({
      success: true,
      data: {
        assignments,
        pagination: {
          page,
          limit,
          totalPages,
          totalCount: total,
        },
      },
    });
  } catch (error) {
    return next({
      statusCode: 500,
      message: error.message || "Internal Server Error",
    });
  }
};

const createAssignment = async (req, res) => {
  try {
    const assignmentData = req.body;
    const file = req.file;

    // console.log(file["attachment_file"]);
    // const doc = file["attachment_file"] !== undefined ? "/uploads/" + file["attachment_file"][0].filename : null;
    // console.log(doc);

    let doc = null;
    if (file) {
      const filedata = await uploadDocToS3(file.buffer, file.originalname);
      doc = filedata.path;
    }
    console.log("Doc:---------------", doc);

    if (Object.keys(assignmentData).length == 0) {
      return res.status(400).json({
        success: false,
        data: {
          error_msg: "Following fields are required",
          response: {
            assignment_name: "Assignment name is required",
            question_details: "Question details is required",
            course_id: "Course id is required",
            module_id: "Module id is required",
            total_content: "",
            attachment_file: "",
          },
        },
      });
    } else {
      await mysqlServerConnection.query(
        `INSERT INTO ${req.user.db_name}.assignments (
                created_by,
                assignment_name,
                question_details,
                course_id,
                module_id,
                instruction_file_url,
                serial_no
            ) VALUES (?,?,?,?,?,?,?)`,
        [
          req.user.userId,
          assignmentData.assignment_name,
          assignmentData.question_details,
          assignmentData.course_id,
          assignmentData.module_id,
          doc,
          assignmentData.total_content,
        ]
      );

      res.status(200).json({
        success: true,
        message: "assignment created successfully",
      });
    }
  } catch (error) {
    console.log(error.message);
    return res.status(500).json({
      success: false,
      error_msg: error.message || "Internal Server Error",
    });
  }
};

const updateAssignment = async (req, res, next) => {
  try {
    const assignmentData = req.body;
    const { assignment_id } = req.params;
    const file = req.files["attachment_file"] || null;
    console.log(file);
    const [filePath] = await mysqlServerConnection.query(
      `SELECT instruction_file_url FROM ${req.user.db_name}.assignments WHERE id = ?`,
      [assignment_id]
    );
    if (file && filePath[0] && filePath[0].instruction_file_url) {
      deleteFileIfExists(filePath[0].instruction_file_url);
    }
    await mysqlServerConnection.query(
      `UPDATE ${req.user.db_name}.assignments
             SET 
                 assignment_name = ?,
                 question_details = ?,
                 course_id = ?,
                 module_id = ?,
                 instruction_file_url = ?,
                 updatedAt = CURRENT_TIMESTAMP
             WHERE id = ?`,
      [
        assignmentData.assignment_name,
        assignmentData.question_details,
        assignmentData.course_id,
        assignmentData.module_id,
        file
          ? req.files["attachment_file"][0].path
          : filePath[0].instruction_file_url,
        assignment_id,
      ]
    );

    const [updatedAssignment] = await mysqlServerConnection.query(
      `SELECT * FROM ${req.user.db_name}.assignments WHERE id = ?`,
      [assignment_id]
    );

    res.status(200).json({
      success: true,
      data: updatedAssignment[0],
    });
  } catch (error) {
    console.error(error);
    return next({
      statusCode: 500,
      message: error.message || "Internal Server Error",
    });
  }
};

// const deleteAssignment = async (req, res, next) => {
//     try {
//         const { assignment_id } = req.params;
//         const dbName = req.user.db_name;
//         console.log("deleting assignment", assignment_id);
//         const query = `UPDATE ${dbName}.assignments SET is_deleted = 1 WHERE id = ?`;
//         await mysqlServerConnection.query(query, [assignment_id]);

//         res.status(200).json({
//             success: true,
//             message: `Module with ID ${assignment_id} marked as deleted successfully`,
//         });
//     } catch (error) {
//         console.error('Error marking module as deleted:', error);
//         return next({
//             statusCode: 500,
//             message: error.message || 'Internal Server Error',
//         });
//     }
// }

const editModule = async (req, res, next) => {
  try {
    const { module_id } = req.params;
    const { module_name, module_desc } = req.body;

    if (!module_id) {
      return next({ statusCode: 404, message: "Please provide Module Id" });
    }
    const [data] = await mysqlServerConnection.query(
      `SELECT * FROM ${req.user.db_name}.modules WHERE id = ?`,
      [module_id]
    );

    if (data.length === 0) {
      return next({ statusCode: 404, message: "Module not found" });
    }
    await mysqlServerConnection.query(
      `UPDATE ${req.user.db_name}.modules 
             SET module_name = ?, module_desc = ? 
             WHERE id = ?`,
      [module_name, module_desc, module_id]
    );

    res.status(200).json({
      success: true,
      message: "Module data updated successfully",
    });
  } catch (error) {
    return next({
      statusCode: 500,
      message: error.message || "Internal Server Error",
    });
  }
};

const deleteModule = async (req, res, next) => {
  try {
    const { course_id, module_id } = req.params;
    const dbName = req.user.db_name;
    console.log("DeLETE:", course_id, module_id);

    // const query = `UPDATE ${dbName}.modules SET is_deleted = 1 WHERE id = ? AND course_id = ?`;
    const query = `DELETE FROM ${dbName}.modules WHERE id = ? AND course_id = ?`;
    await mysqlServerConnection.query(query, [module_id, course_id]);

    res.status(200).json({
      success: true,
      message: `Module with ID ${module_id} marked as deleted successfully`,
    });
  } catch (error) {
    console.error("Error marking module as deleted:", error);
    return next({
      statusCode: 500,
      message: "Internal Server Error",
    });
  }
};

const moduleActiveDeactive = async (req, res, next) => {
  try {
    const { course_id, module_id } = req.params;
    const { status } = req.body;

    await mysqlServerConnection.query(
      `UPDATE ${req.user.db_name}.modules SET is_active = ? where id = ? AND course_id = ?`,
      [status, module_id, course_id]
    );
    res.status(200).json({
      success: true,
      message: `Module ${
        status === 1 ? "Activated" : "Deactivated"
      } Successfully`,
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      error_msg: error.message || "Internal Server Error",
    });
  }
};

const createAssessment = async (req, res) => {
  try {
    const body = req.body;
    const userId = req.user.userId;
    const db = req.user.db_name;

    console.log("📥 Incoming Request Body:", body);
    console.log("👤 User ID:", userId);
    console.log("🏢 Database:", db);

    const requiredFields = {
      assessment_name: "",
      course_id: "",
      module_id: "",
      duration: "",
      earn_point: "",
      earn_point_applicable_after: "",
      assessment_type: "", // Fix spelling
      total_content: "",
      pass_percentage: "",
    };

    // ✅ FIX: Validate required fields even if value is 0
    for (const field of Object.keys(requiredFields)) {
      if (
        body[field] === undefined ||
        body[field] === null ||
        (typeof body[field] === "string" && body[field].trim() === "")
      ) {
        console.warn(`⚠️ Missing required field: ${field}`);
        return res.status(400).json({
          success: false,
          data: {
            error_msg: `Missing field: ${field}`,
            requiredFields,
          },
        });
      }
    }

    console.log("📌 Assessment Type:", body.assessment_type);

    if (body.assessment_type === "assessment") {
      console.log("🛠 Inserting Assessment...");
      const [result] = await mysqlServerConnection.query(
        `INSERT INTO ${db}.assessments 
          (created_by, assessment_name, course_id, module_id, duration, earn_point, earn_point_applicable_after, pass_percentage, is_active, is_approved, assessment_type, serial_no)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          userId,
          body.assessment_name,
          body.course_id,
          body.module_id,
          body.duration,
          body.earn_point,
          body.earn_point_applicable_after,
          body.pass_percentage,
          0,
          1,
          body.assessment_type,
          body.total_content + 1,
        ]
      );

      console.log("✅ Assessment Inserted Successfully:", result);
    } else {
      console.log("🛠 Inserting Survey...");
      const [survey] = await mysqlServerConnection.query(
        `INSERT INTO ${db}.assessments 
          (created_by, assessment_name, course_id, module_id, earn_point, earn_point_applicable_after, is_active, is_approved, assessment_type, serial_no, pass_percentage)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          userId,
          body.assessment_name,
          body.course_id,
          body.module_id,
          body.earn_point,
          0,
          0,
          1,
          body.assessment_type,
          body.total_content + 1,
          0,
        ]
      );

      console.log("✅ Survey Inserted Successfully:", survey);
    }

    console.log("📦 Final Assessment Payload:", body);

    res.status(201).json({
      success: true,
      message: "Assessment created successfully",
    });
  } catch (error) {
    console.error("❌ Error Creating Assessment:", error);
    return res.status(500).json({
      success: false,
      error_msg: error.message || "Internal Server Error",
    });
  }
};



const updateAssessment = async (req, res, next) => {
  try {
    const {
      id = req.body.assessment_id,
      assessment_name,
      module_id,
      duration,
      earn_point = 0,
      earn_point_applicable_after = 0,
      assessment_type,
      pass_percentage,
    } = req.body;

    console.log("📝 Received Update Request Body:", req.body);
    console.log("📦 Extracted Values ->", {
      id,
      assessment_name,
      module_id,
      duration,
      earn_point,
      earn_point_applicable_after,
      assessment_type,
      pass_percentage
    });

    if (!id) {
      console.warn("❌ Missing Assessment ID");
      return res.status(400).json({
        success: false,
        data: {
          error_msg: "Missing required parameter: 'assessment_id' is required.",
        },
      });
    }

    if (assessment_type === "assessment") {
      console.log("📄 Type: Normal Assessment");

      if (!assessment_name || !module_id || !duration) {
        console.warn("❌ Missing Fields for Assessment");
        return res.status(400).json({
          success: false,
          data: {
            error_msg: "Missing required fields: 'assessment_name', 'module_id', and 'duration' are required.",
          },
        });
      }

      console.log("🛠️ Performing UPDATE with full fields (including points and duration)");
      const [result] = await mysqlServerConnection.query(
        `UPDATE ${req.user.db_name}.assessments 
         SET assessment_name = ?, module_id = ?, duration = ?, 
         earn_point = ?, earn_point_applicable_after = ?, pass_percentage = ?, is_active = ?
         WHERE id = ?`,
        [
          assessment_name,
          module_id,
          duration,
          earn_point,
          earn_point_applicable_after,
          pass_percentage,
          1,
          id,
        ]
      );

      console.log("🧾 Update Result:", result);

      if (result.affectedRows === 0) {
        console.warn("⚠️ Assessment not found or unchanged.");
        return next({
          statusCode: 404,
          message: "Assessment not found or no changes made.",
        });
      }

      res.status(200).json({
        success: true,
        message: "Assessment updated successfully",
      });

    } else {
      // For survey or other types
      console.log("📄 Type: Survey or Other Assessment");

      if (!assessment_name || !module_id) {
        console.warn("❌ Missing Fields for other assessment type");
        return res.status(400).json({
          success: false,
          data: {
            error_msg: "Missing required fields: 'assessment_name', 'module_id', and 'duration' are required.",
          },
        });
      }

      console.log("🛠️ Performing UPDATE for survey/other types including earn_point");
      const [result] = await mysqlServerConnection.query(
        `UPDATE ${req.user.db_name}.assessments 
         SET assessment_name = ?, earn_point = ?, earn_point_applicable_after = ?
         WHERE module_id = ? AND id = ?`,
        [assessment_name, earn_point, earn_point_applicable_after, module_id, id]
      );

      console.log("🧾 Update Result:", result);

      res.status(200).json({
        success: true,
        message: "Assessment updated successfully",
      });
    }

  } catch (error) {
    console.error("❌ Error in updateAssessment:", error);
    return next({
      statusCode: 500,
      message: error.message || "Internal Server Error",
    });
  }
};



// const deactivateAssessment = async (req, res, next) => {
//     try {
//         const { course_id, assessment_id } = req.params;
//         const { status } = req.body;

//         await mysqlServerConnection.query(
//             `UPDATE ${req.user.db_name}.assessments
//              SET is_active = ?
//              WHERE id = ? AND course_id = ?`,
//             [status, assessment_id, course_id]
//         );

//         res.status(200).json({
//             success: true,
//             message: `Assessment ${status === 1 ? 'Activated' : 'Deactivated'} Successfully`
//         });

//     } catch (error) {
//         return next({ statusCode: 500, message: error.message || 'Internal Server Error' });
//     }
// };

const deleteAssessment = async (req, res, next) => {
  try {
    const { assessment_id } = req.params;
    const query = `UPDATE ${req.user.db_name}.assessments SET is_deleted = 1 WHERE id = ?`;

    await mysqlServerConnection.query(query, [assessment_id]);
    res.status(200).json({
      success: true,
      message: "Assessment Deleted successfully",
    });
  } catch (error) {
    console.log(error.message);
    next({
      statusCode: 500,
      message: error.message || "Internal Server Error",
    });
  }
};

const changeAssessmentStatus = async (req, res, next) => {
  try {
    const { assessment_id } = req.params;
    const { status } = req.body;

    if (!assessment_id) {
      return next({
        statusCode: 404,
        message: "Assessment ID is missing, please provide assessment ID",
      });
    }

    const [result] = await mysqlServerConnection.query(
      `UPDATE ${req.user.db_name}.assessments SET is_active = ? where id = ?`,
      [status, assessment_id]
    );

    if (result.affectedRows === 0) {
      return next({
        statusCode: 404,
        message: `Assessment not found or is already ${
          status === 0 ? "Deactivated" : "Activated"
        }`,
      });
    }

    res.json(201).status({
      success: true,
      message: `  Assessment now is ${
        status === 0 ? "Deactivated" : "Activated"
      }`,
    });
  } catch (error) {
    return next({
      statusCode: 500,
      message: error.message || "Internal Server Error",
    });
  }
};

const changeAssignmentStatus = async (req, res, next) => {
  try {
    const { assignment_id } = req.params;
    const { status } = req.body;

    if (!assignment_id) {
      return next({
        statusCode: 404,
        message: "Assignment ID is missing, please provide assignment ID",
      });
    }

    const [result] = await mysqlServerConnection.query(
      `UPDATE ${req.user.db_name}.assignments SET is_active = ? where id = ?`,
      [status, assignment_id]
    );

    if (result.affectedRows === 0) {
      return next({
        statusCode: 404,
        message: `Assignment not found or is already ${
          status === 0 ? "Deactivated" : "Activated"
        }`,
      });
    }

    res.status(201).json({
      success: true,
      message: `  Assignment now is ${
        status === 0 ? "Deactivated" : "Activated"
      }`,
    });
  } catch (error) {
    return next({
      statusCode: 500,
      message: error.message || "Internal Server Error",
    });
  }
};

const createQuestion = async (req, res, next) => {
  try {
    const {
      assessment_id,
      module_id,
      question,
      question_type,
      rank_point,
      collect_point,
      options,
      question_content_type,
      question_media_url,
    } = req.body;

    // Validate required fields
    if (
      !assessment_id ||
      !module_id ||
      !question ||
      !question_type ||
      !options
    ) {
      return next({
        statusCode: 400,
        message:
          "Missing required fields: 'assessment_id', 'module_id', 'question', 'question_type', 'options'",
      });
    }

    // Prepare options JSON string for backward compatibility
    const optionsJSON = JSON.stringify(options);

    // Get connection and start transaction
    const connection = await mysqlServerConnection.getConnection();
    await connection.beginTransaction();

    try {
      // SQL query to insert the question with options and content type
      const insertQuestionQuery = `
                INSERT INTO ${req.user.db_name}.questions 
                (assessment_id, module_id, question, question_content_type, question_media_url, options, question_type, rank_point, collect_point, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
            `;

      // Insert the question and get the question ID
      const [questionResult] = await connection.query(insertQuestionQuery, [
        assessment_id,
        module_id,
        question,
        question_content_type || "text", // Default to 'text' if not provided
        question_media_url || null,
        optionsJSON,
        question_type,
        rank_point || 0,
        collect_point || 0,
      ]);

      const questionId = questionResult.insertId;

      // Insert options into the new question_options table
      if (options && Array.isArray(options)) {
        for (let i = 0; i < options.length; i++) {
          const option = options[i];
          const optionKey = option.serial || `option_${i + 1}`;
          const contentType = option.content_type || "text";
          const contentValue = option.label || option.content_value || "";
          const mediaUrl = option.media_url || null;
          const textFallback = option.text_fallback || contentValue;
          const isCorrect = option.isCorrect ? 1 : 0;

          const insertOptionQuery = `
                        INSERT INTO ${req.user.db_name}.question_options
                        (question_id, option_key, content_type, content_value, media_url, text_fallback, is_correct, created_at, updated_at)
                        VALUES (?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
                    `;

          console.log(
            `Inserting option ${optionKey} with value '${contentValue}' into question_options table...`
          );
          await connection.query(insertOptionQuery, [
            questionId,
            optionKey,
            contentType,
            contentValue,
            mediaUrl,
            textFallback,
            isCorrect,
          ]);
          console.log(
            `Option ${optionKey} inserted successfully. Content type: ${contentType}, Is correct: ${
              isCorrect === 1 ? "Yes" : "No"
            }`
          );
        }
      }

      // Commit the transaction
      await connection.commit();
      connection.release();

      res.status(201).json({
        success: true,
        message: "Question created successfully",
        question_id: questionId,
      });
    } catch (error) {
      // Rollback in case of error
      await connection.rollback();
      connection.release();
      throw error;
    }
  } catch (error) {
    console.error("Error creating question:", error.message);
    return next({
      statusCode: 500,
      message: error.message || "Internal Server Error",
    });
  }
};

const addMCQOptions = async (req, res, next) => {
  try {
    console.log("Add MCQ Options");
    const { question_id } = req.params;
    const { options } = req.body;
    updated_options = [];

    if (!options || !Array.isArray(options) || options.length === 0) {
      return next({
        statusCode: 400,
        message: "Options must be provided as a non-empty array",
      });
    }

    const [data] = await mysqlServerConnection.query(
      `SELECT options from ${req.user.db_name}.questions where id = ?`,
      [question_id]
    );

    if (data.length === 0) {
      return next({
        statusCode: 404,
        message: "Question not found or is already deleted",
      });
    }

    if (data[0].options) {
      const existingOptions = data[0].options;
      updated_options = [...existingOptions, ...options];
    }

    const query = `
            UPDATE ${req.user.db_name}.questions
            SET options = ?
            WHERE id = ?
        `;
    const optionsJson =
      updated_options.length > 0
        ? JSON.stringify(updated_options)
        : JSON.stringify(options);
    await mysqlServerConnection.query(query, [optionsJson, question_id]);
    res.status(201).json({
      success: true,
      message: "Options added successfully",
    });
  } catch (error) {
    console.error("Error adding options:", error.message);
    return next({
      statusCode: 500,
      message: error.message || "Internal Server Error",
    });
  }
};

const addCorrectAnswer = async (req, res, next) => {
  try {
    const { question_id } = req.params; // question_id
    const { correct_answer, option_key } = req.body;

    if (!correct_answer) {
      return next({ statusCode: 400, message: "Correct answer is required" });
    }

    // Get connection and start transaction
    const connection = await mysqlServerConnection.getConnection();
    await connection.beginTransaction();

    try {
      // First check if the question exists
      const [questionData] = await connection.query(
        `SELECT id, options FROM ${req.user.db_name}.questions WHERE id = ? AND is_deleted = 0`,
        [question_id]
      );

      if (!questionData || questionData.length === 0) {
        connection.release();
        return next({ statusCode: 404, message: "Question not found" });
      }

      // For backward compatibility: Update correct_option in questions table
      await connection.query(
        `UPDATE ${req.user.db_name}.questions 
                 SET correct_option = ? 
                 WHERE id = ?`,
        [correct_answer, question_id]
      );

      // Update the question_options table if option_key is provided
      if (option_key) {
        // First, set all options for this question to not correct
        await connection.query(
          `UPDATE ${req.user.db_name}.question_options 
                     SET is_correct = 0 
                     WHERE question_id = ?`,
          [question_id]
        );

        // Then set the specified option as correct
        await connection.query(
          `UPDATE ${req.user.db_name}.question_options 
                     SET is_correct = 1 
                     WHERE question_id = ? AND option_key = ?`,
          [question_id, option_key]
        );
      } else {
        // If no option_key is provided but we have options in JSON format
        // Try to find the matching option in the options JSON and update question_options table
        if (questionData[0].options) {
          try {
            const options = JSON.parse(questionData[0].options);
            const correctOptionIndex = options.findIndex(
              (opt) =>
                opt.label === correct_answer ||
                opt.content_value === correct_answer
            );

            if (correctOptionIndex !== -1) {
              const correctOption = options[correctOptionIndex];
              const optionKey =
                correctOption.serial || `option_${correctOptionIndex + 1}`;

              // Update the options JSON for backward compatibility
              options.forEach((opt, idx) => {
                opt.isCorrect = idx === correctOptionIndex;
              });

              await connection.query(
                `UPDATE ${req.user.db_name}.questions 
                                 SET options = ? 
                                 WHERE id = ?`,
                [JSON.stringify(options), question_id]
              );

              // First, set all options for this question to not correct
              await connection.query(
                `UPDATE ${req.user.db_name}.question_options 
                                 SET is_correct = 0 
                                 WHERE question_id = ?`,
                [question_id]
              );

              // Try to find the option in question_options table by content
              const [optionData] = await connection.query(
                `SELECT id FROM ${req.user.db_name}.question_options 
                                 WHERE question_id = ? AND (content_value = ? OR option_key = ?)`,
                [question_id, correct_answer, optionKey]
              );

              if (optionData && optionData.length > 0) {
                // Update the found option
                await connection.query(
                  `UPDATE ${req.user.db_name}.question_options 
                                     SET is_correct = 1 
                                     WHERE id = ?`,
                  [optionData[0].id]
                );
              }
            }
          } catch (e) {
            console.error("Error parsing options JSON:", e.message);
          }
        }
      }

      // Commit the transaction
      await connection.commit();
      connection.release();

      res.status(201).json({
        success: true,
        message: "Correct answer added successfully",
      });
    } catch (error) {
      // Rollback in case of error
      await connection.rollback();
      connection.release();
      throw error;
    }
  } catch (error) {
    console.error("Error adding correct answer:", error.message);
    return next({
      statusCode: 500,
      message: error.message || "Internal Server Error",
    });
  }
};

const editMCQOption = async (req, res, next) => {
  try {
    const { assessment_id, question_id } = req.params;
    const {
      option,
      index,
      option_key,
      content_type,
      media_url,
      text_fallback,
    } = req.body;

    if (!assessment_id || !question_id) {
      return next({
        statusCode: 404,
        message: "Required parameters are assessment ID and Question ID",
      });
    }

    if (!option) {
      return next({
        statusCode: 400,
        message: "Please provide option content",
      });
    }

    // Get connection and start transaction
    const connection = await mysqlServerConnection.getConnection();
    await connection.beginTransaction();

    try {
      // Check if question exists
      const [questionData] = await connection.query(
        `SELECT options FROM ${req.user.db_name}.questions WHERE id = ? AND assessment_id = ?`,
        [question_id, assessment_id]
      );

      if (!questionData || questionData.length === 0) {
        connection.release();
        return next({ statusCode: 404, message: "No Question found" });
      }

      // For backward compatibility: Update the options JSON in the questions table
      if (index !== undefined) {
        try {
          let optionsArray = [];

          if (questionData[0].options) {
            optionsArray = JSON.parse(questionData[0].options);
          }

          // Check if the index is valid
          if (index < 0 || index >= optionsArray.length) {
            connection.release();
            return next({
              statusCode: 400,
              message: `Invalid index: ${index}. Valid range is 0 to ${
                optionsArray.length - 1
              }`,
            });
          }

          // Get the existing option to preserve any fields not being updated
          const existingOption = optionsArray[index];

          // Update the option with new values
          optionsArray[index] = {
            ...existingOption,
            label: option,
            content_type: content_type || existingOption.content_type || "text",
            media_url: media_url || existingOption.media_url,
            text_fallback:
              text_fallback || existingOption.text_fallback || option,
          };

          // Update the questions table
          await connection.query(
            `UPDATE ${req.user.db_name}.questions SET options = ? WHERE id = ?`,
            [JSON.stringify(optionsArray), question_id]
          );

          // If we have option_key, also update the question_options table
          if (option_key || existingOption.serial) {
            const optKey = option_key || existingOption.serial;
            const contentType =
              content_type || existingOption.content_type || "text";
            const contentValue = option;
            const mediaUrl = media_url || existingOption.media_url || null;
            const textFallbackValue =
              text_fallback || existingOption.text_fallback || option;
            const isCorrect = existingOption.isCorrect ? 1 : 0;

            // Check if the option exists in question_options table
            const [optionData] = await connection.query(
              `SELECT id FROM ${req.user.db_name}.question_options 
                             WHERE question_id = ? AND option_key = ?`,
              [question_id, optKey]
            );

            if (optionData && optionData.length > 0) {
              // Update existing option
              await connection.query(
                `UPDATE ${req.user.db_name}.question_options 
                                 SET content_type = ?, 
                                     content_value = ?, 
                                     media_url = ?, 
                                     text_fallback = ?, 
                                     updated_at = CURRENT_TIMESTAMP 
                                 WHERE id = ?`,
                [
                  contentType,
                  contentValue,
                  mediaUrl,
                  textFallbackValue,
                  optionData[0].id,
                ]
              );
            } else {
              // Insert new option in question_options table
              await connection.query(
                `INSERT INTO ${req.user.db_name}.question_options 
                                 (question_id, option_key, content_type, content_value, media_url, text_fallback, is_correct, created_at, updated_at) 
                                 VALUES (?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)`,
                [
                  question_id,
                  optKey,
                  contentType,
                  contentValue,
                  mediaUrl,
                  textFallbackValue,
                  isCorrect,
                ]
              );
            }
          }
        } catch (e) {
          console.error("Error parsing or updating options:", e.message);
          connection.rollback();
          connection.release();
          return next({
            statusCode: 500,
            message: "Error processing options: " + e.message,
          });
        }
      } else if (option_key) {
        // If no index but option_key is provided, update only the question_options table
        const contentType = content_type || "text";
        const contentValue = option;
        const mediaUrl = media_url || null;

        // Check if the option exists in question_options table
        const [optionData] = await connection.query(
          `SELECT id FROM ${req.user.db_name}.question_options 
                     WHERE question_id = ? AND option_key = ?`,
          [question_id, option_key]
        );

        if (optionData && optionData.length > 0) {
          // Update existing option
          await connection.query(
            `UPDATE ${req.user.db_name}.question_options 
                         SET content_type = ?, 
                             content_value = ?, 
                             media_url = ?, 
                             text_fallback = ?, 
                             updated_at = CURRENT_TIMESTAMP 
                         WHERE id = ?`,
            [
              contentType,
              contentValue,
              mediaUrl,
              text_fallback || contentValue,
              optionData[0].id,
            ]
          );
        } else {
          connection.rollback();
          connection.release();
          return next({
            statusCode: 404,
            message: `Option with key ${option_key} not found`,
          });
        }
      } else {
        connection.rollback();
        connection.release();
        return next({
          statusCode: 400,
          message:
            "Please provide either an index or option_key to identify the option to update",
        });
      }

      // Commit the transaction
      await connection.commit();
      connection.release();

      res.status(200).json({
        success: true,
        message: "Option updated successfully",
      });
    } catch (error) {
      // Rollback in case of error
      await connection.rollback();
      connection.release();
      throw error;
    }
  } catch (error) {
    console.error("Error updating MCQ option:", error.message);
    return next({
      statusCode: 500,
      message: error.message || "Internal Server Error",
    });
  }
};

const deleteMCQOption = async (req, res, next) => {
  try {
    const { question_id } = req.params;
    const { serial } = req.body;

    if (serial === undefined) {
      return next({
        statusCode: 400,
        message: "The 'serial' of the option to delete is required.",
      });
    }
    const [questionResult] = await mysqlServerConnection.query(
      `SELECT options FROM ${req.user.db_name}.questions WHERE id = ?`,
      [question_id]
    );

    if (questionResult.length === 0) {
      return next({ statusCode: 404, message: "Question not found." });
    }

    let currentOptions = JSON.parse(questionResult[0].options || "[]");
    const updatedOptions = currentOptions.filter(
      (option) => option.serial !== serial
    );

    if (updatedOptions.length === currentOptions.length) {
      return next({ statusCode: 404, message: "Option not found." });
    }
    await mysqlServerConnection.query(
      `UPDATE ${req.user.db_name}.questions SET options = ? WHERE id = ?`,
      [JSON.stringify(updatedOptions), question_id]
    );
    res.status(200).json({
      success: true,
      message: "Option deleted successfully",
      updatedOptions,
    });
  } catch (error) {
    console.error("Error deleting MCQ option:", error.message);
    return next({
      statusCode: 500,
      message: error.message || "Internal Server Error",
    });
  }
};

// const editQuestion = async (req, res, next) => {
//     try {
//         const { question_id } = req.params;
//         console.log("QUESTION ID:", question_id);

//         if (!question_id) {
//             return next({ statusCode: 404, message: 'Please provide question id' })
//         }

//         const { assessment_id, module_id, question, question_type, rank_point, collect_point, options } = req.body;
//         const optionsJSON = JSON.stringify(options);

//         // Update the question details
//         const [result] = await mysqlServerConnection.query(`
//             UPDATE ${req.user.db_name}.questions
//             SET assessment_id = ?, module_id = ?, question_type = ?, rank_point = ?, question = ?, collect_point = ?, options = ?
//             WHERE id = ?
//         `, [assessment_id, module_id, question_type, rank_point || 0, question, collect_point || 0, optionsJSON, question_id]);

//         if (result.affectedRows === 0) {
//             return next({ statusCode: 404, message: "Question not found or is already deleted" });
//         }

//         // Find the correct option from the options array
//         const correctOption = options.find(option => option.isCorrect);

//         if (correctOption) {
//             // Check if a correct answer already exists in the question_answer table
//             const [existingAnswer] = await mysqlServerConnection.query(`
//                 SELECT * FROM ${req.user.db_name}.question_answer WHERE question_id = ?
//             `, [question_id]);

//             if (existingAnswer.length > 0) {
//                 // Update the existing correct answer
//                 await mysqlServerConnection.query(`
//                     UPDATE ${req.user.db_name}.question_answer
//                     SET answer = ?, collect_point = ?, question_type = ?, updatedAt = CURRENT_TIMESTAMP
//                     WHERE question_id = ?
//                 `, [correctOption.label, collect_point || 0, question_type, question_id]);
//             } else {
//                 // Insert a new correct answer
//                 await mysqlServerConnection.query(`
//                     INSERT INTO ${req.user.db_name}.question_answer
//                     (question_id, answer, collect_point, question_type, createdAt, updatedAt)
//                     VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
//                 `, [question_id, correctOption.label, collect_point || 0, question_type]);
//             }
//         }

//         return res.status(200).json({
//             success: true,
//             message: 'Question updated successfully'
//         });

//     } catch (error) {
//         console.error('Error updating question:', error);
//         return next({
//             statusCode: 500,
//             message: error.message || 'Internal Server Error',
//         });
//     }
// };

const getAssessmentsByModule = async (req, res, next) => {
  try {
    const { module_id } = req.params;
    const [assessments] = await mysqlServerConnection.query(
      `SELECT * from ${req.user.db_name}.assessments where module_id = ? AND is_deleted = false`,
      [module_id]
    );
    console.log("Assessments :", assessments);
    if (!assessments || assessments.length == 0) {
      return next({ statusCode: 404, message: "No assignments found" });
    }

    const assessmentsWithUserCount = await Promise.all(
      assessments.map(async (assessment) => {
        const [userCount] = await mysqlServerConnection.query(
          `SELECT COUNT(*) as user_count FROM ${req.user.db_name}.assessment_users WHERE assessment_id = ? AND is_eligible = 1`,
          [assessment.id]
        );

        const [questionCount] = await mysqlServerConnection.query(
          `SELECT COUNT(*) as question_count FROM ${req.user.db_name}.questions 
                WHERE assessment_id = ? AND module_id = ? and is_deleted = 0`,
          [assessment.id, module_id]
        );
        return {
          ...assessment,
          user_count: userCount[0].user_count,
          question_count: questionCount[0].question_count,
        };
      })
    );

    res.status(200).json({
      success: true,
      data: assessmentsWithUserCount,
    });
  } catch (error) {
    return next({
      statusCode: 500,
      message: error.message || "Internal Server Error",
    });
  }
};

const getQuestionsForAssessment = async (req, res, next) => {
  try {
    const { assessment_id, module_id } = req.params;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const offset = (page - 1) * limit;

    // Get the total count of questions for pagination
    const [countResult] = await mysqlServerConnection.query(
      `
            SELECT COUNT(*) as totalQuestions FROM ${req.user.db_name}.questions 
            WHERE assessment_id = ? AND module_id = ? AND is_deleted = 0
        `,
      [assessment_id, module_id]
    );

    const totalQuestions = countResult[0].totalQuestions;
    const totalPages = Math.ceil(totalQuestions / limit);

    // Fetch the paginated questions
    const [questions] = await mysqlServerConnection.query(
      `
            SELECT * FROM ${req.user.db_name}.questions 
            WHERE assessment_id = ? AND module_id = ? AND is_deleted = 0
            LIMIT ? OFFSET ?
        `,
      [assessment_id, module_id, limit, offset]
    );

    if (!questions || questions.length === 0) {
      return next({
        statusCode: 404,
        message: "No questions found for the assessment",
      });
    }

    // Fetch options for each question from the new question_options table
    const questionsWithOptions = await Promise.all(
      questions.map(async (question) => {
        const [options] = await mysqlServerConnection.query(
          `
                SELECT option_key, content_type, content_value, media_url, text_fallback, is_correct 
                FROM ${req.user.db_name}.question_options 
                WHERE question_id = ?
                ORDER BY option_key
            `,
          [question.id]
        );

        // If options are found in the new table, use them
        if (options && options.length > 0) {
          // Format the options in a way that's compatible with the frontend
          const formattedOptions = options.map((opt) => ({
            key: opt.option_key,
            content_type: opt.content_type,
            content_value: opt.content_value,
            media_url: opt.media_url,
            text_fallback: opt.text_fallback,
            isCorrect: opt.is_correct === 1,
          }));

          return {
            ...question,
            question_content_type: question.question_content_type || "text",
            question_media_url: question.question_media_url || null,
            formatted_options: formattedOptions,
          };
        } else {
          // Fallback to the old options format if no options found in the new table
          // This ensures backward compatibility
          return question;
        }
      })
    );

    // Return paginated data
    res.status(200).json({
      success: true,
      data: questionsWithOptions,
      pagination: {
        totalQuestions,
        currentPage: page,
        totalPages,
        pageSize: limit,
      },
    });
  } catch (error) {
    console.error("Error fetching questions:", error.message);
    return next({
      statusCode: 500,
      message: error.message || "Internal server error",
    });
  }
};

const createCourseApproval = async (req, res, next) => {
  try {
    const user_id = req.user.userId;
    const { course_id } = req.body;

    if (!user_id || !course_id) {
      return res.status(400).json({
        success: false,
        message: "user_id and course_id are required fields",
      });
    }
    const [existingApproval] = await mysqlServerConnection.query(
      `SELECT * FROM ${req.user.db_name}.course_approval WHERE user_id = ? AND course_id = ?`,
      [user_id, course_id]
    );

    if (existingApproval.length > 0) {
      return res.status(400).json({
        success: false,
        message:
          "Course approval request already exists for this user and course",
      });
    }

    await mysqlServerConnection.query(
      `INSERT INTO ${req.user.db_name}.course_approval (user_id, course_id, is_approval, created_at, updated_at)
             VALUES (?, ?, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)`,
      [user_id, course_id]
    );

    res.status(201).json({
      success: true,
      message: "Course approval request created successfully",
    });
  } catch (error) {
    console.error("Error creating course approval request:", error.message);
    return next({
      statusCode: 500,
      message: "Internal Server Error",
    });
  }
};

const approveCourseApproval = async (req, res, next) => {
  try {
    const { user_id, course_id } = req.body;
    if (!user_id || !course_id) {
      return res.status(400).json({
        success: false,
        message: "user_id and course_id are required fields",
      });
    }
    const [existingApproval] = await mysqlServerConnection.query(
      `SELECT * FROM ${req.user.db_name}.course_approval WHERE user_id = ? AND course_id = ?`,
      [user_id, course_id]
    );

    if (existingApproval.length === 0) {
      return next({
        statusCode: 404,
        message: "course approval request not found",
      });
    }

    await mysqlServerConnection.query(
      `UPDATE ${req.user.db_name}.course_approval
             SET is_approval = 1, updated_at = CURRENT_TIMESTAMP
             WHERE user_id = ? AND course_id = ?`,
      [user_id, course_id]
    );
    res.status(200).json({
      success: true,
      message: "Course approval request approved successfully",
    });
  } catch (error) {
    console.error("Error approving course approval request:", error.message);
    return next({
      statusCode: 500,
      message: "Internal Server Error",
    });
  }
};

const deleteModuleContent = async (req, res, next) => {
  try {
    const { content_id, type } = req.params;

    if (!content_id) {
      return res.status(404).json({
        success: false,
        error_msg: "Content ID is required"
      });
    }

    let updateQueries = [];

    if (type === "assessment" || type === "survey") {
      // Soft delete from assessments
      updateQueries.push(
        mysqlServerConnection.query(
          `UPDATE ${req.user.db_name}.assessments SET is_deleted = 1 WHERE id = ?`,
          [content_id]
        )
      );
    } else if (type === "video") {
      updateQueries.push(
        mysqlServerConnection.query(
          `UPDATE ${req.user.db_name}.videos SET is_deleted = 1 WHERE id = ?`,
          [content_id]
        )
      );
    } else if (type === "assignment") {
      updateQueries.push(
        mysqlServerConnection.query(
          `UPDATE ${req.user.db_name}.assignments SET is_deleted = 1 WHERE id = ?`,
          [content_id]
        )
      );
    } else if (type === "document") {
      updateQueries.push(
        mysqlServerConnection.query(
          `UPDATE ${req.user.db_name}.documents SET is_deleted = 1 WHERE id = ?`,
          [content_id]
        )
      );
    } else {
      return res.status(404).json({
        success: false,
        message: `Content type '${type}' not supported. Supported types: video, document, assessment, assignment, survey`,
      });
    }

    await Promise.all(updateQueries);

    return res.status(200).json({
      success: true,
      message: `Content soft-deleted successfully`,
    });
  } catch (error) {
    console.error("Error deleting content:", error);
    return res.status(500).json({
      success: false,
      error_msg: error.message || "Internal Server Error"
    });
  }
};












const ModuleContentStatus = async (req, res) => {
  try {
    const { module_id, content_id } = req.params;
    const { is_active, content_type } = req.body;
    // Validate inputs
    if (!module_id || !content_type || !content_id) {
      return res.status(400).json({
        success: false,
        data: {
          error_msg: "Module ID, Content Type, and Content ID are required.",
        },
      });
    }

    const tableMap = {
      video: "videos",
      document: "documents",
      assessment: "assessments",
      assignment: "assignments",
      survey: "assessments", // Surveys are stored in the assessments table
    };

    const tableName = tableMap[content_type.toLowerCase()];
    if (!tableName) {
      return res.status(400).json({
        success: false,
        data: {
          error_msg:
            "Invalid content type. Must be one of: video, document, assessment, assignment, survey.",
        },
      });
    }

    // Update the `is_active` field in the corresponding table
    const [result] = await mysqlServerConnection.query(
      `UPDATE ${req.user.db_name}.${tableName} 
                 SET is_active = ? 
                 WHERE id = ? AND module_id = ?`,
      [is_active, content_id, module_id]
    );

    if (result.affectedRows === 0) {
      return res.status(404).json({
        success: false,
        data: {
          error_msg: "No content found with the provided ID and module.",
        },
      });
    }

    res.status(200).json({
      success: true,
      data: {
        message: `Successfully updated 'is_active' status for ${content_type}.`,
        updatedContent: { module_id, content_type, content_id, is_active },
      },
    });
  } catch (error) {
    console.error("Error updating content status:", error);
    res
      .status(500)
      .json({ success: false, data: { error_msg: "Internal server error." } });
  }
};

const getVideoDetails = async (req, res, next) => {
  try {
    const { course_id, module_id } = req.params;
    if (!course_id || !module_id) {
      return next({
        statusCode: 400,
        message: "Course ID and module ID are required fields",
      });
    }
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const offset = (page - 1) * limit;

    // Fetch total number of videos for this module (for pagination metadata)
    const [totalResults] = await mysqlServerConnection.query(
      `SELECT COUNT(*) AS total FROM ${req.user.db_name}.videos WHERE module_id = ? AND course_id = ?`,
      [module_id, course_id]
    );
    const totalVideos = totalResults[0].total;

    // Fetch videos with pagination
    const [videos] = await mysqlServerConnection.query(
      `SELECT * FROM ${req.user.db_name}.videos WHERE module_id = ? AND course_id = ? AND is_deleted = 0 LIMIT ? OFFSET ?`,
      [module_id, course_id, limit, offset]
    );

    // Calculate total pages
    const totalPages = Math.ceil(totalVideos / limit);

    // Return the video data along with pagination metadata
    res.status(200).json({
      success: true,
      data: videos,
      pagination: {
        totalVideos,
        totalPages,
        currentPage: page,
        limit,
      },
    });
  } catch (error) {
    return next({
      statusCode: 500,
      message: error.message || "Internal Server Error",
    });
  }
};

const updateVideoDetails = async (req, res, next) => {
  try {
    const { video_id } = req.params;
    const videoData = req.body;
    const file = req.files["attachment_file"] || null;
    const [filePath] = await mysqlServerConnection.query(
      `SELECT video_url from ${req.user.db_name}.videos where id=?`,
      [video_id]
    );
    const [fileType] = await mysqlServerConnection.query(
      `SELECT attachment_type from ${req.user.db_name}.video_details where video_id = ?`,
      [video_id]
    );

    if (
      file &&
      filePath[0].video_url &&
      fileType[0].attachment_type === "file"
    ) {
      console.log("deleting existing file");
      deleteFileIfExists(filePath[0].video_url);
    }
    await mysqlServerConnection.query(
      `
            UPDATE ${req.user.db_name}.videos
            SET video_name = ?,
                video_description = ?,
                video_url = ?,
                updatedAt = CURRENT_TIMESTAMP
            WHERE id = ?`,
      [
        videoData.video_name,
        videoData.video_description,
        file ? req.files["attachment_file"][0].path : filePath[0].video_url,
        video_id,
      ]
    );

    const [video] = await mysqlServerConnection.query(
      `SELECT * from ${req.user.db_name}.videos where id = ?`,
      [video_id]
    );

    await mysqlServerConnection.query(
      `
            UPDATE ${req.user.db_name}.video_details
            SET video_id = ?,
                attachment_type = ?,
                attachment_detail = ?
            WHERE video_id = ?;`,
      [
        video_id,
        videoData.attachment_link ? "link" : "file",
        videoData.attachment_link ? videoData.attachment_link : null,
        video_id,
      ]
      // [
      //     video_id,
      //     videoData.attachment_link ? "link" : "file",
      //     videoData.attachment_link ? videoData.attachment_link : video[0].video_url,
      //     video_id
      // ]
    );

    res.status(200).json({
      success: true,
      message: "Video details updated successfully",
    });
  } catch (error) {
    return next({ statusCode: 500, message: error.message });
  }
};

// const deleteQuestion = async (req, res, next) => {

//     const { question_id } = req.params;
//     if (!question_id) {
//         return next({ statusCode: 404, message: 'Please provide question id' })
//     }

//     const [result] = await mysqlServerConnection.query
//         (`UPDATE ${req.user.db_name}.questions SET is_deleted = 1 where id = ?`,
//             [question_id]);

//     if (result.affectedRows === 0) {
//         return next({ statusCode: 400, message: 'Question not found or is deleted already' })
//     }

//     return res.status(201).json({
//         success: true,
//         message: 'Question deleted Successfully',
//     })
// }

const getUserAllResults = async (req, res, next) => {
  try {
    let { user_id, course_id } = req.params;
    let { page, limit } = req.params;

    // Convert course_id, page, and limit to integers
    course_id = course_id ? parseInt(course_id, 10) : null;
    page = parseInt(page, 10) || 1;
    limit = parseInt(limit, 10) || 10;

    console.log(user_id, typeof course_id, page, limit);

    if (!user_id) {
      return res.status(400).json({
        success: false,
        message: "User ID is required",
      });
    }

    const dbName = req.user.db_name;

    // First, get all results without pagination to count total
    const [assessments] = await mysqlServerConnection.query(
      `
            SELECT
                au.*,
                a.course_id,
                a.assessment_name AS name,
                c.course_name,
                (SELECT COUNT(*)
                 FROM ${dbName}.questions q
                 WHERE q.assessment_id = a.id) AS total_questions,
                (SELECT COUNT(*)
                 FROM ${dbName}.question_answer qa
                 JOIN ${dbName}.questions q ON qa.question_id = q.id
                 WHERE qa.user_id = au.user_id
                   AND q.assessment_id = a.id
                   AND qa.is_correct = true) AS total_correct_answers,
                'assessment' AS type
            FROM
                ${dbName}.assessment_users au
            JOIN
                ${dbName}.assessments a ON au.assessment_id = a.id
            JOIN
                ${dbName}.courses c ON a.course_id = c.id
            WHERE
                au.user_id = ?
                ${course_id ? "AND a.course_id = ?" : ""}
            `,
      course_id ? [user_id, course_id] : [user_id]
    );

    const [assignments] = await mysqlServerConnection.query(
      `
            SELECT
                au.*,
                a.course_id,
                a.assignment_name AS name,
                c.course_name,
                'assignment' AS type
            FROM
                ${dbName}.assignment_user au
            JOIN
                ${dbName}.assignments a ON au.assignment_id = a.id
            JOIN
                ${dbName}.courses c ON a.course_id = c.id
            WHERE
                au.user_id = ?
                ${course_id ? "AND a.course_id = ?" : ""}
            `,
      course_id ? [user_id, course_id] : [user_id]
    );

    // Combine and sort all results
    const allResults = [...assessments, ...assignments];
    const sortedResults = allResults.sort((a, b) => a.type.localeCompare(b.type));

    // Calculate pagination
    const totalCount = sortedResults.length;
    const totalPages = Math.ceil(totalCount / limit);
    const offset = (page - 1) * limit;

    // Apply pagination to the sorted results
    const paginatedResults = sortedResults.slice(offset, offset + limit);

    // Return paginated results with proper metadata
    return res.status(200).json({
      success: true,
      data: paginatedResults,
      pagination: {
        page: page,
        limit: limit,
        totalCount: totalCount,
        totalPages: totalPages,
        currentPage: page
      },
    });
  } catch (error) {
    console.error("Error fetching user results:", error.message);
    return next({
      statusCode: 500,
      message: "Internal Server Error",
    });
  }
};

const getUserAssessmentResults = async (req, res, next) => {
  try {
    let { user_id, course_id } = req.params;
    let { page = 1, limit = 10 } = req.query;

    console.log(user_id, course_id, page, limit);

    // Convert course_id, page, and limit to integers
    course_id = course_id ? parseInt(course_id, 10) : null;
    page = parseInt(page, 10) || 1;
    limit = parseInt(limit, 10) || 10; 

    console.log(user_id, typeof course_id, page, limit);

    if (!user_id) {
      return res.status(400).json({
        success: false,
        message: "User ID is required",
      });
    }

    const dbName = req.user.db_name;

    // Calculate offset for pagination
    const offset = (page - 1) * limit;

    // Query for paginated assessments
    const [assessments] = await mysqlServerConnection.query(
      `
            SELECT 
                au.*,
                a.course_id,
                a.assessment_name,
                c.course_name,
                (SELECT COUNT(*) 
                 FROM ${dbName}.questions q 
                 WHERE q.assessment_id = a.id) AS total_questions,
                (SELECT COUNT(*) 
                 FROM ${dbName}.question_answer qa 
                 JOIN ${dbName}.questions q ON qa.question_id = q.id 
                 WHERE qa.user_id = au.user_id 
                   AND q.assessment_id = a.id 
                   AND qa.is_correct = true) AS total_correct_answers
            FROM 
                ${dbName}.assessment_users au
            JOIN 
                ${dbName}.assessments a ON au.assessment_id = a.id
            JOIN 
                ${dbName}.courses c ON a.course_id = c.id
            WHERE 
                au.user_id = ? 
                ${course_id ? "AND a.course_id = ?" : ""}
            LIMIT ? OFFSET ?
            `,
      course_id ? [user_id, course_id, limit, offset] : [user_id, limit, offset]
    );

    // Count total assessments for pagination metadata
    const [totalCountResult] = await mysqlServerConnection.query(
      `
            SELECT COUNT(*) AS total 
            FROM ${dbName}.assessment_users au
            JOIN ${dbName}.assessments a ON au.assessment_id = a.id
            WHERE au.user_id = ? 
            ${course_id ? "AND a.course_id = ?" : ""}
            `,
      course_id ? [user_id, course_id] : [user_id]
    );
    const totalItems = totalCountResult[0]?.total || 0;
    const totalPages = Math.ceil(totalItems / limit);

    // Return paginated results
    return res.status(200).json({
      success: true,
      data: assessments,
      pagination: {
        currentPage: page,
        limit,
        totalItems,
        totalPages,
      },
    });
  } catch (error) {
    console.error("Error fetching user assessment results:", error.message);
    return next({
      statusCode: 500,
      message: "Internal Server Error",
    });
  }
};

const getAssignmentResults = async (req, res, next) => {
  try {
    let { user_id, course_id } = req.params;
    let { page = 1, limit = 10 } = req.query;

    // Convert course_id, page, and limit to integers
    course_id = course_id ? parseInt(course_id, 10) : null;
    page = parseInt(page, 10) || 1;
    limit = parseInt(limit, 10) || 10;

    console.log(user_id, typeof course_id, page, limit);

    if (!user_id) {
      return res.status(400).json({
        success: false,
        message: "User ID is required",
      });
    }

    const dbName = req.user.db_name;

    // Calculate offset for pagination
    const offset = (page - 1) * limit;

    // Query for paginated assignments
    const [assignments] = await mysqlServerConnection.query(
      `
            SELECT 
                au.*,
                a.course_id,
                a.assignment_name,
                c.course_name
            FROM 
                ${dbName}.assignment_user au
            JOIN 
                ${dbName}.assignments a ON au.assignment_id = a.id
            JOIN 
                ${dbName}.courses c ON a.course_id = c.id
            WHERE 
                au.user_id = ? 
                ${course_id ? "AND a.course_id = ?" : ""}
            LIMIT ? OFFSET ?
            `,
      course_id ? [user_id, course_id, limit, offset] : [user_id, limit, offset]
    );

    // Count total assignments for pagination metadata
    const [totalCountResult] = await mysqlServerConnection.query(
      `
            SELECT COUNT(*) AS total 
            FROM ${dbName}.assignment_user au
            JOIN ${dbName}.assignments a ON au.assignment_id = a.id
            WHERE au.user_id = ? 
            ${course_id ? "AND a.course_id = ?" : ""}
            `,
      course_id ? [user_id, course_id] : [user_id]
    );
    const totalItems = totalCountResult[0]?.total || 0;
    const totalPages = Math.ceil(totalItems / limit);

    // Return paginated results
    return res.status(200).json({
      success: true,
      data: assignments,
      pagination: {
        currentPage: page,
        limit,
        totalItems,
        totalPages,
      },
    });
  } catch (error) {
    console.error("Error fetching user assignment results:", error.message);
    return next({
      statusCode: 500,
      message: "Internal Server Error",
    });
  }
};

const getSurvyResults = async (req, res, next) => {
  try {
    let { user_id, course_id } = req.params;
    course_id = course_id ? parseInt(course_id, 10) : null;

    console.log(user_id, typeof course_id);

    if (!user_id) {
      return res.status(400).json({
        success: false,
        message: "User ID is required",
      });
    }

    const dbName = req.user.db_name;

    // Query for assessments
    const [assessments] = await mysqlServerConnection.query(
      `
                SELECT 
                    au.*,
                    a.course_id,
                    a.assessment_name,
                    c.course_name,
                    (SELECT COUNT(*) 
                     FROM ${dbName}.questions q 
                     WHERE q.assessment_id = a.id) AS total_questions,
                    (SELECT COUNT(*) 
                     FROM ${dbName}.question_answer qa 
                     JOIN ${dbName}.questions q ON qa.question_id = q.id 
                     WHERE qa.user_id = au.user_id 
                       AND q.assessment_id = a.id 
                       AND qa.is_correct = true) AS total_correct_answers
                FROM 
                    ${dbName}.assessment_users au
                JOIN 
                    ${dbName}.assessments a ON au.assessment_id = a.id
                JOIN 
                    ${dbName}.courses c ON a.course_id = c.id
                WHERE au.user_id = ? 
                ${course_id ? "AND a.course_id = ?" : ""}
                `,
      course_id ? [user_id, course_id] : [user_id]
    );

    // Query for assignments

    return res.status(200).json({
      success: true,
      data: assessments,
    });
  } catch (error) {
    console.error("Error fetching user assessment results:", error.message);
    return next({
      statusCode: 500,
      message: "Internal Server Error",
    });
  }
};

const userActivity = async (req, res, next) => {
  try {
    const { user_id } = req.params;
    const [result] = await mysqlServerConnection.query(
      `SELECT id, user_id, log_name, log_description, file_url, createdAt 
            FROM ${req.user.db_name}.user_logs 
            WHERE user_id = ? 
            ORDER BY createdAt DESC`,
      [user_id]
    );
    res.status(200).json({
      success: true,
      data: result,
    });
  } catch (error) {
    return next({ statusCode: 500, message: error.message });
  }
};

const getUserCourseList = async (req, res, next) => {
  try {
    const { user_id } = req.params;

    if (!user_id) {
      return res.status(400).json({
        success: false,
        message: "User ID is required",
      });
    }

    const dbName = req.user.db_name;

    // Query to join mycourses and courses tables
    const [result] = await mysqlServerConnection.query(
      `
            SELECT 
                mc.course_id, 
                c.course_name
            FROM 
                ${dbName}.mycourses mc
            JOIN 
                ${dbName}.courses c ON mc.course_id = c.id
            WHERE 
                mc.user_id = ?
            `,
      [user_id]
    );

    res.status(200).json({
      success: true,
      data: result,
    });
  } catch (error) {
    console.error("Error fetching user course list:", error.message);
    return next({
      statusCode: 500,
      message: "Internal Server Error",
    });
  }
};

const getPerformanceSummary = async (req, res, next) => {
  try {
    let { course_id, user_id } = req.params;

    let totalVideos = 0;
    let completedVideosCount = 0;
    let totalAssessments = 0;
    let completedAssessmentsCount = 0;

    if (course_id === "0") {
      // Fetch data across all courses
      const [videoCounts] = await mysqlServerConnection.query(
        `SELECT 
                    count(*) as videoCount,
                    SUM(CASE WHEN rv.is_complete = 1 THEN 1 ELSE 0 END) as completedVideoCount
                 FROM ${req.user.db_name}.videos v
                 LEFT JOIN ${req.user.db_name}.recent_videos rv ON v.id = rv.video_id
                 WHERE rv.user_id = ?`,
        [user_id]
      );

      totalVideos = videoCounts[0].videoCount || 0;
      completedVideosCount = videoCounts[0].completedVideoCount || 0;

      const [assessmentCounts] = await mysqlServerConnection.query(
        `SELECT 
                    count(*) as assessmentCount,
                    SUM(CASE WHEN au.is_completed = 1 THEN 1 ELSE 0 END) as completedAssessmentCount
                 FROM ${req.user.db_name}.assessments a
                 LEFT JOIN ${req.user.db_name}.assessment_users au ON a.id = au.assessment_id
                 WHERE au.user_id = ?`,
        [user_id]
      );

      totalAssessments = assessmentCounts[0].assessmentCount || 0;
      completedAssessmentsCount =
        assessmentCounts[0].completedAssessmentCount || 0;
    } else {
      // Fetch data for a specific course
      const resolvedCourseId = parseInt(course_id, 10);

      const [videoCount] = await mysqlServerConnection.query(
        `SELECT count(*) as videoCount 
                 FROM ${req.user.db_name}.videos 
                 WHERE course_id = ?`,
        [resolvedCourseId]
      );

      const [completedVideos] = await mysqlServerConnection.query(
        `SELECT count(*) as completed_video_count
                 FROM ${req.user.db_name}.recent_videos rv 
                 JOIN ${req.user.db_name}.videos v ON rv.video_id = v.id 
                 WHERE v.course_id = ? 
                 AND rv.user_id = ? 
                 AND rv.is_complete = 1`,
        [resolvedCourseId, user_id]
      );

      totalVideos = videoCount[0].videoCount || 0;
      completedVideosCount = completedVideos[0].completed_video_count || 0;

      const [assessmentCount] = await mysqlServerConnection.query(
        `SELECT count(*) as assessmentCount
                 FROM ${req.user.db_name}.assessments a
                 WHERE a.course_id = ?`,
        [resolvedCourseId]
      );

      const [completedAssessments] = await mysqlServerConnection.query(
        `SELECT count(*) as completed_assessment_count
                 FROM ${req.user.db_name}.assessment_users au
                 JOIN ${req.user.db_name}.assessments a ON au.assessment_id = a.id
                 WHERE a.course_id = ? 
                 AND au.user_id = ? 
                 AND au.is_completed = 1`,
        [resolvedCourseId, user_id]
      );

      totalAssessments = assessmentCount[0].assessmentCount || 0;
      completedAssessmentsCount =
        completedAssessments[0].completed_assessment_count || 0;
    }

    // Calculate totals
    const totalContent = totalVideos + totalAssessments;
    let completedContent = completedVideosCount + completedAssessmentsCount;

    // Ensure completedContent does not exceed totalContent
    if (completedContent > totalContent) {
      completedContent = totalContent;
    }

    const overallCompletionPercentage =
      totalContent > 0 ? (completedContent / totalContent) * 100 : 0;

    res.status(200).json({
      success: true,
      data: {
        overallCompletionPercentage: overallCompletionPercentage.toFixed(2),
        totalVideos,
        completedVideosCount,
        totalAssessments,
        completedAssessmentsCount,
      },
    });
  } catch (error) {
    console.error("Error fetching performance summary:", error.message);
    res.status(500).json({
      success: false,
      error: error.message || "Internal Server Error",
    });
  }
};

const createDocument = async (req, res) => {
  try {
    const { title, course_id, module_id, total_content } = req.body;
    console.log("createDocument-------------", req.body);
    console.log("📥 Document Creation - Detailed data:", {
      title,
      course_id,
      module_id,
      total_content,
      total_content_type: typeof total_content,
      total_content_parsed: parseInt(total_content),
      is_total_content_nan: isNaN(total_content)
    });
    const file = req.file;
    // const file_url = "/uploads/" + file["attachment_file"][0].filename;
    let file_url = null;
    if (file) {
      const filedata = await uploadDocToS3(file.buffer, file.originalname);
      file_url = filedata.path;
    }
    const db_name = req.user.db_name;

    if (Object.keys(req.body).length == 0 || Object.keys(file).length == 0) {
      return res.status(400).json({
        success: false,
        data: {
          error_msg: "Required fields are missing.",
          response: {
            title: "Title is required",
            course_id: "Course ID is required",
            module_id: "Module ID is required",
            attachment_file: "File URL is required",
            total_content: " ",
          },
        },
      });
    }

    // ✅ FIX: Handle total_content properly and calculate serial_no
    let serialNo = 1; // Default value

    if (total_content !== undefined && total_content !== null && !isNaN(total_content)) {
      serialNo = parseInt(total_content) + 1;
    } else {
      // If total_content is invalid, calculate from database
      try {
        const [maxSerialResult] = await mysqlServerConnection.query(
          `SELECT MAX(serial_no) as max_serial FROM (
            SELECT serial_no FROM ${db_name}.videos WHERE module_id = ? AND is_deleted = 0
            UNION ALL
            SELECT serial_no FROM ${db_name}.documents WHERE module_id = ? AND is_deleted = 0
            UNION ALL
            SELECT serial_no FROM ${db_name}.assessments WHERE module_id = ? AND is_deleted = 0
            UNION ALL
            SELECT serial_no FROM ${db_name}.assignments WHERE module_id = ? AND is_deleted = 0
          ) AS all_content`,
          [module_id, module_id, module_id, module_id]
        );

        const maxSerial = maxSerialResult[0]?.max_serial;
        serialNo = maxSerial ? parseInt(maxSerial) + 1 : 1;
      } catch (error) {
        console.error("Error calculating serial number:", error);
        serialNo = 1;
      }
    }

    console.log("📌 Document Serial Number:", serialNo, "| total_content received:", total_content);

    await mysqlServerConnection.query(
      `INSERT INTO ${db_name}.documents (doc_name, doc_url , created_by,is_active,course_id,module_id,serial_no) VALUES (?, ?, ?, ?, ?, ?,?)`,
      [
        title,
        file_url,
        req.user.userId,
        0,
        course_id,
        module_id,
        serialNo,
      ]
    );

    res
      .status(201)
      .json({ success: true, message: "Document created successfully." });
  } catch (error) {
    console.error("Error creating document:", error);
    res.status(500).json({ success: false, message: "Internal server error." });
  }
};

// const downloadTraineeData = async (req, res) => {
//     try {
//         const { course_id } = req.params;

//         const dbName = req.user.db_name;
//         if (!course_id) {
//             return res.status(400).json({
//                 success: false,
//                 message: 'Course ID is required.',
//             });
//         }

//         // Fetch data for both passed and failed trainees
//         const [results] = await mysqlServerConnection.query(`
//             SELECT
//                 u.id AS trainee_id,
//                 u.name AS trainee_name,
//                 u.email,
//                 CASE
//                     WHEN SUM(au.is_completed) = COUNT(a.id) THEN 'Passed'
//                     ELSE 'Failed'
//                 END AS overall_status,
//                 a.assessment_name,
//                 MAX(au.is_completed) AS is_completed
//             FROM ${dbName}.mycourses mc
//             JOIN ${dbName}.users u ON u.id = mc.user_id
//             LEFT JOIN ${dbName}.assessments a ON a.course_id = mc.course_id
//             LEFT JOIN ${dbName}.assessment_users au ON au.assessment_id = a.id AND au.user_id = mc.user_id
//             WHERE mc.course_id = ?
//             GROUP BY u.id, u.name, u.email, a.assessment_name;
//         `, [course_id]);

//         if (!results.length) {
//             return res.status(404).json({
//                 success: false,
//                 message: 'No data found for the given criteria.',
//             });
//         }

//         // Separate data into passed and failed
//         const passedTrainees = results.filter(trainee => trainee.overall_status === 'Passed');
//         const failedTrainees = results.filter(trainee => trainee.overall_status === 'Failed');

//         // Create worksheets
//         const workbook = XLSX.utils.book_new();
//         if (passedTrainees.length > 0) {
//             const passedSheet = XLSX.utils.json_to_sheet(passedTrainees);
//             XLSX.utils.book_append_sheet(workbook, passedSheet, 'Passed Trainees');
//         }
//         if (failedTrainees.length > 0) {
//             const failedSheet = XLSX.utils.json_to_sheet(failedTrainees);
//             XLSX.utils.book_append_sheet(workbook, failedSheet, 'Failed Trainees');
//         }

//         // Generate file
//         const filePath = path.join(__dirname, `trainee_data_${Date.now()}.xlsx`);
//         XLSX.writeFile(workbook, filePath);

//         // Send file for download
//         res.download(filePath, (err) => {
//             if (err) {
//                 console.error('Error sending file:', err);
//             }
//             // Delete file after download
//             fs.unlinkSync(filePath);
//         });
//     } catch (error) {
//         console.error('Error in /download-trainee-data:', error);
//         res.status(500).json({
//             success: false,
//             message: 'Internal server error',
//         });
//     }
// };

const downloadTraineeData = async (req, res) => {
  try {
    const { course_id } = req.params;
    const dbName = req.user.db_name;

    if (!course_id) {
      return res.status(400).json({
        success: false,
        message: "Course ID is required.",
      });
    }

    // Fetch the course name using the course_id
    const [courseResult] = await mysqlServerConnection.query(
      `SELECT course_name FROM ${dbName}.courses WHERE id = ?`,
      [course_id]
    );

    if (!courseResult.length) {
      return res.status(404).json({
        success: false,
        message: "Course not found.",
      });
    }

    const courseName = courseResult[0].course_name.replace(
      /[^a-zA-Z0-9]/g,
      "_"
    ); // Sanitize file name

    // Fetch trainee data
    const [results] = await mysqlServerConnection.query(
      `
            SELECT 
                u.id AS trainee_id,
                u.employee_no AS employee_id,
                u.name AS trainee_name,
                u.email,
                a.assessment_name,
                COUNT(q.id) AS total_questions,
                IFNULL(SUM(CASE WHEN qa.is_correct = 1 THEN 1 ELSE 0 END), 0) AS correct_answers,
                IFNULL(ROUND((SUM(CASE WHEN qa.is_correct = 1 THEN 1 ELSE 0 END) / COUNT(q.id)) * 100, 2), 0) AS score_percentage,
                au.createdAt AS assessment_completed_date
            FROM ${dbName}.mycourses mc
            JOIN ${dbName}.users u ON u.id = mc.user_id
            JOIN ${dbName}.assessments a ON a.course_id = mc.course_id
            JOIN ${dbName}.questions q ON q.assessment_id = a.id
            LEFT JOIN ${dbName}.question_answer qa ON qa.question_id = q.id AND qa.user_id = mc.user_id
            LEFT JOIN ${dbName}.assessment_users au ON au.assessment_id = a.id AND au.user_id = mc.user_id
            WHERE mc.course_id = ?
            GROUP BY u.id, u.employee_no, u.name, u.email, a.id, a.assessment_name, au.createdAt;
        `,
      [course_id]
    );

    if (!results.length) {
      return res.status(404).json({
        success: false,
        message: "No data found for the given criteria.",
      });
    }

    // Process trainee data
    const traineeData = {};
    results.forEach((row) => {
      if (!traineeData[row.trainee_id]) {
        traineeData[row.trainee_id] = {
          employee_id: String(row.employee_id),
          trainee_name: String(row.trainee_name),
          email: String(row.email),
          completed: 0,
          total_score_percentage: 0,
          total_assessments: 0,
        };
      }
      traineeData[row.trainee_id].total_score_percentage +=
        parseFloat(row.score_percentage) || 0;
      traineeData[row.trainee_id].completed +=
        parseInt(row.correct_answers) || 0;
      traineeData[row.trainee_id].total_assessments++;
      traineeData[row.trainee_id][row.assessment_name] = `${
        parseInt(row.correct_answers) || 0
      }/${parseInt(row.total_questions) || 0} (Completed: ${
        row.assessment_completed_date
          ? new Date(row.assessment_completed_date).toLocaleDateString()
          : "N/A"
      })`;
    });

    // Convert data to an array for the sheet
    const finalData = Object.values(traineeData).map((item) => {
      return {
        "Employee ID": item.employee_id,
        trainee_name: item.trainee_name,
        email: item.email,
        ...Object.keys(item)
          .filter(
            (key) =>
              ![
                "employee_id",
                "trainee_name",
                "email",
                "completed",
                "total_score_percentage",
                "total_assessments",
              ].includes(key)
          )
          .reduce((acc, key) => {
            acc[key] = item[key];
            return acc;
          }, {}),
        completed: item.completed.toString(),
        score: `${(
          item.total_score_percentage / item.total_assessments
        ).toFixed(2)}%`,
      };
    });

    // Create the Excel workbook
    const workbook = XLSX.utils.book_new();
    const worksheet = XLSX.utils.json_to_sheet(finalData);
    XLSX.utils.book_append_sheet(workbook, worksheet, "Trainee Data");

    // Generate the file path with course name
    const filePath = path.join(__dirname, `${courseName}_${Date.now()}.xlsx`);
    XLSX.writeFile(workbook, filePath);

    // Send the file for download
    res.download(filePath, (err) => {
      if (err) {
        console.error("Error sending file:", err);
      }
      fs.unlinkSync(filePath); // Delete file after download
    });
  } catch (error) {
    console.error("Error in /download-trainee-data:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error",
    });
  }
};

const EditDocument = async (req, res) => {
  try {
    const { doc_id, doc_name, file_changed } = req.body;
    let doc_url = req.body.doc_url; // fallback in case file not changed
    console.log("Edit Docs Request body:", doc_id, doc_name, file_changed);
    // Access the file from req.file (not req.files)
    const file = req.file;
    console.log("Edit Docs file:", file);
    // If file_changed is true but no file is uploaded
    if (file_changed === "true" && !file) {
      return res
        .status(400)
        .json({ success: false, message: "Document file is required." });
    }
    // If file is changed, upload to S3 and get new doc_url
    if (file_changed === "true" || file_changed === true) {
      console.log("File is changed");
      const fileBuffer = file.buffer;
      const fileName = file.originalname;
      const s3UploadResult = await uploadDocToS3(fileBuffer, fileName);
      doc_url = s3UploadResult.path;
      console.log("Document URL:", doc_url);
    } else {
      // Get existing doc_url from DB
      const [rows] = await mysqlServerConnection.query(
        `SELECT doc_url FROM ${req.user.db_name}.documents WHERE id = ?`,
        [doc_id]
      );
      doc_url = rows[0]?.doc_url || doc_url;
    }
    console.log("Document URL after processing:", doc_url);
    // Update database
    await mysqlServerConnection.query(
      `UPDATE ${req.user.db_name}.documents SET doc_name = ?, doc_url = ? WHERE id = ?`,
      [doc_name, doc_url, doc_id]
    );
    res
      .status(200)
      .json({ success: true, message: "Document updated successfully." });
  } catch (error) {
    console.error("Error updating document:", error);
    res.status(500).json({ success: false, message: "Internal server error." });
  }
};

const getDocumentById = async (req, res) => {

  try {
    const { doc_id } = req.params;
    const db_name = req.user.db_name;

    console.log("Document ID:", doc_id);

    if (!doc_id) {
      return res.status(400).json({
        success: false,
        message: "Document ID is required",
      });
    }

    const [document] = await mysqlServerConnection.query(
      `SELECT * FROM ${db_name}.documents WHERE id = ?`,
      [doc_id]
    );

    console.log("Document:", document);

    if (document.length === 0) {
      return res.status(404).json({
        success: false,
        message: "Document not found",
      });
    }

    res.status(200).json({
      success: true,
      data: document[0],
    });
  } catch (error) {
    console.error("Error fetching document:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error",
    });
  }
}

const getQuestions = async (req, res) => {
  try {
    const { assessment_id, module_id } = req.params;
    const db_name = req.user.db_name;

    if (!assessment_id || !module_id) {
      return res.status(400).json({
        success: false,
        message: "Missing required fields",
      });
    }

    try {
      const [questions] = await mysqlServerConnection.query(
        `SELECT 
            q.id, q.question, q.question_type, q.options, q.correct_option, q.updated_at, q.created_at,
            q.question_content_type, q.question_media_url, q.ref_video_id, q.video_timestamp,
            v.video_name, v.video_url, v.video_title
         FROM ${db_name}.questions q
         LEFT JOIN ${db_name}.videos v ON q.ref_video_id = v.id
         WHERE q.module_id = ? AND q.assessment_id = ? AND q.is_deleted = 0`,
        [module_id, assessment_id]
      );

      const questionsWithOptions = await Promise.all(
        questions.map(async (question) => {
          const [options] = await mysqlServerConnection.query(
            `SELECT option_key, content_type, content_value, media_url, text_fallback, is_correct 
             FROM ${db_name}.question_options 
             WHERE question_id = ?`,
            [question.id]
          );

          if (options && options.length > 0) {
            const formattedOptions = options.map((opt) => ({
              key: opt.option_key,
              content_type: opt.content_type,
              content_value: opt.content_value,
              media_url: opt.media_url,
              text_fallback: opt.text_fallback,
              isCorrect: opt.is_correct === 1,
            }));

            return {
              ...question,
              formatted_options: formattedOptions,
            };
          }

          return question;
        })
      );

      res.status(200).json({
        success: true,
        data: {
          response: questionsWithOptions,
        },
      });
    } catch (error) {
      console.error("Database error:", error);
      res.status(500).json({
        success: false,
        message: "Failed to get questions",
        error: error.message,
      });
    }
  } catch (error) {
    console.error("Error in getQuestions:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error",
      error: error.message,
    });
  }
};

const editAssessment = async (req, res) => {
  try {
    const {
      id,
      title,
      module_id,
      duration,
      earn_point,
      earn_point_applicable_after,
    } = req.body;
    const db_name = req.user.db_name;
    await mysqlServerConnection.query(
      `UPDATE ${db_name}.assessments SET assessment_name = ?, duration = ?, earn_point = ?, earn_point_applicable_after = ? WHERE id = ? AND module_id = ?`,
      [title, duration, earn_point, earn_point_applicable_after, id, module_id]
    );
    res
      .status(200)
      .json({ success: true, message: "Assessment updated successfully." });
  } catch (error) {
    console.error("Error updating assessment:", error);
    res.status(500).json({ success: false, message: "Internal server error." });
  }
};

// Get videos for a specific course/module for video selection in questions
const getCourseVideos = async (req, res) => {
  try {
    const { course_id, module_id } = req.params;
    const db_name = req.user.db_name;

    // Validate required fields
    if (!course_id) {
      return res.status(400).json({
        success: false,
        message: "Course ID is required",
      });
    }

    let query = `SELECT id, video_name, video_url, video_title, video_description, video_duration, serial_no
                 FROM ${db_name}.videos
                 WHERE course_id = ? AND is_deleted = 0 AND is_active = 1`;
    let params = [course_id];

    // If module_id is provided, filter by module as well
    if (module_id) {
      query += ` AND module_id = ?`;
      params.push(module_id);
    }

    query += ` ORDER BY serial_no ASC, id ASC`;

    const [videos] = await mysqlServerConnection.query(query, params);

    res.status(200).json({
      success: true,
      data: videos,
    });
  } catch (error) {
    console.error("Error in getCourseVideos:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error",
      error: error.message,
    });
  }
};



const deleteQuestion = async (req, res) => {
  try {
    const { question_id } = req.params;
    const db_name = req.user.db_name;

    console.log("🟡 [DELETE QUESTION] Request received");
    console.log("➡️ Question ID:", question_id);
    console.log("➡️ Database Name:", db_name);

    const [updateResult] = await mysqlServerConnection.query(
      `UPDATE ${db_name}.questions SET is_deleted = 1 WHERE id = ?`,
      [question_id]
    );

    console.log("🛠 Query Result:", updateResult);

    if (updateResult.affectedRows === 0) {
      console.warn("⚠️ Question not found or already deleted:", question_id);
      return res.status(404).json({
        success: false,
        message: "Question not found",
      });
    }

    console.log("✅ Question soft-deleted successfully:", question_id);

    res.status(200).json({
      success: true,
      message: "Question soft-deleted successfully",
    });
  } catch (error) {
    console.error("❌ Error in deleteQuestion:", error);
    res.status(500).json({
      success: false,
      data: {
        error_msg: "Internal server error",
      },
    });
  }
};


const editVideoContent = async (req, res) => {
  try {
    const {
      video_id, // Required for identifying the video to update
      module_id,
      video_title,
      description,
      attachment_detail,
      duration, // Array of details to update in `video_details`
      video_changed, // Flag to indicate if video file has been changed
      video_removed, // Flag to indicate if video has been removed
    } = req.body;

    console.log("edit video content", req.body);

    let video_name = video_title;

    console.log("video_id edit:", req.body);

    const db_name = req.user.db_name;

    const file = req.file || null;

    console.log("FILE", file);
    console.log("video_changed", video_changed);
    console.log("video_removed", video_removed);

    // Validate required fields
    if (!video_id || !module_id || !video_title) {
      return res.status(400).json({
        success: false,
        message: "Missing required fields",
      });
    }

    try {
      // Get the current video URL to potentially delete from S3 later
      const [rows] = await mysqlServerConnection.query(
        `SELECT video_url FROM ${db_name}.videos WHERE id = ?`,
        [video_id]
      );

      let video_url = null;
      let currentVideoUrl = rows.length > 0 ? rows[0].video_url : null;

      // Upload new video to S3 if provided and video has changed
      if (file && video_changed === "true") {
        // Upload video to S3
        let path = await uploadVideoToS3(file.buffer);
        video_url = path.path;

        // If there was a previous video, we could delete it from S3 here
        // This would require implementing a deleteFromS3 function
      } else if (video_changed === "true" && !file) {
        // If video_changed is true but no file is provided, this is an error
        return res.status(400).json({
          success: false,
          message: "Video file is required when video_changed is true",
        });
      }

      // Handle video removal without replacement
      if (video_removed === "true" && !file) {
        video_url = ""; // Set to empty string to indicate no video
      }

      // Dynamically build the update query based on what fields are changing
      let updateFields = [];
      let updateParams = [];

      // Only include video_url in the update if it's actually changing
      if ((video_changed === "true" && file) || video_removed === "true") {
        updateFields.push("video_url = ?");
        updateParams.push(video_url);
      }

      // Always update these fields
      updateFields.push("video_name = ?");
      updateParams.push(video_name);

      updateFields.push("video_description = ?");
      updateParams.push(description || null);

      updateFields.push("video_duration = ?");
      updateParams.push(duration || null);

      // Add the WHERE clause parameters
      updateParams.push(video_id, module_id);

      // Update the `videos` table
      const updateVideosQuery = `
                UPDATE ${db_name}.videos
                SET ${updateFields.join(", ")}
                WHERE id = ? AND module_id = ?;
            `;

      await mysqlServerConnection.query(updateVideosQuery, updateParams);

      // Update `video_details` table (if applicable)
      if (attachment_detail) {
        const updateDetailsQuery = `
                    UPDATE ${db_name}.video_details
                    SET attachment_detail = ?
                    WHERE video_id = ?;
                `;

        await mysqlServerConnection.query(updateDetailsQuery, [
          attachment_detail,
          video_id,
        ]);
      }

      console.log("Video content updated successfully");
      res.status(200).json({
        success: true,
        message: "Video content updated successfully",
      });
    } catch (error) {
      console.log("Error during video content update:", error);
      res.status(500).json({
        success: false,
        message: "Internal server error",
        error: error.message,
      });
    }
  } catch (error) {
    console.log("Error in editVideoContent:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error",
      error: error.message,
    });
  }
};



const getVideoDetailsById = async (req, res, next) => {
  try {
    const { video_id, module_id } = req.params;

    if (!video_id) {
      return res.status(400).json({
        success: false,
        message: "Video ID is required",
      });
    }

    if (!module_id) {
      return res.status(400).json({
        success: false,
        message: "Module ID is required",
      });
    }

    const db = req.user.db_name;

    const [video] = await mysqlServerConnection.query(
      `
      SELECT 
        video_title,
        video_name,
        video_duration,
        video_description,
        video_url,
        upload_view_type
      FROM ${db}.videos
      WHERE id = ? AND module_id = ? AND is_deleted = 0
      `,
      [video_id, module_id]
    );

    if (video.length === 0) {
      return res.status(404).json({
        success: false,
        message: "Video not found",
      });
    }

    return res.status(200).json({
      success: true,
      message: "Video details fetched successfully",
      data: video[0],
    });

  } catch (error) {
    console.error("❌ Error fetching video details:", error);
    return res.status(500).json({
      success: false,
      message: "Internal server error",
    });
  }
};


const addQuestion = async (req, res) => {
  try {
    const {
      module_id,
      assessment_id,
      question_type,
      question_text,
      options,
      correct_option,
      question_content_type,
      options_data,
      ref_video_id,
      video_timestamp,
    } = req.body;

    console.log("📥 Incoming Request Body:", req.body);

    const db_name = req.user.db_name;
    const userId = req.user.userId;

    console.log("👤 User ID:", userId);
    console.log("📂 DB Name:", db_name);

    // Basic validation
    if (!assessment_id || !question_type || !question_text) {
      console.warn("❌ Required fields missing!");
      return res.status(400).json({
        success: false,
        message: "Missing required fields",
      });
    }

    const validTypes = ["written", "mcq", "normal"];
    if (!validTypes.includes(question_type)) {
      console.warn("❌ Invalid question_type:", question_type);
      return res.status(400).json({
        success: false,
        message: "Invalid question type",
      });
    }

    // Validate options for MCQ or Normal
    if (["mcq", "normal"].includes(question_type)) {
      try {
        const parsedOptions = typeof options === "string" ? JSON.parse(options) : options;
        const parsedCorrect = typeof correct_option === "string" ? JSON.parse(correct_option) : correct_option;

        if (!parsedOptions || Object.keys(parsedOptions).length === 0 || !parsedCorrect || parsedCorrect.length === 0) {
          console.warn("❌ Options or correct option invalid or empty");
          return res.status(400).json({
            success: false,
            message: "Options and correct option are required for MCQ and normal questions",
          });
        }

        console.log("✅ Parsed Options & Correct Option Validated");
      } catch (error) {
        console.error("❌ Parsing error in options/correct_option:", error.message);
        return res.status(400).json({
          success: false,
          message: "Invalid options or correct_option format",
          error: error.message,
        });
      }
    }

    const connection = await mysqlServerConnection.getConnection();
    await connection.beginTransaction();

    let question_media_url = null;

    try {
      if (req.files) {
        const mediaFile = Array.isArray(req.files)
          ? req.files.find(file => file.fieldname === "question_media")
          : req.files?.question_media?.[0];

        if (mediaFile) {
          const contentType = question_content_type || "image";
          let uploadResult = null;

          if (contentType === "image") {
            uploadResult = await uploadImageToS3(mediaFile.buffer);
          } else if (contentType === "audio") {
            uploadResult = await uploadAudioToS3(mediaFile.buffer, mediaFile.originalname);
          }

          question_media_url = uploadResult?.path || null;
          console.log("🖼️ Question media uploaded:", question_media_url);
        }
      }

      // Insert into questions
      const [questionResult] = await connection.query(
        `INSERT INTO ${db_name}.questions (
          assessment_id, question_type, question, options, correct_option, module_id,
          question_content_type, question_media_url, ref_video_id, video_timestamp
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          assessment_id,
          question_type,
          question_text,
          typeof options === "string" ? options : JSON.stringify(options),
          typeof correct_option === "string" ? correct_option : JSON.stringify(correct_option),
          module_id,
          question_content_type || "text",
          question_media_url,
          ref_video_id || null,
          video_timestamp || null,
        ]
      );

      const questionId = questionResult.insertId;
      console.log("✅ Question inserted with ID:", questionId);

      // Handle options_data if present
      const parsedOptionsData = typeof options_data === "string" ? JSON.parse(options_data) : options_data;
      const parsedCorrect = typeof correct_option === "string" ? JSON.parse(correct_option) : correct_option;

      if (Array.isArray(parsedOptionsData)) {
        for (const optionData of parsedOptionsData) {
          const { option_key, text_value, content_type = "text" } = optionData;
          const mediaFieldName = `option_media_${option_key}`;
          let media_url = null;

          const mediaFile = Array.isArray(req.files)
            ? req.files.find(file => file.fieldname === mediaFieldName)
            : req.files?.[mediaFieldName]?.[0];

          if (mediaFile) {
            let uploadResult;
            if (content_type === "image") {
              uploadResult = await uploadImageToS3(mediaFile.buffer);
            } else if (content_type === "audio") {
              uploadResult = await uploadAudioToS3(mediaFile.buffer, mediaFile.originalname);
            }
            media_url = uploadResult?.path || null;
            console.log(`🎞️ Uploaded media for option ${option_key}: ${media_url}`);
          }

          await connection.query(
            `INSERT INTO ${db_name}.question_options (
              question_id, option_key, content_type, content_value, media_url, text_fallback,
              is_correct, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)`,
            [
              questionId,
              option_key,
              content_type,
              text_value,
              media_url,
              text_value,
              parsedCorrect.includes(text_value) ? 1 : 0,
            ]
          );
          console.log(`✅ Inserted option ${option_key} (${text_value})`);
        }
      } else if (options) {
        const parsedOptions = typeof options === "string" ? JSON.parse(options) : options;
        const parsedCorrect = typeof correct_option === "string" ? JSON.parse(correct_option) : correct_option;

        if (typeof parsedOptions === "object" && !Array.isArray(parsedOptions)) {
          for (const [key, value] of Object.entries(parsedOptions)) {
            let content_type = req.body[`option_content_type_${key}`] || "text";
            const mediaFieldName = `option_media_${key}`;
            let media_url = null;

            const mediaFile = Array.isArray(req.files)
              ? req.files.find(file => file.fieldname === mediaFieldName)
              : req.files?.[mediaFieldName]?.[0];

            if (mediaFile) {
              let uploadResult;
              if (content_type === "image") {
                uploadResult = await uploadImageToS3(mediaFile.buffer);
              } else if (content_type === "audio") {
                uploadResult = await uploadAudioToS3(mediaFile.buffer, mediaFile.originalname);
              }
              media_url = uploadResult?.path || null;
              console.log(`🎞️ Uploaded media for option ${key}: ${media_url}`);
            }

            await connection.query(
              `INSERT INTO ${db_name}.question_options (
                question_id, option_key, content_type, content_value, media_url, text_fallback,
                is_correct, created_at, updated_at
              ) VALUES (?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)`,
              [
                questionId,
                key,
                content_type,
                value,
                media_url,
                value,
                parsedCorrect.includes(value) ? 1 : 0,
              ]
            );
            console.log(`✅ Inserted option ${key} (${value})`);
          }
        }
      }

      await connection.commit();
      connection.release();

      console.log("🎉 Question & options added successfully");
      return res.status(201).json({
        success: true,
        message: "Question added successfully",
        question_id: questionId,
      });
    } catch (err) {
      await connection.rollback();
      connection.release();
      console.error("❌ DB Error in transaction:", err);
      return res.status(500).json({
        success: false,
        message: "Failed to add question",
        error: err.message,
      });
    }
  } catch (err) {
    console.error("❌ Outer Error in addQuestion:", err);
    return res.status(500).json({
      success: false,
      message: "Internal server error",
      error: err.message,
    });
  }
};


const editQuestion = async (req, res) => {
  try {
    const question_id = req.body.id || req.params.question_id;
    console.log("QUESTION ID:", question_id);

    if (!question_id) {
      return res.status(404).json({
        success: false,
        message: "Please provide question id",
      });
    }

    const {
      assessment_id,
      module_id,
      question_text,
      question_type,
      rank_point,
      collect_point,
      options,
      correct_option,
      question_content_type,
      options_data,
      deleted_option_keys, // Add deleted option keys
      ref_video_id, // Add video reference fields
      video_timestamp,
    } = req.body;

    console.log("Requ body in editQuestion", req.body);
    console.log("Video reference data in editQuestion:");
    console.log("- ref_video_id:", ref_video_id);
    console.log("- video_timestamp:", video_timestamp);
    // Get connection and start transaction
    const connection = await mysqlServerConnection.getConnection();
    await connection.beginTransaction();

    try {
      // Handle question media upload if present
      let question_media_url = null;
      if (req.files) {
        // Check if req.files is an array (upload.any()) or an object (upload.fields())
        if (Array.isArray(req.files)) {
          // Find the question_media file
          const mediaFile = req.files.find(
            (file) => file.fieldname === "question_media"
          );
          if (mediaFile) {
            const contentType = question_content_type || "image";

            // Upload to S3 based on content type
            let uploadResult;
            if (contentType === "image") {
              uploadResult = await uploadImageToS3(mediaFile.buffer);
            } else if (contentType === "audio") {
              uploadResult = await uploadAudioToS3(
                mediaFile.buffer,
                mediaFile.originalname
              );
            }

            if (uploadResult) {
              question_media_url = uploadResult.path;
            }
          }
        } else if (req.files.question_media && req.files.question_media[0]) {
          // Legacy format (upload.fields())
          const mediaFile = req.files.question_media[0];
          const contentType = question_content_type || "image";

          // Upload to S3 based on content type
          let uploadResult;
          if (contentType === "image") {
            uploadResult = await uploadImageToS3(mediaFile.buffer);
          } else if (contentType === "audio") {
            uploadResult = await uploadAudioToS3(
              mediaFile.buffer,
              mediaFile.originalname
            );
          }

          if (uploadResult) {
            question_media_url = uploadResult.path;
          }
        }
      } else {
        // Check if there's an existing media URL in the request
        if (req.body.existing_question_media_url) {
          // If no new file but we have an existing URL in the request, use it
          question_media_url = req.body.existing_question_media_url;
          console.log(
            "Using existing question media URL from request:",
            question_media_url
          );
        } else {
          // If no new media file and no existing URL in request, get the existing media URL if any
          const [existingQuestion] = await connection.query(
            `SELECT question_media_url FROM ${req.user.db_name}.questions WHERE id = ?`,
            [question_id]
          );

          if (
            existingQuestion.length > 0 &&
            existingQuestion[0].question_media_url
          ) {
            question_media_url = existingQuestion[0].question_media_url;
            console.log(
              "Retrieved existing question media URL from database:",
              question_media_url
            );
          }
        }
      }

      console.log("question_media_url", question_media_url);
      console.log("Request body:", req.body);
      console.log(
        "existing_question_media_url:",
        req.body.existing_question_media_url
      );

      // Final check to ensure we have a media URL if one was provided
      if (!question_media_url && req.body.existing_question_media_url) {
        question_media_url = req.body.existing_question_media_url;
        console.log(
          "Setting question_media_url from existing_question_media_url as last resort"
        );
      }

      console.log("question_media_url before update:", question_media_url);
      // Update the question details
      const optionsJSON = options ? JSON.stringify(options) : null;
      const correctOptionJSON = correct_option
        ? JSON.stringify(correct_option)
        : null;

      const [result] = await connection.query(
        `
                UPDATE ${req.user.db_name}.questions
                SET assessment_id = ?,
                    module_id = ?,
                    question_type = ?,
                    rank_point = ?,
                    question = ?,
                    collect_point = ?,
                    options = ?,
                    correct_option = ?,
                    question_content_type = ?,
                    question_media_url = ?,
                    ref_video_id = ?,
                    video_timestamp = ?
                WHERE id = ?
            `,
        [
          assessment_id,
          module_id,
          question_type,
          rank_point || 0,
          question_text,
          collect_point || 0,
          typeof options === "string" ? options : JSON.stringify(options),
          typeof correct_option === "string"
            ? correct_option
            : JSON.stringify(correct_option),
          question_content_type || "text",
          question_media_url,
          ref_video_id || null, // Add video reference fields
          video_timestamp || null,
          question_id,
        ]
      );

      console.log("Result", result);
      if (result.affectedRows === 0) {
        await connection.rollback();
        connection.release();
        return res.status(404).json({
          success: false,
          message: "Question not found or is already deleted",
        });
      }

      // Parse deleted option keys first (outside the options_data check)
      const parsedDeletedOptionKeys =
        typeof deleted_option_keys === "string"
          ? JSON.parse(deleted_option_keys)
          : deleted_option_keys;

      // Process options data and media if available
      if (options_data) {
        const parsedOptionsData =
          typeof options_data === "string"
            ? JSON.parse(options_data)
            : options_data;

        if (Array.isArray(parsedOptionsData)) {
          for (const optionData of parsedOptionsData) {
            const { option_key, text_value, is_correct } = optionData;
            let content_type = optionData.content_type || "text";

            // Check if there's a media file for this option
            let media_url = null;
            const mediaFieldName = `option_media_${option_key}`;

            console.log(
              `Looking for option media file with field name: ${mediaFieldName}`
            );
            console.log("Option data:", JSON.stringify(optionData));
            console.log("Content type for option:", content_type);

            // With upload.any(), req.files is an array of files
            if (req.files && Array.isArray(req.files)) {
              // Find the file with the matching fieldname
              const optionMediaFile = req.files.find(
                (file) => file.fieldname === mediaFieldName
              );

              if (optionMediaFile) {
                console.log(
                  `Found media file for option ${option_key}:`,
                  optionMediaFile.originalname
                );

                // Upload to S3 based on content type
                let uploadResult;
                if (content_type === "image") {
                  uploadResult = await uploadImageToS3(optionMediaFile.buffer);
                } else if (content_type === "audio") {
                  uploadResult = await uploadAudioToS3(
                    optionMediaFile.buffer,
                    optionMediaFile.originalname
                  );
                }

                if (uploadResult) {
                  media_url = uploadResult.path;
                  console.log(
                    `Uploaded media for option ${option_key} to: ${media_url}`
                  );
                } else {
                  console.log(
                    `Failed to upload media for option ${option_key}`
                  );
                }
              } else {
                console.log(
                  `No media file found for option ${option_key} in array format`
                );

                // Check if there's an existing media URL in the optionData
                if (optionData.existing_media_url) {
                  media_url = optionData.existing_media_url;
                  console.log(
                    `Using existing media URL from request for option ${option_key}: ${media_url}`
                  );
                } else {
                  // If no new media file and no existing URL in request, check the database
                  const [existingOption] = await connection.query(
                    `SELECT media_url FROM ${req.user.db_name}.question_options 
                                        WHERE question_id = ? AND option_key = ?`,
                    [question_id, option_key]
                  );

                  if (
                    existingOption.length > 0 &&
                    existingOption[0].media_url
                  ) {
                    media_url = existingOption[0].media_url;
                    console.log(
                      `Retrieved existing media URL from database for option ${option_key}: ${media_url}`
                    );
                  }
                }
              }
            }

            // Check if the option already exists in question_options table
            const [existingOption] = await connection.query(
              `SELECT id FROM ${req.user.db_name}.question_options 
                             WHERE question_id = ? AND option_key = ?`,
              [question_id, option_key]
            );

            if (existingOption.length > 0) {
              // Update existing option
              await connection.query(
                `UPDATE ${req.user.db_name}.question_options 
                                 SET content_type = ?, 
                                     content_value = ?, 
                                     media_url = ?, 
                                     text_fallback = ?, 
                                     is_correct = ?,
                                     updated_at = CURRENT_TIMESTAMP 
                                 WHERE id = ?`,
                [
                  content_type,
                  text_value,
                  media_url,
                  text_value, // Use text_value as fallback if not provided
                  is_correct ||
                  (correct_option && correct_option.includes(text_value))
                    ? 1
                    : 0,
                  existingOption[0].id,
                ]
              );
            } else {
              // Insert new option
              await connection.query(
                `INSERT INTO ${req.user.db_name}.question_options (
                                    question_id, option_key, content_type, content_value, media_url, text_fallback, 
                                    is_correct, created_at, updated_at
                                ) VALUES (?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)`,
                [
                  question_id,
                  option_key,
                  content_type,
                  text_value,
                  media_url,
                  text_value, // Use text_value as fallback if not provided
                  is_correct ||
                  (correct_option && correct_option.includes(text_value))
                    ? 1
                    : 0,
                ]
              );
            }
          }
        }
      }

      // Handle deleted options - delete rows from question_options table
      if (parsedDeletedOptionKeys && parsedDeletedOptionKeys.length > 0) {
        console.log("Deleting options with keys:", parsedDeletedOptionKeys);

        for (const option_key of parsedDeletedOptionKeys) {
          await connection.query(
            `DELETE FROM ${req.user.db_name}.question_options
             WHERE question_id = ? AND option_key = ?`,
            [question_id, option_key]
          );
        }
      }

      // Commit the transaction
      await connection.commit();
      connection.release();

      return res.status(200).json({
        success: true,
        message: "Question updated successfully",
      });
    } catch (error) {
      // Rollback in case of error
      await connection.rollback();
      connection.release();
      throw error;
    }
  } catch (error) {
    console.error("Error updating question:", error);
    return res.status(500).json({
      success: false,
      message: error.message || "Internal Server Error",
    });
  }
};

module.exports = {
  getAllCourses,
  createAssignment,
  moduleActiveDeactive,
  deleteModule,
  createAssessment,
  updateAssessment,
  // deleteAssignment,s
  updateAssignment,
  deleteAssessment,
  createQuestion,
  addMCQOptions,
  addCorrectAnswer,
  deleteMCQOption,
  getAssignmentsByModule,
  getAssessmentsByModule,
  getQuestionsForAssessment,
  deleteCourse,
  createCourseApproval,
  approveCourseApproval,
  deleteModuleContent,
  getVideoDetails,
  updateVideoDetails,
  changeAssessmentStatus,
  editMCQOption,
  editModule,
  changeAssignmentStatus,
  editQuestion,
  getUserAllResults,
  getUserAssessmentResults,
  userActivity,
  getAssignmentResults,
  getSurvyResults,
  getUserCourseList,
  getPerformanceSummary,
  downloadTraineeData,
  createDocument,
  ModuleContentStatus,
  EditDocument,
  getDocumentById,
  addQuestion,
  editVideoContent,
  getQuestions,
  deleteQuestion,
  editAssessment,
  getCourseVideos,
  getAllCoursesAPI,
  getVideoDetailsById,
};
