const { mysqlServerConnection } = require("../../../db/db");


const getTraineeClassrooms = async (req, res, next) => {
    try {
        const { user_id, page = 1, limit = 10, search = '' } = req.body;
        const dbName = req.user.db_name;
        const offset = (page - 1) * limit;

        // Validate required fields
        if (!user_id) {
            return res.status(400).json({
                success: false,
                message: 'User ID is required'
            });
        }

        // Check if user exists
        const [userExists] = await mysqlServerConnection.query(
            `SELECT id FROM ${dbName}.users WHERE id = ? AND is_deleted = 0`,
            [user_id]
        );

        if (userExists.length === 0) {
            return res.status(404).json({
                success: false,
                message: 'User not found or has been deleted'
            });
        }

        // Get total count of classrooms for pagination
        let countQuery = `
            SELECT COUNT(*) as total 
            FROM ${dbName}.classroom_trainee ct
            JOIN ${dbName}.classroom c ON ct.class_id = c.id
            WHERE ct.user_id = ? AND c.is_deleted = 0 AND c.is_active = 1`;
        
        let countParams = [user_id];
        
        if (search) {
            countQuery += ` AND (c.cls_name LIKE ? OR c.cls_desc LIKE ? OR c.collaboration_name LIKE ?)`;
            countParams.push(`%${search}%`, `%${search}%`, `%${search}%`);
        }
        
        const [totalResults] = await mysqlServerConnection.query(countQuery, countParams);
        const totalRecords = totalResults[0].total;

        // Get classrooms with pagination and search
        let query = `
        SELECT c.id, c.cls_name, c.cls_desc, c.collaboration_name, c.banner_img, 
        u.name as trainer_name, ct.createdAt as joined_at,
        (SELECT COUNT(*) FROM ${dbName}.classroom_trainee ct2 WHERE ct2.class_id = c.id) as total_users
        FROM ${dbName}.classroom_trainee ct
        JOIN ${dbName}.classroom c ON ct.class_id = c.id
        LEFT JOIN ${dbName}.users u ON c.user_id = u.id
        WHERE ct.user_id = ? AND c.is_deleted = 0 AND c.is_active = 1`;
        
        let params = [user_id];
        
        if (search) {
            query += ` AND (c.cls_name LIKE ? OR c.cls_desc LIKE ? OR c.collaboration_name LIKE ?)`;
            params.push(`%${search}%`, `%${search}%`, `%${search}%`);
        }
        
        query += ` ORDER BY ct.createdAt DESC LIMIT ? OFFSET ?`;
        params.push(Number(limit), offset);
        
        const [classrooms] = await mysqlServerConnection.query(query, params);

        // console.log("🟢 Classrooms fetched:", classrooms);
        
        const totalPages = Math.ceil(totalRecords / limit);

        res.status(200).json({
            success: true,
            data: {
                classrooms,
                user_id: req.user.userId,
                pagination: {
                    totalRecords,
                    totalPages,
                    currentPage: Number(page),
                    limit: Number(limit),
                },
            },
        });

    } catch (error) {
        console.error('Error fetching trainee classrooms:', error);
        return res.status(500).json({
            success: false,
            message: 'An error occurred while fetching classrooms',
            error: error.message
        });
    }
};



const getQuestionsForTraineeAssessment = async (req, res, next) => {
    try {
        const { assessment_id, class_id } = req.body;
        const dbName = req.user.db_name;

        // Validate required fields
        if (!assessment_id || !class_id) {
            return res.status(400).json({
                success: false,
                message: 'Assessment ID and Class ID are required'
            });
        }

        // Check if assessment exists and belongs to the specified class
        const [assessmentExists] = await mysqlServerConnection.query(
            `SELECT * FROM ${dbName}.classroom_assessments 
            WHERE id = ? AND class_id = ? AND is_deleted = 0`,
            [assessment_id, class_id]
        );

        if (assessmentExists.length === 0) {
            return res.status(404).json({
                success: false,
                message: 'Assessment not found or does not belong to the specified classroom'
            });
        }

        // Get assessment details
        const assessment = assessmentExists[0];

        // Get questions for the assessment
        const [questions] = await mysqlServerConnection.query(
            `SELECT qb.id, qb.question_name, qb.question_type
            FROM ${dbName}.classroom_assessment_questions caq
            JOIN ${dbName}.question_bank qb ON caq.question_id = qb.id
            WHERE caq.assessment_id = ? AND caq.class_id = ? AND qb.is_deleted = 0 AND qb.is_active = 1`,
            [assessment_id, class_id]
        );

        // For each question, get its options
        const questionsWithOptions = await Promise.all(questions.map(async (question) => {
            const [options] = await mysqlServerConnection.query(
                `SELECT id, option_number, option_value, is_correct
                FROM ${dbName}.question_bank_options
                WHERE question_id = ? AND is_deleted = 0 AND is_active = 1
                ORDER BY option_number`,
                [question.id]
            );

            return {
                ...question,
                options
            };
        }));

        res.status(200).json({
            success: true,
            data: {
                assessment: {
                    id: assessment.id,
                    assessment_name: assessment.assessment_name,
                    duration: assessment.duration,
                    activate_time: assessment.activate_time,
                    deactivate_time: assessment.deactivate_time,
                    created_at: assessment.created_at,
                    updated_at: assessment.updated_at,
                    class_id: assessment.class_id,
                    total_questions: questionsWithOptions.length,
                    questions: questionsWithOptions,
                },
                user_id: req.user.userId
            },
            message: 'Assessment questions fetched successfully'
        });

    } catch (error) {
        console.error('Error fetching assessment questions:', error);
        return res.status(500).json({
            success: false,
            message: 'An error occurred while fetching assessment questions',
            error: error.message
        });
    }
};

const submitTraineeAssessment = async (req, res, next) => {
    try {
        const { class_id, assessment_id, user_id, quiz_answers } = req.body;
        const dbName = req.user.db_name;

        // Validate required fields
        if (!class_id || !assessment_id || !user_id || !quiz_answers || !Array.isArray(quiz_answers)) {
            return res.status(400).json({
                success: false,
                message: 'Missing required fields or invalid format. class_id, assessment_id, user_id, and quiz_answers array are required'
            });
        }

        // Check if assessment exists and belongs to the specified class
        const [assessmentExists] = await mysqlServerConnection.query(
            `SELECT * FROM ${dbName}.classroom_assessments 
            WHERE id = ? AND class_id = ? AND is_deleted = 0`,
            [assessment_id, class_id]
        );

        // get pass percentage
        const passPercentage = assessmentExists[0].pass_percentage;
        if (assessmentExists.length === 0) {
            return res.status(404).json({
                success: false,
                message: 'Assessment not found or does not belong to the specified classroom'
            });
        }

        // Check if user exists
        const [userExists] = await mysqlServerConnection.query(
            `SELECT id,name FROM ${dbName}.users WHERE id = ? AND is_deleted = 0`,
            [user_id]
        );

        if (userExists.length === 0) {
            return res.status(404).json({
                success: false,
                message: 'User not found or has been deleted'
            });
        }

        // Check if user has already submitted this assessment
        // const [existingResult] = await mysqlServerConnection.query(
        //     `SELECT * FROM ${dbName}.classroom_assessment_results 
        //     WHERE user_id = ? AND class_id = ? AND assessment_id = ? AND is_deleted = 0`,
        //     [user_id, class_id, assessment_id]
        // );

        // if (existingResult.length > 0) {
        //     return res.status(400).json({
        //         success: false,
        //         message: 'User has already submitted this assessment'
        //     });
        // }

        // Get all questions for this assessment
        const [assessmentQuestions] = await mysqlServerConnection.query(
            `SELECT question_id FROM ${dbName}.classroom_assessment_questions 
            WHERE assessment_id = ? AND class_id = ?`,
            [assessment_id, class_id]
        );

        const totalQuestions = assessmentQuestions.length;
        if (totalQuestions === 0) {
            return res.status(400).json({
                success: false,
                message: 'This assessment has no questions'
            });
        }

        // Process each answer
        let totalAttempted = 0;
        let totalCorrect = 0;

        for(const answer of quiz_answers) {
            const { question_id, option } = answer;
                
            if (!question_id || option === undefined) {
                continue; // Skip invalid answers
            }
            totalAttempted++;
            // Check if the question belongs to this assessment
          const questionExists = assessmentQuestions.some(q => q.question_id === question_id);
            if (!questionExists) {
                continue; // Skip questions not in this assessment
            }

            // Check if the answer is correct
            const [correctOption] = await mysqlServerConnection.query(
                `SELECT * FROM ${dbName}.question_bank_options 
                WHERE question_id = ? AND is_correct = 1 AND is_deleted = 0`,
                [question_id]
            );
            
            // console.log("Correct Option:", correctOption);
            // console.log("Option:", option);
            const isCorrect = correctOption.length > 0 && correctOption[0].id === parseInt(option);

            // Record the answer
            await mysqlServerConnection.query(
                `INSERT INTO ${dbName}.classroom_question_assessment_answer 
                (user_id, class_id, assessment_id, question_id, answer, is_correct) 
                VALUES (?, ?, ?, ?, ?, ?)`,
                [user_id, class_id, assessment_id, question_id, option, isCorrect ? 1 : 0]
            );

            if (isCorrect) {
                totalCorrect++;
            }
        }

            // Calculate percentage
            const percentage = (totalCorrect / totalQuestions) * 100;

            // Record the assessment result
            const [resultInsert] = await mysqlServerConnection.query(
                `INSERT INTO ${dbName}.classroom_assessment_results 
                (user_id, class_id, assessment_id, total_questions, total_attempted, total_correct, percentage) 
                VALUES (?, ?, ?, ?, ?, ?, ?)`,
                [user_id, class_id, assessment_id, totalQuestions, totalAttempted, totalCorrect, percentage]
            );
            
            // Get the inserted result ID
            const resultId = resultInsert.insertId;
            
            // Update all the question answers with the result ID
            if (resultId) {
                await mysqlServerConnection.query(
                    `UPDATE ${dbName}.classroom_question_assessment_answer 
                     SET results_id = ? 
                     WHERE user_id = ? AND class_id = ? AND assessment_id = ? AND results_id IS NULL`,
                    [resultId, user_id, class_id, assessment_id]
                );
            }

            res.status(200).json({
                success: true,
                data: {
                    assessment_id,
                    class_id,
                    user_id,
                    total_questions: totalQuestions,
                    total_attempted: totalAttempted,
                    total_correct: totalCorrect,
                    percentage: percentage.toFixed(2),
                    pass_percentage: passPercentage
                },
                message: 'Assessment submitted successfully'
            });
        } catch (error) {
        console.error('Error submitting assessment:', error);
        return res.status(500).json({
            success: false,
            message: 'An error occurred while submitting the assessment',
            error: error.message
        });
    }
};

const getTraineeAssessments = async (req, res) => {
  try {
    const { class_id, user_id, page = 1, limit = 10, search = "" } = req.body;
    const offset = (page - 1) * limit;
    const dbName = req.user.db_name;

    // Validate
    if (!user_id || !class_id) {
      return res.status(400).json({
        status: 400,
        success: false,
        message: 'Missing required fields: user_id and class_id are required'
      });
    }

    // Check user
    const [userExists] = await mysqlServerConnection.query(
      `SELECT id FROM ${dbName}.users WHERE id = ? AND is_deleted = 0`,
      [user_id]
    );
    if (userExists.length === 0) {
      return res.status(404).json({
        status: 404,
        success: false,
        message: 'User not found or has been deleted'
      });
    }

    // Check classroom
    const [classExists] = await mysqlServerConnection.query(
      `SELECT id FROM ${dbName}.classroom WHERE id = ? AND is_deleted = 0`,
      [class_id]
    );
    if (classExists.length === 0) {
      return res.status(404).json({
        status: 404,
        success: false,
        message: 'Classroom not found or has been deleted'
      });
    }

    // Check enrollment
    const [isEnrolled] = await mysqlServerConnection.query(
      `SELECT id FROM ${dbName}.classroom_trainee WHERE class_id = ? AND user_id = ?`,
      [class_id, user_id]
    );
    if (isEnrolled.length === 0) {
      return res.status(403).json({
        status: 403,
        success: false,
        message: 'User is not enrolled in this classroom'
      });
    }

    // Build query with is_visible condition
    let query = `
      SELECT 
        ca.id AS assessment_id,
        ca.class_id,
        ca.assessment_name,
        ca.is_time_based,
        ca.is_active,
        DATE_FORMAT(ca.activate_time, '%Y-%m-%dT%H:%i:%s.000Z') as activate_time,
        DATE_FORMAT(ca.deactivate_time, '%Y-%m-%dT%H:%i:%s.000Z') as deactivate_time,
        ca.duration,
        ca.pass_percentage,
        ca.total_questions,

        (
          SELECT COUNT(*) 
          FROM ${dbName}.classroom_assessment_questions caq
          JOIN ${dbName}.question_bank qb ON caq.question_id = qb.id
          WHERE caq.assessment_id = ca.id 
            AND caq.class_id = ca.class_id 
            AND qb.is_deleted = 0
        ) AS total_questions,

        (
          SELECT COUNT(*) 
          FROM ${dbName}.classroom_assessment_results car 
          WHERE car.assessment_id = ca.id 
            AND car.class_id = ca.class_id 
            AND car.user_id = ?
        ) AS no_of_times_attended,

        IFNULL((
          SELECT MAX(percentage) 
          FROM ${dbName}.classroom_assessment_results car 
          WHERE car.assessment_id = ca.id 
            AND car.class_id = ca.class_id 
            AND car.user_id = ?
        ), 0) AS highest_percentage

      FROM ${dbName}.classroom_assessments ca
      WHERE ca.class_id = ? AND ca.is_deleted = 0 AND ca.is_visible = 1
    `;

    let queryParams = [user_id, user_id, class_id];

    // Add search filter
    if (search) {
      query += ` AND ca.assessment_name LIKE ?`;
      queryParams.push(`%${search}%`);
    }

    query += ` ORDER BY ca.id DESC LIMIT ? OFFSET ?`;
    queryParams.push(Number(limit), offset);

    // Count query for pagination
    let countQuery = `
      SELECT COUNT(*) as total
      FROM ${dbName}.classroom_assessments ca
      WHERE ca.class_id = ? AND ca.is_deleted = 0 AND ca.is_visible = 1
    `;
    let countParams = [class_id];

    if (search) {
      countQuery += ` AND ca.assessment_name LIKE ?`;
      countParams.push(`%${search}%`);
    }

    const [countResult] = await mysqlServerConnection.query(countQuery, countParams);
    const [assessments] = await mysqlServerConnection.query(query, queryParams);

    return res.status(200).json({
      status: 200,
      success: true,
      message: 'Trainee assessments retrieved successfully',
      assessments,
      pagination: {
        totalRecords: countResult[0].total,
        totalPages: Math.ceil(countResult[0].total / limit),
        currentPage: Number(page),
        limit: Number(limit)
      }
    });
  } catch (error) {
    console.error("🔥 Error in getTraineeAssessments:", error);
    return res.status(500).json({
      status: 500,
      success: false,
      message: 'An error occurred while retrieving trainee assessments',
      error: error.message
    });
  }
};


const getTraineeClassroomDashboardDetails = async (req, res, next) => {
  try {
    const { class_id } = req.body;
    const dbName = req.user.db_name;
    const userId = req.user.userId;

    if (!class_id) {
      return res.status(400).json({
        success: false,
        message: "Class ID is required",
      });
    }

    // Get classroom details
    const [classroomDetails] = await mysqlServerConnection.query(
      `
      SELECT c.*, c.is_visibility, u.name as trainer_name
      FROM ${dbName}.classroom c
      LEFT JOIN ${dbName}.users u ON c.user_id = u.id
      WHERE c.id = ? AND c.is_deleted = 0
      `,
      [class_id]
    );

    if (classroomDetails.length === 0) {
      return res.status(404).json({
        success: false,
        message: "Classroom not found or has been deleted",
      });
    }

    // Get count of users in the classroom
    const [userCount] = await mysqlServerConnection.query(
      `
      SELECT COUNT(*) as total_users
      FROM ${dbName}.classroom_trainee
      WHERE class_id = ?
      `,
      [class_id]
    );

    // Get the group and auto-add user if not present
    const [groupDetails] = await mysqlServerConnection.query(
      `
      SELECT id as group_id, group_name
      FROM ${dbName}.group_rooms
      WHERE class_id = ?
      LIMIT 1
      `,
      [class_id]
    );

    if (groupDetails.length > 0) {
      const groupId = groupDetails[0].group_id;

      const [existingMember] = await mysqlServerConnection.query(
        `SELECT id FROM ${dbName}.group_members WHERE group_id = ? AND user_id = ?`,
        [groupId, userId]
      );

      if (existingMember.length === 0) {
        await mysqlServerConnection.query(
          `INSERT INTO ${dbName}.group_members (group_id, user_id) VALUES (?, ?)`,
          [groupId, userId]
        );
      }
    }

    // Assignments
    const [totalAssignmentCount] = await mysqlServerConnection.query(
      `
      SELECT COUNT(*) as total_assignments
      FROM ${dbName}.classroom_assignments
      WHERE class_id = ? AND is_deleted = 0 AND (due_date >= CURRENT_DATE() OR due_date IS NULL)
      `,
      [class_id]
    );

    const totalAssignments = totalAssignmentCount[0].total_assignments;

    const [completedAssignments] = await mysqlServerConnection.query(
      `
      SELECT COUNT(*) as completed_assignments 
      FROM ${dbName}.classroom_assignment_submissions 
      WHERE class_id = ? AND user_id = ? AND is_deleted = 0
      `,
      [class_id, userId]
    );

    const completedAssignmentCount = completedAssignments[0].completed_assignments;

    // Assessments
    const [totalAssessmentCount] = await mysqlServerConnection.query(
      `
      SELECT COUNT(*) as total_assessments
      FROM ${dbName}.classroom_assessments
      WHERE class_id = ? AND is_deleted = 0
      `,
      [class_id]
    );

    const totalAssessments = totalAssessmentCount[0].total_assessments;

    const [completedAssessments] = await mysqlServerConnection.query(
      `
      SELECT COUNT(DISTINCT assessment_id) as completed_assessments
      FROM ${dbName}.classroom_assessment_results
      WHERE class_id = ? AND user_id = ?
      `,
      [class_id, userId]
    );

    const completedAssessmentCount = completedAssessments[0].completed_assessments;

    // ✅ Safely assign is_visibility
    const rawVisibility = classroomDetails[0].is_visibility;
    let parsedVisibility;

    if (Array.isArray(rawVisibility)) {
      parsedVisibility = rawVisibility;
    } else if (typeof rawVisibility === "string") {
      try {
        parsedVisibility = JSON.parse(rawVisibility);
      } catch {
        parsedVisibility = null;
      }
    }

    if (!Array.isArray(parsedVisibility)) {
      parsedVisibility = [
        { tab_name: "assignment", is_visible: "true" },
        { tab_name: "assessment", is_visible: "true" },
        { tab_name: "liveclass", is_visible: "true" },
        { tab_name: "recorded", is_visible: "true" },
        { tab_name: "community", is_visible: "true" }
      ];
    }

    // Final response object
    const response = {
      ...classroomDetails[0],
      is_visibility: parsedVisibility,
      total_assignments: totalAssignments,
      completed_assignments: completedAssignmentCount,
      pending_assignments: totalAssignments - completedAssignmentCount,
      total_assessments: totalAssessments,
      completed_assessments: completedAssessmentCount,
      pending_assessments: totalAssessments - completedAssessmentCount,
      total_users: userCount[0].total_users,
      group: groupDetails.length > 0
        ? {
            group_id: groupDetails[0].group_id,
            group_name: groupDetails[0].group_name,
          }
        : null,
    };

    return res.status(200).json({
      success: true,
      data: response,
      message: "Classroom details fetched successfully",
    });
  } catch (error) {
    console.error("Error fetching classroom details:", error);
    return next({
      status: 500,
      message: error.message || "Internal Server Error",
    });
  }
};



module.exports = {
    getTraineeClassrooms,
    getTraineeClassroomDashboardDetails,
    getQuestionsForTraineeAssessment,
    submitTraineeAssessment,
    getTraineeAssessments
};

  