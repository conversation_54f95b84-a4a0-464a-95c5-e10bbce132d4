const { mysqlServerConnection } = require('../../../db/db');




const getDashboardData = async (req, res) => {
  console.log("📡 [API] getDashboardData called");

  try {
    const dbName = req.user.db_name;
    console.log("🔐 DB Name:", dbName);

    // === USERS ===
    const [totalUsersResult] = await mysqlServerConnection.query(`
      SELECT COUNT(*) AS totalUsers FROM ${dbName}.users WHERE is_deleted = 0
    `);
    const [activeUsersResult] = await mysqlServerConnection.query(`
      SELECT COUNT(*) AS activeUsers FROM ${dbName}.users WHERE is_deleted = 0 AND is_blocked = 0
    `);
    const [inactiveUsersResult] = await mysqlServerConnection.query(`
      SELECT COUNT(*) AS inactiveUsers FROM ${dbName}.users WHERE is_deleted = 0 AND is_blocked = 1
    `);

    // === COURSES ===
    const [totalCoursesResult] = await mysqlServerConnection.query(`
      SELECT COUNT(*) AS totalCourses FROM ${dbName}.courses WHERE is_deleted = 0
    `);
    const [activeCoursesResult] = await mysqlServerConnection.query(`
      SELECT COUNT(*) AS activeCourses FROM ${dbName}.courses WHERE is_active = 1 AND is_deleted = 0
    `);
    const [approvedCoursesResult] = await mysqlServerConnection.query(`
      SELECT COUNT(*) AS approvedCourses FROM ${dbName}.courses WHERE is_approved = 1 AND is_deleted = 0
    `);
    const [freeCoursesResult] = await mysqlServerConnection.query(`
      SELECT COUNT(*) AS freeCourses FROM ${dbName}.courses WHERE course_price = 'free' AND is_deleted = 0
    `);
    const [paidCoursesResult] = await mysqlServerConnection.query(`
      SELECT COUNT(*) AS paidCourses FROM ${dbName}.courses WHERE course_price != 'free' AND is_deleted = 0
    `);

    // === CLASSROOMS ===
    const [totalClassroomsResult] = await mysqlServerConnection.query(`
      SELECT COUNT(*) AS totalClassrooms FROM ${dbName}.classroom WHERE is_deleted = 0
    `);
    const [activeClassroomsResult] = await mysqlServerConnection.query(`
      SELECT COUNT(*) AS activeClassrooms FROM ${dbName}.classroom WHERE is_active = 1 AND is_deleted = 0
    `);
    const [inactiveClassroomsResult] = await mysqlServerConnection.query(`
      SELECT COUNT(*) AS inactiveClassrooms FROM ${dbName}.classroom WHERE is_active = 0 AND is_deleted = 0
    `);

    // === TRAINEES ===
    const [totalTraineesResult] = await mysqlServerConnection.query(`
      SELECT COUNT(*) AS totalTrainees
      FROM ${dbName}.users u
      JOIN ${dbName}.user_roles ur ON u.id = ur.user_id
      JOIN ${dbName}.roles r ON ur.role_id = r.id
      WHERE r.role_type = 'trainee' AND u.is_deleted = 0
    `);
    const [activeTraineesResult] = await mysqlServerConnection.query(`
      SELECT COUNT(*) AS activeTrainees
      FROM ${dbName}.users u
      JOIN ${dbName}.user_roles ur ON u.id = ur.user_id
      JOIN ${dbName}.roles r ON ur.role_id = r.id
      WHERE r.role_type = 'trainee' AND u.is_deleted = 0 AND u.is_blocked = 0
    `);
    const [inactiveTraineesResult] = await mysqlServerConnection.query(`
      SELECT COUNT(*) AS inactiveTrainees
      FROM ${dbName}.users u
      JOIN ${dbName}.user_roles ur ON u.id = ur.user_id
      JOIN ${dbName}.roles r ON ur.role_id = r.id
      WHERE r.role_type = 'trainee' AND u.is_deleted = 0 AND u.is_blocked = 1
    `);

    // === TICKETS ===
    const [pendingTicketsResult] = await mysqlServerConnection.query(`
      SELECT COUNT(*) AS pendingTickets FROM ${dbName}.ticket WHERE status = 'pending' AND is_deleted = 0
    `);
    const [resolvedTicketsResult] = await mysqlServerConnection.query(`
      SELECT COUNT(*) AS resolvedTickets FROM ${dbName}.ticket WHERE status = 'Solved' AND is_deleted = 0
    `);

    // === MONTHLY TRAINEES (last 12 months)
    const [monthlyTraineesResult] = await mysqlServerConnection.query(`
      WITH RECURSIVE months AS (
        SELECT DATE_SUB(CURDATE(), INTERVAL 11 MONTH) AS first_day
        UNION ALL
        SELECT DATE_ADD(first_day, INTERVAL 1 MONTH)
        FROM months
        WHERE first_day < CURDATE()
      )
      SELECT
        DATE_FORMAT(m.first_day, '%Y-%m') AS month,
        COUNT(u.id) AS count
      FROM months m
      LEFT JOIN ${dbName}.users u
        ON DATE_FORMAT(u.createdAt, '%Y-%m') = DATE_FORMAT(m.first_day, '%Y-%m')
        AND u.is_deleted = 0
        AND EXISTS (
          SELECT 1
          FROM ${dbName}.user_roles ur
          JOIN ${dbName}.roles r ON ur.role_id = r.id
          WHERE ur.user_id = u.id AND r.role_type = 'trainee'
        )
      GROUP BY month
      ORDER BY month ASC
    `);

    // === MONTHLY COURSES (last 12 months)
    const [monthlyCoursesResult] = await mysqlServerConnection.query(`
      WITH RECURSIVE months AS (
        SELECT DATE_SUB(CURDATE(), INTERVAL 11 MONTH) AS first_day
        UNION ALL
        SELECT DATE_ADD(first_day, INTERVAL 1 MONTH)
        FROM months
        WHERE first_day < CURDATE()
      )
      SELECT
        DATE_FORMAT(m.first_day, '%Y-%m') AS month,
        COUNT(c.id) AS count
      FROM months m
      LEFT JOIN ${dbName}.courses c
        ON DATE_FORMAT(c.createdAt, '%Y-%m') = DATE_FORMAT(m.first_day, '%Y-%m')
        AND c.is_deleted = 0
      GROUP BY month
      ORDER BY month ASC
    `);

    // === LATEST 10 SUPPORT TICKETS
    const [latestTicketsResult] = await mysqlServerConnection.query(`
      SELECT id, ticket_id, sender_id, subject, status, createdAt
      FROM ${dbName}.ticket
      WHERE is_deleted = 0
      ORDER BY createdAt DESC
      LIMIT 10
    `);

    // === LATEST 10 ANNOUNCEMENTS
    const [latestAnnouncementsResult] = await mysqlServerConnection.query(`
      SELECT id, title, message, createdAt, scheduleDateTime
      FROM ${dbName}.announcements
      WHERE is_delete = 0
      ORDER BY createdAt DESC
      LIMIT 10
    `);

    const dashboardData = {
      totalUsers: totalUsersResult[0].totalUsers,
      activeUsers: activeUsersResult[0].activeUsers,
      inactiveUsers: inactiveUsersResult[0].inactiveUsers,

      totalCourses: totalCoursesResult[0].totalCourses,
      activeCourses: activeCoursesResult[0].activeCourses,
      approvedCourses: approvedCoursesResult[0].approvedCourses,
      freeCourses: freeCoursesResult[0].freeCourses,
      paidCourses: paidCoursesResult[0].paidCourses,

      totalClassrooms: totalClassroomsResult[0].totalClassrooms,
      activeClassrooms: activeClassroomsResult[0].activeClassrooms,
      inactiveClassrooms: inactiveClassroomsResult[0].inactiveClassrooms,

      totalTrainees: totalTraineesResult[0].totalTrainees,
      activeTrainees: activeTraineesResult[0].activeTrainees,
      inactiveTrainees: inactiveTraineesResult[0].inactiveTrainees,

      pendingTickets: pendingTicketsResult[0].pendingTickets,
      resolvedTickets: resolvedTicketsResult[0].resolvedTickets,

      monthlyTraineeJoins: monthlyTraineesResult,
      monthlyCourseAdds: monthlyCoursesResult,

      latestSupportTickets: latestTicketsResult,
      latestAnnouncements: latestAnnouncementsResult
    };

    return res.status(200).json({
      success: true,
      message: "Dashboard data fetched successfully",
      data: dashboardData
    });

  } catch (error) {
    console.error("❌ Error in getDashboardData:", error);
    return res.status(500).json({
      success: false,
      message: "Something went wrong in dashboard API"
    });
  }
};



const DashboardStatus = async (req, res) => {
  try {
    const userId = req.user.userId;
    const db = req.user.db_name;

    // 1. Certificates count
    const [certificates] = await mysqlServerConnection.query(`
      SELECT COUNT(*) AS certificate_count 
      FROM ${db}.certificates 
      WHERE user_id = ?
    `, [userId]);

    // 2. User ranking
    const [ranking] = await mysqlServerConnection.query(`
      SELECT total_rank 
      FROM ${db}.rankings 
      WHERE user_id = ?
    `, [userId]);

    // 3. Earned points
    const [points] = await mysqlServerConnection.query(`
      SELECT total_earn 
      FROM ${db}.user_points 
      WHERE user_id = ?
    `, [userId]);

    // 4. Total classrooms user is part of
    const [classrooms] = await mysqlServerConnection.query(`
      SELECT c.id
      FROM ${db}.classroom_trainee ct
      JOIN ${db}.classroom c ON ct.class_id = c.id
      WHERE ct.user_id = ? AND c.is_deleted = 0
    `, [userId]);

    // 5. Total courses user enrolled in
    const [courses] = await mysqlServerConnection.query(`
      SELECT co.id
      FROM ${db}.mycourses mc
      JOIN ${db}.courses co ON mc.course_id = co.id
      WHERE mc.user_id = ?
    `, [userId]);

    // 6. Free/Paid courses purchased based on `course_type`
    const [courseTypeCounts] = await mysqlServerConnection.query(`
      SELECT 
        SUM(CASE WHEN co.course_type = 'free' THEN 1 ELSE 0 END) AS free_courses,
        SUM(CASE WHEN co.course_type = 'paid' THEN 1 ELSE 0 END) AS paid_courses
      FROM ${db}.mycourses mc
      JOIN ${db}.courses co ON mc.course_id = co.id
      WHERE mc.user_id = ?
    `, [userId]);

    // 7. Completed courses
    const [completedCourses] = await mysqlServerConnection.query(`
      SELECT COUNT(*) AS completed_count
      FROM ${db}.mycourses
      WHERE user_id = ? AND is_completed = 1
    `, [userId]);

    // ✅ Send final JSON response
    res.status(200).json({
      success: true,
      data: {
        certificate: certificates[0]?.certificate_count || 0,
        ranking: ranking[0]?.total_rank || 0,
        points: points[0]?.total_earn || 0,
        totalClassrooms: classrooms.length,
        totalCourses: courses.length,
        totalFreeCoursesPurchased: courseTypeCounts[0]?.free_courses || 0,
        totalPaidCoursesPurchased: courseTypeCounts[0]?.paid_courses || 0,
        totalCompletedCourses: completedCourses[0]?.completed_count || 0
      }
    });

  } catch (error) {
    console.error('DashboardStatus error:', error);
    res.status(500).json({
      success: false,
      message: error.message || 'Internal Server Error'
    });
  }
};




// Get Top Courses and Reviews
// Get Top Courses and Reviews
const getTopCourses = async (req, res) => {
  try {
    // Fetch the top 6 courses based on total_rating and recent createdAt, including course_desc
    const [topCourses] = await mysqlServerConnection.query(
      `SELECT 
         id, 
         course_name, 
         banner_image, 
         course_desc, 
         total_rating, 
         subscribers, 
         createdAt 
       FROM ${req.user.db_name}.courses 
       WHERE is_active = TRUE 
         AND is_deleted = FALSE 
       ORDER BY total_rating DESC, createdAt DESC 
       LIMIT 6`
    );

    // Fetch the top 6 recent reviews based on rating and createdAt
    const [topReviews] = await mysqlServerConnection.query(
      `SELECT 
         cr.id, 
         cr.class_rating AS rating, 
         cr.review_text, 
         cr.createdAt, 
         c.course_name, 
         u.name AS user_name
       FROM ${req.user.db_name}.course_reviews cr
       JOIN ${req.user.db_name}.courses c 
         ON cr.course_id = c.id
       JOIN ${req.user.db_name}.users u 
         ON cr.user_id = u.id
       ORDER BY cr.class_rating DESC, cr.createdAt DESC 
       LIMIT 6`
    );

    // Format the response for top courses, now including course_desc
    const formattedCourses = topCourses.map(course => ({
      id: course.id,
      course_name: course.course_name,
      banner_image: course.banner_image,
      course_desc: course.course_desc,         // ← added here
      total_rating: course.total_rating,
      subscribers: course.subscribers,
      createdAt: course.createdAt,
    }));

    // Format the response for top reviews
    const formattedReviews = topReviews.map(review => ({
      id: review.id,
      rating: review.rating,
      review_text: review.review_text,
      createdAt: review.createdAt,
      course_name: review.course_name,
      user_name: review.user_name,
    }));

    // Combine the data in a single response object
    const responseData = {
      top_courses: formattedCourses,
      top_reviews: formattedReviews,
    };

    return res.status(200).json({
      success: true,
      data: responseData
    });
  } catch (error) {
    console.error('Error retrieving top courses and reviews:', error);
    return res.status(500).json({
      success: false,
      data: {
        error_msg: 'Internal server error.'
      }
    });
  }
};


const getUserLogs = async (req, res) => {
  try {
    const { userId } = req.user; // Assuming you will pass userId as a route parameter
    console.log("UsEr Id :", userId)
    // Fetch all user logs from the database for the given userId
    const [userLogs] = await mysqlServerConnection.query(
      `SELECT id, user_id, log_name, log_description, file_url, createdAt 
             FROM ${req.user.db_name}.user_logs 
             WHERE user_id = ? 
             ORDER BY createdAt DESC`,
      [userId]
    );

    if (userLogs.length === 0) {
      return res.status(404).json({
        success: false,
        data: {
          error_msg: 'No logs found for this user.'
        }
      });
    }

    return res.status(200).json({
      success: true,
      data: userLogs
    });
  } catch (error) {
    console.error('Error retrieving user logs:', error);
    return res.status(500).json({
      success: false,
      data: {
        error_msg: 'Internal server error.'
      }
    });
  }
};


const DashboardStatistic = async (req, res) => {
  const year = req.query.year ? parseInt(req.query.year) : new Date().getFullYear();
  const currentMonth = new Date().getMonth() + 1;
  const { userId } = req.user;

  try {
    const yearPerformanceSummary = [];

    for (let month = 1; month <= currentMonth; month++) {
      const startDate = new Date(year, month - 1, 1);
      const endDate = new Date(year, month, 0);
      const monthNames = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];
      const monthName = monthNames[month - 1];

      console.log(`\n📅 Processing Month: ${monthName} (${startDate.toISOString()} to ${endDate.toISOString()})`);

      // ✅ FIXED: Don't filter courses by purchase date
      const coursesQuery = `
        SELECT c.id, c.validity
        FROM ${req.user.db_name}.mycourses AS mc
        JOIN ${req.user.db_name}.courses AS c ON mc.course_id = c.id
        WHERE mc.user_id = ? AND c.is_deleted = FALSE;
      `;
      const [userCourses] = await mysqlServerConnection.query(coursesQuery, [userId]);

      console.log(`📚 Active Courses: ${userCourses.length}`);

      let totalVideos = 0, completedVideos = 0;
      let totalAssessments = 0, completedAssessments = 0;
      let totalAssignments = 0, completedAssignments = 0;
      let totalDuration = 0;

      for (const course of userCourses) {
        totalDuration += parseInt(course.validity || 0, 10);

        // 🎬 Videos completed during the month
        const videosQuery = `
          SELECT rv.is_complete 
          FROM ${req.user.db_name}.recent_videos AS rv
          JOIN ${req.user.db_name}.videos AS v ON rv.video_id = v.id
          WHERE rv.user_id = ? AND v.course_id = ?
          AND rv.createdAt BETWEEN ? AND ?
        `;
        const [videos] = await mysqlServerConnection.query(videosQuery, [userId, course.id, startDate, endDate]);
        totalVideos += videos.length;
        completedVideos += videos.filter(v => v.is_complete === 1).length;

        // 🧪 Assessments
        const assessmentsExistQuery = `SELECT COUNT(*) AS count FROM ${req.user.db_name}.assessments WHERE course_id = ?`;
        const [{ count: assessmentsExist }] = await mysqlServerConnection.query(assessmentsExistQuery, [course.id]);

        if (assessmentsExist > 0) {
          const assessmentsQuery = `
            SELECT au.status 
            FROM ${req.user.db_name}.assessment_users AS au
            JOIN ${req.user.db_name}.assessments AS a ON au.assessment_id = a.id
            WHERE a.course_id = ? AND au.user_id = ?
            AND au.createdAt BETWEEN ? AND ?
          `;
          const [assessments] = await mysqlServerConnection.query(assessmentsQuery, [course.id, userId, startDate, endDate]);
          totalAssessments += assessments.length;
          completedAssessments += assessments.filter(a => a.status === '1').length;
        }

        // 📝 Assignments
        const assignmentsExistQuery = `SELECT COUNT(*) AS count FROM ${req.user.db_name}.assignments WHERE course_id = ?`;
        const [{ count: assignmentsExist }] = await mysqlServerConnection.query(assignmentsExistQuery, [course.id]);

        if (assignmentsExist > 0) {
          const assignmentsQuery = `
            SELECT au.status 
            FROM ${req.user.db_name}.assignment_user AS au
            JOIN ${req.user.db_name}.assignments AS a ON au.assignment_id = a.id
            WHERE a.course_id = ? AND au.user_id = ?
            AND au.createdAt BETWEEN ? AND ?
          `;
          const [assignments] = await mysqlServerConnection.query(assignmentsQuery, [course.id, userId, startDate, endDate]);
          totalAssignments += assignments.length;
          completedAssignments += assignments.filter(a => a.status === '1').length;
        }
      }

      const videoCompletionRate = totalVideos ? (completedVideos / totalVideos) * 100 : 0;
      const assessmentCompletionRate = totalAssessments ? (completedAssessments / totalAssessments) * 100 : 0;
      const assignmentCompletionRate = totalAssignments ? (completedAssignments / totalAssignments) * 100 : 0;

      // ✅ Average only non-zero categories
      let totalRate = 0, rateCount = 0;
      if (totalVideos) {
        totalRate += videoCompletionRate;
        rateCount++;
      }
      if (totalAssessments) {
        totalRate += assessmentCompletionRate;
        rateCount++;
      }
      if (totalAssignments) {
        totalRate += assignmentCompletionRate;
        rateCount++;
      }

      const userPerformance = rateCount ? (totalRate / rateCount).toFixed(2) : '0.00';

      // 📊 Monthly summary
      console.log(`🎬 Videos: ${completedVideos}/${totalVideos} (${videoCompletionRate.toFixed(2)}%)`);
      console.log(`🧪 Assessments: ${completedAssessments}/${totalAssessments} (${assessmentCompletionRate.toFixed(2)}%)`);
      console.log(`📝 Assignments: ${completedAssignments}/${totalAssignments} (${assignmentCompletionRate.toFixed(2)}%)`);
      console.log(`✅ Final User Performance: ${userPerformance}%`);

      yearPerformanceSummary.push({
        month: monthName,
        userPerformance
      });
    }

    res.json({ success: true, data: yearPerformanceSummary });

  } catch (error) {
    console.error("❌ Error in DashboardStatistic:", error);
    res.status(500).json({ error: 'An error occurred while fetching user performance' });
  }
};


const getAllTasksByUser = async (req, res) => {
  console.log("getAllTasksByUser API called");
  try {
    const { userId } = req.user;
    const dbName = req.user.db_name;

    if (!userId) {
      return res.status(400).json({
        success: false,
        message: "User ID is missing from token"
      });
    }

    const [tasks] = await mysqlServerConnection.query(
      `SELECT id, task, marked, created_at, updated_at
       FROM ${dbName}.task_list
       WHERE user_id = ? AND is_deleted = 0
       ORDER BY created_at DESC`,
      [userId]
    );

    return res.status(200).json({
      success: true,
      message: tasks.length > 0 ? "Tasks fetched successfully" : "No tasks found",
      data: tasks
    });

  } catch (error) {
    console.error("Error fetching tasks:", error);
    return res.status(500).json({
      success: false,
      message: "Internal server error"
    });
  }
};

const addTask = async (req, res) => {
  try {
    const { userId } = req.user;
    const dbName = req.user.db_name;
    const { task } = req.body;

    if (!task) {
      return res.status(400).json({
        success: false,
        message: "Task is required",
      });
    }

    const [result] = await mysqlServerConnection.query(
      `INSERT INTO ${dbName}.task_list (user_id, task, marked, created_at, updated_at)
       VALUES (?, ?, 0, NOW(), NOW())`,
      [userId, task]
    );

    return res.status(201).json({
      success: true,
      message: "Task added successfully",
      data: { id: result.insertId, task, marked: false, created_at: new Date(), updated_at: new Date() }
      });

  } catch (error) {
    console.error("Error adding task:", error);
    return res.status(500).json({
      success: false,
      message: "Internal server error",
    });
  }
};


const editTaskById = async (req, res) => {
  console.log("editTaskById API called");
  try {
    const { userId } = req.user;
    const dbName = req.user.db_name;
    const { task_id, task, marked } = req.body;
    console.log("task_id", task_id);
    console.log("task", task);
    console.log("marked", marked);
    console.log("userId", userId);

    // Validate required data
    if (!task_id || typeof task !== "string" || typeof marked !== "boolean") {
      return res.status(400).json({
        success: false,
        message: "task_id, task (string), and marked (boolean) are required",
      });
    }

    // Update task
    const [result] = await mysqlServerConnection.query(
      `UPDATE ${dbName}.task_list
       SET task = ?, marked = ?, updated_at = NOW()
       WHERE id = ? AND user_id = ? AND is_deleted = 0`,
      [task, marked, task_id, userId]
    );

    if (result.affectedRows === 0) {
      return res.status(404).json({
        success: false,
        message: "Task not found or already deleted",
      });
    }

    return res.status(200).json({
      success: true,
      message: "Task updated successfully",
    });

  } catch (error) {
    console.error("Error updating task:", error);
    return res.status(500).json({
      success: false,
      message: "Internal server error",
    });
  }
};

const deleteTaskById = async (req, res) => {
  console.log("deleteTaskById API called");
  try {
    const { userId } = req.user;
    const dbName = req.user.db_name;
    const { task_id } = req.query; // 👈 changed from req.body to req.query

    console.log("task_id", task_id);
    console.log("userId", userId);

    if (!task_id) {
      return res.status(400).json({
        success: false,
        message: "task_id is required",
      });
    }

    const [result] = await mysqlServerConnection.query(
      `UPDATE ${dbName}.task_list
       SET is_deleted = 1, updated_at = NOW()
       WHERE id = ? AND user_id = ? AND is_deleted = 0`,
      [task_id, userId]
    );

    if (result.affectedRows === 0) {
      return res.status(404).json({
        success: false,
        message: "Task not found or already deleted",
      });
    }

    return res.status(200).json({
      success: true,
      message: "Task deleted successfully",
    });

  } catch (error) {
    console.error("Error deleting task:", error);
    return res.status(500).json({
      success: false,
      message: "Internal server error",
    });
  }
};


const markTaskAsCompleted = async (req, res) => {
  try {
    const { userId } = req.user;
    const dbName = req.user.db_name;
    const { task_id } = req.body;

    if (!task_id) {
      return res.status(400).json({
        success: false,
        message: "task_id is required",
      });
    }

    // Get current status
    const [existingTaskRows] = await mysqlServerConnection.query(
      `SELECT marked FROM ${dbName}.task_list 
       WHERE id = ? AND user_id = ? AND is_deleted = 0`,
      [task_id, userId]
    );

    if (existingTaskRows.length === 0) {
      return res.status(404).json({
        success: false,
        message: "Task not found or already deleted",
      });
    }

    const currentStatus = existingTaskRows[0].marked;
    const newStatus = currentStatus === 1 ? 0 : 1;

    const [updateResult] = await mysqlServerConnection.query(
      `UPDATE ${dbName}.task_list 
       SET marked = ?, updated_at = NOW() 
       WHERE id = ? AND user_id = ? AND is_deleted = 0`,
      [newStatus, task_id, userId]
    );

    return res.status(200).json({
      success: true,
      message: `Task marked as ${newStatus === 1 ? 'completed' : 'not completed'}`,
      marked: newStatus,
    });

  } catch (error) {
    console.error("Error toggling task marked status:", error);
    return res.status(500).json({
      success: false,
      message: "Internal server error",
    });
  }
};

const getTaskLists = async (req, res) => {
  try {
    const { user_id, page = 1, limit = 20 } = req.body;
    const dbName = req.user.db_name;

    if (!user_id) {
      return res.status(400).json({
        success: false,
        message: "user_id is required",
      });
    }

    const offset = (page - 1) * limit;

    // 1. Get assignment tasks
    const [assignmentTasks] = await mysqlServerConnection.query(
      `
      SELECT 
        t.id AS task_id,
        t.classroom_id,
        t.assignment_id,
        t.is_seen,
        ca.title,
        ca.due_date,
        ca.assigned_date,
        ca.createdAt AS created_at,
        'assignment' AS task_type
      FROM ${dbName}.task_lists t
      JOIN ${dbName}.classroom_assignments ca ON t.assignment_id = ca.id
      WHERE t.user_id = ?
        AND ca.is_deleted = 0
        AND ca.is_visible = 1
      `,
      [user_id]
    );

    // 2. Get assessment tasks
    const [assessmentTasks] = await mysqlServerConnection.query(
      `
      SELECT 
        t.id AS task_id,
        t.classroom_id,
        t.assessment_id,
        t.is_seen,
        cas.assessment_name,
        cas.is_time_based,
        cas.activate_time,
        cas.deactivate_time,
        cas.created_at AS created_at,
        'assessment' AS task_type
      FROM ${dbName}.task_lists t
      JOIN ${dbName}.classroom_assessments cas ON t.assessment_id = cas.id
      WHERE t.user_id = ?
        AND cas.is_deleted = 0
        AND cas.is_visible = 1
      `,
      [user_id]
    );

    // 3. Merge tasks
    const allTasks = [
      ...assignmentTasks.map(task => ({
        task_id: task.task_id,
        classroom_id: task.classroom_id,
        task_type: "assignment",
        title: task.title,
        due_date: task.due_date,
        assigned_date: task.assigned_date,
        created_at: task.created_at,
        is_seen: task.is_seen === 1
      })),
      ...assessmentTasks.map(task => ({
        task_id: task.task_id,
        classroom_id: task.classroom_id,
        task_type: "assessment",
        assessment_name: task.assessment_name,
        is_time_based: task.is_time_based === 1,
        activate_time: task.is_time_based === 1 ? task.activate_time : null,
        deactivate_time: task.is_time_based === 1 ? task.deactivate_time : null,
        created_at: task.created_at,
        is_seen: task.is_seen === 1
      }))
    ];

    // 4. Sort by created_at DESC
    const sortedTasks = allTasks.sort((a, b) =>
      new Date(b.created_at) - new Date(a.created_at)
    );

    // 5. Apply pagination
    const paginatedTasks = sortedTasks.slice(offset, offset + limit);
    const hasMore = offset + limit < sortedTasks.length;

    // 6. Response
    return res.status(200).json({
      success: true,
      message: "Task list fetched successfully",
      data: paginatedTasks,
      pagination: {
        currentPage: Number(page),
        limit: Number(limit),
        totalTasks: sortedTasks.length,
        hasMore
      }
    });

  } catch (error) {
    console.error("❌ Error in getTaskLists:", error);
    return res.status(500).json({
      success: false,
      message: "Internal server error",
      error: error.message
    });
  }
};

const markTaskAsSeen = async (req, res) => {
  console.log("📥 markTaskAsSeen API called");
  try {
    const { task_id } = req.body;
    const dbName = req.user.db_name;

    if (!task_id) {
      return res.status(400).json({
        success: false,
        message: "task_id is required",
      });
    }

    console.log("🔍 Input task_id:", task_id);

    const [result] = await mysqlServerConnection.query(
      `
      UPDATE ${dbName}.task_lists
      SET is_seen = 1, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
      `,
      [task_id]
    );

    console.log("✅ Update Result:", result);

    return res.status(200).json({
      success: true,
      message: result.affectedRows > 0
        ? "Task marked as seen"
        : "No matching task found",
    });

  } catch (error) {
    console.error("❌ Error in markTaskAsSeen:", error);
    return res.status(500).json({
      success: false,
      message: "Internal server error",
      error: error.message
    });
  }
};
;




module.exports = {
  DashboardStatus,
  getTopCourses,
  getUserLogs,
  DashboardStatistic,

  getAllTasksByUser,
  addTask,
  editTaskById,
  deleteTaskById,
  markTaskAsCompleted,

  getDashboardData,
  getTaskLists,
  markTaskAsSeen
  
}