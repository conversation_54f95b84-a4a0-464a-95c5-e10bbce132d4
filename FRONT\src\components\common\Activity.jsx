import React, { useEffect, useState } from 'react';
import { Icon } from '@iconify/react';
import './Activity.css';
import NoData from '../../components/common/NoData'
import { getUserLogs } from '../../services/userService';

function Activity() {
  const [activities, setActivities] = useState([]);
  // const activities = [
  //   {
  //     id: 1,
  //     title: 'Completed "HTML Basics" course',
  //     time: '2 hours ago',
  //     type: 'course',
  //     status: 'success'
  //   },
  //   {
  //     id: 2,
  //     title: 'Started "CSS Fundamentals"',
  //     time: '3 hours ago',
  //     type: 'course',
  //     status: 'primary'
  //   },
  //   {
  //     id: 3,
  //     title: 'Earned Frontend Developer Certificate',
  //     time: '5 hours ago',
  //     type: 'certificate',
  //     status: 'warning'
  //   },
  //   {
  //     id: 4,
  //     title: 'Completed "JavaScript Fundamentals"',
  //     time: '6 hours ago',
  //     type: 'course',
  //     status: 'success'
  //   },
  //   {
  //     id: 5,
  //     title: 'Completed "JavaScript Fundamentals"',
  //     time: '6 hours ago',
  //     type: 'course',
  //     status: 'success'
  //   },
  //   {
  //     id: 6,
  //     title: 'Completed "JavaScript Fundamentals"',
  //     time: '6 hours ago',
  //     type: 'course',
  //     status: 'success'
  //   },
  //   {
  //     id: 7,
  //     title: 'Completed "JavaScript Fundamentals"',
  //     time: '6 hours ago',
  //     type: 'course',
  //     status: 'success'
  //   },
  //   {
  //     id: 8,
  //     title: 'Completed "JavaScript Fundamentals"',
  //     time: '6 hours ago',
  //     type: 'course',
  //     status: 'success'
  //   },
  //   {
  //     id: 9,
  //     title: 'Completed "JavaScript Fundamentals"',
  //     time: '6 hours ago',
  //     type: 'course',
  //     status: 'success'
  //   },
  //   {
  //     id: 10,
  //     title: 'Completed "JavaScript Fundamentals"',
  //     time: '6 hours ago',
  //     type: 'course',
  //     status: 'success'
  //   },
  // ];

  useEffect(() => {
    fetchActivities();
  }, []);

  const fetchActivities = async () => {
    try {
      const response = await getUserLogs();
      if (response.success && response.data) {
        setActivities(response.data);
      }
    } catch (error) {
      console.error('Error fetching activities:', error);
    }
  };

  const getTimeAgo = (timestamp) => {
    if (!timestamp) return '';
    
    const now = new Date();
    const past = new Date(timestamp);
    const diffInSeconds = Math.floor((now - past) / 1000);
    
    // Less than a minute
    if (diffInSeconds < 60) {
      return `${diffInSeconds} seconds ago`;
    }
    
    // Less than an hour
    const diffInMinutes = Math.floor(diffInSeconds / 60);
    if (diffInMinutes < 60) {
      return `${diffInMinutes} ${diffInMinutes === 1 ? 'minute' : 'minutes'} ago`;
    }
    
    // Less than a day
    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) {
      return `${diffInHours} ${diffInHours === 1 ? 'hour' : 'hours'} ago`;
    }
    
    // Less than a week
    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) {
      return `${diffInDays} ${diffInDays === 1 ? 'day' : 'days'} ago`;
    }
    
    // Less than a month
    const diffInWeeks = Math.floor(diffInDays / 7);
    if (diffInWeeks < 4) {
      return `${diffInWeeks} ${diffInWeeks === 1 ? 'week' : 'weeks'} ago`;
    }
    
    // Less than a year
    const diffInMonths = Math.floor(diffInDays / 30);
    if (diffInMonths < 12) {
      return `${diffInMonths} ${diffInMonths === 1 ? 'month' : 'months'} ago`;
    }
    
    // More than a year
    const diffInYears = Math.floor(diffInDays / 365);
    return `${diffInYears} ${diffInYears === 1 ? 'year' : 'years'} ago`;
  };

  return (
    <div className="card activity-card">
      <div className="card-header d-flex align-items-center">
        <h5 className="d-flex align-items-center gap-2 mb-0">
          <Icon icon="fluent:activity-24-regular" className="header-icon" /> 
          <span>Recent Activity</span>
        </h5>
      </div>
      <div className="card-body p-0">
        <div className="activity-list">
          {activities.map((activity) => (
            <div key={activity.id} className="activity-item">
              <div className={`activity-dot activity-dot-${activity.status || 'primary'}`} />
              <div className="activity-content">
                <div className="activity-title">{activity.log_name}</div>
                <div className="activity-description mb-1">{activity.log_description}</div>
                <div className="activity-time text-muted">
                  <Icon icon="mdi:clock-outline" className="me-1" width="14" height="14" />
                  {getTimeAgo(activity.createdAt)}
                </div>
              </div>
            </div>
          ))}
          {activities.length === 0 && <NoData caption="No activity found" />}
        </div>
      </div>
    </div>
  );
}

export default Activity;