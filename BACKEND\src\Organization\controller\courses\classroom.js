const { response } = require('express');
const { mysqlServerConnection } = require('../../../db/db');



const getCourseClassroomData = async (req, res) => {
    try {
        const { search = '', page = 1, pageSize = 10 } = req.body;
        // Pagination: Calculate the limit and offset
        const limit = parseInt(pageSize, 10); // Number of items per page
        const offset = (parseInt(page, 10) - 1) * limit; // Offset for pagination

        let query = `
            SELECT 
                cc.course_id,
                cc.classroom_id,
                cc.createdAt,
                cc.updatedAt,
                c.created_by,
                c.banner_image,
                c.course_name,
                c.course_desc,
                c.subscribers,
                cl.cls_name,
                cl.cls_desc
            FROM ${req.user.db_name}.course_classroom AS cc
            JOIN ${req.user.db_name}.courses AS c ON cc.course_id = c.id
            JOIN ${req.user.db_name}.classroom AS cl ON cc.classroom_id = cl.id
        `;

        let countQuery = `
            SELECT COUNT(*) as total 
            FROM ${req.user.db_name}.course_classroom AS cc
            JOIN ${req.user.db_name}.courses AS c ON cc.course_id = c.id
            JOIN ${req.user.db_name}.classroom AS cl ON cc.classroom_id = cl.id
        `;

        let [approval]=await mysqlServerConnection.query(`SELECT is_approval 
        FROM ${req.user.db_name}.classroom_approval where user_id=${req.user.userId}`);

        console.log(approval,'=======approval============')
        let queryParams = [];
        let whereClauses = [];


        if (search && search.trim()) {
            whereClauses.push(`(
                c.course_name LIKE ? OR 
                c.course_desc LIKE ? OR 
                cl.cls_name LIKE ? OR 
                cl.cls_desc LIKE ?
            )`);
            const searchQuery = `%${search}%`;
            queryParams.push(searchQuery, searchQuery, searchQuery, searchQuery);
        }

        // Add WHERE clauses if there are any
        if (whereClauses.length > 0) {
            const whereClause = ' WHERE ' + whereClauses.join(' AND ');
            query += whereClause;
            countQuery += whereClause;  
        }

        // Add pagination: limit and offset
        query += ` LIMIT ? OFFSET ?`;
        queryParams.push(limit, offset);

        // Execute the queries
        const [rows] = await mysqlServerConnection.query(query, queryParams);
        const [[{ total }]] = await mysqlServerConnection.query(countQuery, queryParams.slice(0, queryParams.length - 2)); // Exclude limit and offset from count query

        if (rows.length === 0) {
            return res.status(200).json({
                success: false,
                data: {
                    message: 'No data found for the provided parameters',
                    response:[]
                }
            });
        }

        // Format the response data
        const formattedData = rows.map(row => ({
            course_id: row.course_id,
            classroom_id: row.classroom_id,
            is_approval:approval.length==0?0:approval[0].is_approval,
            createdAt: row.createdAt,
            updatedAt: row.updatedAt,
            course: {
                created_by: row.created_by,
                banner_image: row.banner_image,
                course_name: row.course_name,
                course_desc: row.course_desc,
                subscribers: row.subscribers
            },
            classroom: {
                cls_name: row.cls_name,
                cls_desc: row.cls_desc
            }
        }));

        // Response with pagination data
        return res.status(200).json({
            success: true,
            data: {response:formattedData},
            pagination: {
                totalItems: total,
                totalPages: Math.ceil(total / limit),
                currentPage: parseInt(page, 10),
                pageSize: limit
            }
        });
    } catch (error) {
        console.error('Error retrieving course_classroom data:', error);
        return res.status(500).json({
            success: false,
            data: {
                error_msg: 'Internal server error.'
            }
        });
    }
};








// Create Group
const createGroup = async (req, res) => {
    try {
        const { groupName } = req.body;
        const userId = req.user.userId;

        if (!groupName || !userId) {
            return res.status(400).json({
                success: false,
                data: { error_msg: 'Group name and User ID are required.' }
            });
        }

        // Insert new group
        const [result] = await mysqlServerConnection.query(
            `INSERT INTO ${req.user.db_name}.group_rooms (group_name, created_by, createdAt, updatedAt) VALUES (?, ?, NOW(), NOW())`,
            [groupName, userId]
        );
        const groupId = result.insertId;

        // Add the creator as the first member
        await mysqlServerConnection.query(
            `INSERT INTO ${req.user.db_name}.group_members (group_id, user_id, joinedAt) VALUES (?, ?, NOW())`,
            [groupId, userId]
        );

        return res.status(201).json({
            success: true,
            data: { groupId, groupName, createdBy: userId }
        });
    } catch (error) {
        console.error('Error creating group:', error);
        return res.status(500).json({ success: false, data: { error_msg: 'Internal server error.' } });
    }
};

// Add Group Member
const addGroupMember = async (req, res) => {
    try {
        const { groupId, userId } = req.body;

        if (!groupId || !userId) {
            return res.status(400).json({
                success: false,
                data: { error_msg: 'Group ID and User ID are required.' }
            });
        }

        // Check if user is already a member
        const [existingMember] = await mysqlServerConnection.query(
            `SELECT * FROM ${req.user.db_name}.group_members WHERE group_id = ? AND user_id = ?`,
            [groupId, userId]
        );

        if (existingMember.length > 0) {
            return res.status(400).json({
                success: false,
                data: { error_msg: 'User is already a member of this group.' }
            });
        }

        // Add new member
        await mysqlServerConnection.query(
            `INSERT INTO ${req.user.db_name}.group_members (group_id, user_id, joinedAt) VALUES (?, ?, NOW())`,
            [groupId, userId]
        );

        return res.status(201).json({
            success: true,
            data: { groupId, userId, message: 'User added to the group successfully.' }
        });
    } catch (error) {
        console.error('Error adding group member:', error);
        return res.status(500).json({ success: false, data: { error_msg: 'Internal server error.' } });
    }
};

// Edit Group Details
const editGroupDetails = async (req, res) => {
    try {
        const { groupId, newGroupName } = req.body;
        const userId = req.user.userId;

        if (!groupId || !newGroupName) {
            return res.status(400).json({
                success: false,
                data: { error_msg: 'Group ID and new group name are required.' }
            });
        }

        // Check if user is the creator
        const [group] = await mysqlServerConnection.query(
            `SELECT * FROM ${req.user.db_name}.group_rooms WHERE id = ? AND created_by = ?`,
            [groupId, userId]
        );

        if (group.length === 0) {
            return res.status(403).json({
                success: false,
                data: { error_msg: 'Only the group creator can edit the group details.' }
            });
        }

        // Update group name
        await mysqlServerConnection.query(
            `UPDATE ${req.user.db_name}.group_rooms SET group_name = ?, updatedAt = NOW() WHERE id = ?`,
            [newGroupName, groupId]
        );

        return res.status(200).json({
            success: true,
            data: { groupId, newGroupName, message: 'Group details updated successfully.' }
        });
    } catch (error) {
        console.error('Error editing group details:', error);
        return res.status(500).json({ success: false, data: { error_msg: 'Internal server error.' } });
    }
};

// Remove Group Member
const removeGroupMember = async (req, res) => {
    try {
        const { groupId, memberId } = req.body;
        const userId = req.user.userId;

        if (!groupId || !memberId) {
            return res.status(400).json({
                success: false,
                data: { error_msg: 'Group ID and member ID are required.' }
            });
        }

        // Check if user is the creator
        const [group] = await mysqlServerConnection.query(
            `SELECT * FROM ${req.user.db_name}.group_rooms WHERE id = ? AND created_by = ?`,
            [groupId, userId]
        );

        if (group.length === 0) {
            return res.status(403).json({
                success: false,
                data: { error_msg: 'Only the group creator can remove members.' }
            });
        }

        // Remove member
        await mysqlServerConnection.query(
            `DELETE FROM ${req.user.db_name}.group_members WHERE group_id = ? AND user_id = ?`,
            [groupId, memberId]
        );

        return res.status(200).json({
            success: true,
            data: { groupId, memberId, message: 'Member removed from the group successfully.' }
        });
    } catch (error) {
        console.error('Error removing group member:', error);
        return res.status(500).json({ success: false, data: { error_msg: 'Internal server error.' } });
    }
};

// Leave Group
const leaveGroup = async (req, res) => {
    try {
        const { groupId } = req.body;
        const userId = req.user.userId;

        if (!groupId) {
            return res.status(400).json({
                success: false,
                data: { error_msg: 'Group ID is required.' }
            });
        }

        // Check if user is the creator
        const [group] = await mysqlServerConnection.query(
            `SELECT * FROM ${req.user.db_name}.group_rooms WHERE id = ? AND created_by = ?`,
            [groupId, userId]
        );

        if (group.length > 0) {
            // User is the creator, delete the group
            await mysqlServerConnection.query(
                `DELETE FROM ${req.user.db_name}.group_rooms WHERE id = ?`,
                [groupId]
            );
            return res.status(200).json({
                success: true,
                data: { message: 'Group deleted successfully.' }
            });
        } else {
            // User is not the creator, just leave the group
            await mysqlServerConnection.query(
                `DELETE FROM ${req.user.db_name}.group_members WHERE group_id = ? AND user_id = ?`,
                [groupId, userId]
            );
            return res.status(200).json({
                success: true,
                data: { message: 'You have left the group.' }
            });
        }
    } catch (error) {
        console.error('Error leaving group:', error);
        return res.status(500).json({ success: false, data: { error_msg: 'Internal server error.' } });
    }
};

// Send Message to Group
const sendMessage = async (req, res) => {
    try {
        const { groupId, message } = req.body;
        const userId = req.user.userId;

        console.log(groupId)
        if (!groupId || !message) {
            return res.status(400).json({
                success: false,
                data: { error_msg: 'Group ID and message are required.' }
            });
        }

        // Insert message into group_messages table
        await mysqlServerConnection.query(
            `INSERT INTO ${req.user.db_name}.group_messages (group_id, user_id, message, createdAt) VALUES (?, ?, ?, NOW())`,
            [groupId, userId, message]
        );

        return res.status(201).json({
            success: true,
            data: { message: 'Message sent successfully.' }
        });
    } catch (error) {
        console.error('Error sending message:', error);
        return res.status(500).json({ success: false, data: { error_msg: 'Internal server error.' } });
    }
};

// Get Group Messages
const getGroupMessages = async (req, res) => {
    try {
        const { groupId } = req.query;

        if (!groupId) {
            return res.status(400).json({
                success: false,
                data: { error_msg: 'Group ID is required.' }
            });
        }

        // Retrieve group messages
        const [messages] = await mysqlServerConnection.query(
            `SELECT gm.id, gm.user_id, gm.message, gm.createdAt, u.name AS user_name
             FROM ${req.user.db_name}.group_messages gm
             JOIN ${req.user.db_name}.users u ON gm.user_id = u.id
             WHERE gm.group_id = ?
             ORDER BY gm.createdAt ASC`,
            [groupId]
        );

        return res.status(200).json({
            success: true,
            data: { groupId, messages }
        });
    } catch (error) {
        console.error('Error retrieving group messages:', error);
        return res.status(500).json({ success: false, data: { error_msg: 'Internal server error.' } });
    }
};

// Edit Message
const editMessage = async (req, res) => {
    try {
        const { messageId, newMessage } = req.body;
        const userId = req.user.userId;

        if (!messageId || !newMessage) {
            return res.status(400).json({
                success: false,
                data: { error_msg: 'Message ID and new message content are required.' }
            });
        }

        // Check if the message belongs to the user
        const [message] = await mysqlServerConnection.query(
            `SELECT * FROM ${req.user.db_name}.group_messages WHERE id = ? AND user_id = ?`,
            [messageId, userId]
        );

        if (message.length === 0) {
            return res.status(403).json({
                success: false,
                data: { error_msg: 'You can only edit your own messages.' }
            });
        }

        // Update message content
        await mysqlServerConnection.query(
            `UPDATE ${req.user.db_name}.group_messages SET message = ?, createdAt = NOW() WHERE id = ?`,
            [newMessage, messageId]
        );

        return res.status(200).json({
            success: true,
            data: { messageId, newMessage, message: 'Message edited successfully.' }
        });
    } catch (error) {
        console.error('Error editing message:', error);
        return res.status(500).json({ success: false, data: { error_msg: 'Internal server error.' } });
    }
};

// Delete Message
const deleteMessage = async (req, res) => {
    try {
        const { messageId } = req.body;
        const userId = req.user.userId;

        if (!messageId) {
            return res.status(400).json({
                success: false,
                data: { error_msg: 'Message ID is required.' }
            });
        }

        // Check if the message belongs to the user
        const [message] = await mysqlServerConnection.query(
            `SELECT * FROM ${req.user.db_name}.group_messages WHERE id = ? AND user_id = ?`,
            [messageId, userId]
        );

        if (message.length === 0) {
            return res.status(403).json({
                success: false,
                data: { error_msg: 'You can only delete your own messages.' }
            });
        }

        // Delete message
        await mysqlServerConnection.query(
            `DELETE FROM ${req.user.db_name}.group_messages WHERE id = ?`,
            [messageId]
        );

        return res.status(200).json({
            success: true,
            data: { messageId, message: 'Message deleted successfully.' }
        });
    } catch (error) {
        console.error('Error deleting message:', error);
        return res.status(500).json({ success: false, data: { error_msg: 'Internal server error.' } });
    }
};



module.exports = {
    getCourseClassroomData,
    createGroup,
    getGroupMessages,
    addGroupMember,
    sendMessage
}