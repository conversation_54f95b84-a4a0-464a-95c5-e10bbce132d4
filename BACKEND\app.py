import sys
import docx
import traceback
import qrcode
import json
from docx.shared import Inches

def main():
    try:
        # Read command-line arguments
        json_string = sys.argv[1] if len(sys.argv) > 1 else "{}"
        data_from_node = json.loads(json_string)

        # Extract data from JSON
        input_docx_path = sys.argv[2] if len(sys.argv) > 2 else "demo1.docx"
        output_docx_path = sys.argv[3] if len(sys.argv) > 3 else "output.docx"

        # Generate QR code
        qr = qrcode.QRCode(
            version=1,
            box_size=10,
            border=5
        )
        certificate_url = data_from_node.get('certificate_url', "https://www.example.com")
        qr.add_data(certificate_url)
        qr.make(fit=True)
        img = qr.make_image(fill_color="black", back_color="white")
        img.save("qr_code.png")

        # i want to save this img in loal storage and then send it to the user

        

        # Open Word document
        document = docx.Document(input_docx_path)

        # Find and replace placeholders with values
        for paragraph in document.paragraphs:
            for run in paragraph.runs:
                text_to_replace = run.text.lower()  # Convert to lowercase for case-insensitive comparison

                for placeholder, value in data_from_node.items():
                    if placeholder.lower() in text_to_replace:
                        run.text = run.text.replace(placeholder, str(value))

                # Add QR code for a specific placeholder (e.g., "certificate_url")
                if "certificate_url" in text_to_replace:
                    run.clear()
                    run.add_picture("qr_code.png", width=Inches(0.95))  # Adjust width as needed

        # Save the modified document
        document.save(output_docx_path)

    except Exception as e:
        traceback.print_exc(file=sys.stderr)
        sys.exit(1)

if __name__ == "__main__":
    main()
