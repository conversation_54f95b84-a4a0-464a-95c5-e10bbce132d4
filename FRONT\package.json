{"name": "testing", "version": "0.1.0", "private": true, "dependencies": {"@emoji-mart/data": "^1.2.1", "@emoji-mart/react": "^1.1.1", "@iconify/react": "^6.0.0", "@stripe/stripe-js": "^7.3.1", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@testing-library/user-event": "^13.5.0", "@zoom/meetingsdk": "^4.0.0", "ajv": "6.12.6", "ajv-keywords": "3.5.2", "axios": "^1.10.0", "bitmovin-player": "^8.214.0", "bitmovin-player-ui": "^3.98.0", "bootstrap": "^5.3.6", "chart.js": "^4.5.0", "date-fns": "^4.1.0", "emoji-mart": "^5.6.0", "framer-motion": "^12.23.0", "lodash": "^4.17.21", "mammoth": "^1.9.1", "moment": "^2.30.1", "moment-timezone": "^0.6.0", "react": "^18.3.1", "react-beautiful-dnd": "^13.1.1", "react-bootstrap": "^2.10.10", "react-chartjs-2": "^5.3.0", "react-dom": "^18.3.1", "react-icons": "^5.5.0", "react-pdf": "^9.2.1", "react-router-dom": "^6.30.1", "react-scripts": "5.0.1", "react-toastify": "^11.0.5", "recharts": "^2.15.4", "socket.io-client": "^4.8.1", "url": "^0.11.4", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}