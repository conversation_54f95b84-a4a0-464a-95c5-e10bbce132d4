import React, { useState, useEffect } from "react";
import { Icon } from "@iconify/react";
import AccountInfo from "./AccountInfo";
import Notification from "./Notification";
import DeleteAccount from "./DeleteAccount";
import AboutUs from "./AboutUs";
import Payment from "./Payment";
import "./Settings.css";

function Settings() {
  const [activeTab, setActiveTab] = useState("about");

  return (
    <div className="contianer">
      <div className="row">
        <div className="col-12">
          <div className="settings-container">
            {/* Settings Tabs */}
            <div className="settings-tabs-container">
              <div className="settings-tabs">
                {/* <button
                                    className={`settings-tab-item ${activeTab === 'account' ? 'active' : ''}`}
                                    onClick={() => setActiveTab('account')}
                                >
                                    <Icon 
                                        icon="fluent:person-24-regular" 
                                        width="22" 
                                        height="22" 
                                        className="me-2"
                                    />
                                    Account Information
                                </button> */}

                <button
                  className={`settings-tab-item ${
                    activeTab === "about" ? "active" : ""
                  }`}
                  onClick={() => setActiveTab("about")}
                >
                  <Icon
                    icon="mdi:information-outline"
                    width="22"
                    height="22"
                    className="me-2"
                  />
                  About Us
                </button>

                <button
                  className={`settings-tab-item ${
                    activeTab === "delete" ? "active" : ""
                  }`}
                  onClick={() => setActiveTab("delete")}
                >
                  <Icon
                    icon="fluent:delete-24-regular"
                    width="22"
                    height="22"
                    className="me-2"
                    style={{
                      color: activeTab === "delete" ? "#dc3545" : "inherit",
                    }}
                  />
                  Delete Account
                </button>
                <button
                  className={`settings-tab-item ${
                    activeTab === "payment" ? "active" : ""
                  }`}
                  onClick={() => setActiveTab("payment")}
                >
                  <Icon
                    icon="mdi:credit-card-outline"
                    width="22"
                    height="22"
                    className="me-2"
                  />
                  Payment
                </button>

                {/* <button
                  className={`settings-tab-item ${
                    activeTab === "notifications" ? "active" : ""
                  }`}
                  onClick={() => setActiveTab("notifications")}
                >
                  <Icon
                    icon="fluent:alert-24-regular"
                    width="22"
                    height="22"
                    className="me-2"
                  />
                  Notification Preferences
                </button> */}
              </div>
            </div>

            {/* Tab Content */}
            <div className="settings-content-container">
           {/*    {activeTab === "account" && (
                <div className="settings-card">
                  <AccountInfo />
                </div>
              )} */}

              {/* {activeTab === "notifications" && (
                <div className="settings-card">
                  <Notification />
                </div>
              )} */}

              {activeTab === "delete" && (
                <div className="settings-card">
                  <DeleteAccount />
                </div>
              )}

              {activeTab === "about" && (
                <div className="settings-card">
                  <AboutUs />
                </div>
              )}

              {activeTab === "payment" && (
                <div className="settings-card">
                  <Payment />
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default Settings;
