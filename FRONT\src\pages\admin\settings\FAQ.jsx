import React, { useState, useEffect, useCallback } from 'react'
import { Icon } from '@iconify/react';
import { toast } from 'react-toastify';
import {
  getFAQs,
  addFAQ,
  updateFAQ,
  updateFAQStatus,
  deleteFAQ
} from '../../../services/adminService';
import Loader from '../../../components/common/Loader';
import NoData from '../../../components/common/NoData';
import { usePermissions } from '../../../context/PermissionsContext';

function FAQ() {
  const { permissions } = usePermissions();
  const [showModal, setShowModal] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);
  const [editingId, setEditingId] = useState(null);
  const [loading, setLoading] = useState(false);
  const [tableLoading, setTableLoading] = useState(false);
  const [statusLoading, setStatusLoading] = useState(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [deleteFaqData, setDeleteFaqData] = useState(null);
  const [faqData, setFaqData] = useState([]);
  const [pagination, setPagination] = useState({
    currentPage: 1,
    totalPages: 1,
    totalRecords: 0,
    recordsPerPage: 10
  });
  const [search, setSearch] = useState('');
  const [formData, setFormData] = useState({
    question: '',
    answer: '',
  });
  const [formErrors, setFormErrors] = useState({
    question: '',
    answer: ''
  });

  // Constants for validation
  const MAX_QUESTION_LENGTH = 200;
  const MAX_ANSWER_LENGTH = 1000;
  const MIN_QUESTION_LENGTH = 10;
  const MIN_ANSWER_LENGTH = 20;

  // Validation function
  const validateForm = () => {
    let isValid = true;
    const errors = {
      question: '',
      answer: ''
    };

    // Question validation
    if (!formData.question.trim()) {
      errors.question = 'Question is required';
      isValid = false;
    } else if (formData.question.trim().length < MIN_QUESTION_LENGTH) {
      errors.question = `Question must be at least ${MIN_QUESTION_LENGTH} characters`;
      isValid = false;
    } else if (formData.question.trim().length > MAX_QUESTION_LENGTH) {
      errors.question = `Question cannot exceed ${MAX_QUESTION_LENGTH} characters`;
      isValid = false;
    }

    // Answer validation
    if (!formData.answer.trim()) {
      errors.answer = 'Answer is required';
      isValid = false;
    } else if (formData.answer.trim().length < MIN_ANSWER_LENGTH) {
      errors.answer = `Answer must be at least ${MIN_ANSWER_LENGTH} characters`;
      isValid = false;
    } else if (formData.answer.trim().length > MAX_ANSWER_LENGTH) {
      errors.answer = `Answer cannot exceed ${MAX_ANSWER_LENGTH} characters`;
      isValid = false;
    }

    setFormErrors(errors);
    return isValid;
  };

  // Fetch FAQ data
  const fetchFAQData = useCallback(async () => {
    try {
      setTableLoading(true);
      const response = await getFAQs({
        page: pagination.currentPage,
        limit: pagination.recordsPerPage,
        search: search
      });

      if (response.success) {
        setFaqData(response.data.records);
        setPagination(response.data.pagination);
      } else {
        toast.error('Failed to fetch FAQ data');
      }
    } catch (error) {
      console.error('Error fetching FAQ data:', error);
      toast.error('Error fetching FAQ data');
    } finally {
      setTimeout(() => {
        setTableLoading(false);
      }, 500);
    }
  }, [pagination.currentPage, pagination.recordsPerPage, search]);

  // Load data on component mount and when dependencies change
  useEffect(() => {
    fetchFAQData();
  }, [fetchFAQData]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    
    // Clear error when user starts typing
    setFormErrors(prev => ({
      ...prev,
      [name]: ''
    }));

    // Prevent typing if max length is reached
    if (
      (name === 'question' && value.length <= MAX_QUESTION_LENGTH) ||
      (name === 'answer' && value.length <= MAX_ANSWER_LENGTH)
    ) {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    // Validate form before submission
    if (!validateForm()) {
      return;
    }

    // Check permissions based on mode
    if (isEditMode && !permissions.settings_faq_edit) {
      toast.warning("You don't have permission to edit FAQs");
      return;
    }
    if (!isEditMode && !permissions.settings_faq_create) {
      toast.warning("You don't have permission to create FAQs");
      return;
    }

    try {
      setLoading(true);
      setTableLoading(true);

      if (isEditMode) {
        const response = await updateFAQ(editingId, {
          ...formData,
          question: formData.question.trim(),
          answer: formData.answer.trim()
        });
        if (response.success) {
          toast.success('FAQ updated successfully');
          await fetchFAQData();
        } else {
          toast.error(response.message || 'Failed to update FAQ');
        }
      } else {
        const response = await addFAQ({
          question: formData.question.trim(),
          answer: formData.answer.trim()
        });
        if (response.success) {
          toast.success('FAQ added successfully');
          await fetchFAQData();
        } else {
          toast.error(response.message || 'Failed to add FAQ');
        }
      }

      setShowModal(false);
      resetForm();
    } catch (error) {
      console.error('Error submitting form:', error);
      toast.error(error.response?.data?.message || 'Failed to submit form');
    } finally {
      setLoading(false);
      setTableLoading(false);
    }
  };

  const resetForm = () => {
    setFormData({ question: '', answer: '' });
    setFormErrors({ question: '', answer: '' });
    setIsEditMode(false);
    setEditingId(null);
  };

  const handleEdit = (item) => {
    setFormData({
      question: item.question,
      answer: item.answer
    });
    setEditingId(item.id);
    setIsEditMode(true);
    setShowModal(true);
  };

  const handleDelete = async (id) => {
    if (!permissions.settings_faq_delete) {
      toast.warning("You don't have permission to delete FAQs");
      return;
    }

    try {
      setLoading(true);
      setTableLoading(true);
      const response = await deleteFAQ(id);
      if (response.success) {
        toast.success('FAQ deleted successfully');
        await fetchFAQData();
      } else {
        toast.error(response.message || 'Failed to delete FAQ');
      }
    } catch (error) {
      console.error('Error deleting FAQ:', error);
      toast.error(error.response?.data?.message || 'Error deleting FAQ');
    } finally {
      setShowDeleteModal(false);
      setDeleteFaqData(null);
      setLoading(false);
      setTableLoading(false);
    }
  };

  const handleStatusToggle = async (id, currentStatus) => {
    if (!permissions.settings_faq_status) {
      toast.warning("You don't have permission to change FAQ status");
      return;
    }

    try {
      setStatusLoading(id);
      setTableLoading(true);
      const response = await updateFAQStatus(id, { status: !currentStatus });
      if (response.success) {
        toast.success(`FAQ ${!currentStatus ? 'activated' : 'deactivated'} successfully`);
        await fetchFAQData();
      } else {
        toast.error(response.message || 'Failed to update status');
      }
    } catch (error) {
      console.error('Error updating status:', error);
      toast.error(error.response?.data?.message || 'Error updating status');
    } finally {
      setStatusLoading(null);
      setTableLoading(false);
    }
  };

  const handleSearchChange = (e) => {
    setSearch(e.target.value);
    setPagination(prev => ({ ...prev, currentPage: 1 }));
  };

  const handlePageChange = (newPage) => {
    setPagination(prev => ({ ...prev, currentPage: newPage }));
  };

  const handleLimitChange = (e) => {
    const newLimit = parseInt(e.target.value);
    setPagination(prev => ({
      ...prev,
      recordsPerPage: newLimit,
      currentPage: 1
    }));
  };

  const handleAddNew = () => {
    resetForm();
    setShowModal(true);
  };

  const handleDeleteClick = (e, id, question) => {
    e.stopPropagation();
    setDeleteFaqData({ id, question });
    setShowDeleteModal(true);
  };

  const getStatusBadge = (isActive) => {
    if (isActive) {
      return (
        <span className="badge border border-success bg-success bg-opacity-10 text-success d-inline-flex align-items-center gap-1">
          <Icon icon="mdi:check-circle-outline" className="fs-6" />
          Active
        </span>
      );
    }
    return (
      <span className="badge border border-danger bg-danger bg-opacity-10 text-danger d-inline-flex align-items-center gap-1">
        <Icon icon="mdi:close-circle-outline" className="fs-6" />
        Inactive
      </span>
    );
  };

  return (
    <>
      {/* search and add button  */}
      <div className="row mb-3">
        <div className="col-md-6 mt-2 d-flex justify-content-start align-items-center">
          <input
            type="search"
            className="form-control"
            placeholder="Search FAQs..."
            style={{ maxWidth: '300px' }}
            value={search}
            onChange={handleSearchChange}
          />
        </div>
        <div className="col-md-6 text-end mt-2 d-flex justify-content-end align-items-center">
          <button
            className="btn btn-primary text-nowrap"
            style={{ width: '180px' }}
            onClick={handleAddNew}
            disabled={loading || !permissions.settings_faq_create}
            title={!permissions.settings_faq_create ? "You don't have permission to create FAQs" : ""}
          >
            {loading ? 'Loading...' : 'Add New FAQ'}
          </button>
        </div>
      </div>

      {/* table */}
      <div className="row mb-3">
        <div className="col-12">
          <div className="card">
            <div className="table-responsive">
              <table className="table table-borderless mb-0" style={{ minWidth: '800px' }}>
                <thead>
                  <tr>
                    <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500', width: '80px' }}>SL No</th>
                    <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500', width: '30%' }}>Question</th>
                    <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500', width: '30%' }}>Answer</th>
                    <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500', width: '100px' }}>Status</th>
                    <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500', width: '120px' }}>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {tableLoading ? (
                    <tr>
                      <td colSpan="5" style={{ height: '400px' }}>
                        <Loader
                          caption="Loading FAQ Data..."
                          minLoadTime={500}
                          containerHeight="400px"
                        />
                      </td>
                    </tr>
                  ) : faqData.length === 0 ? (
                    <tr>
                      <td colSpan="5" style={{ height: '400px' }}>
                        <NoData caption="No FAQs found" />
                      </td>
                    </tr>
                  ) : (
                    faqData.map((item, index) => (
                      <tr key={item.id} className="border-bottom">
                        <td className="py-3 align-middle">
                          {(pagination.currentPage - 1) * pagination.recordsPerPage + index + 1}
                        </td>
                        <td className="py-3 align-middle">{item.question}</td>
                        <td className="py-3 align-middle">{item.answer}</td>
                        <td className="py-3 align-middle">
                          {getStatusBadge(item.is_active)}
                        </td>
                        <td className="py-3 align-middle">
                          <div className="d-flex gap-2">
                            <button
                              className="btn btn-outline-dark btn-sm border border-dark"
                              style={{ width: '36px', height: '36px' }}
                              title={!permissions.settings_faq_edit ? "You don't have permission to edit" : "Edit FAQ"}
                              onClick={() => handleEdit(item)}
                              disabled={loading || !permissions.settings_faq_edit}
                            >
                              <Icon icon="fluent:edit-24-regular" width="16" height="16" />
                            </button>
                            <button
                              className={`btn ${item.is_active ? "btn-success" : "btn-outline-warning"} btn-sm border`}
                              style={{ width: '36px', height: '36px' }}
                              title={!permissions.settings_faq_status ? "You don't have permission to change status" : (item.is_active ? "Deactivate" : "Activate")}
                              onClick={() => handleStatusToggle(item.id, item.is_active)}
                              disabled={statusLoading === item.id || !permissions.settings_faq_status}
                            >
                              {statusLoading === item.id ? (
                                <span className="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                              ) : (
                                <Icon 
                                  icon={item.is_active ? "fluent:checkmark-circle-24-regular" : "fluent:dismiss-circle-24-regular"}
                                  width="16" 
                                  height="16" 
                                />
                              )}
                            </button>
                            <button
                              className="btn btn-outline-danger btn-sm border border-danger"
                              style={{ width: '36px', height: '36px' }}
                              title={!permissions.settings_faq_delete ? "You don't have permission to delete" : "Delete FAQ"}
                              onClick={(e) => handleDeleteClick(e, item.id, item.question)}
                              disabled={loading || !permissions.settings_faq_delete}
                            >
                              <Icon icon="fluent:delete-24-regular" width="16" height="16" />
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))
                  )}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>

      {/* record per page and pagination */}
      <div className="row">
        <div className="col-md-6">
          <select
            className="form-select"
            style={{ width: 'auto' }}
            value={pagination.recordsPerPage}
            onChange={handleLimitChange}
          >
            <option value={10}>10 records per page</option>
            <option value={25}>25 records per page</option>
            <option value={50}>50 records per page</option>
          </select>
        </div>
        <div className="col-md-6 d-flex justify-content-end align-items-center gap-3">
          <button
            className="btn btn-primary"
            style={{ width: '150px' }}
            onClick={() => handlePageChange(pagination.currentPage - 1)}
            disabled={pagination.currentPage === 1 || loading}
          >
            Prev
          </button>
          <span>Page {pagination.currentPage} of {pagination.totalPages}</span>
          <button
            className="btn btn-primary"
            style={{ width: '150px' }}
            onClick={() => handlePageChange(pagination.currentPage + 1)}
            disabled={pagination.currentPage === pagination.totalPages || loading}
          >
            Next
          </button>
        </div>
      </div>

      {/* Add/Edit Modal */}
      {showModal && (
        <div className="modal show d-block" tabIndex="-1" style={{ backgroundColor: 'rgba(0,0,0,0.5)' }}>
          <div className="modal-dialog modal-dialog-centered">
            <div className="modal-content">
              <div className="modal-header border-0 pb-0">
                <h5 className="modal-title">
                  {isEditMode ? 'Edit FAQ' : 'Add New FAQ'}
                </h5>
                <button
                  type="button"
                  className="btn-close"
                  onClick={() => {
                    setShowModal(false);
                    resetForm();
                  }}
                ></button>
              </div>
              <div className="modal-body pt-2">
                <form onSubmit={handleSubmit}>
                  <div className="mb-3">
                    <label className="form-label">
                      Question <span className="text-danger">*</span>
                    </label>
                    <input
                      type="text"
                      className={`form-control ${formErrors.question ? 'is-invalid' : ''}`}
                      name="question"
                      value={formData.question}
                      onChange={handleInputChange}
                      placeholder="Enter question"
                      disabled={loading}
                    />
                    {formErrors.question && (
                      <div className="invalid-feedback">{formErrors.question}</div>
                    )}
                    <small className="text-muted">
                      {formData.question.length}/{MAX_QUESTION_LENGTH} characters
                    </small>
                  </div>
                  <div className="mb-3">
                    <label className="form-label">
                      Answer <span className="text-danger">*</span>
                    </label>
                    <textarea
                      className={`form-control ${formErrors.answer ? 'is-invalid' : ''}`}
                      name="answer"
                      value={formData.answer}
                      onChange={handleInputChange}
                      placeholder="Enter answer"
                      rows="4"
                      disabled={loading}
                    />
                    {formErrors.answer && (
                      <div className="invalid-feedback">{formErrors.answer}</div>
                    )}
                    <small className="text-muted">
                      {formData.answer.length}/{MAX_ANSWER_LENGTH} characters
                    </small>
                  </div>
                  <div className="modal-footer px-0 pb-0">
                    <button
                      type="button"
                      className="btn btn-secondary"
                      onClick={() => {
                        setShowModal(false);
                        resetForm();
                      }}
                      disabled={loading}
                    >
                      Cancel
                    </button>
                    <button
                      type="submit"
                      className="btn btn-primary"
                      disabled={loading}
                    >
                      {loading ? 'Saving...' : (isEditMode ? 'Update FAQ' : 'Save FAQ')}
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Delete Confirmation Modal */}
      {showDeleteModal && (
        <div className="modal show d-block" style={{ backgroundColor: 'rgba(0,0,0,0.5)' }}>
          <div className="modal-dialog modal-dialog-centered">
            <div className="modal-content">
              <div className="modal-header">
                <h5 className="modal-title">Delete FAQ</h5>
                <button
                  type="button"
                  className="btn-close"
                  onClick={() => {
                    setShowDeleteModal(false);
                    setDeleteFaqData(null);
                  }}
                ></button>
              </div>
              <div className="modal-body">
                <div className="text-center mb-3">
                  <Icon icon="fluent:delete-24-regular" className="text-danger mb-2" width="48" height="48" />
                  <h5>Are you sure?</h5>
                  <p className="text-muted">
                    Do you really want to delete this FAQ? This action cannot be undone.
                  </p>
                </div>
              </div>
              <div className="modal-footer">
                <button
                  type="button"
                  className="btn btn-secondary"
                  onClick={() => {
                    setShowDeleteModal(false);
                    setDeleteFaqData(null);
                  }}
                >
                  Cancel
                </button>
                <button
                  type="button"
                  className="btn btn-danger d-flex align-items-center gap-2"
                  onClick={() => handleDelete(deleteFaqData?.id)}
                  disabled={loading}
                >
                  {loading ? (
                    <>
                      <span className="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                      Deleting...
                    </>
                  ) : (
                    <>
                      <Icon icon="fluent:delete-24-regular" width="16" height="16" />
                      Delete FAQ
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  )
}

export default FAQ
