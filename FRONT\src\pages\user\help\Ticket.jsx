import React, { useState, useEffect } from 'react';
import { Table, Form, InputGroup, Button, Modal } from 'react-bootstrap';
import { Icon } from '@iconify/react';
import { toast } from 'react-toastify';
import { getAllTicket, postRaiseTicketV2, deleteTicket, editOrganisationUser } from '../../../services/userService';
import NoData from '../../../components/common/NoData';

function Ticket() {
  const [searchTerm, setSearchTerm] = useState('');
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(10);
  const [ticketData, setTicketData] = useState([]);
  const [pagination, setPagination] = useState({ page: 1, limit: 10, totalPages: 1, totalCount: 0 });

  const [showModal, setShowModal] = useState(false);
  const [showViewModal, setShowViewModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);

  const [subject, setSubject] = useState('');
  const [summary, setSummary] = useState('');
  const [errors, setErrors] = useState({ subject: '', summary: '' });

  const [selectedTicket, setSelectedTicket] = useState(null);
  const [ticketToDelete, setTicketToDelete] = useState(null);
  const [deleting, setDeleting] = useState(false);
  const [raising, setRaising] = useState(false);

  useEffect(() => {
    getTicketData();
  }, [page, limit, searchTerm]);

  const getTicketData = async () => {
    try {
      const payload = { page, limit, search: searchTerm };
      const response = await getAllTicket(payload);
      if (response.success && response.data) {
        setTicketData(response.data.tickets);
        setPagination(response.data.pagination);
      }
    } catch (error) {
      console.error('Error fetching ticket data:', error);
    }
  };

  const getStatusBadge = (status) => {
    switch (status) {
      case 'Pending':
        return (
          <span className="badge border border-warning bg-warning bg-opacity-10 text-warning d-inline-flex align-items-center gap-1">
            <Icon icon="mdi:clock-outline" className="fs-6" />
            Pending
          </span>
        );
      case 'Solved':
        return (
          <span className="badge border border-success bg-success bg-opacity-10 text-success d-inline-flex align-items-center gap-1">
            <Icon icon="mdi:check-circle-outline" className="fs-6" />
            Solved
          </span>
        );
      case 'In Progress':
        return (
          <span className="badge border border-info bg-info bg-opacity-10 text-info d-inline-flex align-items-center gap-1">
            <Icon icon="mdi:progress-clock" className="fs-6" />
            In Progress
          </span>
        );
      case 'Cancelled':
        return (
          <span className="badge border border-danger bg-danger bg-opacity-10 text-danger d-inline-flex align-items-center gap-1">
            <Icon icon="mdi:close-circle-outline" className="fs-6" />
            Cancelled
          </span>
        );
      default:
        return (
          <span className="badge border border-secondary bg-secondary bg-opacity-10 text-secondary d-inline-flex align-items-center gap-1">
            <Icon icon="mdi:help-circle-outline" className="fs-6" />
            Unknown
          </span>
        );
    }
  };

  const handleRaiseTicket = async () => {
    const newErrors = { subject: '', summary: '' };

    if (!subject.trim()) newErrors.subject = 'Subject is required';
    if (!summary.trim()) newErrors.summary = 'Summary is required';

    setErrors(newErrors);
    if (Object.values(newErrors).some(msg => msg)) return;

    setRaising(true);
    const payload = { subject, description: summary };

    setTimeout(async () => {
      try {
        const response = await postRaiseTicketV2(payload);
        if (response.success) {
          toast.success('Ticket raised successfully');
          getTicketData();
          setShowModal(false);
          setSubject('');
          setSummary('');
        } else toast.error(response.error_msg || 'Something went wrong');
      } catch (error) {
        console.error('Error raising ticket:', error);
        toast.error('Internal server error');
      } finally {
        setRaising(false);
      }
    }, 2000);
  };

  const handleDeleteConfirm = async () => {
    setDeleting(true);

    setTimeout(async () => {
      try {
        const response = await deleteTicket(ticketToDelete.ticket_id);
        if (response.success) {
          toast.success('Ticket deleted successfully');
          getTicketData();
        } else {
          toast.error(response.error_msg || 'Something went wrong');
        }
      } catch (err) {
        console.error('Delete error:', err);
        toast.error('Internal server error');
      } finally {
        setDeleting(false);
        setShowDeleteModal(false);
        setTicketToDelete(null);
      }
    }, 2000);
  };


  return (
    <>
      <div className="row py-3">
        <div className="col-12 mb-3">
          <div className="d-flex flex-wrap justify-content-between align-items-center">
            <h4 className="mb-2 mb-md-0">Ticket List</h4>
            <Button
              className="btn btn-primary px-4"
              style={{ whiteSpace: 'nowrap', width: 'auto' }}  // ✅ avoid full width
              onClick={() => setShowModal(true)}
            >
              Raise Ticket
            </Button>
          </div>
        </div>


        <div className="col-12">
          <div className="d-flex justify-content-between align-items-center mb-3 flex-wrap">
            <InputGroup className="w-auto">
              <Form.Control
                placeholder="Search tickets..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="rounded-3"
              />
            </InputGroup>
            <div className="d-flex align-items-center gap-2">
              <span className="d-none d-md-flex">Page </span>
              <span className="d-none d-md-flex">Size:</span>

              <Form.Select
                size="sm"
                value={limit}
                onChange={(e) => { setLimit(Number(e.target.value)); setPage(1); }}
                className="rounded-3"
              >
                {[10, 50, 100].map((num) => (
                  <option key={num} value={num}>{num}</option>
                ))}
              </Form.Select>
            </div>
          </div>

          <div style={{ overflowX: 'auto' }}>
            <Table striped bordered hover responsive className="table-hover" style={{ minWidth: '900px' }}>
              <thead className="table-primary">
                <tr>
                  <th style={{ whiteSpace: 'nowrap', paddingInline: '10px' }}>SL No.</th>
                  <th style={{ whiteSpace: 'nowrap', paddingInline: '10px' }}>Ticket ID</th>
                  <th style={{ whiteSpace: 'nowrap', paddingInline: '10px', minWidth: '250px' }}>Subject</th>
                  <th style={{ whiteSpace: 'nowrap', paddingInline: '10px', minWidth: '250px' }}>Response</th>
                  <th style={{ whiteSpace: 'nowrap', paddingInline: '10px' }}>Status</th>
                  <th style={{ whiteSpace: 'nowrap', paddingInline: '10px' }}>Submitted Date</th>
                  <th style={{ whiteSpace: 'nowrap', paddingInline: '10px' }} className="text-center">Action</th>
                </tr>
              </thead>
              <tbody>
                {ticketData.length > 0 ? (
                  ticketData.map((item, index) => (
                    <tr key={item.ticket_id}>
                      <td className="text-center">{(page - 1) * limit + index + 1}</td>
                      <td>{item.ticket_id}</td>
                      <td style={{ verticalAlign: 'middle' }}>
                        <div className="truncate-2-line" title={item.subject}>
                          {item.subject}
                        </div>
                      </td>
                      <td style={{ verticalAlign: 'middle' }}>
                        <div className="truncate-2-line" title={item.response || 'No response yet'}>
                          {item.response || 'No response yet'}
                        </div>
                      </td>
                      <td>{getStatusBadge(item.status)}</td>
                      <td>{new Date(item.createdAt).toLocaleDateString()}</td>
                      <td className="d-flex justify-content-center align-items-center gap-2">
                        <Button variant="outline-primary" size="sm" className="rounded-3"
                          onClick={() => { setSelectedTicket(item); setShowViewModal(true); }}
                          style={{ width: '32px', height: '32px', padding: '4px' }}
                        >
                          <Icon icon="mdi:eye" />
                        </Button>
                        <Button variant="outline-danger" size="sm" className="rounded-3"
                          onClick={() => { setTicketToDelete(item); setShowDeleteModal(true); }}
                          style={{ width: '32px', height: '32px', padding: '4px' }}
                        >
                          <Icon icon="mdi:delete" />
                        </Button>
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan="7" className="text-center">No tickets found.</td>
                  </tr>
                )}
              </tbody>
            </Table>
          </div>

          <div className="d-flex justify-content-center justify-content-md-between align-items-center flex-wrap gap-2 mt-3">
            <div>Showing {(page - 1) * limit + 1} to {Math.min(page * limit, pagination.totalCount)} of {pagination.totalCount} entries</div>
            <div className="d-flex gap-2">
              <Button variant="outline-secondary" size="sm" disabled={page === 1} onClick={() => setPage(page - 1)}>Prev</Button>
              <span>Page {page} of {pagination.totalPages}</span>
              <Button variant="outline-secondary" size="sm" disabled={page === pagination.totalPages} onClick={() => setPage(page + 1)}>Next</Button>
            </div>
          </div>
        </div>
      </div>

      {/* View Modal */}
      <Modal show={showViewModal} onHide={() => setShowViewModal(false)}>
        <Modal.Header closeButton><Modal.Title>Ticket Details</Modal.Title></Modal.Header>
        <Modal.Body>
          {selectedTicket && (
            <div className="d-flex flex-column gap-3">
              <div><strong>Ticket ID:</strong><div>{selectedTicket.ticket_id}</div></div>
              <div><strong>Subject:</strong><div>{selectedTicket.subject}</div></div>
              <div><strong>Summary:</strong><div>{selectedTicket.description}</div></div>
              <div><strong>Response:</strong><div>{selectedTicket.response || 'No response yet'}</div></div>
              <div><strong>Status:</strong><div>{selectedTicket.status}</div></div>
              <div><strong>Submitted Date:</strong><div>{new Date(selectedTicket.createdAt).toLocaleDateString()}</div></div>
            </div>
          )}
        </Modal.Body>
        <Modal.Footer><Button variant="secondary" onClick={() => setShowViewModal(false)}>Close</Button></Modal.Footer>
      </Modal>

      {/* Delete Modal */}
      <Modal show={showDeleteModal} onHide={() => setShowDeleteModal(false)} centered>
        <Modal.Header closeButton><Modal.Title>Confirm Deletion</Modal.Title></Modal.Header>
        <Modal.Body>
          Are you sure you want to delete this ticket?
          <br />
          <strong>Ticket ID: {ticketToDelete?.ticket_id}</strong>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowDeleteModal(false)} disabled={deleting}>Cancel</Button>
          <Button variant="danger" onClick={handleDeleteConfirm} disabled={deleting}>
            {deleting ? (<><span className="spinner-border spinner-border-sm me-2" /> Deleting...</>) : 'Delete'}
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Raise Ticket Modal */}
      <Modal show={showModal} onHide={() => setShowModal(false)}>
        <Modal.Header closeButton><Modal.Title>Raise New Ticket</Modal.Title></Modal.Header>
        <Modal.Body>
          <Form>
            <Form.Group className="mb-3">
              <Form.Label>Subject</Form.Label>
              <Form.Control
                type="text"
                placeholder="Enter subject"
                value={subject}
                onChange={(e) => setSubject(e.target.value)}
                isInvalid={!!errors.subject}
              />
              {errors.subject && <Form.Text className="text-danger">{errors.subject}</Form.Text>}
            </Form.Group>

            <Form.Group className="mb-3">
              <Form.Label>Summary</Form.Label>
              <Form.Control
                as="textarea"
                rows={4}
                placeholder="Enter summary"
                value={summary}
                onChange={(e) => setSummary(e.target.value)}
                isInvalid={!!errors.summary}
                maxLength={2000}
              />
              {errors.summary && <Form.Text className="text-danger">{errors.summary}</Form.Text>}
            </Form.Group>
          </Form>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowModal(false)} disabled={raising}>Close</Button>
          <Button variant="primary" onClick={handleRaiseTicket} disabled={raising}>
            {raising ? (<><span className="spinner-border spinner-border-sm me-2" /> Raising...</>) : 'Raise Ticket'}
          </Button>
        </Modal.Footer>
      </Modal>
    </>
  );
}

export default Ticket;
