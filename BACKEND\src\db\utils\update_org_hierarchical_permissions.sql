-- Update Organization Databases with Hierarchical Permission Tables
-- This script should be run for each organization database

-- 1) Create hierarchical_role_permissions table for organization databases
-- Note: Replace {ORG_DB_NAME} with actual organization database name when executing

-- For each organization database, create the hierarchical_role_permissions table
CREATE TABLE IF NOT EXISTS hierarchical_role_permissions (
  role_id   INT NOT NULL,
  module_id INT NOT NULL,
  action_id INT NOT NULL,
  created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY(role_id, module_id, action_id),
  FOREIGN KEY(role_id) REFERENCES roles(id) ON DELETE CASCADE,
  FOREIGN KEY(module_id) REFERENCES auth.permission_modules(id) ON DELETE CASCADE,
  FOREIGN KEY(action_id) REFERENCES auth.actions(id) ON DELETE CASCADE
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_hierarchical_role_permissions_role_id ON hierarchical_role_permissions(role_id);
CREATE INDEX IF NOT EXISTS idx_hierarchical_role_permissions_module_id ON hierarchical_role_permissions(module_id);
CREATE INDEX IF NOT EXISTS idx_hierarchical_role_permissions_action_id ON hierarchical_role_permissions(action_id);

-- Note: This script needs to be executed for each organization database
-- The actual execution should be done through the Node.js migration script
