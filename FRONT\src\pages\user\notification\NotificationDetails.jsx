import React, { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { Icon } from '@iconify/react';
import './NotificationDetails.css';
import { getCourseDetailsByIdForNotification } from '../../../services/userService';
import { encodeData } from '../../../utils/encodeAndEncode';
import moment from 'moment-timezone';

function NotificationDetails() {
  const location = useLocation();
  const navigate = useNavigate();
  const { notification } = location.state || {};
  const [courseDetails, setCourseDetails] = useState(null);

  useEffect(() => {
    const fetchCourseDetails = async () => {
      if (notification?.course_id) {
        try {
          const response = await getCourseDetailsByIdForNotification({
            course_id: notification.course_id
          });
          console.log('Course Details:', response.data);
          setCourseDetails(response.data);
        } catch (error) {
          console.error('Error fetching course details:', error);
        }
      }
    };

    fetchCourseDetails();
  }, [notification]);

  if (!notification) {
    return (
      <div className="notification-details-container">
        <div className="text-center p-5">
          Notification not found
        </div>
      </div>
    );
  }

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleString();
  };

  const handleStartLearning = () => {
    if (!courseDetails) return;

    const encodedCourseId = encodeData({ id: notification.course_id });
    
    // If course is already purchased/enrolled, go directly to watch page
    if (courseDetails.is_course_purchase) {
      navigate(`/user/courses/WatchCourse/${encodeURIComponent(encodedCourseId)}`);
    } else {
      // For non-enrolled courses, redirect to course details page
      navigate(`/user/courses/courseDetails/${encodeURIComponent(encodedCourseId)}`);
    }
  };

  

  return (
    <div className="row">
      <div className="col-12">
        <div className="notification-details-container">
          <div className="notification-details-card">
            <div className="notification-details-content">
              <div className="detail-group">
                <label>Title</label>
                <h3>{notification.title}</h3>
              </div>

              <div className="detail-group">
                <label>Message</label>
                <p>{notification.body}</p>
              </div>

              <div className="detail-group">
                <label>Date</label>
                <p>{moment.utc(notification.createdAt).local().format('MMM DD, YYYY, hh:mm A')}</p>

              </div>

              {notification.file_url && (
                <div className="detail-group">
                  <label>Attachment</label>
                  <a 
                    href={notification.file_url} 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="attachment-link"
                  >
                    <Icon icon="mdi:file-document-outline" width="20" height="20" />
                    View Attachment
                  </a>
                </div>
              )}
            </div>

            {courseDetails && (
              <div className="notification-preview-card">
                <div className="row g-0">
                  <div className="col-md-5">
                    <img
                      src={courseDetails.imageName}
                      alt={courseDetails.course_name}
                      className="notification-preview-image"
                      onError={(e) => {
                        e.target.src = 'https://via.placeholder.com/400x300?text=Course+Image';
                      }}
                    />
                  </div>
                  <div className="col-md-7">
                    <div className="notification-preview-content">
                      <h2 className="notification-preview-title">{courseDetails.course_name}</h2>
                      <p className="notification-preview-description">{courseDetails.description}</p>
                      
                      <div className="notification-preview-instructor">
                        <Icon icon="mdi:account" className="instructor-icon" width="24" height="24" />
                        <span>{courseDetails.trainer_name}</span>
                      </div>
                      
                      <div className='col-12 col-md-6'>
                      <button 
                        className="btn btn-primary d-flex align-items-center gap-2"
                        onClick={handleStartLearning}
                      >
                        <Icon icon="mdi:play-circle" width="20" height="20" />
                        Start Learning
                      </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

export default NotificationDetails;