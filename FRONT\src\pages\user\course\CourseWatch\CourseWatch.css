.course-watch-container {
  width: 100%;
  max-width: 100%;
  margin: 0;
  padding: 0;
  background: #fff;
}

/* Header styling */
.course-watch-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px;
  background: #fff;
  border-bottom: 1px solid #eee;
  position: sticky;
  top: 0;
  z-index: 100;
}

.course-watch-header h2 {
  margin: 0;
  font-size: 1.2rem;
  font-weight: 500;
  color: #333;
}

.back-button {
  cursor: pointer;
  display: flex;
  align-items: center;
}

.menu-toggle-btn {
  background: none;
  border: none;
  padding: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Mobile close button */
.mobile-close-btn {
  position: absolute;
  top: 15px;
  right: 15px;
  z-index: 100;
  background: transparent;
  border: none;
  color: #333;
  cursor: pointer;
  padding: 8px;
}

/* Course module wrapper for mobile */
@media (max-width: 767px) {
  .course-module-wrapper {
    position: fixed;
    top: 0;
    right: -100%;
    width: 100%;
    height: 100vh;
    background: #ffffff;
    z-index: 10000;
    transition: right 0.3s ease;
    padding: 20px;
    overflow-y: auto;
  }

  .course-module-wrapper.show-mobile {
    right: 0;
  }

  /* Hide scrollbar on main body when mobile menu is open */
  body.mobile-menu-open {
    overflow: hidden;
  }

  .row {
    margin: 0;
  }

  .col-md-8 {
    padding: 0;
  }
}

/* Desktop styles */
@media (min-width: 768px) {
  .course-watch-header {
    display: none;
  }

  .mobile-close-btn {
    display: none;
  }

  .course-module-wrapper {
    position: static;
    height: auto;
    background: transparent;
    padding: 0;
  }

  .course-watch-container {
    padding: 0 15px;
  }
} 