import React, { useState, Suspense, lazy } from 'react';
import { Icon } from '@iconify/react';
import Loader from '../../../components/common/Loader';

// Lazy load components
const PendingApproval = lazy(() => import('./PendingApproval'));
const Approved = lazy(() => import('./Approved'));
const Rejected = lazy(() => import('./Rejected'));

function ApprovalRequest() {
  const [activeTab, setActiveTab] = useState('pending');

  return (
    <>
      <div className="row">
        <div className="col-12">
          <div className="card">
            {/* Tabs Navigation */}
            <ul className="nav nav-tabs border-bottom">
              <li className="nav-item">
                <button
                  className={`nav-link d-flex align-items-center ${activeTab === 'pending' ? 'active text-primary border-primary' : ''}`}
                  onClick={() => setActiveTab('pending')}
                  style={{ borderBottom: activeTab === 'pending' ? '3px solid' : 'none' }}
                >
                  <Icon 
                    icon="fluent:clock-24-regular" 
                    width="20" 
                    height="20" 
                    className="me-2"
                  />
                  Pending Approval
                </button>
              </li>
              <li className="nav-item">
                <button
                  className={`nav-link d-flex align-items-center ${activeTab === 'approved' ? 'active text-primary border-primary' : ''}`}
                  onClick={() => setActiveTab('approved')}
                  style={{ borderBottom: activeTab === 'approved' ? '3px solid' : 'none' }}
                >
                  <Icon 
                    icon="fluent:checkmark-circle-24-regular" 
                    width="20" 
                    height="20" 
                    className="me-2"
                  />
                  Approved
                </button>
              </li>
              <li className="nav-item">
                <button
                  className={`nav-link d-flex align-items-center ${activeTab === 'rejected' ? 'active text-primary border-primary' : ''}`}
                  onClick={() => setActiveTab('rejected')}
                  style={{ borderBottom: activeTab === 'rejected' ? '3px solid' : 'none' }}
                >
                  <Icon 
                    icon="fluent:dismiss-circle-24-regular" 
                    width="20" 
                    height="20" 
                    className="me-2"
                  />
                  Rejected
                </button>
              </li>
            </ul>

            {/* Tab Content */}
            <div className="tab-content">
              <Suspense fallback={<Loader />}>
                <div className={`tab-pane fade ${activeTab === 'pending' ? 'show active' : ''}`}>
                  <div className="card-body p-1">
                    {activeTab === 'pending' && <PendingApproval />}
                  </div>
                </div>

                <div className={`tab-pane fade ${activeTab === 'approved' ? 'show active' : ''}`}>
                  <div className="card-body p-1">
                    {activeTab === 'approved' && <Approved />}
                  </div>
                </div>

                <div className={`tab-pane fade ${activeTab === 'rejected' ? 'show active' : ''}`}>
                  <div className="card-body p-1">
                    {activeTab === 'rejected' && <Rejected />}
                  </div>
                </div>
              </Suspense>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}

export default ApprovalRequest;
