const { sendEmail } = require("./tools");
const ApplicationName = process.env.APPLICATION_NAME || 'LMS';


// Register OTP
const registerOTP = (email, subject, otp) => {

    const body = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Account Verification</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Poppins', sans-serif;
            background-color: #f4f6f8;
            margin: 0;
            padding: 0;
            color: #333333;
        }
        .container {
            width: 100%;
            max-width: 600px;
            margin: 30px auto;
            background-color: #ffffff;
            border-radius: 10px;
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
            overflow: hidden;
        }
        .header {
            background-color: #3152E8;
            color: #ffffff;
            padding: 30px 20px;
            text-align: center;
        }
        .header h2 {
            margin: 0;
            font-size: 26px;
            font-weight: 600;
        }
        .content {
            padding: 35px 30px;
            text-align: left;
        }
        .content p {
            font-size: 16px;
            line-height: 1.6;
            margin: 18px 0;
        }
        .otp-box {
            display: inline-block;
            margin: 30px auto;
            padding: 16px 34px;
            font-size: 28px;
            font-weight: 600;
            background-color: #3152E8;
            color: #ffffff;
            border-radius: 8px;
            letter-spacing: 5px;
            text-align: center;
        }
        .footer {
            background-color: #f0f0f0;
            padding: 18px 25px;
            text-align: center;
            font-size: 13px;
            color: #777;
        }
        .footer a {
            color: #3152E8;
            text-decoration: none;
        }
        @media only screen and (max-width: 600px) {
            .content, .footer {
                padding: 25px 20px;
            }
            .otp-box {
                font-size: 24px;
                padding: 14px 28px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h2>Account Verification</h2>
        </div>
        <div class="content">
            <p>Hello,</p>
            <p>Thank you for registering with <strong>${ApplicationName}</strong>. Please use the following One-Time Password (OTP) to verify your email and complete your registration:</p>
            <div style="text-align: center;">
                <span class="otp-box">${otp}</span>
            </div>
            <p>This OTP is valid for a limited time. Please do not share it with anyone for security reasons.</p>
            <p>If you did not initiate this request, you can safely ignore this message.</p>
            <p>Best regards,<br><strong>The ${ApplicationName} Team</strong></p>
        </div>
    </div>
</body>
</html>


    `;

    sendEmail(email, subject, body);
};

// Forgot Password OTP
const forgotPasswordOTP = (email, subject, otp) => {

    const body = `
      <!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Forgot Password OTP</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Poppins', sans-serif;
            background-color: #f4f6f8;
            margin: 0;
            padding: 0;
            color: #333;
        }
        .container {
            width: 100%;
            max-width: 600px;
            margin: 30px auto;
            background-color: #ffffff;
            border-radius: 10px;
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
            overflow: hidden;
        }
        .header {
            background-color: #3152E8;
            color: #ffffff;
            padding: 30px 20px;
            text-align: center;
        }
        .header h2 {
            margin: 0;
            font-size: 26px;
            font-weight: 600;
        }
        .content {
            padding: 35px 30px;
            text-align: left;
        }
        .content p {
            font-size: 16px;
            line-height: 1.6;
            margin: 18px 0;
        }
        .otp-box {
            display: inline-block;
            padding: 16px 34px;
            font-size: 28px;
            font-weight: 600;
            background-color: #3152E8;
            color: #ffffff;
            border-radius: 8px;
            letter-spacing: 5px;
            text-align: center;
            margin: 25px 0;
        }
        .footer {
            background-color: #f0f0f0;
            padding: 18px 25px;
            text-align: center;
            font-size: 13px;
            color: #777;
        }
        .footer a {
            color: #3152E8;
            text-decoration: none;
        }
        @media only screen and (max-width: 600px) {
            .content, .footer {
                padding: 25px 20px;
            }
            .otp-box {
                font-size: 24px;
                padding: 14px 28px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h2>Forgot Password OTP</h2>
        </div>
        <div class="content">
            <p>Hello,</p>
            <p>We received a request to reset the password for your <strong>${ApplicationName}</strong> account. To proceed, please use the following One-Time Password (OTP):</p>
            
            <div style="text-align: center;">
                <span class="otp-box">${otp}</span>
            </div>

            <p>This OTP is valid for a short time. Please do not share it with anyone.</p>

            <p><strong>If you did not request a password reset, you can safely ignore this email.</strong></p>

            <p>Regards,<br><strong>${ApplicationName} Support Team</strong></p>
        </div>
    </div>
</body>
</html>

    `;

    sendEmail(email, subject, body);
};

const ActiveAccountOTPFromLoginPage = (email, subject, otp) => {
    console.log("🔍 ------------------------------------------------:", email, subject, otp);

    const body = `
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <title>Account Verification</title>
        <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600&display=swap" rel="stylesheet">
        <style>
            body {
                font-family: 'Poppins', sans-serif;
                background-color: #f4f6f8;
                margin: 0;
                padding: 0;
                color: #333333;
            }
            .container {
                width: 100%;
                max-width: 600px;
                margin: 30px auto;
                background-color: #ffffff;
                border-radius: 10px;
                box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
                overflow: hidden;
            }
            .header {
                background-color: #3152E8;
                color: #ffffff;
                padding: 30px 20px;
                text-align: center;
            }
            .header h2 {
                margin: 0;
                font-size: 26px;
                font-weight: 600;
            }
            .content {
                padding: 35px 30px;
                text-align: left;
            }
            .content p {
                font-size: 16px;
                line-height: 1.6;
                margin: 18px 0;
            }
            .otp-box {
                display: inline-block;
                margin: 30px auto;
                padding: 16px 34px;
                font-size: 28px;
                font-weight: 600;
                background-color: #3152E8;
                color: #ffffff;
                border-radius: 8px;
                letter-spacing: 5px;
                text-align: center;
            }
            .footer {
                background-color: #f0f0f0;
                padding: 18px 25px;
                text-align: center;
                font-size: 13px;
                color: #777;
            }
            .footer a {
                color: #3152E8;
                text-decoration: none;
            }
            @media only screen and (max-width: 600px) {
                .content, .footer {
                    padding: 25px 20px;
                }
                .otp-box {
                    font-size: 24px;
                    padding: 14px 28px;
                }
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h2>Account Verification</h2>
            </div>
            <div class="content">
                <p>Hello,</p>
                <p>Thank you for registering with <strong>${ApplicationName}</strong>. Please use the following One-Time Password (OTP) to verify your email and complete your registration:</p>
                <div style="text-align: center;">
                    <span class="otp-box">${otp}</span>
                </div>
                <p>This OTP is valid for a limited time. Please do not share it with anyone for security reasons.</p>
                <p>If you did not initiate this request, you can safely ignore this message.</p>
                <p>Best regards,<br><strong>The ${ApplicationName} Team</strong></p>
            </div>
        </div>
    </body>
    </html>
    
    
        `;

    sendEmail(email, subject, body);
};

const sendMagicLink = (email, subject, magicLink) => {

    body = `
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <title>Support Ticket Received</title>
                <style>
                    body {
                        font-family: Arial, sans-serif;
                        color: #333333;
                        line-height: 1.6;
                    }
                    .container {
                        width: 90%;
                        max-width: 600px;
                        margin: auto;
                    }
                    .header {
                        background-color: #3152E8;
                        color: white;
                        padding: 10px 20px;
                        text-align: center;
                    }
                    .content {
                        padding: 20px;
                        border: 1px solid #dddddd;
                        border-top: none;
                    }
                    .footer {
                        padding: 10px 20px;
                        text-align: center;
                        color: #777777;
                        font-size: 12px;
                    }
                    .button {
                        display: inline-block;
                        padding: 10px 15px;
                        margin-top: 20px;
                        background-color: #3152E8;
                        color: white;
                        text-decoration: none;
                        border-radius: 2rem;
                    }
                    .button:hover {
                        border: 2px solid #3152E8;
                        background: white;
                        color:#3152E8;
                        transition: all 0.3s ease;
                        cursor: pointer;
                    }
                    .button:after {
                        border: 2px solid #3152E8;
                        background: white;
                    }
                    .issue-summary {
                        background-color: #f9f9f9;
                        padding: 10px;
                        border-left: 4px solid orange;
                        margin: 10px 0;
                    }
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <h2>Welcome to LMS</h2>
                    </div>
                    <div class="content">
                        <p>Hello,</p>
                        <p>We received a request to log in to your account using a magic link. Click the button below to securely log in:</p>                        
                        <a  class="button" href="${magicLink}" >Log In to Your Account</a>
                        <p>If you did not request this link, please ignore this email or contact support if you have any questions.</p>
                        <p>Best regards,<br>
                        <strong>LMS Team</strong></p>
                    </div>
                    <div class="footer">
                        <p>&copy; 2024 LMS. All rights reserved</p>
                        <p><a>Visit our website</a></p>
                    </div>
                </div>
            </body>
            </html>`

    sendEmail(email, subject, body);
}

const sendForgotPasswordLink = (email, subject, link) => {

    body = `
    <!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Reset Your Password</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Poppins', sans-serif;
            background-color: #f4f6f8;
            margin: 0;
            padding: 0;
            color: #333;
        }
        .container {
            width: 100%;
            max-width: 600px;
            margin: 30px auto;
            background-color: #ffffff;
            border-radius: 10px;
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
            overflow: hidden;
        }
        .header {
            background-color: #3152E8;
            color: #ffffff;
            padding: 30px 20px;
            text-align: center;
        }
        .header h2 {
            margin: 0;
            font-size: 26px;
            font-weight: 600;
        }
        .content {
            padding: 35px 30px;
            text-align: left;
        }
        .content p {
            font-size: 16px;
            line-height: 1.6;
            margin: 18px 0;
        }
        .button {
            display: inline-block;
            padding: 12px 30px;
            margin-top: 25px;
            background-color: #3152E8;
            color: #ffffff;
            text-decoration: none;
            border-radius: 2rem;
            font-size: 16px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        .button:hover {
            background-color: #ffffff;
            color: #3152E8;
            border: 2px solid #3152E8;
        }
        .footer {
            background-color: #f0f0f0;
            padding: 18px 25px;
            text-align: center;
            font-size: 13px;
            color: #777;
        }
        .footer a {
            color: #3152E8;
            text-decoration: none;
        }
        @media only screen and (max-width: 600px) {
            .content, .footer {
                padding: 25px 20px;
            }
            .button {
                font-size: 15px;
                padding: 10px 24px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h2>Reset Your Password</h2>
        </div>
        <div class="content">
            <p>Hello,</p>

            <p>We received a request to reset your account password. Click the button below to reset your password:</p>

            <div style="text-align: center;">
                <a class="button" href="${link}">Reset Password</a>
            </div>

            <p>If you did not request this password reset, please ignore this email or contact support.</p>

            <p>Best regards,<br>
            <strong>The ${ApplicationName} Team</strong></p>
        </div>
    </div>
</body>
</html>

`

    sendEmail(email, subject, body);
}

const sendOTP = (email, subject, otp) => {

    body = `
         <!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Resend OTP</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            color: #333333;
            line-height: 1.6;
        }
        .container {
            width: 90%;
            max-width: 600px;
            margin: auto;
        }
        .header {
            background-color: #3152E8;
            color: white;
            padding: 10px 20px;
            text-align: center;
        }
        .content {
            padding: 20px;
            border: 1px solid #dddddd;
            border-top: none;
        }
        .footer {
            padding: 10px 20px;
            text-align: center;
            color: #777777;
            font-size: 12px;
        }
        .button {
            display: inline-block;
            padding: 10px 15px;
            margin-top: 20px;
            background-color: #3152E8;
            color: white;
            text-decoration: none;
            border-radius: 2rem;
        }
        .button:hover {
            border: 2px solid #3152E8;
            background: white;
            color:#3152E8;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .otp-code {
            text-align: center;
            margin: 20px 0;
        }
        .otp-code label {
            padding: 10px 20px;
            font-size: 18px;
            font-weight: bold;
            color: #ffffff;
            background-color: #007bff;
            border-radius: 5px;
            display: inline-block;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h2>OTP Resend</h2>
        </div>
        <div class="content">
            <p>Hello,</p>

            <p>You recently requested a new OTP for your ${ApplicationName} registration. Please use the following 6-digit code to complete your account verification:</p>

            <div class="otp-code">
                <label>${otp}</label>
            </div>

            <p>If you did not request this OTP, please ignore this email. For any concerns, feel free to contact our support team.</p>

            <p>Best regards,<br>
            <strong>${ApplicationName} Team</strong></p>
        </div>
    </div>
</body>
</html>
`
    sendEmail(email, subject, body);
}

const supportTeamMail = (email, ticket_id, submitted_date, discription, name) => {

    body = `
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>Support Ticket Received</title>
            <style>
                body {
                    font-family: Arial, sans-serif;
                    color: #333333;
                    line-height: 1.6;
                }
                .container {
                    width: 90%;
                    max-width: 600px;
                    margin: auto;
                }
                .header {
                    background-color: #3152E8;
                    color: white;
                    padding: 10px 20px;
                    text-align: center;
                }
                .content {
                    padding: 20px;
                    border: 1px solid #dddddd;
                    border-top: none;
                }
                .footer {
                    padding: 10px 20px;
                    text-align: center;
                    color: #777777;
                    font-size: 12px;
                }
                .button {
                    display: inline-block;
                    padding: 10px 15px;
                    margin-top: 20px;
                    background-color: #3152E8;
                    color: white;
                    text-decoration: none;
                    border-radius: 2rem;
                }
                .button:hover {
                    border: 2px solid #3152E8;
                    background: white;
                    color:#3152E8;
                    transition: all 0.3s ease;
                    cursor: pointer;
                }
                .button:after {
                    border: 2px solid #3152E8;
                    background: white;
                }
                .issue-summary {
                    background-color: #f9f9f9;
                    padding: 10px;
                    border-left: 4px solid orange;
                    margin: 10px 0;
                }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h2>LMS Support Team</h2>
                </div>
                <div class="content">
                    <p>Hi <strong>${name}</strong>,</p>

                    <p>Thank you for contacting us. We have received your support ticket regarding:</p>

                    <div class="issue-summary">
                        <strong>Issue Summary:</strong><br>
                        ${discription}
                    </div>

                    <p><strong>Ticket ID:</strong>#${ticket_id}<br>
                    <strong>Submitted On:</strong> ${submitted_date}</p>

                    <p>Our support team is currently reviewing your request and will get back to you within <strong>48 Hours.</strong></p>

                    <a  class="button">Track Your Ticket</a>


                    <p>Thank you for your patience and understanding.</p>

                    <p>Best regards,<br>
                    <strong>LMS Support Team</strong></p>
                </div>
                <div class="footer">
                    <p>&copy; 2024 LMS. All rights reserved</p>
                    <p><a>Visit our website</a></p>
                </div>
            </div>
        </body>
        </html>`

    subject = `Your Ticket #${ticket_id} Has Been Received`
    sendEmail(email, subject, body);
}

const sendRecoveryEmail = (email, subject, recoveryLink) => {
    body = `
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>Account Recovery</title>
            <style>
            body {
                font-family: Arial, sans-serif;
                color: #333333;
                line-height: 1.6;
            }
            .container {
                width: 90%;
                max-width: 600px;
                margin: auto;
            }
            .header {
                background-color: #3152E8;
                color: white;
                padding: 10px 20px;
                text-align: center;
            }
            .content {
                padding: 20px;
                border: 1px solid #dddddd;
                border-top: none;
            }
            .footer {
                padding: 10px 20px;
                text-align: center;
                color: #777777;
                font-size: 12px;
            }
            .button {
                display: inline-block;
                padding: 10px 15px;
                margin-top: 20px;
                background-color: #3152E8;
                color: white;
                text-decoration: none;
                border-radius: 2rem;
            }
            .button:hover {
                border: 2px solid #3152E8;
                background: white;
                color:#3152E8;
                transition: all 0.3s ease;
                cursor: pointer;
            }
            .button:after {
                border: 2px solid #3152E8;
                background: white;
            }
            .issue-summary {
                background-color: #f9f9f9;
                padding: 10px;
                border-left: 4px solid orange;
                margin: 10px 0;
            }
        </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h2>LMS Account Recovery</h2>
                </div>
                <div class="content">
                    <p>Hello,</p>

                    <p>We received a request to recover your LMS account. Click the button below to proceed with the account recovery process:</p>

                    <a class="button" href="${recoveryLink}">Recover Your Account</a>

                    <p>If you did not request this recovery, please ignore this email or contact support if you have any concerns.</p>

                    <p>Best regards,<br>
                    <strong>LMS Team</strong></p>
                </div>
                <div class="footer">
                    <p>&copy; 2024 LMS. All rights reserved</p>
                    <p><a>Visit our website</a></p>
                </div>
            </div>
        </body>
        </html>`
    sendEmail(email, subject, body);
}

const sentEmailNotification = (email, subject, title, body, name) => {
    body = `
       <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>Welcome to LMS</title>
            <style>
                body {
                    font-family: Arial, sans-serif;
                    color: #333333;
                    line-height: 1.6;
                }
                .container {
                    width: 90%;
                    max-width: 600px;
                    margin: auto;
                }
                .header {
                    background-color: #3152E8;
                    color: white;
                    padding: 10px 20px;
                    text-align: center;
                }
                .content {
                    padding: 20px;
                    border: 1px solid #dddddd;
                    border-top: none;
                }
                .footer {
                    padding: 10px 20px;
                    text-align: center;
                    color: #777777;
                    font-size: 12px;
                }
                .button {
                    display: inline-block;
                    padding: 10px 15px;
                    margin-top: 20px;
                    background-color: #3152E8;
                    color: white;
                    text-decoration: none;
                    border-radius: 2rem;
                }
                .button:hover {
                    border: 2px solid #3152E8;
                    background: white;
                    color: #3152E8;
                    transition: all 0.3s ease;
                    cursor: pointer;
                }
                .button:after {
                    border: 2px solid #3152E8;
                    background: white;
                }
                .issue-summary {
                    background-color: #f9f9f9;
                    padding: 10px;
                    border-left: 4px solid green;
                    margin: 10px 0;
                }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h2>${title}</h2>
                </div>
                <div class="content">
                    <p>Hello <strong>${name}</strong>,</p>

                    <p>${body}</p>


                    <p>If you have any questions, feel free to reach out to our support team. We're here to help!</p>

                    <p>Best regards,<br>
                    <strong>LMS Team</strong></p>
                </div>
                <div class="footer">
                    <p>&copy; 2024 LMS. All rights reserved</p>
                    <p><a href="#">Visit our website</a></p>
                </div>
            </div>
        </body>
        </html>
        `
    sendEmail(email, subject, body);
}

const sendAccountCreatedEmail = (email, firstName, generatedPassword, loginLink, org) => {
    const subject = 'Account Created';
    const body = `
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>Welcome to LMS</title>
            <style>
                body {
                    font-family: Arial, sans-serif;
                    color: #333333;
                    line-height: 1.6;
                }
                .container {
                    width: 90%;
                    max-width: 600px;
                    margin: auto;
                }
                .header {
                    background-color: #3152E8;
                    color: white;
                    padding: 10px 20px;
                    text-align: center;
                }
                .content {
                    padding: 20px;
                    border: 1px solid #dddddd;
                    border-top: none;
                }
                .footer {
                    padding: 10px 20px;
                    text-align: center;
                    color: #777777;
                    font-size: 12px;
                }
                .button {
                    display: inline-block;
                    padding: 10px 15px;
                    margin-top: 20px;
                    background-color: #3152E8;
                    color: white;
                    text-decoration: none;
                    border-radius: 2rem;
                }
                .button:hover {
                    border: 2px solid #3152E8;
                    background: white;
                    color: #3152E8;
                    transition: all 0.3s ease;
                    cursor: pointer;
                }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h2>Welcome to ${org.name}</h2>
                </div>
                <div class="content">
                    <p>Dear ${firstName},</p>

                    <p>Your account has been successfully created.</p>

                    <p>Here are your login details:</p>
                    <ul>
                        <li><strong>Email:</strong> ${email}</li>
                        <li><strong>Password:</strong> ${generatedPassword}</li>
                    </ul>

                    <p>Click the button below to log in to your account:</p>

                    <a class="button" href="${loginLink}">Log In to Your Account</a>

                    <p>Please make sure to change your password after logging in for security reasons.</p>

                    <p>Best regards,<br>
                    <strong>${org.name} Team</strong></p>
                </div>
                <div class="footer">
                    <p>&copy; 2024 ${org.name} . All rights reserved</p>
                    <p><a href="${org.auth_sub_domain}">Visit our website</a></p>
                </div>
            </div>
        </body>
        </html>
    `;

    sendEmail(email, subject, body);
};


const sendLiveClassEmail = (email, subject, title, classDetails, isReminder = false) => {
    const body = `
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>${title}</title>
            <style>
                body {
                    font-family: Arial, sans-serif;
                    color: #333333;
                    line-height: 1.6;
                    margin: 0;
                    padding: 0;
                }
                .container {
                    width: 90%;
                    max-width: 600px;
                    margin: auto;
                    background-color: #ffffff;
                }
                .header {
                    background-color: #3152E8;
                    color: white;
                    padding: 20px;
                    text-align: center;
                    border-radius: 8px 8px 0 0;
                }
                .content {
                    padding: 30px;
                    border: 1px solid #dddddd;
                    border-top: none;
                    background-color: #f9f9f9;
                }
                .class-details {
                    background-color: white;
                    padding: 20px;
                    border-radius: 8px;
                    margin: 20px 0;
                    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                }
                .class-info {
                    margin: 10px 0;
                    padding-left: 15px;
                    border-left: 3px solid #3152E8;
                }
                .benefits {
                    margin: 20px 0;
                }
                .benefit-item {
                    margin: 10px 0;
                    padding-left: 25px;
                    position: relative;
                }
                .benefit-item:before {
                    content: "•";
                    color: #3152E8;
                    font-size: 20px;
                    position: absolute;
                    left: 5px;
                }
                .footer {
                    padding: 20px;
                    text-align: center;
                    color: #777777;
                    font-size: 12px;
                    background-color: #f1f1f1;
                    border-radius: 0 0 8px 8px;
                }
                .highlight {
                    color: #3152E8;
                    font-weight: bold;
                }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h2>${title}</h2>
                </div>
                <div class="content">
                    <p>Dear Student,</p>
                    <p>${isReminder ? 
                        'This is a friendly reminder about your upcoming live class.' : 
                        'We are excited to share the details of your upcoming live class!'}</p>
                    
                    <div class="class-details">
                        <h3 style="color: #3152E8; margin-bottom: 15px;">Class Details</h3>
                        <div class="class-info">
                            <p><strong>Topic:</strong> ${classDetails.className}</p>
                            <p><strong>Description:</strong> ${classDetails.description || 'N/A'}</p>
                            <p><strong>Date:</strong> ${classDetails.formattedDate}</p>
                            <p><strong>Time:</strong> ${classDetails.formattedTime}</p>
                        </div>
                    </div>

                    <div class="benefits">
                        <p><strong>This is a great opportunity to:</strong></p>
                        <div class="benefit-item">Interact directly with your instructor</div>
                        <div class="benefit-item">Participate in live discussions</div>
                        <div class="benefit-item">Clear your doubts in real-time</div>
                        <div class="benefit-item">Engage with your peers</div>
                    </div>

                    <p>Please ensure you join the class on time to make the most of this learning opportunity.</p>
                    <p>If you have any technical issues, our support team is here to help.</p>
                </div>
                <div class="footer">
                    <p>Best regards,<br>Your Education Team</p>
                </div>
            </div>
        </body>
        </html>
    `;

    sendEmail(email, subject, body);
};

module.exports = { 
    sendMagicLink, 
    sendForgotPasswordLink, 
    sendOTP, 
    registerOTP,
    supportTeamMail, 
    sendRecoveryEmail, 
    sentEmailNotification,
    sendAccountCreatedEmail,
    sendLiveClassEmail,
    forgotPasswordOTP ,
    ActiveAccountOTPFromLoginPage
};

