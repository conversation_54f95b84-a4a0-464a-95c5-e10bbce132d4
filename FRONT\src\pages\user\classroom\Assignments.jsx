import React, { useEffect, useState, useRef, useCallback } from 'react'
import { useNavigate } from 'react-router-dom';
import { Icon } from '@iconify/react';
import moment from 'moment-timezone';
import { getAssignments } from '../../../services/userService'; 
import { encodeData } from '../../../utils/encodeAndEncode';

function Assignments({ classroom_id }) {
  const navigate = useNavigate();
  const userId = JSON.parse(localStorage.getItem('user'))?.id;
  const [assignments, setAssignments] = useState([]);
  const [search, setSearch] = useState('');
  const [page, setPage] = useState(1);
  const [itemsPerPage] = useState(10);
  const [loading, setLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const observer = useRef();
  const userTimeZone = moment.tz.guess(); // Get user's local timezone

  const lastAssignmentElementRef = useCallback(node => {
    if (loading) return;
    if (observer.current) observer.current.disconnect();
    observer.current = new IntersectionObserver(entries => {
      if (entries[0].isIntersecting && hasMore) {
        setPage(prevPage => prevPage + 1);
      }
    });
    if (node) observer.current.observe(node);
  }, [loading, hasMore]);

  useEffect(() => {
    const fetchAssignments = async () => {
      try {
        setLoading(true);
        const response = await getAssignments({ 
          class_id: classroom_id, 
          user_id: userId,
          search: search,
          page: page,
          limit: itemsPerPage
        });
        console.log("Assignments Response Data:", response);
        if(response.success === true){
          if (page === 1) {
            setAssignments(response.data.assignments || []);
          } else {
            setAssignments(prev => [...prev, ...(response.data.assignments || [])]);
          }
          setHasMore(response.data.pagination.currentPage < response.data.pagination.totalPages);
        }
      } catch (error) {
        console.error("Error fetching assignments:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchAssignments();
  }, [classroom_id, userId, search, page, itemsPerPage]);

  const handleSearch = (e) => {
    setSearch(e.target.value);
    setPage(1);
    setAssignments([]);
  };

  const getStatusClass = (status) => {
    switch(status?.toLowerCase()) {
      case 'submitted':
        return 'bg-success-subtle text-success';
      case 'in-progress':
        return 'bg-warning-subtle text-warning';
      case 'overdue-pending':
        return 'bg-danger-subtle text-danger';
      default:
        return 'bg-secondary-subtle text-secondary';
    }
  };

  const formatDate = (utcDateString) => {
    if (!utcDateString) return '';
    
    // Convert UTC to user's local timezone
    const utcDate = moment.utc(utcDateString);
    const localDate = utcDate.tz(userTimeZone);
    
    return `${localDate.format('MMMM D, YYYY h:mm A')} (${moment.tz(userTimeZone).zoneAbbr()})`;
  };

  const handleViewAssignment = (assignment) => {
    const encodedAssignmentId = encodeData(assignment.id);
    const encodedClassroomId = encodeData(classroom_id);
    navigate(`/user/assignmentsQuestions/${encodeURIComponent(encodedAssignmentId)}?classroomid=${encodeURIComponent(encodedClassroomId)}`);
  };

  return (
    <div className="assignments">
      <div className="row mb-4 mt-2">
        <div className="col-12 col-md-4 col-lg-4">
          <div className="seach-control">
            <input
              type="text"
              className="form-control search-input"
              placeholder="Search assignments..."
              value={search}
              onChange={handleSearch}
            />
          </div>
        </div>
      
      </div>

      <div className="row mb-4">
        <div className="col-12">
          {assignments && assignments.length > 0 ? (
            assignments.map((assignment, index) => (
              <div 
                key={assignment.id} 
                ref={index === assignments.length - 1 ? lastAssignmentElementRef : null}
                className="card mb-4"
              >
                <div className="card-body">
                  <div className="row">
                    <div className="col-12">
                      <div className="d-flex align-items-center mb-3">
                        <Icon
                          icon="mdi:file-document-outline"
                          className="text-primary me-3"
                          width="24"
                          height="24"
                        />
                        <h5 className="mb-0">{assignment.title}</h5>
                      </div>
                      <p className="text-muted mb-3">{assignment.description}</p>
                      <div className="d-flex flex-column flex-md-row align-items-start align-items-md-center gap-4 mb-3">
                        <span className="d-flex align-items-center text-muted">
                          <Icon icon="mdi:clock-outline" className="me-2" />
                          Due: {moment.utc(assignment.due_date)
                              .local()
                              .format('MMM DD, YYYY, hh:mm A')}


                        </span>
                        <span className="d-flex align-items-center text-muted">
                          <Icon icon="mdi:help-circle-outline" className="me-2" width="20" height="20" />
                          Questions: {assignment.total_questions || 0}
                        </span>
                        <span className={`badge ${
                          assignment.status === "completed" ? 'bg-success-subtle text-success border border-success' :
                          assignment.status === "pending" ? 'bg-warning-subtle text-warning border border-warning' :
                          assignment.status === "overdue-pending" ? 'bg-danger-subtle text-danger border border-danger' :
                          assignment.status === "overdue-completed" ? 'bg-success-subtle text-success border border-success' : ''
                        } px-3 py-2`}>
                          <Icon icon={
                            assignment.status === "completed" ? 'mdi:check-circle' :
                            assignment.status === "pending" ? 'mdi:clock-alert' :
                            assignment.status === "overdue-pending" ? 'mdi:alert-circle' :
                            assignment.status === "overdue-completed" ? 'mdi:check-circle' : ''
                          } className="me-2" width="16" height="16" />
                          {assignment.status === "completed" ? 'COMPLETED' :
                           assignment.status === "pending" ? 'PENDING' :
                           assignment.status === "overdue-pending" ? 'OVERDUE' :
                           assignment.status === "overdue-completed" ? 'COMPLETED LATE' : ''}
                        </span>
                      </div>
                      <div className="progress mb-3" style={{ height: '6px' }}>
                        <div
                          className="progress-bar"
                          role="progressbar"
                          style={{
                            width: `${((assignment.attended_questions || 0) / (assignment.total_questions || 1)) * 100}%`,
                            backgroundColor: assignment.status === "completed" || assignment.status === "overdue-completed" ? 
                              'var(--bs-success)' : 
                              assignment.status === "pending" ? 'var(--bs-warning)' : 'var(--bs-danger)'
                          }}
                          aria-valuenow={((assignment.attended_questions || 0) / (assignment.total_questions || 1)) * 100}
                          aria-valuemin="0"
                          aria-valuemax="100"
                        />
                      </div>
                      <div className="d-flex flex-column flex-md-row justify-content-between align-items-md-center gap-2">
                        <div className={`small d-flex align-items-center ${
                          assignment.status === "completed" ? 'text-success' :
                          assignment.status === "pending" ? 'text-warning' :
                          assignment.status === "overdue-pending" ? 'text-danger' :
                          assignment.status === "overdue-completed" ? 'text-success' : ''
                        }`}>
                          <Icon icon={
                            assignment.status === "completed" ? 'mdi:check-circle' :
                            assignment.status === "pending" ? 'mdi:clock-alert' :
                            assignment.status === "overdue-pending" ? 'mdi:alert-circle' :
                            assignment.status === "overdue-completed" ? 'mdi:check-circle' : ''
                          } className="me-2" width="20" height="20" />
                          {assignment.status === "completed" && 'Assignment completed successfully'}
                          {assignment.status === "pending" && 'Assignment is pending submission'}
                          {assignment.status === "overdue-pending" && 'Assignment is overdue and cannot be submitted'}
                          {assignment.status === "overdue-completed" && 'Assignment completed after due date'}
                        </div>
                        <button
                          className="btn btn-primary btn-sm d-flex align-items-center px-3"
                          style={{ width: 'fit-content' }}
                          onClick={() => handleViewAssignment(assignment)}
                        >
                          <Icon icon="mdi:eye" className="me-2" />
                          View
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))
          ) : !loading && (
            <div className="col-12">
            <div className="text-center py-5">
              <Icon icon="mdi:folder-open" className="text-muted mb-3" width="64" height="64" />
              <h5 className="text-muted">No Assignments Available</h5>
              <p className="text-muted">Assignments will appear here when they are added by the instructor.</p>
            </div>
          </div>
          )}
          {loading && (
            <div className="text-center p-3">
              <div className="spinner-border text-primary" role="status">
                <span className="visually-hidden">Loading...</span>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export default Assignments;