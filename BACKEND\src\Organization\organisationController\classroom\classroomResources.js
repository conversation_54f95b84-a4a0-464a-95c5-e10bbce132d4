const { mysqlServerConnection } = require("../../../db/db");
const {
  uploadDocToS3,
  uploadVideoToS3,
  uploadImageToS3,
  uploadAudioToS3,
} = require("../../../tools/aws");

// Create classroom resource
const createClassroomResource = async (req, res) => {
  console.log("🚀 [CREATE CLASSROOM RESOURCE] API called");
  console.log("📥 Request body:", req.body);
  console.log("📁 File:", req.file ? {
    originalname: req.file.originalname,
    mimetype: req.file.mimetype,
    size: req.file.size
  } : "No file");
  console.log("👤 User:", {
    userId: req.user.userId,
    dbName: req.user.db_name
  });

  try {
    const {
      classroom_id,
      resource_name,
      resource_description,
      resource_type,
      url, // For URL type resources
    } = req.body;

    const file = req.file; // For file uploads
    const dbName = req.user.db_name;
    const userId = req.user.userId;

    console.log("🔍 [CREATE] Validating input data:", {
      classroom_id,
      resource_name,
      resource_type,
      hasFile: !!file,
      hasUrl: !!url,
      dbName,
      userId
    });

    // Validate required fields
    if (!classroom_id || !resource_name || !resource_type) {
      console.warn("❌ [CREATE] Missing required fields");
      return res.status(400).json({
        success: false,
        message: "classroom_id, resource_name, and resource_type are required",
      });
    }

    // Validate resource_type
    const validTypes = ['text', 'image', 'audio', 'video', 'url', 'document'];
    if (!validTypes.includes(resource_type)) {
      console.warn("❌ [CREATE] Invalid resource_type:", resource_type);
      return res.status(400).json({
        success: false,
        message: `Invalid resource_type. Must be one of: ${validTypes.join(', ')}`,
      });
    }

    console.log("✅ [CREATE] Input validation passed");

    let media_url = null;

    // Handle different resource types
    switch (resource_type) {
      case 'text':
        console.log("📝 [CREATE] Processing text resource type");
        // For text type, no file upload needed
        media_url = null;
        break;

      case 'url':
        console.log("🔗 [CREATE] Processing URL resource type");
        // For URL type, use the provided URL
        if (!url) {
          console.warn("❌ [CREATE] URL required but not provided");
          return res.status(400).json({
            success: false,
            message: "URL is required for resource_type 'url'",
          });
        }
        media_url = url;
        console.log("✅ [CREATE] URL set:", media_url);
        break;

      case 'image':
      case 'audio':
      case 'video':
      case 'document':
        console.log(`📁 [CREATE] Processing ${resource_type} resource type`);
        // For file types, validate file upload
        if (!file) {
          console.warn(`❌ [CREATE] File upload required for ${resource_type} but no file provided`);
          return res.status(400).json({
            success: false,
            message: `File upload is required for resource_type '${resource_type}'`,
          });
        }

        console.log(`📤 [CREATE] Uploading ${resource_type} file to S3:`, {
          filename: file.originalname,
          size: file.size,
          mimetype: file.mimetype
        });

        // Upload file to S3 based on type
        try {
          let uploadResult;
          
          switch (resource_type) {
            case 'image':
              console.log("🖼️ [CREATE] Uploading image to S3");
              uploadResult = await uploadImageToS3(file.buffer);
              break;
            case 'audio':
              console.log("🎵 [CREATE] Uploading audio to S3");
              uploadResult = await uploadAudioToS3(file.buffer, file.originalname);
              break;
            case 'video':
              console.log("🎬 [CREATE] Uploading video to S3");
              uploadResult = await uploadVideoToS3(file.buffer, file.originalname);
              break;
            case 'document':
              console.log("📄 [CREATE] Uploading document to S3");
              uploadResult = await uploadDocToS3(file.buffer, file.originalname);
              break;
          }

          if (uploadResult && uploadResult.path) {
            media_url = uploadResult.path;
            console.log(`✅ [CREATE] File uploaded to S3 successfully: ${media_url}`);
          } else {
            console.error("❌ [CREATE] Upload failed - no path returned");
            throw new Error('Upload failed - no path returned');
          }
        } catch (uploadError) {
          console.error('❌ [CREATE] S3 upload error:', uploadError);
          return res.status(500).json({
            success: false,
            message: 'Failed to upload file to S3',
            error: uploadError.message,
          });
        }
        break;

      default:
        console.warn("❌ [CREATE] Unsupported resource_type:", resource_type);
        return res.status(400).json({
          success: false,
          message: `Unsupported resource_type: ${resource_type}`,
        });
    }

    console.log("💾 [CREATE] Inserting into database with data:", {
      classroom_id,
      userId,
      resource_name,
      resource_description,
      resource_type,
      media_url
    });

    // Insert into database
    const [result] = await mysqlServerConnection.query(
      `INSERT INTO ${dbName}.classroom_resources (
        classroom_id,
        updated_by,
        resource_name,
        resource_description,
        resource_type,
        media_url,
        is_active,
        is_deleted,
        seen,
        created_at,
        updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)`,
      [
        classroom_id,
        userId,
        resource_name,
        resource_description || null,
        resource_type,
        media_url,
        1, // is_active
        0, // is_deleted
        0, // seen
      ]
    );

    const resourceId = result.insertId;
    console.log("✅ [CREATE] Database insert successful, resource ID:", resourceId);

    // Fetch the created resource
    const [createdResource] = await mysqlServerConnection.query(
      `SELECT * FROM ${dbName}.classroom_resources WHERE id = ?`,
      [resourceId]
    );

    console.log("✅ [CREATE] Classroom resource created successfully:", {
      resourceId,
      resourceName: createdResource[0].resource_name,
      resourceType: createdResource[0].resource_type
    });

    return res.status(201).json({
      success: true,
      message: "Classroom resource created successfully",
      data: createdResource[0],
    });

  } catch (error) {
    console.error("❌ [CREATE] Error creating classroom resource:", error);
    console.error("❌ [CREATE] Error stack:", error.stack);
    return res.status(500).json({
      success: false,
      message: "Internal server error",
      error: error.message,
    });
  }
};

// Get all classroom resources
const getClassroomResources = async (req, res) => {
  console.log("🚀 [GET CLASSROOM RESOURCES] API called");
  console.log("📥 Request body:", req.body);
  console.log("👤 User:", {
    userId: req.user.userId,
    dbName: req.user.db_name
  });

  try {
    const { classroom_id } = req.body;
    const { page = 1, limit = 10, resource_type } = req.body;
    const dbName = req.user.db_name;

    console.log("🔍 [GET] Input parameters:", {
      classroom_id,
      page,
      limit,
      resource_type,
      dbName
    });

    if (!classroom_id) {
      console.warn("❌ [GET] classroom_id is required but not provided");
      return res.status(400).json({
        success: false,
        message: "classroom_id is required",
      });
    }

    const offset = (page - 1) * limit;
    console.log("📊 [GET] Pagination calculated:", { page, limit, offset });

    // Build query with optional filters
    let query = `
      SELECT * FROM ${dbName}.classroom_resources 
      WHERE classroom_id = ? AND is_deleted = 0
    `;
    let queryParams = [classroom_id];

    // Add resource_type filter if provided
    if (resource_type) {
      query += ` AND resource_type = ?`;
      queryParams.push(resource_type);
      console.log("🔍 [GET] Added resource_type filter:", resource_type);
    }

    // Add ordering and pagination
    query += ` ORDER BY created_at DESC LIMIT ? OFFSET ?`;
    queryParams.push(parseInt(limit), offset);

    console.log("🔍 [GET] Executing query:", query);
    console.log("🔍 [GET] Query parameters:", queryParams);

    const [resources] = await mysqlServerConnection.query(query, queryParams);

    console.log("✅ [GET] Query executed successfully, found resources:", resources.length);

    // Get total count for pagination
    let countQuery = `
      SELECT COUNT(*) as total FROM ${dbName}.classroom_resources 
      WHERE classroom_id = ? AND is_deleted = 0
    `;
    let countParams = [classroom_id];

    if (resource_type) {
      countQuery += ` AND resource_type = ?`;
      countParams.push(resource_type);
    }

    console.log("🔍 [GET] Executing count query:", countQuery);
    console.log("🔍 [GET] Count query parameters:", countParams);

    const [countResult] = await mysqlServerConnection.query(countQuery, countParams);
    const totalCount = countResult[0].total;
    const totalPages = Math.ceil(totalCount / limit);

    console.log("📊 [GET] Pagination info:", {
      totalCount,
      totalPages,
      currentPage: parseInt(page),
      limit: parseInt(limit)
    });

    console.log("✅ [GET] Classroom resources fetched successfully");

    return res.status(200).json({
      success: true,
      message: "Classroom resources fetched successfully",
      data: {
        resources,
        pagination: {
          currentPage: parseInt(page),
          limit: parseInt(limit),
          totalCount,
          totalPages,
        },
      },
    });

  } catch (error) {
    console.error("❌ [GET] Error fetching classroom resources:", error);
    console.error("❌ [GET] Error stack:", error.stack);
    return res.status(500).json({
      success: false,
      message: "Internal server error",
      error: error.message,
    });
  }
};


// Update classroom resource
const updateClassroomResource = async (req, res) => {
  console.log("🚀 [UPDATE CLASSROOM RESOURCE] API called");
  console.log("📥 Request body:", req.body);
  console.log("📁 File:", req.file ? {
    originalname: req.file.originalname,
    mimetype: req.file.mimetype,
    size: req.file.size
  } : "No file");
  console.log("👤 User:", {
    userId: req.user.userId,
    dbName: req.user.db_name
  });

  try {
    const { resource_id } = req.body;
    const {
      resource_name,
      resource_description,
      resource_type,
      url,
    } = req.body;

    const file = req.file;
    const dbName = req.user.db_name;
    const userId = req.user.userId;

    console.log("🔍 [UPDATE] Input parameters:", {
      resource_id,
      resource_name,
      resource_description,
      resource_type,
      url,
      hasFile: !!file,
      dbName,
      userId
    });

    if (!resource_id) {
      console.warn("❌ [UPDATE] resource_id is required but not provided");
      return res.status(400).json({
        success: false,
        message: "resource_id is required",
      });
    }

    console.log("🔍 [UPDATE] Checking if resource exists...");

    // Check if resource exists
    const [existingResource] = await mysqlServerConnection.query(
      `SELECT * FROM ${dbName}.classroom_resources WHERE id = ? AND is_deleted = 0`,
      [resource_id]
    );

    if (existingResource.length === 0) {
      console.warn("❌ [UPDATE] Resource not found:", resource_id);
      return res.status(404).json({
        success: false,
        message: "Classroom resource not found",
      });
    }

    console.log("✅ [UPDATE] Resource found:", {
      id: existingResource[0].id,
      name: existingResource[0].resource_name,
      type: existingResource[0].resource_type
    });

    let media_url = existingResource[0].media_url;

    // Handle file upload if provided
    if (file) {
      console.log("📤 [UPDATE] Processing file upload...");
      try {
        let uploadResult;
        
        switch (resource_type || existingResource[0].resource_type) {
          case 'image':
            console.log("🖼️ [UPDATE] Uploading new image to S3");
            uploadResult = await uploadImageToS3(file.buffer);
            break;
          case 'audio':
            console.log("🎵 [UPDATE] Uploading new audio to S3");
            uploadResult = await uploadAudioToS3(file.buffer, file.originalname);
            break;
          case 'video':
            console.log("🎬 [UPDATE] Uploading new video to S3");
            uploadResult = await uploadVideoToS3(file.buffer, file.originalname);
            break;
          case 'document':
            console.log("📄 [UPDATE] Uploading new document to S3");
            uploadResult = await uploadDocToS3(file.buffer, file.originalname);
            break;
          default:
            console.warn("❌ [UPDATE] File upload not supported for resource type:", resource_type);
            return res.status(400).json({
              success: false,
              message: "File upload not supported for this resource type",
            });
        }

        if (uploadResult && uploadResult.path) {
          media_url = uploadResult.path;
          console.log(`✅ [UPDATE] New file uploaded to S3: ${media_url}`);
        } else {
          console.error("❌ [UPDATE] Upload failed - no path returned");
          throw new Error('Upload failed - no path returned');
        }
      } catch (uploadError) {
        console.error('❌ [UPDATE] S3 upload error:', uploadError);
        return res.status(500).json({
          success: false,
          message: 'Failed to upload file to S3',
          error: uploadError.message,
        });
      }
    } else if (resource_type === 'url' && url) {
      console.log("🔗 [UPDATE] Updating URL for URL type resource");
      media_url = url;
    }

    console.log("💾 [UPDATE] Updating database with data:", {
      resource_name: resource_name || existingResource[0].resource_name,
      resource_description: resource_description || existingResource[0].resource_description,
      resource_type: resource_type || existingResource[0].resource_type,
      media_url,
      userId
    });

    // Update database
    await mysqlServerConnection.query(
      `UPDATE ${dbName}.classroom_resources 
       SET resource_name = ?, 
           resource_description = ?, 
           resource_type = ?, 
           media_url = ?, 
           updated_by = ?,
           updated_at = CURRENT_TIMESTAMP
       WHERE id = ?`,
      [
        resource_name || existingResource[0].resource_name,
        resource_description || existingResource[0].resource_description,
        resource_type || existingResource[0].resource_type,
        media_url,
        userId,
        resource_id,
      ]
    );

    console.log("✅ [UPDATE] Database update successful");

    // Fetch updated resource
    const [updatedResource] = await mysqlServerConnection.query(
      `SELECT * FROM ${dbName}.classroom_resources WHERE id = ?`,
      [resource_id]
    );

    console.log("✅ [UPDATE] Classroom resource updated successfully:", {
      resourceId: resource_id,
      resourceName: updatedResource[0].resource_name,
      resourceType: updatedResource[0].resource_type
    });

    return res.status(200).json({
      success: true,
      message: "Classroom resource updated successfully",
      data: updatedResource[0],
    });

  } catch (error) {
    console.error("❌ [UPDATE] Error updating classroom resource:", error);
    console.error("❌ [UPDATE] Error stack:", error.stack);
    return res.status(500).json({
      success: false,
      message: "Internal server error",
      error: error.message,
    });
  }
};

// Delete classroom resource (soft delete)
const deleteClassroomResource = async (req, res) => {
  console.log("🚀 [DELETE CLASSROOM RESOURCE] API called");
  console.log("📥 Request body:", req.body);
  console.log("👤 User:", {
    userId: req.user.userId,
    dbName: req.user.db_name
  });

  try {
    const { resource_id } = req.body;
    const dbName = req.user.db_name;

    console.log("🔍 [DELETE] Input parameters:", {
      resource_id,
      dbName
    });

    if (!resource_id) {
      console.warn("❌ [DELETE] resource_id is required but not provided");
      return res.status(400).json({
        success: false,
        message: "resource_id is required",
      });
    }

    console.log("🔍 [DELETE] Checking if resource exists...");

    // Check if resource exists
    const [existingResource] = await mysqlServerConnection.query(
      `SELECT * FROM ${dbName}.classroom_resources WHERE id = ? AND is_deleted = 0`,
      [resource_id]
    );

    if (existingResource.length === 0) {
      console.warn("❌ [DELETE] Resource not found:", resource_id);
      return res.status(404).json({
        success: false,
        message: "Classroom resource not found",
      });
    }

    console.log("✅ [DELETE] Resource found:", {
      id: existingResource[0].id,
      name: existingResource[0].resource_name,
      type: existingResource[0].resource_type
    });

    console.log("🗑️ [DELETE] Performing soft delete...");

    // Soft delete
    await mysqlServerConnection.query(
      `UPDATE ${dbName}.classroom_resources 
       SET is_deleted = 1, updated_at = CURRENT_TIMESTAMP 
       WHERE id = ?`,
      [resource_id]
    );

    console.log("✅ [DELETE] Classroom resource deleted successfully:", resource_id);

    return res.status(200).json({
      success: true,
      message: "Classroom resource deleted successfully",
    });

  } catch (error) {
    console.error("❌ [DELETE] Error deleting classroom resource:", error);
    console.error("❌ [DELETE] Error stack:", error.stack);
    return res.status(500).json({
      success: false,
      message: "Internal server error",
      error: error.message,
    });
  }
};

// Toggle resource active status
const toggleResourceStatus = async (req, res) => {
  console.log("🚀 [TOGGLE RESOURCE STATUS] API called");
  console.log("📥 Request body:", req.body);
  console.log("👤 User:", {
    userId: req.user.userId,
    dbName: req.user.db_name
  });

  try {
    const { resource_id } = req.body;
    const { is_active } = req.body;
    const dbName = req.user.db_name;

    console.log("🔍 [TOGGLE] Input parameters:", {
      resource_id,
      is_active,
      dbName
    });

    if (!resource_id) {
      console.warn("❌ [TOGGLE] resource_id is required but not provided");
      return res.status(400).json({
        success: false,
        message: "resource_id is required",
      });
    }

    if (typeof is_active !== 'boolean') {
      console.warn("❌ [TOGGLE] is_active must be boolean, received:", typeof is_active);
      return res.status(400).json({
        success: false,
        message: "is_active must be a boolean value",
      });
    }

    console.log("🔍 [TOGGLE] Checking if resource exists...");

    // Check if resource exists
    const [existingResource] = await mysqlServerConnection.query(
      `SELECT * FROM ${dbName}.classroom_resources WHERE id = ? AND is_deleted = 0`,
      [resource_id]
    );

    if (existingResource.length === 0) {
      console.warn("❌ [TOGGLE] Resource not found:", resource_id);
      return res.status(404).json({
        success: false,
        message: "Classroom resource not found",
      });
    }

    console.log("✅ [TOGGLE] Resource found:", {
      id: existingResource[0].id,
      name: existingResource[0].resource_name,
      currentStatus: existingResource[0].is_active,
      newStatus: is_active
    });

    console.log("🔄 [TOGGLE] Updating status...");

    // Update status
    await mysqlServerConnection.query(
      `UPDATE ${dbName}.classroom_resources 
       SET is_active = ?, updated_at = CURRENT_TIMESTAMP 
       WHERE id = ?`,
      [is_active ? 1 : 0, resource_id]
    );

    console.log(`✅ [TOGGLE] Classroom resource ${is_active ? 'activated' : 'deactivated'} successfully:`, resource_id);

    return res.status(200).json({
      success: true,
      message: `Classroom resource ${is_active ? 'activated' : 'deactivated'} successfully`,
    });

  } catch (error) {
    console.error("❌ [TOGGLE] Error toggling resource status:", error);
    console.error("❌ [TOGGLE] Error stack:", error.stack);
    return res.status(500).json({
      success: false,
      message: "Internal server error",
      error: error.message,
    });
  }
};


module.exports = {
  createClassroomResource,
  getClassroomResources,
  updateClassroomResource,
  deleteClassroomResource,
  toggleResourceStatus,
};