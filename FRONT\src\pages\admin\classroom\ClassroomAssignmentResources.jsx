import React, { useState, useEffect, useCallback } from 'react'
import { Icon } from '@iconify/react';
import { Modal } from 'react-bootstrap';
import { toast } from 'react-toastify';
import Loader from '../../../components/common/Loader';
import Nodata from '../../../components/common/NoData';
import './Classroom.css';
import { usePermissions } from '../../../context/PermissionsContext';
import {
  getClassroomAssignmentQuestions,
  addClassroomAssignmentQuestion,
  updateClassroomAssignmentQuestion,
  deleteClassroomAssignmentQuestion
 } from '../../../services/adminService';

function ClassroomAssignmentResources({decodedClassroomId, decodedAssignmentId}) {
  const { permissions } = usePermissions();
  console.log('decodedClassroomId',decodedClassroomId);
  console.log('decodedAssignmentId',decodedAssignmentId);

  // State for questions/resources
  const [questions, setQuestions] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [totalQuestions, setTotalQuestions] = useState(0);

  // Pagination states
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(10);
  const [totalPages, setTotalPages] = useState(1);
  const [searchTerm, setSearchTerm] = useState('');

  // Modal states
  const [showModal, setShowModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [questionToDelete, setQuestionToDelete] = useState(null);
  const [isDeleting, setIsDeleting] = useState(false);

  // Edit mode states
  const [isEditMode, setIsEditMode] = useState(false);
  const [questionToEdit, setQuestionToEdit] = useState(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Form data
  const [formData, setFormData] = useState({
    questionText: '',
    questionType: 'text',
    responseType: 'text_response',
    mediaFile: null,
    marks: 0,
    existingMediaUrl: null,
    removeMedia: false
  });
  const [formErrors, setFormErrors] = useState({});

  // Media preview state
  const [mediaPreview, setMediaPreview] = useState(null);
  const [showMediaPreview, setShowMediaPreview] = useState(false);

  // Fetch questions function
  const fetchQuestions = useCallback(async () => {
    setIsLoading(true);
    try {
      const response = await getClassroomAssignmentQuestions({
        assignment_id: decodedAssignmentId,
        class_id: decodedClassroomId,
        page: page,
        limit: limit,
        search: searchTerm
      });

      console.log("Get questions response:", response);

      if (response && response.success) {
        setQuestions(response.data.questions || []);
        setTotalQuestions(response.data.pagination?.totalRecords || 0);
        setTotalPages(response.data.pagination?.totalPages || 1);
      } else {
        toast.error(response?.message || 'Failed to fetch questions');
      }
    } catch (error) {
      console.error('Error fetching questions:', error);
      toast.error('An error occurred while fetching questions');
    } finally {
      setIsLoading(false);
    }
  }, [decodedAssignmentId, decodedClassroomId, page, limit, searchTerm]);

  // Fetch questions on component mount
  useEffect(() => {
    if (decodedAssignmentId && decodedClassroomId) {
      fetchQuestions();
    }
  }, [decodedAssignmentId, decodedClassroomId, fetchQuestions]);

  // Handle form input changes
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Clear error when user types
    if (formErrors[name]) {
      setFormErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }

    // Reset media when question type changes to text
    if (name === 'questionType' && value === 'text') {
      setMediaPreview(null);
      setShowMediaPreview(false);
      setFormData(prev => ({
        ...prev,
        mediaFile: null,
        existingMediaUrl: null,
        removeMedia: false
      }));
    }
  };

  // Handle media file changes
  const handleMediaChange = (e) => {
    const file = e.target.files[0];
    if (!file) return;

    setFormData(prev => ({
      ...prev,
      mediaFile: file,
      removeMedia: false
    }));

    // Create preview
    const fileType = file.type.split('/')[0];
    const reader = new FileReader();

    reader.onloadend = () => {
      setMediaPreview({
        type: fileType,
        url: reader.result
      });
      setShowMediaPreview(true);
    };

    if (file) {
      reader.readAsDataURL(file);
    }
  };

  // Handle removing media
  const handleRemoveMedia = () => {
    if (isEditMode && formData.existingMediaUrl) {
      setFormData(prev => ({
        ...prev,
        removeMedia: true,
        mediaFile: null
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        mediaFile: null
      }));
    }

    setMediaPreview(null);
    setShowMediaPreview(false);
  };

  // Validate form
  const validateForm = () => {
    const errors = {};

    if (!formData.questionText.trim()) {
      errors.questionText = 'Question text is required';
    }

    if (!formData.questionType) {
      errors.questionType = 'Question type is required';
    }

    if (!formData.responseType) {
      errors.responseType = 'Response type is required';
    }

    // Check if media file is required and provided
    if (formData.questionType === 'image' || formData.questionType === 'audio') {
      const hasExistingMedia = isEditMode && formData.existingMediaUrl && !formData.removeMedia;
      if (!hasExistingMedia && !formData.mediaFile) {
        errors.mediaFile = `${formData.questionType === 'image' ? 'Image' : 'Audio'} file is required`;
      }
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      const firstError = Object.values(formErrors)[0];
      if (firstError) {
        toast.error(firstError);
      }
      return;
    }

    setIsSubmitting(true);

    try {
      const formDataToSend = new FormData();

      formDataToSend.append('class_id', decodedClassroomId);
      formDataToSend.append('assignment_id', decodedAssignmentId);
      formDataToSend.append('question_type', formData.questionType);
      formDataToSend.append('question_text', formData.questionText);
      formDataToSend.append('response_type', formData.responseType);
      formDataToSend.append('marks', Number(formData.marks || 0));

      if (isEditMode && questionToEdit) {
        formDataToSend.append('question_id', questionToEdit.id);
      }

      // Handle media
      if (formData.mediaFile) {
        formDataToSend.append('media', formData.mediaFile);
      } else if (isEditMode && formData.existingMediaUrl && !formData.removeMedia) {
        formDataToSend.append('existing_media_url', formData.existingMediaUrl);
      } else if (isEditMode && formData.removeMedia) {
        formDataToSend.append('remove_media', 'true');
      }

      let response;
      if (isEditMode && questionToEdit) {
        response = await updateClassroomAssignmentQuestion(formDataToSend);
      } else {
        response = await addClassroomAssignmentQuestion(formDataToSend);
      }

      if (response && response.success) {
        toast.success(isEditMode ? 'Question updated successfully' : 'Question added successfully');
        handleModalClose();
        fetchQuestions();
      } else {
        toast.error(response?.message || `Failed to ${isEditMode ? 'update' : 'add'} question`);
      }
    } catch (error) {
      console.error(`Error ${isEditMode ? 'updating' : 'adding'} question:`, error);
      toast.error(`An error occurred while ${isEditMode ? 'updating' : 'adding'} the question`);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Reset form
  const resetForm = () => {
    setFormData({
      questionText: '',
      questionType: 'text',
      responseType: 'text_response',
      mediaFile: null,
      marks: 0,
      existingMediaUrl: null,
      removeMedia: false
    });
    setFormErrors({});
    setIsEditMode(false);
    setQuestionToEdit(null);
    setMediaPreview(null);
    setShowMediaPreview(false);
  };

  // Handle modal close
  const handleModalClose = () => {
    setShowModal(false);
    resetForm();
  };

  // Handle modal show
  const handleModalShow = () => {
    if (!permissions.classroom_assignment_resource_create) {
      toast.warning("You don't have permission to create resources");
      return;
    }
    resetForm();
    setShowModal(true);
  };

  // Handle edit question
  const handleEdit = (question) => {
    if (!permissions.classroom_assignment_resource_edit) {
      toast.warning("You don't have permission to edit resources");
      return;
    }
    setIsEditMode(true);
    setQuestionToEdit(question);

    setFormData({
      questionText: question.question_text || '',
      questionType: question.question_type || 'text',
      responseType: question.response_type || 'text_response',
      marks: question.marks || 0,
      mediaFile: null,
      existingMediaUrl: question.media_url || null,
      removeMedia: false
    });

    // Set media preview if there's a media URL
    if (question.media_url) {
      if (question.question_type === 'image') {
        setMediaPreview({
          type: 'image',
          url: question.media_url
        });
      } else if (question.question_type === 'audio') {
        setMediaPreview({
          type: 'audio',
          url: question.media_url
        });
      }
      setShowMediaPreview(true);
    } else {
      setShowMediaPreview(false);
      setMediaPreview(null);
    }

    setShowModal(true);
  };

  // Handle delete question
  const handleDelete = (question) => {
    if (!permissions.classroom_assignment_resource_delete) {
      toast.warning("You don't have permission to delete resources");
      return;
    }
    setQuestionToDelete(question);
    setShowDeleteModal(true);
  };

  // Confirm delete question
  const confirmDeleteQuestion = async () => {
    if (!questionToDelete) return;

    setIsDeleting(true);
    try {
      const response = await deleteClassroomAssignmentQuestion({
        assignment_id: decodedAssignmentId,
        class_id: decodedClassroomId,
        question_id: questionToDelete.id
      });

      if (response && response.success) {
        toast.success('Question deleted successfully');
        fetchQuestions();
      } else {
        toast.error(response?.message || 'Failed to delete question');
      }
    } catch (error) {
      console.error('Error deleting question:', error);
      toast.error('An error occurred while deleting the question');
    } finally {
      setIsDeleting(false);
      setShowDeleteModal(false);
      setQuestionToDelete(null);
    }
  };

  // Handle search
  const handleSearch = (e) => {
    setSearchTerm(e.target.value);
    setPage(1);
  };

  // Handle page change
  const handlePageChange = (newPage) => {
    if (newPage > 0 && newPage <= totalPages) {
      setPage(newPage);
    }
  };

  // Handle limit change
  const handleLimitChange = (e) => {
    setLimit(Number(e.target.value));
    setPage(1);
  };

  return (
    <>
      {/* add the search and add resources  */}
      <div className="row mb-3">
        <div className="col-12 col-md-6 mb-2">
          <input
            type="text"
            className="form-control"
            placeholder="Search Resource"
            style={{ width: '300px' }}
            value={searchTerm}
            onChange={handleSearch}
          />
        </div>
        <div className="col-12 col-md-6 d-flex justify-content-end mb-2">
          <button 
            className="btn btn-primary" 
            style={{ width: '300px' }} 
            onClick={handleModalShow}
            disabled={!permissions.classroom_assignment_resource_create}
            title={!permissions.classroom_assignment_resource_create ? "You don't have permission to create resources" : "Add Resource"}
          >
            <Icon icon="fluent:add-24-regular" width="20" height="20" className="me-2" />
            Add Resource
          </button>
        </div>
      </div>

      {/* Add Resource Modal */}
      <Modal show={showModal} onHide={handleModalClose} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>{isEditMode ? 'Edit Resource' : 'Add Resource'}</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <form onSubmit={handleSubmit}>
            <div className="mb-3">
              <label className="form-label">Question Type*</label>
              <select
                className={`form-select ${formErrors.questionType ? 'is-invalid' : ''}`}
                name="questionType"
                value={formData.questionType}
                onChange={handleInputChange}
                disabled={isEditMode}
              >
                <option value="">Select Question Type</option>
                <option value="text">Text Question</option>
                <option value="image">Image Question</option>
                <option value="audio">Audio Question</option>
              </select>
              {formErrors.questionType && (
                <div className="invalid-feedback">{formErrors.questionType}</div>
              )}
              {isEditMode && (
                <small className="text-muted">Question type cannot be changed when editing</small>
              )}
            </div>

            <div className="mb-3">
              <label className="form-label">Question Text*</label>
              <textarea
                className={`form-control ${formErrors.questionText ? 'is-invalid' : ''}`}
                rows="3"
                name="questionText"
                value={formData.questionText}
                onChange={handleInputChange}
                placeholder="Enter your question here"
              />
              {formErrors.questionText && (
                <div className="invalid-feedback">{formErrors.questionText}</div>
              )}
            </div>

            {formData.questionType !== 'text' && (
              <div className="mb-3">
                <label className="form-label">
                  {formData.questionType === 'image' ? 'Upload Image' : 'Upload Audio'}
                  {(isEditMode && formData.existingMediaUrl && !formData.removeMedia) ? '' : '*'}
                </label>

                {/* Show file input if no existing media or it's being removed */}
                {(!isEditMode || !formData.existingMediaUrl || formData.removeMedia) && (
                  <>
                    <input
                      type="file"
                      className={`form-control ${formErrors.mediaFile ? 'is-invalid' : ''}`}
                      onChange={handleMediaChange}
                      accept={formData.questionType === 'image' ? 'image/*' : 'audio/*'}
                    />
                    {formErrors.mediaFile && (
                      <div className="invalid-feedback">{formErrors.mediaFile}</div>
                    )}
                    <small className="text-muted">
                      {formData.questionType === 'image'
                        ? 'Supported formats: JPG, PNG, GIF (Max 5MB)'
                        : 'Supported formats: MP3, WAV (Max 10MB)'}
                    </small>
                  </>
                )}
              </div>
            )}

            {/* Media Preview */}
            {(showMediaPreview && (mediaPreview || formData.existingMediaUrl)) && (
              <div className="mb-3">
                {formData.questionType === 'image' ? (
                  <div className="border rounded p-2 bg-light">
                    <div className="d-flex justify-content-between align-items-center mb-2">
                      <span className="fw-light">Image Preview</span>
                      <button
                        type="button"
                        className="btn btn-outline-danger btn-sm"
                        onClick={handleRemoveMedia}
                      >
                        <Icon icon="fluent:delete-24-regular" className="me-1" />
                        Remove
                      </button>
                    </div>
                    <img
                      src={mediaPreview?.url || formData.existingMediaUrl}
                      alt="Question preview"
                      className="img-fluid rounded"
                      style={{ maxHeight: '200px' }}
                    />
                  </div>
                ) : (
                  <div className="d-flex align-items-center gap-3 p-2 bg-light rounded">
                    <audio
                      controls
                      className="flex-grow-1"
                      style={{ height: '40px', minWidth: '300px' }}
                    >
                      <source src={mediaPreview?.url || formData.existingMediaUrl} type="audio/mpeg" />
                      Your browser does not support the audio element.
                    </audio>
                    <button
                      type="button"
                      className="btn btn-outline-danger btn-sm"
                      onClick={handleRemoveMedia}
                      title="Remove audio"
                    >
                      <Icon icon="fluent:delete-24-regular" />
                    </button>
                  </div>
                )}
              </div>
            )}

            <div className="mb-3">
              <label className="form-label">Response Type*</label>
              <select
                className={`form-select ${formErrors.responseType ? 'is-invalid' : ''}`}
                name="responseType"
                value={formData.responseType}
                onChange={handleInputChange}
              >
                <option value="">Select Response Type</option>
                <option value="text_response">Text Response</option>
                <option value="image_response">Image Response</option>
                <option value="audio_response">Audio Response</option>
              </select>
              {formErrors.responseType && (
                <div className="invalid-feedback">{formErrors.responseType}</div>
              )}
              <small className="text-muted">
                Select the type of response you expect from trainees
              </small>
            </div>

            <div className="d-flex flex-column gap-2">
              <button
                type="button"
                className="btn w-100"
                style={{
                  backgroundColor: '#f8f9fa',
                  border: '1px solid #dee2e6',
                  color: '#6c757d',
                  padding: '12px'
                }}
                onClick={handleModalClose}
                disabled={isSubmitting}
              >
                Cancel
              </button>
              <button
                type="submit"
                className="btn btn-primary w-100"
                style={{ padding: '12px' }}
                disabled={isSubmitting}
              >
                {isSubmitting ? (
                  <>
                    <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                    {isEditMode ? 'Updating...' : 'Adding...'}
                  </>
                ) : (
                  isEditMode ? 'Update Resource' : 'Add Resource'
                )}
              </button>
            </div>
          </form>
        </Modal.Body>
      </Modal>

      {/* table  */}
      <div className="row mb-3">
        <div className="col-12">
          <div className="card">
            {isLoading ? (
              <div className="d-flex justify-content-center my-5">
                <Loader />
              </div>
            ) : questions.length === 0 ? (
              <div className="d-flex justify-content-center my-5">
                <Nodata message="No resources found" />
              </div>
            ) : (
              <div className="table-responsive">
                <table className="table table-borderless mb-0">
                  <thead>
                    <tr>
                      <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500', width: '80px' }}>SL. No</th>
                      <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500' }}>Resource</th>
                      <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500' }}>Resource Type</th>
                      <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500' }}>Response Type</th>
                      <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500', width: '120px' }}>Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {questions.map((question, index) => (
                      <tr key={question.id} className="border-bottom">
                        <td className="py-3 align-middle">{(page - 1) * limit + index + 1}</td>
                        <td className="py-3 align-middle">
                          <div className="text-truncate" style={{ maxWidth: '300px' }} title={question.question_text}>
                            {question.question_text}
                          </div>
                        </td>
                        <td className="py-3 align-middle">
                          {question.question_type === 'image' && question.media_url ? (
                            <div className="d-flex align-items-center">
                              <Icon icon="fluent:image-24-regular" className="text-primary me-2" />
                              <span>Image</span>
                      
                            </div>
                          ) : question.question_type === 'audio' && question.media_url ? (
                            <div className="d-flex align-items-center">
                              <Icon icon="fluent:speaker-2-24-regular" className="text-success me-2" />
                              <span>Audio</span>
                            </div>
                          ) : (
                            <div className="d-flex align-items-center">
                              <Icon icon="fluent:text-24-regular" className="text-secondary me-2" />
                              <span>Text</span>
                            </div>
                          )}
                        </td>
                        <td className="py-3 align-middle">
                          <span className="badge bg-info text-dark">
                            {question.response_type === 'text_response' ? 'Text' :
                             question.response_type === 'image_response' ? 'Image' :
                             question.response_type === 'audio_response' ? 'Audio' :
                             question.response_type}
                          </span>
                        </td>
                        <td className="py-3 align-middle">
                          <div className="d-flex gap-2">
                            <button
                              className="btn btn-outline-primary btn-sm border border-dark"
                              style={{ width: '36px', height: '36px' }}
                              title={!permissions.classroom_assignment_resource_edit ? "You don't have permission to edit resources" : "Edit Resource"}
                              onClick={() => handleEdit(question)}
                              disabled={!permissions.classroom_assignment_resource_edit}
                            >
                              <Icon icon="fluent:edit-24-regular" width="16" height="16" />
                            </button>
                            <button
                              className="btn btn-outline-danger btn-sm border border-dark"
                              style={{ width: '36px', height: '36px' }}
                              title={!permissions.classroom_assignment_resource_delete ? "You don't have permission to delete resources" : "Delete Resource"}
                              onClick={() => handleDelete(question)}
                              disabled={!permissions.classroom_assignment_resource_delete}
                            >
                              <Icon icon="fluent:delete-24-regular" width="16" height="16" />
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* record per page and pagination  */}
      {!isLoading && questions.length > 0 && (
        <div className="row">
          <div className="col-md-6 d-flex flex-row align-items-center">
            <select
              className="form-select"
              style={{ width: 'auto' }}
              value={limit}
              onChange={handleLimitChange}
            >
              <option value={5}>5 records per page</option>
              <option value={10}>10 records per page</option>
              <option value={25}>25 records per page</option>
              <option value={50}>50 records per page</option>
            </select>
            <small className="text-muted ms-2">
              Total {totalQuestions} records
            </small>
          </div>
          <div className="col-md-6 d-flex justify-content-end align-items-center gap-3">
            <button
              className="btn btn-primary"
              style={{ width: '150px' }}
              onClick={() => handlePageChange(page - 1)}
              disabled={page === 1}
            >
              Prev
            </button>
            <span>Page {page} of {totalPages}</span>
            <button
              className="btn btn-primary"
              style={{ width: '150px' }}
              onClick={() => handlePageChange(page + 1)}
              disabled={page === totalPages}
            >
              Next
            </button>
          </div>
        </div>
      )}

      {/* Delete Confirmation Modal */}
      {showDeleteModal && (
        <div className="modal show d-block fade" tabIndex="-1" style={{ backgroundColor: "rgba(0,0,0,0.5)" }}>
          <div className="modal-dialog">
            <div className="modal-content">
              <div className="modal-header">
                <h5 className="modal-title text-danger">Confirm Deletion</h5>
                <button
                  type="button"
                  className="btn-close"
                  onClick={() => {
                    setShowDeleteModal(false);
                    setQuestionToDelete(null);
                  }}
                ></button>
              </div>
              <div className="modal-body">
                <p>Are you sure you want to delete this resource?</p>
                {questionToDelete && (
                  <p className="mb-0"><strong>Resource:</strong> {questionToDelete.question_text}</p>
                )}
                <p className="text-danger mb-0 mt-2">This action cannot be undone.</p>
              </div>
              <div className="modal-footer">
                <div className="d-flex flex-column gap-2 w-100">
                  <button
                    className="btn w-100"
                    style={{
                      backgroundColor: '#f8f9fa',
                      border: '1px solid #dee2e6',
                      color: '#6c757d',
                      padding: '12px'
                    }}
                    onClick={() => {
                      setShowDeleteModal(false);
                      setQuestionToDelete(null);
                    }}
                    disabled={isDeleting}
                  >
                    Cancel
                  </button>
                  <button
                    className="btn btn-danger w-100"
                    style={{ padding: '12px' }}
                    onClick={confirmDeleteQuestion}
                    disabled={isDeleting}
                  >
                    {isDeleting ? (
                      <>
                        <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                        Deleting...
                      </>
                    ) : (
                      'Delete Resource'
                    )}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  )
}

export default ClassroomAssignmentResources
