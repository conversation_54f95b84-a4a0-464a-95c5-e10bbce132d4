import React, { useState, useEffect } from 'react';
import { Icon } from '@iconify/react';
import { getUserClassroomAssignmentsQuestions, submitUserClassroomAssignmentResponse } from '../../../services/userService';
import { decodeData } from '../../../utils/encodeAndEncode';
import { useParams, useSearchParams } from 'react-router-dom';

function AssignmentQuestions() {
    const [selectedFiles, setSelectedFiles] = useState({});
    const [answerTexts, setAnswerTexts] = useState({});
    const [questions, setQuestions] = useState([]);
    const [submittedQuestions, setSubmittedQuestions] = useState(new Set());
    const [dueDate, setDueDate] = useState(null);

    const { encodedAssignmentId } = useParams();
    const [searchParams] = useSearchParams();
    const encodedClassroomId = searchParams.get('classroomid');

    // Format due date
    const formatDueDate = (dueDate) => {
        if (!dueDate) return 'No due date';
        
        const date = new Date(dueDate);
        if (isNaN(date.getTime())) return 'Invalid date';
        
        return date.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    };

    // Check if due date has passed
    const checkDueDatePassed = (dueDate) => {
        if (!dueDate) return false;
        const date = new Date(dueDate);
        if (isNaN(date.getTime())) return false;
        return date < new Date();
    };

    const assignment_id = decodeData(decodeURIComponent(encodedAssignmentId));
    const classroom_id = encodedClassroomId ? decodeData(decodeURIComponent(encodedClassroomId)) : null;
    const user_id = JSON.parse(localStorage.getItem('user'))?.id;

    console.log("Assignment ID:", assignment_id);
    console.log("Classroom ID:", classroom_id);

    // Fetch Questions
    const fetchQuestions = async () => {
        try {
            const response = await getUserClassroomAssignmentsQuestions({
                assignment_id,
                classroom_id,
                user_id
            });

            console.log("Assignment Questions Response Data:", response);

            if (response.success) {
                setQuestions(response.data);
                setDueDate(response.due_date); // Store the due date
            }
            console.log("Questions Response Data:", response);
        } catch (error) {
            console.error("Error fetching questions:", error);
        }
    };

    useEffect(() => {
        if (assignment_id && classroom_id && user_id) {
            fetchQuestions();
        }
    }, [assignment_id, classroom_id, user_id]);

    // Handle File Upload
    const handleFileUpload = (event, type, questionId) => {
        if (submittedQuestions.has(questionId)) return;

        const file = event.target.files[0];
        if (file) {
            if (type === 'image' && !file.type.startsWith('image/')) {
                alert('Please upload an image file');
                return;
            }
            if (type === 'audio' && !file.type.startsWith('audio/')) {
                alert('Please upload an audio file');
                return;
            }
            setSelectedFiles(prev => ({
                ...prev,
                [questionId]: {
                    file,
                    preview: type === 'image' ? URL.createObjectURL(file) : null
                }
            }));
        }
    };

    const handleDeleteFile = (questionId) => {
        setSelectedFiles(prev => {
            const newState = { ...prev };
            if (newState[questionId]?.preview) {
                URL.revokeObjectURL(newState[questionId].preview);
            }
            delete newState[questionId];
            return newState;
        });
    };

    // Handle Text Change
    const handleTextChange = (value, questionId) => {
        const isDueDatePassed = checkDueDatePassed(dueDate);
        
        if (!submittedQuestions.has(questionId) && !isDueDatePassed) {
            setAnswerTexts(prev => ({
                ...prev,
                [questionId]: value
            }));
        }
    };

    const handleSubmitAnswer = async (question) => {
        if (submittedQuestions.has(question.id)) return;

        const formData = new FormData();
        formData.append('classroom_id', classroom_id);
        formData.append('assignment_id', assignment_id);
        formData.append('question_id', question.id);
        formData.append('user_id', user_id);
        formData.append('response_type', question.response_type);

        if (question.response_type === 'text_response') {
            formData.append('answer_text', answerTexts[question.id] || '');
        } else {
            const file = selectedFiles[question.id];
            if (file) {
                formData.append('media', file.file);
            }
        }

        try {
            const response = await submitUserClassroomAssignmentResponse(formData);
            console.log("Submit Response:", response);

            if (response.success) {
                // Mark question as submitted locally
                setSubmittedQuestions(prev => new Set([...prev, question.id]));

                // Clear the response after submission
                setAnswerTexts(prev => {
                    const newState = { ...prev };
                    delete newState[question.id];
                    return newState;
                });
                setSelectedFiles(prev => {
                    const newState = { ...prev };
                    delete newState[question.id];
                    return newState;
                });

                // Refresh questions data to get updated status
                await fetchQuestions();
            }
        } catch (error) {
            console.error("Error submitting answer:", error);
        }
    };

    const renderQuestionMedia = (question) => {
        switch (question.question_type) {
            case 'image':
                return (
                    <div className="mb-3">
                        <img
                            src={question.media_url}
                            alt="Question"
                            className="img-fluid rounded"
                            style={{ maxHeight: '300px' }}
                        />
                    </div>
                );
            case 'audio':
                return (
                    <div className="mb-3">
                        <audio controls className="w-100">
                            <source src={question.media_url} type="audio/mpeg" />
                            Your browser does not support the audio element.
                        </audio>
                    </div>
                );
            default:
                return null;
        }
    };

    const renderFilePreview = (question) => {
        const fileData = selectedFiles[question.id];
        if (!fileData) return null;

        return (
            <div className="mt-3 bg-light rounded p-2">
                {question.response_type === 'image_response' && fileData.preview && (
                    <>
                        <div className="d-flex align-items-center justify-content-between mb-2 ">
                            <span className="text-muted">Selected Image:</span>
                            <button
                                className="btn btn-sm btn-outline-danger d-flex align-items-center gap-1"
                                style={{
                                    width: 'auto',           // ✅ prevents full width
                                    padding: '4px 12px',
                                    fontSize: '13px',
                                    whiteSpace: 'nowrap',
                                }}
                                onClick={() => handleDeleteFile(question.id)}
                            >
                                <Icon icon="mdi:delete" width="18" height="18" />
                                Remove
                            </button>
                        </div>
                        <img
                            src={fileData.preview}
                            alt="Preview"
                            className="img-fluid rounded"
                            style={{ maxHeight: '200px' }}
                        />
                    </>
                )}
                {question.response_type === 'audio_response' && fileData.file && (
                    <div className="position-relative bg-light rounded p-3">
                        {/* Top-right aligned small Remove button */}
                        <div className="d-flex justify-content-end mb-2">
                            <button
                                className="btn btn-sm btn-danger d-flex align-items-center gap-1"
                                style={{
                                    width: 'auto',           // ✅ prevents full width
                                    padding: '4px 12px',
                                    fontSize: '13px',
                                    whiteSpace: 'nowrap',
                                }}
                                onClick={() => handleDeleteFile(question.id)}
                            >
                                <Icon icon="mdi:delete" width="14" height="14" />
                                Remove
                            </button>
                        </div>

                        {/* File Name */}
                        <div className="d-flex align-items-center mb-2">
                            <Icon icon="mdi:file-music-outline" className="text-primary me-2" width="20" height="20" />
                            <span className="text-muted text-truncate" style={{ maxWidth: '250px' }}>
                                {fileData.file.name}
                            </span>
                        </div>

                        {/* Audio Player */}
                        <audio controls className="w-100">
                            <source src={URL.createObjectURL(fileData.file)} type={fileData.file.type} />
                            Your browser does not support the audio element.
                        </audio>
                    </div>



                )}
            </div>
        );
    };

    const renderAnswerSection = (question) => {
        // Check if due date has passed
        const isDueDatePassed = checkDueDatePassed(dueDate);

        if (question.is_completed === 1 || submittedQuestions.has(question.id)) {
            return (
                <div className="p-3 bg-light rounded">
                    <div className="d-flex align-items-center mb-2">
                        <span className="badge border border-success text-success bg-success-subtle d-flex align-items-center gap-2 px-3 py-2">
                            <Icon icon="mdi:check-circle" width="14" height="14" />
                            Response Submitted
                        </span>
                    </div>
                    {question.answer_text && (
                        <p className="mb-0">{question.answer_text}</p>
                    )}
                    {question.submission_media_url && (
                        question.submitted_response_type === 'image_response' ? (
                            <img
                                src={question.submission_media_url}
                                alt="Submitted Answer"
                                className="img-fluid rounded mt-2"
                                style={{ maxHeight: '200px' }}
                            />
                        ) : (
                            <audio controls className="w-100 mt-2">
                                <source src={question.submission_media_url} type="audio/mpeg" />
                                Your browser does not support the audio element.
                            </audio>
                        )
                    )}
                </div>
            );
        }

        if (isDueDatePassed) {
            return (
                <div className="p-3 bg-light rounded">
                    <div className="d-flex align-items-center mb-2">
                        <span className="badge border border-danger text-danger bg-danger-subtle d-flex align-items-center gap-2 px-3 py-2">
                            <Icon icon="mdi:clock-alert" width="14" height="14" />
                            Submission Time Over
                        </span>
                    </div>
                    <p className="text-danger small mb-0">
                        You cannot submit answers after the due date. The submission deadline has passed.
                    </p>
                </div>
            );
        }

        return (
            <div className="answer-section">
                {question.response_type === 'text_response' && (
                    <div className="mb-3">
                        <textarea
                            className="form-control"
                            rows="3"
                            placeholder="Type your answer here..."
                            value={answerTexts[question.id] || ''}
                            onChange={(e) => handleTextChange(e.target.value, question.id)}
                            disabled={submittedQuestions.has(question.id) || isDueDatePassed}
                        ></textarea>
                    </div>
                )}

                {(question.response_type === 'image_response' || question.response_type === 'audio_response') && (
                    <div className="d-flex justify-content-start">
                        <div className="col-12 col-md-3">
                            <div className="d-flex gap-2 mb-3">
                                <button
                                    className="btn btn-outline-primary d-flex align-items-center gap-2"
                                    onClick={() => document.getElementById(`file-upload-${question.id}`).click()}
                                    disabled={submittedQuestions.has(question.id) || selectedFiles[question.id] || isDueDatePassed}
                                >
                                    <Icon
                                        icon={question.response_type === 'image_response' ? 'mdi:image' : 'mdi:microphone'}
                                        width="20"
                                        height="20"
                                    />
                                    Upload {question.response_type === 'image_response' ? 'Image' : 'Audio'}
                                    <input
                                        id={`file-upload-${question.id}`}
                                        type="file"
                                        accept={question.response_type === 'image_response' ? 'image/*' : 'audio/*'}
                                        onChange={(e) => handleFileUpload(e, question.response_type === 'image_response' ? 'image' : 'audio', question.id)}
                                        style={{ display: 'none' }}
                                        disabled={submittedQuestions.has(question.id) || selectedFiles[question.id] || isDueDatePassed}
                                    />
                                </button>
                            </div>
                        </div>
                    </div>
                )}

                {/* File Preview Section */}
                {renderFilePreview(question)}

                <div className="d-flex justify-content-end mt-3">
                    <div className="col-12 col-md-3">
                        <button
                            className="btn btn-primary btn-sm px-4 w-100 w-md-25"
                            onClick={() => handleSubmitAnswer(question)}
                            disabled={
                                submittedQuestions.has(question.id) ||
                                isDueDatePassed ||
                                (question.response_type !== 'text_response' && !selectedFiles[question.id]) ||
                                (question.response_type === 'text_response' && !answerTexts[question.id])
                            }
                        >
                            Submit Answer
                        </button>
                    </div>
                </div>
            </div>
        );
    };

    return (
        <div className="row">
            {/* Header with Question Stats */}
            <div className="d-flex flex-wrap flex-md-row justify-content-between align-items-center mb-4">
                <h4 className="mb-0">Assignment Questions</h4>
                <div className="d-flex gap-2 flex-wrap align-items-center">
                    <span className="badge border border-info text-info bg-info-subtle d-flex align-items-center gap-2 px-3 py-2">
                        <Icon icon="mdi:calendar" width="14" height="14" />
                        Due: {formatDueDate(dueDate)}
                    </span>
                    <span className="badge border border-primary text-primary bg-primary-subtle d-flex align-items-center gap-2 px-3 py-2">
                        <Icon icon="mdi:help-circle" width="14" height="14" />
                        {questions.length} Questions
                    </span>
                    <span className="badge border border-success text-success bg-success-subtle d-flex align-items-center gap-2 px-3 py-2">
                        <Icon icon="mdi:check-circle" width="14" height="14" />
                        {questions.filter(q => q.is_completed === 1).length} Submitted
                    </span>
                    <span className="badge border border-warning text-warning bg-warning-subtle d-flex align-items-center gap-2 px-3 py-2">
                        <Icon icon="mdi:clock" width="14" height="14" />
                        {questions.filter(q => q.is_completed === 0).length} Remaining
                    </span>
                </div>
            </div>

            {/* No Questions State */}
            {questions.length === 0 && (
                <div className="col-md-6 mx-auto">
                    <div className="card shadow-sm border-0">
                        <div className="card-body text-center p-5">
                            <div className="mb-4">
                                <Icon 
                                    icon="mdi:file-document-outline" 
                                    className="text-muted" 
                                    width="80" 
                                    height="80" 
                                />
                            </div>
                            <h4 className="text-muted mb-3">No Assignment Questions Available</h4>
                            <p className="text-muted mb-4">The instructor hasn't added any questions to this assignment yet.</p>
                            <button 
                                className="btn btn-primary d-flex align-items-center gap-2 mx-auto"
                                onClick={() => window.history.back()}
                            >
                                <Icon icon="mdi:arrow-left" width="20" height="20" />
                                Back to Assignment
                            </button>
                        </div>
                    </div>
                </div>
            )}

            {/* Questions List */}
            {questions.length > 0 && questions.map((question) => (
                <div key={question.id} className="card mb-4 border shadow-sm">
                    <div className="card-body">
                        <div className="d-flex justify-content-between align-items-start mb-4">
                            <h5 className="card-title mb-0">Question {question.question_order}</h5>
                            {question.is_completed === 1 && (
                                <span className={`badge d-flex align-items-center gap-2 px-3 py-2 ${
                                    question.answer_status === 'right' ? 'border border-success text-success bg-success-subtle' :
                                    question.answer_status === 'wrong' ? 'border border-danger text-danger bg-danger-subtle' :
                                    'border border-warning text-warning bg-warning-subtle'
                                }`}>
                                    <Icon 
                                        icon={
                                            question.answer_status === 'right' ? 'mdi:check-circle' :
                                            question.answer_status === 'wrong' ? 'mdi:close-circle' :
                                            'mdi:clock-outline'
                                        } 
                                        width="14" 
                                        height="14" 
                                    />
                                    {question.answer_status === 'right' ? 'Correct' :
                                     question.answer_status === 'wrong' ? 'Incorrect' :
                                     'Pending Review'}
                                </span>
                            )}
                        </div>

                        <div className="mb-4">
                            <p className="card-text">{question.question_text}</p>
                            {renderQuestionMedia(question)}
                        </div>

                        <div>
                            <h6 className="mb-3">Your Response</h6>
                            {renderAnswerSection(question)}
                        </div>
                    </div>
                </div>
            ))}
        </div>
    );
}

export default AssignmentQuestions;