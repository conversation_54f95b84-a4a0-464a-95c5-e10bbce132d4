import React, { useState, useEffect } from 'react'
import { Icon } from '@iconify/react';
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>Bar } from 'react-bootstrap';
import { toast } from 'react-toastify';
import NoData from '../../../components/common/NoData';
import { getAssessmentResultsInClassroom, getSpecificAssessmentResultInClassroom, getAssessmentAnalytics } from '../../../services/adminService';
import { usePermissions } from '../../../context/PermissionsContext';

function ClassroomAssessmentResult({ decodedClassroomId, decodedAssessmentId }) {
    const { permissions } = usePermissions();
    console.log('decodedClassroomId:', decodedClassroomId);
    console.log('decodedAssessmentId:', decodedAssessmentId);

    // Main results state
    const [results, setResults] = useState([]);
    const [allResults, setAllResults] = useState([]);
    const [groupedResults, setGroupedResults] = useState([]);
    const [isLoading, setIsLoading] = useState(false);

    // Search and pagination state
    const [searchTerm, setSearchTerm] = useState('');
    const [currentPage, setCurrentPage] = useState(1);
    const [itemsPerPage, setItemsPerPage] = useState(10);
    const [totalPages, setTotalPages] = useState(1);
    const [totalRecords, setTotalRecords] = useState(0);

    // Expandable rows state
    const [expandedRows, setExpandedRows] = useState({});

    // Details modal state
    const [showDetailsModal, setShowDetailsModal] = useState(false);
    const [selectedResult, setSelectedResult] = useState(null);
    const [resultDetails, setResultDetails] = useState(null);
    const [loadingDetails, setLoadingDetails] = useState(false);

    // Analytics state
    const [analyticsData, setAnalyticsData] = useState(null);
    const [showAnalytics, setShowAnalytics] = useState(true);
    const [loadingAnalytics, setLoadingAnalytics] = useState(false);

    // Fetch results on component mount
    useEffect(() => {
        if (decodedClassroomId && decodedAssessmentId) {
            fetchResults();
            fetchAnalyticsData();
        } else {
            toast.error('Classroom ID or Assessment ID is missing');
        }
    }, [decodedClassroomId, decodedAssessmentId, currentPage, itemsPerPage, searchTerm]);

    // Function to fetch assessment results
    const fetchResults = async () => {
        if (!decodedClassroomId || !decodedAssessmentId) {
            toast.error('Classroom ID or Assessment ID is missing');
            return;
        }

        setIsLoading(true);

        try {
            const payload = {
                class_id: decodedClassroomId,
                assessment_id: decodedAssessmentId,
                page: 1,
                limit: 100, // Fetch more results to ensure we get all attempts for each user
                search: searchTerm
            };

            console.log('Fetching assessment results with payload:', payload);
            const response = await getAssessmentResultsInClassroom(payload);
            console.log('Assessment results response:', response);

            if (response.success) {
                const allResultsData = response.data.results || [];
                setAllResults(allResultsData);

                // Group results by user_id
                const groupedByUser = {};

                allResultsData.forEach(result => {
                    if (!groupedByUser[result.user_id]) {
                        groupedByUser[result.user_id] = [];
                    }
                    groupedByUser[result.user_id].push(result);
                });

                // For each user, sort attempts by percentage (highest first) and take the best one
                const uniqueUserResults = Object.values(groupedByUser).map(userAttempts => {
                    // First sort by percentage (highest first)
                    const sortedByPercentage = userAttempts.sort((a, b) =>
                        parseFloat(b.percentage) - parseFloat(a.percentage)
                    );

                    // For the display in the main table, use the highest percentage attempt
                    const bestAttempt = sortedByPercentage[0];

                    // For the expanded view, sort by date (newest first)
                    const sortedByDate = [...userAttempts].sort((a, b) =>
                        new Date(b.attempt_date) - new Date(a.attempt_date)
                    );

                    // Return the best attempt along with all attempts for this user (sorted by date)
                    return {
                        ...bestAttempt,
                        allAttempts: sortedByDate
                    };
                });

                // Filter by search term if needed
                const filteredResults = searchTerm
                    ? uniqueUserResults.filter(result =>
                          result.username.toLowerCase().includes(searchTerm.toLowerCase())
                      )
                    : uniqueUserResults;

                // Calculate pagination for the grouped results
                const totalItems = filteredResults.length;
                const calculatedTotalPages = Math.ceil(totalItems / itemsPerPage);

                // Paginate the results
                const startIndex = (currentPage - 1) * itemsPerPage;
                const endIndex = startIndex + itemsPerPage;
                const paginatedResults = filteredResults.slice(startIndex, endIndex);

                setGroupedResults(paginatedResults);
                setResults(paginatedResults);
                setTotalPages(calculatedTotalPages || 1);
                setTotalRecords(totalItems || 0);
            } else {
                toast.error(response.message || 'Failed to fetch assessment results');
                setResults([]);
                setGroupedResults([]);
            }
        } catch (error) {
            console.error('Error fetching results:', error);
            toast.error('An error occurred while fetching assessment results');
            setResults([]);
            setGroupedResults([]);
        } finally {
            setIsLoading(false);
        }
    };

    // Function to fetch analytics data
    const fetchAnalyticsData = async () => {
        if (!decodedClassroomId || !decodedAssessmentId) {
            return;
        }

        if (!permissions.classroom_assessment_result_analytics) {
            setAnalyticsData(null);
            return;
        }

        setLoadingAnalytics(true);

        try {
            const payload = {
                class_id: decodedClassroomId,
                assessment_id: decodedAssessmentId
            };

            console.log('Fetching analytics with payload:', payload);
            const response = await getAssessmentAnalytics(payload);
            console.log('Analytics response:', response);

            if (response.success) {
                setAnalyticsData(response.data);
            } else {
                toast.error(response.message || 'Failed to load analytics data');
                setAnalyticsData(null);
            }
        } catch (error) {
            console.error('Error fetching analytics:', error);
            toast.error('Something went wrong while fetching analytics data');
            setAnalyticsData(null);
        } finally {
            setLoadingAnalytics(false);
        }
    };

    // Function to handle view result details
    const handleViewResult = async (result) => {
        setSelectedResult(result);
        setShowDetailsModal(true);
        setLoadingDetails(true);

        try {
            const payload = {
                results_id: result.id,
                user_id: result.user_id,
                class_id: decodedClassroomId,
                assessment_id: decodedAssessmentId
            };

            console.log('Fetching result details with payload:', payload);
            const response = await getSpecificAssessmentResultInClassroom(payload);
            console.log('Result details response:', response);

            if (response.success) {
                setResultDetails(response.data);
            } else {
                toast.error(response.message || 'Failed to load result details');
                setResultDetails(null);
            }
        } catch (error) {
            console.error('Error fetching result details:', error);
            toast.error('An error occurred while loading result details');
            setResultDetails(null);
        } finally {
            setLoadingDetails(false);
        }
    };

    // Function to close details modal
    const handleCloseDetailsModal = () => {
        setShowDetailsModal(false);
        setSelectedResult(null);
        setResultDetails(null);
    };

    // Function to toggle analytics visibility
    const handleToggleAnalytics = () => {
        setShowAnalytics(!showAnalytics);
    };

    // Function to toggle row expansion
    const toggleRowExpansion = (userId) => {
        setExpandedRows(prev => ({
            ...prev,
            [userId]: !prev[userId]
        }));
    };

    // Pagination handlers
    const handlePageChange = (newPage) => {
        if (newPage >= 1 && newPage <= totalPages) {
            setCurrentPage(newPage);
        }
    };

    const handleItemsPerPageChange = (e) => {
        const newItemsPerPage = parseInt(e.target.value);
        setItemsPerPage(newItemsPerPage);
        setCurrentPage(1);
    };

    const handleSearchChange = (e) => {
        setSearchTerm(e.target.value);
        setCurrentPage(1);
    };

    return (
        <>
            {/* Analytics Toggle and Statistics Cards */}
            {showAnalytics && permissions.classroom_assessment_result_analytics && (
                <div className="row mb-4">
                    <div className="col-12">
                        <div className="card">
                            <div className="card-header d-flex justify-content-between align-items-center">
                                <div className="d-flex align-items-center">
                                    <Icon icon="fluent:chart-multiple-24-regular" className="text-primary me-2" width="24" height="24" />
                                    <h5 className="mb-0">Assessment Analytics</h5>
                                </div>
                                <Icon 
                                    icon="fluent:eye-off-24-regular" 
                                    width="20" 
                                    height="20" 
                                    className="text-muted cursor-pointer"
                                    onClick={handleToggleAnalytics}
                                    style={{ cursor: 'pointer' }}
                                    title="Hide Analytics"
                                />
                            </div>
                            <div className="card-body">
                                {loadingAnalytics ? (
                                    <div className="text-center py-4">
                                        <Spinner animation="border" variant="primary" />
                                        <p className="mt-2">Loading analytics...</p>
                                    </div>
                                ) : analyticsData ? (
                                    <div className="row g-3">
                                        <div className="col-md-2">
                                            <div className="analytics-card h-100 p-3 bg-light rounded text-center">
                                                <Icon icon="fluent:people-24-regular" className="text-primary mb-2" width="32" height="32" />
                                                <div className="text-muted small">Total Trainees</div>
                                                <div className="h3 mb-0">{analyticsData.total_trainees || 0}</div>
                                            </div>
                                        </div>
                                        <div className="col-md-2">
                                            <div className="analytics-card h-100 p-3 bg-light rounded text-center">
                                                <Icon icon="fluent:checkmark-circle-24-regular" className="text-success mb-2" width="32" height="32" />
                                                <div className="text-muted small">Passed</div>
                                                <div className="h3 mb-0 text-success">{analyticsData.passed || 0}</div>
                                            </div>
                                        </div>
                                        <div className="col-md-2">
                                            <div className="analytics-card h-100 p-3 bg-light rounded text-center">
                                                <Icon icon="fluent:dismiss-circle-24-regular" className="text-danger mb-2" width="32" height="32" />
                                                <div className="text-muted small">Failed</div>
                                                <div className="h3 mb-0 text-danger">{analyticsData.failed || 0}</div>
                                            </div>
                                        </div>
                                        <div className="col-md-2">
                                            <div className="analytics-card h-100 p-3 bg-light rounded text-center">
                                                <Icon icon="fluent:clock-24-regular" className="text-secondary mb-2" width="32" height="32" />
                                                <div className="text-muted small">Not Attempted</div>
                                                <div className="h3 mb-0 text-secondary">{analyticsData.not_attempted || 0}</div>
                                            </div>
                                        </div>
                                        <div className="col-md-2">
                                            <div className="analytics-card h-100 p-3 bg-light rounded text-center">
                                                <Icon icon="fluent:data-trending-24-regular" className="text-info mb-2" width="32" height="32" />
                                                <div className="text-muted small">Pass Rate</div>
                                                <div className="h3 mb-0 text-info">
                                                    {analyticsData.total_trainees > 0
                                                        ? Math.round((analyticsData.passed / analyticsData.total_trainees) * 100)
                                                        : 0
                                                    }%
                                                </div>
                                                <ProgressBar
                                                    now={analyticsData.total_trainees > 0
                                                        ? Math.round((analyticsData.passed / analyticsData.total_trainees) * 100)
                                                        : 0
                                                    }
                                                    variant={
                                                        analyticsData.total_trainees > 0
                                                            ? Math.round((analyticsData.passed / analyticsData.total_trainees) * 100) >= 70
                                                                ? 'success'
                                                                : Math.round((analyticsData.passed / analyticsData.total_trainees) * 100) >= 40
                                                                    ? 'info'
                                                                    : 'danger'
                                                            : 'secondary'
                                                    }
                                                    style={{ height: '4px', marginTop: '8px' }}
                                                />
                                            </div>
                                        </div>
                                        <div className="col-md-2">
                                            <div className="analytics-card h-100 p-3 bg-light rounded text-center">
                                                <Icon icon="fluent:target-24-regular" className="text-warning mb-2" width="32" height="32" />
                                                <div className="text-muted small">Passing %</div>
                                                <div className="h3 mb-0 text-warning">{analyticsData.passing_percentage || 0}%</div>
                                            </div>
                                        </div>
                                    </div>
                                ) : (
                                    <div className="text-center py-4">
                                        <Icon icon="fluent:chart-multiple-24-regular" className="text-muted mb-2" width="48" height="48" />
                                        <p className="text-muted">No analytics data available</p>
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>
                </div>
            )}

            {/* Show Analytics Button when hidden */}
            {!showAnalytics && permissions.classroom_assessment_result_analytics && (
                <div className="row mb-3">
                    <div className="col-12 d-flex justify-content-end">
                        <Icon 
                            icon="fluent:chart-multiple-24-regular" 
                            width="24" 
                            height="24" 
                            className="text-primary cursor-pointer"
                            onClick={handleToggleAnalytics}
                            style={{ cursor: 'pointer' }}
                            title="Show Analytics"
                        />
                    </div>
                </div>
            )}

            {/* Search Section */}
            <div className="row mb-3">
                <div className="col-12 col-md-6 mb-1">
                    <input
                        type="text"
                        className="form-control"
                        placeholder="Search by trainee name"
                        style={{ width: '300px' }}
                        value={searchTerm}
                        onChange={handleSearchChange}
                    />
                </div>
                <div className="col-12 col-md-6 d-flex justify-content-end mb-1">
                    <div className="d-flex align-items-center gap-2">
                        <span className="text-muted">
                            Total Records: {totalRecords}
                        </span>
                    </div>
                </div>
            </div>

            {/* Results Table */}
            <div className="row mb-3">
                <div className="col-12">
                    <div className="card">
                        <div className="table-responsive">
                            <table className="table table-borderless mb-0">
                                <thead>
                                    <tr>
                                        <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500', width: '80px', textAlign: 'left', borderBottom: '2px solid #dee2e6' }}>SL No</th>
                                        <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500', width: '150px', textAlign: 'left', borderBottom: '2px solid #dee2e6' }}>Username</th>
                                        <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500', width: '100px', textAlign: 'left', borderBottom: '2px solid #dee2e6' }}>Total QA</th>
                                        <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500', width: '120px', textAlign: 'left', borderBottom: '2px solid #dee2e6' }}>Attempted QA</th>
                                        <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500', width: '100px', textAlign: 'left', borderBottom: '2px solid #dee2e6' }}>Correct QA</th>
                                        <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500', width: '100px', textAlign: 'left', borderBottom: '2px solid #dee2e6' }}>highest percentage</th>
                                        <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500', width: '150px', textAlign: 'left', borderBottom: '2px solid #dee2e6' }}>Passing %</th>
                                        <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500', width: '100px', textAlign: 'left', borderBottom: '2px solid #dee2e6' }}>Status</th>
                                        <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500', width: '120px', textAlign: 'center', borderBottom: '2px solid #dee2e6' }}>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {isLoading ? (
                                        <tr>
                                            <td colSpan="9" className="text-center py-5">
                                                <Spinner animation="border" variant="primary" />
                                                <p className="mt-2">Loading results...</p>
                                            </td>
                                        </tr>
                                    ) : results.length === 0 ? (
                                        <tr>
                                            <td colSpan="9" className="text-center py-5">
                                                <NoData caption="No assessment results available" />
                                            </td>
                                        </tr>
                                    ) : (
                                        results.map((result, index) => (
                                            <React.Fragment key={result.user_id || index}>
                                                {/* Main row showing the best attempt */}
                                                <tr
                                                    className="border-bottom"
                                                    style={{
                                                        cursor: result.allAttempts && result.allAttempts.length > 1 ? 'pointer' : 'default',
                                                        backgroundColor: expandedRows[result.user_id] ? '#f8f9fa' : 'transparent'
                                                    }}
                                                    onClick={() => {
                                                        if (result.allAttempts && result.allAttempts.length > 1) {
                                                            toggleRowExpansion(result.user_id);
                                                        }
                                                    }}
                                                >
                                                    <td className="py-3 align-middle">{(currentPage - 1) * itemsPerPage + index + 1}</td>
                                                    <td className="py-3 align-middle">
                                                        <div className="d-flex align-items-center">
                                                            {result.username}
                                                            {result.allAttempts && result.allAttempts.length > 1 && (
                                                                <span className="badge bg-info ms-2" style={{ fontSize: '0.7rem' }}>
                                                                    {result.allAttempts.length} attempts
                                                                </span>
                                                            )}
                                                        </div>
                                                    </td>
                                                    <td className="py-3 align-middle">{result.total_questions}</td>
                                                    <td className="py-3 align-middle">{result.questions_attempted}</td>
                                                    <td className="py-3 align-middle">{result.correct_questions}</td>
                                                    <td className="py-3 align-middle">{parseFloat(result.percentage) || 0}%</td>
                                                    <td className="py-3 align-middle">{result.passing_percentage}%</td>
                                                    <td className="py-3 align-middle">
                                                        <span className={`badge ${parseFloat(result.percentage) >= result.passing_percentage ? 'bg-success' : 'bg-danger'}`}>
                                                            {parseFloat(result.percentage) >= result.passing_percentage ? 'Pass' : 'Fail'}
                                                        </span>
                                                    </td>
                                                    <td className="py-3 align-middle text-center">
                                                        <div className="d-flex justify-content-center gap-2">
                                                            <button
                                                                className="btn btn-outline-dark btn-sm border border-dark"
                                                                style={{ width: '36px', height: '36px' }}
                                                                title="View Details"
                                                                onClick={(e) => {
                                                                    e.stopPropagation();
                                                                    handleViewResult(result);
                                                                }}
                                                            >
                                                                <Icon icon="fluent:eye-24-regular" width="16" height="16" />
                                                            </button>
                                                            {result.allAttempts && result.allAttempts.length > 1 && (
                                                                <button
                                                                    className="btn btn-outline-secondary btn-sm border border-dark"
                                                                    style={{ width: '36px', height: '36px' }}
                                                                    title={expandedRows[result.user_id] ? "Hide Attempts" : "Show All Attempts"}
                                                                    onClick={(e) => {
                                                                        e.stopPropagation();
                                                                        toggleRowExpansion(result.user_id);
                                                                    }}
                                                                >
                                                                    <Icon
                                                                        icon={expandedRows[result.user_id] ? "fluent:chevron-up-24-regular" : "fluent:chevron-down-24-regular"}
                                                                        width="16"
                                                                        height="16"
                                                                    />
                                                                </button>
                                                            )}
                                                        </div>
                                                    </td>
                                                </tr>

                                                {/* Expandable row for previous attempts */}
                                                {result.allAttempts && result.allAttempts.length > 1 && expandedRows[result.user_id] && (
                                                    <tr>
                                                        <td colSpan="9" className="p-0">
                                                            <div className="p-3 bg-light">
                                                                <h6 className="mb-3">All Attempts for {result.username}</h6>
                                                                <div className="table-responsive">
                                                                    <table className="table table-sm table-bordered mb-0">
                                                                        <thead>
                                                                            <tr>
                                                                                <th style={{ width: '10%' }}>Attempt #</th>
                                                                                <th style={{ width: '20%' }}>Date</th>
                                                                                <th style={{ width: '10%' }}>Total QA</th>
                                                                                <th style={{ width: '10%' }}>Attempted</th>
                                                                                <th style={{ width: '10%' }}>Correct</th>
                                                                                <th style={{ width: '10%' }}>Percentage</th>
                                                                                <th style={{ width: '10%' }}>Status</th>
                                                                                <th style={{ width: '10%' }}>Action</th>
                                                                            </tr>
                                                                        </thead>
                                                                        <tbody>
                                                                            {result.allAttempts.map((attempt, attemptIndex) => (
                                                                                <tr key={attempt.id || `${result.user_id}-${attemptIndex}`}>
                                                                                    <td>{attemptIndex + 1}</td>
                                                                                    <td>{new Date(attempt.attempt_date).toLocaleString()}</td>
                                                                                    <td>{attempt.total_questions}</td>
                                                                                    <td>{attempt.questions_attempted}</td>
                                                                                    <td>{attempt.correct_questions}</td>
                                                                                    <td>{parseFloat(attempt.percentage) || 0}%</td>
                                                                                    <td>
                                                                                        <span className={`badge ${parseFloat(attempt.percentage) >= result.passing_percentage ? 'bg-success' : 'bg-danger'}`}>
                                                                                            {parseFloat(attempt.percentage) >= result.passing_percentage ? 'Pass' : 'Fail'}
                                                                                        </span>
                                                                                    </td>
                                                                                    <td>
                                                                                        <button
                                                                                            className="btn btn-outline-dark btn-sm"
                                                                                            style={{ width: '32px', height: '32px' }}
                                                                                            title="View Details"
                                                                                            onClick={() => handleViewResult(attempt)}
                                                                                        >
                                                                                            <Icon icon="fluent:eye-24-regular" width="14" height="14" />
                                                                                        </button>
                                                                                    </td>
                                                                                </tr>
                                                                            ))}
                                                                        </tbody>
                                                                    </table>
                                                                </div>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                )}
                                            </React.Fragment>
                                        ))
                                    )}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            {/* Record per page and pagination  */}
            <div className="row">
                <div className="col-md-6 d-flex align-items-center gap-3">
                    <select
                        className="form-select"
                        style={{ width: 'auto' }}
                        value={itemsPerPage}
                        onChange={handleItemsPerPageChange}
                    >
                        <option value={5}>5 records per page</option>
                        <option value={10}>10 records per page</option>
                        <option value={25}>25 records per page</option>
                        <option value={50}>50 records per page</option>
                    </select>
                </div>
                <div className="col-md-6 d-flex justify-content-end align-items-center gap-3">
                    <button
                        className="btn btn-primary"
                        style={{ width: '150px' }}
                        onClick={() => handlePageChange(currentPage - 1)}
                        disabled={currentPage === 1 || isLoading}
                    >
                        Prev
                    </button>
                    <span>Page {currentPage} of {totalPages}</span>
                    <button
                        className="btn btn-primary"
                        style={{ width: '150px' }}
                        onClick={() => handlePageChange(currentPage + 1)}
                        disabled={currentPage >= totalPages || isLoading}
                    >
                        Next
                    </button>
                </div>
            </div>

            <style>
                {`
                    .wider-assessment-modal {
                        max-width: 95%;
                        width: 1400px;
                    }
                `}
            </style>

            {/* Assessment Result Details Modal */}
            <Modal
                show={showDetailsModal}
                onHide={handleCloseDetailsModal}
                size="xl"
                centered
                dialogClassName="wider-assessment-modal"
            >
                <Modal.Header closeButton className="border-bottom">
                    <Modal.Title>
                        <Icon icon="fluent:document-data-24-regular" className="me-2" />
                        Assessment Result Details
                    </Modal.Title>
                </Modal.Header>
                <Modal.Body className="p-0">
                    {loadingDetails ? (
                        <div className="text-center py-5">
                            <Spinner animation="border" variant="primary" />
                            <p className="mt-2">Loading result details...</p>
                        </div>
                    ) : resultDetails ? (
                        <div className="result-details">
                            {/* Summary Section */}
                            <div className="p-4">
                                <div className="card mb-4">
                                    <div className="card-header bg-light">
                                        <h5 className="mb-0 d-flex align-items-center">
                                            <Icon icon="fluent:person-24-regular" className="me-2" />
                                            Result Summary
                                        </h5>
                                    </div>
                                    <div className="card-body">
                                        <div className="row">
                                            <div className="col-md-6">
                                                <div className="d-flex justify-content-between mb-3">
                                                    <strong>Username:</strong>
                                                    <span>{selectedResult?.username}</span>
                                                </div>
                                                <div className="d-flex justify-content-between mb-3">
                                                    <strong>Total Questions:</strong>
                                                    <span>{resultDetails.summary?.total_questions}</span>
                                                </div>
                                                <div className="d-flex justify-content-between mb-3">
                                                    <strong>Attempted Questions:</strong>
                                                    <span>{resultDetails.summary?.total_attempted}</span>
                                                </div>
                                            </div>
                                            <div className="col-md-6">
                                                <div className="d-flex justify-content-between mb-3">
                                                    <strong>Correct Answers:</strong>
                                                    <span>{resultDetails.summary?.total_correct}</span>
                                                </div>
                                                <div className="d-flex justify-content-between mb-3">
                                                    <strong>Result Status:</strong>
                                                    <span className={`fw-bold ${
                                                        resultDetails.summary?.result_status === 'Pass' ? 'text-success' : 'text-danger'
                                                    }`}>
                                                        {resultDetails.summary?.result_status}
                                                    </span>
                                                </div>
                                                <div className="d-flex justify-content-between mb-3">
                                                    <strong>Attempt Date:</strong>
                                                    <span>{new Date(resultDetails.summary?.attempt_date).toLocaleString()}</span>
                                                </div>
                                            </div>
                                        </div>

                                        {/* Progress Bar */}
                                        <div className="mt-3">
                                            <div className="d-flex justify-content-between mb-2">
                                                <span><strong>Score Progress</strong></span>
                                                <span><strong>{resultDetails.summary?.percentage}%</strong></span>
                                            </div>
                                            <ProgressBar
                                                now={parseFloat(resultDetails.summary?.percentage) || 0}
                                                variant={
                                                    parseFloat(resultDetails.summary?.percentage) >= 70
                                                        ? 'success'
                                                        : parseFloat(resultDetails.summary?.percentage) >= 40
                                                        ? 'warning'
                                                        : 'danger'
                                                }
                                                style={{ height: '12px' }}
                                            />
                                        </div>
                                    </div>
                                </div>
                            </div>

                            {/* Questions Section */}
                            <div className="px-4 pb-4">
                                <div className="card">
                                    <div className="card-header bg-light">
                                        <h5 className="mb-0 d-flex align-items-center">
                                            <Icon icon="fluent:question-circle-24-regular" className="me-2" />
                                            Questions & Answers
                                        </h5>
                                    </div>
                                    <div className="card-body p-0">
                                        <div className="table-responsive">
                                            <table className="table table-hover mb-0">
                                                <thead style={{ backgroundColor: '#f8f9fa' }}>
                                                    <tr>
                                                        <th style={{ width: '8%' }}>SL No</th>
                                                        <th style={{ width: '40%' }}>Question</th>
                                                        <th style={{ width: '15%' }}>Status</th>
                                                        <th style={{ width: '18%' }}>Your Answer</th>
                                                        <th style={{ width: '19%' }}>Correct Answer</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    {resultDetails.questions?.map((question, index) => (
                                                        <tr key={question.question_id}>
                                                            <td className="py-3">{index + 1}</td>
                                                            <td className="py-3">{question.question_name}</td>
                                                            <td className="py-3">
                                                                {question.is_attempted ? (
                                                                    question.is_correct ? (
                                                                        <div className="d-flex align-items-center">
                                                                            <Icon icon="fluent:checkmark-circle-24-filled" className="text-success me-2" style={{ fontSize: '1.2rem' }} />
                                                                            <span className="text-success">Correct</span>
                                                                        </div>
                                                                    ) : (
                                                                        <div className="d-flex align-items-center">
                                                                            <Icon icon="fluent:dismiss-circle-24-filled" className="text-danger me-2" style={{ fontSize: '1.2rem' }} />
                                                                            <span className="text-danger">Incorrect</span>
                                                                        </div>
                                                                    )
                                                                ) : (
                                                                    <div className="d-flex align-items-center">
                                                                        <Icon icon="fluent:subtract-circle-24-regular" className="text-secondary me-2" style={{ fontSize: '1.2rem' }} />
                                                                        <span className="text-secondary">Not Attempted</span>
                                                                    </div>
                                                                )}
                                                            </td>
                                                            <td className="py-3">
                                                                {question.is_attempted ? (
                                                                    <span className={question.is_correct ? 'text-success' : 'text-danger'}>
                                                                        {question.user_selected_option?.option_value || 'N/A'}
                                                                    </span>
                                                                ) : (
                                                                    <span className="text-muted">Not answered</span>
                                                                )}
                                                            </td>
                                                            <td className="py-3">
                                                                <span className="text-success fw-bold">
                                                                    {question.correct_option?.option_value || 'N/A'}
                                                                </span>
                                                            </td>
                                                        </tr>
                                                    ))}
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    ) : (
                        <div className="text-center py-5">
                            <Icon icon="fluent:warning-24-regular" style={{ fontSize: '3rem', color: 'var(--bs-warning)' }} />
                            <p className="mt-2">No result details available</p>
                        </div>
                    )}
                </Modal.Body>
                <Modal.Footer className="border-top">
                    <Button variant="secondary" onClick={handleCloseDetailsModal}>
                        <Icon icon="fluent:dismiss-24-regular" className="me-2" />
                        Close
                    </Button>
                </Modal.Footer>
            </Modal>
        </>
    )
}

export default ClassroomAssessmentResult

