const { mysqlServerConnection } = require("../../../db/db");

const getPayments = async (req, res) => {
  console.log("📡 [API] getPayments called");

  try {
    const { page = 1, limit = 10, search = "", status = "" } = req.body;
    const dbName = req.user.db_name;
    const offset = (page - 1) * limit;

    console.log("🏢 Database:", dbName);
    console.log("📄 Page:", page, "Limit:", limit, "Search:", search, "Status:", status);

    // Build search condition
    let searchCondition = "";
    let queryParams = [];

    if (search && search.trim() !== "") {
      searchCondition = "AND (course_name LIKE ? OR payment_method LIKE ? OR payment_uid LIKE ?)";
      queryParams.push(`%${search}%`, `%${search}%`, `%${search}%`);
    }

    // Build status filter condition
    let statusCondition = "";
    if (status && status.trim() !== "" && status !== "all") {
      statusCondition = "AND status = ?";
      queryParams.push(status);
    }

    // Get total count for pagination
    const countQuery = `
      SELECT COUNT(*) as total
      FROM ${dbName}.payments
      WHERE 1=1 ${searchCondition} ${statusCondition}
    `;
    const [countResult] = await mysqlServerConnection.query(countQuery, queryParams);
    const totalRecords = countResult[0].total;

    // Get paginated data with user information
    const dataQuery = `
      SELECT
        p.id,
        p.user_id,
        p.amount,
        p.currency,
        p.status,
        p.payment_method,
        p.created_at,
        p.updated_at,
        p.payment_uid,
        p.course_name,
        p.discount_point,
        p.receipt_url,
        p.receipt_pdf_path,
        u.name as user_name,
        u.email as user_email,
        u.mobile as user_phone
      FROM ${dbName}.payments p
      LEFT JOIN ${dbName}.users u ON p.user_id = u.id
      WHERE 1=1 ${searchCondition} ${statusCondition}
      ORDER BY p.created_at DESC
      LIMIT ? OFFSET ?
    `;
    const dataParams = [...queryParams, limit, offset];
    const [result] = await mysqlServerConnection.query(dataQuery, dataParams);

    console.log("✅ Found", result.length, "payment records");

    return res.status(200).json({
      success: true,
      status: 200,
      data: {
        records: result,
        pagination: {
          currentPage: page,
          totalPages: Math.ceil(totalRecords / limit),
          totalRecords: totalRecords,
          recordsPerPage: limit
        }
      },
      message: result.length > 0 ? "Payments fetched successfully" : "No payments found"
    });

  } catch (err) {
    console.error("❌ Error in getPayments:", err);
    return res.status(500).json({
      success: false,
      status: 500,
      message: "Server error while fetching payments",
      error: err.message
    });
  }
};

const getPaymentById = async (req, res) => {
  console.log("📡 [API] getPaymentById called");

  try {
    const { id } = req.params;
    const dbName = req.user.db_name;

    console.log("🔍 Fetching payment with ID:", id);

    // Validate required fields
    if (!id) {
      return res.status(400).json({
        success: false,
        status: 400,
        message: "Payment ID is required"
      });
    }

    // Get payment details with user information
    const dataQuery = `
      SELECT
        p.id,
        p.user_id,
        p.amount,
        p.currency,
        p.status,
        p.payment_method,
        p.created_at,
        p.updated_at,
        p.payment_uid,
        p.course_name,
        p.discount_point,
        p.receipt_url,
        p.receipt_pdf_path,
        u.name as user_name,
        u.email as user_email,
        u.mobile as user_phone
      FROM ${dbName}.payments p
      LEFT JOIN ${dbName}.users u ON p.user_id = u.id
      WHERE p.id = ?
    `;
    const [result] = await mysqlServerConnection.query(dataQuery, [id]);

    if (result.length === 0) {
      return res.status(404).json({
        success: false,
        status: 404,
        message: "Payment not found"
      });
    }

    console.log("✅ Payment details fetched successfully");

    return res.status(200).json({
      success: true,
      status: 200,
      data: result[0],
      message: "Payment details fetched successfully"
    });

  } catch (err) {
    console.error("❌ Error in getPaymentById:", err);
    return res.status(500).json({
      success: false,
      status: 500,
      message: "Server error while fetching payment details",
      error: err.message
    });
  }
};

module.exports = {
  getPayments,
  getPaymentById,
};
