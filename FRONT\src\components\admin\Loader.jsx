import React from 'react';
import PropTypes from 'prop-types';

const Loader = ({
  color = '#495057',
  size = 60,
  caption = 'Loading the data...',
  minLoadTime = 500,
  containerHeight = '200px'
}) => {

    
  const loaderStyle = {
    width: `${size}px`,
    height: `${size}px`,
    border: `${Math.max(size / 10, 3)}px solid ${color}`,
    borderBottomColor: 'transparent',
    borderRadius: '50%',
    display: 'inline-block',
    boxSizing: 'border-box',
    animation: 'rotation 1s linear infinite'
  };

  const captionStyle = {
    color,
    fontSize: `${Math.max(size / 3, 14)}px`,
    marginTop: '8px',
    textAlign: 'center'
  };

  const containerStyle = {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    minHeight: containerHeight,
    flexDirection: 'column',
    gap: '15px'
  };

  return (
    <div style={containerStyle}>
      <style>
        {`
          @keyframes rotation {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `}
      </style>
      <span style={loaderStyle}></span>
      {caption && <div style={captionStyle}>{caption}</div>}
    </div>
  );
};

Loader.propTypes = {
  color: PropTypes.string,
  size: PropTypes.number,
  caption: PropTypes.string,
  minLoadTime: PropTypes.number,
  containerHeight: PropTypes.string
};

export default Loader;
