.activity-card {
  background-color: white;
  border-radius: 8px;
  padding: 1.25rem;
  display: flex;
  flex-direction: column;
  height:   404px !important;  
}

.activity-title { 
  font-weight: 700;
  margin-bottom: 1rem;
  color: #333;
  font-size: 1.1rem;
}

.activity-timeline {
  position: relative;
  padding-left: 0.5rem;
  padding-right: 0.6rem;
  overflow-y: auto;
  flex: 1;
  scrollbar-width: thin;
  scrollbar-color: #e0e0e0 transparent;
}

.activity-timeline::-webkit-scrollbar {
  width: 6px;
}

.activity-timeline::-webkit-scrollbar-track {
  background: transparent;
}

.activity-timeline::-webkit-scrollbar-thumb {
  background-color: #e0e0e0;
  border-radius: 6px;
}

.activity-timeline::-webkit-scrollbar-thumb:hover {
  background-color: #d0d0d0;
}

.timeline-item {
  display: flex;
  margin-bottom: 1.25rem;
  position: relative;
}

.timeline-item:last-child {
  margin-bottom: 0;
}

.timeline-dot-container {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 1rem;
  min-width: 20px;
}

.timeline-dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  z-index: 2;
  margin-top: 6px;
}

.timeline-line {
  position: absolute;
  top: 25px;
  bottom: -15px;
  width: 2px;
  background-color: #e0e0e0;
  left: 9px;
  z-index: 1;
}

.timeline-content {
  flex: 1;
}

.timeline-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.25rem;
}

.timeline-title {
  margin: 0;
  font-weight: 600;
  font-size: 0.95rem;
  color: #333;
}

.timeline-time {
  font-size: 0.8rem;
  color: #888;
}

.timeline-text {
  margin: 0;
  font-size: 0.85rem;
  color: #666;
  line-height: 1.4;
}

.timeline-more {
  display: inline-block;
  margin-top: 0.25rem;
  font-size: 0.8rem;
  color: #3152e8;
  text-decoration: none;
}

.timeline-more:hover {
  text-decoration: underline;
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  height: calc(370px - 60px);
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: var(--border-color) transparent;
  padding: 1rem;
}

.activity-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 0.75rem;
  border-radius: 8px;
  background: white;
  transition: all 0.2s ease;
  border: 1px solid #e0e0e0;
}

.activity-item:hover {
  border-color: #4361ee;
  box-shadow: 0 2px 4px rgba(67, 97, 238, 0.1);
}

.activity-dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  flex-shrink: 0;
  margin-top: 6px;
}

.activity-dot-success {
  background-color: #28a745;
  box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.2);
}

.activity-dot-primary {
  background-color: #4361ee;
  box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.2);
}

.activity-dot-warning {
  background-color: #ffc107;
  box-shadow: 0 0 0 3px rgba(255, 193, 7, 0.2);
}

.activity-content {
  flex: 1;
  min-width: 0;
}

.activity-title {
  font-size: 0.875rem;
  color: #333;
  margin-bottom: 0.25rem;
  font-weight: 600;
}

.activity-description {
  font-size: 0.8125rem;
  color: #666;
  line-height: 1.4;
}

.activity-time {
  font-size: 0.75rem;
  color: #888;
  display: flex;
  align-items: center;
}

/* Scrollbar Styles */
.activity-list::-webkit-scrollbar {
  width: 4px;
}

.activity-list::-webkit-scrollbar-track {
  background: transparent;
}

.activity-list::-webkit-scrollbar-thumb {
  background-color: #e0e0e0;
  border-radius: 4px;
}

.activity-list::-webkit-scrollbar-thumb:hover {
  background-color: #c0c0c0;
}

.header-icon {
  font-size: 1.25rem;
  color: #333;
  display: inline-flex;
  align-items: center;
}

