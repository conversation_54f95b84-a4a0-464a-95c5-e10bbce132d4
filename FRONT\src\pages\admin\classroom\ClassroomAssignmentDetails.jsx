import React, { useState } from 'react'
import { Icon } from '@iconify/react';
import { ToastContainer } from 'react-toastify';
import ClassroomAssignmentResources from './ClassroomAssignmentResources'
import ClassroomAssignmentResponse from './ClassroomAssignmentResponse'
import { usePermissions } from '../../../context/PermissionsContext';
import './Classroom.css';
import { toast } from 'react-toastify';
import { useParams } from 'react-router-dom';
import { decodeData } from '../../../utils/encodeAndEncode';

function ClassroomAssignmentDetails() {
  const [activeTab, setActiveTab] = useState('resources');
  const { permissions } = usePermissions();
  const { classroomId, assignmentId } = useParams();
  const decodedClassroomId = decodeData(classroomId);
  const decodedAssignmentId = decodeData(assignmentId);

  const handleTabChange = (tab) => {
    if (tab === 'resources' && !permissions.classroom_assignment_resource_view) {
      toast.warning("You don't have permission to view resources");
      return;
    }
    if (tab === 'response' && !permissions.classroom_assignment_response_view) {
      toast.warning("You don't have permission to view responses");
      return;
    }
    setActiveTab(tab);
  };

  return (
    <div className="row">
      <div className="col-md-12">
        <div className="card">
          <div className="card-body p-0">

            {/* Tabs */}
            <div className="tab-header border-bottom">
              <ul className="nav nav-tabs">
                <li className="nav-item">
                  <button
                    className={`nav-link d-flex align-items-center ${activeTab === 'resources' ? 'active' : ''}`}
                    onClick={() => handleTabChange('resources')}
                    disabled={!permissions.classroom_assignment_resource_view}
                    title={!permissions.classroom_assignment_resource_view ? "You don't have permission to view resources" : "View assignment resources"}
                  >
                    <Icon icon="fluent:document-24-regular" className="me-2" width="20" height="20" />
                    Resources
                  </button>
                </li>
                <li className="nav-item">
                  <button
                    className={`nav-link d-flex align-items-center ${activeTab === 'response' ? 'active' : ''}`}
                    onClick={() => handleTabChange('response')}
                    disabled={!permissions.classroom_assignment_response_view}
                    title={!permissions.classroom_assignment_response_view ? "You don't have permission to view responses" : "View assignment responses"}
                  >
                    <Icon icon="fluent:chat-24-regular" className="me-2" width="20" height="20" />
                    Response
                  </button>
                </li>
              </ul>
            </div>

            {/* Tab Content */}
            <div className="tab-content p-2">
              {activeTab === 'resources' && permissions.classroom_assignment_resource_view && (
                <div className="resources">
                  <ClassroomAssignmentResources decodedClassroomId={decodedClassroomId} decodedAssignmentId={decodedAssignmentId} />
                </div>
              )}

              {activeTab === 'response' && permissions.classroom_assignment_response_view && (
                <div className="response">
                  <ClassroomAssignmentResponse decodedClassroomId={decodedClassroomId} decodedAssignmentId={decodedAssignmentId} />
                </div>
              )}

              {((activeTab === 'resources' && !permissions.classroom_assignment_resource_view) ||
                (activeTab === 'response' && !permissions.classroom_assignment_response_view)) && (
                <div className="alert alert-warning m-3">
                  You don't have permission to view this content.
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
      <ToastContainer limit={3} />
    </div>
  )
}

export default ClassroomAssignmentDetails
