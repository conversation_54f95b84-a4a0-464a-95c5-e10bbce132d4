import React, { useState, useEffect, useCallback } from 'react'
import { Icon } from '@iconify/react';
import { toast } from 'react-toastify';
import {
  getPayments,
  getPaymentById
} from '../../../services/adminService';
import { usePermissions } from '../../../context/PermissionsContext';

function PaymentHistory() {
  const { permissions } = usePermissions();
  const [loading, setLoading] = useState(false);
  const [paymentData, setPaymentData] = useState([]);
  const [pagination, setPagination] = useState({
    currentPage: 1,
    totalPages: 1,
    totalRecords: 0,
    recordsPerPage: 10
  });
  const [search, setSearch] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [showInvoiceModal, setShowInvoiceModal] = useState(false);
  const [selectedPayment, setSelectedPayment] = useState(null);

  // Fetch payment data
  const fetchPaymentData = useCallback(async () => {
    try {
      setLoading(true);
      const response = await getPayments({
        page: pagination.currentPage,
        limit: pagination.recordsPerPage,
        search: search,
        status: statusFilter
      });

      if (response.success) {
        setPaymentData(response.data.records);
        setPagination(response.data.pagination);
      } else {
        toast.error('Failed to fetch payment data');
      }
    } catch (error) {
      console.error('Error fetching payment data:', error);
      toast.error('Error fetching payment data');
    } finally {
      setLoading(false);
    }
  }, [pagination.currentPage, pagination.recordsPerPage, search, statusFilter]);

  // Load data on component mount and when dependencies change
  useEffect(() => {
    fetchPaymentData();
  }, [fetchPaymentData]);

  const getStatusClass = (status) => {
    const formattedStatus = status?.charAt(0).toUpperCase() + status?.slice(1);
    switch (status?.toLowerCase()) {
      case 'completed':
        return (
          <span className="badge border border-success bg-success bg-opacity-10 text-success d-inline-flex align-items-center gap-1">
            <Icon icon="mdi:check-circle-outline" className="fs-6" />
            {formattedStatus}
          </span>
        );
      case 'pending':
        return (
          <span className="badge border border-warning bg-warning bg-opacity-10 text-warning d-inline-flex align-items-center gap-1">
            <Icon icon="mdi:clock-outline" className="fs-6" />
            {formattedStatus}
          </span>
        );
      case 'failed':
        return (
          <span className="badge border border-danger bg-danger bg-opacity-10 text-danger d-inline-flex align-items-center gap-1">
            <Icon icon="mdi:close-circle-outline" className="fs-6" />
            {formattedStatus}
          </span>
        );
      case 'refunded':
        return (
          <span className="badge border border-info bg-info bg-opacity-10 text-info d-inline-flex align-items-center gap-1">
            <Icon icon="mdi:refresh-circle-outline" className="fs-6" />
            {formattedStatus}
          </span>
        );
      default:
        return (
          <span className="badge border border-secondary bg-secondary bg-opacity-10 text-secondary d-inline-flex align-items-center gap-1">
            <Icon icon="mdi:help-circle-outline" className="fs-6" />
            Unknown
          </span>
        );
    }
  };

  const handleViewDetails = async (paymentId) => {
    if (!permissions.settings_payments_view_analytics) {
      toast.warning("You don't have permission to view payment details");
      return;
    }

    try {
      setLoading(true);
      const response = await getPaymentById(paymentId);
      if (response.success) {
        if (response.data.receipt_url) {
          window.open(response.data.receipt_url, '_blank');
        } else if (response.data.receipt_pdf_path) {
          const pdfUrl = `${process.env.REACT_APP_API_BASE_URL || 'http://localhost:5000'}/${response.data.receipt_pdf_path}`;
          window.open(pdfUrl, '_blank');
        } else {
          toast.warning('Invoice not available for this payment');
        }
      } else {
        toast.error('Failed to fetch payment details');
      }
    } catch (error) {
      console.error('Error fetching payment details:', error);
      toast.error('Error fetching payment details');
    } finally {
      setLoading(false);
    }
  };

  const handleViewInvoice = async (paymentId) => {
    if (!permissions.settings_payments_view_analytics) {
      toast.warning("You don't have permission to view payment details");
      return;
    }

    try {
      setLoading(true);
      const response = await getPaymentById(paymentId);
      if (response.success) {
        setSelectedPayment(response.data);
        setShowInvoiceModal(true);
      } else {
        toast.error('Failed to fetch payment details');
      }
    } catch (error) {
      console.error('Error fetching payment details:', error);
      toast.error('Error fetching payment details');
    } finally {
      setLoading(false);
    }
  };

  const handleDownloadInvoice = (payment) => {
    if (payment.receipt_url) {
      window.open(payment.receipt_url, '_blank');
    } else if (payment.receipt_pdf_path) {
      // Construct the full URL for the PDF path
      const pdfUrl = `${process.env.REACT_APP_API_BASE_URL || 'http://localhost:5000'}/${payment.receipt_pdf_path}`;
      window.open(pdfUrl, '_blank');
    } else {
      toast.warning('Invoice not available for this payment');
    }
  };

  const handleSearchChange = (e) => {
    setSearch(e.target.value);
    setPagination(prev => ({ ...prev, currentPage: 1 }));
  };

  const handleStatusFilterChange = (e) => {
    setStatusFilter(e.target.value);
    setPagination(prev => ({ ...prev, currentPage: 1 }));
  };

  const handlePageChange = (newPage) => {
    setPagination(prev => ({ ...prev, currentPage: newPage }));
  };

  const handleLimitChange = (e) => {
    const newLimit = parseInt(e.target.value);
    setPagination(prev => ({
      ...prev,
      recordsPerPage: newLimit,
      currentPage: 1
    }));
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleString();
  };

  const formatAmount = (amount, currency = 'USD') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency
    }).format(amount);
  };

  return (
    <>
      {/* search and filter section */}
      <div className="row mb-3">
        <div className="col-md-6 mt-2 d-flex justify-content-start align-items-center gap-2">
          <input
            type="search"
            className="form-control"
            placeholder="Search by course name, payment method, or payment ID..."
            style={{ maxWidth: '350px' }}
            value={search}
            onChange={handleSearchChange}
          />
        </div>
        <div className="col-md-6 text-end mt-2 d-flex justify-content-end align-items-center gap-2">
          <select
            className="form-select"
            style={{ width: '180px' }}
            value={statusFilter}
            onChange={handleStatusFilterChange}
          >
            <option value="">All Status</option>
            <option value="completed">Completed</option>
            <option value="pending">Pending</option>
            <option value="failed">Failed</option>
            <option value="refunded">Refunded</option>
          </select>
        </div>
      </div>

      {/* table */}
      <div className="row mb-3">
        <div className="col-12">
          <div className="card">
            <div className="table-responsive">
              <table className="table table-borderless mb-0" style={{ minWidth: '1200px' }}>
                <thead>
                  <tr>
                    <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500', width: '80px' }}>SL No</th>
                    <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500', width: '150px' }}>Transaction ID</th>
                    <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500', width: '120px' }}>Amount</th>
                    <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500', width: '180px' }}>Payment Date</th>
                    <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500', width: '120px' }}>Status</th>
                    <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500', width: '150px' }}>Action</th>
                  </tr>
                </thead>
                <tbody>
                  {loading ? (
                    <tr>
                      <td colSpan="6" className="text-center py-4">
                        <div className="spinner-border text-primary" role="status">
                          <span className="visually-hidden">Loading...</span>
                        </div>
                      </td>
                    </tr>
                  ) : paymentData.length === 0 ? (
                    <tr>
                      <td colSpan="6" className="text-center py-4">
                        No payment records found
                      </td>
                    </tr>
                  ) : (
                    paymentData.map((payment, index) => (
                      <tr key={payment.id} className="border-bottom">
                        <td className="py-3 align-middle">
                          {(pagination.currentPage - 1) * pagination.recordsPerPage + index + 1}
                        </td>
                        <td className="py-3 align-middle">
                          {payment.payment_uid ? payment.payment_uid.substring(0, 20) + '...' : 'N/A'}
                        </td>
                        <td className="py-3 align-middle">
                          {formatAmount(payment.amount, payment.currency)}
                        </td>
                        <td className="py-3 align-middle">{formatDate(payment.created_at)}</td>
                        <td className="py-3 align-middle">
                          {getStatusClass(payment.status)}
                        </td>
                        <td className="py-3 align-middle">
                          <div className="d-flex gap-2">
                            <button
                              className="btn btn-outline-dark btn-sm border border-dark"
                              style={{ width: '36px', height: '36px' }}
                              title={!permissions.settings_payments_view_analytics ? "You don't have permission to view invoice" : "Open Invoice"}
                              onClick={() => handleViewDetails(payment.id)}
                              disabled={loading || !permissions.settings_payments_view_analytics}
                            >
                              <Icon icon="fluent:document-pdf-24-regular" width="16" height="16" />
                            </button>
                            <button
                              className="btn btn-outline-dark btn-sm border border-dark"
                              style={{ width: '36px', height: '36px' }}
                              title={!permissions.settings_payments_view_analytics ? "You don't have permission to view details" : "View Details"}
                              onClick={() => handleViewInvoice(payment.id)}
                              disabled={loading || !permissions.settings_payments_view_analytics}
                            >
                              <Icon icon="fluent:eye-24-regular" width="16" height="16" />
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))
                  )}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>

      {/* record per page and pagination */}
      <div className="row">
        <div className="col-md-6">
          <select
            className="form-select"
            style={{ width: 'auto' }}
            value={pagination.recordsPerPage}
            onChange={handleLimitChange}
          >
            <option value={10}>10 records per page</option>
            <option value={25}>25 records per page</option>
            <option value={50}>50 records per page</option>
          </select>
        </div>
        <div className="col-md-6 d-flex justify-content-end align-items-center gap-3">
          <button
            className="btn btn-primary"
            style={{ width: '150px' }}
            onClick={() => handlePageChange(pagination.currentPage - 1)}
            disabled={pagination.currentPage === 1 || loading}
          >
            Prev
          </button>
          <span>Page {pagination.currentPage} of {pagination.totalPages}</span>
          <button
            className="btn btn-primary"
            style={{ width: '150px' }}
            onClick={() => handlePageChange(pagination.currentPage + 1)}
            disabled={pagination.currentPage === pagination.totalPages || loading}
          >
            Next
          </button>
        </div>
      </div>

      {/* Payment Details Modal */}
      {showDetailsModal && selectedPayment && (
        <div className="modal show d-block" tabIndex="-1" style={{ backgroundColor: 'rgba(0,0,0,0.5)' }}>
          <div className="modal-dialog modal-dialog-centered modal-lg">
            <div className="modal-content">
              <div className="modal-header border-0 pb-0">
                <h5 className="modal-title">Payment Details</h5>
                <button
                  type="button"
                  className="btn-close"
                  onClick={() => {
                    setShowDetailsModal(false);
                    setSelectedPayment(null);
                  }}
                ></button>
              </div>
              <div className="modal-body pt-2">
                <div className="row">
                  <div className="col-12">
                    <div className="mb-3">
                      <label className="form-label fw-bold">Transaction ID:</label>
                      <p className="mb-0">{selectedPayment.payment_uid ? selectedPayment.payment_uid.substring(0, 20) + '...' : 'N/A'}</p>
                    </div>
                  </div>
                  <div className="col-12">
                    <div className="mb-3">
                      <label className="form-label fw-bold">User Name:</label>
                      <p className="mb-0">{selectedPayment.user_name || 'N/A'}</p>
                    </div>
                  </div>
                  <div className="col-12">
                    <div className="mb-3">
                      <label className="form-label fw-bold">Phone Number:</label>
                      <p className="mb-0">{selectedPayment.user_phone || 'N/A'}</p>
                    </div>
                  </div>
                  <div className="col-12">
                    <div className="mb-3">
                      <label className="form-label fw-bold">Email:</label>
                      <p className="mb-0">{selectedPayment.user_email || 'N/A'}</p>
                    </div>
                  </div>
                  <div className="col-12">
                    <div className="mb-3">
                      <label className="form-label fw-bold">Payment Method:</label>
                      <p className="mb-0">{selectedPayment.payment_method}</p>
                    </div>
                  </div>
                  <div className="col-12">
                    <div className="mb-3">
                      <label className="form-label fw-bold">Course Name:</label>
                      <p className="mb-0">{selectedPayment.course_name || 'N/A'}</p>
                    </div>
                  </div>
                  <div className="col-12">
                    <div className="mb-3">
                      <label className="form-label fw-bold">Status:</label>
                      <div>
                        {getStatusClass(selectedPayment.status)}
                      </div>
                    </div>
                  </div>
                  <div className="col-12">
                    <div className="mb-3">
                      <label className="form-label fw-bold">Amount:</label>
                      <p className="mb-0">{formatAmount(selectedPayment.amount, selectedPayment.currency)}</p>
                    </div>
                  </div>
                  <div className="col-12">
                    <div className="mb-3">
                      <label className="form-label fw-bold">Payment Date:</label>
                      <p className="mb-0">{formatDate(selectedPayment.created_at)}</p>
                    </div>
                  </div>
                  {(selectedPayment.receipt_url || selectedPayment.receipt_pdf_path) && (
                    <div className="col-12">
                      <div className="d-flex justify-content-center mt-3">
                        <button
                          className="btn btn-primary"
                          onClick={() => handleDownloadInvoice(selectedPayment)}
                        >
                          <Icon icon="fluent:document-pdf-24-regular" width="16" height="16" className="me-2" />
                          Open Invoice
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              </div>
              <div className="modal-footer">
                <button
                  type="button"
                  className="btn btn-secondary"
                  onClick={() => {
                    setShowDetailsModal(false);
                    setSelectedPayment(null);
                  }}
                >
                  Close
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Invoice Modal */}
      {showInvoiceModal && selectedPayment && (
        <div className="modal show d-block" tabIndex="-1" style={{ backgroundColor: 'rgba(0,0,0,0.5)' }}>
          <div className="modal-dialog modal-dialog-centered modal-lg">
            <div className="modal-content">
              <div className="modal-header border-0 pb-0">
                <h5 className="modal-title">Invoice Details</h5>
                <button
                  type="button"
                  className="btn-close"
                  onClick={() => {
                    setShowInvoiceModal(false);
                    setSelectedPayment(null);
                  }}
                ></button>
              </div>
              <div className="modal-body pt-2">
                <div className="row">
                  <div className="col-12 mt-2 mb-2">
                    {(selectedPayment.receipt_url || selectedPayment.receipt_pdf_path) && (
                      <button
                        className="btn btn-primary w-100"
                        onClick={() => handleDownloadInvoice(selectedPayment)}
                      >
                        <Icon icon="fluent:document-pdf-24-regular" width="16" height="16" className="me-2" />
                        Open Invoice in New Tab
                      </button>
                    )}
                  </div>
                  <div className="col-12">
                    <div className="mb-3">
                      <label className="form-label fw-bold">Transaction ID:</label>
                      <p className="mb-0" style={{ wordBreak: 'break-all' }}>{selectedPayment.payment_uid || 'N/A'}</p>
                    </div>
                  </div>
                  <div className="col-12">
                    <div className="mb-3">
                      <label className="form-label fw-bold">User Name:</label>
                      <p className="mb-0">{selectedPayment.user_name || 'N/A'}</p>
                    </div>
                  </div>
                  <div className="col-12">
                    <div className="mb-3">
                      <label className="form-label fw-bold">Phone Number:</label>
                      <p className="mb-0">{selectedPayment.user_phone || 'N/A'}</p>
                    </div>
                  </div>
                  <div className="col-12">
                    <div className="mb-3">
                      <label className="form-label fw-bold">Email:</label>
                      <p className="mb-0">{selectedPayment.user_email || 'N/A'}</p>
                    </div>
                  </div>
                  <div className="col-12">
                    <div className="mb-3">
                      <label className="form-label fw-bold">Payment Method:</label>
                      <p className="mb-0">{selectedPayment.payment_method}</p>
                    </div>
                  </div>
                  <div className="col-12">
                    <div className="mb-3">
                      <label className="form-label fw-bold">Course Name:</label>
                      <p className="mb-0">{selectedPayment.course_name || 'N/A'}</p>
                    </div>
                  </div>
                  <div className="col-12">
                    <div className="mb-3">
                      <label className="form-label fw-bold">Status:</label>
                      <div>
                        {getStatusClass(selectedPayment.status)}
                      </div>
                    </div>
                  </div>
                  <div className="col-12">
                    <div className="mb-3">
                      <label className="form-label fw-bold">Amount:</label>
                      <p className="mb-0">{formatAmount(selectedPayment.amount, selectedPayment.currency)}</p>
                    </div>
                  </div>
                  <div className="col-12">
                    <div className="mb-3">
                      <label className="form-label fw-bold">Payment Date:</label>
                      <p className="mb-0">{formatDate(selectedPayment.created_at)}</p>
                    </div>
                  </div>
                </div>
              </div>
              <div className="modal-footer">
                <button
                  type="button"
                  className="btn btn-secondary"
                  onClick={() => {
                    setShowInvoiceModal(false);
                    setSelectedPayment(null);
                  }}
                >
                  Close
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  )
}

export default PaymentHistory
