const { mysqlServerConnection } = require('../../../db/db');
const { hashPassword, deleteFileIfExists, LogsHandler } = require('../../../tools/tools');
const MaindbName = process.env.MAIN_DB;
const { validateEmail, validatePassword } = require('../../../Organization/controller/accounts/orgUser');
const { stringify } = require('uuid');
const { sendAccountCreatedEmail, sentEmailNotification } = require('../../../tools/emailtemplates');
const { SetNotification } = require('../../../utils/sendNotificaiton');
const { getRoleByName } = require('../../../tools/fintRole');
const { uploadImageToS3 } = require('../../../tools/aws');
const bcrypt = require("bcrypt"); // make sure this is installed

const getUserDetails = async (req, res, next) => {
    console.log('-------------------')
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const offset = (page - 1) * limit;
    const search = req.query.search ? req.query.search.trim() : '';
    const status = req.query.status;

    const role_id = await getRoleByName({ db_name: req.user.db_name, type: "trainee" });

    let query = `
      SELECT 
        u.id, 
        u.profile_pic_url, 
        u.name, 
        u.mobile, 
        u.mobileno_code, 
        u.email, 
        u.is_blocked, 
        r.name AS role_name
      FROM ${req.user.db_name}.users u
      JOIN ${req.user.db_name}.user_roles ur ON u.id = ur.user_id
      JOIN ${req.user.db_name}.roles r ON r.id = ur.role_id
      WHERE ur.role_id != ${role_id} AND u.is_deleted = 0`;

    if (search) {
      query += ` AND (u.name LIKE ? OR u.email LIKE ?)`;
    }

    if (status == 'true' || status == 'false') {
      query += ` AND u.is_blocked = ?`;
    }

    query += ` LIMIT ? OFFSET ?`;

    const params = [];
    if (search) {
      params.push(`%${search}%`, `%${search}%`);
    }
    if (status == 'true' || status == 'false') {
      params.push(status == 'true' ? 0 : 1);
    }
    params.push(limit, offset);

    const [users] = await mysqlServerConnection.query(query, params);

    const [[{ totalCount }]] = await mysqlServerConnection.query(
        `
        SELECT COUNT(*) AS totalCount
        FROM ${req.user.db_name}.users u
        JOIN ${req.user.db_name}.user_roles ur ON u.id = ur.user_id
        JOIN ${req.user.db_name}.roles r ON r.id = ur.role_id
        WHERE ur.role_id != ${role_id} 
        AND u.is_deleted = 0
        ${search ? `AND (u.name LIKE ? OR u.email LIKE ?)` : ''}
        ${status !== undefined ? `AND u.is_blocked = ?` : ''}
        `,
        [
          ...(search ? [`%${search}%`, `%${search}%`] : []),
          ...(status !== undefined ? [status === 'true' ? 1 : 0] : [])
        ]
      );
      
console.log("📦 Final Fetched Users:", users);
console.log("📊 Pagination Info:", {
  page,
  limit,
  totalPages: Math.ceil(totalCount / limit),
  totalCount
});

return res.status(200).json({
  success: true,
  data: {
    users,
    pagination: {
      page,
      limit,
      totalPages: Math.ceil(totalCount / limit),
      totalCount
    },
  }
});


  } catch (error) {
    console.error('Error fetching user details:', error);
    return next({ statusCode: 500, message: error.message || 'Internal Server Error' });
  }
};





const createActiveUser = async (req, res, next) => {

    try {
        const { user_id } = req.body;
        if (!user_id) {
            return next({ statusCode: 404, message: 'please provide user id' })
        }
        const [result] = await mysqlServerConnection.query(`UPDATE ${req.user.db_name}.users SET is_blocked = 0 where id= ?`, [user_id]);

        if (result.affectedRows === 0) {
            res.next({ statusCode: 404, message: 'User not found or is aldready activated' })
        }

        res.status(200).json({
            success: true,
            message: 'User Activated Successfully'
        })
    } catch (error) {
        return next({ statusCode: 500, message: error.message || 'Internal Server Error' })
    }
}

const activeDeactiveUser = async (req, res, next) => {
    try {
        const { user_id } = req.params;
        const { status } = req.body;
        console.log("status from trainee", status, user_id)
        if (!user_id) {
            return next({ statusCode: 404, message: 'please provide user id' })
        }

        const [result] = await mysqlServerConnection.query(`UPDATE ${req.user.db_name}.users SET is_blocked = ? where id= ?`, [status, user_id]);

        if (result.affectedRows === 0) {
            res.next({ statusCode: 404, message: 'User not found or is updated aldready' })
        }
        res.status(200).json({
            success: true,
            message: 'Trainee status updated successfully'
        })
    } catch (error) {
        return next({ statusCode: 500, message: error.message || 'Internal Server Error' })
    }
};

// const createOrganistionUser = async (req, res, next) => {

//     try {
//         const { name, email, mobile, password, role_id, organisation_id,notify_by_email } = req.body;
//         const hash_password = await hashPassword(password);
//         console.log("REQUEST FOR CREATE USER", req.body);
//         const file = req.files["profile_pic"] || null;

//         const [maindb] = await mysqlServerConnection.query(`SELECT db_name FROM ${MaindbName}.organization WHERE id = ?`, [organisation_id]);

//         const [user] = await mysqlServerConnection.query(`SELECT * from ${maindb[0].db_name}.users where email = ? AND mobile = ?`, [email, mobile]);
//         if (user.length > 0) {
//             return next({ statusCode: 409, message: 'User aldready exists' })
//         }


//         console.log("maindb", maindb);
//         const [result] = await mysqlServerConnection.query(`
//         INSERT INTO ${maindb[0].db_name}.users (name,email,mobile,password,profile_pic_url,db_name,is_verified) 
//         VALUES (?,?,?,?,?,?,?);
//        `, [name, email, mobile, hash_password, file ? `/uploads/${req.files["profile_pic"][0].fileName}` : null, maindb[0].db_name, 1]);

//         await mysqlServerConnection.query(`INSERT INTO ${maindb[0].db_name}.user_roles (user_id,role_id) VALUES (?,?)`, [result.insertId, role_id]);

//         if(notify_by_email == 'true'){
//            console.log("SENDING MAIL");
//            sendAccountCreatedEmail(email,name,password,'http://localhost:3000/login');
//         }



//         res.status(200).json({
//             success: true,
//             message: 'User created successfully',
//         }) 

//     } catch (error) {
//         console.log("Error Creating User",error)
//         return next({ statusCode: 500, message: error.message || 'Internal Server Error' })
//     }
// }

const createOrganistionUser = async (req, res) => {
    try {
        let { name, email, mobile, password, send_email, mobileno_code, employee_no, company_description, ic_no, fin_no, gender, pwm_rank, job_title, date_of_birth, date_joined, company_code } = req.body;

        const hash_password = await hashPassword(password);
        const file = req.file;
        const db_name = req.user.db_name;
        console.log("REQUEST FOR CREATE USER", req.body);

        let pathNew = '';
        if (file) {
            // const pathNew = file["profile_pic"][0]
            const filedata = await uploadImageToS3(file.buffer)
            pathNew = filedata.path;
        }
        else {
            pathNew = null
        }

        console.log("PATH NEW", pathNew);

        if(!employee_no){
            employee_no = null;
        }

        if(!company_description){
            company_description = null;
        }

        if(!ic_no){
            ic_no = null;
        }

        if(!fin_no){
            fin_no = null;
        }

        if(!gender){
            gender = null;
        }

        if(!pwm_rank){
            pwm_rank = null;
        }

        if(!job_title){
            job_title = null;
        }

        if(!date_of_birth){
            date_of_birth = null;
        }

        if(!date_joined){
            date_joined = null;
        }

        if(!company_code){
            company_code = null;
        }

        // convert ic_no and fin_co and store it as the blob
        ic_no = ic_no? Buffer.from(ic_no).toString('base64'):null;
        fin_no = fin_no? Buffer.from(fin_no).toString('base64'):null;
        const [user] = await mysqlServerConnection.query(`SELECT * from ${db_name}.users where email = ?`, [email]);


        if (user.length > 0) {
            return res.status(404).json({
                state: 404,
                success: false,
                message: 'Email already exists',
            });
        }


        const role_id = await getRoleByName({ db_name: db_name, type: 'trainee' });
        const [result] = await mysqlServerConnection.query(`
        INSERT INTO ${db_name}.users (name,email,mobileno_code,mobile,password,profile_pic_url,db_name,is_email_verified,employee_no,company_description,ic_no,fin_no,gender,pwm_rank,job_title,date_of_birth,date_joined,company_code) 
        VALUES (
                ?, -- name
                ?, -- email
                ?, -- mobileno_code
                ?, -- mobile
                ?, -- password
                ?, -- profile_pic_url
                ?, -- db_name
                ?, -- is_email_verified
                ?, -- employee_no
                ?, -- company_description
                ?, -- ic_no
                ?, -- fin_no
                ?, -- gender
                ?, -- pwm_rank
                ?, -- job_title
                ?, -- date_of_birth
                ?, -- date_joined
                ?  -- company_code
                );
       `, [name, email, mobileno_code, mobile, hash_password, pathNew, db_name, 1, employee_no, company_description, ic_no, fin_no, gender, pwm_rank, job_title, date_of_birth, date_joined, company_code]);

        console.log("User inserted:",result);

        const [org] = await mysqlServerConnection.query(`SELECT * from ${MaindbName}.organization where db_name = ?`, [db_name]);

        await mysqlServerConnection.query(`INSERT INTO ${db_name}.user_roles (user_id,role_id) VALUES (?,?)`, [result.insertId, role_id])

        let dec = `We are thrilled to have you join our learning community, where education meets innovation. ${org[0].name} is designed to empower learners like you by providing personalized learning experiences, interactive features, and the flexibility to learn at your own pace. Whether you’re looking to gain new skills, achieve professional goals, or simply explore new areas of interest, our platform offers a wide range of resources to support your journey. With engaging content, progress tracking, and anytime, anywhere access,  ${org[0].name} ensures that learning is not only effective but also enjoyable. Let’s embark on this journey of growth and discovery together!`

        let title = `Welcome to ${org[0].name}!`

        const [newUser] = await mysqlServerConnection.query(`SELECT * from ${db_name}.users where id = ?`, [result.insertId])
        let new_username = newUser[0].name;
        // await SetNotification({ dbName: db_name, userId: result.insertId, sendFrom: null, title: title, body: dec });

        // sentEmailNotification(newUser[0].email, title, title, dec,)
        LogsHandler(db_name, req.user.userId, "Crate user", `User created with email ${email} and name ${name}`)
        // sendAccountCreatedEmail(newUser[0].email, newUser[0].name, password, org[0].auth_sub_domain, org[0])

        res.status(200).json({
            success: true,
            user: new_username,
            message: 'User created successfully',
        })

    } catch (error) {
        console.log(error)
        return res.status(500).json({ success: false, data: { error_msg: error.message || 'Internal Server Error' } })
    }
}

const deleteOrganisationUser = async (req, res, next) => {

    try {

        const { user_id } = req.params;
        if (!user_id) {
            return next({ statusCode: 404, message: 'User id is not provided' })
        }
        const [result] = await mysqlServerConnection.query(`UPDATE ${req.user.db_name}.users SET is_deleted = 1 WHERE id = ?`, [user_id]);

        if (result.affectedRows === 0) {
            return next({ statusCode: 404, message: 'User with the specified ID is not found or is already deleted' })
        }

        await mysqlServerConnection.query(`DELETE FROM ${req.user.db_name}.user_roles where user_id = ?`, [user_id]);

        res.status(201).json({
            success: true,
            message: 'User deleted successfully'
        })

    } catch (error) {
        return next({ statusCode: 500, message: error.message || 'Internal Server Error' })
    }
}

const editOrganisationUser = async (req, res, next) => {
  try {
    const { user_id } = req.params;
    console.log("USER ID", user_id);
    const { name, email, mobile, role_id, password, mobileno_code } = req.body;

    console.log("REQUEST FOR EDIT USER", req.body);

    const file = req.file; // Changed from req.files to req.file since using upload.single()
    const db_name = req.user.db_name; // Get db_name from JWT token

    console.log("FILE", file);

    if (!user_id) {
      return next({ statusCode: 404, message: 'Please provide user id' });
    }

    if (!db_name) {
      return next({ statusCode: 400, message: 'Database name not found in user session' });
    }

    // Fetch existing user
    const [user] = await mysqlServerConnection.query(
      `SELECT * FROM ${db_name}.users WHERE id = ? AND is_deleted = 0`,
      [user_id]
    );

    if (user.length === 0) {
      return next({ statusCode: 404, message: 'User not found or is already deleted' });
    }

    // Handle profile picture upload
    let profilePicPath = user[0]?.profile_pic_url || null;

    if (file) {
      try {
        // Upload new image to S3
        const filedata = await uploadImageToS3(file.buffer);
        profilePicPath = filedata.path;

        // Delete old file if exists and new file uploaded successfully
        if (user[0].profile_pic_url && filedata.success) {
          deleteFileIfExists(user[0].profile_pic_url);
        }
      } catch (uploadError) {
        console.error("Error uploading file to S3:", uploadError);
        return next({ statusCode: 500, message: 'Failed to upload profile picture' });
      }
    }

    // Update user fields
    const updateFields = [name, email, mobile, mobileno_code, profilePicPath, user_id];
    const updateQuery = `
      UPDATE ${db_name}.users
      SET name = ?, email = ?, mobile = ?, mobileno_code = ?, profile_pic_url = ?
      WHERE id = ?
    `;

    await mysqlServerConnection.query(updateQuery, updateFields);

    // Optionally update password if provided
    if (password && password.trim().length >= 6) {
      const hashedPassword = await bcrypt.hash(password, 10);
      await mysqlServerConnection.query(
        `UPDATE ${db_name}.users SET password = ? WHERE id = ?`,
        [hashedPassword, user_id]
      );
    }

    // Update role
    await mysqlServerConnection.query(
      `UPDATE ${db_name}.user_roles SET role_id = ? WHERE user_id = ?`,
      [role_id, user_id]
    );

    res.status(200).json({
      success: true,
      message: 'User details updated successfully',
    });

  } catch (error) {
    console.error("ERROR IN EDIT USER", error);
    return next({ statusCode: 500, message: error.message || 'Internal Server Error' });
  }
};
 


const getTraineeDataWithCertificateCount = async (req, res, next) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 10;
        const offset = (page - 1) * limit;
        const search = req.query.search !== '' ? `%${req.query.search}%` : null;
        const status = req.query.status && req.query.status !== 'null' ? (req.query.status === 'true' ? 0 : 1) : null;

        const role_id = await getRoleByName({ db_name: req.user.db_name, type: "trainee" });

        let queryParams = [role_id];
        let whereConditions = [`ur.role_id = ?`, `u.is_deleted = 0`];

        // Add status filter if provided
        if (status !== null) {
            whereConditions.push(`u.is_blocked = ?`);
            queryParams.push(status);
        }

        // Add search filter if provided
        if (search) {
            whereConditions.push(`(u.name LIKE ? OR u.email LIKE ?)`);
            queryParams.push(search, search);
        }

        // Construct WHERE clause
        const whereClauseString = whereConditions.length ? `WHERE ${whereConditions.join(" AND ")}` : "";

        // Main Query with filtering
        const dataQuery = `
            SELECT u.profile_pic_url, u.id, u.name, u.is_blocked, u.email, u.employee_no, u.company_description, u.ic_no, u.fin_no, u.gender, u.pwm_rank, u.job_title,
                   COUNT(DISTINCT c.id) AS certificate_count,
                   COUNT(DISTINCT mc.id) AS active_courses
            FROM ${req.user.db_name}.users u
            JOIN ${req.user.db_name}.user_roles ur ON ur.user_id = u.id
            LEFT JOIN ${req.user.db_name}.certificates c ON c.user_id = u.id
            LEFT JOIN ${req.user.db_name}.mycourses mc ON mc.user_id = u.id
            ${whereClauseString}
            GROUP BY u.id
            ORDER BY u.updatedAt DESC
            LIMIT ? OFFSET ?`;

        // Append limit & offset
        queryParams.push(limit, offset);

        // Execute the paginated query
        const [data] = await mysqlServerConnection.query(dataQuery, queryParams);

        // Count Query for Pagination
        const countQuery = `
            SELECT COUNT(DISTINCT u.id) AS totalCount
            FROM ${req.user.db_name}.users u
            JOIN ${req.user.db_name}.user_roles ur ON ur.user_id = u.id
            ${whereClauseString}`;

        const [[{ totalCount }]] = await mysqlServerConnection.query(countQuery, queryParams.slice(0, queryParams.length - 2));

        // Calculate total pages
        const totalPages = totalCount > 0 ? Math.ceil(totalCount / limit) : 1;

        res.status(200).json({
            success: true,
            data: {
                data,
                pagination: {
                    page,
                    limit,
                    totalPages,
                    totalCount
                }
            }
        });
    } catch (error) {
        console.error('Error fetching trainee data:', error);
        return res.status(500).json({ success: false, error_msg: error.message || 'Internal Server Error' });
    }
};





const getTrainerDetails = async (req, res, next) => {

    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const offset = (page - 1) * limit;
    const { role_id } = req.params;

    const [data] = await mysqlServerConnection.query(`
    SELECT u.name,u.id from ${req.user.db_name}.users u 
    join ${req.user.db_name}.user_roles ur on u.id = ur.user_id
    where ur.role_id = ?
    LIMIT ? OFFSET ?`, [role_id, limit, offset]);

    res.status(200).json({
        success: true,
        data,
        pagination: {
            page,
            limit
        }
    })

}





const createAdminUser = async (req, res) => {
    try {
        const { name, email, mobile, mobileno_code, password, role_id, notify_by_email } = req.body;
        console.log('Users response -------------', req.body);

        const hash_password = await hashPassword(password);
        const files = req.files;
        const db_name = req.user.db_name;

        let pathNew = '';
        if (files && files["profile_pic"] && files["profile_pic"][0]) {
            // Upload image to S3 like other functions
            console.log("Uploading profile picture to S3...");
            const filedata = await uploadImageToS3(files["profile_pic"][0].buffer);
            pathNew = filedata.path;
            console.log("Profile picture uploaded to S3:", pathNew);
        } else {
            pathNew = null;
            console.log("No profile picture provided");
        }

        const [existingUser] = await mysqlServerConnection.query(
            `SELECT * FROM ${db_name}.users WHERE email = ?`, [email]
        );

        if (existingUser.length > 0) {
            return res.status(409).json({ success: false, message: 'User already exists' });
        }

        const [result] = await mysqlServerConnection.query(`
            INSERT INTO ${db_name}.users 
            (name, email, mobile, mobileno_code, password, profile_pic_url, db_name, is_email_verified) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?);
        `, [name, email, mobile, mobileno_code, hash_password, pathNew, db_name, 1]);

        const [org] = await mysqlServerConnection.query(
            `SELECT * FROM ${MaindbName}.organization WHERE db_name = ?`, [db_name]
        );

        await mysqlServerConnection.query(
            `INSERT INTO ${db_name}.user_roles (user_id, role_id) VALUES (?, ?)`,
            [result.insertId, role_id]
        );

        const [role] = await mysqlServerConnection.query(
            `SELECT name FROM ${db_name}.roles WHERE id = ?`, [role_id]
        );

        const title = `Welcome to ${org[0].name}!`;
        const body = `Congratulations, ${name}! You have been assigned as ${role[0].name} for ${org[0].name}.`;

        const [newUser] = await mysqlServerConnection.query(
            `SELECT * FROM ${db_name}.users WHERE id = ?`, [result.insertId]
        );

        await SetNotification({
            dbName: db_name,
            userId: result.insertId,
            sendFrom: req.user.userId,
            title,
            body
        });

        if (notify_by_email) {
            sentEmailNotification(newUser[0].email, title, title, body);
        }

        sendAccountCreatedEmail(newUser[0].email, newUser[0].name, password, org[0].auth_sub_domain, org[0]);

        LogsHandler(db_name, req.user.userId, "Create Admin", `Admin user created with email ${email} and name ${name}`);

        res.status(201).json({
            success: true,
            message: `${name}'s account created successfully as ${role[0].name}`
        });

    } catch (error) {
        console.log(error);
        return res.status(500).json({ success: false, data: { error_msg: error.message || 'Internal Server Error' } });
    }
};





module.exports = {
    getUserDetails,
    createActiveUser,
    activeDeactiveUser,
    createOrganistionUser,
    deleteOrganisationUser,
    editOrganisationUser,
    getTraineeDataWithCertificateCount,
    getTrainerDetails,
    createAdminUser
}