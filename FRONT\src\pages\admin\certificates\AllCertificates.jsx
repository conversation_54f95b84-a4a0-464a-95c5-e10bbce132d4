import React from 'react'
import { useState, useEffect } from 'react'
import { toast } from 'react-toastify'
import mammoth from 'mammoth'
import Loader from '../../../components/common/Loader'
import { getOrgCertificate, deleteCertificateTemplate, createCertificate } from '../../../services/adminService'
import { Icon } from '@iconify/react'
import { usePermissions } from '../../../context/PermissionsContext';

// Toast configuration
const toastConfig = {
    position: "top-center",
    autoClose: 3000,
    hideProgressBar: false,
    closeOnClick: true,
    pauseOnHover: true,
    draggable: true,
    progress: undefined,
    theme: "light",
}

function AllCertificates() {
    const { permissions } = usePermissions();
    const [loading, setLoading] = useState(false)
    const [certificates, setCertificates] = useState([])
    const [page, setPage] = useState(1)
    const [limit, setLimit] = useState(10)
    const [selectedCertificate, setSelectedCertificate] = useState(null)
    const [showModal, setShowModal] = useState(false)
    const [showDeleteModal, setShowDeleteModal] = useState(false)
    const [deleteLoading, setDeleteLoading] = useState(false)
    const [certificateToDelete, setCertificateToDelete] = useState(null)
    const [showCreateModal, setShowCreateModal] = useState(false)
    const [createLoading, setCreateLoading] = useState(false)
    const [createForm, setCreateForm] = useState({
        templateFile: null,
        detectedTags: [],
        invalidTags: [],
        isProcessing: false
    })
    const [isDragging, setIsDragging] = useState(false)
    const [documentPreview, setDocumentPreview] = useState(null)
    const [showPreview, setShowPreview] = useState(false)
  
    const fetchCertificates = async () => {
      try {
        setLoading(true)
        const response = await getOrgCertificate({ page, limit })
        console.log('Certificates Data:', response)
  
        if (response.success) {
                setCertificates(response.data?.templates || [])
        } else {
          toast.error(response.message || 'Failed to fetch certificates', toastConfig)
        }
      } catch (error) {
        console.error('Error fetching certificates:', error)
        toast.error('Something went wrong while fetching certificates', toastConfig)
      } finally {
        setLoading(false)
      }
    }
  
    useEffect(() => {
      fetchCertificates()
    }, [page, limit])

    const handleViewCertificate = (certificate) => {
        setSelectedCertificate(certificate)
        setShowModal(true)
    }

    const handleDeleteClick = (certificate) => {
        if (!permissions.certificate_management_delete) {
            toast.warning("You don't have permission to delete certificates");
            return;
        }
        setCertificateToDelete(certificate)
        setShowDeleteModal(true)
    }

    const handleDeleteConfirm = async () => {
        try {
            setDeleteLoading(true)
            const response = await deleteCertificateTemplate(certificateToDelete.id)

            if (response.success) {
                toast.success('Certificate template deleted successfully', toastConfig)
                fetchCertificates() // Refresh the list
                setShowDeleteModal(false)
                setCertificateToDelete(null)
            } else {
                toast.error(response.message || 'Failed to delete certificate template', toastConfig)
            }
        } catch (error) {
            console.error('Error deleting certificate:', error)
            toast.error('Something went wrong while deleting the certificate', toastConfig)
        } finally {
            setDeleteLoading(false)
        }
    }

    const checkDocumentTags = async (file) => {
        try {
            setCreateForm(prev => ({
                ...prev,
                isProcessing: true,
                detectedTags: [],
                invalidTags: []
            }))

            const reader = new FileReader()
            reader.onload = async function(e) {
                const arrayBuffer = e.target.result
                
                const result = await mammoth.extractRawText({ arrayBuffer: arrayBuffer })
                const text = result.value

                // Define the valid tags
                const validTags = ['{NAME}', '{COURSE}', '{DATE}']
                const foundValidTags = []
                const foundInvalidTags = []

                // Check for valid tags
                validTags.forEach(tag => {
                    if (text.includes(tag)) {
                        foundValidTags.push(tag)
                    }
                })

                // Check for invalid tags using regex to find all {WORD} patterns
                const tagRegex = /\{[A-Z_]+\}/g
                const allTags = text.match(tagRegex) || []
                
                // Find invalid tags (tags that exist but are not in validTags)
                const uniqueTags = [...new Set(allTags)]
                uniqueTags.forEach(tag => {
                    if (!validTags.includes(tag)) {
                        foundInvalidTags.push(tag)
                    }
                })

                // Add a delay for smooth transition
                await new Promise(resolve => setTimeout(resolve, 2000))

                setCreateForm(prev => ({
                    ...prev,
                    detectedTags: foundValidTags,
                    invalidTags: foundInvalidTags,
                    isProcessing: false
                }))
            }
            reader.readAsArrayBuffer(file)
        } catch (error) {
            console.error('Error checking document tags:', error)
            toast.error('Error checking document tags', toastConfig)
            setCreateForm(prev => ({
                ...prev,
                isProcessing: false
            }))
        }
    }

    const handleFileChange = async (e) => {
        const file = e.target.files[0]
        if (!file) return

        // Validate file type
        if (!file.name.toLowerCase().endsWith('.docx')) {
            toast.error('Only .docx files are allowed', toastConfig)
            return
        }

        // Validate file size
        const maxSize = 30 * 1024 * 1024
        if (file.size > maxSize) {
            toast.error('File size must be less than 30MB', toastConfig)
            return
        }

        setCreateForm(prev => ({
            ...prev,
            templateFile: file,
            detectedTags: [],
            invalidTags: [] // Reset invalid tags when new file is uploaded
        }))

        // Check for tags in the document
        await checkDocumentTags(file)
    }

    const handleCreateSubmit = async () => {
        if (!permissions.certificate_management_create) {
            toast.warning("You don't have permission to create certificates");
            return;
        }
        try {
            setCreateLoading(true)

            // Add validation
            if (!createForm.templateFile) {
                toast.error('Please select a template file', toastConfig)
                return
            }

            // Check file type
            if (!createForm.templateFile.name.endsWith('.docx')) {
                toast.error('Please select a .docx file', toastConfig)
                return
            }

            // Check file size (30MB limit as per backend)
            const maxSize = 30 * 1024 * 1024; // 30MB
            if (createForm.templateFile.size > maxSize) {
                toast.error('File size should be less than 30MB', toastConfig)
                return
            }

            // Check for spaces in filename
            if (createForm.templateFile.name.includes(' ')) {
                toast.error('File name should not contain spaces', toastConfig)
                return
            }

            const formData = new FormData()
            formData.append("upload_file", createForm.templateFile)

            // Convert detected tags to required format
            const fields = createForm.detectedTags.map(tag => {
                const tagName = tag.replace(/[{}]/g, ''); // Remove curly braces from tag
                return {
                    inputName: tagName,
                    inputType: tagName === 'DATE' ? 'date' : 'text'
                }
            })

            formData.append("fields", JSON.stringify(fields))

            const response = await createCertificate(formData)

            if (response.success) {
                toast.success('Certificate template created successfully', toastConfig)
                setShowCreateModal(false)
                setCreateForm({
                    templateFile: null,
                    detectedTags: [],
                    invalidTags: [],
                    isProcessing: false
                })
                fetchCertificates()
            } else {
                toast.error(response.data?.error_msg || 'Failed to create certificate template', toastConfig)
            }
        } catch (error) {
            console.error('Error creating certificate:', error)
            if (error.response) {
                toast.error(error.response.data?.data?.error_msg || error.response.data?.message || 'Server error occurred', toastConfig)
            } else if (error.request) {
                toast.error('No response from server. Please check if the server is running.', toastConfig)
            } else {
                toast.error('Request failed: ' + error.message, toastConfig)
            }
        } finally {
            setCreateLoading(false)
        }
    }

    const handleCreateModalClose = () => {
        setShowCreateModal(false)
        setCreateForm({
            templateName: '',
            templateFile: null,
            detectedTags: [],
            invalidTags: [],
            isProcessing: false
        })
        setDocumentPreview(null)
        setShowPreview(false)
        setIsDragging(false)
    }

    const togglePreview = () => {
        setShowPreview(!showPreview)
    }

    const handleFileDragOver = (e) => {
        e.preventDefault()
        e.stopPropagation()
        e.currentTarget.classList.add('dragover')
    }

    const handleFileDragLeave = (e) => {
        e.preventDefault()
        e.stopPropagation()
        e.currentTarget.classList.remove('dragover')
    }

    const handleFileDrop = async (e) => {
        e.preventDefault()
        e.stopPropagation()
        e.currentTarget.classList.remove('dragover')

        const file = e.dataTransfer.files[0]
        if (!file) return

        // Validate file type
        if (!file.name.toLowerCase().endsWith('.docx')) {
            toast.error('Only .docx files are allowed', toastConfig)
            return
        }

        // Validate file size
        const maxSize = 30 * 1024 * 1024
        if (file.size > maxSize) {
            toast.error('File size must be less than 30MB', toastConfig)
            return
        }

        setCreateForm(prev => ({
            ...prev,
            templateFile: file,
            detectedTags: [],
            invalidTags: []
        }))

        // Check for tags in the document
        await checkDocumentTags(file)
    }

    if (loading) {
        return (
          <div className="d-flex justify-content-center align-items-center" style={{ minHeight: '400px' }}>
            <Loader />
          </div>
        )
      }
  
    return (
        <div className="container-fluid">
        <div className="row mb-4">
                <div className="col-12 d-flex justify-content-end ">
                  <div className="row d-flex justify-content-end">
                    <div className="col-12 d-flex justify-content-end">
                    <button 
                        className="btn btn-primary d-flex align-items-center gap-2"
                        onClick={() => setShowCreateModal(true)}
                        disabled={!permissions.certificate_management_create}
                    >
                        <Icon icon="solar:add-circle-linear" width="20" height="20" />
                        Create Certificate
                    </button>
                    </div>
                  </div>
                </div>
            </div>

            <div className="row g-4">
                {certificates.map((certificate) => (
                    <div key={certificate.id} className="col-12 col-sm-6 col-md-4 col-lg-3">
                        <div className="card h-100 border-0 shadow-sm rounded-3 overflow-hidden">
                            <div className="p-3">
                                <div className="bg-white rounded-3 mb-3">
                                    <img
                                        src={certificate.image_url}
                                        className="img-fluid rounded-2 w-100"
                                        alt={certificate.template_name}
                                        style={{ height: '240px', objectFit: 'cover' }}
                                    />
                                </div>
                                <div className="d-flex justify-content-center align-items-center gap-3 mb-3">
                                    <button
                                        className="btn border border-dark btn-outline-dark p-1 rounded-2 d-flex align-items-center justify-content-center"
                                        style={{ width: '36px', height: '36px' }}
                                        title="View Certificate"
                                        onClick={() => handleViewCertificate(certificate)}
                                    >
                                        <Icon icon="solar:eye-linear" width="20" height="20" />
                                    </button>
                                    {/* <button
                                        className="btn border border-dark btn-outline-dark p-1 rounded-2 d-flex align-items-center justify-content-center"
                                        style={{ width: '36px', height: '36px' }}
                                        title="Edit Certificate"
                                        disabled={!permissions.certificate_management_update}
                                    >
                                        <Icon icon="solar:pen-linear" width="20" height="20" />
                                    </button> */}
                                    <button
                                        className="btn border border-danger btn-outline-danger p-1 rounded-2 d-flex align-items-center justify-content-center"
                                        style={{ width: '36px', height: '36px' }}
                                        title="Delete Certificate"
                                        onClick={() => handleDeleteClick(certificate)}
                                        disabled={!permissions.certificate_management_delete}
                                    >
                                        <Icon icon="solar:trash-bin-trash-linear" width="20" height="20" />
                                    </button>
                                </div>
                                <h6 className="text-truncate text-center mb-0 fs-6">
                                    {certificate.template_name}
                                </h6>
                            </div>
                        </div>
                    </div>
                ))}

                {certificates.length === 0 && (
                    <div className="col-12">
                        <div className="alert alert-info text-center">
                            No certificates found
                        </div>
                    </div>
                )}
            </div>

            {/* View Certificate Modal */}
            {showModal && selectedCertificate && (
                <div className="modal show d-block" style={{ backgroundColor: 'rgba(0,0,0,0.5)' }} tabIndex="-1">
                    <div className="modal-dialog modal-dialog-centered modal-lg">
                        <div className="modal-content border-0">
                            <div className="modal-header border-0">
                                <h5 className="modal-title">{selectedCertificate.template_name}</h5>
                                <button
                                    type="button"
                                    className="btn-close"
                                    onClick={() => {
                                        setShowModal(false)
                                        setSelectedCertificate(null)
                                    }}
                                ></button>
                            </div>
                            <div className="modal-body p-0">
                                <div className="p-3 bg-light rounded-3">
                                    <img
                                        src={selectedCertificate.image_url}
                                        alt={selectedCertificate.template_name}
                                        className="img-fluid w-100 rounded-2"
                                        style={{ maxHeight: '70vh', objectFit: 'contain' }}
                                    />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            )}

            {/* Delete Confirmation Modal */}
            {showDeleteModal && certificateToDelete && (
                <div className="modal show d-block" style={{ backgroundColor: 'rgba(0,0,0,0.5)' }} tabIndex="-1">
                    <div className="modal-dialog modal-dialog-centered">
                        <div className="modal-content border-0">
                            <div className="modal-header border-0">
                                <h5 className="modal-title">Delete Certificate</h5>
                                <button
                                    type="button"
                                    className="btn-close"
                                    onClick={() => {
                                        setShowDeleteModal(false)
                                        setCertificateToDelete(null)
                                    }}
                                    disabled={deleteLoading}
                                ></button>
                            </div>
                            <div className="modal-body text-center">
                                <div className="mb-3">
                                    <Icon icon="solar:trash-bin-trash-linear" className="text-danger" width="48" height="48" />
                                </div>
                                <h5>Are you sure?</h5>
                                <p className="text-muted mb-0">
                                    Do you really want to delete "{certificateToDelete.template_name}"? <br />
                                    This action cannot be undone.
                                </p>
                            </div>
                            <div className="modal-footer border-0">
                                <button
                                    type="button"
                                    className="btn btn-secondary"
                                    onClick={() => {
                                        setShowDeleteModal(false)
                                        setCertificateToDelete(null)
                                    }}
                                    disabled={deleteLoading}
                                >
                                    Cancel
                                </button>
                                <button
                                    type="button"
                                    className="btn btn-danger d-flex align-items-center gap-2"
                                    onClick={handleDeleteConfirm}
                                    disabled={deleteLoading}
                                >
                                    {deleteLoading ? (
                                        <>
                                            <span className="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                            Deleting...
                                        </>
                                    ) : (
                                        <>
                                            <Icon icon="solar:trash-bin-trash-linear" width="18" height="18" />
                                            Delete Certificate
                                        </>
                                    )}
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            )}

            {/* Create Certificate Modal */}
            {showCreateModal && (
                <div className="modal show d-block" style={{ backgroundColor: 'rgba(0,0,0,0.5)' }} tabIndex="-1">
                    <div className="modal-dialog modal-dialog-centered modal-lg">
                        <div className="modal-content border-0">
                            <div className="modal-header border-0">
                                <h5 className="modal-title">Create Certificate Template</h5>
                                <button
                                    type="button"
                                    className="btn-close"
                                    onClick={() => setShowCreateModal(false)}
                                    disabled={createLoading}
                                ></button>
                            </div>
                            <div className="modal-body">
                                <form>
                                    <div className="mb-3">
                                        <label className="form-label">
                                            Upload Document (.docx only) <span className="text-danger">*</span>
                                        </label>
                                        <ul className="text-muted small mb-3" style={{fontSize: '18px'}}>
                                            <li>Document name will be used as the template name</li>
                                            <li>File name must not contain spaces</li>
                                            <li>Fields must not be empty or undefined</li>
                                            <li>File size must be under 25 MB</li>
                                            <li>File must have a .docx extension</li>
                                            <li>File name must contain only one dot (.)</li>
                                            <li>Use only {'{NAME}'}, {'{COURSE}'}, and {'{DATE}'} in your .docx file.</li>
                                            </ul>

                                        {!createForm.templateFile ? (
                                            <div
                                                className={`border-2 border-dashed rounded-3 p-4 text-center position-relative ${
                                                    isDragging ? 'border-primary bg-light' : 'border-secondary'
                                                }`}
                                                style={{
                                                    minHeight: '200px',
                                                    cursor: 'pointer',
                                                    transition: 'all 0.3s ease',
                                                    border: '2px dashed #ccc',
                                                    backgroundColor: '#f8f9fa'
                                                }}
                                                onDragOver={handleFileDragOver}
                                                onDragLeave={handleFileDragLeave}
                                                onDrop={handleFileDrop}
                                                onClick={() => document.getElementById('certificateTemplate').click()}
                                            >
                                                <input
                                                    type="file"
                                                    id="certificateTemplate"
                                                    className="d-none"
                                                    accept=".docx"
                                                    onChange={handleFileChange}
                                                    disabled={createLoading}
                                                />
                                                <div className="d-flex flex-column align-items-center justify-content-center h-100">
                                                    <Icon
                                                        icon="solar:upload-linear"
                                                        width="48"
                                                        height="48"
                                                        className="text-primary mb-3"
                                                        style={{ color: '#0d6efd' }}
                                                    />
                                                    <h6 className="mb-2">Drag and drop your certificate template here</h6>
                                                    <p className="text-muted mb-2">or click to browse</p>
                                                    <small className="text-muted">Only .docx files allowed • Maximum file size: 25MB</small>
                                                </div>
                                            </div>
                                        ) : (
                                            <div className="border rounded-3 p-3">
                                                <div className="d-flex align-items-center justify-content-between">
                                                    <div className="d-flex align-items-center gap-2">
                                                        <Icon icon="vscode-icons:file-type-word" width="24" height="24" />
                                                        <div>
                                                            <div className="fw-medium">{createForm.templateFile.name}</div>
                                                            <small className="text-muted">
                                                                {(createForm.templateFile.size / (1024 * 1024)).toFixed(2)} MB
                                                            </small>
                                                        </div>
                                                    </div>
                                                    <button
                                                        type="button"
                                                        className="border-0 btn-outline-danger btn-sm"
                                                        onClick={() => {
                                                            setCreateForm(prev => ({ ...prev, templateFile: null }))
                                                        }}
                                                        disabled={createLoading}
                                                    >
                                                        <Icon icon="solar:trash-bin-trash-linear" width="16" height="16" />
                                                    </button>
                                                </div>
                                            </div>
                                        )}
                                    </div>

                                    {/* Tags processing and display section */}
                                    {createForm.templateFile && (
                                        <div className="mt-3">
                                            {createForm.isProcessing ? (
                                                <div className="text-center py-3">
                                                    <div className="spinner-border text-primary" role="status">
                                                        <span className="visually-hidden">Loading...</span>
                                                    </div>
                                                    <p className="text-muted mt-2 mb-0">Processing document tags...</p>
                                                </div>
                                            ) : (
                                                <>
                                                    <p className="mb-2 text-secondary fw-medium">Tags found in document:</p>
                                                    {/* Valid tags section */}
                                                    {createForm.detectedTags.length > 0 && (
                                                        <>
                                                            <div className="d-flex flex-wrap gap-2 mb-2">
                                                                {createForm.detectedTags.map((tag, index) => (
                                                                    <span key={index} className="badge bg-success-subtle text-success px-3 py-2 rounded-pill">
                                                                        {tag}
                                                                    </span>
                                                                ))}
                                                            </div>
                                                            <div className="alert alert-light border p-2 mb-2">
                                                                <small className="text-muted">
                                                                    These tags will be replaced with actual values when generating certificates.
                                                                </small>
                                                            </div>
                                                        </>
                                                    )}

                                                    {/* Invalid tags section */}
                                                    {createForm.invalidTags.length > 0 && (
                                                        <>
                                                            <p className="mb-2 text-danger fw-medium">Invalid tags found:</p>
                                                            <div className="d-flex flex-wrap gap-2 mb-2">
                                                                {createForm.invalidTags.map((tag, index) => (
                                                                    <span key={index} className="badge bg-danger-subtle text-danger px-3 py-2 rounded-pill">
                                                                        {tag}
                                                                    </span>
                                                                ))}
                                                            </div>
                                                            <div className="alert alert-danger d-flex align-items-center" role="alert">
                                                                <i className="bi bi-exclamation-triangle me-2"></i>
                                                                <div>
                                                                    These are invalid tags. Please remove them from your document and reload. Only use <span className="fw-medium">{'{NAME}'}</span>, <span className="fw-medium">{'{COURSE}'}</span>, or <span className="fw-medium">{'{DATE}'}</span>.
                                                                </div>
                                                            </div>
                                                        </>
                                                    )}

                                                    {/* No tags found */}
                                                    {createForm.detectedTags.length === 0 && createForm.invalidTags.length === 0 && (
                                                        <div className="alert alert-warning d-flex align-items-center" role="alert">
                                                            <i className="bi bi-exclamation-triangle me-2"></i>
                                                            <div>
                                                                No valid tags found. Please use <span className="fw-medium">{'{NAME}'}</span>, <span className="fw-medium">{'{COURSE}'}</span>, or <span className="fw-medium">{'{DATE}'}</span> in your document.
                                                            </div>
                                                        </div>
                                                    )}
                                                </>
                                            )}
                                        </div>
                                    )}
                                </form>
                            </div>
                            <div className="modal-footer">
                                <button
                                    type="button"
                                    className="btn btn-light"
                                    onClick={() => setShowCreateModal(false)}
                                >
                                    Cancel
                                </button>
                                <button
                                    type="button"
                                    className="btn btn-primary d-flex align-items-center gap-2"
                                    onClick={handleCreateSubmit}
                                    disabled={
                                        !createForm.templateFile || 
                                        createForm.detectedTags.length === 0 || 
                                        createForm.invalidTags.length > 0 ||
                                        createLoading ||
                                        createForm.isProcessing
                                    }
                                >
                                    {createLoading ? (
                                        <>
                                            <span className="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                            <span>Creating...</span>
                                        </>
                                    ) : (
                                        <>
                                            <i className="bi bi-plus-circle"></i>
                                            Create Certificate
                                        </>
                                    )}
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            )}

      </div>
    )
}

export default AllCertificates
