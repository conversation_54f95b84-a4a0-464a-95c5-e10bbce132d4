const { mysqlServerConnection } = require("../db");
const fs = require("fs");
const path = require("path");

const maindb = process.env.MAIN_DB;

/**
 * Migration script to add hierarchical permission tables to all organization databases
 */
async function addHierarchicalPermissions() {
  const connection = await mysqlServerConnection.getConnection();

  try {
    console.log("🚀 Starting hierarchical permissions migration...");

    // Step 1: Execute the main hierarchical permissions script for auth database
    console.log(
      "📝 Creating hierarchical permission tables in auth database..."
    );

    const hierarchicalPermissionsSQL = fs.readFileSync(
      path.join(__dirname, "../utils/hierarchical_permissions.sql"),
      "utf8"
    );

    // Split SQL file by statements and execute each one
    const statements = hierarchicalPermissionsSQL
      .split(";")
      .map((stmt) => stmt.trim())
      .filter((stmt) => stmt.length > 0);

    for (const statement of statements) {
      if (statement.trim()) {
        try {
          await connection.query(statement);
          console.log("✅ Executed statement successfully");
        } catch (error) {
          if (
            error.code === "ER_TABLE_EXISTS_ERROR" ||
            error.code === "ER_DUP_ENTRY"
          ) {
            console.log("⚠️  Table/data already exists, skipping...");
          } else {
            console.error("❌ Error executing statement:", error.message);
            throw error;
          }
        }
      }
    }

    // Step 2: Get all organization databases
    console.log("🔍 Fetching organization databases...");
    const [organizations] = await connection.query(`
            SELECT db_name FROM ${maindb}.organization WHERE is_active = 1
        `);

    console.log(`📊 Found ${organizations.length} organization databases`);

    // Step 3: Update each organization database
    for (const org of organizations) {
      const dbName = org.db_name;
      console.log(`🔧 Updating database: ${dbName}`);

      try {
        // Create hierarchical_role_permissions table
        await connection.query(`
                    CREATE TABLE IF NOT EXISTS ${dbName}.hierarchical_role_permissions (
                        role_id   INT NOT NULL,
                        module_id INT NOT NULL,
                        action_id INT NOT NULL,
                        created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                        PRIMARY KEY(role_id, module_id, action_id),
                        FOREIGN KEY(role_id) REFERENCES ${dbName}.roles(id) ON DELETE CASCADE,
                        FOREIGN KEY(module_id) REFERENCES ${maindb}.permission_modules(id) ON DELETE CASCADE,
                        FOREIGN KEY(action_id) REFERENCES ${maindb}.actions(id) ON DELETE CASCADE
                    )
                `);

        // Create indexes for better performance
        await connection.query(`
                    CREATE INDEX IF NOT EXISTS idx_hierarchical_role_permissions_role_id 
                    ON ${dbName}.hierarchical_role_permissions(role_id)
                `);

        await connection.query(`
                    CREATE INDEX IF NOT EXISTS idx_hierarchical_role_permissions_module_id 
                    ON ${dbName}.hierarchical_role_permissions(module_id)
                `);

        await connection.query(`
                    CREATE INDEX IF NOT EXISTS idx_hierarchical_role_permissions_action_id 
                    ON ${dbName}.hierarchical_role_permissions(action_id)
                `);

        console.log(`✅ Successfully updated ${dbName}`);
      } catch (error) {
        if (error.code === "ER_TABLE_EXISTS_ERROR") {
          console.log(`⚠️  Tables already exist in ${dbName}, skipping...`);
        } else {
          console.error(`❌ Error updating ${dbName}:`, error.message);
          // Continue with other databases instead of failing completely
        }
      }
    }

    console.log(
      "🎉 Hierarchical permissions migration completed successfully!"
    );
  } catch (error) {
    console.error("💥 Migration failed:", error);
    throw error;
  } finally {
    connection.release();
  }
}

/**
 * Rollback function to remove hierarchical permission tables
 */
async function rollbackHierarchicalPermissions() {
  const connection = await mysqlServerConnection.getConnection();

  try {
    console.log("🔄 Starting hierarchical permissions rollback...");

    // Get all organization databases
    const [organizations] = await connection.query(`
            SELECT db_name FROM ${maindb}.organization WHERE is_active = 1
        `);

    // Remove hierarchical_role_permissions table from each organization database
    for (const org of organizations) {
      const dbName = org.db_name;
      console.log(`🗑️  Removing hierarchical tables from: ${dbName}`);

      try {
        await connection.query(
          `DROP TABLE IF EXISTS ${dbName}.hierarchical_role_permissions`
        );
        console.log(`✅ Successfully removed tables from ${dbName}`);
      } catch (error) {
        console.error(
          `❌ Error removing tables from ${dbName}:`,
          error.message
        );
      }
    }

    // Remove from auth database
    console.log("🗑️  Removing hierarchical tables from auth database...");
    await connection.query(
      `DROP TABLE IF EXISTS ${maindb}.hierarchical_role_permissions`
    );
    await connection.query(`DROP TABLE IF EXISTS ${maindb}.permission_modules`);

    console.log("🎉 Hierarchical permissions rollback completed!");
  } catch (error) {
    console.error("💥 Rollback failed:", error);
    throw error;
  } finally {
    connection.release();
  }
}

// Export functions for use in migration scripts
module.exports = {
  addHierarchicalPermissions,
  rollbackHierarchicalPermissions,
};

// If this script is run directly, execute the migration
if (require.main === module) {
  const command = process.argv[2];

  if (command === "rollback") {
    rollbackHierarchicalPermissions()
      .then(() => {
        console.log("Rollback completed successfully");
        process.exit(0);
      })
      .catch((error) => {
        console.error("Rollback failed:", error);
        process.exit(1);
      });
  } else {
    addHierarchicalPermissions()
      .then(() => {
        console.log("Migration completed successfully");
        process.exit(0);
      })
      .catch((error) => {
        console.error("Migration failed:", error);
        process.exit(1);
      });
  }
}
