.notification-details-container {
  /* padding: 20px; */
  /* max-width: 800px; */
  margin: 0 auto;
}

.notification-details-header {
  display: flex;
  align-items: center;
  margin-bottom: 24px;
  gap: 20px;
}

.notification-details-header h2 {
  margin: 0;
  font-size: 24px;
  color: #333;
}

.back-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border: none;
  background: #f5f5f5;
  border-radius: 8px;
  color: #333;
  cursor: pointer;
  transition: all 0.2s ease;
}

.back-button:hover {
  background: #e5e5e5;
}

.notification-details-card {
  background: #fff;
  border-radius: 10px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.notification-details-content {
  margin-bottom: 30px;
}

.detail-group {
  margin-bottom: 20px;
}

.detail-group label {
  font-weight: 600;
  color: #666;
  display: block;
  margin-bottom: 5px;
}

.detail-group h3 {
  margin: 0;
  font-size: 20px;
  color: #333;
}

.detail-group p {
  margin: 0;
  font-size: 16px;
  color: #444;
  line-height: 1.5;
}

.status-badge {
  display: inline-flex;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 14px;
  font-weight: 500;
}

.status-badge.unread {
  background: #e8f0ff;
  color: #4A6CF7;
}

.status-badge.read {
  background: #f0f0f0;
  color: #666;
}

.attachment-link {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  color: #4070f4;
  text-decoration: none;
}

.attachment-link:hover {
  text-decoration: underline;
}

.notification-details-main {
  display: flex;
  gap: 16px;
  margin-bottom: 24px;
}

.notification-details-icon {
  width: 56px;
  height: 56px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.notification-details-title {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin: 0 0 8px 0;
}

.notification-details-message {
  font-size: 15px;
  color: #555;
  margin: 0 0 12px 0;
  line-height: 1.5;
}

.notification-details-meta {
  display: flex;
  align-items: center;
  gap: 16px;
}

.notification-details-date {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 13px;
  color: #777;
}

.notification-details-divider {
  height: 1px;
  background-color: #eee;
  margin: 0 -24px 24px;
}

.notification-details-info h4 {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0 0 16px 0;
}

.notification-details-fields {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 24px;
}

.detail-row {
  display: flex;
  align-items: flex-start;
}

.detail-label {
  width: 160px;
  font-weight: 500;
  color: #666;
  flex-shrink: 0;
}

.detail-value {
  flex: 1;
  color: #333;
}

.detail-action {
  margin-top: 20px;
}

.join-class-btn,
.add-calendar-btn,
.start-assessment-btn,
.view-certificate-btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  background-color: #3152e8;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  text-decoration: none;
  transition: background-color 0.2s;
}

.join-class-btn:hover,
.add-calendar-btn:hover,
.start-assessment-btn:hover,
.view-certificate-btn:hover {
  background-color: #2642d0;
}

.notification-additional-info {
  background-color: #f8f9fa;
  border-radius: 6px;
  padding: 16px;
}

.notification-additional-info h4 {
  font-size: 15px;
  margin: 0 0 8px 0;
}

.notification-additional-info p {
  font-size: 14px;
  color: #555;
  margin: 0;
  line-height: 1.5;
}

.notification-details-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
  font-size: 16px;
  color: #666;
}

.notification-not-found {
  text-align: center;
  padding: 40px 20px;
}

.notification-not-found h2 {
  font-size: 24px;
  color: #333;
  margin-bottom: 12px;
}

.notification-not-found p {
  font-size: 16px;
  color: #666;
  margin-bottom: 24px;
}

.back-to-notifications {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  background-color: #f0f0f0;
  color: #333;
  border-radius: 6px;
  text-decoration: none;
  font-weight: 500;
  transition: background-color 0.2s;
}

.back-to-notifications:hover {
  background-color: #e0e0e0;
}

/* Notification Preview Section */
.notification-preview-card {
  background: #fff;
  border-radius: 15px;
  overflow: hidden;
  margin-top: 30px;
  transition: transform 0.3s ease;
}

.notification-preview-card:hover {
  transform: translateY(-5px);
}

.notification-preview-image {
  width: 100%;
  height: 250px;
  object-fit: cover;
}

.notification-preview-content {
  padding: 20px;
}

.notification-preview-title {
  color: #333;
  font-size: 1.5rem;
  margin-bottom: 10px;
}

.notification-preview-description {
  color: #666;
  margin-bottom: 20px;
}

.notification-preview-instructor {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 20px;
}

.instructor-icon {
  color: #4070f4;
}

.start-learning-btn {
  background-color: #4070f4;
  color: white;
  padding: 10px 25px;
  border-radius: 25px;
  border: none;
  font-weight: 500;
  transition: background-color 0.3s ease;
}

.start-learning-btn:hover {
  background-color: #3060e0;
  color: white;
}

@media (max-width: 768px) {
  .notification-details-container {
    /* padding: 16px; */
  }
  
  .notification-details-card {
    padding: 16px;
  }
  
  .notification-details-divider {
    margin: 0 -16px 20px;
  }
  
  .detail-row {
    flex-direction: column;
  }
  
  .detail-label {
    width: 100%;
    margin-bottom: 4px;
  }
}
