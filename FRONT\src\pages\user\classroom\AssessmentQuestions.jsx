import React, { useState } from 'react';
import { Icon } from '@iconify/react';

function AssessmentQuestions() {
  const [selectedFile, setSelectedFile] = useState(null);
  const [answerText, setAnswerText] = useState('');

  const questions = [
    {
      id: 1,
      type: 'text',
      question: "What is Bomb threat?",
      status: "Right",
      isSubmitted: true,
      submittedAnswer: {
        type: 'text',
        content: 'test'
      }
    },
    {
      id: 2,
      type: 'image_text',
      question: "Identify the security breach in this image:",
      imageUrl: "https://example.com/security-image.jpg",
      status: "Pending",
      isSubmitted: false,
      allowedAnswerType: 'image'
    },
    {
      id: 3,
      type: 'audio_text',
      question: "Listen to the audio and identify the emergency signal:",
      audioUrl: "https://example.com/emergency-signal.mp3",
      status: "Pending",
      isSubmitted: false,
      allowedAnswerType: 'audio'
    }
  ];

  const handleFileUpload = (event, type) => {
    const file = event.target.files[0];
    if (file) {
      // Validate file type
      if (type === 'image' && !file.type.startsWith('image/')) {
        alert('Please upload an image file');
        return;
      }
      if (type === 'audio' && !file.type.startsWith('audio/')) {
        alert('Please upload an audio file');
        return;
      }
      setSelectedFile(file);
    }
  };

  const renderQuestionMedia = (question) => {
    switch (question.type) {
      case 'image_text':
        return (
          <div className="mb-3">
            <img
              src={question.imageUrl}
              alt="Question"
              className="img-fluid rounded"
              style={{ maxHeight: '300px' }}
            />
          </div>
        );
      case 'audio_text':
        return (
          <div className="mb-3">
            <audio controls className="w-100">
              <source src={question.audioUrl} type="audio/mpeg" />
              Your browser does not support the audio element.
            </audio>
          </div>
        );
      default:
        return null;
    }
  };

  const renderAnswerSection = (question) => {
    if (question.isSubmitted) {
      return (
        <div className="p-3 bg-light rounded">
          <div className="d-flex align-items-center mb-2">
            <span className="badge bg-success me-2">Submitted Response</span>
          </div>
          {question.submittedAnswer.type === 'text' && (
            <p className="mb-0">{question.submittedAnswer.content}</p>
          )}
          {question.submittedAnswer.type === 'image' && (
            <img
              src={question.submittedAnswer.content}
              alt="Submitted Answer"
              className="img-fluid rounded"
              style={{ maxHeight: '200px' }}
            />
          )}
          {question.submittedAnswer.type === 'audio' && (
            <audio controls className="w-100">
              <source src={question.submittedAnswer.content} type="audio/mpeg" />
              Your browser does not support the audio element.
            </audio>
          )}
        </div>
      );
    }

    return (
      <div className="answer-section">
        {/* Text Answer Input */}
        <div className="mb-3">
          <textarea
            className="form-control"
            rows="3"
            placeholder="Type your answer here..."
            value={answerText}
            onChange={(e) => setAnswerText(e.target.value)}
          ></textarea>
        </div>

        {/* File Upload Option - Either Image or Audio based on question type */}
        <div className="d-flex gap-2 mb-3">
          {question.allowedAnswerType === 'image' && (
            <button
              className="btn btn-outline-primary d-flex align-items-center gap-2"
              onClick={() => document.getElementById(`file-upload-${question.id}`).click()}
            >
              <Icon icon="mdi:image" width="20" height="20" />
              Upload Image
              <input
                id={`file-upload-${question.id}`}
                type="file"
                accept="image/*"
                onChange={(e) => handleFileUpload(e, 'image')}
                style={{ display: 'none' }}
              />
            </button>
          )}

          {question.allowedAnswerType === 'audio' && (
            <button
              className="btn btn-outline-primary d-flex align-items-center gap-2"
              onClick={() => document.getElementById(`file-upload-${question.id}`).click()}
            >
              <Icon icon="mdi:microphone" width="20" height="20" />
              Upload Audio
              <input
                id={`file-upload-${question.id}`}
                type="file"
                accept="audio/*"
                onChange={(e) => handleFileUpload(e, 'audio')}
                style={{ display: 'none' }}
              />
            </button>
          )}
        </div>

        <div className='d-flex justify-content-end'>

          <div className="d-flex justify-content-end mt-2 ">
            <button className="btn btn-primary btn-sm px-4 w-100 w-md-25">
              Submit Answer
            </button>
          </div>
        </div>

      </div>
    );
  };

  return (
    <div className="row">
      {/* Header with Question Stats */}
      <div className="d-flex flex-wrap flex-md-row justify-content-between align-items-center mb-4 ">
        <h4 className="mb-0 d-none d-md-block">Assignment Questions</h4>
        <div className="d-flex gap-2 flex-wrap">
          <span className="badge bg-primary d-flex align-items-center gap-1">
            <Icon icon="mdi:help-circle" width="18" height="18" />
            {questions.length} Questions
          </span>
          <span className="badge bg-success d-flex align-items-center gap-1">
            <Icon icon="mdi:check-circle" width="18" height="18" />
            {questions.filter(q => q.isSubmitted).length} Submitted
          </span>
          <span className="badge bg-warning d-flex align-items-center gap-1">
            <Icon icon="mdi:clock" width="18" height="18" />
            {questions.filter(q => !q.isSubmitted).length} Remaining
          </span>
        </div>
      </div>

      {/* Questions List */}
      {questions.map((question) => (
        <div key={question.id} className="card mb-4 border shadow-sm">
          <div className="card-body">
            {/* Question Header */}
            <div className="d-flex justify-content-between align-items-start mb-4">
              <h5 className="card-title mb-0">Question {question.id}</h5>
              {question.status === "Right" && (
                <span className="badge bg-success">Right</span>
              )}
            </div>

            {/* Question Content */}
            <div className="mb-4">
              <p className="card-text">{question.question}</p>
              {renderQuestionMedia(question)}
            </div>

            {/* Response Section */}
            <div>
              <h6 className="mb-3">Your Response</h6>
              {renderAnswerSection(question)}
            </div>

            {/* Submission Status */}
            {question.isSubmitted && (
              <div className="mt-4 d-flex justify-content-end">
                <span className="text-muted d-flex align-items-center">
                  <Icon icon="mdi:check-circle" className="text-success me-2" width="20" height="20" />
                  Response Submitted
                </span>
              </div>
            )}
          </div>
        </div>
      ))}
    </div>
  );
}

export default AssessmentQuestions;