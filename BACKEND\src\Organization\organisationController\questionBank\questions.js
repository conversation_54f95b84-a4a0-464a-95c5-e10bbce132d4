const { mysqlServerConnection } = require('../../../db/db');
// const { LogsHandler } = require('../../../tools/tools');

const getQuestionTags = async (req, res) => {
    try {
        const dbName = req.user.db_name;
        const [tags] = await mysqlServerConnection.query(
            `SELECT * FROM ${dbName}.question_tags`
        );
        res.status(200).json({
            success: true,
            status: 200,
            message: 'Question tags fetched successfully',
            data: tags
        });
    } catch (error) {
        console.error('Error fetching question tags:', error);
        res.status(500).json({
            status: 500,
            success: false,
            message: 'An error occurred while fetching question tags',
            error: error.message
        });
    }
};


const deleteQuestionTag = async (req, res) => {
    try {
        const dbName = req.user.db_name;
        const { id } = req.body;

        console.log(`[DELETE TAG] Requested by user with DB: ${dbName}`);
        console.log(`[DELETE TAG] Tag ID received: ${id}`);

        if (!id) {
            console.warn('[DELETE TAG] No ID provided in request');
            return res.status(400).json({
                success: false,
                status: 400,
                message: 'Tag ID is required'
            });
        }

        const deleteQuery = `DELETE FROM ${dbName}.question_tags WHERE id = ?`;
        console.log(`[DELETE TAG] Executing query: ${deleteQuery} with ID: ${id}`);

        const [result] = await mysqlServerConnection.query(deleteQuery, [id]);

        console.log(`[DELETE TAG] Query result:`, result);

        if (result.affectedRows === 0) {
            console.warn('[DELETE TAG] No rows affected — tag not found');
            return res.status(404).json({
                success: false,
                status: 404,
                message: 'Tag not found or already deleted'
            });
        }

        console.log('[DELETE TAG] Tag deleted successfully');
        res.status(200).json({
            success: true,
            status: 200,
            message: 'Tag deleted successfully'
        });
    } catch (error) {
        console.error('[DELETE TAG] Error deleting question tag:', error);
        res.status(500).json({
            status: 500,
            success: false,
            message: 'An error occurred while deleting the tag',
            error: error.message
        });
    }
};





const qbCreateQuestion = async (req, res, next) => {
    try {
        const { question_name, question_type, options, subject, modules, tags } = req.body;
        const dbName = req.user.db_name;

        // Validate required fields
        if (!question_name || !question_type || !options || !Array.isArray(options) || options.length === 0|| tags.length === 0) {
            return res.status(400).json({
                success: false,
                message: 'Question name, type, and at least one option are required'
            });
        }

        // Validate at least one correct option
        const hasCorrectOption = options.some(option => option.is_correct === true);
        if (!hasCorrectOption) {
            return res.status(400).json({
                success: false,
                message: 'At least one option must be marked as correct'
            });
        }

        // Validate tags
        if (!tags || !Array.isArray(tags) || tags.length === 0) {
            return res.status(400).json({
                success: false,
                message: 'Tags are required and must be an array'
            });
        }
        // check question_tags
        const [existingTags] = await mysqlServerConnection.query(
            `SELECT * FROM ${dbName}.question_tags WHERE tag_name IN (?)`,
            [tags]
        );

        // Insert only new tags
        const existingTagNames = existingTags.map(tag => tag.tag_name);
        for (let i = 0; i < tags.length; i++) {
            if (!existingTagNames.includes(tags[i])) {
                await mysqlServerConnection.query(
                    `INSERT INTO ${dbName}.question_tags (tag_name) VALUES (?)`,
                    [tags[i]]
                );
            }
        }
        console.log(tags);
        // Insert question
        const [questionResult] = await mysqlServerConnection.query(
            `INSERT INTO ${dbName}.question_bank (question_name, question_type, subject, module, tags) 
            VALUES (?, ?, ?, ?, ?)`,
            [question_name, question_type, subject, modules, JSON.stringify(tags)]
        );

        console.log(questionResult);
        const questionId = questionResult.insertId;
        // Insert options
        for (let i = 0; i < options.length; i++) {
            const option = options[i];
            await mysqlServerConnection.query(
                `INSERT INTO ${dbName}.question_bank_options 
                (question_id, option_number, option_value, is_correct, is_active) 
                VALUES (?, ?, ?, ?, ?)`,
                [
                    questionId,
                    i + 1, // option_number (1-based index)
                    option.option_value,
                    option.is_correct || false,
                    option.is_active !== undefined ? option.is_active : true
                ]
            );
        }

        // Fetch the created question with options
        const [question] = await mysqlServerConnection.query(
            `SELECT * FROM ${dbName}.question_bank WHERE id = ?`,
            [questionId]
        );

        const [questionOptions] = await mysqlServerConnection.query(
            `SELECT id, option_number, option_value, is_correct, is_active 
            FROM ${dbName}.question_bank_options 
            WHERE question_id = ?`,
            [questionId]
        );

        res.status(201).json({
            success: true,
            status: 201,
            message: 'Question created successfully',
            data: {
                ...question[0],
                options: questionOptions
            }
        });

    } 
    catch (error) {
        console.error('Error creating question:', error);
        res.status(500).json({
            status: 500,
            success: false,
            message: 'An error occurred while creating the question',
            error: error.message
        });
    }
};

const qbUpdateQuestion = async (req, res, next) => {
    try {
        console.log("=== FUNCTION START: qbUpdateQuestion ===");
        console.log(req.body);
        let { question_id, question_name, question_type, subject, modules, tags, is_active, options } = req.body;
        const dbName = req.user.db_name;
        console.log("Database name:", dbName);

        // Validate required fields
        console.log("Validating required fields...");
        if (!question_id || !question_name || !question_type || !options || !Array.isArray(options)) {
            console.log("Validation failed: Missing required fields");
            return res.status(400).json({
                success: false,
                message: 'Question ID, name, type, and options are required'
            });
        }
        console.log("Required fields validation passed");

        console.log("Options", options);
        console.log("Tags", tags);
        console.log("Is active", is_active);
        console.log("Question id", question_id);
        
        // Validate at least one correct option if options are provided
        console.log("Validating correct option...");
        if (options.length > 0) {
            const hasCorrectOption = options.some(option => 
                option.is_correct === true || option.is_correct === 1 || option.is_correct === '1'
            );
            console.log("Has correct option:", hasCorrectOption);
            if (!hasCorrectOption) {
                console.log("Validation failed: No correct option");
                return res.status(400).json({
                    success: false,
                    message: 'At least one option must be marked as correct'
                });
            }
        }
        console.log("Correct option validation passed");
        
        console.log("About to check if question exists...");
        console.log("Question id", question_id);
        
        // Check if question exists
        // Declare existingQuestion at a higher scope
        let existingQuestion;

        try {
            console.log("About to execute query with question_id:", question_id, "type:", typeof question_id);
            
            // Make sure question_id is a number
            const numericQuestionId = Number(question_id);
            console.log("Converted question_id:", numericQuestionId);
            
            console.log("Executing database query...");
            const [result] = await mysqlServerConnection.query(
                `SELECT * FROM ${dbName}.question_bank WHERE id = ? AND is_deleted = 0`,
                [numericQuestionId]
            );
            
            existingQuestion = result;
            
            console.log("Query executed successfully");
            console.log("Question found", existingQuestion);
            
            if (existingQuestion.length === 0) {
                console.log("No question found with ID:", numericQuestionId);
                return res.status(404).json({
                    success: false,
                    message: 'Question not found or has been deleted'
                });
            }
        } catch (error) {
            console.error("Error querying for existing question:", error);
            return res.status(500).json({
                success: false,
                message: 'Error checking if question exists',
                error: error.message
            });
        }

        console.log("Question id", question_id);
        console.log("Question found", existingQuestion);
        if (existingQuestion.length === 0) {
            return res.status(404).json({
                success: false,
                message: 'Question not found or has been deleted'
            });
        }
        console.log("Question found", existingQuestion);
        // Process tags if provided
        if (tags && Array.isArray(tags) && tags.length > 0) {
            // Check existing tags
            const [existingTags] = await mysqlServerConnection.query(
                `SELECT * FROM ${dbName}.question_tags WHERE tag_name IN (?)`,
                [tags]
            );
            console.log("Existing tags", existingTags);

            // Insert only new tags
            const existingTagNames = existingTags.map(tag => tag.tag_name);
            for (let i = 0; i < tags.length; i++) {
                if (!existingTagNames.includes(tags[i])) {
                    await mysqlServerConnection.query(
                        `INSERT INTO ${dbName}.question_tags (tag_name) VALUES (?)`,
                        [tags[i]]
                    );
                }
            }
        }
        console.log("Tags processed", tags);
        // Update question with new fields
        await mysqlServerConnection.query(
            `UPDATE ${dbName}.question_bank 
            SET question_name = ?, question_type = ?, subject = ?, module = ?, tags = ?, is_active = ? 
            WHERE id = ?`,
            [
                question_name, 
                question_type, 
                subject || null, 
                modules || null, 
                tags ? JSON.stringify(tags) : null, 
                is_active !== undefined ? is_active : true, 
                question_id
            ]
        );
        console.log("Question updated successfully", question_id);
        // Get existing options
        const [existingOptions] = await mysqlServerConnection.query(
            `SELECT id FROM ${dbName}.question_bank_options 
            WHERE question_id = ? AND is_deleted = 0`,
            [question_id]
        );
        console.log("Existing options", existingOptions);
        const existingOptionIds = existingOptions.map(opt => opt.id);
        const updatedOptionIds = options.filter(opt => opt.id).map(opt => opt.id);
        console.log("Existing options", existingOptionIds);
        console.log("Updated options", updatedOptionIds);
        // Options to delete (existing but not in updated list)
        const optionsToDelete = existingOptionIds.filter(id => !updatedOptionIds.includes(id));
        console.log("Options to delete", optionsToDelete);
        // Soft delete options that are no longer needed
        if (optionsToDelete.length > 0) {
            await mysqlServerConnection.query(
                `UPDATE ${dbName}.question_bank_options 
                SET is_deleted = 1 
                WHERE id IN (?)`,
                [optionsToDelete]
            );
        }
        console.log("Options deleted successfully", optionsToDelete);
        // Update or insert options
        for (let i = 0; i < options.length; i++) {
            const option = options[i];
            const optionNumber = i + 1; // 1-based index for option_number
            
            if (option.id) {
                // Update existing option
                await mysqlServerConnection.query(
                    `UPDATE ${dbName}.question_bank_options 
                    SET option_number = ?, option_value = ?, is_correct = ?, is_active = ? 
                    WHERE id = ? AND question_id = ?`,
                    [
                        optionNumber,
                        option.option_value,
                        option.is_correct || false,
                        option.is_active !== undefined ? option.is_active : true,
                        option.id,
                        question_id
                    ]
                );
            } else {
                // Insert new option
                await mysqlServerConnection.query(
                    `INSERT INTO ${dbName}.question_bank_options 
                    (question_id, option_number, option_value, is_correct, is_active) 
                    VALUES (?, ?, ?, ?, ?)`,
                    [
                        question_id,
                        optionNumber,
                        option.option_value,
                        option.is_correct || false,
                        option.is_active !== undefined ? option.is_active : true
                    ]
                );
            }
        }
        console.log("Options updated successfully", options);
        // Fetch the updated question with options
        const [question] = await mysqlServerConnection.query(
            `SELECT * FROM ${dbName}.question_bank WHERE id = ?`,
            [question_id]
        );

        const [questionOptions] = await mysqlServerConnection.query(
            `SELECT id, option_number, option_value, is_correct, is_active 
            FROM ${dbName}.question_bank_options 
            WHERE question_id = ? AND is_deleted = 0
            ORDER BY option_number`,
            [question_id]
        );
        console.log("Question options", questionOptions);
        // Parse tags from JSON if they exist
        let questionData = {...question[0]};
        if (questionData.tags && typeof questionData.tags === 'string') {
            try {
                questionData.tags = JSON.parse(questionData.tags);
            } catch (e) {
                console.error('Error parsing tags:', e);
                questionData.tags = [];
            }
        }
        console.log("Question data", questionData);
        console.log("Question options", questionOptions);
        console.log("Question updated successfully",questionData);
        res.status(200).json({
            success: true,
            status: 200,
            message: 'Question updated successfully',
            data: {
                ...questionData,
                options: questionOptions
            }
        });

    } catch (error) {
        console.error('Error updating question:', error);
        res.status(500).json({
            success: false,
            status: 500,
            message: 'An error occurred while updating the question',
            error: error.message
        });
    }
};

const qbDeleteQuestion = async (req, res, next) => {
    try {
        const { question_id } = req.body;
        const dbName = req.user.db_name;

        // Validate required fields
        if (!question_id) {
            return res.status(400).json({
                success: false,
                message: 'Question ID is required'
            });
        }

        // Check if question exists
        const [questionExists] = await mysqlServerConnection.query(
            `SELECT id FROM ${dbName}.question_bank WHERE id = ? AND is_deleted = 0`,
            [question_id]
        );

        if (questionExists.length === 0) {
            return res.status(404).json({
                success: false,
                message: 'Question not found or has already been deleted'
            });
        }

        // Soft delete question
        await mysqlServerConnection.query(
            `UPDATE ${dbName}.question_bank SET is_deleted = 1 WHERE id = ?`,
            [question_id]
        );

        // Soft delete all options for this question
        await mysqlServerConnection.query(
            `UPDATE ${dbName}.question_bank_options SET is_deleted = 1 WHERE question_id = ?`,
            [question_id]
        );

        res.status(200).json({
            success: true,
            status: 200,
            message: 'Question deleted successfully'
        });

    } catch (error) {
        console.error('Error deleting question:', error);
        res.status(500).json({
            success: false,
            status: 500,
            message: 'An error occurred while deleting the question',
            error: error.message
        });
    }
};


const qbGetQuestions = async (req, res, next) => {
    try {
        const { page = 1, limit = 10, search = '', subject = null, module = null, tags = [] } = req.body;
        const dbName = req.user.db_name;
        
        const offset = (page - 1) * limit;
        
        // Prepare queries
        let countQuery = `SELECT COUNT(*) as total FROM ${dbName}.question_bank WHERE is_deleted = 0`;
        let questionsQuery = `SELECT * FROM ${dbName}.question_bank WHERE is_deleted = 0`;
        
        const countParams = [];
        const queryParams = [];
        
        // Add search condition if provided
        if (search) {
            countQuery += ` AND question_name LIKE ?`;
            questionsQuery += ` AND question_name LIKE ?`;
            countParams.push(`%${search}%`);
            queryParams.push(`%${search}%`);
        }

        // Add subject filter if provided
        if (subject) {
            countQuery += ` AND subject = ?`;
            questionsQuery += ` AND subject = ?`;
            countParams.push(subject);
            queryParams.push(subject);
        }

        // Add module filter if provided
        if (module) {
            countQuery += ` AND module = ?`;
            questionsQuery += ` AND module = ?`;
            countParams.push(module);
            queryParams.push(module);
        }

        // Add tags filter if provided
        if (tags && Array.isArray(tags) && tags.length > 0) {
            // For each tag, check if it's in the JSON array
            tags.forEach(tag => {
                countQuery += ` AND JSON_CONTAINS(tags, ?)`;
                questionsQuery += ` AND JSON_CONTAINS(tags, ?)`;
                const jsonTag = JSON.stringify(tag);
                countParams.push(jsonTag);
                queryParams.push(jsonTag);
            });
        }
        
        questionsQuery += ` ORDER BY created_at DESC LIMIT ? OFFSET ?`;
        queryParams.push(Number(limit), offset);
        
        // Get total count for pagination
        const [totalResults] = await mysqlServerConnection.query(countQuery, countParams);
        const totalRecords = totalResults[0].total;
        
        // Get questions
        const [questions] = await mysqlServerConnection.query(questionsQuery, queryParams);
        
        // Get options for all questions
        const questionIds = questions.map(q => q.id);
        let optionsQuery = '';
        let optionsParams = [];
        
        if (questionIds.length > 0) {
            optionsQuery = `
                SELECT * FROM ${dbName}.question_bank_options 
                WHERE question_id IN (?) AND is_deleted = 0
                ORDER BY option_number
            `;
            optionsParams = [questionIds];
        }
        
        const questionsWithOptions = [...questions].map(question => {
            // Parse tags from JSON if they exist
            if (question.tags && typeof question.tags === 'string') {
                try {
                    question.tags = JSON.parse(question.tags);
                } catch (e) {
                    console.error('Error parsing tags for question', question.id, e);
                    question.tags = [];
                }
            }
            return question;
        });
        
        if (questionIds.length > 0) {
            const [options] = await mysqlServerConnection.query(optionsQuery, optionsParams);
            
            for (const question of questionsWithOptions) {
                question.options = options.filter(opt => opt.question_id === question.id)
                    .sort((a, b) => a.option_number - b.option_number);
            }
        }
        
        res.status(200).json({
            success: true,
            message: 'Questions retrieved successfully',
            status: 200,
            data: {
                questions: questionsWithOptions,
                pagination: {
                    totalRecords,
                    totalPages: Math.ceil(totalRecords / limit),
                    page: Number(page),
                    limit: Number(limit)
                }
            }
        });
        
    } catch (error) {
        console.error('Error fetching questions:', error);
        res.status(500).json({
            success: false,
            message: 'An error occurred while fetching questions',
            status: 500,
            error: error.message
        });
    }
};


const qbGetQuestionById = async (req, res, next) => {
    try {
        const { question_id } = req.body;
        const dbName = req.user.db_name;

        // Validate required fields
        if (!question_id) {
            return res.status(400).json({
                success: false,
                message: 'Question ID is required'
            });
        }

        // Get question
        const [question] = await mysqlServerConnection.query(
            `SELECT * FROM ${dbName}.question_bank WHERE id = ? AND is_deleted = 0`,
            [question_id]
        );

        if (question.length === 0) {
            return res.status(404).json({
                success: false,
                message: 'Question not found or has been deleted'
            });
        }

        // Get options for the question
        const [options] = await mysqlServerConnection.query(
            `SELECT id, option_number, option_value, is_correct, is_active 
            FROM ${dbName}.question_bank_options 
            WHERE question_id = ? AND is_deleted = 0
            ORDER BY option_number`,
            [question_id]
        );

        // Parse tags from JSON if they exist
        let questionData = {...question[0]};
        if (questionData.tags && typeof questionData.tags === 'string') {
            try {
                questionData.tags = JSON.parse(questionData.tags);
            } catch (e) {
                console.error('Error parsing tags:', e);
                questionData.tags = [];
            }
        }

        res.status(200).json({
            success: true,
            message: 'Question retrieved successfully',
            status: 200,
            data: {
                ...questionData,
                options
            }
        });

    } catch (error) {
        console.error('Error fetching question:', error);
        res.status(500).json({
            success: false,
            message: 'An error occurred while fetching the question',
            status: 500,
            error: error.message
        });
    }
};

module.exports = {
    getQuestionTags,
    qbCreateQuestion,
    qbUpdateQuestion,
    qbDeleteQuestion,
    qbGetQuestions,
    qbGetQuestionById,
    deleteQuestionTag
};