import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, But<PERSON> } from 'react-bootstrap';
import { Icon } from '@iconify/react';
import NoData from '../../../components/common/NoData';
import './TodoList.css';
import { getAllTasksByUser, addTask, editTaskById, deleteTaskById, markTaskAsCompleted } from '../../../services/userService';

function TodoList() {
  const [todos, setTodos] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [newTask, setNewTask] = useState('');
  const [editingId, setEditingId] = useState(null);
  const [editingText, setEditingText] = useState('');
  const [editingMarked, setEditingMarked] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [selectedTask, setSelectedTask] = useState(null);

  // Fetch all tasks on component mount
  useEffect(() => {
    fetchTasks();
  }, []);

  const fetchTasks = async () => {
    try {
      setLoading(true);
      const response = await getAllTasksByUser();
      console.log("response--------------------------------------", response);
      if (response.success) {
        setTodos(response.data || []);
      }
    } catch (error) {
      console.error('Error fetching tasks:', error);
    } finally {
      setLoading(false);
    }
  };

  // Add new task
  const handleAddTask = async (e) => {
    e.preventDefault();
    if (!newTask.trim()) return;

    try {
      const response = await addTask({ task: newTask.trim() });
      if (response.success) {
        setNewTask('');
        fetchTasks(); // Refresh the list
      }
    } catch (error) {
      console.error('Error adding task:', error);
    }
  };

  // Edit task
  const handleEditTask = async (taskId, newText) => {
    if (!newText.trim()) return;
  
    try {
      const response = await editTaskById({ 
        task_id: taskId, 
        task: newText.trim(),
        marked: editingMarked
      });
      if (response.success) {
        setEditingId(null);
        setEditingText('');
        setEditingMarked(false);
        fetchTasks(); // Refresh the list
      }
    } catch (error) {
      console.error('Error editing task:', error);
    }
  };
  

  // Delete task
  const handleDeleteTask = async (taskId) => {
    try {
      const response = await deleteTaskById(taskId );
      if (response.success) {
        fetchTasks(); // refresh list
      }
    } catch (error) {
      console.error('Error deleting task:', error);
    }
  };
  

  // Mark task as completed
  const handleToggleComplete = async (taskId, currentStatus) => {
    try {
      const response = await markTaskAsCompleted({ task_id: taskId });
      if (response.success) {
        fetchTasks(); // Refresh the list
      }
    } catch (error) {
      console.error('Error updating task status:', error);
    }
  };

  // Start editing
  const startEditing = (task) => {
    setEditingId(task.id);
    setEditingText(task.task || task.text);
    setEditingMarked(task.completed || task.is_completed || false);
  };

  // Cancel editing
  const cancelEditing = () => {
    setEditingId(null);
    setEditingText('');
    setEditingMarked(false);
  };

  // View task details
  const viewTask = (task) => {
    setSelectedTask(task);
    setShowModal(true);
  };

  // Filter tasks based on search term
  const filteredTodos = todos.filter(todo =>
    (todo.task || todo.text || '').toLowerCase().includes(searchTerm.toLowerCase())
  );

  const truncateText = (text, maxLength = 50) => {
    return text && text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
  };

  if (loading) {
    return (
      <div className="card todo-lit-card">
        <div className="card-body d-flex align-items-center justify-content-center">
          <div className="text-center">
            <div className="spinner-border text-primary" role="status">
              <span className="visually-hidden">Loading...</span>
            </div>
            <p className="mt-2 text-muted">Loading tasks...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <>
      <div className="card todo-lit-card">
        <div className="card-header">
          <h5 className="text-dark mb-0">
            <Icon icon="fluent:task-list-square-24-regular" className="me-2" /> 
            Task List ({filteredTodos.length})
          </h5>
        </div>
        <div className="card-body p-0">
          {/* Search and Add Form */}
          <div className="todo-controls">
            <div className="search-container">
              <Icon icon="fluent:search-24-regular" className="search-icon text-muted" />
              <input
                type="text"
                className="search-input form-control"
                placeholder="Search tasks..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <form className="add-form d-flex gap-2 mb-1" onSubmit={handleAddTask}>
              <input
                type="text"
                className="task-input form-control"
                placeholder="Add new task..."
                value={newTask}
                onChange={(e) => setNewTask(e.target.value)}
              />
              <button type="submit" className="btn btn-primary d-flex align-items-center justify-content-center" style={{ width: '35px', height: '35px', padding: 0 }}>
                <Icon icon="fluent:add-24-regular" width="20" height="20" />
              </button>
            </form>
          </div>

          {/* Todo List */}
          <div className="todo-list p-1" style={{ backgroundColor: '#ffffff' }}>
            {filteredTodos.length === 0 ? (
              <div className="text-center py-4">
                <Icon icon="fluent:task-list-square-24-regular" width="48" height="48" className="text-muted mb-2" />
                <p className="text-muted mb-0">
                  {searchTerm ? 'No tasks found matching your search.' : 'No tasks yet. Add your first task above!'}
                </p>
              </div>
            ) : (
              filteredTodos.map(todo => (
                <div key={todo.id} className="todo-item">
                  <div className="todo-left">
                    <div className="checkbox-container">
                      <input
                        type="checkbox"
                        checked={editingId === todo.id ? editingMarked : (todo.completed || todo.is_completed || todo.marked === 1)}
                        onChange={() => {
                          if (editingId === todo.id) {
                            setEditingMarked(!editingMarked);
                          } else {
                            handleToggleComplete(todo.id, todo.completed || todo.is_completed);
                          }
                        }}
                      />
                      <span className="checkbox-label">Mark as completed</span>
                    </div>
                    <div className="task-content">
                      {editingId === todo.id ? (
                        <div className="edit-form">
                          <input
                            type="text"
                            className="edit-input"
                            value={editingText}
                            onChange={(e) => setEditingText(e.target.value)}
                            onKeyDown={(e) => {
                              if (e.key === 'Enter') {
                                handleEditTask(todo.id, editingText);
                              } else if (e.key === 'Escape') {
                                cancelEditing();
                              }
                            }}
                            autoFocus
                          />
                          <button 
                            className="save-btn"
                            onClick={() => handleEditTask(todo.id, editingText)}
                          >
                            <Icon icon="fluent:checkmark-24-regular" />
                          </button>
                          <button 
                            className="action-btn"
                            onClick={cancelEditing}
                          >
                            <Icon icon="fluent:dismiss-24-regular" />
                          </button>
                        </div>
                      ) : (
                        <>
                          <span className={`todo-text ${(todo.completed || todo.is_completed || todo.marked === 1) ? 'completed' : ''}`}>
                            {truncateText(todo.task || todo.text)}
                          </span>
                          <div className="todo-actions">
                            <button 
                              className="action-btn view-btn" 
                              onClick={() => viewTask(todo)}
                              title="View details"
                            >
                              <Icon icon="fluent:eye-24-regular" />
                            </button>
                            <button 
                              className="action-btn edit-btn" 
                              onClick={() => startEditing(todo)}
                              title="Edit task"
                            >
                              <Icon icon="fluent:edit-24-regular" />
                            </button>
                            <button 
                              className="action-btn delete-btn" 
                              onClick={() => handleDeleteTask(todo.id)}
                              title="Delete task"
                            >
                              <Icon icon="fluent:delete-24-regular" />
                            </button>
                          </div>
                        </>
                      )}
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        </div>
      </div>

      {/* Task Details Modal */}
      <Modal show={showModal} onHide={() => setShowModal(false)} centered>
        <Modal.Header closeButton>
          <Modal.Title className="d-flex align-items-center">
            <input
              type="checkbox"
              checked={selectedTask?.completed || selectedTask?.is_completed}
              onChange={() => {
                if (selectedTask) {
                  handleToggleComplete(selectedTask.id, selectedTask.completed || selectedTask.is_completed);
                  setSelectedTask({
                    ...selectedTask,
                    completed: !(selectedTask.completed || selectedTask.is_completed),
                    is_completed: !(selectedTask.completed || selectedTask.is_completed)
                  });
                }
              }}
              className="me-2"
            />
            Task Details
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {selectedTask && (
            <div>
              <div className="task-detail">
                <label>Task:</label>
                <p>{selectedTask.task || selectedTask.text}</p>
              </div>
          
              {selectedTask.created_at && (
                <div className="task-detail">
                  <label>Created:</label>
                  <p>{new Date(selectedTask.created_at).toLocaleDateString()}</p>
                </div>
              )}
              {selectedTask.updated_at && (
                <div className="task-detail">
                  <label>Last Updated:</label>
                  <p>{new Date(selectedTask.updated_at).toLocaleDateString()}</p>
                </div>
              )}
            </div>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowModal(false)}>
            Close
          </Button>
          {selectedTask && (
            <>
              <Button 
                variant="warning" 
                onClick={() => {
                  startEditing(selectedTask);
                  setShowModal(false);
                }}
              >
                Edit Task
              </Button>
              <Button 
                variant="danger" 
                onClick={() => {
                  handleDeleteTask(selectedTask.id);
                  setShowModal(false);
                }}
              >
                Delete Task
              </Button>
            </>
          )}
        </Modal.Footer>
      </Modal>
    </>
  );
}

export default TodoList; 