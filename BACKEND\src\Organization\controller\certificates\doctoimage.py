import os
from pdf2image import convert_from_path
import platform
from io import BytesIO
import base64
import json
import argparse



def convert_pdf_to_images(pdf_path, output_dir, dpi=300, poppler_path=None):
    os.makedirs(output_dir, exist_ok=True)
    
    # More flexible Poppler path handling
    if platform.system() == "Windows":
        # Try multiple possible locations for Poppler
        possible_paths = [
            poppler_path,  # Use provided path if available
            r"C:\Users\<USER>\Downloads\Release-24.08.0-0\poppler-24.08.0\Library\bin",
            r"C:\Program Files\poppler\bin",
            r"C:\poppler\bin",
        ]
        
        # Use the first path that exists
        for path in possible_paths:
            if path and os.path.exists(path):
                poppler_path = path
                print("Using Poppler path:", path)
                break
                
        if not poppler_path or not os.path.exists(poppler_path):
            print("Poppler path not found. PDF conversion may fail.")
    else:
        # For Linux/Mac, try common locations
        poppler_path = poppler_path or "/usr/bin"

    # Convert PDF to images with improved quality settings
    try:
        images = convert_from_path(
            pdf_path, 
            dpi=dpi, 
            poppler_path=poppler_path,
            use_pdftocairo=True,  # Use pdftocairo for better text rendering
            transparent=True,      # Maintain transparency
            fmt='png'              # Use PNG for better quality
        )
    except Exception as e:
        print(f"Error converting PDF to images: {e}")
        return json.dumps({"error": str(e)})

    saved_files = []
    for i, image in enumerate(images):
        filename = f"page_{i+1}.png"  # Changed to PNG format
        buffer = BytesIO()
        # Save with maximum quality
        image.save(buffer, "PNG", quality=100, optimize=True)
        buffer.seek(0)  # Reset the buffer position to the start
        encoded_buffer = base64.b64encode(buffer.getvalue()).decode('utf-8')
        saved_files.append({
            "filename": filename,
            "buffer": encoded_buffer
        })

    return json.dumps(saved_files) 


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='Convert PDF to images')
    parser.add_argument('pdf_path', help='Path to the PDF file')
    parser.add_argument('output_dir', help='Directory to save output images')
    parser.add_argument('--dpi', type=int, default=300, help='DPI for image quality (default: 300)')
    parser.add_argument('--poppler_path', help='Path to Poppler bin directory (optional, Windows only)')
    
    args = parser.parse_args()
    saved_files = convert_pdf_to_images(args.pdf_path, args.output_dir, args.dpi, args.poppler_path)
    # Only output the JSON result, nothing else
    print(saved_files)
