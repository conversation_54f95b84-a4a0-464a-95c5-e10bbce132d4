import React, { useEffect, useState } from 'react';

import CourseAnalyticsAssessment from './CourseAnalyticsAssessment';
import CourseAnalyticsSurvey from './CourseAnalyticsSurvey';
import CourseAnalyticsTraineeProgress from './CourseAnalyticsTraineeProgress';
import { Icon } from '@iconify/react';
import { coursePerformance } from '../../../services/adminService';
import { decodeData } from '../../../utils/encodeAndEncode';
import { useParams } from 'react-router-dom';

function CourseAnalytics() {
    const { courseId } = useParams();
    const decodedCourseId = decodeData(courseId);
    console.log('Decoded Course ID', decodedCourseId);

    const [activeTab, setActiveTab] = useState('progress');
    const [courseStats, setCourseStats] = useState(null);
    const [loading, setLoading] = useState(true);

    // Fetch course performance data
    useEffect(() => {
        fetchCourseStats();
    }, [decodedCourseId]);

    const fetchCourseStats = async () => {
        try {
            setLoading(true);
            const response = await coursePerformance(decodedCourseId);
            console.log('Course Stats Response:', response);

            if (response.success && response.data) {
                setCourseStats(response.data);
            }
        } catch (error) {
            console.error('Error fetching course stats:', error);
        } finally {
            setLoading(false);
        }
    };

    // Dynamic cards data based on API response
    const getCardsData = () => {
        if (!courseStats) {
            // Default/loading values
            return [
                {
                    title: 'Total Enrollments',
                    value: loading ? '...' : '0',
                    icon: 'fluent:people-24-regular',
                    bgColor: '#F4F7FE',
                    textColor: 'text-dark',
                    iconColor: 'text-primary'
                },
                {
                    title: 'Total Revenue',
                    value: loading ? '...' : '$0',
                    icon: 'fluent:money-24-regular',
                    bgColor: '#E6FAF5',
                    textColor: 'text-dark',
                    iconColor: 'text-success'
                },
                {
                    title: 'Completed',
                    value: loading ? '...' : '0',
                    icon: 'fluent:checkmark-circle-24-regular',
                    bgColor: '#E8F3FF',
                    textColor: 'text-dark',
                    iconColor: 'text-info'
                },
                {
                    title: 'In Progress',
                    value: loading ? '...' : '0',
                    icon: 'fluent:clock-24-regular',
                    bgColor: '#FFF6E5',
                    textColor: 'text-dark',
                    iconColor: 'text-warning'
                },
                {
                    title: 'Average Rating',
                    value: loading ? '...' : '0/5.0',
                    icon: 'fluent:star-24-regular',
                    bgColor: '#FFE7E7',
                    textColor: 'text-dark',
                    iconColor: 'text-danger'
                }
            ];
        }

        // Real data from API - using correct field names from old implementation
        const statsData = Array.isArray(courseStats) ? courseStats[0] : courseStats;

        return [
            {
                title: 'Total Enrollments',
                value: statsData?.total_purchased || 0,
                icon: 'fluent:people-24-regular',
                bgColor: '#F4F7FE',
                textColor: 'text-dark',
                iconColor: 'text-primary'
            },
            {
                title: 'Total Revenue',
                value: `$${statsData?.total_revenue || 0}`,
                icon: 'fluent:money-24-regular',
                bgColor: '#E6FAF5',
                textColor: 'text-dark',
                iconColor: 'text-success'
            },
            {
                title: 'Completed',
                value: statsData?.completed || 0,
                icon: 'fluent:checkmark-circle-24-regular',
                bgColor: '#E8F3FF',
                textColor: 'text-dark',
                iconColor: 'text-info'
            },
            {
                title: 'In Progress',
                value: statsData?.in_progress || 0,
                icon: 'fluent:clock-24-regular',
                bgColor: '#FFF6E5',
                textColor: 'text-dark',
                iconColor: 'text-warning'
            },
            {
                title: 'Average Rating',
                value: `${statsData?.average_rating || 0}/5.0`,
                icon: 'fluent:star-24-regular',
                bgColor: '#FFE7E7',
                textColor: 'text-dark',
                iconColor: 'text-danger'
            }
        ];
    };

    const cardsData = getCardsData();

    return (
        <>
            <div className="row">
                <div className="col-12 mb-4">
                    <div className="card border-0">
                        <div className="card-body p-0">
                            <h4 className="card-title mb-4">Course Analytics Dashboard</h4>

                            {/* Analytics Cards */}
                            <div className="row g-3">
                                {cardsData.map((card, index) => (
                                    <div key={index} className="col-md-4 col-lg">
                                        <div className="card border-0" style={{ backgroundColor: card.bgColor }}>
                                            <div className="card-body">
                                                <div className="d-flex align-items-center mb-2">
                                                    <Icon
                                                        icon={card.icon}
                                                        width="24"
                                                        height="24"
                                                        className={`${card.iconColor} me-2`}
                                                    />
                                                    <h6 className={`card-subtitle mb-0 ${card.textColor}`}>{card.title}</h6>
                                                </div>
                                                <h2 className={`card-title mb-0 ${card.textColor}`}>{card.value}</h2>
                                            </div>
                                        </div>
                                    </div>
                                ))}
                            </div>

                            {/* Additional Sections */}
                            <div className="row mt-4">
                                <div className="col-12">
                                    <div className="card border">
                                        <div className="card-body ">
                                            {/* Tabs Navigation */}
                                            <div className="tab-header border-bottom mb-0">
                                                <ul className="nav nav-tabs">
                                                    <li className="nav-item">
                                                        <button
                                                            className={`nav-link d-flex align-items-center ${activeTab === 'progress' ? 'active' : ''}`}
                                                            onClick={() => setActiveTab('progress')}
                                                        >
                                                            <Icon 
                                                                icon="fluent:people-community-24-regular" 
                                                                width="20" 
                                                                height="20" 
                                                                className="me-2"
                                                            />
                                                            Trainee Progress
                                                        </button>
                                                    </li>
                                                    <li className="nav-item">
                                                        <button
                                                            className={`nav-link d-flex align-items-center ${activeTab === 'assessment' ? 'active' : ''}`}
                                                            onClick={() => setActiveTab('assessment')}
                                                        >
                                                            <Icon 
                                                                icon="fluent:clipboard-task-24-regular" 
                                                                width="20" 
                                                                height="20" 
                                                                className="me-2"
                                                            />
                                                            Assessment
                                                        </button>
                                                    </li>
                                                    <li className="nav-item">
                                                        <button
                                                            className={`nav-link d-flex align-items-center ${activeTab === 'survey' ? 'active' : ''}`}
                                                            onClick={() => setActiveTab('survey')}
                                                        >
                                                            <Icon 
                                                                icon="fluent:form-24-regular" 
                                                                width="20" 
                                                                height="20" 
                                                                className="me-2"
                                                            />
                                                            Survey Data
                                                        </button>
                                                    </li>
                                                </ul>
                                            </div>

                                            {/* Tab Content */}
                                            <div className="tab-content">
                                                <div className={`tab-pane p-0 ${activeTab === 'progress' ? 'active' : ''}`}>
                                                    <div className="card-body p-0 pt-2">
                                                        <CourseAnalyticsTraineeProgress />
                                                    </div>
                                                </div>

                                                <div className={`tab-pane p-0 ${activeTab === 'assessment' ? 'active' : ''}`}>
                                                    <div className="card-body p-0 pt-2">
                                                        <CourseAnalyticsAssessment />
                                                    </div>
                                                </div>

                                                <div className={`tab-pane p-0 ${activeTab === 'survey' ? 'active' : ''}`}>
                                                    <div className="card-body p-0 pt-2">
                                                        <CourseAnalyticsSurvey />
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </>
    );
}

export default CourseAnalytics;
