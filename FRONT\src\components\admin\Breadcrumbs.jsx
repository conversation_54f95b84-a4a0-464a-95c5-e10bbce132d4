import React from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { Icon } from '@iconify/react';

function Breadcrumbs() {
  const location = useLocation();
  const navigate = useNavigate();
  
  // Show breadcrumbs for all admin paths
  if (!location.pathname.startsWith('/admin/')) {
    return null;
  }
  
  // Filter out empty strings and 'admin'
  const pathnames = location.pathname
    .split('/')
    .filter((x) => x !== '' && x !== 'admin');

  const handleBack = () => {
    navigate(-1);
  };

  // Create a mapping of path segments to readable names and icons
  const pathMap = {
    'dashboard': {
      name: 'Dashboard',
      icon: 'fluent:home-24-regular'
    },
    'roles-and-access': {
      name: 'Roles & Access',
      icon: 'fluent:shield-lock-24-regular'
    },
    'courses': {
      name: 'All Courses',
      icon: 'fluent:book-24-regular'
    },
    'edit-create': {
      name: 'Edit Course',
      icon: 'fluent:edit-24-regular'
    },
    'classrooms': {
      name: 'Classrooms',
      icon: 'fluent:video-person-24-regular'
    },
    'classroom-dashboard': {
      name: 'Classroom Dashboard',
      icon: 'fluent:video-person-24-regular'
    },
    'trainees': {
      name: 'Trainees',
      icon: 'fluent:people-24-regular'
    },
    'assessment': {
      name: 'Assessment',
      icon: 'fluent:clipboard-task-list-24-regular'
    },
    'ClassroomDashboard': {
      name: 'Classroom Dashboard',
      icon: 'fluent:video-person-24-regular'
    },
    'ClassroomAssessmentDetails': {
      name: 'Assessment',
      icon: 'fluent:clipboard-task-list-24-regular'
    },
    'assingment': {
      name: 'Assignment',
      icon: 'fluent:clipboard-task-list-24-regular'
    },
    'ClassroomAssignmentDetails': {
      name: 'Assignment',
      icon: 'fluent:clipboard-task-list-24-regular'
    },
    'analytics': {
      name: 'Course Analytics',
      icon: 'fluent:data-trending-24-regular'
    },
    'module': {
      name: 'Course Modules',
      icon: 'fluent:book-information-24-regular'
    },
    'content': {
      name: 'Content',
      icon: 'fluent:document-24-regular'
    },
    'video': {
      name: 'Edit Video',
      icon: 'fluent:video-24-regular'
    },
    'quiz': {
      name: 'Edit Assessment',
      icon: 'fluent:clipboard-task-list-ltr-24-regular'
    },
    'survey': {
      name: 'Edit Survey',
      icon: 'fluent:form-24-regular'
    },
    'document': {
      name: 'Edit Document',
      icon: 'fluent:document-24-regular'
    }
  };

  const getPathInfo = (path) => {
    // Handle encoded IDs
    if (path.includes('%')) {
      const pathType = pathnames[pathnames.indexOf(path) - 1];
      switch (pathType) {
        case 'ClassroomDashboard':
          return {
            name: 'Classroom Dashboard',
            icon: 'fluent:video-person-24-regular'
          };
        case 'assessment':
          return {
            name: 'Assessment',
            icon: 'fluent:clipboard-task-list-24-regular'
          };
        case 'ClassroomAssessmentDetails':
          return {
            name: 'Assessment',
            icon: 'fluent:clipboard-task-list-24-regular'
          };
        case 'assingment':
          return {
            name: 'Assignment',
            icon: 'fluent:clipboard-task-list-24-regular'
          };
        case 'ClassroomAssignmentDetails':
          return {
            name: 'Assignment',
            icon: 'fluent:clipboard-task-list-24-regular'
          };
        case 'video':
          return {
            name: 'Edit Video',
            icon: 'fluent:video-24-regular'
          };
        case 'quiz':
          return {
            name: 'Edit Assessment',
            icon: 'fluent:clipboard-task-list-ltr-24-regular'
          };
        case 'survey':
          return {
            name: 'Edit Survey',
            icon: 'fluent:form-24-regular'
          };
        case 'document':
          return {
            name: 'Edit Document',
            icon: 'fluent:document-24-regular'
          };
        case 'content':
          return {
            name: 'Content',
            icon: 'fluent:document-24-regular'
          };
        default:
          return {
            name: 'Module Details',
            icon: 'fluent:book-information-24-regular'
          };
      }
    }
    
    return pathMap[path] || {
      name: path.charAt(0).toUpperCase() + path.slice(1),
      icon: 'fluent:document-24-regular'
    };
  };

  // Get the current page title
  const getPageTitle = () => {
    if (pathnames.length === 0) return 'Dashboard';
    
    if (pathnames.includes('roles-and-access')) {
      return 'Roles & Access';
    }

    if (pathnames.includes('traineesAnalytics')) {
      return 'Trainee Analytics';
    }

    if (pathnames.includes('CourseAnalyticsAssessmentDetails')) {
      return 'Assessment Analytics';
    }

    if (pathnames.includes('CourseAnalyticsSurveyDetails')) {
      return 'Survey Analytics';
    }

    if (pathnames.includes('edit-create')) {
      return 'Edit Course';
    }

    if (pathnames.includes('classrooms') && pathnames.length === 1) {
      return 'Classrooms';
    }

    if (pathnames.includes('classroom-dashboard') && pathnames.length === 3) {
      return 'Classroom Dashboard';
    }

    if (pathnames.includes('ClassroomAssignmentDetails')) {
      return 'Assignment';
    }

    if (pathnames.includes('ClassroomAssessmentDetails')) {
      return 'Assessment';
    }

    if (pathnames.includes('classroom-dashboard') && pathnames.includes('trainees')) {
      return 'Trainees';
    }
    
    const lastSegment = pathnames[pathnames.length - 2]; // Get the type (video, quiz, etc.)
    const pathInfo = getPathInfo(pathnames[pathnames.length - 1]);
    
    switch (lastSegment) {
      case 'analytics':
        return 'Course Analytics';
      case 'video':
        return 'Edit Video';
      case 'quiz':
        return 'Edit Assessment';
      case 'survey':
        return 'Edit Survey';
      case 'document':
        return 'Edit Document';
      case 'content':
        return 'Content';
      case 'module':
        return 'Course Modules';
      default:
        return pathInfo.name;
    }
  };

  // Build the breadcrumb path
  const buildPath = (index) => {
    return `/admin/${pathnames.slice(0, index + 1).join('/')}`;
  };

  // Custom breadcrumb items for specific pages
  const getBreadcrumbItems = () => {
    // Add new condition for roles and access
    if (pathnames.includes('roles-and-access')) {
      return [
        {
          name: 'Dashboard',
          icon: 'fluent:home-24-regular',
          path: '/admin/dashboard'
        },
        {
          name: 'Roles & Access',
          icon: 'fluent:shield-lock-24-regular',
          path: location.pathname
        }
      ];
    }

    // Handle trainee analytics path
    if (pathnames.includes('traineesAnalytics')) {
      return [
        {
          name: 'Dashboard',
          icon: 'fluent:home-24-regular',
          path: '/admin/dashboard'
        },
        {
          name: 'Trainees',
          icon: 'fluent:people-24-regular',
          path: '/admin/trainees'
        },
        {
          name: 'Trainee Analytics',
          icon: 'fluent:data-trending-24-regular',
          path: location.pathname
        }
      ];
    }
    
    // Handle ClassroomDashboard assessment path
    if (pathnames.includes('ClassroomAssessmentDetails')) {
      const classroomId = pathnames[pathnames.indexOf('ClassroomDashboard') + 1];
      return [
        {
          name: 'Dashboard',
          icon: 'fluent:home-24-regular',
          path: '/admin/dashboard'
        },
        {
          name: 'Classroom Dashboard',
          icon: 'fluent:video-person-24-regular',
          path: `/admin/classrooms/classroom-dashboard/${classroomId}`
        },
        {
          name: 'Assessment',
          icon: 'fluent:clipboard-task-list-24-regular',
          path: location.pathname
        }
      ];
    }

    // Handle ClassroomDashboard assignment path
    if (pathnames.includes('ClassroomAssignmentDetails')) {
      const classroomId = pathnames[pathnames.indexOf('ClassroomDashboard') + 1];
      return [
        {
          name: 'Dashboard',
          icon: 'fluent:home-24-regular',
          path: '/admin/dashboard'
        },
        {
          name: 'Classroom Dashboard',
          icon: 'fluent:video-person-24-regular',
          path: `/admin/classrooms/classroom-dashboard/${classroomId}`
        },
        {
          name: 'Assignment',
          icon: 'fluent:clipboard-task-list-24-regular',
          path: location.pathname
        }
      ];
    }

    if (pathnames.includes('analytics')) {
      const hasAssessmentDetails = pathnames.includes('CourseAnalyticsAssessmentDetails');
      const hasSurveyDetails = pathnames.includes('CourseAnalyticsSurveyDetails');
      
      if (hasAssessmentDetails) {
        return [
          {
            name: 'Dashboard',
            icon: 'fluent:home-24-regular',
            path: '/admin/dashboard'
          },
          {
            name: 'All Courses',
            icon: 'fluent:book-24-regular',
            path: '/admin/courses'
          },
          {
            name: 'Course Analytics',
            icon: 'fluent:data-trending-24-regular',
            path: `/admin/courses/analytics/${pathnames[pathnames.indexOf('analytics') + 1]}`
          },
          {
            name: 'Assessment Analytics',
            icon: 'fluent:clipboard-task-list-24-regular',
            path: location.pathname
          }
        ];
      }

      if (hasSurveyDetails) {
        return [
          {
            name: 'Dashboard',
            icon: 'fluent:home-24-regular',
            path: '/admin/dashboard'
          },
          {
            name: 'All Courses',
            icon: 'fluent:book-24-regular',
            path: '/admin/courses'
          },
          {
            name: 'Course Analytics',
            icon: 'fluent:data-trending-24-regular',
            path: `/admin/courses/analytics/${pathnames[pathnames.indexOf('analytics') + 1]}`
          },
          {
            name: 'Survey Analytics',
            icon: 'fluent:form-24-regular',
            path: location.pathname
          }
        ];
      }
      
      return [
        {
          name: 'Dashboard',
          icon: 'fluent:home-24-regular',
          path: '/admin/dashboard'
        },
        {
          name: 'All Courses',
          icon: 'fluent:book-24-regular',
          path: '/admin/courses'
        },
        {
          name: 'Course Analytics',
          icon: 'fluent:data-trending-24-regular',
          path: location.pathname
        }
      ];
    }
    
    // Add new condition for classroom trainees
    if (pathnames.includes('classroom-dashboard') && pathnames.includes('trainees')) {
      const classroomId = pathnames[pathnames.indexOf('classroom-dashboard') + 1];
      return [
        {
          name: 'Dashboard',
          icon: 'fluent:home-24-regular',
          path: '/admin/dashboard'
        },
        {
          name: 'Classrooms',
          icon: 'fluent:video-person-24-regular',
          path: '/admin/classrooms'
        },
        {
          name: 'Classroom Dashboard',
          icon: 'fluent:video-person-24-regular',
          path: `/admin/classrooms/classroom-dashboard/${classroomId}`
        },
        {
          name: 'Trainees',
          icon: 'fluent:people-24-regular',
          path: location.pathname
        }
      ];
    }
    
    // Add new condition for classroom assessment details
    if (pathnames.includes('ClassroomAssessmentDetails')) {
      const classroomId = pathnames[pathnames.indexOf('classroom-dashboard') + 1];
      return [
        {
          name: 'Dashboard',
          icon: 'fluent:home-24-regular',
          path: '/admin/dashboard'
        },
        {
          name: 'Classrooms',
          icon: 'fluent:video-person-24-regular',
          path: '/admin/classrooms'
        },
        {
          name: 'Classroom Dashboard',
          icon: 'fluent:video-person-24-regular',
          path: `/admin/classrooms/classroom-dashboard/${classroomId}`
        },
        {
          name: 'Assessment',
          icon: 'fluent:clipboard-task-list-24-regular',
          path: location.pathname
        }
      ];
    }
    
    // Add new condition for classroom assignment details
    if (pathnames.includes('ClassroomAssignmentDetails')) {
      const classroomId = pathnames[pathnames.indexOf('classroom-dashboard') + 1];
      return [
        {
          name: 'Dashboard',
          icon: 'fluent:home-24-regular',
          path: '/admin/dashboard'
        },
        {
          name: 'Classrooms',
          icon: 'fluent:video-person-24-regular',
          path: '/admin/classrooms'
        },
        {
          name: 'Classroom Dashboard',
          icon: 'fluent:video-person-24-regular',
          path: `/admin/classrooms/classroom-dashboard/${classroomId}`
        },
        {
          name: 'Assignment',
          icon: 'fluent:clipboard-task-list-24-regular',
          path: location.pathname
        }
      ];
    }
    
    // Add new condition for classroom dashboard
    if (pathnames.includes('classroom-dashboard')) {
      return [
        {
          name: 'Dashboard',
          icon: 'fluent:home-24-regular',
          path: '/admin/dashboard'
        },
        {
          name: 'Classrooms',
          icon: 'fluent:video-person-24-regular',
          path: '/admin/classrooms'
        },
        {
          name: 'Classroom Dashboard',
          icon: 'fluent:video-person-24-regular',
          path: location.pathname
        }
      ];
    }
    
    // Add new condition for classrooms main page
    if (pathnames.includes('classrooms') && pathnames.length === 1) {
      return [
        {
          name: 'Dashboard',
          icon: 'fluent:home-24-regular',
          path: '/admin/dashboard'
        },
        {
          name: 'Classrooms',
          icon: 'fluent:video-person-24-regular',
          path: location.pathname
        }
      ];
    }
    
    // Add new condition for edit-create course
    if (pathnames.includes('edit-create')) {
      return [
        {
          name: 'Dashboard',
          icon: 'fluent:home-24-regular',
          path: '/admin/dashboard'
        },
        {
          name: 'All Courses',
          icon: 'fluent:book-24-regular',
          path: '/admin/courses'
        },
        {
          name: 'Edit Course',
          icon: 'fluent:edit-24-regular',
          path: location.pathname
        }
      ];
    }
    
    if (pathnames.includes('module') && pathnames.includes('content')) {
      const moduleIndex = pathnames.indexOf('module');
      const contentIndex = pathnames.indexOf('content');
      const contentType = pathnames[contentIndex + 2]; // Get content type (video, quiz, etc.)
      const typeNames = {
        'video': 'Edit Video',
        'quiz': 'Edit Assessment',
        'survey': 'Edit Survey',
        'document': 'Edit Document'
      };
      const typeIcons = {
        'video': 'fluent:video-24-regular',
        'quiz': 'fluent:clipboard-task-list-ltr-24-regular',
        'survey': 'fluent:form-24-regular',
        'document': 'fluent:document-24-regular'
      };
      
      return [
        {
          name: 'Dashboard',
          icon: 'fluent:home-24-regular',
          path: '/admin/dashboard'
        },
        {
          name: 'All Courses',
          icon: 'fluent:book-24-regular',
          path: '/admin/courses'
        },
        {
          name: 'Course Modules',
          icon: 'fluent:book-information-24-regular',
          path: `/admin/courses/module/${pathnames[moduleIndex + 1]}`
        },
        {
          name: 'Content',
          icon: 'fluent:document-24-regular',
          path: `/admin/courses/module/${pathnames[moduleIndex + 1]}/content/${pathnames[contentIndex + 1]}`
        },
        {
          name: typeNames[contentType],
          icon: typeIcons[contentType],
          path: location.pathname
        }
      ];
    } else if (pathnames.includes('module')) {
      const moduleIndex = pathnames.indexOf('module');
      return [
        {
          name: 'Dashboard',
          icon: 'fluent:home-24-regular',
          path: '/admin/dashboard'
        },
        {
          name: 'All Courses',
          icon: 'fluent:book-24-regular',
          path: '/admin/courses'
        },
        {
          name: 'Course Modules',
          icon: 'fluent:book-information-24-regular',
          path: location.pathname
        }
      ];
    }
    
    return null;
  };

  const customBreadcrumbs = getBreadcrumbItems();

  return (
    <nav aria-label="breadcrumb" className="bg-white py-3 px-4 mb-3 rounded shadow-sm">
      <div className="d-flex justify-content-between align-items-center">
        {/* Page Title with Back Button */}
        <div className="d-flex align-items-center">
          {pathnames.length > 0 && (
            <button 
              onClick={handleBack}
              className="btn btn-icon btn-light rounded-circle me-2"
              style={{ 
                width: '32px', 
                height: '32px', 
                padding: '0',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                border: '1px solid #e0e0e0'
              }}
            >
              <Icon icon="fluent:arrow-left-24-regular" width="20" height="20" />
            </button>
          )}
          <h4 className="mb-0 fw-bold d-flex align-items-center">
            <Icon
              icon={getPathInfo(pathnames[pathnames.length - 1] || 'dashboard').icon}
              className="me-2"
              width="24"
              height="24"
            />
            {getPageTitle()}
          </h4>
        </div>

        {/* Breadcrumbs on the right */}
        <ol className="breadcrumb mb-0 d-flex align-items-center d-none d-md-flex">
          {customBreadcrumbs ? (
            // Render custom breadcrumbs for module and content pages
            customBreadcrumbs.map((item, index) => (
              <React.Fragment key={item.path}>
                {index > 0 && (
                  <li className="mx-1 text-muted">
                    <Icon icon="fluent:chevron-right-24-regular" width="16" height="16" />
                  </li>
                )}
                <li className={`breadcrumb-item ${index === customBreadcrumbs.length - 1 ? 'active fw-semibold' : ''}`}>
                  {index === customBreadcrumbs.length - 1 ? (
                    <span className="text-primary d-flex align-items-center">
                      <Icon icon={item.icon} className="me-1" width="18" height="18" />
                      {item.name}
                    </span>
                  ) : (
                    <Link 
                      to={item.path}
                      className="text-decoration-none text-secondary d-flex align-items-center"
                    >
                      <Icon icon={item.icon} className="me-1" width="18" height="18" />
                      {item.name}
                    </Link>
                  )}
                </li>
              </React.Fragment>
            ))
          ) : (
            // Default breadcrumb rendering
            <>
          <li className="breadcrumb-item">
            <Link 
              to="/admin/dashboard" 
              className="text-decoration-none text-secondary d-flex align-items-center"
            >
              <Icon icon="fluent:home-24-regular" className="me-1" width="18" height="18" />
                  <span>Dashboard</span>
            </Link>
          </li>

          {pathnames.map((value, index) => {
            const to = buildPath(index);
            const isLast = index === pathnames.length - 1;
            const pathInfo = getPathInfo(value);

            return (
              <React.Fragment key={to}>
                <li className="mx-1 text-muted">
                  <Icon icon="fluent:chevron-right-24-regular" width="16" height="16" />
                </li>
                <li className={`breadcrumb-item ${isLast ? 'active fw-semibold' : ''}`}>
                {isLast ? (
                    <span className="text-primary d-flex align-items-center">
                      <Icon icon={pathInfo.icon} className="me-1" width="18" height="18" />
                      {pathInfo.name}
                    </span>
                ) : (
                    <Link 
                      to={to} 
                      className="text-decoration-none text-secondary d-flex align-items-center"
                    >
                      <Icon icon={pathInfo.icon} className="me-1" width="18" height="18" />
                      {pathInfo.name}
                    </Link>
                  )}
                  </li>
              </React.Fragment>
            );
          })}
            </>
          )}
        </ol>
      </div>
    </nav>
  );
}

export default Breadcrumbs;