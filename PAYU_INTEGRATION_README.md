# PayU Hosted Checkout Integration

This document explains the PayU Hosted Checkout (Redirect Method) integration implemented for the LMS system.

## Overview

The PayU integration allows users to make payments in Indian Rupees (INR) using PayU's hosted checkout page. The implementation includes:

1. **Backend API** - Handles payment processing and verification
2. **Frontend Component** - Provides the payment interface
3. **Success Page** - Displays payment confirmation

## Files Modified/Created

### Backend Files
- `BACKEND/src/Organization/controller/payment/Payment_with_payu.js` - PayU payment controller
- `BACKEND/src/Organization/routes/organizationroutes.js` - Added PayU routes
- `BACKEND/.env` - Added PayU environment variables

### Frontend Files
- `FRONT/src/pages/user/course/OrderDetails_for_payu.jsx` - PayU order details page
- `FRONT/src/pages/user/settings/SuccessPayUPayment.jsx` - PayU success page
- `FRONT/src/services/userService.js` - Added PayU service functions
- `FRONT/src/routes/UserRoutes.jsx` - Added PayU routes

## Environment Variables

Add the following environment variables to your `BACKEND/.env` file:

```env
# PayU Payment Gateway Configuration
PAY_U_MERCHANT_KEY=your_payu_merchant_key
PAY_U_MERCHANT_SALT=your_payu_merchant_salt
PAYU_BASE_URL=https://test.payu.in/_payment
```

### For Production
Replace the test URL with the production URL:
```env
PAYU_BASE_URL=https://secure.payu.in/_payment
```

## API Endpoints

### 1. Process PayU Payment
- **Endpoint**: `POST /org/process_payu_payment`
- **Description**: Initiates PayU payment process
- **Request Body**:
```json
{
  "amount": 10000,
  "currency": "INR",
  "origin": "http://test.localhost:3001",
  "courseName": "Course Name",
  "points": 0,
  "course_id": "course_123"
}
```

### 2. PayU Success Callback
- **Endpoint**: `GET /org/success_payu_payment`
- **Description**: Handles successful payment verification
- **Query Parameters**: `course_id`, `txnid`, `mihpayid`, `status`

### 3. PayU Cancel Callback
- **Endpoint**: `GET /org/cancel_payu_payment`
- **Description**: Handles payment cancellation
- **Query Parameters**: `txnid`

## Frontend Routes

### 1. PayU Order Details
- **Route**: `/user/courses/orderDetails_for_payu`
- **Component**: `OrderDetailsForPayU`
- **Description**: Displays course details and initiates PayU payment

### 2. PayU Success Page
- **Route**: `/user/courses/successPayUPayment`
- **Component**: `SuccessPayUPayment`
- **Description**: Shows payment success confirmation

## Payment Flow

1. **User selects course** → Navigates to PayU order details page
2. **Payment initiation** → Frontend calls `/process_payu_payment` API
3. **Redirect to PayU** → User is redirected to PayU hosted checkout
4. **Payment completion** → PayU redirects back to success/failure URL
5. **Verification** → Backend verifies payment and updates database
6. **Course enrollment** → User is enrolled in the purchased course

## Key Features

- **Indian Rupee Support**: All payments are processed in INR
- **Secure Hash Generation**: Uses SHA512 hash for payment security
- **Transaction Tracking**: Unique transaction IDs for each payment
- **Course Enrollment**: Automatic enrollment upon successful payment
- **Error Handling**: Comprehensive error handling and user feedback
- **Responsive UI**: Mobile-friendly payment interface

## Security Features

1. **Hash Verification**: All payment requests include secure hash
2. **Transaction Validation**: Server-side payment verification
3. **Duplicate Prevention**: Checks for existing course purchases
4. **Status Tracking**: Payment status updates in database

## Testing

### Test Credentials
Use PayU test credentials for development:
- Test URL: `https://test.payu.in/_payment`
- Use test merchant key and salt provided by PayU

### Test Cards
PayU provides test card numbers for testing different scenarios.

## Production Deployment

1. **Update Environment Variables**: Use production PayU credentials
2. **Change Base URL**: Update to production PayU URL
3. **SSL Certificate**: Ensure HTTPS is enabled for production
4. **Webhook URLs**: Update success/failure URLs to production domain

## Troubleshooting

### Common Issues

1. **Hash Mismatch**: Verify merchant key and salt are correct
2. **Redirect Issues**: Check success/failure URLs are accessible
3. **Database Errors**: Ensure database connection and table structure
4. **CORS Issues**: Configure CORS for PayU domain if needed

### Logs
Check console logs for detailed error information:
- Backend: Payment processing logs
- Frontend: Payment initiation and redirect logs

## Support

For PayU-specific issues, refer to:
- PayU Documentation: https://docs.payu.in/
- PayU Support: Contact PayU technical support

For implementation issues, check the code comments and error logs.
