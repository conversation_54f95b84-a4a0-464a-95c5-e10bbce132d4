const { mysqlServerConnection } = require('../../../db/db');

const getOrgInfomation = async (req, res) => {
  try {
    const dbName = req.user.db_name;

    const selectQuery = `
      SELECT name, description 
      FROM ${dbName}.orginfo 
      WHERE is_deleted = 0 AND is_active = 1` ;

    const [result] = await mysqlServerConnection.query(selectQuery);

    return res.status(200).json({
      success: true,
      data: {
        response: result,
        message: result.length > 0 ? 'Data fetched successfully.' : 'No data found.'
      }
    });

  } catch (error) {
    console.error('Error:', error);
    return res.status(500).json({
      success: false,
      data: { error_msg: 'Internal server error.' }
    });
  }
};




module.exports = { getOrgInfomation };
