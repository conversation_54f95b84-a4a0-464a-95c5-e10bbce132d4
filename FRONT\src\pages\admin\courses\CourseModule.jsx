import React, { useState, useEffect } from "react";
import { Icon } from "@iconify/react";
import { useParams, useNavigate } from "react-router-dom";
import { decodeData, encodeData } from "../../../utils/encodeAndEncode";
import { getCourseModule, createModule, updateModule, deleteModule, moduleStatus } from "../../../services/adminService";
import Loader from '../../../components/admin/Loader';
import { toast } from 'react-toastify';
import NoData from "../../../components/common/NoData";
import { usePermissions } from '../../../context/PermissionsContext';

function  CourseModule() {
  const { courseId } = useParams();
  const decodedCourseId = decodeData(courseId);
  const { permissions } = usePermissions();
  
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [search, setSearch] = useState("");
  const [showModal, setShowModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [deleteModuleId, setDeleteModuleId] = useState(null);
  const [editModuleId, setEditModuleId] = useState(null);
  const [newModuleName, setNewModuleName] = useState("");
  const [newModuleDescription, setNewModuleDescription] = useState("");
  const [moduleData, setModuleData] = useState([]);
  const [statusLoading, setStatusLoading] = useState(null);
  const [classroomName, setClassroomName] = useState(null);
  
  const [errors, setErrors] = useState({
    moduleName: "",
    moduleDescription: ""
  });

  const fetchCourseModule = async (searchQuery = "") => {
    try {
      if (!searchQuery) {
        setLoading(true); // Only show loading on initial fetch, not during search
      }
      const response = await getCourseModule({ 
        id: decodedCourseId,
        search: searchQuery 
      });
      setClassroomName(response.course_name);
      console.log('classroomName---------------', classroomName);
      console.log('get module response', response);
      
      if (response.success) {
        
        const formattedModules = response.data.modulesWithDetails.map(module => ({
          id: module.id,
          title: module.module_name,
          subtitle: module.module_desc,
          status: module.is_active ? "Active" : "Deactive",
          videos: module.video_counts,
          assessments: module.assessment_counts,
          surveys: module.survey_counts,
          documents: module.document_counts,
          assignments: module.assignment_counts,
          totalContent: module.video_counts + module.assessment_counts + 
                       module.survey_counts + module.assignment_counts + 
                       module.document_counts
        }));
        setModuleData(formattedModules);
      } else {
        setModuleData([]);
      }
    } catch (error) {
      console.log('error', error);
      setModuleData([]);
      toast.error('Error fetching modules: ' + error.message, {
        position: 'top-center',
        autoClose: 3000
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCourseModule();
  }, []);

  useEffect(() => {
    const debounceTimer = setTimeout(() => {
      fetchCourseModule(search);
    }, 500);

    return () => clearTimeout(debounceTimer);
  }, [search]);

  // Reset errors when modal is opened/closed
  useEffect(() => {
    if (showModal) {
      setErrors({
        moduleName: "",
        moduleDescription: ""
      });
    }
  }, [showModal]);

  const validateForm = () => {
    const newErrors = {
      moduleName: "",
      moduleDescription: ""
    };
    let isValid = true;

    if (!newModuleName.trim()) {
      newErrors.moduleName = "Module Name is required";
      isValid = false;
    } else if (newModuleName.trim().length > 30) {
      newErrors.moduleName = "Module Name cannot exceed 30 characters";
      isValid = false;
    }

    if (!newModuleDescription.trim()) {
      newErrors.moduleDescription = "Module Description is required";
      isValid = false;
    } else if (newModuleDescription.trim().length > 50) {
      newErrors.moduleDescription = "Module Description cannot exceed 50 characters";
      isValid = false;
    }

    setErrors(newErrors);
    return isValid;
  };

  const handleSaveModule = async () => {
    if (editModuleId && !permissions.course_module_management_edit) {
      toast.warning("You don't have permission to edit modules");
      return;
    }
    if (!editModuleId && !permissions.course_module_management_create) {
      toast.warning("You don't have permission to create modules");
      return;
    }

    if (!validateForm()) {
      return;
    }

    try {
      setSaving(true);
      let response;

      if (editModuleId) {
        response = await updateModule(editModuleId, {
          module_name: newModuleName.trim(),
          module_desc: newModuleDescription.trim()
        });
      } else {
        response = await createModule({
          course_id: decodedCourseId,
          module_name: newModuleName.trim(),
          module_desc: newModuleDescription.trim(),
          module_sequence: moduleData.length + 1
        });
      }

      if (response.success) {
        toast.success(editModuleId ? 'Module updated successfully' : 'Module created successfully', {
          position: 'top-center',
          autoClose: 3000
        });
        setShowModal(false);
        fetchCourseModule(search);
      } else {
        toast.error(response.message || 'Operation failed', {
          position: 'top-center',
          autoClose: 3000
        });
      }
    } catch (error) {
      console.error('Error:', error);
      toast.error('Operation failed: ' + error.message, {
        position: 'top-center',
        autoClose: 3000
      });
    } finally {
      setSaving(false);
    }
  };

  const handleDeleteModule = async () => {
    if (!permissions.course_module_management_delete) {
      toast.warning("You don't have permission to delete modules");
      return;
    }

    try {
      setSaving(true);
      const response = await deleteModule({
        course_id: decodedCourseId,
        module_id: deleteModuleId
      });

      if (response.success) {
        toast.success('Module deleted successfully', {
          position: 'top-center',
          autoClose: 3000
        });
        setShowDeleteModal(false);
        setDeleteModuleId(null);
        fetchCourseModule(search); // Refresh the list
      } else {
        toast.error(response.message || 'Failed to delete module', {
          position: 'top-center',
          autoClose: 3000
        });
      }
    } catch (error) {
      console.error('Error:', error);
      toast.error('Failed to delete module: ' + error.message, {
        position: 'top-center',
        autoClose: 3000
      });
    } finally {
      setSaving(false);
    }
  };

  const handleEditModule = (module) => {
    if (!permissions.course_module_management_edit) {
      toast.warning("You don't have permission to edit modules");
      return;
    }
    setEditModuleId(module.id);
    setNewModuleName(module.title);
    setNewModuleDescription(module.subtitle);
    setShowModal(true);
  };

  const handleToggleStatus = async (e, module) => {
    e.stopPropagation();
    if (!permissions.course_module_management_status) {
      toast.warning("You don't have permission to change module status");
      return;
    }

    try {
      setStatusLoading(module.id);
      const newStatus = module.status === "Active" ? 0 : 1; // Convert status to number (0 for inactive, 1 for active)
      
      const response = await moduleStatus({
        course_id: decodedCourseId,
        module_id: module.id,
        status: newStatus
      });

      if (response.success) {
        toast.success(`Module ${newStatus ? 'activated' : 'deactivated'} successfully`, {
          position: 'top-center',
          autoClose: 3000
        });
        fetchCourseModule(search); // Refresh the list
      } else {
        toast.error(response.message || 'Failed to update module status', {
          position: 'top-center',
          autoClose: 3000
        });
      }
    } catch (error) {
      console.error('Error:', error);
      toast.error('Failed to update module status: ' + error.message, {
        position: 'top-center',
        autoClose: 3000
      });
    } finally {
      setStatusLoading(null);
    }
  };

  const getStatusClass = (status) => {
    const statusClasses = {
      Active: "bg-success-subtle text-success",
      Deactive: "bg-warning-subtle text-warning",
      Pending: "bg-info-subtle text-info",
    };
    return statusClasses[status] || "bg-secondary-subtle text-secondary";
  };

  const HandleRedirectToModuleContent = (moduleId) => {
    if (!permissions.course_module_content_management_view) {
      toast.warning("You don't have permission to view module content");
      return;
    }
    
    console.log('decodedCourseId-------', decodedCourseId);
    console.log('moduleId-------', moduleId);
    
    const encodedCourseId = encodeData(decodedCourseId);
    const encodedModuleId = encodeData(moduleId);

    console.log('encodedCourseId', encodedCourseId);
    console.log('encodedModuleId', encodedModuleId);

    navigate(`/admin/courses/module/${encodedCourseId}/content/${encodedModuleId}`);
  };

  return (
    <>
      {loading ? (
        <Loader />
      ) : (
        <>
          {/* Search and Add Module */}
          <div className="row mb-3">
            <div className="col-md-6 mt-2">
              <div className="d-flex gap-3 align-items-center">
                <div className="flex-grow-1 w-100" style={{ maxWidth: '300px' }}>
                  <div className="input-group">
                    <input
                      type="text"
                      className="form-control"
                      placeholder="Search modules..."
                      value={search}
                      onChange={(e) => setSearch(e.target.value)}
                    />
                  </div>
                </div>
              </div>
            </div>
            <div className="col-md-6 text-end mt-2 d-flex justify-content-end align-items-center">
              <button
                className="btn btn-primary text-nowrap"
                style={{ width: "150px" }}
                onClick={() => {
                  if (!permissions.course_module_management_create) {
                    toast.warning("You don't have permission to create modules");
                    return;
                  }
                  setEditModuleId(null);
                  setNewModuleName("");
                  setNewModuleDescription("");
                  setShowModal(true);
                }}
                disabled={!permissions.course_module_management_create}
                title={!permissions.course_module_management_create ? "You don't have permission to create modules" : "Add Module"}
              >
                Add Module
              </button>
            </div>
          </div>

          {classroomName && (
            <div className="row mb-3">
              <div className="col-12">
                <h6 className="text-muted mb-0">Course Name: {classroomName}</h6>
              </div>
            </div>
          )}

          {/* Module Cards */}
          <div className="row">
            <div className="col-12">
              <div className="row">
                {moduleData.length === 0 ? (
                  <div className="col-12">
                    <NoData 
                      message={search ? "No modules found for your search" : "No modules found. Click 'Add Module' to create one."} 
                      caption="No modules found"
                    />
                  </div>
                ) : (
                  moduleData.map((module) => (
                    <div className="col-12" key={module.id} style={{ cursor: permissions.course_module_content_management_view ? "pointer" : "default" }}>
                      <div 
                        className="card shadow-sm mb-3" 
                        onClick={() => HandleRedirectToModuleContent(module.id)}
                      >
                        <div className="card-body">
                          <div className="row">
                            <div className="col-12 d-flex justify-content-end">
                              <div className="d-flex gap-2">
                                <button
                                  className="btn btn-outline-dark btn-sm border border-dark"
                                  style={{ width: "44px", height: "44px" }}
                                  title={!permissions.course_module_management_edit ? "You don't have permission to edit modules" : "Edit Module"}
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleEditModule(module);
                                  }}
                                  disabled={!permissions.course_module_management_edit}
                                >
                                  <Icon icon="fluent:edit-24-regular" width="28" height="28" />
                                </button>
                                <button
                                  className="btn btn-outline-dark btn-sm border border-dark"
                                  style={{ width: "44px", height: "44px" }}
                                  title={!permissions.course_module_management_delete ? "You don't have permission to delete modules" : "Delete Module"}
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    if (!permissions.course_module_management_delete) {
                                      toast.warning("You don't have permission to delete modules");
                                      return;
                                    }
                                    setDeleteModuleId(module.id);
                                    setShowDeleteModal(true);
                                  }}
                                  disabled={!permissions.course_module_management_delete}
                                >
                                  <Icon icon="fluent:delete-24-regular" width="28" height="28" />
                                </button>
                                <button
                                  className={`btn ${module.status === "Active" ? "btn-success " : "btn-outline-warning"} btn-sm border`}
                                  style={{ width: "44px", height: "44px" }}
                                  title={!permissions.course_module_management_status ? "You don't have permission to change status" : `${module.status === "Active" ? "Deactivate" : "Activate"} Module`}
                                  onClick={(e) => handleToggleStatus(e, module)}
                                  disabled={statusLoading === module.id || !permissions.course_module_management_status}
                                >
                                  {statusLoading === module.id ? (
                                    <span className="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                  ) : (
                                    <Icon 
                                      icon={module.status === "Active" ? "fluent:checkmark-circle-24-regular" : "fluent:dismiss-circle-24-regular"} 
                                      width="28" 
                                      height="28"
                                    />
                                  )}
                                </button>
                              </div>
                            </div>
                            <div className="col-12">
                              <div className="mb-2">
                                <div className="d-flex align-items-center gap-2 mb-1">
                                  <h5 className="mb-0 text-truncate" style={{ maxWidth: '80%' }}>{module.title}</h5>
                                  <span className={`badge border-0 rounded-pill ${getStatusClass(module.status)}`}>
                                    {module.status}
                                  </span>
                                </div>
                                <p className="text-muted mb-0 text-break" style={{ wordWrap: 'break-word' }}>{module.subtitle}</p>
                              </div>
                              <div className="d-flex flex-wrap gap-2">
                                <div className="d-flex align-items-center gap-1 bg-light rounded-2 px-3 py-2">
                                  <Icon icon="fluent:video-24-regular" className="text-muted" width="14" />
                                  <span className="text-muted small">Videos {module.videos}</span>
                                </div>
                                <div className="d-flex align-items-center gap-1 bg-light rounded-2 px-3 py-2">
                                  <Icon icon="fluent:clipboard-task-24-regular" className="text-muted" width="14" />
                                  <span className="text-muted small">Assessments {module.assessments}</span>
                                </div>
                                <div className="d-flex align-items-center gap-1 bg-light rounded-2 px-3 py-2">
                                  <Icon icon="fluent:poll-24-regular" className="text-muted" width="14" />
                                  <span className="text-muted small">Surveys {module.surveys}</span>
                                </div>
                                <div className="d-flex align-items-center gap-1 bg-light rounded-2 px-3 py-2">
                                  <Icon icon="fluent:document-24-regular" className="text-muted" width="14" />
                                  <span className="text-muted small">Documents {module.documents}</span>
                                </div>
                                <div className="d-flex align-items-center gap-1 bg-light rounded-2 px-3 py-2">
                                  <Icon icon="fluent:task-list-24-regular" className="text-muted" width="14" />
                                  <span className="text-muted small">Assignments {module.assignments}</span>
                                </div>
                                <div className="d-flex align-items-center gap-1 bg-light rounded-2 px-3 py-2">
                                  <Icon icon="fluent:document-24-regular" className="text-muted" width="14" />
                                  <span className="text-muted small">Total {module.totalContent}</span>
                                </div>
                              </div>
                            </div>
                            {permissions.course_module_content_management_view && (
                              <div className="bg-primary-subtle rounded-2 p-2 mt-3">
                                <div className="d-flex align-items-center gap-2">
                                  <Icon icon="fluent:info-24-regular" className="text-primary" width="16" />
                                  <span className="text-primary small">Click to manage content</span>
                                </div>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </div>
          </div>

          {/* Add/Edit Module Modal */}
          {showModal && (
            <div className="modal show d-block fade" tabIndex="-1" style={{ backgroundColor: "rgba(0,0,0,0.5)" }}>
              <div className="modal-dialog">
                <div className="modal-content">
                  <div className="modal-header">
                    <h5 className="modal-title">{editModuleId ? "Edit Module" : "Add New Module"}</h5>
                    <button type="button" className="btn-close" onClick={() => setShowModal(false)}></button>
                  </div>
                  <div className="modal-body">
                    {saving ? (
                      <div className="text-center py-4">
                        <Loader />
                      </div>
                    ) : (
                      <>
                        <div className="mb-3">
                          <label className="form-label">Module Name *</label>
                          <input
                            type="text"
                            className={`form-control ${errors.moduleName ? 'is-invalid' : ''}`}
                            value={newModuleName}
                            onChange={(e) => {
                              setNewModuleName(e.target.value);
                              if (errors.moduleName) {
                                setErrors({...errors, moduleName: ""});
                              }
                            }}
                            placeholder="Enter module name"
                            maxLength={30}
                          />
                          {errors.moduleName ? (
                            <div className="invalid-feedback">{errors.moduleName}</div>
                          ) : (
                            <small className="text-muted">Maximum 30 characters allowed ({30 - newModuleName.length} remaining)</small>
                          )}
                        </div>
                        <div className="mb-3">
                          <label className="form-label">Module Description *</label>
                          <textarea
                            className={`form-control ${errors.moduleDescription ? 'is-invalid' : ''}`}
                            rows="3"
                            value={newModuleDescription}
                            onChange={(e) => {
                              setNewModuleDescription(e.target.value);
                              if (errors.moduleDescription) {
                                setErrors({...errors, moduleDescription: ""});
                              }
                            }}
                            placeholder="Enter description"
                            maxLength={50}
                          ></textarea>
                          {errors.moduleDescription ? (
                            <div className="invalid-feedback">{errors.moduleDescription}</div>
                          ) : (
                            <small className="text-muted">Maximum 50 characters allowed ({50 - newModuleDescription.length} remaining)</small>
                          )}
                        </div>
                      </>
                    )}
                  </div>
                  <div className="modal-footer">
                    <button 
                      className="btn btn-secondary" 
                      onClick={() => setShowModal(false)}
                      disabled={saving}
                    >
                      Cancel
                    </button>
                    <button
                      className="btn btn-primary"
                      onClick={handleSaveModule}
                      disabled={saving}
                    >
                      {saving ? (
                        <>
                          <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                          Saving...
                        </>
                      ) : (
                        editModuleId ? "Update Module" : "Save Module"
                      )}
                    </button>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Delete Confirmation Modal */}
          {showDeleteModal && (
            <div className="modal show d-block fade" tabIndex="-1" style={{ backgroundColor: "rgba(0,0,0,0.5)" }}>
              <div className="modal-dialog">
                <div className="modal-content">
                  <div className="modal-header">
                    <h5 className="modal-title text-danger">Confirm Deletion</h5>
                    <button type="button" className="btn-close" onClick={() => setShowDeleteModal(false)}></button>
                  </div>
                  <div className="modal-body">
                    {saving ? (
                      <div className="text-center py-4">
                        <Loader />
                      </div>
                    ) : (
                      <p>Are you sure you want to delete this module?</p>
                    )}
                  </div>
                  <div className="modal-footer">
                    <button 
                      className="btn btn-secondary" 
                      onClick={() => setShowDeleteModal(false)}
                      disabled={saving}
                    >
                      Cancel
                    </button>
                    <button 
                      className="btn btn-danger" 
                      onClick={handleDeleteModule}
                      disabled={saving}
                    >
                      {saving ? (
                        <>
                          <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                          Deleting...
                        </>
                      ) : (
                        "Confirm Delete"
                      )}
                    </button>
                  </div>
                </div>
              </div>
            </div>
          )}
        </>
      )}
    </>
  );
}

export default CourseModule;
