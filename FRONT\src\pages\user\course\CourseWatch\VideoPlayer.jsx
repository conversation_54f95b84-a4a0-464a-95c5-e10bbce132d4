import React, { useEffect, useRef, useState, useCallback } from 'react';
import CourseOverview from './CourseOverview';
import CourseComment from './CourseComment';
import CourseRating from './CourseRating';
import CourseNotes from './CourseNotes';
import { saveRecentVideo } from '../../../../services/userService';
import './VideoPlayer.css';

const bitmovinKey = process.env.REACT_APP_BITMOVIN_PLAYER_KEY;

function VideoPlayer({ videoUrl, contentData, courseId, onVideoProgress }) {
  const playerRef = useRef(null);
  const playerContainerRef = useRef(null);
  const [activeTab, setActiveTab] = useState('overview');
  const [key, setKey] = useState(0);
  const [hasReached30Percent, setHasReached30Percent] = useState(false);
  const progressIntervalRef = useRef(null);
  const trackVideoProgressRef = useRef(null);
  const currentVideoIdRef = useRef(null);
  const currentVideoDataRef = useRef(null);

  // Reset progress tracking when content changes
  useEffect(() => {
    console.log('🔄 Content changed - Video ID:', contentData?.id, 'Title:', contentData?.title);
    console.log('🔄 Full contentData object:', contentData);

    // Update refs with current video data
    currentVideoIdRef.current = contentData?.id;
    currentVideoDataRef.current = contentData;
    console.log('📝 Updated refs - Video ID:', currentVideoIdRef.current, 'Title:', currentVideoDataRef.current?.title);

    setActiveTab('overview');
    setKey(prevKey => prevKey + 1);
    setHasReached30Percent(false);

    // Clear existing progress interval when content changes
    if (progressIntervalRef.current) {
      console.log('🔄 Clearing old progress interval');
      clearInterval(progressIntervalRef.current);
      progressIntervalRef.current = null;
    }
  }, [contentData?.id]);

  // Function to save video progress with specific video data
  const saveVideoProgressWithData = useCallback(async (videoData, duration) => {
    if (!videoData?.id) return;

    try {
      const jsonData = {
        video_detail_id: videoData.id,
        completed_stream: duration
      };
      console.log('🚀 Saving video progress for:', videoData.title);
      console.log('🚀 Video ID being sent:', videoData.id);
      console.log('🚀 Complete payload:', jsonData);
      
      // Wait for the save operation to complete
      await saveRecentVideo(jsonData);
      console.log('✅ Video progress saved successfully');

      // Notify parent component about progress update after save is complete
      if (onVideoProgress) {
        onVideoProgress(videoData.id);
      }
    } catch (error) {
      console.error('Error saving video progress:', error);
    }
  }, [onVideoProgress]);

  // Function to save video progress (legacy - using current contentData)
  const saveVideoProgress = useCallback(async (duration) => {
    if (!contentData?.id) return;

    try {
      const jsonData = {
        video_detail_id: contentData.id,
        completed_stream: duration
      };
      console.log('🚀 Saving video progress for:', contentData.title);
      console.log('🚀 Video ID being sent:', contentData.id);
      console.log('🚀 Complete payload:', jsonData);
      await saveRecentVideo(jsonData);

      // Notify parent component about progress update
      if (onVideoProgress) {
        onVideoProgress(contentData.id);
      }
    } catch (error) {
      console.error('Error saving video progress:', error);
    }
  }, [contentData, onVideoProgress]);

  // Function to format time from seconds to mm:ss
  const formatTime = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${minutes}:${secs < 10 ? '0' : ''}${secs}`;
  };

  // Function to track video progress
  const trackVideoProgress = useCallback(() => {
    if (!playerRef.current) return;

    try {
      const currentTime = playerRef.current.getCurrentTime();
      const duration = playerRef.current.getDuration();

      if (currentTime && duration && duration > 0) {
        const progressPercentage = (currentTime / duration) * 100;
        const formattedTime = formatTime(currentTime);

        console.log(`Video progress: ${progressPercentage.toFixed(2)}% (${formattedTime})`);

        // Save progress when reaching 80% for the first time
        if (progressPercentage >= 80 && !hasReached30Percent) {
          console.log(`🎯 Reached 80% completion (${progressPercentage.toFixed(2)}%), saving progress...`);
          console.log(`📹 Video ID: ${currentVideoIdRef.current}, Time: ${formattedTime}`);
          console.log(`📹 Using ref data - Title: ${currentVideoDataRef.current?.title}`);
          setHasReached30Percent(true);

          // Call saveVideoProgress with the current video data from ref
          if (currentVideoDataRef.current?.id) {
            saveVideoProgressWithData(currentVideoDataRef.current, formattedTime);
          }
        }

        // Also save progress at other milestones
        if (progressPercentage >= 80) {
          if (currentVideoDataRef.current?.id) {
            saveVideoProgressWithData(currentVideoDataRef.current, formattedTime);
          }
        }
      }
    } catch (error) {
      console.error('Error tracking video progress:', error);
    }
  }, [hasReached30Percent, saveVideoProgressWithData]);

  // Update the ref whenever trackVideoProgress changes
  useEffect(() => {
    trackVideoProgressRef.current = trackVideoProgress;
  }, [trackVideoProgress]);

  // Reload video when content changes
  useEffect(() => {
    if (playerRef.current && videoUrl) {
      console.log('🔄 Reloading video for new content:', contentData?.title, 'ID:', contentData?.id);
      const source = {
        hls: videoUrl,
        progressive: videoUrl
      };

      playerRef.current.load(source).then(() => {
        console.log('✅ Video reloaded successfully for:', contentData?.title);
      }).catch((error) => {
        console.error('❌ Error reloading video:', error);
      });
    }
  }, [videoUrl, contentData?.id]);

  useEffect(() => {
    const script = document.createElement('script');
    script.src = 'https://cdn.bitmovin.com/player/web/8/bitmovinplayer.js';
    script.async = true;
    script.onload = initPlayer;
    document.body.appendChild(script);

    const link = document.createElement('link');
    link.href = 'https://cdn.bitmovin.com/player/web/8/bitmovinplayer-ui.css';
    link.rel = 'stylesheet';
    document.head.appendChild(link);

    return () => {
      if (playerRef.current) {
        playerRef.current.destroy();
      }
      if (progressIntervalRef.current) {
        clearInterval(progressIntervalRef.current);
      }
      document.body.removeChild(script);
      document.head.removeChild(link);
    };
  }, []);

  useEffect(() => {
    if (playerRef.current) {
      const source = { progressive: videoUrl };
      playerRef.current.load(source).catch((error) =>
        console.error('Error loading video source:', error)
      );
    }
  }, [videoUrl]);

  const initPlayer = () => {
    if (!window.bitmovin?.player) {
      console.error('Bitmovin player not loaded');
      return;
    }

    const playerConfig = {
      key: bitmovinKey,
      playback: {
        autoplay: true,
        muted: false
      },
      style: {
        width: '100%',
        aspectratio: '16:9'
      }
    };

    const source = { progressive: videoUrl };

    try {
      const container = playerContainerRef.current;
      const player = new window.bitmovin.player.Player(container, playerConfig);
      playerRef.current = player;

      player.load(source).then(() => {
        console.log('Player loaded successfully');

        // Change watermark link or hide it
        const watermark = document.querySelector('button.bmpui-ui-watermark');
        if (watermark) {
          // To hide:
          watermark.style.display = 'none';

          // OR to set a custom logo and link, comment above and use this:
          /*
          watermark.style.backgroundImage = "url('/your-logo.png')";
          watermark.style.backgroundSize = "contain";
          watermark.style.backgroundRepeat = "no-repeat";
          watermark.style.width = "120px";
          watermark.style.height = "40px";
          watermark.setAttribute('data-url', 'https://yourdomain.com');
          */
        }

        // Start progress tracking interval
        if (progressIntervalRef.current) {
          clearInterval(progressIntervalRef.current);
        }
        progressIntervalRef.current = setInterval(() => {
          if (trackVideoProgressRef.current) {
            trackVideoProgressRef.current();
          }
        }, 2000); // Check every 2 seconds
      }).catch((error) => {
        console.error('Error loading player:', error);
      });

      player.on('play', () => {
        console.log('Video started playing');
        // Start progress tracking when video plays
        if (progressIntervalRef.current) {
          clearInterval(progressIntervalRef.current);
        }
        console.log('🎬 Starting progress tracking for video ID:', currentVideoIdRef.current);
        progressIntervalRef.current = setInterval(() => {
          if (trackVideoProgressRef.current) {
            trackVideoProgressRef.current();
          }
        }, 2000);
      });

      player.on('paused', () => {
        console.log('Video paused');
        // Continue tracking even when paused to save current position
        if (trackVideoProgressRef.current) {
          trackVideoProgressRef.current();
        }
      });

      player.on('playbackfinished', () => {
        console.log('Video finished');
        // Save final progress when video finishes
        if (trackVideoProgressRef.current) {
          trackVideoProgressRef.current();
        }
        if (progressIntervalRef.current) {
          clearInterval(progressIntervalRef.current);
        }
      });
    } catch (error) {
      console.error('Error initializing player:', error);
    }
  };

  return (
    <>
      <div className="video-player-container">
        <div className="player-wrapper">
          <div ref={playerContainerRef} className="bitmovin-player"></div>
        </div>
      </div>

      <div className="video-tabs">
        <div className="tab-header mb-0">
          <ul className="nav nav-tabs" role="tablist">
            <li className="nav-item">
              <button
                className={`nav-link ${activeTab === 'overview' ? 'active' : ''}`}
                onClick={() => setActiveTab('overview')}
                role="tab"
              >
                Overview
              </button>
            </li>
            <li className="nav-item">
              <button
                className={`nav-link ${activeTab === 'notes' ? 'active' : ''}`}
                onClick={() => setActiveTab('notes')}
                role="tab"
              >
                Notes
              </button>
            </li>
            <li className="nav-item">
              <button
                className={`nav-link ${activeTab === 'comments' ? 'active' : ''}`}
                onClick={() => setActiveTab('comments')}
                role="tab"
              >
                Comments
              </button>
            </li>
            <li className="nav-item">
              <button
                className={`nav-link ${activeTab === 'ratings' ? 'active' : ''}`}
                onClick={() => setActiveTab('ratings')}
                role="tab"
              >
                Ratings
              </button>
            </li>
          </ul>
        </div>

        <div className="tab-content">
          {activeTab === 'overview' && (
            <div className="tab-pane fade show active" role="tabpanel">
              <div className="card border-0 p-0">
                <div className="card-body p-0">
                  <CourseOverview key={`overview-${key}`} contentData={contentData} />
                </div>
              </div>
            </div>
          )}

          {activeTab === 'notes' && (
            <div className="tab-pane fade show active" role="tabpanel">
              <div className="card border-0 p-0">
                <div className="card-body p-0">
                  <CourseNotes key={`notes-${key}`} contentData={contentData} player={playerRef.current} />
                </div>
              </div>
            </div>
          )}
 
          {activeTab === 'comments' && (
            <div className="tab-pane fade show active" role="tabpanel">
              <div className="card border-0 p-0">
                <div className="card-body p-0">
                  <CourseComment key={`comments-${key}`} contentData={contentData} player={playerRef.current} />
                </div>
              </div>
            </div>
          )}

          {activeTab === 'ratings' && (
            <div className="tab-pane fade show active" role="tabpanel">
              <div className="card border-0 p-0">
                <div className="card-body p-0">
                  <CourseRating key={`ratings-${key}`} contentData={{ ...contentData, course_id: courseId }} />
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </>
  );
}

export default VideoPlayer;
