// utils/encodeAndEncode.js

export function encodeData(obj) {
  try {
    const jsonString = JSON.stringify(obj);
    const utf8Bytes = new TextEncoder().encode(jsonString);
    const base64String = btoa(String.fromCharCode(...utf8Bytes));
    return encodeURIComponent(base64String);
  } catch (error) {
    console.error('Encoding failed:', error);
    return '';
  }
}

export function decodeData(str) {
  try {
    const base64String = decodeURIComponent(str);
    const binaryString = atob(base64String);
    const utf8Bytes = Uint8Array.from(binaryString, c => c.charCodeAt(0));
    return JSON.parse(new TextDecoder().decode(utf8Bytes));
  } catch (error) {
    console.error('Decoding failed:', error);
    return null;
  }
}
