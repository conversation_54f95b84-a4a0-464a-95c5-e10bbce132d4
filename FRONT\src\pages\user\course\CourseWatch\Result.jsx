import React, { useState, useEffect } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { getAssessmentResultsDetailed } from "../../../../services/userService";
import { Icon } from "@iconify/react";

function Result() {
  const location = useLocation();
  const navigate = useNavigate();
  const assessmentId = location.state?.assessmentId;
  const [assessmentResults, setAssessmentResults] = useState(null);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);

  useEffect(() => {
    if (assessmentId) fetchResults();
  }, [assessmentId]);

  async function fetchResults() {
    try {
      const response = await getAssessmentResultsDetailed(assessmentId);
      console.log("response", response);
      setAssessmentResults(response.data);
    } catch (error) {
      console.log("error", error);
    }
  }

  const handlePrevQuestion = () => {
    setCurrentQuestionIndex((prev) => Math.max(0, prev - 1));
  };

  const handleNextQuestion = () => {
    if (assessmentResults) {
      setCurrentQuestionIndex((prev) =>
        Math.min(assessmentResults.questions.length - 1, prev + 1)
      );
    }
  };

  const handlePlayVideo = (videoId, timestamp, videoUrl) => {
    navigate("/user/resultvideo", {
      state: {
        videoId,
        timestamp,
        videoUrl,
      },
    });
  };

  const renderMediaContent = (type, url, content, showContentValue = false) => (
    <div className="my-3 text-center">
      {showContentValue && <div className="mb-2 fw-semibold">{content}</div>}
      {type === "image" && (
        <img
          src={url}
          alt={content}
          className="img-fluid rounded"
          style={{
            maxWidth: "300px",
            maxHeight: "200px",
            objectFit: "contain",
          }}
        />
      )}
      {type === "audio" && (
        <audio
          controls
          src={url}
          className="w-100"
          style={{ maxWidth: "300px" }}
        >
          Your browser does not support audio.
        </audio>
      )}
    </div>
  );

  if (!assessmentResults || !assessmentId) {
    return <div className="text-center py-5 fs-5 text-muted">Loading...</div>;
  }

  const currentQuestion = assessmentResults.questions[currentQuestionIndex];
  if (!currentQuestion) {
    return (
      <div className="text-center py-5 fs-5 text-muted">
        Question data not available
      </div>
    );
  }

  const userAnswers = currentQuestion.user_answer
    ? JSON.parse(currentQuestion.user_answer)
    : [];
  const correctOptions = currentQuestion.correct_option || [];

  return (
    <div className="container p-0 py-md-4">
      <div className="bg-white shadow-sm p-4 rounded">
        {/* Question Header */}
        <div className="mb-4">
          <div className="d-flex flex-column flex-md-row justify-content-between align-items-center">
            <div className="text-md-start text-center">
              <h4 className="fw-bold mb-1">
                Question {currentQuestionIndex + 1} of{" "}
                {assessmentResults.questions.length}
              </h4>
              {currentQuestion.video_timestamp && (
                <div className="text-muted small">
                  Timestamp: {currentQuestion.video_timestamp}
                </div>
              )}
            </div>

            {currentQuestion.ref_video_id &&
              currentQuestion.video_timestamp && (
                <div className="mt-3 mt-md-0">
                  <button
                    className="btn btn-primary btn-sm"
                    style={{ border: "none" }}
                    onClick={() =>
                      handlePlayVideo(
                        currentQuestion.ref_video_id,
                        currentQuestion.video_timestamp,
                        currentQuestion.video_url
                      )
                    }
                  >
                    <Icon icon="mdi:play-circle" /> Play Video
                  </button>
                </div>
              )}
          </div>
        </div>

        {/* Question Text */}
        <div className="mb-4 text-start">
          <p className="fw-semibold">{currentQuestion.question}</p>
          {(currentQuestion.question_content_type === "image" ||
            currentQuestion.question_content_type === "audio") &&
            renderMediaContent(
              currentQuestion.question_content_type,
              currentQuestion.question_media_url,
              currentQuestion.question
            )}
        </div>

        {/* Options List */}
        <div className="mb-3">
          <h6 className="text-muted">Answers</h6>
          <div className="d-flex flex-column gap-3">
            {currentQuestion.options?.map((option) => {
              const isUserSelected = userAnswers.includes(option.id.toString());
              const isCorrectAnswer = correctOptions.includes(
                option.content_value
              );

              return (
                <div
                  key={option.id}
                  className={`border p-3 rounded ${
                    isUserSelected || isCorrectAnswer ? "bg-light" : ""
                  }`}
                >
                  {option.content_type === "text" ? (
                    <p className="mb-2">{option.content_value}</p>
                  ) : (
                    renderMediaContent(
                      option.content_type,
                      option.media_url,
                      option.content_value,
                      true
                    )
                  )}
                  <div className="d-flex flex-wrap gap-2 mt-2">
                    {isUserSelected && (
                      <span
                        className="badge"
                        style={{ backgroundColor: "#e0f3ff", color: "#0277bd" }}
                      >
                        Your Choice
                      </span>
                    )}
                    {isCorrectAnswer && (
                      <span
                        className="badge"
                        style={{ backgroundColor: "#e6f7ec", color: "#2e7d32" }}
                      >
                        Correct Answer
                      </span>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Feedback */}
        <div className="text-center mt-4">
          {currentQuestion.user_is_correct ? (
            <div className="text-success d-flex justify-content-center align-items-center gap-2">
              <Icon icon="mdi:check-circle" />
              <span className="fw-semibold">Correct</span>
            </div>
          ) : (
            <div className="text-danger d-flex justify-content-center align-items-center gap-2">
              <Icon icon="mdi:close-circle" />
              <span className="fw-semibold">Incorrect</span>
            </div>
          )}
        </div>

        {/* Navigation Buttons */}
        <div className="row mt-4 pt-3 border-top">
          <div className="col-12 d-flex flex-column flex-md-row justify-content-center gap-3 mt-2">
            <button
              onClick={handlePrevQuestion}
              disabled={currentQuestionIndex === 0}
              className="btn btn-secondary w-100 w-md-auto px-4"
            >
              <Icon icon="mdi:chevron-left" /> Previous
            </button>
            <button
              onClick={handleNextQuestion}
              disabled={
                currentQuestionIndex === assessmentResults.questions.length - 1
              }
              className="btn btn-primary w-100 w-md-auto px-4"
            >
              Next <Icon icon="mdi:chevron-right" />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

export default Result;
