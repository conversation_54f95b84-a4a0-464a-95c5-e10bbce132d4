We need to install Libra-Office first


Step 1: Download and Install Poppler


For Windows:

Download the latest Poppler for Windows from:

https://github.com/oschwartz10612/poppler-windows/releases

Extract the downloaded ZIP file (e.g., poppler-23.11.0-x86_64.zip).

Move the extracted folder to a permanent location, e.g., C:\poppler\.



Step 2: Add Poppler to System PATH

Open Start Menu and search for Environment Variables.

Click Edit the system environment variables.

Under System Properties, click Environment Variables.

Find the Path variable under System Variables, and click Edit.

Click New, then add the path to <PERSON><PERSON>'s bin directory, e.g.:C:\poppler\bin


Click OK on all windows to save the changes.

<b>Step 3: Verify Installation
Open Command Prompt (cmd) and run:</b>
<br>
<b>poppler --version</b>


or


<b>pdfinfo -v</b>



If Poppler is correctly installed, it should display the version.



<b>For ubuntu:

Installing Poppler on Ubuntu Server</b>
Since you're using Ubuntu, installing Poppler is straightforward with apt.

Step 1: Install Poppler
Run the following command to install <PERSON>pler:

<i>sudo apt update</i>
<i>sudo apt install poppler-utils</i>



This will install poppler-utils, which includes pdfinfo and other tools required by pdf2image.

Step 2: Verify Installation
Check if Poppler is installed correctly by running:

<i>pdfinfo --version</i>

If installed, it should display the version.




<b>For Role permissions:(Admin and Trainee)</b>
<ul>
    <li>we need to import the role_permissons.sql file from db/utils in the organization table</li>
</ul>
Reason is to have no hustle for permissions related issues.


<ul>
    <li>git clone https://gitlab.com/ellipsonic/lms_backend.git</li>
    <li>cd lms_backend</li>
    <li>npm install</li>
    <li>pm2 start </li>
</ul>

In Server for Python3 we need to set virtual environment

<ul>
    <li>cd /root/lms_backend_new/src/Organization/controller/certificates/</li>
    <li>python3 -m venv myenv</li>
    <li>source myenv/bin/activate</li>
    <li>pip install --upgrade pip</li>
    <li>pip install pdf2image Pillow</li>
    or
    <li>pip install -r requirements.txt</li>
</ul>



To export database from server:
mysqldump --column-statistics=0 --host=************** --port=3306 --default-character-set=utf8 --user=lms_remote --protocol=tcp --routines --events --triggers --no-data --databases lmsr > lms_schema_only.sql

To import database to server:
mysqlrestore --host=************** --port=3306 --default-character-set=utf8 --user=lms_remote --protocol=tcp --routines --events --triggers --databases lmsr < lms_schema_only.sql