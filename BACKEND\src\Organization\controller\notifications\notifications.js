const { mysqlServerConnection } = require('../../../db/db');

const getNotifications = async (req, res) => {
    const { userId } = req.user;
    const page = req.body.page || 1;
    const limit = req.body.limit || 10;
    const offset = (page - 1) * limit;
    const search = req.body.search || '';
    const db_name = req.user.db_name;

    console.log("REQUEST QUERY", req.body);

    try {
        // Construct the main SQL query
        let sqlQuery = `
            SELECT * FROM ${db_name}.notifications
            WHERE user_id = ? AND is_deleted = 0
        `;
        const placeholders = [userId];

        if (search) {
            sqlQuery += ` AND title LIKE ?`;
            placeholders.push(`%${search}%`);
        }

        sqlQuery += `
            ORDER BY createdAt DESC
            LIMIT ? OFFSET ?
        `;
        placeholders.push(limit, offset);

        // Fetch notifications with pagination
        const [notifications] = await mysqlServerConnection.query(sqlQuery, placeholders);

        console.log("NOTIFICATIONS", notifications.length);
        console.log(sqlQuery, placeholders);

// Construct the count query
let countQuery = `
    SELECT COUNT(*) AS totalCount FROM ${db_name}.notifications
    WHERE user_id = ? AND is_deleted = 0
`;
const countPlaceholders = [userId];

if (search) {
    countQuery += ` AND title LIKE ?`;
    countPlaceholders.push(`%${search}%`);
}

// Fetch total count of notifications
const [countResult] = await mysqlServerConnection.query(countQuery, countPlaceholders);
        const totalCount = countResult[0].totalCount;

        // Calculate total pages
        const totalPages = Math.ceil(totalCount / limit);

        // Send response
        res.status(200).json({
            success: true,
            data: {
                response: {
                    notifications,
                    pagination: {
                        page,
                        limit,
                        totalPages,
                        totalCount,
                    },
                },
            },
        });
    } catch (error) {
        console.error("Error while fetching notifications:", error);
        res.status(500).json({ success: false, data: { message: 'Internal server error.' } });
    }
};

const NewgetNotifications = async (req, res) => {
    const { db_name, userId } = req.user;
    const { page = 1, limit = 20, search = '' } = req.body;
  
    console.log("📥 Incoming request to get notifications with pagination");
    console.log(" db_name:", db_name);
    console.log("👤 userId:", userId);
    console.log("📄 page:", page, "📏 limit:", limit, "🔍 search:", search);
  
    try {
      const offset = (page - 1) * limit;
      
      // Build search condition
      let searchCondition = '';
      let searchParams = [];
      
      if (search && search.trim() !== '') {
        searchCondition = 'AND (title LIKE ? OR body LIKE ?)';
        const searchTerm = `%${search.trim()}%`;
        searchParams = [searchTerm, searchTerm];
      }
  
      // ✅ Fetch notifications with pagination
      const [notifications] = await mysqlServerConnection.query(
        `SELECT 
           id,
           user_id,
           send_from,
           title,
           body,
           file_url,
           is_deleted,
           is_read,
           course_id,
           DATE_FORMAT(createdAt, '%Y-%m-%dT%H:%i:%sZ') AS createdAt,
           DATE_FORMAT(updatedAt, '%Y-%m-%dT%H:%i:%sZ') AS updatedAt
         FROM ${db_name}.notifications 
         WHERE user_id = ? AND is_deleted = 0 ${searchCondition}
         ORDER BY createdAt DESC 
         LIMIT ? OFFSET ?`,
        [userId, ...searchParams, parseInt(limit), offset]
      );
  
      console.log("NOTIFICATIONS fetched:", notifications.length);
  
      // ✅ Count total notifications with search
      const [countResult] = await mysqlServerConnection.query(
        `SELECT COUNT(*) AS totalCount 
         FROM ${db_name}.notifications 
         WHERE user_id = ? AND is_deleted = 0 ${searchCondition}`,
        [userId, ...searchParams]
      );
  
      const totalCount = countResult[0]?.totalCount || 0;
      const totalPages = Math.ceil(totalCount / limit);
      const hasNextPage = page < totalPages;
  
      console.log("🔢 Total count:", totalCount);
      console.log("📄 Total pages:", totalPages);
      console.log("➡️ Has next page:", hasNextPage);
  
      return res.status(200).json({
        success: true,
        message: 'Notifications fetched successfully',
        notifications,
        pagination: {
          currentPage: parseInt(page),
          totalPages,
          totalCount,
          limit: parseInt(limit),
          hasNextPage,
          hasPrevPage: page > 1
        }
      });
    } catch (error) {
      console.error("❌ Error while fetching notifications:");
      console.error("Message:", error.message);
      console.error("Stack:", error.stack);
      console.error("Full Error:", error);
  
      return res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
};

  




const markNotificationAsRead = async (req, res) => {
    const { db_name, userId } = req.user;
    const { notificationId } = req.body;

    console.log("USERid : ", userId);
    console.log("Notification ID : ", notificationId);
    console.log("Database Name: ", db_name);

    const sqlQuery = `
        UPDATE ?? .notifications
        SET is_read = true
        WHERE user_id = ? AND id = ?
    `;

    const placeholders = [db_name, userId, notificationId];

    try {
        const [result] = await mysqlServerConnection.query(sqlQuery, placeholders);

        // Log the result to check if rows were updated
        console.log("Query result:", result);

        if (result.affectedRows === 0) {
            return res.status(404).json({ success: false, data: { message: 'No matching notification found.' } });
        }

        res.status(200).json({ success: true, data: { message: 'Notification marked as read.' } });

    } catch (error) {
        console.error("Error while marking notification as read:", error);
        res.status(500).json({ success: false, data: { message: 'Internal server error.' } });
    }
};


const markAllNotificationsAsRead = async (req, res) => {

    const { db_name, userId } = req.user;
    console.log("UseR ID : ", userId);
    const sqlQuery = ` UPDATE ?? .notifications
    SET is_read = true WHERE user_id = ?`

    const placeholders = [db_name, userId];

    try {
        await mysqlServerConnection.query(sqlQuery, placeholders).then(() => {
            res.status(200).json({ success: true, data: { message: 'All Notification marked as read.' } });
        })
    } catch (error) {
        console.error("Error while marking notification as read:", error);
        res.status(500).json({ success: false, data: { message: 'Internal server error.' } });
    }
}




module.exports = {
    getNotifications,
    markNotificationAsRead,
    markAllNotificationsAsRead,
    NewgetNotifications
}

