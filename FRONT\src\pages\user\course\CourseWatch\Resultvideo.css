.result-video-container {
  max-width: 1200px;
  margin: 2rem auto;
  padding: 1rem;
  height: calc(100vh - 4rem); /* Adjust container height */
  display: flex;
  flex-direction: column;
}

.video-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.video-controls {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.back-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background-color: #f0f0f0;
  color: #333;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.2s;
}

.back-btn:hover {
  background-color: #e0e0e0;
}

.replay-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background-color: #2196f3;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.2s;
}

.replay-btn:hover {
  background-color: #1976d2;
}

.replay-btn svg {
  font-size: 1.2rem;
}

.timestamp-display {
  color: #666;
  font-size: 0.9rem;
  padding: 0.5rem;
  background: #f5f5f5;
  border-radius: 4px;
}

.video-wrapper {
  flex: 1; /* Take remaining space */
  width: 100%;
  background: #000;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  position: relative; /* Add position relative */
  min-height: 400px; /* Add minimum height */
}

.bitmovin-player {
  position: absolute; /* Position absolute within wrapper */
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.error-message {
  text-align: center;
  padding: 2rem;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.error-message p {
  margin-bottom: 1rem;
  color: #666;
  font-size: 1.1rem;
}

/* Add responsive styles */
@media (max-width: 768px) {
  .result-video-container {
    margin: 1rem auto;
    height: calc(100vh - 2rem);
  }

  .video-wrapper {
    min-height: 300px;
  }

  .video-header {
    flex-direction: column;
    gap: 1rem;
  }

  .video-controls {
    width: 100%;
    justify-content: space-between;
  }
} 