import { useState, useEffect, useCallback, Fragment } from 'react'
import { Icon } from '@iconify/react';
import { toast } from 'react-toastify';
import Loader from '../../../components/common/Loader';
import Nodata from '../../../components/common/NoData';
import { usePermissions } from '../../../context/PermissionsContext';
import {
  getClassroomAssignmentSubmissions,
  gradeClassroomAssignmentSubmission,
  getClassroomAssignmentDetails,
  userAssignmentStatus
} from '../../../services/adminService';

function ClassroomAssignmentResponse({decodedClassroomId, decodedAssignmentId}) {
  const { permissions } = usePermissions();
  console.log('decodedClassroomId',decodedClassroomId);
  console.log('decodedAssignmentId',decodedAssignmentId);

  // State management
  const [submissions, setSubmissions] = useState([]);
  const [expandedRows, setExpandedRows] = useState({});
  const [loading, setLoading] = useState(false);
  const [assignmentDetails, setAssignmentDetails] = useState(null);

  // Search and pagination
  const [searchTerm, setSearchTerm] = useState('');
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(10);
  const [totalPages, setTotalPages] = useState(1);
  const [totalSubmissions, setTotalSubmissions] = useState(0);

  // Fetch assignment details
  const fetchAssignmentDetails = useCallback(async () => {
    if (!decodedClassroomId || !decodedAssignmentId) return;

    try {
      const response = await getClassroomAssignmentDetails({
        class_id: decodedClassroomId,
        assignment_id: decodedAssignmentId
      });

      if (response && response.success) {
        setAssignmentDetails(response.data.assignment || {});
      } else {
        toast.error(response?.message || 'Failed to fetch assignment details');
      }
    } catch (error) {
      console.error('Error fetching assignment details:', error);
      toast.error('Error fetching assignment details');
    }
  }, [decodedClassroomId, decodedAssignmentId]);

  // Fetch submissions
  const fetchSubmissions = useCallback(async () => {
    if (!decodedClassroomId || !decodedAssignmentId) return;

    setLoading(true);
    try {
      const response = await getClassroomAssignmentSubmissions({
        class_id: decodedClassroomId,
        assignment_id: decodedAssignmentId,
        page: page,
        limit: limit,
        search: searchTerm
      });

      console.log("Get submissions response:", response);

      if (response && response.data) {
        const submissionsWithAnswers = response.data.map(submission => ({
          ...submission,
          questions: submission.answers || []
        }));

        setSubmissions(submissionsWithAnswers);

        if (response.pagination) {
          setTotalPages(response.pagination.totalPages || 1);
          setTotalSubmissions(response.pagination.total || 0);
        }
      }
    } catch (error) {
      console.error('Error fetching submissions:', error);
      toast.error('Error fetching submissions');
    } finally {
      setLoading(false);
    }
  }, [decodedClassroomId, decodedAssignmentId, page, limit, searchTerm]);

  // Initialize data
  useEffect(() => {
    if (decodedClassroomId && decodedAssignmentId) {
      fetchAssignmentDetails();
      fetchSubmissions();
    }
  }, [decodedClassroomId, decodedAssignmentId, fetchAssignmentDetails, fetchSubmissions]);

  // Handle search
  const handleSearch = (e) => {
    setSearchTerm(e.target.value);
    setPage(1);
  };

  // Handle page change
  const handlePageChange = (newPage) => {
    if (newPage > 0 && newPage <= totalPages) {
      setPage(newPage);
    }
  };

  // Handle limit change
  const handleLimitChange = (e) => {
    setLimit(Number(e.target.value));
    setPage(1);
  };

  // Toggle row expansion
  const toggleRowExpand = (userId) => {
    if (!permissions.classroom_assignment_response_view_analytics) {
      toast.warning("You don't have permission to view response details");
      return;
    }
    setExpandedRows(prev => ({
      ...prev,
      [userId]: !prev[userId]
    }));
  };

  // Handle marking answer as wrong
  const handleMarkWrong = async (question, userId) => {
    try {
      const response = await userAssignmentStatus({
        class_id: decodedClassroomId,
        assignment_id: decodedAssignmentId,
        question_id: question.question_id,
        user_id: userId,
        answer_status: 'wrong'
      });

      if (response.success) {
        setSubmissions(prevSubmissions =>
          prevSubmissions.map(submission => {
            if (submission.user_id === userId) {
              return {
                ...submission,
                questions: submission.questions.map(q =>
                  q.question_id === question.question_id
                    ? { ...q, answer_status: 'wrong' }
                    : q
                )
              };
            }
            return submission;
          })
        );
        toast.success(response.message || 'Answer marked as wrong');
      } else {
        toast.error(response.message || 'Failed to update answer status');
      }
    } catch (error) {
      console.error('Error updating answer status:', error);
      toast.error('An error occurred while updating the answer status');
    }
  };

  // Handle marking answer as right
  const handleMarkRight = async (question, userId) => {
    try {
      const response = await userAssignmentStatus({
        class_id: decodedClassroomId,
        assignment_id: decodedAssignmentId,
        question_id: question.question_id,
        user_id: userId,
        answer_status: 'right'
      });

      if (response.success) {
        setSubmissions(prevSubmissions =>
          prevSubmissions.map(submission => {
            if (submission.user_id === userId) {
              return {
                ...submission,
                questions: submission.questions.map(q =>
                  q.question_id === question.question_id
                    ? { ...q, answer_status: 'right' }
                    : q
                )
              };
            }
            return submission;
          })
        );
        toast.success(response.message || 'Answer marked as right');
      } else {
        toast.error(response.message || 'Failed to update answer status');
      }
    } catch (error) {
      console.error('Error updating answer status:', error);
      toast.error('An error occurred while updating the answer status');
    }
  };

  // Render media based on type
  const renderMedia = (url, type, altText = 'Media') => {
    if (!url) return null;

    if (type === 'image') {
      return <img src={url} alt={altText} className="img-fluid mb-3 rounded" style={{ maxWidth: '100%', maxHeight: '300px' }} />;
    } else if (type === 'audio') {
      return (
        <div className="mb-3">
          <audio controls className="w-100" style={{ minHeight: '50px' }}>
            <source src={url} type="audio/mpeg" />
            Your browser does not support the audio element.
          </audio>
        </div>
      );
    }
    return null;
  };

  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <>
      {/* Search section */}
      <div className="row mb-3">
        <div className="col-12 col-md-6 mb-2">
          <input
            type="text"
            className="form-control"
            placeholder="Search User"
            style={{ width: '300px' }}
            value={searchTerm}
            onChange={handleSearch}
          />
        </div>
      </div>

      {/* table */}
      <div className="row mb-3">
        <div className="col-12">
          <div className="card">
            {loading ? (
              <div className="d-flex justify-content-center my-5">
                <Loader />
              </div>
            ) : submissions.length === 0 ? (
              <div className="d-flex justify-content-center my-5">
                <Nodata message="No submissions found" />
              </div>
            ) : (
              <div className="table-responsive">
                <table className="table table-borderless mb-0">
                  <thead>
                    <tr>
                      <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500', width: '80px' }}>Sl No</th>
                      <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500' }}>User Name</th>
                      <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500' }}>Email</th>
                      <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500' }}>Total Questions</th>
                      <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500' }}>Attended</th>
                      <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500' }}>Remaining</th>
                      <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500', width: '100px' }}>Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {submissions.map((submission, index) => {
                      const isExpanded = expandedRows[submission.user_id];
                      return (
                        <Fragment key={submission.user_id || index}>
                          <tr className="border-bottom">
                            <td className="py-3 align-middle">{(page - 1) * limit + index + 1}</td>
                            <td className="py-3 align-middle">{submission.user_name || 'N/A'}</td>
                            <td className="py-3 align-middle">{submission.email || 'N/A'}</td>
                            <td className="py-3 align-middle">
                              <span className="badge bg-primary">{submission.total_questions || 0}</span>
                            </td>
                            <td className="py-3 align-middle">
                              <span className="badge bg-success">{submission.attended_questions || 0}</span>
                            </td>
                            <td className="py-3 align-middle">
                              <span className="badge bg-warning text-dark">{submission.remaining_questions || 0}</span>
                            </td>
                            <td className="py-3 align-middle">
                              <button
                                className="btn btn-outline-primary btn-sm border border-dark"
                                style={{ width: '36px', height: '36px' }}
                                title={!permissions.classroom_assignment_response_view_analytics ? "You don't have permission to view response details" : (isExpanded ? 'Hide details' : 'View details')}
                                onClick={() => toggleRowExpand(submission.user_id)}
                                disabled={!permissions.classroom_assignment_response_view_analytics}
                              >
                                <Icon
                                  icon={isExpanded ? "fluent:chevron-up-24-regular" : "fluent:chevron-down-24-regular"}
                                  width="16"
                                  height="16"
                                />
                              </button>
                            </td>
                          </tr>
                          {isExpanded && permissions.classroom_assignment_response_view_analytics && (
                            <tr>
                              <td colSpan={7} className="p-0">
                                <div className="border-top bg-light">
                                  <div className="p-3 border-bottom">
                                    <h5 className="mb-0 fw-light">Questions & Answers</h5>
                                  </div>
                                  <div className="p-4">
                                    {submission.questions && submission.questions.length > 0 ? (
                                      submission.questions.map((question, qIndex) => (
                                        <div key={qIndex} className="card mb-4">
                                          <div className="card-body">
                                            <div className="d-flex justify-content-between align-items-center mb-3">
                                              <h6 className="card-subtitle text-muted mb-0">
                                                <span className="fw-bold">Question {qIndex + 1}</span>
                                              </h6>
                                              <div className="d-flex gap-2">
                                                <button
                                                  className={`btn btn-sm ${question.answer_status === 'wrong' ? 'btn-danger' : 'btn-outline-danger'}`}
                                                  onClick={(e) => {
                                                    e.stopPropagation();
                                                    if (question.answer_status !== 'wrong') {
                                                      handleMarkWrong(question, submission.user_id);
                                                    }
                                                  }}
                                                  disabled={question.answer_status === 'wrong'}
                                                >
                                                  Wrong
                                                </button>
                                                <button
                                                  className={`btn btn-sm ${question.answer_status === 'right' ? 'btn-success' : 'btn-outline-success'}`}
                                                  onClick={(e) => {
                                                    e.stopPropagation();
                                                    if (question.answer_status !== 'right') {
                                                      handleMarkRight(question, submission.user_id);
                                                    }
                                                  }}
                                                  disabled={question.answer_status === 'right'}
                                                >
                                                  Right
                                                </button>
                                              </div>
                                            </div>

                                            {/* Question Content */}
                                            <div className="mb-3 p-3 bg-light rounded">
                                              <h6 className="mb-2 fw-light">Question:</h6>
                                              {question.question_text && (
                                                <p className="mb-2" style={{ whiteSpace: 'pre-wrap' }}>{question.question_text}</p>
                                              )}

                                              {/* Question Media */}
                                              {question.question_media_url && (
                                                <div className="mt-2">
                                                  <h6 className="mb-2 fw-light">Question Media:</h6>
                                                  {renderMedia(question.question_media_url, question.question_type === 'image' ? 'image' : 'audio')}
                                                </div>
                                              )}
                                            </div>

                                            {/* User's Answer */}
                                            <div className="mb-3 p-3 bg-light rounded">
                                              <h6 className="mb-2">
                                                <span className="badge bg-primary me-2">Trainee Answer</span>
                                              </h6>

                                              {/* Text Response */}
                                              {question.response_type === 'text_response' && question.answer_text && (
                                                <div className="p-2 bg-white rounded">
                                                  <p className="mb-0">{question.answer_text}</p>
                                                </div>
                                              )}

                                              {/* Image Response */}
                                              {question.response_type === 'image_response' && question.submission_media_url && (
                                                <div>
                                                  {renderMedia(question.submission_media_url, 'image', 'Trainee\'s image response')}
                                                </div>
                                              )}

                                              {/* Audio Response */}
                                              {question.response_type === 'audio_response' && question.submission_media_url && (
                                                <div>
                                                  <h6 className="mb-2">Audio Response:</h6>
                                                  {renderMedia(question.submission_media_url, 'audio')}
                                                </div>
                                              )}

                                              {/* No answer provided */}
                                              {!question.answer_text && !question.submission_media_url && (
                                                <div className="text-muted">No answer provided</div>
                                              )}
                                            </div>
                                          </div>
                                        </div>
                                      ))
                                    ) : (
                                      <p className="text-muted">No question details available</p>
                                    )}
                                  </div>
                                </div>
                              </td>
                            </tr>
                          )}
                        </Fragment>
                      );
                    })}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* record per page and pagination */}
      {!loading && submissions.length > 0 && (
        <div className="row">
          <div className="col-md-6 d-flex flex-row align-items-center">
            <select
              className="form-select"
              style={{ width: 'auto' }}
              value={limit}
              onChange={handleLimitChange}
            >
              <option value={5}>5 records per page</option>
              <option value={10}>10 records per page</option>
              <option value={25}>25 records per page</option>
              <option value={50}>50 records per page</option>
            </select>
            <small className="text-muted ms-2">
              Total {totalSubmissions} records
            </small>
          </div>
          <div className="col-md-6 d-flex justify-content-end align-items-center gap-3">
            <button
              className="btn btn-primary"
              style={{ width: '150px' }}
              onClick={() => handlePageChange(page - 1)}
              disabled={page === 1}
            >
              Prev
            </button>
            <span>Page {page} of {totalPages}</span>
            <button
              className="btn btn-primary"
              style={{ width: '150px' }}
              onClick={() => handlePageChange(page + 1)}
              disabled={page === totalPages}
            >
              Next
            </button>
          </div>
        </div>
      )}
    </>
  )
}

export default ClassroomAssignmentResponse
