import React, { useState, useEffect } from 'react';
import { Icon } from '@iconify/react';
import { Modal } from 'react-bootstrap';
import { toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import '../../../assets/styles/custom.css';
import countryData from '../../../utils/countory.json';
import useDebounce from '../../../hooks/useDebounce';
import { usePermissions } from '../../../context/PermissionsContext';
import {
  getTraineeDetails,
  activeInactiveTrainee,
  fetchUserDetails,
  deleteTrainee,
  editTraineeDetails,
  createNewOrganisationUser,
  BulkUploadUserDetails,
  getSample
} from '../../../services/traineeService';
import { useNavigate } from 'react-router-dom';
import { encodeData } from '../../../utils/encodeAndEncode';

function Trainees() {
  const navigate = useNavigate();
  const { permissions } = usePermissions();
  const [showModal, setShowModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [activeFilter, setActiveFilter] = useState('all'); // 'all', 'active', 'inactive'
  const [mode, setMode] = useState('Create'); // 'Create' or 'Edit'
  const [selectedTrainee, setSelectedTrainee] = useState(null);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [trainees, setTrainees] = useState([]);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    totalPages: 0,
    totalCount: 0
  });

  const [formData, setFormData] = useState({
    profileImage: null,
    profile_pic_url: '',
    upload_image_url: null,
    name: '',
    email: '',
    gender: '',
    dateOfBirth: '',
    phoneNumber: '',
    countryCode: '+91',
    employeeNo: '',
    companyCode: '',
    companyDescription: '',
    jobTitle: '',
    dateJoined: '',
    icNo: '',
    finNo: '',
    pwmRank: '',
    password: ''
  });

  const [errors, setErrors] = useState({});
  const [imagePreview, setImagePreview] = useState(null);
  const [showPassword, setShowPassword] = useState(false);

  // Debounced search term
  const debouncedSearchTerm = useDebounce(searchTerm, 500);

  // Validation function
  const validateForm = (mode) => {
    const newErrors = {};
    const emailRegex = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,4}$/;
    const phoneRegex = /^[0-9]+$/;
    const passwordRegex = /^(?=.*[a-zA-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/;

    // Required fields
    if (!formData.name.trim()) newErrors.name = 'Name is required';
    if (formData.name.length > 25) newErrors.name = 'Name must be less than 25 characters';
    if (!formData.email.trim()) newErrors.email = 'Email is required';
    if (formData.email && !emailRegex.test(formData.email)) newErrors.email = 'Invalid email format';
    if (!formData.gender) newErrors.gender = 'Gender is required';
    if (!formData.dateOfBirth) newErrors.dateOfBirth = 'Date of Birth is required';

    // Password validation (required for create, optional for edit)
    if (mode === 'Create' && !formData.password) newErrors.password = 'Password is required';
    if (formData.password && formData.password.length < 8) newErrors.password = 'Password must be at least 8 characters';
    if (formData.password && !passwordRegex.test(formData.password)) {
      newErrors.password = 'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character';
    }

    // Optional field validations
    if (formData.phoneNumber && !phoneRegex.test(formData.phoneNumber)) {
      newErrors.phoneNumber = 'Phone number must contain only digits';
    }
    if (formData.employeeNo.length > 50) newErrors.employeeNo = 'Employee No must be less than 50 characters';
    if (formData.companyCode.length > 50) newErrors.companyCode = 'Company Code must be less than 50 characters';
    if (formData.companyDescription.length > 255) newErrors.companyDescription = 'Company Description must be less than 255 characters';
    if (formData.pwmRank.length > 1000) newErrors.pwmRank = 'PWM Rank must be less than 1000 characters';
    if (formData.jobTitle.length > 1000) newErrors.jobTitle = 'Job Title must be less than 1000 characters';

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const handleImageChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      setFormData(prev => ({
        ...prev,
        profileImage: file,
        upload_image_url: file
      }));

      // Create preview URL
      const reader = new FileReader();
      reader.onloadend = () => {
        setImagePreview(reader.result);
      };
      reader.readAsDataURL(file);
    }
  };

  // Fetch trainees data
  const fetchTraineesData = async () => {
    try {
      setLoading(true);
      const status = activeFilter === 'all' ? null : activeFilter === 'active' ? true : false;
      const response = await getTraineeDetails({
        search: debouncedSearchTerm,
        page: pagination.page,
        limit: pagination.limit,
        status: status
      });

      console.log('Trainees data', response);

      if (response.success) {
        const { data, pagination: paginationData } = response.data;
        setTrainees(data);
        setPagination({
          page: paginationData.page,
          limit: paginationData.limit,
          totalPages: paginationData.totalPages,
          totalCount: paginationData.totalCount
        });
      } else {
        toast.error("Failed to fetch trainee data");
      }
    } catch (error) {
      console.error("Error fetching trainee data:", error);
      toast.error("Something went wrong");
    } finally {
      setLoading(false);
    }
  };

  // Reset form data
  const resetForm = () => {
    setFormData({
      profileImage: null,
      profile_pic_url: '',
      upload_image_url: null,
      name: '',
      email: '',
      gender: '',
      dateOfBirth: '',
      phoneNumber: '',
      countryCode: '+91',
      employeeNo: '',
      companyCode: '',
      companyDescription: '',
      jobTitle: '',
      dateJoined: '',
      icNo: '',
      finNo: '',
      pwmRank: '',
      password: ''
    });
    setImagePreview(null);
    setErrors({});
  };

  // Handle modal show for create/edit
  const handleShowModal = async (modeType, trainee = null) => {
    if (modeType === 'Create' && !permissions.trainee_management_create) {
      toast.warning("You don't have permission to create trainees");
      return;
    }
    if (modeType === 'Edit' && !permissions.trainee_management_edit) {
      toast.warning("You don't have permission to edit trainees");
      return;
    }
    setMode(modeType);
    setSelectedTrainee(trainee);
    setShowModal(true);

    if (modeType === 'Edit' && trainee) {
      try {
        setLoading(true);
        const response = await fetchUserDetails(trainee.id);

        if (response.success && response.data) {
          console.log('Full API Response:', response.data);
          const personalInfo = response.data.personal_information || {};
          const employeeDetails = response.data.employee_details || {};
          console.log('IC No from employeeDetails:', employeeDetails.ic_no);
          console.log('IC No from personalInfo:', personalInfo.ic_no);
          console.log('FIN No from employeeDetails:', employeeDetails.fin_no);
          console.log('FIN No from personalInfo:', personalInfo.fin_no);

          // Helper function to safely get string value and handle Buffer objects
          const getStringValue = (value) => {
            console.log('Processing value:', value, 'Type:', typeof value);

            if (value === null || value === undefined) return '';

            // If it's already a string, return it
            if (typeof value === 'string') return value;

            // Handle Buffer objects specifically
            if (value && typeof value === 'object') {
              // Check if it's a Buffer object (Node.js style)
              if (value.type === 'Buffer' && Array.isArray(value.data)) {
                try {
                  // Convert Buffer data array to string
                  const result = String.fromCharCode.apply(null, value.data);
                  console.log('Converted Buffer to string:', result);
                  return result;
                } catch (e) {
                  console.error('Error converting buffer to string:', e);
                  return '';
                }
              }

              // Check if it's a Uint8Array or similar
              if (value instanceof Uint8Array || (value.constructor && value.constructor.name === 'Uint8Array')) {
                try {
                  const result = String.fromCharCode.apply(null, Array.from(value));
                  console.log('Converted Uint8Array to string:', result);
                  return result;
                } catch (e) {
                  console.error('Error converting Uint8Array to string:', e);
                  return '';
                }
              }

              // Check if it has a data property that might be the actual value
              if (value.data !== undefined) {
                return getStringValue(value.data);
              }

              // For other objects, avoid "[object Object]"
              const stringValue = value.toString();
              if (stringValue === '[object Object]') {
                console.log('Returning empty string for [object Object]');
                return '';
              }
              return stringValue;
            }

            return String(value);
          };

          setFormData({
            profileImage: null,
            profile_pic_url: response.data.profile_pic || '',
            upload_image_url: null,
            name: getStringValue(personalInfo.name),
            email: getStringValue(personalInfo.email),
            phoneNumber: getStringValue(personalInfo.mobile),
            countryCode: getStringValue(personalInfo.mobilenocode) || '+91',
            employeeNo: getStringValue(employeeDetails.employee_no || personalInfo.employee_no),
            companyCode: getStringValue(employeeDetails.company_code || personalInfo.company_code),
            companyDescription: getStringValue(employeeDetails.company_description || personalInfo.company_description),
            icNo: getStringValue(employeeDetails.ic_no || personalInfo.ic_no),
            finNo: getStringValue(employeeDetails.fin_no || personalInfo.fin_no),
            gender: getStringValue(employeeDetails.gender || personalInfo.gender),
            dateOfBirth: personalInfo.date_of_birth ? personalInfo.date_of_birth.split('T')[0] : '',
            dateJoined: personalInfo.date_joined ? personalInfo.date_joined.split('T')[0] : '',
            pwmRank: getStringValue(employeeDetails.pwm_rank || personalInfo.pwm_rank),
            jobTitle: getStringValue(employeeDetails.job_title || personalInfo.job_title),
            password: ''
          });

          if (response.data.profile_pic) {
            setImagePreview(response.data.profile_pic);
          }
        } else {
          toast.error("Failed to fetch user details");
        }
      } catch (error) {
        console.error("Error fetching user details:", error);
        toast.error("Something went wrong");
      } finally {
        setLoading(false);
      }
    } else {
      resetForm();
    }
  };

  // Handle modal close
  const handleCloseModal = () => {
    setShowModal(false);
    setSelectedTrainee(null);
    resetForm();
    setMode('Create');
  };

  // Handle save (create/edit)
  const handleSave = async () => {
    try {
      if (!validateForm(mode)) {
        return;
      }

      setLoading(true);
      const formDataToSend = new FormData();

      // Append basic fields
      formDataToSend.append("name", formData.name);
      formDataToSend.append("email", formData.email);
      formDataToSend.append("mobileno_code", formData.countryCode);
      formDataToSend.append("mobile", formData.phoneNumber);
      formDataToSend.append("gender", formData.gender);
      formDataToSend.append("date_of_birth", formData.dateOfBirth);

      // Password handling
      if (mode === 'Create' || (mode === 'Edit' && formData.password.trim() !== "")) {
        formDataToSend.append("password", formData.password);
      }

      // Optional fields
      formDataToSend.append("employee_no", formData.employeeNo || "");
      formDataToSend.append("company_code", formData.companyCode || "");
      formDataToSend.append("company_description", formData.companyDescription || "");
      formDataToSend.append("ic_no", formData.icNo || "");
      formDataToSend.append("fin_no", formData.finNo || "");
      formDataToSend.append("date_joined", formData.dateJoined || "");
      formDataToSend.append("pwm_rank", formData.pwmRank || "");
      formDataToSend.append("job_title", formData.jobTitle || "");

      let response;
      if (mode === 'Edit' && selectedTrainee) {
        // Handle image for edit
        if (formData.upload_image_url !== null && formData.upload_image_url !== undefined) {
          formDataToSend.append("profile_pic", formData.upload_image_url);
        }
        response = await editTraineeDetails(selectedTrainee.id, formDataToSend);
      } else {
        // Handle image for create
        if (formData.upload_image_url !== null) {
          formDataToSend.append("profile_pic", formData.upload_image_url);
        } else if (formData.profile_pic_url && formData.profile_pic_url.trim() !== '') {
          formDataToSend.append("profile_pic_url", formData.profile_pic_url);
        }
        response = await createNewOrganisationUser(formDataToSend);
      }

      if (response.success) {
        toast.success(mode === 'Edit' ? "Trainee updated successfully" : "Trainee created successfully");
        handleCloseModal();
        fetchTraineesData();
      } else {
        toast.error("Failed to save trainee data");
      }
    } catch (error) {
      console.error("Error saving trainee:", error);
      toast.error(error.response?.data?.message || "Something went wrong");
    } finally {
      setLoading(false);
    }
  };

  // Handle delete trainee
  const handleDelete = async (trainee) => {
    if (!permissions.trainee_management_delete) {
      toast.warning("You don't have permission to delete trainees");
      return;
    }
    if (window.confirm(`Are you sure you want to delete ${trainee.name}?`)) {
      try {
        setLoading(true);
        const response = await deleteTrainee(trainee.id);

        if (response.success) {
          toast.success("Trainee deleted successfully");
          fetchTraineesData();
        } else {
          toast.error("Failed to delete trainee");
        }
      } catch (error) {
        console.error("Error deleting trainee:", error);
        toast.error("Something went wrong");
      } finally {
        setLoading(false);
      }
    }
  };

  // Handle status toggle
  const handleStatusToggle = async (trainee) => {
    if (!permissions.trainee_management_status) {
      toast.warning("You don't have permission to change trainee status");
      return;
    }
    const traineeId = trainee.id;
    try {
      const newStatus = trainee.is_blocked ? 0 : 1;
      const response = await activeInactiveTrainee(traineeId, newStatus);

      if (response.success) {
        // Update trainee status locally
        setTrainees(prevTrainees => prevTrainees.map(t => 
          t.id === traineeId ? { ...t, is_blocked: newStatus } : t
        ));
        toast.success(`Trainee ${newStatus === 0 ? 'activated' : 'deactivated'} successfully`);
      } else {
        toast.error("Failed to update status");
      }
    } catch (error) {
      console.error("Error updating status:", error);
      toast.error("Something went wrong");
    }
  };

  // Handle bulk upload
  const handleBulkUpload = async (file) => {
    try {
      if (!file) {
        toast.error("Please select a file to upload");
        return;
      }

      setLoading(true);
      const uploadFile = new FormData();
      uploadFile.append('file', file);
      uploadFile.append('link', window.location.origin);
      uploadFile.append('notify_email', 'false');

      const response = await BulkUploadUserDetails(uploadFile);

      if (response.status) {
        toast.success("Trainees uploaded successfully");
        fetchTraineesData();
      } else {
        toast.error("Failed to upload trainees");
      }
    } catch (error) {
      console.error("Error uploading file:", error);
      toast.error("Something went wrong");
    } finally {
      setLoading(false);
    }
  };

  // Handle sample download
  const handleSampleDownload = async () => {
    try {
      setLoading(true);
      const response = await getSample();

      const blob = new Blob([response], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = 'Trainee_Sample.xlsx';
      link.click();
      window.URL.revokeObjectURL(url);

      toast.success("Sample file downloaded successfully");
    } catch (error) {
      console.error("Error downloading sample:", error);
      toast.error("Failed to download sample file");
    } finally {
      setLoading(false);
    }
  };

  // Use effect for initial data fetch and search
  useEffect(() => {
    fetchTraineesData();
  }, [pagination.page, pagination.limit, activeFilter, debouncedSearchTerm]);

  // Filter trainees based on status
  const filteredTrainees = trainees;

  const handleAnalytics = (traineeId) => {
    if (!permissions.trainee_management_view_analytics) {
      toast.warning("You don't have permission to view trainee analytics");
      return;
    }
    const encodedTraineeId = encodeData(traineeId.toString()); // Convert ID to string before encoding
    // Navigate to trainee analytics page with the trainee ID
    navigate(`/admin/traineesAnalytics/${encodedTraineeId}`);
  };

  return (
    <>
      {/* Search, Filter and Add Trainee Button */}
      <div className="row mb-3">
        <div className="col-12">
          <div className="row">

            <div className="col-12 col-md-4">
              <input
                type="text"
                className="form-control"
                placeholder="Search trainees..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>

            <div className="col-12 col-md-8 d-flex gap-2 justify-content-end">
              <select
                className="form-select"
                value={activeFilter}
                onChange={(e) => setActiveFilter(e.target.value)}
                style={{ width: '250px' }}
              >
                <option value="all">All Trainees</option>
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
              </select>

              {/* <button
                className="btn btn-outline-primary"
                onClick={handleSampleDownload}
                disabled={loading}
                style={{ width: '120px' }}
              >
                <Icon icon="fluent:document-arrow-down-24-regular" width="16" height="16" className="me-1" />
                Sample
              </button>

              <label className="btn btn-outline-secondary" style={{ width: '120px' }}>
                <Icon icon="fluent:arrow-upload-24-regular" width="16" height="16" className="me-1" />
                Upload
                <input
                  type="file"
                  accept=".xlsx,.xls"
                  onChange={(e) => handleBulkUpload(e.target.files[0])}
                  hidden
                />
              </label> */}

              <button
                className="btn btn-primary"
                onClick={() => handleShowModal('Create')}
                disabled={loading || !permissions.trainee_management_create}
                style={{ width: '250px' }}
              >
                <Icon icon="fluent:add-24-regular" width="20" height="20" className="me-2" />
                Add Trainee
              </button>
            </div>



          </div>
        </div>
      </div>

      {/* Table */}
      <div className="row mb-3">
        <div className="col-12">
          <div className="card">
            <div className="table-responsive">
              <table className="table table-borderless mb-0">
                <thead>
                  <tr>
                    <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500', width: '80px' }}>SL No.</th>
                    <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500',  width: '100px' }}>Trainees</th>
                    <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500', width: '150px' }} className='text-center'> Active Course</th>
                    <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500', width: '150px' }} className='text-center'>Certificates</th>
                    <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500', width: '120px' }}>Status</th>
                    <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500', width: '150px' }}>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {loading ? (
                    <tr>
                      <td colSpan="6" className="text-center py-4">
                        <div className="spinner-border text-primary" role="status">
                          <span className="visually-hidden">Loading...</span>
                        </div>
                      </td>
                    </tr>
                  ) : filteredTrainees.length === 0 ? (
                    <tr>
                      <td colSpan="6" className="text-center py-4 text-muted">
                        No trainees found
                      </td>
                    </tr>
                  ) : (
                    filteredTrainees.map((trainee, index) => (
                      <tr 
                        key={trainee.id} 
                        className="border-bottom clickable-row" 
                        onClick={(e) => {
                          if (!e.target.closest('.action-buttons') && permissions.trainee_management_view_analytics) {
                            handleAnalytics(trainee.id);
                          }
                        }}
                        style={{ cursor: permissions.trainee_management_view_analytics ? 'pointer' : 'default' }}
                      >
                        <td className="py-3 align-middle">
                          {(pagination.page - 1) * pagination.limit + index + 1}
                        </td>
                        <td className="py-3 align-middle">
                          <div className="d-flex align-items-center">
                            {trainee.profile_pic_url ? (
                              <img
                                src={trainee.profile_pic_url}
                                alt="Profile"
                                className="rounded-circle me-3"
                                style={{
                                  height: "40px",
                                  width: '40px',
                                  objectFit: 'cover',
                                  border: '2px solid #e9ecef'
                                }}
                              />
                            ) : (
                              <div
                                className="rounded-circle me-3 d-flex align-items-center justify-content-center bg-secondary text-white"
                                style={{ height: "40px", width: '40px', fontSize: '14px', fontWeight: '600' }}
                              >
                                {trainee.name ? trainee.name[0].toUpperCase() : "?"}
                              </div>
                            )}
                            <div>
                              <div className="fw-semibold text-truncate" style={{ maxWidth: '400px' }}>{trainee.name}</div>
                              <div className="text-muted small">{trainee.email}</div>
                            </div>
                          </div>
                        </td>
                        <td className="py-3 align-middle text-center">{trainee.active_courses || 0}</td>
                        <td className="py-3 align-middle text-center">{trainee.certificate_count || 0}</td>
                        <td className="py-3 align-middle ">
                          <span className={`badge border ${!trainee.is_blocked ? 'border-success text-success' : 'border-danger text-danger'} bg-white d-inline-flex align-items-center gap-1`}>
                            <Icon
                              icon={!trainee.is_blocked ? 'fluent:presence-available-16-regular' : 'fluent:presence-blocked-16-regular'}
                              width="12"
                              height="12"
                            />
                            {!trainee.is_blocked ? 'Active' : 'Inactive'}
                          </span>
                        </td>
                        <td className="py-3 d-flex justify-content-start align-items-center  ">
                          <div className="d-flex gap-2  justify-content-center align-items-center">
                            <button
                              className="btn btn-outline-dark btn-sm border border-dark"
                              style={{ width: '36px', height: '36px' }}
                              title="Edit"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleShowModal('Edit', trainee);
                              }}
                              disabled={loading || !permissions.trainee_management_edit}
                            >
                              <Icon icon="fluent:edit-24-regular" width="16" height="16" />
                            </button>
                            <button
                              className={`btn btn-outline-${!trainee.is_blocked ? 'success' : 'danger'} btn-sm border border-${!trainee.is_blocked ? 'danger' : 'success'}`}
                              style={{ width: '36px', height: '36px' }}
                              title={!trainee.is_blocked ? 'Deactivate' : 'Activate'}
                              onClick={(e) => {
                                e.stopPropagation();
                                handleStatusToggle(trainee);
                              }}
                              disabled={loading || !permissions.trainee_management_status}
                            >
                              <Icon
                                icon={!trainee.is_blocked ? 'fluent:presence-available-24-regular' : 'fluent:presence-blocked-24-regular'}
                                width="16"
                                height="16"
                              />
                            </button>
                            <button
                              className="btn btn-outline-danger btn-sm border border-danger"
                              style={{ width: '36px', height: '36px' }}
                              title="Delete"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleDelete(trainee);
                              }}
                              disabled={loading || !permissions.trainee_management_delete}
                            >
                              <Icon icon="fluent:delete-24-regular" width="16" height="16" />
                            </button>
                            <button
                              className="btn btn-outline-dark btn-sm border border-dark"
                              style={{ width: '36px', height: '36px' }}
                              title="View Analytics"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleAnalytics(trainee.id);
                              }}
                              disabled={!permissions.trainee_management_view_analytics}
                            >
                              <Icon icon="fluent:data-trending-24-regular" width="16" height="16" />
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))
                  )}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>

      {/* Pagination */}
      <div className="row">
        <div className="col-md-6 d-flex align-items-center gap-3">
          <div className="d-flex align-items-center gap-2">
            <span className="text-muted">Items per page:</span>
            <select
              className="form-select"
              style={{ width: '80px' }}
              value={pagination.limit}
              onChange={(e) => setPagination(prev => ({
                ...prev,
                limit: Number(e.target.value),
                page: 1
              }))}
            >
              <option value={5}>5</option>
              <option value={10}>10</option>
              <option value={25}>25</option>
              <option value={50}>50</option>
            </select>
          </div>
          <div className="text-muted">
            Total Records: {pagination.totalCount}
          </div>
        </div>
        <div className="col-md-6 d-flex justify-content-end align-items-center gap-3">
          <button
            className="btn btn-outline-primary"
            style={{ width: '100px' }}
            onClick={() => setPagination(prev => ({
              ...prev,
              page: Math.max(prev.page - 1, 1)
            }))}
            disabled={pagination.page === 1 || loading}
          >
            Previous
          </button>
          <span>Page {pagination.page} of {pagination.totalPages}</span>
          <button
            className="btn btn-outline-primary"
            style={{ width: '100px' }}
            onClick={() => setPagination(prev => ({
              ...prev,
              page: Math.min(prev.page + 1, pagination.totalPages)
            }))}
            disabled={pagination.page >= pagination.totalPages || loading}
          >
            Next
          </button>
        </div>
      </div>

      {/* Add/Edit Trainee Modal */}
      <Modal show={showModal} onHide={handleCloseModal} size="lg">
        <Modal.Header closeButton className="border-bottom sticky-top bg-white">
          <Modal.Title>{mode === 'Create' ? 'Add Trainee' : 'Edit Trainee'}</Modal.Title>
        </Modal.Header>
        <Modal.Body className="p-0">
          <div style={{ height: 'calc(100vh - 200px)', overflowY: 'auto' }}>
            <form className="p-4">
              {/* Profile Image Upload */}
              <div className="mb-4 text-center">
                <div className="position-relative d-inline-block">
                  <div
                    className="rounded-circle overflow-hidden"
                    style={{
                      width: '150px',
                      height: '150px',
                      border: '2px dashed #ccc',
                      backgroundColor: '#f8f9fa',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center'
                    }}
                  >
                    {imagePreview ? (
                      <img
                        src={imagePreview}
                        alt="Profile Preview"
                        style={{
                          width: '100%',
                          height: '100%',
                          objectFit: 'cover'
                        }}
                      />
                    ) : (
                      <div className="text-center p-4">
                        <Icon
                          icon="fluent:person-24-regular"
                          width="40"
                          height="40"
                          className="text-secondary mb-2"
                        />
                        <div className="text-secondary" style={{ fontSize: '0.875rem' }}>
                          Upload Photo
                        </div>
                      </div>
                    )}
                  </div>
                  <input
                    type="file"
                    accept="image/*"
                    className="position-absolute top-0 start-0 opacity-0"
                    style={{
                      width: '150px',
                      height: '150px',
                      cursor: 'pointer'
                    }}
                    onChange={handleImageChange}
                  />
                  {imagePreview && (
                    <button
                      type="button"
                      className="btn btn-danger position-absolute"
                      onClick={() => {
                        setImagePreview(null);
                        setFormData(prev => ({
                          ...prev,
                          profileImage: null
                        }));
                      }}
                      style={{
                        borderRadius: '50%',
                        width: '24px',
                        height: '24px',
                        padding: '0',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        top: '5px',
                        right: '5px',
                        border: '2px solid #fff',
                        boxShadow: '0 2px 4px rgba(0,0,0,0.2)'
                      }}
                    >
                      <Icon icon="fluent:dismiss-12-filled" width="12" height="12" color="#fff" />
                    </button>
                  )}
                </div>
                <div className="text-muted mt-2" style={{ fontSize: '0.875rem' }}>
                  Click to upload profile photo
                </div>
              </div>

              <div className="mb-4">
                <label className="form-label">
                  Name<span className="text-danger">*</span>
                </label>
                <input
                  type="text"
                  className={`form-control bg-light ${errors.name ? 'is-invalid' : ''}`}
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  maxLength={25}
                  required
                />
                {errors.name && <div className="invalid-feedback">{errors.name}</div>}
                <div className="text-muted mt-1" style={{ fontSize: '0.875rem' }}>
                  Maximum 25 characters
                </div>
              </div>

              <div className="mb-4">
                <label className="form-label">
                  Email<span className="text-danger">*</span>
                </label>
                <input
                  type="email"
                  className={`form-control bg-light ${errors.email ? 'is-invalid' : ''} ${mode === 'Edit' ? 'cursor-not-allowed' : ''}`}
                  name="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  placeholder="<EMAIL>"
                  required
                  readOnly={mode === 'Edit'}
                  style={mode === 'Edit' ? { backgroundColor: '#e9ecef' } : {}}
                />
                {errors.email && <div className="invalid-feedback">{errors.email}</div>}
                {mode === 'Edit' && (
                  <div className="text-muted mt-1" style={{ fontSize: '0.875rem' }}>
                    Email cannot be changed after creation
                  </div>
                )}
              </div>

              <div className="mb-4">
                <label className="form-label">
                  Gender<span className="text-danger">*</span>
                </label>
                <select
                  className={`form-select bg-light ${errors.gender ? 'is-invalid' : ''}`}
                  name="gender"
                  value={formData.gender}
                  onChange={handleInputChange}
                  required
                >
                  <option value="">Select Gender</option>
                  <option value="male">Male</option>
                  <option value="female">Female</option>
                  <option value="other">Other</option>
                </select>
                {errors.gender && <div className="invalid-feedback">{errors.gender}</div>}
              </div>

              <div className="mb-4">
                <label className="form-label">
                  Date of Birth<span className="text-danger">*</span>
                </label>
                <input
                  type="date"
                  className={`form-control bg-light ${errors.dateOfBirth ? 'is-invalid' : ''}`}
                  name="dateOfBirth"
                  value={formData.dateOfBirth}
                  onChange={handleInputChange}
                  required
                />
                {errors.dateOfBirth && <div className="invalid-feedback">{errors.dateOfBirth}</div>}
              </div>

              <div className="mb-4">
                <label className="form-label">Phone Number</label>
                <div className="row g-2">
                  <div className="col-md-4">
                    <select
                      className="form-select bg-light"
                      name="countryCode"
                      value={formData.countryCode}
                      onChange={handleInputChange}
                    >
                      {countryData.countoryCode.map((country, index) => (
                        <option key={index} value={country.code}>
                          {country.country}
                        </option>
                      ))}
                    </select>
                  </div>
                  <div className="col-md-8">
                    <input
                      type="text"
                      className={`form-control bg-light ${errors.phoneNumber ? 'is-invalid' : ''}`}
                      name="phoneNumber"
                      value={formData.phoneNumber}
                      onChange={handleInputChange}
                      placeholder="Enter phone number"
                    />
                    {errors.phoneNumber && <div className="invalid-feedback">{errors.phoneNumber}</div>}
                  </div>
                </div>
                <div className="text-muted mt-1" style={{ fontSize: '0.875rem' }}>
                  Phone number must contain only digits
                </div>
              </div>

              <div className="mb-4">
                <label className="form-label">Employee No</label>
                <input
                  type="text"
                  className={`form-control bg-light ${errors.employeeNo ? 'is-invalid' : ''}`}
                  name="employeeNo"
                  value={formData.employeeNo}
                  onChange={handleInputChange}
                  maxLength={50}
                />
                {errors.employeeNo && <div className="invalid-feedback">{errors.employeeNo}</div>}
                <div className="text-muted mt-1" style={{ fontSize: '0.875rem' }}>
                  Maximum 50 characters
                </div>
              </div>

              <div className="mb-4">
                <label className="form-label">Company Code</label>
                <input
                  type="text"
                  className={`form-control bg-light ${errors.companyCode ? 'is-invalid' : ''}`}
                  name="companyCode"
                  value={formData.companyCode}
                  onChange={handleInputChange}
                  maxLength={50}
                />
                {errors.companyCode && <div className="invalid-feedback">{errors.companyCode}</div>}
                <div className="text-muted mt-1" style={{ fontSize: '0.875rem' }}>
                  Maximum 50 characters
                </div>
              </div>

              <div className="mb-4">
                <label className="form-label">Company Description</label>
                <textarea
                  className={`form-control bg-light ${errors.companyDescription ? 'is-invalid' : ''}`}
                  name="companyDescription"
                  value={formData.companyDescription}
                  onChange={handleInputChange}
                  maxLength={255}
                  rows={3}
                />
                {errors.companyDescription && <div className="invalid-feedback">{errors.companyDescription}</div>}
                <div className="text-muted mt-1" style={{ fontSize: '0.875rem' }}>
                  Maximum 255 characters
                </div>
              </div>

              <div className="mb-4">
                <label className="form-label">Job Title</label>
                <input
                  type="text"
                  className={`form-control bg-light ${errors.jobTitle ? 'is-invalid' : ''}`}
                  name="jobTitle"
                  value={formData.jobTitle}
                  onChange={handleInputChange}
                  maxLength={1000}
                />
                {errors.jobTitle && <div className="invalid-feedback">{errors.jobTitle}</div>}
                <div className="text-muted mt-1" style={{ fontSize: '0.875rem' }}>
                  Maximum 1000 characters
                </div>
              </div>

              <div className="mb-4">
                <label className="form-label">Date Joined</label>
                <input
                  type="date"
                  className="form-control bg-light"
                  name="dateJoined"
                  value={formData.dateJoined}
                  onChange={handleInputChange}
                  placeholder="dd-mm-yyyy"
                />
                <div className="text-muted mt-1" style={{ fontSize: '0.875rem' }}>
                  Format: dd-mm-yyyy
                </div>
              </div>

              <div className="mb-4">
                <label className="form-label">IC No</label>
                <input
                  type="text"
                  className={`form-control bg-light ${errors.icNo ? 'is-invalid' : ''}`}
                  name="icNo"
                  value={formData.icNo}
                  onChange={handleInputChange}
                  maxLength={255}
                />
                {errors.icNo && <div className="invalid-feedback">{errors.icNo}</div>}
                <div className="text-muted mt-1" style={{ fontSize: '0.875rem' }}>
                  Maximum 255 characters
                </div>
              </div>

              <div className="mb-4">
                <label className="form-label">FIN No</label>
                <input
                  type="text"
                  className={`form-control bg-light ${errors.finNo ? 'is-invalid' : ''}`}
                  name="finNo"
                  value={formData.finNo}
                  onChange={handleInputChange}
                  maxLength={255}
                />
                {errors.finNo && <div className="invalid-feedback">{errors.finNo}</div>}
                <div className="text-muted mt-1" style={{ fontSize: '0.875rem' }}>
                  Maximum 255 characters
                </div>
              </div>

              <div className="mb-4">
                <label className="form-label">PWM Rank</label>
                <input
                  type="text"
                  className={`form-control bg-light ${errors.pwmRank ? 'is-invalid' : ''}`}
                  name="pwmRank"
                  value={formData.pwmRank}
                  onChange={handleInputChange}
                  maxLength={1000}
                />
                {errors.pwmRank && <div className="invalid-feedback">{errors.pwmRank}</div>}
                <div className="text-muted mt-1" style={{ fontSize: '0.875rem' }}>
                  Maximum 1000 characters
                </div>
              </div>

              <div className="mb-4">
                <label className="form-label">
                  Password{mode === 'Create' && <span className="text-danger">*</span>}
                  {mode === 'Edit' && <span className="text-muted"> (Leave blank to keep current password)</span>}
                </label>
                <div className="position-relative">
                  <input
                    type={showPassword ? "text" : "password"}
                    className={`form-control bg-light pe-5 ${errors.password ? 'is-invalid' : ''}`}
                    name="password"
                    value={formData.password}
                    onChange={handleInputChange}
                    required={mode === 'Create'}
                    minLength={8}
                    placeholder="Password"
                  />
                  <Icon
                    icon={showPassword ? "fluent:eye-off-24-regular" : "fluent:eye-24-regular"}
                    className="position-absolute top-50 end-0 translate-middle-y me-3 text-secondary"
                    style={{ cursor: 'pointer' }}
                    onClick={() => setShowPassword(!showPassword)}
                    width="20"
                    height="20"
                  />
                  {errors.password && <div className="invalid-feedback">{errors.password}</div>}
                </div>
                <div className="text-muted mt-1" style={{ fontSize: '0.875rem' }}>
                  Password must be at least 8 characters and include at least one letter (uppercase or lowercase), one number, and one special character.
                </div>
              </div>
            </form>
          </div>
          {/* Buttons */}
          <div className="p-4 border-top bg-white" style={{ position: 'sticky', bottom: 0 }}>
            <button
              type="button"
              className="btn btn-light w-100 mb-3"
              onClick={handleCloseModal}
              disabled={loading}
            >
              Cancel
            </button>
            <button
              type="button"
              className="btn btn-primary w-100"
              onClick={handleSave}
              disabled={loading}
            >
              {loading ? (
                <>
                  <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                  {mode === 'Create' ? 'Creating...' : 'Updating...'}
                </>
              ) : (
                mode === 'Create' ? 'Add Trainee' : 'Update Trainee'
              )}
            </button>
          </div>
        </Modal.Body>
      </Modal>


    </>
  );
}

export default Trainees;
