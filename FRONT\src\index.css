.truncate-1-line {
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: normal;
    max-width: 400px;
    line-height: 1.4;
    min-height: calc(1.4em * 1);
  }
  
  .truncate-2-line {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: normal;
    max-width: 400px;
    line-height: 1.4;
    min-height: calc(1.4em * 2);
  }
  
  .truncate-3-line {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: normal;
    max-width: 400px;
    line-height: 1.4;
    min-height: calc(1.4em * 3);
  }
  
  .truncate-4-line {
    display: -webkit-box;
    -webkit-line-clamp: 4;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: normal;
    max-width: 400px;
    line-height: 1.4;
    min-height: calc(1.4em * 4);
  }
  
  .truncate-5-line {
    display: -webkit-box;
    -webkit-line-clamp: 5;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: normal;
    max-width: 400px;
    line-height: 1.4;
    min-height: calc(1.4em * 5);
  }
  
  .truncate-6-line {
    display: -webkit-box;
    -webkit-line-clamp: 6;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: normal;
    max-width: 400px;
    line-height: 1.4;
    min-height: calc(1.4em * 6);
  }
  

  .responsive-btn {
    width: 100%;
  }
  
  @media (min-width: 768px) {
    .responsive-btn {
      width: 25% !important;
    }
  }

  /* Drag and Drop Styles */
  .drag-handle {
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
  }

  .drag-handle:hover {
    transform: scale(1.05);
  }

  .drag-handle:active {
    transform: scale(0.95);
  }

  /* Smooth transitions for draggable items */
  .draggable-item {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
  }

  .draggable-item.is-dragging {
    z-index: 1000;
    transform: rotate(5deg);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
  }

  /* Drop zone styling */
  .drop-zone {
    transition: background-color 0.2s ease, border-color 0.2s ease;
  }

  .drop-zone.is-dragging-over {
    background-color: #f8f9fa;
    border: 2px dashed #007bff;
    border-radius: 8px;
  }