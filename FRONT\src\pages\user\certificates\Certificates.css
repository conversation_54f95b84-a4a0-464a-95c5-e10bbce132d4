.certificates-container {
  padding: 24px;
  background-color: #f6f7f9;
  min-height: 100%;
}

/* Header styling */
.certificates-header {
  margin-bottom: 32px;
}

.certificates-header h2 {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.certificates-header p {
  font-size: 16px;
  color: #666;
}

/* Grid layout */
.certificates-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 24px;
  padding: 20px;
  /* min-height: calc(100vh - 200px); */
}

.no-data-container {
  grid-column: 1 / -1;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: calc(100vh - 300px);
}

/* Certificate card */
.certificate-card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease;
  display: flex;
  flex-direction: column;
  height: auto;
  min-height: 400px;
}

.certificate-card:nth-child(4n+1) {
  background: linear-gradient(135deg, #f6f8ff 0%, #f1f5ff 100%);
}

.certificate-card:nth-child(4n+2) {
  background: linear-gradient(135deg, #fff6f6 0%, #fff1f1 100%);
}

.certificate-card:nth-child(4n+3) {
  background: linear-gradient(135deg, #f6fff6 0%, #f1fff1 100%);
}

.certificate-card:nth-child(4n+4) {
  background: linear-gradient(135deg, #fff6fb 0%, #fff1f8 100%);
}

.certificate-card:hover {
  transform: translateY(-5px);
}

/* Badge */
.certificate-badge {
  position: absolute;
  top: 12px;
  right: 12px;
  z-index: 10;
}

.certificate-badge span {
  display: inline-block;
  padding: 6px 12px;
  background-color: rgba(49, 82, 232, 0.9);
  color: white;
  font-size: 12px;
  font-weight: 500;
  border-radius: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

/* Certificate image */
.certificate-image {
  position: relative;
  width: 100%;
  padding: 20px;
  flex: 1;
  min-height: 250px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.certificate-image img {
  width: 100%;
  height: auto;
  max-height: 250px;
  object-fit: contain;
  border-radius: 8px;
  background: white;
  padding: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.certificate-overlay {
  position: absolute;
  top: 20px;
  left: 20px;
  right: 20px;
  bottom: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  background: rgba(0, 0, 0, 0.5);
  opacity: 0;
  transition: opacity 0.2s ease;
  border-radius: 8px;
}

.certificate-card:hover .certificate-overlay {
  opacity: 1;
}

.preview-btn {
  background: white;
  border: none;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.preview-btn:hover {
  transform: scale(1.1);
}

/* Certificate details */
.certificate-details {
  padding: 20px;
  background: rgba(255, 255, 255, 0.5);
  border-top: 1px solid rgba(0, 0, 0, 0.05);
}

.certificate-details h3 {
  margin: 0;
  font-size: 18px;
  color: #333;
  margin-bottom: 10px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.certificate-meta {
  margin-bottom: 15px;
}

.certificate-date {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #666;
  font-size: 14px;
}

/* Certificate actions */
.certificate-actions {
  display: flex;
  gap: 12px;
  margin-top: 15px;
}

.download-btn, .view-details-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 10px 20px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 15px;
  font-weight: 500;
  min-width: 130px;
  height: 42px;
}

.download-btn {
  background: #4A6CF7;
  color: white;
}

.view-details-btn {
  background: #f0f0f0;
  color: #333;
  border: 1px solid #e0e0e0;
}

.download-btn:hover {
  background: #3557e0;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(74, 108, 247, 0.2);
}

.view-details-btn:hover {
  background: #e5e5e5;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Certificate Preview Modal */
.certificate-preview-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

/* Custom modal dialog for certificates */
.certificate-modal-dialog {
  max-width: 95vw !important;
  width: auto !important;
  margin: 1rem !important;
}

.certificate-preview-content {
  position: relative;
  max-width: 90%;
  max-height: 90vh;
  background: white;
  padding: 10px;
  border-radius: 8px;
}

.certificate-preview-content img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  max-height: 80vh;
}

.certificate-preview-close {
  position: absolute;
  top: -30px;
  right: -30px;
  background: white;
  border: none;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  cursor: pointer;
  font-size: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Certificate Details Modal */
.certificate-details-modal .modal-content {
  border-radius: 12px;
  overflow: hidden;
  border: none;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

.certificate-details-modal .modal-header {
  padding: 16px 24px;
  border-bottom: 1px solid #ccc;
}

.certificate-details-modal .modal-title {
  font-weight: 600;
  font-size: 20px;
}

.certificate-details-modal .modal-body {
  padding: 24px;
}

.certificate-modal-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.certificate-modal-image {
  text-align: center;
  margin-bottom: 16px;
}

.certificate-modal-image img {
  max-width: 100%;
  max-height: 300px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.certificate-modal-info {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.info-group {
  margin-bottom: 16px;
}

.info-group h4 {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #eee;
}

.info-row {
  display: flex;
  margin-bottom: 8px;
}

.info-label {
  width: 40%;
  font-weight: 500;
  color: #666;
}

.info-value {
  width: 60%;
  color: #333;
}

.certificate-details-modal .modal-footer {
  border-top: 1px solid #eee;
  padding: 16px 24px;
}

.certificate-details-modal .btn-primary {
  background-color: #3152e8;
  border-color: #3152e8;
}

.certificate-details-modal .btn-primary:hover {
  background-color: #2642d0;
  border-color: #2642d0;
}

/* Responsive adjustments */
@media (max-width: 992px) {
  .certificates-grid {
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
  }

  .certificate-card {
    min-height: 350px;
  }

  .certificate-image {
    min-height: 200px;
  }

  .certificate-image img {
    max-height: 200px;
  }

  .certificate-modal-content {
    flex-direction: column;
  }

  .certificate-modal-image {
    width: 100%;
  }

  .certificate-modal-info {
    width: 100%;
  }

  /* Mobile certificate preview adjustments */
  .certificate-modal-dialog {
    max-width: 98vw !important;
    margin: 0.5rem !important;
  }

  .certificate-preview-container {
    min-height: 60vh;
    max-height: 75vh;
    padding: 10px;
  }
}

@media (max-width: 768px) {
  .certificate-modal-dialog {
    max-width: 100vw !important;
    margin: 0 !important;
    height: 100vh !important;
  }

  .certificate-preview-modal .modal-content {
    height: 100vh !important;
    border-radius: 0 !important;
  }

  .certificate-preview-container {
    min-height: calc(100vh - 140px);
    max-height: calc(100vh - 140px);
    padding: 10px;
  }
}

/* Landscape certificate adjustments */
@media (orientation: landscape) and (max-height: 600px) {
  .certificate-preview-container {
    min-height: 50vh;
    max-height: 70vh;
  }
}

@media (max-width: 768px) {
  .certificates-container {
    padding: 16px;
  }
  
  .certificates-header {
    margin-bottom: 24px;
  }
  
  .certificates-grid {
    gap: 16px;
  }
  
  .certificate-card {
    min-height: 300px;
  }
  
  .certificate-image {
    min-height: 180px;
  }
  
  .certificate-image img {
    max-height: 180px;
  }
  
  .certificate-details {
    padding: 16px;
  }
  
  .certificate-details h3 {
    font-size: 16px;
  }
  
  .certificate-actions button {
    padding: 8px 12px;
    font-size: 13px;
  }
}

.form-control{
  background-color: #F9F9F9;
}

.form-control{
  background-color: #F9F9F9 !important;
}

/* Update the certificate card image as well */
.certificate-card .certificate-image img {
  width: 100%;
  height: 200px;
  object-fit: contain;
  background: #f8f9fa;
}

.certificate-preview-modal .modal-content {
  background: #f8f9fa;
  border: none;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.certificate-preview-modal .modal-body {
  padding: 0;
}

.certificate-preview-modal .modal-header {
  background: white;
  border-bottom: 1px solid #dee2e6;
  padding: 1rem 1.5rem;
}

.certificate-preview-modal .modal-footer {
  background: white;
  border-top: 1px solid #dee2e6;
  padding: 1rem 1.5rem;
}

.certificate-preview-modal .modal-title {
  font-weight: 600;
  color: #2c3e50;
  font-size: 1.25rem;
}

.certificate-preview-container {
  width: 100%;
  min-height: 70vh;
  max-height: 85vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background: white;
  padding: 20px;
  overflow: auto;
}

.preview-image {
  max-width: 100%;
  max-height: 100%;
  width: auto;
  height: auto;
  object-fit: contain;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.certificate-preview-modal .modal-dialog {
  max-width: 90vw;
  max-height: 90vh;
  margin: 1.75rem auto;
}

.certificate-preview-modal .modal-content {
  height: auto;
  max-height: 90vh;
}

.certificate-preview-container {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  background-color: #f8f9fa;
}

.certificate-preview-container .preview-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
  max-height: calc(90vh - 120px); /* Subtracting header and footer height */
}