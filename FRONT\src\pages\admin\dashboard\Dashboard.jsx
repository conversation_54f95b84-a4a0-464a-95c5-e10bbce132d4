import React from "react";
import { Icon } from "@iconify/react";
import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import TraineeGraph from "./TraineeGraph";
import CourseGraph from "./CourseGraph";
import RecentPushedAnnouncement from "./RecentPushedAnnouncement";
import Ticket from "./Ticket";
import NoData from "../../../components/common/NoData";
import Loader from "../../../components/admin/Loader";
import { getDashboardData } from "../../../services/adminService";
import { usePermissions } from "../../../context/PermissionsContext";

function Dashboard() {
  const navigate = useNavigate();
  const { permissions } = usePermissions();
  const [loading, setLoading] = useState(true);
  const [allDashboardData, setAllDashboardData] = useState(null);
  const [error, setError] = useState(null);

  const fetchDashboardData = async () => {
    console.log('Starting fetchDashboardData...');
    try {
      setLoading(true);
      setError(null);
      console.log('Making API call to getDashboardData...');
      const response = await getDashboardData();
      console.log('API Response:', {
        success: response?.success,
        status: response?.status,
        message: response?.message,
        dataExists: !!response?.data,
        dataKeys: response?.data ? Object.keys(response.data) : [],
      });

      if (response.success) {
        console.log('Dashboard data received successfully:', {
          totalUsers: response.data?.totalUsers,
          activeUsers: response.data?.activeUsers,
          totalCourses: response.data?.totalCourses,
          totalClassrooms: response.data?.totalClassrooms
        });
        setAllDashboardData(response.data);
      } else {
        console.error('API call failed with response:', {
          error: response?.error,
          message: response?.message,
          details: response?.details || 'No additional details'
        });
        setError("Failed to fetch dashboard data");
      }
    } catch (error) {
      console.error('Error in fetchDashboardData:', {
        name: error?.name,
        message: error?.message,
        stack: error?.stack,
        response: error?.response?.data,
        status: error?.response?.status,
        statusText: error?.response?.statusText
      });
      
      // Additional network error debugging
      if (error?.response) {
        console.error('Server Response Error:', {
          data: error.response.data,
          status: error.response.status,
          headers: error.response.headers
        });
      } else if (error?.request) {
        console.error('No Response Received:', {
          request: error.request,
          message: 'The request was made but no response was received'
        });
      } else {
        console.error('Request Setup Error:', {
          message: 'Error setting up the request',
          error: error.message
        });
      }
      
      setError("An error occurred while fetching data");
    } finally {
      console.log('fetchDashboardData completed. Loading state:', loading);
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchDashboardData();
  }, []);

  // Generate dashboard cards data from API response
  const getDashboardCards = () => {
    if (!allDashboardData) return [];

    return [
      {
        id: 1,
        title: "Total Users",
        value: allDashboardData.totalUsers || 0,
        icon: "ph:users-bold",
        color: "info",
        redirectTo: "/admin/trainees"
      },
      {
        id: 2,
        title: "Active Users",
        value: allDashboardData.activeUsers || 0,
        icon: "ph:user-circle-bold",
        color: "success",
        redirectTo: "/admin/trainees"
      },
      {
        id: 3,
        title: "Inactive Users",
        value: allDashboardData.inactiveUsers || 0,
        icon: "ph:user-x-bold",
        color: "danger",
        redirectTo: "/admin/trainees"
      },
      {
        id: 4,
        title: "Total Courses",
        value: allDashboardData.totalCourses || 0,
        icon: "lucide:book-copy",
        color: "info",
        redirectTo: "/admin/courses"
      },
      {
        id: 5,
        title: "Active Courses",
        value: allDashboardData.activeCourses || 0,
        icon: "lucide:book-open",
        color: "success",
        redirectTo: "/admin/courses"
      },
      {
        id: 6,
        title: "Approved Courses",
        value: allDashboardData.approvedCourses || 0,
        icon: "lucide:badge-check",
        color: "success",
        redirectTo: "/admin/courses"
      },
      {
        id: 7,
        title: "Free Courses",
        value: allDashboardData.freeCourses || 0,
        icon: "ph:gift-bold",
        color: "warning",
        redirectTo: "/admin/courses"
      },
      {
        id: 8,
        title: "Paid Courses",
        value: allDashboardData.paidCourses || 0,
        icon: "ph:credit-card-bold",
        color: "info",
        redirectTo: "/admin/courses"
      },
      {
        id: 9,
        title: "Total Classrooms",
        value: allDashboardData.totalClassrooms || 0,
        icon: "lucide:building",
        color: "info",
        redirectTo: "/admin/classrooms"
      },
      {
        id: 10,
        title: "Active Classrooms",
        value: allDashboardData.activeClassrooms || 0,
        icon: "lucide:layout-grid",
        color: "success",
        redirectTo: "/admin/classrooms"
      },
      {
        id: 11,
        title: "Inactive Classrooms",
        value: allDashboardData.inactiveClassrooms || 0,
        icon: "lucide:lock",
        color: "danger",
        redirectTo: "/admin/classrooms"
      },
      {
        id: 12,
        title: "Total Trainees",
        value: allDashboardData.totalTrainees || 0,
        icon: "lucide:users",
        color: "info",
        redirectTo: "/admin/roles-and-access"
      },
      {
        id: 13,
        title: "Active Trainees",
        value: allDashboardData.activeTrainees || 0,
        icon: "lucide:user-check",
        color: "success",
        redirectTo: "/admin/roles-and-access"
      },
      {
        id: 14,
        title: "Inactive Trainees",
        value: allDashboardData.inactiveTrainees || 0,
        icon: "lucide:user-x",
        color: "warning",
        redirectTo: "/admin/roles-and-access"
      },
      {
        id: 15,
        title: "Pending Tickets",
        value: allDashboardData.pendingTickets || 0,
        icon: "lucide:ticket",
        color: "danger",
        redirectTo: "/admin/settings"
      },
      {
        id: 16,
        title: "Resolved Tickets",
        value: allDashboardData.resolvedTickets || 0,
        icon: "lucide:ticket-check",
        color: "success",
        redirectTo: "/admin/settings"
      }
    ];
  };

  const handleCardClick = (card) => {
    if (card.redirectTo) {
      if (card.redirectTo === '/admin/settings') {
        navigate(`${card.redirectTo}?activeTab=query`);
      } else {
        navigate(card.redirectTo);
      }
    }
  };

  // Show loading state
  if (loading) {
    return (
      <div className="d-flex justify-content-center align-items-center" style={{ minHeight: '400px' }}>
        <Loader />
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="d-flex justify-content-center align-items-center" style={{ minHeight: '400px' }}>
        <div className="text-center">
          <Icon icon="lucide:alert-circle" width="48" height="48" className="text-danger mb-3" />
          <h5 className="text-danger">{error}</h5>
          <button
            className="btn btn-primary mt-3"
            onClick={fetchDashboardData}
          >
            <Icon icon="lucide:refresh-cw" width="16" height="16" className="me-2" />
            Retry
          </button>
        </div>
      </div>
    );
  }

  const dashboardCards = getDashboardCards();

  return (
    <>
      {/* Dashboard Cards */}
      {permissions.dashboard_management_cards_view && (
        <div className="row g-4">
          {dashboardCards.map((item) => (
            <div key={item.id} className="col-12 col-sm-6 col-md-4 col-lg-3">
              <div 
                className={`card h-100 ${item.redirectTo ? 'cursor-pointer' : ''}`} 
                onClick={() => item.redirectTo && handleCardClick(item)}
                style={item.redirectTo ? { transition: 'transform 0.2s', cursor: 'pointer' } : {}}
                onMouseOver={(e) => item.redirectTo && (e.currentTarget.style.transform = 'translateY(-5px)')}
                onMouseOut={(e) => item.redirectTo && (e.currentTarget.style.transform = 'translateY(0)')}
              >
                <div className="card-body d-flex align-items-center">
                  <div
                    className={`bg-${item.color} bg-opacity-10 rounded-circle p-3 me-3`}
                  >
                    <Icon
                      icon={item.icon}
                      width="24"
                      height="24"
                      className={`text-${item.color}`}
                    />
                  </div>
                  <div>
                    <h6 className="card-subtitle mb-1 text-muted">
                      {item.title}
                    </h6>
                    <h2 className="card-title mb-0">{item.value}</h2>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Charts Section */}
      {permissions.dashboard_management_graphs_view && (
        <div className="row mt-4">
          <div className="col-md-6">
            <TraineeGraph
              data={allDashboardData?.monthlyTraineeJoins || []}
              loading={loading}
            />
          </div>
          <div className="col-md-6">
            <CourseGraph
              data={allDashboardData?.monthlyCourseAdds || []}
              loading={loading}
            />
          </div>
        </div>
      )}

      {/* Tickets and Announcements Section */}
      {permissions.dashboard_management_view_latest_updates_view && (
        <div className="row mt-4">
          <div className="col-md-6">
            <Ticket
              tickets={allDashboardData?.latestSupportTickets || []}
              loading={loading}
            />
          </div>
          <div className="col-md-6">
            <RecentPushedAnnouncement
              announcements={allDashboardData?.latestAnnouncements || []}
              loading={loading}
            />
          </div>
        </div>
      )}

      {/* Show message if no permissions */}
      {!permissions.dashboard_management_cards_view && 
       !permissions.dashboard_management_graphs_view && 
       !permissions.dashboard_management_view_latest_updates_view && (
        <div className="d-flex justify-content-center align-items-center" style={{ minHeight: '400px' }}>
          <div className="text-center">
            <Icon icon="lucide:shield-x" width="48" height="48" className="text-warning mb-3" />
            <h5 className="text-warning">Access Restricted</h5>
            <p className="text-muted">You don't have permission to view any dashboard content.</p>
          </div>
        </div>
      )}
    </>
  );
}

export default Dashboard;
