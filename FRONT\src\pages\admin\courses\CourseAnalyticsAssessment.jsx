import React, { useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';

import { Icon } from '@iconify/react';
import { decodeData, encodeData } from '../../../utils/encodeAndEncode';
import { getCourseAssessments } from '../../../services/adminService';

function CourseAnalyticsAssessment() {
  const navigate = useNavigate();
  const { courseId } = useParams();
  const decodedCourseId = decodeData(courseId);

  const [assessmentData, setAssessmentData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [search, setSearch] = useState('');
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    totalPages: 0,
    totalCount: 0
  });

  // Fetch assessment data
  useEffect(() => {
    fetchAssessmentData();
  }, [decodedCourseId, search, pagination.page]);

  const fetchAssessmentData = async () => {
    try {
      setLoading(true);
      const response = await getCourseAssessments({
        course_id: decodedCourseId,
        search: search,
        page: pagination.page,
        limit: pagination.limit
      });

      console.log('Assessment Data Response:', response);

      if (response.success && response.data) {
        setAssessmentData(response.data.assessments || []);
        setPagination(prev => ({
          ...prev,
          page: response.data.pagination?.current_page || 1,
          totalPages: response.data.pagination?.total_pages || 0,
          totalCount: response.data.pagination?.total_records || 0
        }));
      }
    } catch (error) {
      console.error('Error fetching assessment data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSearchChange = (e) => {
    setSearch(e.target.value);
    setPagination(prev => ({ ...prev, page: 1 })); // Reset to first page
  };

  const handleNavigateToDetails = (assessmentId) => {
    const encodedAssessmentId = encodeData(assessmentId);
    const encodedCourseId = encodeData(decodedCourseId);
    navigate(`/admin/courses/analytics/${encodedCourseId}/CourseAnalyticsAssessmentDetails/${encodedAssessmentId}`);
  };
  

  const handlePageChange = (newPage) => {
    setPagination(prev => ({ ...prev, page: newPage }));
  };

  return (
    <>
      {/* Search and Filter */}
      <div className="row d-flex mb-3">
        <div className="col-md-6 mt-2 d-flex justify-content-start align-items-center">
          <input
            type="search"
            className="form-control"
            placeholder="Search assessments..."
            style={{ maxWidth: '300px' }}
            value={search}
            onChange={handleSearchChange}
          />
        </div>
      </div>

      {/* Table */}
      <div className="row mb-3">
        <div className="col-12">
          <div className="card">
            <div className="table-responsive">
              <table className="table table-borderless mb-0">
                <thead>
                  <tr>
                    <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500' }}>SL.No</th>
                    <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500' }}>Assessment Name</th>
                    <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500' }}>Questions</th>
                    <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500' }}>Attempts</th>
                    <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500' }}>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {loading ? (
                    <tr>
                      <td colSpan="5" className="text-center py-4">
                        <div className="spinner-border text-primary" role="status">
                          <span className="visually-hidden">Loading...</span>
                        </div>
                      </td>
                    </tr>
                  ) : assessmentData.length === 0 ? (
                    <tr>
                      <td colSpan="5" className="text-center py-4 text-muted">
                        No assessments found
                      </td>
                    </tr>
                  ) : (
                    assessmentData.map((assessment, index) => (
                      <tr
                        key={assessment.assessment_id || index}
                        className="border-bottom"
                        onClick={() => handleNavigateToDetails(assessment.assessment_id)}
                        style={{ cursor: 'pointer' }}
                      >
                        <td className="py-3 align-middle">
                          {(pagination.page - 1) * pagination.limit + index + 1}
                        </td>
                        <td className="py-3 align-middle">
                          <span className="fw-medium">{assessment.assessment_name || 'N/A'}</span>
                        </td>
                        <td className="py-3 align-middle">
                          {assessment.total_questions || 0}
                        </td>
                        <td className="py-3 align-middle">
                          {assessment.total_attempts || 0}
                        </td>
                        <td className="py-3 align-middle">
                          <div className="d-flex gap-2">
                            <button
                              className="btn btn-outline-primary btn-sm border"
                              title="View Details"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleNavigateToDetails(assessment.assessment_id);
                              }}
                            >
                              <Icon icon="fluent:eye-24-regular" width="16" height="16" />
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))
                  )}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>

      {/* Pagination */}
      {!loading && assessmentData.length > 0 && (
        <div className="row">
          <div className="col-md-6">
            <select
              className="form-select"
              style={{ width: 'auto' }}
              value={pagination.limit}
              onChange={(e) => setPagination(prev => ({ ...prev, limit: parseInt(e.target.value), page: 1 }))}
            >
              <option value={10}>10 records per page</option>
              <option value={25}>25 records per page</option>
              <option value={50}>50 records per page</option>
            </select>
          </div>
          <div className="col-md-6 d-flex justify-content-end align-items-center gap-3">
            <button
              className="btn btn-primary"
              style={{ width: '150px' }}
              disabled={pagination.page <= 1}
              onClick={() => handlePageChange(pagination.page - 1)}
            >
              Prev
            </button>
            <span>Page {pagination.page} of {pagination.totalPages}</span>
            <button
              className="btn btn-primary"
              style={{ width: '150px' }}
              disabled={pagination.page >= pagination.totalPages}
              onClick={() => handlePageChange(pagination.page + 1)}
            >
              Next
            </button>
          </div>
        </div>
      )}
    </>
  );
}

export default CourseAnalyticsAssessment;
