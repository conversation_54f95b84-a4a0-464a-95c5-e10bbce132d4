const { mysqlServerConnection } = require('../../../db/db');

const getFAQs = async (req, res) => {
  console.log("📡 [API] getFAQs called");

  try {
    const { page = 1, limit = 10, search = "" } = req.body;
    const dbName = req.user.db_name;
    const offset = (page - 1) * limit;

    console.log("🏢 Database:", dbName);
    console.log("📄 Page:", page, "Limit:", limit, "Search:", search);

    // Build search condition
    let searchCondition = "";
    let queryParams = [];

    if (search && search.trim() !== "") {
      searchCondition = "AND (question LIKE ? OR answer LIKE ?)";
      queryParams.push(`%${search}%`, `%${search}%`);
    }

    // Get total count for pagination
    const countQuery = `
      SELECT COUNT(*) as total
      FROM ${dbName}.faqs
      WHERE is_deleted = 0 ${searchCondition}
    `;
    const [countResult] = await mysqlServerConnection.query(countQuery, queryParams);
    const totalRecords = countResult[0].total;

    // Get paginated data
    const dataQuery = `
      SELECT id, user_id, question, answer, is_active, createdAt, updatedAt
      FROM ${dbName}.faqs
      WHERE is_deleted = 0 ${searchCondition}
      ORDER BY createdAt DESC
      LIMIT ? OFFSET ?
    `;
    const dataParams = [...queryParams, limit, offset];
    const [result] = await mysqlServerConnection.query(dataQuery, dataParams);

    console.log("✅ Found", result.length, "FAQ records");

    return res.status(200).json({
      success: true,
      status: 200,
      data: {
        records: result,
        pagination: {
          currentPage: page,
          totalPages: Math.ceil(totalRecords / limit),
          totalRecords: totalRecords,
          recordsPerPage: limit
        }
      },
      message: result.length > 0 ? "FAQs fetched successfully" : "No FAQs found"
    });

  } catch (err) {
    console.error("❌ Error in getFAQs:", err);
    return res.status(500).json({
      success: false,
      status: 500,
      message: "Server error while fetching FAQs",
      error: err.message
    });
  }
};

const addFAQ = async (req, res) => {
  console.log("📡 [API] addFAQ called");

  try {
    const { question, answer } = req.body;
    const dbName = req.user.db_name;
    const user_id = req.user.id;

    console.log("📝 Adding FAQ:", { question, answer, user_id });

    // Validate required fields
    if (!question || !answer) {
      return res.status(400).json({
        success: false,
        status: 400,
        message: "Question and answer are required fields"
      });
    }

    // Insert new FAQ
    const [result] = await mysqlServerConnection.query(
      `INSERT INTO ${dbName}.faqs (user_id, question, answer, is_active, is_deleted, createdAt, updatedAt)
       VALUES (?, ?, ?, 1, 0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)`,
      [user_id, question, answer]
    );

    console.log("✅ FAQ added successfully with ID:", result.insertId);

    return res.status(201).json({
      success: true,
      status: 201,
      data: {
        id: result.insertId,
        question,
        answer,
        is_active: true
      },
      message: "FAQ added successfully"
    });

  } catch (err) {
    console.error("❌ Error in addFAQ:", err);
    return res.status(500).json({
      success: false,
      status: 500,
      message: "Server error while adding FAQ",
      error: err.message
    });
  }
};

const updateFAQ = async (req, res) => {
  console.log("📡 [API] updateFAQ called");

  try {
    const { id } = req.params;
    const { question, answer } = req.body;
    const dbName = req.user.db_name;

    console.log("✏️ Updating FAQ:", { id, question, answer });

    // Validate required fields
    if (!id || !question || !answer) {
      return res.status(400).json({
        success: false,
        status: 400,
        message: "ID, question and answer are required fields"
      });
    }

    // Check if record exists and is not deleted
    const [existingRecord] = await mysqlServerConnection.query(
      `SELECT id, question FROM ${dbName}.faqs WHERE id = ? AND is_deleted = 0`,
      [id]
    );

    if (existingRecord.length === 0) {
      return res.status(404).json({
        success: false,
        status: 404,
        message: "FAQ not found or has been deleted"
      });
    }

    // Update the record
    const [result] = await mysqlServerConnection.query(
      `UPDATE ${dbName}.faqs
       SET question = ?, answer = ?, updatedAt = CURRENT_TIMESTAMP
       WHERE id = ? AND is_deleted = 0`,
      [question, answer, id]
    );

    if (result.affectedRows === 0) {
      return res.status(404).json({
        success: false,
        status: 404,
        message: "FAQ not found or could not be updated"
      });
    }

    console.log("✅ FAQ updated successfully");

    return res.status(200).json({
      success: true,
      status: 200,
      data: {
        id,
        question,
        answer
      },
      message: "FAQ updated successfully"
    });

  } catch (err) {
    console.error("❌ Error in updateFAQ:", err);
    return res.status(500).json({
      success: false,
      status: 500,
      message: "Server error while updating FAQ",
      error: err.message
    });
  }
};

const statusFAQ = async (req, res) => {
  console.log("📡 [API] statusFAQ called");

  try {
    const { id } = req.params;
    const { status } = req.body;
    const dbName = req.user.db_name;

    console.log("🔄 Updating FAQ status:", { id, status });

    // Validate required fields
    if (!id || status === undefined) {
      return res.status(400).json({
        success: false,
        status: 400,
        message: "ID and status are required fields"
      });
    }

    // Validate status value (should be boolean or 0/1)
    const isActiveStatus = status === true || status === 1 || status === "1";

    // Check if record exists and is not deleted
    const [existingRecord] = await mysqlServerConnection.query(
      `SELECT id, question, is_active FROM ${dbName}.faqs WHERE id = ? AND is_deleted = 0`,
      [id]
    );

    if (existingRecord.length === 0) {
      return res.status(404).json({
        success: false,
        status: 404,
        message: "FAQ not found or has been deleted"
      });
    }

    // Update the status
    const [result] = await mysqlServerConnection.query(
      `UPDATE ${dbName}.faqs
       SET is_active = ?, updatedAt = CURRENT_TIMESTAMP
       WHERE id = ? AND is_deleted = 0`,
      [isActiveStatus ? 1 : 0, id]
    );

    if (result.affectedRows === 0) {
      return res.status(404).json({
        success: false,
        status: 404,
        message: "FAQ not found or could not be updated"
      });
    }

    const statusMessage = isActiveStatus ? "activated" : "deactivated";
    console.log(`✅ FAQ ${statusMessage} successfully`);

    return res.status(200).json({
      success: true,
      status: 200,
      data: {
        id,
        is_active: isActiveStatus
      },
      message: `FAQ has been ${statusMessage} successfully`
    });

  } catch (err) {
    console.error("❌ Error in statusFAQ:", err);
    return res.status(500).json({
      success: false,
      status: 500,
      message: "Server error while updating FAQ status",
      error: err.message
    });
  }
};

const deleteFAQ = async (req, res) => {
  console.log("📡 [API] deleteFAQ called");

  try {
    const { id } = req.params || req.body;
    const dbName = req.user.db_name;

    console.log("🗑️ Deleting FAQ with ID:", id);

    // Validate required fields
    if (!id) {
      return res.status(400).json({
        success: false,
        status: 400,
        message: "ID is required"
      });
    }

    // Check if record exists and is not already deleted
    const [existingRecord] = await mysqlServerConnection.query(
      `SELECT id, question FROM ${dbName}.faqs WHERE id = ? AND is_deleted = 0`,
      [id]
    );

    if (existingRecord.length === 0) {
      return res.status(404).json({
        success: false,
        status: 404,
        message: "FAQ not found or has already been deleted"
      });
    }

    // Soft delete the record
    const [result] = await mysqlServerConnection.query(
      `UPDATE ${dbName}.faqs
       SET is_deleted = 1, updatedAt = CURRENT_TIMESTAMP
       WHERE id = ? AND is_deleted = 0`,
      [id]
    );

    if (result.affectedRows === 0) {
      return res.status(404).json({
        success: false,
        status: 404,
        message: "FAQ not found or could not be deleted"
      });
    }

    console.log("✅ FAQ deleted successfully");

    return res.status(200).json({
      success: true,
      status: 200,
      message: "FAQ deleted successfully"
    });

  } catch (err) {
    console.error("❌ Error in deleteFAQ:", err);
    return res.status(500).json({
      success: false,
      status: 500,
      message: "Server error while deleting FAQ",
      error: err.message
    });
  }
};

module.exports = {
  getFAQs,
  addFAQ,
  updateFAQ,
  statusFAQ,
  deleteFAQ,
};