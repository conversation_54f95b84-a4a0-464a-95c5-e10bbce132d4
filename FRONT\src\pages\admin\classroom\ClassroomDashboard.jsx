import React, { useRef, useEffect, useState } from 'react'
import './Classroom.css';
import { Icon } from '@iconify/react';
import { useParams, useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import { classroomDashboard, classroomVisibility } from '../../../services/adminService';
import ClassroomLiveClasses from './ClassroomLiveClasses';
import ClassroomAssessments from './ClassroomAssessments';
import ClassroomAssignments from './ClassroomAssignments';
import ClassroomCommunity from '../../user/classroom/Cummunity';
import ClassroomRecorded from './ClassroomRecorded';

import { usePermissions } from '../../../context/PermissionsContext';
import { encodeData, decodeData } from '../../../utils/encodeAndEncode';

function ClassroomDashboard() {
  const navigate = useNavigate();
  const { permissions } = usePermissions();
  const { classroomId } = useParams();
  const decodedClassroomId = decodeData(classroomId);
  console.log('decodedClassroomId', decodedClassroomId);

  const [activeTab, setActiveTab] = useState('assignments');
  const [classroomData, setClassroomData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [dashboardData, setDashboardData] = useState(null);
  const [trainers, setTrainers] = useState([]);
  const [groupData, setGroupData] = useState(null);
  const [classroomName, setClassroomName] = useState('');
  const [isTrainersExpanded, setIsTrainersExpanded] = useState(false);
  const [isTabSettingsOpen, setIsTabSettingsOpen] = useState(false);
  const [tabVisibility, setTabVisibility] = useState({
    assignments: true,
    assessments: true,
    live: true,
    recorded: true,
    community: true
  });
  const dropdownRef = useRef(null);
  const buttonRef = useRef(null);

  // Handle clicks outside the dropdown
  useEffect(() => {
    const handleClickOutside = (event) => {
      // Don't close if clicking inside dropdown or on the button
      if (dropdownRef.current?.contains(event.target) || buttonRef.current?.contains(event.target)) {
        return;
      }
      // Close if clicking outside
      if (isTabSettingsOpen) {
        setIsTabSettingsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [isTabSettingsOpen]);

  // Mock function to simulate API call for tab visibility update
  const updateTabVisibility = async (e, tabName, isVisible) => {
    e.stopPropagation();

    try {
      // Create new state
      const newState = {
        ...tabVisibility,
        [tabName]: isVisible
      };

      // Generate payload for API
      const is_visibility = [
        {
          tab_name: "assignment",
          is_visible: newState.assignments.toString()
        },
        {
          tab_name: "assessment",
          is_visible: newState.assessments.toString()
        },
        {
          tab_name: "liveclass",
          is_visible: newState.live.toString()
        },
        {
          tab_name: "recorded",
          is_visible: newState.recorded.toString()
        },
        {
          tab_name: "community",
          is_visible: newState.community.toString()
        }
      ];

      // Prepare API payload
      const apiPayload = {
        classroom_id: parseInt(decodedClassroomId),
        is_visibility: is_visibility
      };

      // Update local state first (optimistic update)
      setTabVisibility(newState);

      // Call the API
      const response = await classroomVisibility(apiPayload);

      if (response.success) {
        toast.success('Tab visibility updated successfully');
      } else {
        // Revert state if API call fails
        setTabVisibility(tabVisibility);
        toast.error('Failed to update tab visibility');
      }
    } catch (error) {
      // Revert state if API call fails
      setTabVisibility(tabVisibility);
      console.error('Error updating tab visibility:', error);
      toast.error('Failed to update tab visibility');
    }
  };

  // Tab settings component
  const TabSettings = () => (
    <div
      ref={dropdownRef}
      className="tab-settings-dropdown position-absolute bg-white shadow-sm rounded p-3"
      style={{ right: '0', top: '100%', zIndex: 1000, minWidth: '200px' }}
      onClick={(e) => e.stopPropagation()}
    >
      <h6 className="mb-3">Tab Visibility</h6>
      <div className="d-flex flex-column gap-2">
        <label className="d-flex align-items-center gap-2" onClick={(e) => e.stopPropagation()}>
          <input
            type="checkbox"
            checked={tabVisibility.assignments}
            onChange={(e) => updateTabVisibility(e, 'assignments', !tabVisibility.assignments)}
          />
          <span style={{ color: tabVisibility.assignments ? 'inherit' : '#666' }}>
            Assignments
          </span>
        </label>
        <label className="d-flex align-items-center gap-2" onClick={(e) => e.stopPropagation()}>
          <input
            type="checkbox"
            checked={tabVisibility.assessments}
            onChange={(e) => updateTabVisibility(e, 'assessments', !tabVisibility.assessments)}
          />
          <span style={{ color: tabVisibility.assessments ? 'inherit' : '#666' }}>
            Assessments
          </span>
        </label>
        <label className="d-flex align-items-center gap-2" onClick={(e) => e.stopPropagation()}>
          <input
            type="checkbox"
            checked={tabVisibility.live}
            onChange={(e) => updateTabVisibility(e, 'live', !tabVisibility.live)}
          />
          <span style={{ color: tabVisibility.live ? 'inherit' : '#666' }}>
            Live Classes
          </span>
        </label>
        <label className="d-flex align-items-center gap-2" onClick={(e) => e.stopPropagation()}>
          <input
            type="checkbox"
            checked={tabVisibility.recorded}
            onChange={(e) => updateTabVisibility(e, 'recorded', !tabVisibility.recorded)}
          />
          <span style={{ color: tabVisibility.recorded ? 'inherit' : '#666' }}>
            Resources
          </span>
        </label>
        <label className="d-flex align-items-center gap-2" onClick={(e) => e.stopPropagation()}>
          <input
            type="checkbox"
            checked={tabVisibility.community}
            onChange={(e) => updateTabVisibility(e, 'community', !tabVisibility.community)}
          />
          <span style={{ color: tabVisibility.community ? 'inherit' : '#666' }}>
            Community
          </span>
        </label>
      </div>
    </div>
  );

  const fetchDashboardData = async () => {
    try {
      const response = await classroomDashboard({ classroom_id: decodedClassroomId });

      console.log('Dashboard data---------:', response);
      console.log('Classroom name:', response?.cls_name);

      if (response) {
        setDashboardData(response.data);
        setTrainers(response.data || []);
        setGroupData(response.group);
        if (response.cls_name) {
          setClassroomName(response.cls_name);
        }

        // Update tab visibility from API response
        if (response.is_visibility && Array.isArray(response.is_visibility)) {
          const newTabVisibility = {
            assignments: response.is_visibility.find(tab => tab.tab_name === 'assignment')?.is_visible === 'true',
            assessments: response.is_visibility.find(tab => tab.tab_name === 'assessment')?.is_visible === 'true',
            live: response.is_visibility.find(tab => tab.tab_name === 'liveclass')?.is_visible === 'true',
            recorded: response.is_visibility.find(tab => tab.tab_name === 'recorded')?.is_visible === 'true',
            community: response.is_visibility.find(tab => tab.tab_name === 'community')?.is_visible === 'true'
          };
          setTabVisibility(newTabVisibility);
        }
      }
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      toast.error('Failed to fetch dashboard data');
    }
  };

  useEffect(() => {
    fetchDashboardData();
    setLoading(false);
  }, []);

  useEffect(() => {
    console.log('Classroom name state updated:', classroomName);
  }, [classroomName]);

  const handleTabChange = (tab) => {
    // Check permissions before changing tabs
    switch(tab) {
      case 'assignments':
        if (!permissions.classroom_assignment_view) {
          toast.warning("You don't have permission to view assignments");
          return;
        }
        break;
      case 'assessments':
        if (!permissions.classroom_assessment_view) {
          toast.warning("You don't have permission to view assessments");
          return;
        }
        break;
      case 'live':
        if (!permissions.classroom_live_classes_view) {
          toast.warning("You don't have permission to view live classes");
          return;
        }
        break;
      case 'recorded':
        if (!permissions.classroom_resources_view) {
          toast.warning("You don't have permission to view resources");
          return;
        }
        break;
      case 'community':
        if (!permissions.classroom_community_view) {
          toast.warning("You don't have permission to view community");
          return;
        }
        break;
    }
    setActiveTab(tab);
  };

  const handleAddTrainee = () => {
    if (!permissions.classroom_trainees_view) {
      toast.warning("You don't have permission to view or add trainees");
      return;
    }
    let encodedClassroomId = encodeData(decodedClassroomId);
    navigate(`/admin/classrooms/classroom-dashboard/${encodedClassroomId}/trainees/${encodedClassroomId}`);
  };

  const toggleTrainers = () => {
    setIsTrainersExpanded(!isTrainersExpanded);
  };

  if (loading) {
    return <div className="text-center p-4">Loading...</div>;
  }

  return (
    <>
      <div className="row mb-4">
        <div className="col-md-6">
          <div className="card-header bg-white border-bottom-0 d-flex justify-content-between align-items-center">
            <div>
              <div className="text-muted mb-0">
                {classroomName ? (
                  <h4 className="text-muted mb-0">{classroomName}</h4>
                ) : (
                  <small>(No classroom name available)</small>
                )}
              </div>
            </div>
          </div>
        </div>
        <div className="col-md-6 d-flex justify-content-end align-items-center">
          <button
            className="btn btn-primary d-flex align-items-center gap-2"
            style={{ width: '200px' }}
            onClick={handleAddTrainee}
            disabled={!permissions.classroom_trainees_view}
            title={!permissions.classroom_trainees_view ? "You don't have permission to view or add trainees" : "Add Trainees"}
          >
            <Icon icon="fluent:people-add-24-regular" width="20" height="20" />
            Add Trainees
          </button>
        </div>
      </div>

      {/* Trainers Section as Dropdown */}
      <div className="row mb-4">
        <div className="col-12">
          <div className="card trainers-dropdown">
            <div
              className="card-header bg-white d-flex justify-content-between align-items-center cursor-pointer"
              onClick={toggleTrainers}
              style={{ cursor: 'pointer' }}
            >
              <h5 className="mb-0 d-flex align-items-center">
                <Icon icon="fluent:people-team-24-regular" className="me-2" />
                Classroom Trainers
                <span className="ms-2 text-muted">({trainers.length})</span>
              </h5>
              <Icon
                icon={isTrainersExpanded ? "fluent:chevron-up-24-regular" : "fluent:chevron-down-24-regular"}
                width="24"
                height="24"
                className="text-muted"
              />
            </div>
            <div className={`collapse ${isTrainersExpanded ? 'show' : ''}`}>
              <div className="card-body">
                <div className="row">
                  {trainers.length === 0 && (
                    <div className="col-12">
                      <div className="text-center">
                        <Icon icon="fluent:people-team-24-regular" className="me-2" />
                        No trainers added yet
                      </div>
                    </div>
                  )}
                  {/* when there is no trainer show no trainee added yet */}
                  {trainers.map((trainer, index) => (
                    <div key={trainer.user_id} className="col-md-6 col-lg-4 mb-3">
                      <div className="trainer-card p-3 border rounded h-100">
                        <div className="d-flex align-items-center mb-3">
                          <div className="trainer-avatar me-3">
                            {trainer.profile_pic_url ? (
                              <img
                                src={trainer.profile_pic_url}
                                alt={trainer.name}
                                className="rounded-circle"
                                style={{ width: '60px', height: '60px', objectFit: 'cover' }}
                              />
                            ) : (
                              <div
                                className="rounded-circle bg-light d-flex align-items-center justify-content-center"
                                style={{ width: '60px', height: '60px' }}
                              >
                                <Icon icon="fluent:person-24-regular" width="30" height="30" />
                              </div>
                            )}
                          </div>
                          <div>
                            <h6 className="mb-1">{trainer.name}</h6>
                            <span className="badge bg-primary-subtle text-primary">{trainer.role}</span>
                          </div>
                        </div>
                        <div className="trainer-details">
                          <div className="mb-2">
                            <Icon icon="fluent:mail-24-regular" className="me-2" />
                            <a href={`mailto:${trainer.email}`} className="text-decoration-none">
                              {trainer.email}
                            </a>
                          </div>
                          <div>
                            <Icon icon="fluent:phone-24-regular" className="me-2" />
                            <a href={`tel:${trainer.phone}`} className="text-decoration-none">
                              {trainer.phone}
                            </a>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="row">
        <div className="card-body p-0">
          <div className="tab-header border-bottom position-relative">
            <div className="d-flex justify-content-between align-items-center">
              <ul className="nav nav-tabs">
                <li className="nav-item">
                  <button
                    className={`nav-link d-flex align-items-center ${activeTab === 'assignments' ? 'active' : ''} ${!permissions.classroom_assignment_view ? 'disabled' : ''}`}
                    onClick={() => handleTabChange('assignments')}
                    disabled={!permissions.classroom_assignment_view}
                    title={!permissions.classroom_assignment_view ? "You don't have permission to view assignments" : "View Assignments"}
                  >
                    <Icon icon="fluent:clipboard-task-24-regular" className="me-2" width="20" height="20" />
                    Assignments
                  </button>
                </li>
                <li className="nav-item">
                  <button
                    className={`nav-link d-flex align-items-center ${activeTab === 'assessments' ? 'active' : ''} ${!permissions.classroom_assessment_view ? 'disabled' : ''}`}
                    onClick={() => handleTabChange('assessments')}
                    disabled={!permissions.classroom_assessment_view}
                    title={!permissions.classroom_assessment_view ? "You don't have permission to view assessments" : "View Assessments"}
                  >
                    <Icon icon="fluent:quiz-new-24-regular" className="me-2" width="20" height="20" />
                    Assessments
                  </button>
                </li>
                <li className="nav-item">
                  <button
                    className={`nav-link d-flex align-items-center ${activeTab === 'live' ? 'active' : ''} ${!permissions.classroom_live_classes_view ? 'disabled' : ''}`}
                    onClick={() => handleTabChange('live')}
                    disabled={!permissions.classroom_live_classes_view}
                    title={!permissions.classroom_live_classes_view ? "You don't have permission to view live classes" : "View Live Classes"}
                  >
                    <Icon icon="fluent:video-person-24-regular" className="me-2" width="20" height="20" />
                    Live Classes
                  </button>
                </li>
                <li className="nav-item">
                  <button
                    className={`nav-link d-flex align-items-center ${activeTab === 'recorded' ? 'active' : ''} ${!permissions.classroom_resources_view ? 'disabled' : ''}`}
                    onClick={() => handleTabChange('recorded')}
                    disabled={!permissions.classroom_resources_view}
                    title={!permissions.classroom_resources_view ? "You don't have permission to view resources" : "View Resources"}
                  >
                    <Icon icon="fluent:document-24-regular" className="me-2" width="20" height="20" />
                    Resources
                  </button>
                </li>
                <li className="nav-item">
                  <button
                    className={`nav-link d-flex align-items-center ${activeTab === 'community' ? 'active' : ''} ${!permissions.classroom_community_view ? 'disabled' : ''}`}
                    onClick={() => handleTabChange('community')}
                    disabled={!permissions.classroom_community_view}
                    title={!permissions.classroom_community_view ? "You don't have permission to view community" : "View Community"}
                  >
                    <Icon icon="fluent:chat-24-regular" className="me-2" width="20" height="20" />
                    Community
                  </button>
                </li>
              </ul>

              <div className='d-none d-sm-flex align-items-center gap-3 '>
                <div className="text-muted small ">
                  <span className="me-2">
                    <Icon icon="fluent:eye-24-regular" className="me-1" />
                    {Object.values(tabVisibility).filter(Boolean).length} visible
                  </span>
                  <span>
                    <Icon icon="fluent:eye-off-24-regular" className="me-1" />
                    {Object.values(tabVisibility).filter(v => !v).length} hidden
                  </span>
                </div>
                <div className="position-relative d-flex align-items-center gap-3">

                  <button
                    ref={buttonRef}
                    className="btn btn-outline-secondary btn-sm d-flex align-items-center gap-2"
                    onClick={(e) => {
                      e.stopPropagation();
                      setIsTabSettingsOpen(!isTabSettingsOpen);
                    }}
                  >
                    <Icon icon="fluent:settings-24-regular" width="20" height="20" />
                    Tab Settings
                  </button>
                  {isTabSettingsOpen && <TabSettings />}
                </div>
              </div>

            </div>
          </div>

          {/* Tab Content */}
          <div className="tab-content">
            {activeTab === 'assignments' && permissions.classroom_assignment_view && (
              <div className="assignments">
                <ClassroomAssignments decodedClassroomId={decodedClassroomId} />
              </div>
            )}

            {activeTab === 'assessments' && permissions.classroom_assessment_view && (
              <div className="assessments">
                <ClassroomAssessments decodedClassroomId={decodedClassroomId} />
              </div>
            )}

            {activeTab === 'live' && permissions.classroom_live_classes_view && (
              <div className="live-classes">
                <ClassroomLiveClasses decodedClassroomId={decodedClassroomId} />
              </div>
            )}

            {activeTab === 'recorded' && permissions.classroom_resources_view && (
              <div className="recorded">
                <ClassroomRecorded decodedClassroomId={decodedClassroomId} />
              </div>
            )}

            {activeTab === 'community' && permissions.classroom_community_view && (
              <div className="community">
                <ClassroomCommunity
                  classroom_id={classroomData?.classroom_id}
                  group_id={groupData?.group_id}
                  group_name={groupData?.group_name}
                />
              </div>
            )}

            {/* Show message when user doesn't have permission for current tab */}
            {((activeTab === 'assignments' && !permissions.classroom_assignment_view) ||
              (activeTab === 'assessments' && !permissions.classroom_assessment_view) ||
              (activeTab === 'live' && !permissions.classroom_live_classes_view) ||
              (activeTab === 'recorded' && !permissions.classroom_resources_view) ||
              (activeTab === 'community' && !permissions.classroom_community_view)) && (
              <div className="text-center py-5">
                <Icon icon="fluent:error-circle-24-regular" className="text-danger mb-3" width="48" height="48" />
                <h5>Access Denied</h5>
                <p className="text-muted">You don't have permission to view this content.</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </>
  )
}

export default ClassroomDashboard 