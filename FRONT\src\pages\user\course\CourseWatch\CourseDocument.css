.course-document-container {
  padding: 1.5rem;
}

.document-controls {
  position: sticky;
  top: 0;
  z-index: 100;
}

.navigation-controls,
.zoom-controls {
  display: flex;
  align-items: center;
}

.pdf-container {
  min-height: 500px;
  position: relative;
}

.pdf-container .react-pdf__Document {
  display: flex;
  justify-content: center;
}

.pdf-container .react-pdf__Page {
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.15);
  margin: 1rem 0;
}

.loading-spinner {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .course-document-container {
    padding: 1rem;
  }

  .document-controls .d-flex {
    flex-direction: column;
    gap: 1rem;
  }

  .navigation-controls,
  .zoom-controls {
    width: 100%;
    justify-content: center;
  }
}

/* Dark mode support */
[data-theme='dark'] .document-controls,
[data-theme='dark'] .pdf-container {
  background-color: var(--bg-secondary) !important;
}

[data-theme='dark'] .pdf-container .react-pdf__Page {
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
} 