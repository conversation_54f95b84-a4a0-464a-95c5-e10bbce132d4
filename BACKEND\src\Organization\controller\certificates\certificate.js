const { mysqlServerConnection } = require('../../../db/db');
const path = require("path");
const fs = require("fs");
const { createCanvas, loadImage } = require("canvas");
// const pdfPoppler = require('pdf-poppler');
// const { PDFDocument } = require('pdf-lib');
// const { fromPath } = require('pdf2pic');
const { exec } = require('child_process');
const { spawn } = require('child_process');
const AWS = require('aws-sdk');
const { uploadDocxToS3, uploadImageToS3 } = require('../../../tools/aws');
// Import required modules for FastAPI conversion
const axios = require('axios');
const zlib = require('zlib');
const FormData = require('form-data');

// Configure AWS S3
const s3 = new AWS.S3({
    accessKeyId: process.env.S3_AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.S3_AWS_SECRET_ACCESS_KEY,
    region: process.env.S3_AWS_REGION
});


// const convertDocToImages = async (docS3Url, outputDir) => {
//     try {
//         // Parse the S3 URL to get the bucket name and key
//         const urlParts = new URL(docS3Url);
//         const bucketName = urlParts.hostname.split('.')[0];
//         const key = decodeURIComponent(urlParts.pathname.substring(1));

//         // Ensure the output directory exists
//         if (!fs.existsSync(outputDir)) {
//             fs.mkdirSync(outputDir, { recursive: true });
//         }

//         // Download the DOCX file from S3
//         const docFilePath = path.join(outputDir, path.basename(key));
//         const docFile = await s3.getObject({ Bucket: bucketName, Key: key }).promise();
//         fs.writeFileSync(docFilePath, docFile.Body);
//         console.log('DOCX file downloaded successfully from S3:', docFilePath);

//         // Step 1: Convert DOCX to PDF using LibreOffice
//         const pdfFileName = `${path.basename(docFilePath, path.extname(docFilePath))}.pdf`;
//         const pdfPath = path.join(outputDir, pdfFileName);
//         const command = process.platform === "win32"
//         ? `"C:\\Program Files\\LibreOffice\\program\\soffice.exe" --headless --convert-to pdf --outdir "${outputDir}" "${docFilePath}"`
//         : `libreoffice --headless --convert-to pdf --outdir "${outputDir}" "${docFilePath}"`;
//         console.log(`Converting DOCX to PDF: ${pdfPath}`);
//         await new Promise((resolve, reject) => {
//             exec(command, (error) => {
//                 if (error) {
//                     console.error('Error converting DOCX to PDF:', error);
//                     return reject(error);
//                 }
//                 console.log('DOCX converted to PDF successfully:', pdfPath);
//                 resolve();
//             });
//         });

//         // Validate if the PDF file was created
//         if (!fs.existsSync(pdfPath)) {
//             throw new Error(`PDF file not found at path: ${pdfPath}`);
//         }

//         // Step 2: Convert PDF to images using pdf-poppler
//         const options = {
//             format: 'png',
//             out_dir: outputDir,
//             out_prefix: path.basename(pdfPath, path.extname(pdfPath)),
//             page: null, // Convert all pages
//         };

//         console.log('Converting PDF to images...');
//         await pdfPoppler.convert(pdfPath, options);

//         // Collect all generated image files
//         const imageFiles = fs.readdirSync(outputDir).filter((file) =>
//             file.startsWith(options.out_prefix) && file.endsWith('.png')
//         );

//         if (imageFiles.length === 0) {
//             throw new Error('No images generated from the PDF');
//         }

//         console.log('Images generated:', imageFiles);

//         let imageUrl = ""
//         // Step 3: Upload the images to S3
//         const uploadedImageUrls = [];
//         for (const file of imageFiles) {
//             const imagePath = path.join(outputDir, file);
//             const fileStream = fs.createReadStream(imagePath);
//             console.log(fileStream)

//             imageUrl = await uploadImageToS3(fileStream)
//         }

//         // Return the URLs of the uploaded images
//         return imageUrl;
//     } catch (error) {
//         console.error('Error in convertDocToImages:', error);
//         throw error;
//     }
// };

const convertDocToImages = async (docS3Url, outputDir, dpi = 300, templateData = null, userData = null) => {
    try {
        // Parse S3 URL
        const urlParts = new URL(docS3Url);
        const bucketName = urlParts.hostname.split('.')[0];
        const key = decodeURIComponent(urlParts.pathname.substring(1));

        // Ensure output directory exists
        if (!fs.existsSync(outputDir)) {
            fs.mkdirSync(outputDir, { recursive: true });
        }

        // Download DOCX from S3
        const docFile = await s3.getObject({ Bucket: bucketName, Key: key }).promise();
        let docBuffer = docFile.Body;

        // If we have template data and user data, process the template
        if (templateData && userData) {
            try {
                // Use docxtemplater to replace variables in the template
                const PizZip = require('pizzip');
                const Docxtemplater = require('docxtemplater');
                
                // Load the template
                const zip = new PizZip(docBuffer);
                const doc = new Docxtemplater(zip, {
                    paragraphLoop: true,
                    linebreaks: true,
                });

                // Parse template fields to get variable names
                let templateFields = [];
                try {
                    templateFields = JSON.parse(templateData.template_fields);
                } catch (e) {
                    console.error('Error parsing template fields:', e);
                }

                // Create data object for template
                const templateVars = {};
                
                // Map template fields to user data
                templateFields.forEach(field => {
                    const fieldName = field.inputName;
                    console.log(`Processing field: ${fieldName}`);
                    
                    // Add the variable with and without brackets to ensure it works
                    if (fieldName === "NAME") {
                        templateVars[fieldName] = userData.name;
                        templateVars["{" + fieldName + "}"] = userData.name;
                    } else if (fieldName === "COURSE") {
                        templateVars[fieldName] = userData.course;
                        templateVars["{" + fieldName + "}"] = userData.course;
                    } else if (fieldName === "DATE") {
                        templateVars[fieldName] = new Date().toLocaleDateString();
                        templateVars["{" + fieldName + "}"] = new Date().toLocaleDateString();
                    }
                });
                
                // Add certificate_url with empty string to remove it from template
                // This ensures any certificate_url text in the template is replaced with nothing
                templateVars["certificate_url"] = " ";
                templateVars["{certificate_url}"] = " ";
                
                // Add all variables again with lowercase for case-insensitivity
                Object.keys(templateVars).forEach(key => {
                    if (!key.includes("{")) { // Only add lowercase for non-bracketed keys
                        templateVars[key.toLowerCase()] = templateVars[key];
                        templateVars["{" + key.toLowerCase() + "}"] = templateVars[key];
                    }
                });

                console.log('Final template variables:', templateVars);
                
                // Apply the template data using the recommended approach
                // This replaces the deprecated setData + render sequence
                doc.render(templateVars);
                
                // Get the modified document
                docBuffer = doc.getZip().generate({type: 'nodebuffer'});
                console.log('Template variables successfully replaced');
            } catch (error) {
                console.error('Error processing template:', error);
                // Continue with original document if template processing fails
            }
        }

        // Define paths for the DOCX and PDF output
        const docFilePath = path.join(outputDir, path.basename(key));
        fs.writeFileSync(docFilePath, docBuffer);
        console.log('[INFO] DOCX file processed and saved:', docFilePath);

        // Define paths for the PDF output
        const pdfFilePath = path.join(outputDir, path.basename(key, path.extname(key)) + '.pdf');
        
        console.log('Converting DOCX to PDF using FastAPI service...');
        
        try {
            // Read the DOCX file
            const fileBuffer = fs.readFileSync(docFilePath);
            
            // Compress the file buffer with gzip
            const gzippedBuffer = zlib.gzipSync(fileBuffer);
            
            // Create form data for the API request
            const form = new FormData();
            form.append("file", gzippedBuffer, {
                filename: path.basename(docFilePath),
                contentType: "application/gzip",
            });
            
            // Send the request to the FastAPI service
            const apiResponse = await axios.post(
                "http://************:8000/convert",
                form,
                {
                    headers: form.getHeaders(),
                    responseType: "stream",
                    timeout: 30000, // 30 seconds timeout
                }
            );
            
            // Pipe through gunzip before saving as PDF
            const writeStream = fs.createWriteStream(pdfFilePath);
            const gunzip = zlib.createGunzip();
            
            await new Promise((resolve, reject) => {
                apiResponse.data
                    .pipe(gunzip)
                    .pipe(writeStream)
                    .on("finish", resolve)
                    .on("error", reject);
            });
            
            console.log('DOCX converted to PDF successfully using FastAPI service:', pdfFilePath);
            
        } catch (error) {
            console.error('Error converting DOCX to PDF with FastAPI:', error);
            throw error;
        }

        // Validate if the PDF file was created
        let pythonPath = '';
        if (process.platform === 'win32') {
            pythonPath = "python";
        } else {
            pythonPath = path.join(__dirname, 'myenv', 'bin', 'python3');
        }

        const imageurl = await new Promise((resolve, reject) => {
            const pythonProcess = spawn(pythonPath, [
                'src/Organization/controller/certificates/doctoimage.py',
                pdfFilePath,
                outputDir,
                '--dpi',
                '300'  // Increased from default to 300 for better quality
            ]);

            let result = '';
            let error = '';

            pythonProcess.stdout.on('data', (data) => {
                result += data.toString();
            });

            pythonProcess.stderr.on('data', (data) => {
                error += data.toString();
            });

            pythonProcess.on('close', (code) => {
                if (code !== 0 || error) {
                    console.error('Python script error:', error);
                    reject(new Error(`Python script failed: ${error}`));
                } else {
                    resolve(result.trim());
                }
            });
        });

        console.log(imageurl);
        
        // Add robust error handling for JSON parsing
        let imagedata;
        try {
            // Extract only the valid JSON part from the output
            // This is a more robust approach to find the complete JSON object or array
            let jsonStart = imageurl.indexOf('[');
            if (jsonStart === -1) {
                jsonStart = imageurl.indexOf('{');
            }
            
            if (jsonStart === -1) {
                throw new Error('No JSON data found in Python script output');
            }
            
            // Find the matching closing bracket/brace
            let jsonEnd = -1;
            if (imageurl[jsonStart] === '[') {
                // Find the matching closing bracket for an array
                let depth = 0;
                for (let i = jsonStart; i < imageurl.length; i++) {
                    if (imageurl[i] === '[') depth++;
                    if (imageurl[i] === ']') depth--;
                    if (depth === 0) {
                        jsonEnd = i + 1; // Include the closing bracket
                        break;
                    }
                }
            } else {
                // Find the matching closing brace for an object
                let depth = 0;
                for (let i = jsonStart; i < imageurl.length; i++) {
                    if (imageurl[i] === '{') depth++;
                    if (imageurl[i] === '}') depth--;
                    if (depth === 0) {
                        jsonEnd = i + 1; // Include the closing brace
                        break;
                    }
                }
            }
            
            if (jsonEnd === -1) {
                throw new Error('Could not find end of JSON data in Python script output');
            }
            
            const jsonPart = imageurl.substring(jsonStart, jsonEnd);
            console.log('Extracted JSON part (length):', jsonPart.length);
            
            imagedata = JSON.parse(jsonPart);
            
            // Check if the response contains an error
            if (imagedata.error) {
                throw new Error(`PDF conversion error: ${imagedata.error}`);
            }
            
            // Verify we have the expected data structure
            if (!Array.isArray(imagedata) || imagedata.length === 0 || !imagedata[0].buffer) {
                throw new Error('Invalid image data format returned from conversion');
            }
            
            const imageBuffer = Buffer.from(imagedata[0].buffer, "base64");
            const result = await uploadImageToS3(imageBuffer, imagedata[0].filename);
            
            //remove all file in outputDir

            return result;
        } catch (err) {
            console.error('[ERROR] JSON parsing or processing error:', err);
            throw new Error(`Conversion failed: ${err.message}`);
        }
    } catch (err) {
        console.error('[ERROR]', err);
        throw new Error(`Conversion failed: ${err.message}`);
    }
};

// Route handler for generating certificates
const generateCertificate = async (req, res) => {
    try {
        const { course_id } = req.body;
        const db_name = req.user.db_name;
        const userId = req.user.userId;

        console.log('\n========== GENERATE CERTIFICATE ROUTE START ==========');
        console.log('[INFO] course_id from request:', course_id);
        console.log('[INFO] userId:', userId);

        if (course_id) {
            // Generate certificate for specific course
            const result = await GenerateCertificate(req.user, course_id);
            return res.status(200).json({
                success: true,
                message: result
            });
        } else {
            // Generate certificates for all completed courses
            console.log('[INFO] No course_id provided, generating certificates for all completed courses');

            // Get all completed courses for the user
            const [completedCourses] = await mysqlServerConnection.query(
                `SELECT course_id FROM ${db_name}.mycourses
                 WHERE user_id = ? AND is_completed = 1`,
                [userId]
            );

            if (!completedCourses || completedCourses.length === 0) {
                return res.status(404).json({
                    success: false,
                    message: "No completed courses found for certificate generation"
                });
            }

            console.log('[INFO] Found completed courses:', completedCourses.length);

            let generatedCount = 0;
            const errors = [];

            for (const course of completedCourses) {
                try {
                    // Check if certificate already exists
                    const [existingCert] = await mysqlServerConnection.query(
                        `SELECT id FROM ${db_name}.certificates
                         WHERE user_id = ? AND course_id = ?`,
                        [userId, course.course_id]
                    );

                    if (existingCert.length === 0) {
                        await GenerateCertificate(req.user, course.course_id);
                        generatedCount++;
                        console.log(`[SUCCESS] Certificate generated for course ${course.course_id}`);
                    } else {
                        console.log(`[SKIP] Certificate already exists for course ${course.course_id}`);
                    }
                } catch (error) {
                    console.error(`[ERROR] Failed to generate certificate for course ${course.course_id}:`, error.message);
                    errors.push(`Course ${course.course_id}: ${error.message}`);
                }
            }

            return res.status(200).json({
                success: true,
                message: `Generated ${generatedCount} new certificates`,
                details: {
                    generated: generatedCount,
                    total_completed_courses: completedCourses.length,
                    errors: errors
                }
            });
        }
    } catch (error) {
        console.error('Error in generateCertificate route:', error);
        return res.status(500).json({
            success: false,
            message: error.message || "Certificate generation failed"
        });
    }
};

// Internal function for generating certificates
const GenerateCertificate = async (user, course_id) => {
    const db_name = user.db_name;
    const userId = user.userId;

    console.log('\n========== GENERATE CERTIFICATE START ==========');
    console.log('[INFO] db_name:', db_name);
    console.log('[INFO] userId:', userId);
    console.log('[INFO] course_id:', course_id);

    try {
        console.log('[INFO] Fetching user data...');
        const [userData] = await mysqlServerConnection.query(
            `SELECT name FROM ${db_name}.users WHERE id = ?`,
            [userId]
        );
        console.log('[DATA] User Data:', userData);

        if (!userData || userData.length === 0) {
            throw new Error("User not found.");
        }

        const username = userData[0].name;
        console.log('[INFO] Username:', username);

        console.log('[INFO] Fetching course data...');
        const [courseData] = await mysqlServerConnection.query(
            `SELECT course_name, certificate_template_id FROM ${db_name}.courses WHERE id = ?`,
            [course_id]
        );
        console.log('[DATA] Course Data:', courseData);

        if (!courseData || courseData.length === 0) {
            throw new Error("Course not found.");
        }

        const courseName = courseData[0].course_name;
        let certificateTemplateId = courseData[0].certificate_template_id;

        if (!certificateTemplateId) {
            console.log('[WARN] No certificate template found, fetching default template...');
            const [defaultTemplate] = await mysqlServerConnection.query(
                `SELECT id FROM ${db_name}.certificate_templates WHERE is_deleted = 0 AND is_active = 1 LIMIT 1`
            );
            console.log('[DATA] Default Template:', defaultTemplate);

            if (!defaultTemplate || defaultTemplate.length === 0) {
                throw new Error("No certificate templates available. Please create a template first.");
            }

            certificateTemplateId = defaultTemplate[0].id;

            await mysqlServerConnection.query(
                `UPDATE ${db_name}.courses SET certificate_template_id = ? WHERE id = ?`,
                [certificateTemplateId, course_id]
            );
            console.log(`[INFO] Updated course ${course_id} with default certificate template ${certificateTemplateId}`);
        }

        console.log('[INFO] Checking for existing certificate...');
        const [existingCertificate] = await mysqlServerConnection.query(
            `SELECT * FROM ${db_name}.certificates WHERE user_id = ? AND course_id = ? FOR UPDATE`,
            [userId, course_id]
        );
        console.log('[DATA] Existing Certificate:', existingCertificate);

        if (existingCertificate && existingCertificate.length > 0) {
            console.log('[INFO] Certificate already exists.');
            return "Certificate for the course is already generated.";
        }

        const tempPlaceholderUrl = "temp_placeholder";
        console.log('[INFO] Inserting placeholder certificate...');
        await mysqlServerConnection.query(
            `INSERT INTO ${db_name}.certificates 
             (course_id, created_by, user_id, certificate_url, certificate_name, template_id) 
             VALUES (?, ?, ?, ?, ?, ?)`,
            [
                course_id,
                1,
                userId,
                tempPlaceholderUrl,
                "Certificate Of Completion",
                certificateTemplateId,
            ]
        );
        console.log('[INFO] Placeholder inserted.');

        console.log('[INFO] Fetching certificate template data...');
        const [templateData] = await mysqlServerConnection.query(
            `SELECT * FROM ${db_name}.certificate_templates WHERE id = ? AND is_deleted = 0 AND is_active = 1`,
            [certificateTemplateId]
        );
        console.log('[DATA] Template Data:', templateData);

        if (!templateData || templateData.length === 0) {
            throw new Error(`Certificate template with ID ${certificateTemplateId} not found or inactive.`);
        }

        const userTemplateData = {
            name: username,
            course: courseName,
            date: new Date().toLocaleDateString()
        };

        const templateDocUrl = templateData[0].template_url;
        if (!templateDocUrl) {
            throw new Error("Certificate template URL not found.");
        }
        console.log('[INFO] Template URL:', templateDocUrl);

        const tempDir = path.join(__dirname, '..', '..', '..', 'temp', `cert_${userId}_${Date.now()}`);
        if (!fs.existsSync(tempDir)) {
            fs.mkdirSync(tempDir, { recursive: true });
        }
        console.log('[INFO] Created temp directory:', tempDir);

        try {
            console.log('[INFO] Starting certificate generation...');
            const result = await convertDocToImages(templateDocUrl, tempDir, 300, templateData[0], userTemplateData);
            console.log('[DATA] Conversion Result:', result);

            if (!result || !result.path) {
                throw new Error("Failed to generate certificate image");
            }

            await mysqlServerConnection.query(
                `UPDATE ${db_name}.certificates 
                 SET certificate_url = ? 
                 WHERE user_id = ? AND course_id = ?`,
                [
                    result.path,
                    userId,
                    course_id,
                ]
            );
            console.log('[INFO] Certificate updated with final URL.');

            try {
                if (fs.existsSync(tempDir)) {
                    fs.rmSync(tempDir, { recursive: true, force: true });
                    console.log('[INFO] Temp directory cleaned up.');
                }
            } catch (cleanupError) {
                console.error("Error cleaning up temp directory:", cleanupError);
            }

            console.log('[SUCCESS] Certificate generated successfully.');
            return "Certificate generated successfully.";
        } catch (processingError) {
            console.error("Error processing certificate:", processingError);

            await mysqlServerConnection.query(
                `DELETE FROM ${db_name}.certificates 
                 WHERE user_id = ? AND course_id = ? AND certificate_url = ?`,
                [userId, course_id, tempPlaceholderUrl]
            );
            console.log('[INFO] Placeholder entry deleted after error.');

            try {
                if (fs.existsSync(tempDir)) {
                    fs.rmSync(tempDir, { recursive: true, force: true });
                    console.log('[INFO] Temp directory cleaned after error.');
                }
            } catch (cleanupError) {
                console.error("Error cleaning up temp directory:", cleanupError);
            }

            throw processingError;
        }
    } catch (error) {
        console.error("Error generating certificate:", error);

        await mysqlServerConnection.query(
            `DELETE FROM ${db_name}.certificates 
             WHERE user_id = ? AND course_id = ? AND certificate_url = ?`,
            [userId, course_id, "temp_placeholder"]
        );
        console.log('[INFO] Final fallback delete for placeholder certificate.');

        throw new Error("Certificate generation failed: " + error.message);
    }
};


const getTraineeCertificates = async (req, res) => {
  try {
    const db_name = req.user.db_name;
    const userId = req.user.userId;

    if (!userId || !db_name) {
      return res.status(400).json({
        success: false,
        message: 'Missing user or database information.',
      });
    }

    const [certificates] = await mysqlServerConnection.query(
      `SELECT 
          u.name AS user_name,
          u.email AS user_email,
          c.certificate_name,
          c.issued_date,
          c.certificate_url,
          co.course_name,
          created_by_user.name AS createdBy
       FROM ${db_name}.certificates c
       JOIN ${db_name}.users u 
         ON c.user_id = u.id
       JOIN ${db_name}.courses co 
         ON c.course_id = co.id
       JOIN ${db_name}.users created_by_user 
         ON c.created_by = created_by_user.id
       WHERE c.user_id = ? 
         AND c.is_deleted = 0 
         AND c.certificate_url IS NOT NULL 
         AND c.certificate_url != ''`,
      [userId]
    );

    return res.status(200).json({
      success: true,
      message: 'Certificate data fetched successfully',
      data: {
        certificates: certificates || []
      }
    });

  } catch (error) {
    console.error('❌ Error in getTraineeCertificates:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error',
    });
  }
};


const createCertificate = async (req, res) => {

    console.log(req.body)
    try {
        // check if file is there in the request or not
        const db_name = req.user.db_name
        const userId = req.user.userId

        console.log(`File ${db_name}    ${userId}`)

        try {
            JSON.parse(req.body.fields);
        }
        catch (error) {
            return res.status(400).json({ success: false, data: { error_msg: "Fields should be in JSON format" } });
        }
        if (!req.file) {
            return res.status(400).json({ success: false, data: { error_msg: "Please upload a certificate file" } });
        }

        const fileSize = 30 * 1024 * 1024; // 30 MB
        if (req.file.size > fileSize) {
            return res.status(400).json({ success: false, data: { error_msg: "File size should be less than 30 MB" } });
        }

        // check if file is of type docx or not and have only single . which is before the extension docx or DOCX
        if (!req.file.originalname.endsWith('.docx')) {
            return res.status(400).json({ success: false, data: { error_msg: "File should be of type docx with extension .docx" } });
        }

        if (req.file.originalname.split('.').length > 2) {
            return res.status(400).json({ success: false, data: { error_msg: "File name should not contain more than one dot" } });
        }

        if (req.file.mimetype !== 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') {
            return res.status(400).json({ success: false, data: { error_msg: "File should be of type docx" } });
        }

        // no space in file name
        if (req.file.originalname.includes(' ')) {
            return res.status(400).json({ success: false, data: { error_msg: "File name should not contain space" } });
        }
        if (req.body.fields === undefined) {
            return res.status(400).json({ success: false, data: { error_msg: "Fields should not be empty" } });
        }
        const timeStamp = new Date().getTime();

        const doc = await uploadDocxToS3(req.file.buffer, req.file.originalname)



        
        // check in db that this template name is already exist or not
        const [template] = await mysqlServerConnection.query(`SELECT * FROM ${db_name}.certificate_templates WHERE template_name = ? AND is_deleted = 0`, [req.file.originalname]);

        if (template.length > 0) {
            return res.status(400).json({ success: false, data: { error_msg: "Template name already exist" } });
        }

        const docPath = doc.path
        const outputDir = "nexgenversity"
        if (!fs.existsSync(outputDir)) {
            fs.mkdirSync(outputDir, { recursive: true });
        }

        const imageUrls = await convertDocToImages(docPath, outputDir);

        console.log(`Converting ${imageUrls} to images...`);

        await mysqlServerConnection.query(`INSERT INTO ${db_name}.certificate_templates (template_name, template_url, template_fields,image_url) VALUES (?, ?, ?,?)`, [req.file.originalname, docPath, req.body.fields, imageUrls.path]);

        // i want to delete all the files inside the outputdir
        fs.readdir(outputDir, (err, files) => {
            if (err) {
                console.error(err);
                return;
            }

            for (const file of files) {
                fs.unlink(path.join(outputDir, file), (err) => {
                    if (err) {
                        console.error(err);
                        return;
                    }
                });
            }
        });

        return res.status(200).json({
            success: true,
            data: {
                name: req.file.originalname,
                fields: req.body.fields
            }
        });
    }

    catch (error) {
        console.log(error);
        return res.status(500).json({ success: false, data: { error_msg: "Internal Server Error" } });
    }
};

const createCertificatesForRandomUsers = async (req, res) => {
    try {
        const { userCount } = req.body; // Input: Number of users and courses to process

        // Step 1: Fetch random users
        const [users] = await mysqlServerConnection.query(
            `SELECT id FROM ${req.user.db_name}.users ORDER BY RAND() LIMIT ?`,
            [userCount]
        );

        if (!users.length) {
            return res.status(404).json({
                success: false,
                message: "No users found",
            });
        }

        for (const user of users) {
            // Step 2: Fetch random courses

            const [courses] = await mysqlServerConnection.query(
                `SELECT id FROM ${req.user.db_name}.courses ORDER BY RAND() LIMIT ?`,
                [1]
            );

            if (!courses.length) continue;


            // Step 3: Update the mycourse table for the user and course
            const [result] = await mysqlServerConnection.query(
                `INSERT INTO ${req.user.db_name}.mycourses (user_id, course_id, is_completed)
                     VALUES (?, ?, 1)
                     ON DUPLICATE KEY UPDATE is_completed = 1`,
                [user.id, courses[0].id]
            );

            console.log(
                `Updated mycourses for user ${user.id} and course ${result.insertId}`
            );

            // Step 4: Generate the certificate
            const userObj = { db_name: req.user.db_name, userId: user.id };
            const [template] = await mysqlServerConnection.query(
                `SELECT id FROM ${req.user.db_name}.certificate_templates WHERE is_active = 1 AND is_deleted = 0 LIMIT 1`,
                []
            );
            if (template && template.length > 0) {
                const created = await GenerateCertificate(userObj, result.insertId);

                if (!created) {
                    console.log(
                        `Failed to generate certificate for user ${user.id}, course ${result.insertId}`
                    );
                }
            }

        }

        return res.status(200).json({
            success: true,
            message: "Certificates generated for random users and courses",
        });
    } catch (error) {
        console.error(error);
        return res.status(500).json({
            success: false,
            message: error.message || "Internal Server Error",
        });
    }
};

const getTemplates = async (req, res) => {
    try {
        const db_name = req.user.db_name;

        const page = parseInt(req.query.page, 10) || 1;
        const limit = parseInt(req.query.limit, 10) || 10;
        const offset = (page - 1) * limit;

        const [templates] = await mysqlServerConnection.query(
            `SELECT * FROM ${db_name}.certificate_templates WHERE is_active = 1 AND is_deleted = 0 LIMIT ? OFFSET ?`,
            [limit, offset]
        );

        const [[{ total }]] = await mysqlServerConnection.query(
            `SELECT COUNT(*) AS total FROM ${db_name}.certificate_templates WHERE is_active = 1 AND is_deleted = 0`
        );

        const origin = `${req.protocol}://${req.get('host')}`;  
        console.log('Request origin:', origin);

        const updatedTemplates = await Promise.all(
            templates.map(async (template) => {
                try {

                    return { ...template };
                } catch (error) {
                    console.error(`Error processing template ${template.id}:`, error);
                    return { ...template, imageUrls: [] };
                }
            })
        );

        const totalPages = Math.ceil(total / limit);

        return res.status(200).json({
            success: true,
            data: {
                templates: updatedTemplates,
                pagination: {
                    currentPage: page,
                    totalPages,
                    totalRecords: total,
                    recordsPerPage: limit,
                },
            },
        });
    } catch (error) {
        console.error('Error in getTemplates:', error);
        return res.status(500).json({
            success: false,
            data: { error_msg: 'Internal Server Error' },
        });
    }
};

const getCertificates = async (req, res) => {
    console.log('getCertificates called');
    try {
        const db_name = req.user.db_name;
        const search = req.query.search; // Get the search query parameter

        // Base query to fetch certificate templates
        let query = `SELECT * FROM ${db_name}.certificate_templates WHERE is_active = 1 AND is_deleted = 0`;

        // Add search filter if search value is provided
        // if (search) {
        //     query += ` AND (template_name LIKE ?)`;
        // }

        // Execute the query with search parameters (if any)
        const [templates] = await mysqlServerConnection.query(
            query,
            search ? [`%${search}%`, `%${search}%`] : []
        );

        const origin = `${req.protocol}://${req.get('host')}`;
        console.log('Request origin:', origin);

        // Process templates (if needed)
        const updatedTemplates = await Promise.all(
            templates.map(async (template) => {
                try {
                    // Add any additional processing here if needed
                    return { ...template };
                } catch (error) {
                    console.error(`Error processing template ${template.id}:`, error);
                    return { ...template };
                }
            })
        );

        return res.status(200).json({
            success: true,
            data: {
                templates: updatedTemplates,
            },
        });
    } catch (error) {
        console.error('Error in getCertificates:', error);
        return res.status(500).json({
            success: false,
            data: { error_msg: 'Internal Server Error' },
        });
    }
};

const editTemplates = async (req, res) => {
    try {
        // check if file is there in the request or not
        const db_name = req.user.db_name
        const userId = req.user.userId
        const { id, fields, upload_url } = req.body

        if (upload_url == null) {
            try {
                JSON.parse(req.body.fields);
            }
            catch (error) {
                return res.status(400).json({ success: false, data: { error_msg: "Fields should be in JSON format" } });
            }
            if (!req.file) {
                return res.status(400).json({ success: false, data: { error_msg: "Please upload a certificate file" } });
            }

            const fileSize = 30 * 1024 * 1024; // 30 MB
            if (req.file.size > fileSize) {
                return res.status(400).json({ success: false, data: { error_msg: "File size should be less than 30 MB" } });
            }

            // check if file is of type docx or not and have only single . which is before the extension docx or DOCX
            if (!req.file.originalname.endsWith('.docx')) {
                return res.status(400).json({ success: false, data: { error_msg: "File should be of type docx with extension .docx" } });
            }

            if (req.file.originalname.split('.').length > 2) {
                return res.status(400).json({ success: false, data: { error_msg: "File name should not contain more than one dot" } });
            }

            if (req.file.mimetype !== 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') {
                return res.status(400).json({ success: false, data: { error_msg: "File should be of type docx" } });
            }

            // no space in file name
            if (req.file.originalname.includes(' ')) {
                return res.status(400).json({ success: false, data: { error_msg: "File name should not contain space" } });
            }
            if (req.body.fields === undefined) {
                return res.status(400).json({ success: false, data: { error_msg: "Fields should not be empty" } });
            }

            const timeStamp = new Date().getTime();
            let templateName = req.file.originalname;

            const doc = await uploadDocxToS3(req.file.buffer, templateName)

            const docPath = doc.path
            const outputDir = `nexgenversity/images/${timeStamp}-${req.file.originalname}`
            console.log(`Processing template: -> ${docPath}`);

            const imageUrls = await convertDocToImages(docPath, outputDir);
            console.log(`Images generated for template :`, imageUrls);


            console.log(imageUrls);


            await mysqlServerConnection.query(
                `UPDATE ${db_name}.certificate_templates 
                 SET template_url = ?, template_fields = ?, image_url = ?, template_name = ? 
                 WHERE id = ?`,
                [docPath, fields, imageUrls.path, templateName, id]
            );

        }
        else {
            console.log("upload_url", req.body)
            await mysqlServerConnection.query(
                `UPDATE ${db_name}.certificate_templates 
                 SET template_url = ?, template_fields = ? 
                 WHERE id = ?`,
                [upload_url, fields, id]
            );

        }
        return res.status(200).json({
            success: true,
            data: {
                message: "Template updated successfully"
            }
        });
    }

    catch (error) {
        console.log(error);

    };
}

const deleteTemplate = async (req, res) => {
    try {
        const db_name = req.user.db_name;
        const { id } = req.params;

        if (!id) {
            return res.status(400).json({
                success: false,
                data: { error_msg: "Template ID is required" },
            });
        }

        // Check if the template exists
        const [template] = await mysqlServerConnection.query(
            `SELECT * FROM ${db_name}.certificate_templates WHERE id = ?`,
            [id]
        );

        if (template.length === 0) {
            return res.status(404).json({
                success: false,
                data: { error_msg: "Template not found" },
            });
        }

        const filePath = path.join(__dirname, '../../../app', template[0].template_url);
        const imagePath = path.join(__dirname, '../../../app', template[0].image_url);
        if (fs.existsSync(filePath) && fs.existsSync(imagePath)) {
            fs.unlinkSync(filePath);
            fs.unlinkSync(imagePath);
        } else {
            console.warn(`File not found for template ID: ${id}, skipping file deletion.`);
        }

        // Delete the template from the database
        await mysqlServerConnection.query(
            `DELETE FROM ${db_name}.certificate_templates WHERE id = ?`,
            [id]
        );

        return res.status(200).json({
            success: true,
            data: { message: "Template deleted successfully" },
        });
    } catch (error) {
        console.error('Error in deleteTemplate:', error);
        return res.status(500).json({
            success: false,
            data: { error_msg: "Internal Server Error" },
        });
    }
};

module.exports = {
    getTraineeCertificates,
    createCertificate,
    GenerateCertificate,
    generateCertificate, // Added the route handler
    createCertificatesForRandomUsers,
    getTemplates,
    editTemplates,
    deleteTemplate,
    getCertificates
}