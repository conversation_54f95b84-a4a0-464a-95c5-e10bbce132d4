import React, { useState, useEffect } from 'react';
import { Icon } from '@iconify/react';
import NoData from '../../../components/common/NoData';
import Loader from '../../../components/common/Loader';
import { getOrgUsers, deleteUser, toggleUserStatus, getRolesAndPermissions, createAdminUser, editOrganisationUser } from '../../../services/adminService';
import { toast } from 'react-toastify';
import countryData from '../../../utils/countory.json';
import { usePermissions } from '../../../context/PermissionsContext';

// Password input field component for reuse (moved outside to prevent re-creation)
const PasswordInput = ({ id, label, value, onChange, isRequired, placeholder, error, showPassword, setShowPassword }) => (
  <div className="mb-3">
    <label htmlFor={id} className="form-label">{label}</label>
    <div className="position-relative">
      <input
        type={showPassword ? "text" : "password"}
        className={`form-control ${error ? 'is-invalid' : ''}`}
        id={id}
        name="userPassword"
        value={value}
        onChange={onChange}
        required={isRequired}
        placeholder={placeholder}
      />
      <div
        className="position-absolute end-0 top-50 translate-middle-y px-2"
        style={{ cursor: 'pointer', zIndex: 5 }}
        onClick={() => setShowPassword(!showPassword)}
      >
        <Icon
          icon={showPassword ? "mdi:eye-off" : "mdi:eye"}
          width="20"
          height="20"
          className="text-muted"
        />
      </div>
      {error && (
        <div className="invalid-feedback">
          {error}
        </div>
      )}
    </div>
  </div>
);

function Users() {
  const { permissions } = usePermissions();
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedUser, setSelectedUser] = useState(null);
  const [isDeleting, setIsDeleting] = useState(false);
  const [togglingStatusId, setTogglingStatusId] = useState(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [actionType, setActionType] = useState('Create'); // Add this line
  const [users, setUsers] = useState([]);
  const [totalPages, setTotalPages] = useState(0);
  const [totalUsers, setTotalUsers] = useState(0);
  const [loading, setLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [roles, setRoles] = useState([]);
  const [loadingRoles, setLoadingRoles] = useState(false);
  const [showPassword, setShowPassword] = useState(false);

  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    countryCode: '+65', // Default to Singapore
    password: '',
    role: '',
    profilePhoto: null,
    profilePhotoPreview: null
  });

  const [formErrors, setFormErrors] = useState({
    name: '',
    email: '',
    phone: '',
    password: '',
    role: '',
    profilePhoto: ''
  });

  const [searchCountry, setSearchCountry] = useState('');
  const [showCountryDropdown, setShowCountryDropdown] = useState(false);

  // Filter countries based on search
  const filteredCountries = countryData.countoryCode.filter(country =>
    country.country.toLowerCase().includes(searchCountry.toLowerCase()) ||
    country.code.includes(searchCountry)
  );

  const fetchUsers = async () => {
    setLoading(true);
    try {
      const response = await getOrgUsers({
        page: currentPage,
        limit: itemsPerPage,
        search: searchQuery,
        state: statusFilter
      });

      console.log('users response--------------', response);

      if (response.success) {
        setUsers(response.data.users);
        setTotalPages(response.data.pagination.totalPages);
        setTotalUsers(response.data.pagination.totalCount);
      }
    } catch (error) {
      console.error('Error fetching users:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchUsers();
  }, [currentPage, itemsPerPage, searchQuery, statusFilter]);

  // Add new function to fetch roles
  const fetchRoles = async () => {
    setLoadingRoles(true);
    try {
      const response = await getRolesAndPermissions({
        page: 1,
        limit: 100, // Get all roles
        state: 'active',
        search: ''
      });

      console.log("roles response--------------", response.data.rolesWithPermissions);

      if (response.success) {
        // Filter only active roles
        const activeRoles = response.data.rolesWithPermissions.filter(role => role.is_active === 1);
        setRoles(activeRoles);
      }
    } catch (error) {
      console.error('Error fetching roles:', error);
      toast.error('Failed to fetch roles');
    } finally {
      setLoadingRoles(false);
    }
  };

  useEffect(() => {
    fetchRoles(); // Fetch roles when component mounts
  }, []);

  // Handle opening the create modal
  const handleOpenCreateModal = () => {
    if (!permissions.roles_and_access_user_create) {
      toast.warning("You don't have permission to create users");
      return;
    }
    // Clear search input to prevent autocomplete issues
    setSearchQuery('');
    
    // Reset form data first
    setFormData({
      name: '',
      email: '',
      phone: '',
      countryCode: '+91',
      password: '',
      role: '',
      profilePhoto: null,
      profilePhotoPreview: null
    });
    setFormErrors({});
    setActionType('Create'); // Add this line
    setShowCreateModal(true);
  };

  // Handle closing the create modal
  const handleCloseCreateModal = () => {
    setShowCreateModal(false);
    handleRemovePhoto();
    setFormData({
      name: '',
      email: '',
      phone: '',
      countryCode: '+91',
      password: '',
      role: '',
      profilePhoto: null,
      profilePhotoPreview: null
    });
    setFormErrors({});
  };

  // Handle opening the edit modal
  const handleOpenEditModal = async (user) => {
    if (!permissions.roles_and_access_user_edit) {
      toast.warning("You don't have permission to edit users");
      return;
    }
    // Clear search input to prevent autocomplete issues
    setSearchQuery('');
    
    // Fetch latest roles before opening modal
    setLoadingRoles(true);
    try {
      const response = await getRolesAndPermissions({
        page: 1,
        limit: 100,
        state: 'active',
        search: ''
      });

      if (response.success) {
        const activeRoles = response.data.rolesWithPermissions.filter(role => role.is_active === 1);
        setRoles(activeRoles);
      }
    } catch (error) {
      console.error('Error fetching roles:', error);
      toast.error('Failed to fetch roles');
      return; // Don't open modal if roles fetch fails
    } finally {
      setLoadingRoles(false);
    }
    
    setSelectedUser(user);
    setActionType('Edit');
    // Find the role ID based on role name
    const userRole = roles.find(role => role.role_name.toLowerCase() === user.role_name.toLowerCase());

    if (!userRole) {
      console.warn(`Role not found for role_name: ${user.role_name}`);
    }

    // Populate form data with user information
    setFormData({
      name: user.name || '',
      email: user.email || '',
      phone: user.mobile || '',
      countryCode: user.mobileno_code || '+91',
      password: '', // Don't populate password for security
      role: userRole?.role_id || '',
      profilePhoto: null,
      profilePhotoPreview: user.profile_pic_url || null
    });
    setFormErrors({});
    setShowEditModal(true);
  };

  // Handle closing the edit modal
  const handleCloseEditModal = () => {
    setShowEditModal(false);
    setSelectedUser(null);
    setActionType('Create'); // Add this line
    handleRemovePhoto();
    setFormData({
      name: '',
      email: '',
      phone: '',
      countryCode: '+91',
      password: '',
      role: '',
      profilePhoto: null,
      profilePhotoPreview: null
    });
    setFormErrors({});
  };

  const handleSearchChange = (e) => {
    const value = e.target.value;
    setSearchQuery(value);
    setCurrentPage(1);
  };

  const handleStatusFilterChange = (e) => {
    setStatusFilter(e.target.value);
  };

  const handlePageChange = (newPage) => {
    setCurrentPage(newPage);
  };

  const handleLimitChange = (e) => {
    setItemsPerPage(parseInt(e.target.value));
    setCurrentPage(1);
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;

    // Map form field names to state properties
    const fieldMap = {
      'userName': 'name',
      'userEmail': 'email',
      'userPhone': 'phone',
      'userPassword': 'password',
      'countryCode': 'countryCode',
      'role': 'role'
    };

    const stateField = fieldMap[name] || name;

    setFormData(prev => ({
      ...prev,
      [stateField]: value
    }));

    // Clear error when user starts typing
    setFormErrors(prev => ({
      ...prev,
      [stateField]: ''
    }));
  };

  const handleProfilePhotoUpload = (e) => {
    const file = e.target.files[0];
    if (file) {
      if (file.size > 5 * 1024 * 1024) { // 5MB
        setFormErrors(prev => ({
          ...prev,
          profilePhoto: 'File size should not exceed 5MB'
        }));
        return;
      }

      if (!['image/jpeg', 'image/png', 'image/jpg'].includes(file.type)) {
        setFormErrors(prev => ({
          ...prev,
          profilePhoto: 'Please upload a valid image file (JPG, JPEG, PNG)'
        }));
        return;
      }

      setFormData(prev => ({
        ...prev,
        profilePhoto: file,
        profilePhotoPreview: URL.createObjectURL(file)
      }));
      setFormErrors(prev => ({
        ...prev,
        profilePhoto: ''
      }));
    }
  };

  const handleRemovePhoto = () => {
    if (formData.profilePhotoPreview) {
      URL.revokeObjectURL(formData.profilePhotoPreview);
    }
    setFormData(prev => ({
      ...prev,
      profilePhoto: null,
      profilePhotoPreview: null
    }));
  };

  // Password validation helper function
  const validatePassword = (password) => {
    const errors = [];

    // Check minimum length
    if (password.length < 8) {
      errors.push('Password must be at least 8 characters long');
    }

    // Check for letters
    if (!/[a-zA-Z]/.test(password)) {
      errors.push('Password must contain at least one letter');
    }

    // Check for numbers
    if (!/\d/.test(password)) {
      errors.push('Password must contain at least one number');
    }

    // Check for special characters
    if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
      errors.push('Password must contain at least one special character');
    }

    return errors;
  };

  const handleCreateUser = async () => {
    // Validate form
    const errors = {};
    if (!formData.name.trim()) {
      errors.name = 'Name is required';
    }
    if (!formData.email.trim()) {
      errors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      errors.email = 'Invalid email format';
    }
    if (formData.phone && !/^\d+$/.test(formData.phone.trim())) {
      errors.phone = 'Phone number should contain only digits';
    }
    if (!formData.role) {
      errors.role = 'Role is required';
    }

    // Enhanced password validation for Create
    if (!formData.password) {
      errors.password = 'Password is required';
    } else {
      const passwordErrors = validatePassword(formData.password);
      if (passwordErrors.length > 0) {
        errors.password = passwordErrors.join('. ');
      }
    }

    if (Object.keys(errors).length > 0) {
      setFormErrors(errors);
      return;
    }

    setIsSubmitting(true);
    try {
      const formDataToSend = new FormData();
      formDataToSend.append('name', formData.name.trim());
      formDataToSend.append('email', formData.email.trim());
      formDataToSend.append('mobile', formData.phone.trim());
      formDataToSend.append('mobileno_code', formData.countryCode);
      formDataToSend.append('password', formData.password);
      formDataToSend.append('role_id', formData.role);
      formDataToSend.append('notify_by_email', 'true');

      // Handle profile picture
      if (formData.profilePhoto) {
        formDataToSend.append('profile_pic', formData.profilePhoto);
      } else {
        formDataToSend.append('profile_pic', null);
      }

      const response = await createAdminUser(formDataToSend);

      if (response.success) {
        toast.success(response.message || 'User created successfully');
        // Refresh the users list
        fetchUsers();
        // Close modal and reset form
        setShowCreateModal(false);
        setFormData({
          name: '',
          email: '',
          phone: '',
          countryCode: '+91',
          password: '',
          role: '',
          profilePhoto: null,
          profilePhotoPreview: null
        });
        setFormErrors({});
      } else {
        toast.error(response.message || 'Failed to create user');
      }
    } catch (error) {
      console.error('Error creating user:', error);
      toast.error(error.response?.data?.message || 'Error creating user');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDeleteClick = (user) => {
    if (!permissions.roles_and_access_user_delete) {
      toast.warning("You don't have permission to delete users");
      return;
    }
    setSelectedUser(user);
    setShowDeleteModal(true);
  };

  const handleDeleteConfirm = async () => {
    if (!selectedUser) return;

    setIsDeleting(true);
    try {
      const response = await deleteUser(selectedUser.id);
      if (response.success) {
        // Refresh the users list
        fetchUsers();
        // Show success message if needed
      }
    } catch (error) {
      console.error('Error deleting user:', error);
      // Handle error if needed
    } finally {
      setIsDeleting(false);
      setShowDeleteModal(false);
      setSelectedUser(null);
    }
  };

  // Add this new function to handle direct status toggle
  const handleStatusToggle = async (user) => {
    if (!permissions.roles_and_access_user_status) {
      toast.warning("You don't have permission to change user status");
      return;
    }
    setTogglingStatusId(user.id);
    try {
      const newStatus = user.is_blocked === 0 ? 1 : 0;
      const response = await toggleUserStatus(user.id, newStatus);
      if (response.success) {
        // Update user status locally
        setUsers(prevUsers => prevUsers.map(u =>
          u.id === user.id ? { ...u, is_blocked: newStatus } : u
        ));
        // Show success notification
        toast.success(`User ${newStatus === 0 ? 'unblocked' : 'blocked'} successfully`);
      }
    } catch (error) {
      console.error('Error toggling user status:', error);
      toast.error('Failed to update user status');
    } finally {
      setTogglingStatusId(null);
    }
  };

  // Handle edit user submit
  const handleEditUser = async () => {
    // Validate form (similar to create but password is optional)
    const errors = {};
    if (!formData.name.trim()) {
      errors.name = 'Name is required';
    }
    if (!formData.email.trim()) {
      errors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      errors.email = 'Invalid email format';
    }
    if (formData.phone && !/^\d+$/.test(formData.phone.trim())) {
      errors.phone = 'Phone number should contain only digits';
    }
    if (!formData.role) {
      errors.role = 'Role is required';
    }

    // Password validation (only if password is provided in edit mode)
    if (formData.password && formData.password.trim() !== '') {
      const passwordErrors = validatePassword(formData.password);
      if (passwordErrors.length > 0) {
        errors.password = passwordErrors.join('. ');
      }
    }

    if (Object.keys(errors).length > 0) {
      setFormErrors(errors);
      return;
    }

    setIsEditing(true);
    try {
      const formDataToSend = new FormData();
      formDataToSend.append('name', formData.name.trim());
      formDataToSend.append('email', formData.email.trim());
      formDataToSend.append('mobile', formData.phone.trim());
      formDataToSend.append('role_id', formData.role);
      formDataToSend.append('mobileno_code', formData.countryCode || '+65');

      // loop through formDataToSend and log the key and value
      for (const [key, value] of formDataToSend.entries()) {
        console.log("key", key);
        console.log(`${key}: ${value}`);
      }

      // Only append password if it's provided and passes validation
      if (formData.password && formData.password.trim() !== '') {
        formDataToSend.append('password', formData.password.trim());
      }

      // Handle profile picture
      if (formData.profilePhoto) {
        formDataToSend.append('profile_pic', formData.profilePhoto);
      }

      const response = await editOrganisationUser(formDataToSend, selectedUser.id);

      if (response.success) {
        toast.success(response.message || 'User updated successfully');
        // Refresh the users list
        fetchUsers();
        // Close modal and reset form
        handleCloseEditModal();
      } else {
        console.error("Failed to edit user:", response.message);
        toast.error(`Failed to edit user: ${response.message}`);
      }

    } catch (error) {
      console.error('Error editing user:', error);
      toast.error(error.response?.data?.message || 'Error updating user');
    } finally {
      setIsEditing(false);
    }
  };

  const getFormattedMobileNumber = (mobile, mobileCode) => {
    if (!mobile) return '--';
    return mobileCode ? `${mobileCode} ${mobile}` : mobile;
  };

  // Add click outside handler to close country dropdown
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (showCountryDropdown && !event.target.closest('.position-relative')) {
        setShowCountryDropdown(false);
      }
    };

    document.addEventListener('click', handleClickOutside);
    return () => {
      document.removeEventListener('click', handleClickOutside);
    };
  }, [showCountryDropdown]);

  // Clear search when modals are closed to prevent autocomplete issues
  useEffect(() => {
    if (!showCreateModal && !showEditModal) {
      // Small delay to ensure modal is fully closed
      const timer = setTimeout(() => {
        const searchInput = document.getElementById('userSearchInput');
        if (searchInput && searchInput.value !== searchQuery) {
          searchInput.value = searchQuery;
        }
      }, 100);
      return () => clearTimeout(timer);
    }
  }, [showCreateModal, showEditModal, searchQuery]);


  return (
    <>
      {/* Search and Filter Section */}
      <div className="row d-flex mb-3">
        <div className="col-md-6 mt-2 d-flex justify-content-start align-items-center">
          <input
            type="search"
            className="form-control"
            placeholder="Search users..."
            style={{ maxWidth: '300px' }}
            value={searchQuery}
            onChange={handleSearchChange}
            autoComplete="off"
            autoCorrect="off"
            autoCapitalize="off"
            spellCheck="false"
            name="userSearch"
            id="userSearchInput"
            data-form-type="other"
          />
        </div>
        <div className="col-md-6 text-end mt-2 d-flex justify-content-end align-items-center gap-2">
          {/* <select
            className="form-select"
            style={{ width: '180px' }}
            value={statusFilter}
            onChange={handleStatusFilterChange}
          >
            <option value="all">All Status</option>
            <option value="active">Active</option>
            <option value="blocked">Blocked</option>
          </select> */}
          <button
            className="btn btn-primary text-nowrap"
            style={{ width: '150px' }}
            onClick={handleOpenCreateModal}
            disabled={!permissions.roles_and_access_user_create}
          >
            <Icon icon="fluent:add-24-regular" className="me-2" width="20" height="20" />
            Create User
          </button>
        </div>
      </div>

      {/* Users Table */}
      <div className="row mb-3">
        <div className="col-12">
          <div className="card">
            <div className="table-responsive">
              {loading ? (
                <Loader
                  caption="Loading Users..."
                  minLoadTime={500}
                  containerHeight="400px"
                />
              ) : (
                <table className="table table-borderless mb-0">
                  <thead>
                    <tr>
                      <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500' }}>SL. NO.</th>
                      <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500' }}>User Details</th>
                      <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500' }}>Role</th>
                      <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500' }}>Mobile</th>
                      <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500' }}>Status</th>
                      <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500' }}>Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {users.length > 0 ? users.map((user, index) => (
                      <tr key={user.id} className="border-bottom">
                        <td className="py-3 align-middle">{(currentPage - 1) * itemsPerPage + index + 1}</td>
                        <td className="py-3">
                          <div className="d-flex align-items-center">
                            {user.profile_pic_url ? (
                              <img
                                src={user.profile_pic_url}
                                alt={user.name}
                                className="rounded-circle me-3"
                                style={{ width: '40px', height: '40px', objectFit: 'cover' }}
                              />
                            ) : (
                              <div className="rounded-circle bg-secondary-subtle d-flex align-items-center justify-content-center me-3"
                                style={{ width: '40px', height: '40px' }}>
                                <Icon icon="fluent:person-24-regular" width="20" height="20" />
                              </div>
                            )}
                            <div>
                              <div className="fw-medium">{user.name}</div>
                              <div className="text-muted small">{user.email}</div>
                            </div>
                          </div>
                        </td>
                        <td className="py-3 align-middle">
                          <span className="badge bg-primary-subtle text-primary">
                            {user.role_name}
                          </span>
                        </td>
                        <td className="py-3 align-middle">
                          {getFormattedMobileNumber(user.mobile, user.mobileno_code)}
                        </td>
                        <td className="py-3 align-middle">
                          <span className={`badge border ${user.is_blocked === 0 ? 'border-success text-success' : 'border-danger text-danger'} bg-white d-inline-flex align-items-center gap-1 p-2`}>
                            <Icon
                              icon={user.is_blocked === 0 ? 'fluent:presence-available-16-regular' : 'fluent:presence-blocked-16-regular'}
                              width="12"
                              height="12"
                            />
                            {user.is_blocked === 0 ? 'Active' : 'Blocked'}
                          </span>
                        </td>
                        <td className="py-3 align-middle">
                          <div className="d-flex gap-2">
                            <button
                              className="btn btn-outline-dark btn-sm border border-dark"
                              style={{ width: '36px', height: '36px' }}
                              title="Edit User"
                              onClick={() => handleOpenEditModal(user)}
                              disabled={!permissions.roles_and_access_user_edit}
                            >
                              <Icon icon="fluent:edit-24-regular" width="16" height="16" />
                            </button>
                            <button
                              className="btn btn-outline-dark btn-sm border border-dark"
                              style={{ width: '36px', height: '36px' }}
                              title="Delete User"
                              onClick={() => handleDeleteClick(user)}
                              disabled={!permissions.roles_and_access_user_delete}
                            >
                              <Icon icon="fluent:delete-24-regular" width="16" height="16" />
                            </button>
                            <button
                              className={`btn ${user.is_blocked === 0 ? "btn-success" : "btn-outline-warning"} btn-sm border`}
                              style={{ width: '36px', height: '36px' }}
                              title={`${user.is_blocked === 0 ? 'Block' : 'Unblock'} User`}
                              onClick={() => handleStatusToggle(user)}
                              disabled={togglingStatusId === user.id || !permissions.roles_and_access_user_status}
                            >
                              {togglingStatusId === user.id ? (
                                <span className="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                              ) : (
                                <Icon
                                  icon={user.is_blocked === 0 ?
                                    "fluent:checkmark-circle-24-regular" :
                                    "fluent:dismiss-circle-24-regular"
                                  }
                                  width="16"
                                  height="16"
                                />
                              )}
                            </button>
                          </div>
                        </td>
                      </tr>
                    )) : (
                      <tr>
                        <td colSpan="6" className="text-center" style={{ height: '400px', verticalAlign: 'middle' }}>
                          <NoData caption='No users found' />
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Pagination Section */}
      <div className="row">
        <div className="col-md-6 d-flex align-items-center gap-3">
          <select
            className="form-select"
            style={{ width: 'auto' }}
            value={itemsPerPage}
            onChange={handleLimitChange}
          >
            <option value={10}>10 records per page</option>
            <option value={25}>25 records per page</option>
            <option value={50}>50 records per page</option>
          </select>
          <span className="text-muted">
            Total Records: {totalUsers}
          </span>
        </div>
        <div className="col-md-6 d-flex justify-content-end align-items-center gap-3">
          <button
            className="btn btn-primary"
            style={{ width: '150px' }}
            onClick={() => handlePageChange(currentPage - 1)}
            disabled={currentPage === 1}
          >
            Prev
          </button>
          <span>Page {currentPage} of {totalPages}</span>
          <button
            className="btn btn-primary"
            style={{ width: '150px' }}
            onClick={() => handlePageChange(currentPage + 1)}
            disabled={currentPage === totalPages}
          >
            Next
          </button>
        </div>
      </div>

      {/* Create User Modal */}
      {showCreateModal && (
        <div className="modal show d-block" style={{ backgroundColor: 'rgba(0,0,0,0.5)' }}>
          <div className="modal-dialog modal-dialog-centered">
            <div className="modal-content">
              <div className="modal-header">
                <h5 className="modal-title">Add User</h5>
                <button
                  type="button"
                  className="btn-close"
                  onClick={handleCloseCreateModal}
                ></button>
              </div>
              <div className="modal-body">
                {/* Profile Photo Upload */}
                <div className="text-center mb-4">
                  <div
                    className="position-relative d-inline-block"
                    style={{ cursor: 'pointer' }}
                    onClick={() => document.getElementById('profilePhotoUpload').click()}
                  >
                    {formData.profilePhotoPreview ? (
                      <>
                        <img
                          src={formData.profilePhotoPreview}
                          alt="Profile Preview"
                          className="rounded-circle"
                          style={{ width: '120px', height: '120px', objectFit: 'cover' }}
                        />
                        <button
                          className="btn btn-sm btn-link position-absolute top-0 end-0 text-danger p-0"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleRemovePhoto();
                          }}
                          style={{
                            background: 'white',
                            borderRadius: '50%',
                            width: '24px',
                            height: '24px',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center'
                          }}
                        >
                          <Icon icon="fluent:dismiss-24-filled" width="16" height="16" />
                        </button>
                      </>
                    ) : (
                      <div
                        className="rounded-circle bg-light d-flex align-items-center justify-content-center"
                        style={{ width: '120px', height: '120px', border: '2px dashed #ccc' }}
                      >
                        <div className="text-center">
                          <Icon icon="fluent:person-24-regular" width="40" height="40" className="text-muted" />
                          <div className="small text-muted mt-1">Upload Photo</div>
                        </div>
                      </div>
                    )}
                  </div>
                  <input
                    type="file"
                    id="profilePhotoUpload"
                    className="d-none"
                    accept="image/jpeg,image/png,image/jpg"
                    onChange={handleProfilePhotoUpload}
                  />
                  <div className="small text-muted mt-2">Click to upload profile photo</div>
                  {formErrors.profilePhoto && (
                    <div className="text-danger small mt-1">{formErrors.profilePhoto}</div>
                  )}
                </div>

                {/* Name */}
                <div className="mb-3">
                  <label className="form-label">Full Name*</label>
                  <input
                    type="text"
                    className={`form-control ${formErrors.name ? 'is-invalid' : ''}`}
                    name="userName"
                    value={formData.name}
                    onChange={handleInputChange}
                    placeholder="Enter full name"
                    autoComplete="off"
                  />
                  {formErrors.name && (
                    <div className="invalid-feedback">{formErrors.name}</div>
                  )}
                </div>

                {/* Email */}
                <div className="mb-3">
                  <label className="form-label">Email Address*</label>
                  <input
                    type="email"
                    className={`form-control ${formErrors.email ? 'is-invalid' : ''}`}
                    name="userEmail"
                    value={formData.email}
                    onChange={handleInputChange}
                    placeholder="Enter email address"
                    autoComplete="off"
                  />
                  {formErrors.email && (
                    <div className="invalid-feedback">{formErrors.email}</div>
                  )}
                </div>

                {/* Phone */}
                <div className="mb-3">
                  <label className="form-label">Phone Number</label>
                  <div className="row g-2">
                    <div className="col-md-4">
                      <select
                        className="form-select bg-light"
                        name="countryCode"
                        value={formData.countryCode}
                        onChange={handleInputChange}
                      >
                        {countryData.countoryCode.map((country, index) => (
                          <option key={index} value={country.code}>
                            {country.country}
                          </option>
                        ))}
                      </select>
                    </div>
                    <div className="col-md-8">
                      <input
                        type="tel"
                        className={`form-control ${formErrors.phone ? 'is-invalid' : ''}`}
                        name="userPhone"
                        value={formData.phone}
                        onChange={handleInputChange}
                        placeholder="Enter phone number"
                        autoComplete="off"
                      />
                      {formErrors.phone && (
                        <div className="invalid-feedback">{formErrors.phone}</div>
                      )}
                    </div>
                  </div>
                  <div className="text-muted mt-1" style={{ fontSize: '0.875rem' }}>
                    Phone number must contain only digits
                  </div>
                </div>

                {/* Password Field */}
                <PasswordInput
                  id="userPassword"
                  label="Password*"
                  value={formData.password}
                  onChange={handleInputChange}
                  isRequired={true}
                  error={formErrors.password}
                  showPassword={showPassword}
                  setShowPassword={setShowPassword}
                />

                <div className="form-text text-muted mt-2">
                  <div>Password Requirements:</div>
                  <ul className="mb-0 ps-3 mt-1">
                    <li className={/^.{8,}$/.test(formData.password) ? 'text-success' : ''}>
                      <Icon
                        icon={/^.{8,}$/.test(formData.password) ? "mdi:check-circle" : "mdi:circle-outline"}
                        className="me-1"
                        width="14"
                      />
                      At least 8 characters
                    </li>
                    <li className={/[a-zA-Z]/.test(formData.password) ? 'text-success' : ''}>
                      <Icon
                        icon={/[a-zA-Z]/.test(formData.password) ? "mdi:check-circle" : "mdi:circle-outline"}
                        className="me-1"
                        width="14"
                      />
                      At least one letter
                    </li>
                    <li className={/\d/.test(formData.password) ? 'text-success' : ''}>
                      <Icon
                        icon={/\d/.test(formData.password) ? "mdi:check-circle" : "mdi:circle-outline"}
                        className="me-1"
                        width="14"
                      />
                      At least one number
                    </li>
                    <li className={/[!@#$%^&*(),.?":{}|<>]/.test(formData.password) ? 'text-success' : ''}>
                      <Icon
                        icon={/[!@#$%^&*(),.?":{}|<>]/.test(formData.password) ? "mdi:check-circle" : "mdi:circle-outline"}
                        className="me-1"
                        width="14"
                      />
                      At least one special character (!@#$%^&*(),.?":{ }|&lt;&gt;)
                    </li>
                  </ul>
                </div>

                {/* Role Selection */}
                <div className="mb-3">
                  <label className="form-label">Select Role*</label>
                  <select
                    className={`form-select ${formErrors.role ? 'is-invalid' : ''}`}
                    name="role"
                    value={formData.role}
                    onChange={handleInputChange}
                    disabled={loadingRoles}
                  >
                    <option value="">Choose role...</option>
                    {loadingRoles ? (
                      <option value="" disabled>Loading roles...</option>
                    ) : (
                      roles.map(role => (
                        <option key={role.role_id} value={role.role_id}>
                          {role.role_name}
                        </option>
                      ))
                    )}
                  </select>
                  {formErrors.role && (
                    <div className="invalid-feedback">{formErrors.role}</div>
                  )}
                </div>
              </div>
              <div className="modal-footer">
                <button
                  type="button"
                  className="btn btn-secondary"
                  onClick={handleCloseCreateModal}
                  disabled={isSubmitting}
                >
                  Cancel
                </button>
                <button
                  type="button"
                  className="btn btn-primary d-flex align-items-center gap-2"
                  onClick={handleCreateUser}
                  disabled={isSubmitting}
                >
                  {isSubmitting ? (
                    <>
                      <span className="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                      Creating...
                    </>
                  ) : (
                    'Create User'
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Edit User Modal */}
      {showEditModal && selectedUser && (
        <div className="modal show d-block" style={{ backgroundColor: 'rgba(0,0,0,0.5)' }}>
          <div className="modal-dialog modal-dialog-centered">
            <div className="modal-content">
              <div className="modal-header">
                <h5 className="modal-title">Edit User - {selectedUser.name}</h5>
                <button
                  type="button"
                  className="btn-close"
                  onClick={handleCloseEditModal}
                ></button>
              </div>
              <div className="modal-body">
                {/* Profile Photo Upload */}
                <div className="text-center mb-4">
                  <div
                    className="position-relative d-inline-block"
                    style={{ cursor: 'pointer' }}
                    onClick={() => document.getElementById('editProfilePhotoUpload').click()}
                  >
                    {formData.profilePhotoPreview ? (
                      <>
                        <img
                          src={formData.profilePhotoPreview}
                          alt="Profile Preview"
                          className="rounded-circle"
                          style={{ width: '120px', height: '120px', objectFit: 'cover' }}
                        />
                        <button
                          className="btn btn-sm btn-link position-absolute top-0 end-0 text-danger p-0"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleRemovePhoto();
                          }}
                          style={{
                            background: 'white',
                            borderRadius: '50%',
                            width: '24px',
                            height: '24px',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center'
                          }}
                        >
                          <Icon icon="fluent:dismiss-24-filled" width="16" height="16" />
                        </button>
                      </>
                    ) : (
                      <div
                        className="rounded-circle bg-light d-flex align-items-center justify-content-center"
                        style={{ width: '120px', height: '120px', border: '2px dashed #ccc' }}
                      >
                        <div className="text-center">
                          <Icon icon="fluent:person-24-regular" width="40" height="40" className="text-muted" />
                          <div className="small text-muted mt-1">Upload Photo</div>
                        </div>
                      </div>
                    )}
                  </div>
                  <input
                    type="file"
                    id="editProfilePhotoUpload"
                    className="d-none"
                    accept="image/jpeg,image/png,image/jpg"
                    onChange={handleProfilePhotoUpload}
                  />
                  <div className="small text-muted mt-2">Click to upload profile photo</div>
                  {formErrors.profilePhoto && (
                    <div className="text-danger small mt-1">{formErrors.profilePhoto}</div>
                  )}
                </div>

                {/* Name */}
                <div className="mb-3">
                  <label className="form-label">Full Name*</label>
                  <input
                    type="text"
                    className={`form-control ${formErrors.name ? 'is-invalid' : ''}`}
                    name="userName"
                    value={formData.name}
                    onChange={handleInputChange}
                    placeholder="Enter full name"
                    autoComplete="off"
                  />
                  {formErrors.name && (
                    <div className="invalid-feedback">{formErrors.name}</div>
                  )}
                </div>

                {/* Email */}
                <div className="mb-3">
                  <label className="form-label">Email Address*</label>
                  <input
                    type="email"
                    className={`form-control ${formErrors.email ? 'is-invalid' : ''}`}
                    name="userEmail"
                    value={formData.email}
                    onChange={handleInputChange}
                    placeholder="Enter email address"
                    autoComplete="off"
                    readOnly
                    style={{ backgroundColor: '#e9ecef', cursor: 'not-allowed' }}
                  />
                  {formErrors.email && (
                    <div className="invalid-feedback">{formErrors.email}</div>
                  )}
                  <div className="text-muted mt-1" style={{ fontSize: '0.875rem' }}>
                    Email cannot be edited
                  </div>
                </div>

                {/* Phone */}
                <div className="mb-3">
                  <label className="form-label">Phone Number</label>
                  <div className="row g-2">
                    <div className="col-md-4">
                      <select
                        className="form-select bg-light"
                        name="countryCode"
                        value={formData.countryCode}
                        onChange={handleInputChange}
                      >
                        {countryData.countoryCode.map((country, index) => (
                          <option key={index} value={country.code}>
                            {country.country}
                          </option>
                        ))}
                      </select>
                    </div>
                    <div className="col-md-8">
                      <input
                        type="tel"
                        className={`form-control ${formErrors.phone ? 'is-invalid' : ''}`}
                        name="userPhone"
                        value={formData.phone}
                        onChange={handleInputChange}
                        placeholder="Enter phone number"
                        autoComplete="off"
                      />
                      {formErrors.phone && (
                        <div className="invalid-feedback">{formErrors.phone}</div>
                      )}
                    </div>
                  </div>
                  <div className="text-muted mt-1" style={{ fontSize: '0.875rem' }}>
                    Phone number must contain only digits
                  </div>
                </div>

                {/* Password Field */}
                <PasswordInput
                  id="editUserPassword"
                  label="Password (Optional)"
                  value={formData.password}
                  onChange={handleInputChange}
                  isRequired={false}
                  placeholder="Leave blank to keep current password"
                  error={formErrors.password}
                  showPassword={showPassword}
                  setShowPassword={setShowPassword}
                />

                {/* Show password requirements only when user starts typing */}
                {formData.password && (
                  <div className="form-text text-muted mt-2">
                    <div>Password Requirements:</div>
                    <ul className="mb-0 ps-3 mt-1">
                      <li className={/^.{8,}$/.test(formData.password) ? 'text-success' : ''}>
                        <Icon
                          icon={/^.{8,}$/.test(formData.password) ? "mdi:check-circle" : "mdi:circle-outline"}
                          className="me-1"
                          width="14"
                        />
                        At least 8 characters
                      </li>
                      <li className={/[a-zA-Z]/.test(formData.password) ? 'text-success' : ''}>
                        <Icon
                          icon={/[a-zA-Z]/.test(formData.password) ? "mdi:check-circle" : "mdi:circle-outline"}
                          className="me-1"
                          width="14"
                        />
                        At least one letter
                      </li>
                      <li className={/\d/.test(formData.password) ? 'text-success' : ''}>
                        <Icon
                          icon={/\d/.test(formData.password) ? "mdi:check-circle" : "mdi:circle-outline"}
                          className="me-1"
                          width="14"
                        />
                        At least one number
                      </li>
                      <li className={/[!@#$%^&*(),.?":{}|<>]/.test(formData.password) ? 'text-success' : ''}>
                        <Icon
                          icon={/[!@#$%^&*(),.?":{}|<>]/.test(formData.password) ? "mdi:check-circle" : "mdi:circle-outline"}
                          className="me-1"
                          width="14"
                        />
                        At least one special character (!@#$%^&*(),.?":{ }|&lt;&gt;)
                      </li>
                    </ul>
                  </div>
                )}

                {/* Role Selection */}
                <div className="mb-3">
                  <label className="form-label">Select Role*</label>
                  <select
                    className={`form-select ${formErrors.role ? 'is-invalid' : ''}`}
                    name="role"
                    value={formData.role}
                    onChange={handleInputChange}
                    disabled={loadingRoles}
                  >
                    {loadingRoles ? (
                      <option value="" disabled>Loading roles...</option>
                    ) : (
                      <>
                        <option value="">Select role</option>
                        {roles.map(role => (
                          <option key={role.role_id} value={role.role_id} selected={role.role_name.toLowerCase() === selectedUser?.role_name?.toLowerCase()}>
                            {role.role_name}
                          </option>
                        ))}
                      </>
                    )}
                  </select>
                  {formErrors.role && (
                    <div className="invalid-feedback">{formErrors.role}</div>
                  )}
                </div>
              </div>
              <div className="modal-footer">
                <button
                  type="button"
                  className="btn btn-secondary"
                  onClick={handleCloseEditModal}
                  disabled={isEditing}
                >
                  Cancel
                </button>
                <button
                  type="button"
                  className="btn btn-primary d-flex align-items-center gap-2"
                  onClick={handleEditUser}
                  disabled={isEditing}
                >
                  {isEditing ? (
                    <>
                      <span className="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                      Updating...
                    </>
                  ) : (
                    'Update User'
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Add Delete Confirmation Modal */}
      {showDeleteModal && selectedUser && (
        <div className="modal show d-block" style={{ backgroundColor: 'rgba(0,0,0,0.5)' }}>
          <div className="modal-dialog modal-dialog-centered">
            <div className="modal-content">
              <div className="modal-header">
                <h5 className="modal-title">Confirm Delete</h5>
                <button
                  type="button"
                  className="btn-close"
                  onClick={() => {
                    setShowDeleteModal(false);
                    setSelectedUser(null);
                  }}
                  disabled={isDeleting}
                ></button>
              </div>
              <div className="modal-body">
                <p>Are you sure you want to delete the user <strong>{selectedUser.name}</strong>?</p>
                <p className="text-danger mb-0">This action cannot be undone.</p>
              </div>
              <div className="modal-footer">
                <button
                  type="button"
                  className="btn btn-secondary"
                  onClick={() => {
                    setShowDeleteModal(false);
                    setSelectedUser(null);
                  }}
                  disabled={isDeleting}
                >
                  Cancel
                </button>
                <button
                  type="button"
                  className="btn btn-danger d-flex align-items-center gap-2"
                  onClick={handleDeleteConfirm}
                  disabled={isDeleting}
                >
                  {isDeleting ? (
                    <>
                      <span className="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                      Deleting...
                    </>
                  ) : (
                    'Delete User'
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
}

export default Users;
