.account-info-container {
  padding: 0;
}

.section-header {
  margin-bottom: 24px;
}

.section-header h3 {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.section-header p {
  font-size: 14px;
  color: #666;
  margin: 0;
}

/* Settings Sections */
.settings-section {
  margin-bottom: 32px;
}

.settings-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 16px;
}

/* Color Options */
.color-options-container {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-bottom: 16px;
}

.color-option {
  width: 56px;
  height: 56px;
  border-radius: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
  position: relative;
}

.color-option:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.color-option.selected {
  border: 2px solid #3152e8;
}

.color-selected-icon {
  color: #333;
  font-size: 24px;
}

/* Color classes */
.color-white {
  background-color: #ffffff;
  border: 1px solid #e0e0e0;
}

.color-beige {
  background-color: #e8d0b0;
}

.color-pink {
  background-color: #ffd4e5;
}

.color-peach {
  background-color: #f8d0b0;
}

.color-lavender-pink {
  background-color: #e8b0d5;
}

.color-sky-blue {
  background-color: #90c4f8;
}

.color-mint {
  background-color: #b0e8e0;
}

.color-light-purple {
  background-color: #d0b0e8;
}

.color-light-green {
  background-color: #c0d0c0;
}

/* Appearance Options */
.appearance-options {
  display: flex;
  gap: 24px;
}

.appearance-option {
  width: 180px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.appearance-option:hover {
  transform: translateY(-2px);
}

.appearance-option.selected .appearance-preview {
  border: 2px solid #3152e8;
}

.appearance-preview {
  width: 100%;
  height: 120px;
  border-radius: 8px;
  overflow: hidden;
  border: 2px solid transparent;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  margin-bottom: 8px;
}

.light-preview {
  background-color: #f6f7f9;
}

.dark-preview {
  background-color: #1a1a1a;
}

.preview-header {
  height: 20px;
  background-color: #ffffff;
  border-bottom: 1px solid #e0e0e0;
}

.dark-preview .preview-header {
  background-color: #2a2a2a;
  border-bottom: 1px solid #3a3a3a;
}

.preview-sidebar {
  width: 30px;
  height: 100%;
  background-color: #ffffff;
  border-right: 1px solid #e0e0e0;
  float: left;
}

.dark-preview .preview-sidebar {
  background-color: #2a2a2a;
  border-right: 1px solid #3a3a3a;
}

.preview-content {
  flex: 1;
  padding: 8px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.preview-card {
  height: 20px;
  background-color: #ffffff;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.dark-preview .preview-card {
  background-color: #3a3a3a;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.appearance-label {
  font-size: 14px;
  font-weight: 500;
  text-align: center;
  color: #333;
}

/* Security Options */
.security-options {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.security-option {
  display: flex;
  gap: 16px;
  padding: 16px;
  background-color: #f9f9f9;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.security-icon {
  color: #3152e8;
  display: flex;
  align-items: flex-start;
}

.security-details {
  flex: 1;
}

.security-details h5 {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.security-details p {
  font-size: 14px;
  color: #666;
  margin-bottom: 0;
}

/* Button Styles */
.btn {
  display: inline-flex;
  align-items: center;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.btn-outline-primary {
  color: #3152e8;
  border-color: #3152e8;
}

.btn-outline-primary:hover {
  background-color: #3152e8;
  color: white;
}

/* Form Switch */
.form-check-input:checked {
  background-color: #3152e8;
  border-color: #3152e8;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .appearance-options {
    flex-direction: column;
    gap: 16px;
  }
  
  .appearance-option {
    width: 100%;
    max-width: 240px;
  }
  
  .security-option {
    flex-direction: column;
    gap: 12px;
  }
}
