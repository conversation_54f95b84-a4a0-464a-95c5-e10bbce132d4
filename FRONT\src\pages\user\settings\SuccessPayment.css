.success-payment-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  background-color: #f8f9fa;
}

.success-payment-card {
  background: white;
  padding: 40px;
  border-radius: 16px;
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.08);
  width: 100%;
  max-width: 480px;
  text-align: center;
}

.success-icon-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 96px;
  height: 96px;
  margin: 0 auto;
  border-radius: 50%;
  background: rgba(40, 199, 111, 0.1);
}

.success-title {
  font-size: 28px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 8px;
}

.success-subtitle {
  color: #6c757d;
  font-size: 16px;
  margin-bottom: 24px;
}

.amount-display {
  background: rgba(40, 199, 111, 0.05);
  padding: 16px;
  border-radius: 12px;
}

.amount-value {
  font-size: 36px;
  font-weight: 700;
  color: #28c76f;
}

.transaction-details {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 20px;
  margin: 24px 0;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #e9ecef;
}

.detail-row:last-child {
  border-bottom: none;
}

.detail-label {
  color: #6c757d;
  font-size: 14px;
}

.detail-value {
  font-weight: 500;
  color: #2c3e50;
}

.status-success {
  color: #28c76f;
  display: flex;
  align-items: center;
  gap: 4px;
}

.action-buttons {
  margin-top: 32px;
}

.action-buttons .btn {
  padding: 12px 24px;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

/* Error styles */
.payment-error-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  background-color: #f8f9fa;
}

.error-content {
  background: white;
  padding: 40px;
  border-radius: 16px;
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.08);
  width: 100%;
  max-width: 400px;
}

/* Responsive styles */
@media (max-width: 576px) {
  .success-payment-card {
    padding: 24px;
  }

  .amount-value {
    font-size: 28px;
  }

  .success-title {
    font-size: 24px;
  }

  .transaction-details {
    padding: 16px;
  }
} 