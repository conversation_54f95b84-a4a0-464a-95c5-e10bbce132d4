import React, { useState, useRef, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Icon } from '@iconify/react';
import './Course.css';
import NoData from '../../../components/common/NoData';
import { getMyCourses } from '../../../services/userService';
import { encodeData } from '../../../utils/encodeAndEncode';

function MyCourseTab() {
  const navigate = useNavigate();
  const [courses, setCourses] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [isLoading, setIsLoading] = useState(false);
  const [clickedCourseId, setClickedCourseId] = useState(null);
  const searchTimeout = useRef(null);

  useEffect(() => {
    fetchMyCourses(1, searchTerm, true);
  }, []);

  useEffect(() => {
    const handleScroll = () => {
      if (
        window.innerHeight + document.documentElement.scrollTop + 100 >= document.documentElement.offsetHeight &&
        hasMore && !isLoading
      ) {
        fetchMyCourses(page + 1, searchTerm);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [page, hasMore, isLoading]);

  const fetchMyCourses = async (pageNumber = 1, search = '', reset = false) => {
    try {
      setIsLoading(true);
      const response = await getMyCourses({ page: pageNumber, limit: 10, search });
      console.log('My Courses Response:---------------------------------------------', response);

      if (response.success && response.data?.courses.length > 0) {
        const mapped = response.data.courses.map((course, index) => ({
          id: course.course_id,
          title: course.course_name,
          description: course.course_desc,
          image: course.banner_image,
          modules: response.data.metadata[index]?.total_modules || 0,
          enrolled: response.data.metadata[index]?.totalUsers || 0,
          rating: course.total_rating || 0,
          level: course.levels || 'N/A' 
        }));

        setCourses(prev => reset ? mapped : [...prev, ...mapped]);
        setPage(pageNumber);
        setHasMore(pageNumber < response.data.totalPages);
      } else {
        if (reset) setCourses([]);
        setHasMore(false);
      }
    } catch (error) {
      console.error('Error fetching courses:', error);
      setHasMore(false);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSearchChange = (e) => {
    const value = e.target.value;
    setSearchTerm(value);
    if (searchTimeout.current) clearTimeout(searchTimeout.current);
    searchTimeout.current = setTimeout(() => {
      fetchMyCourses(1, value, true);
    }, 500);
  };

  const handleCourseClick = (courseId) => {
    setClickedCourseId(courseId);
    const encoded = encodeData({ id: courseId });

    setTimeout(() => {
      navigate(`/user/courses/WatchCourse/${encodeURIComponent(encoded)}`);
    }, 1000);
  };

  return (
    <div className="course-tab-content">
      <div className="row mb-3 mt-2">
        <div className="col-md-4">
          <div className="position-relative">
            <input
              type="text"
              className="form-control"
              placeholder="Search courses..."
              value={searchTerm}
              onChange={handleSearchChange}
            />
            {isLoading && (
              <div className="spinner-border text-primary position-absolute"
                style={{ width: '1rem', height: '1rem', right: '10px', top: '50%', transform: 'translateY(-50%)' }}
                role="status">
                <span className="visually-hidden">Loading...</span>
              </div>
            )}
          </div>
        </div>
      </div>

      <div className="row">
        {courses.length === 0 && !isLoading ? (
          <div className="d-flex justify-content-center align-items-center w-100" style={{ minHeight: '300px' }}>
            <NoData message="No courses found." />
          </div>
        ) : (
          courses.map(course => (
            <div key={course.id} className="col-md-6 col-lg-3 mb-2">
              <div className="course-card">
                <div className="course-image">
                  <img src={course.image} alt={course.title} className="img-fluid" />
                </div>
                <div className="course-details">
                  <h5 className="course-title">{course.title}</h5>
                  <p className="course-description">{course.description}</p>

                  <div className="course-meta-info">
                    <div className="meta-row d-flex justify-content-between">
                      <div className="views-count">
                        <Icon icon="mdi:eye-outline" className="meta-icon" />
                        <span>{course.enrolled}</span>
                      </div>
                      <div className="rating-stars-container">
                        <Icon icon="mdi:star" className="star-icon" />
                        <span className="rating-value">{course.rating}</span>
                      </div>
                    </div>

                    <div className="meta-row d-flex justify-content-between">
                      <div className="course-duration">
                        <Icon icon="mdi:clock-outline" className="meta-icon" />
                        <span>{course.modules} module{course.modules !== 1 ? 's' : ''}</span>
                      </div>
                      <div className="course-duration">
                        <Icon icon="mdi:signal-cellular-outline" className="meta-icon" />
                        <span>{course.level}</span>
                      </div>
                    </div>
                  </div>

                  <div className="course-footer d-flex justify-content-center">
                    <button
                      onClick={() => handleCourseClick(course.id)}
                      className="watch-now-btn w-75 primary-btn"
                      disabled={clickedCourseId === course.id}
                    >
                      {clickedCourseId === course.id ? (
                        <div className="spinner-border spinner-border-sm text-light" role="status">
                          <span className="visually-hidden">Loading...</span>
                        </div>
                      ) : (
                        <>
                          <Icon icon="mdi:play-circle" className="btn-icon" />
                          Continue
                        </>
                      )}
                    </button>
                  </div>
                </div>
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  );
}

export default MyCourseTab;
