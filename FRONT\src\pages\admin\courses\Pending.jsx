import React, { useState, useEffect } from 'react'
import { Icon } from '@iconify/react';
import { useNavigate } from 'react-router-dom';
import { courses_api, courseDelete, sendApprovalCourse } from '../../../services/adminService';
import Loader from '../../../components/admin/Loader';
import NoData from '../../../components/common/NoData';
import { encodeData } from '../../../utils/encodeAndEncode';
import { Modal } from 'react-bootstrap';
import { toast } from 'react-toastify';
import { usePermissions } from '../../../context/PermissionsContext';

function Pending() {
    const navigate = useNavigate();
    const { permissions } = usePermissions();
    const [loading, setLoading] = useState(true);
    const [courseData, setCourseData] = useState([]);
    const [searchTerm, setSearchTerm] = useState('');
    const [currentPage, setCurrentPage] = useState(1);
    const [recordsPerPage, setRecordsPerPage] = useState(10);
    const [showDeleteModal, setShowDeleteModal] = useState(false);
    const [showApprovalModal, setShowApprovalModal] = useState(false);
    const [selectedCourse, setSelectedCourse] = useState(null);

    useEffect(() => {
        fetchCourses();
    }, []);

    const fetchCourses = async () => {
        try {
            setLoading(true);
            const startTime = Date.now();
            const response = await courses_api();
            
            // Ensure loader shows for at least 500ms
            const elapsedTime = Date.now() - startTime;
            if (elapsedTime < 500) {
                await new Promise(resolve => setTimeout(resolve, 500 - elapsedTime));
            }

            if (response.success) {
                // Filter only pending courses from the response
                const pendingCourses = response.data.pending || [];
                setCourseData(pendingCourses);
            }
        } catch (error) {
            console.error('Error fetching courses:', error);
            toast.error('Error fetching courses: ' + error.message, {
                position: 'top-center',
                autoClose: 3000
            });
        } finally {
            setLoading(false);
        }
    };

    const handleNavigateToModule = (courseId) => {
        if (!permissions.course_module_management_view) {
            toast.warning("You don't have permission to view course modules");
            return;
        }
        const encodedCourseId = encodeData(courseId);
        console.log('Encoded Course ID', encodedCourseId);
        navigate(`/admin/courses/module/${encodedCourseId}`);
    };


    const handleNavigateToAnalytics = (e, courseId) => {
        e.stopPropagation();
        if (!permissions.course_management_view_analytics) {
            toast.warning("You don't have permission to view analytics");
            return;
        }
        const encodedCourseId = encodeData(courseId); 
        console.log('Encoded Course ID for analytics', encodedCourseId);
        navigate(`/admin/courses/analytics/${encodedCourseId}`);
    };

    const handleEditCourse = (e, course) => {
        e.stopPropagation();
        if (!permissions.course_management_edit) {
            toast.warning("You don't have permission to edit courses");
            return;
        }
        navigate(`/admin/courses/edit-create/${encodeData(course.id)}`);
    };

    const handleApprovalClick = (e, course) => {
        e.stopPropagation();
        if (!permissions.course_management_send_approval) {
            toast.warning("You don't have permission to send courses for approval");
            return;
        }
        setSelectedCourse(course);
        setShowApprovalModal(true);
    };

    const handleCloseApprovalModal = () => {
        setShowApprovalModal(false);
        setSelectedCourse(null);
    };

    const handleApprovalConfirm = async () => {
        try {
            setLoading(true);
            const user_id = JSON.parse(localStorage.getItem('user'))?.id; // Get user_id from localStorage or your auth context
            const response = await sendApprovalCourse({ 
                user_id: user_id,
                course_id: selectedCourse.id 
            });

            if (response.success === true) {
                toast.success('Course sent for approval successfully', {
                    position: 'top-center',
                    autoClose: 3000
                });
                // Refresh the course list to update status
                fetchCourses();
            } else {
                console.log('Failed to send course for approval', response);
                toast.error(response.message || 'Failed to send course for approval', {
                    position: 'top-center',
                    autoClose: 3000
                });
            }
        } catch (error) {
            toast.error('Error sending course for approval: ' + error.message, {
                position: 'top-center',
                autoClose: 3000
            });
        } finally {
            setLoading(false);
            handleCloseApprovalModal();
        }
    };

    const getStatusClass = (status) => {
        const statusClasses = {
            'pending': 'bg-warning-subtle',
            'approved': 'bg-success-subtle',
            'waiting_for_approval': 'bg-info-subtle',
            'rejected': 'bg-danger-subtle'
        }
        return statusClasses[status] || 'bg-secondary-subtle'
    }

    // Filter courses based on search term
    const filteredCourses = courseData.filter(course => 
        course.course_name.toLowerCase().includes(searchTerm.toLowerCase())
    );

    // Calculate pagination
    const indexOfLastRecord = currentPage * recordsPerPage;
    const indexOfFirstRecord = indexOfLastRecord - recordsPerPage;
    const currentRecords = filteredCourses.slice(indexOfFirstRecord, indexOfLastRecord);
    const totalPages = Math.ceil(filteredCourses.length / recordsPerPage);

    const handleDeleteClick = (e, course) => {
        e.stopPropagation();
        if (!permissions.course_management_delete) {
            toast.warning("You don't have permission to delete courses");
            return;
        }
        setSelectedCourse(course);
        setShowDeleteModal(true);
    };

    const handleCloseModal = () => {
        setShowDeleteModal(false);
        setSelectedCourse(null);
    };

    const handleDeleteConfirm = async () => {
        try {
            setLoading(true);
            const response = await courseDelete({ course_id: selectedCourse.id });
            if (response.success === true) {
                toast.success('Course deleted successfully', {
                    position: 'top-center',
                    autoClose: 3000
                });
                setCourseData(prevList => prevList.filter(course => course.id !== selectedCourse.id));
            } else {
                console.log('Failed to delete course', response);
                toast.error(response.message || 'Failed to delete course', {
                    position: 'top-center',
                    autoClose: 3000
                });
            }
        } catch (error) {
            toast.error('Error deleting course: ' + error.message, {
                position: 'top-center',
                autoClose: 3000
            });
        } finally {
            setLoading(false);
            handleCloseModal();
        }
    };

    if (loading) {
        return <Loader />;
    }

    return (
        <>
            {/* Search and Create course button  */}
            <div className="row d-flex mb-3">
                <div className="col-md-6 mt-2 d-flex justify-content-start align-items-center">
                    <input
                        type="search"
                        className="form-control"
                        placeholder="Search pending courses..."
                        style={{ maxWidth: '300px' }}
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                    />
                </div>
               
            </div>

            {/* Table  */}
            <div className="row mb-3">
                <div className="col-12">
                    {currentRecords.length === 0 ? (
                        <NoData message={searchTerm ? "No courses match your search" : "No pending courses available"} />
                    ) : (
                        <div className="card">
                            <div className="table-responsive">
                                <table className="table table-borderless mb-0" style={{ minWidth: '800px' }}>
                                    <thead>
                                        <tr>
                                            <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500', width: '40%' }}>Course Name</th>
                                            <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500', width: '100px' }}>Enrolled</th>
                                            <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500', width: '100px' }}>Price</th>
                                            <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500', width: '100px' }}>Ratings</th>
                                            <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500', width: '100px' }}>Status</th>
                                            <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500', width: '100px' }}>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {currentRecords.map((course) => (
                                            <tr 
                                                key={course.id} 
                                                className="border-bottom" 
                                                style={{ cursor: 'pointer' }}
                                                onClick={() => handleNavigateToModule(course.id)}
                                            >
                                                <td className="py-3">
                                                    <div className="d-flex align-items-center">
                                                        <img
                                                            src={course.banner_image || 'default-course-image.png'}
                                                            alt={course.course_name}
                                                            className="me-3 rounded"
                                                            style={{ 
                                                                width: '160px', 
                                                                height: '90px', 
                                                                objectFit: 'cover',
                                                                flexShrink: 0
                                                            }}
                                                        />
                                                        <div>
                                                            <div className="fw-medium mb-2">{course.course_name}</div>
                                                            <div className="d-flex flex-wrap" style={{ gap: '16px', fontSize: '14px', color: '#6c757d' }}>
                                                                <span className="d-inline-flex align-items-center">
                                                                    <Icon icon="tabler:video" className="me-2" width="16" height="16" />
                                                                    {course.course_summary.total_videos} Videos
                                                                </span>
                                                                <span className="d-inline-flex align-items-center">
                                                                    <Icon icon="tabler:file-text" className="me-2" width="16" height="16" />
                                                                    {course.course_summary.total_documents} Documents
                                                                </span>
                                                                <span className="d-inline-flex align-items-center">
                                                                    <Icon icon="tabler:writing" className="me-2" width="16" height="16" />
                                                                    {course.course_summary.total_assessments} Assessments
                                                                </span>
                                                                <span className="d-inline-flex align-items-center">
                                                                    <Icon icon="tabler:chart-dots" className="me-2" width="16" height="16" />
                                                                    {course.course_summary.total_surveys} Surveys
                                                                </span>
                                                                <span className="d-inline-flex align-items-center">
                                                                    <Icon icon="tabler:clock" className="me-2" width="16" height="16" />
                                                                    {course.course_summary.total_duration} Hours
                                                                </span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td className="py-3 align-middle text-start">{course.course_summary.total_purchased || 0}</td>
                                                <td className="py-3 align-middle text-start">
                                                    {course.course_price ? `$${course.course_price}` : 'Free'}
                                                </td>
                                                <td className="py-3 align-middle text-start">{course.course_summary.average_rating || 0}</td>
                                                <td className="py-3 align-middle text-start">
                                                    <span className={`badge text-dark ${getStatusClass(course.course_status)}`}>
                                                        {course.course_status}
                                                    </span>
                                                </td>
                                                <td className="py-3 align-middle text-start">
                                                    <div className="d-flex gap-2">
                                                        <button
                                                            className="btn btn-outline-dark btn-sm border border-dark"
                                                            style={{ width: '36px', height: '36px' }}
                                                            title={!permissions.course_management_send_approval ? "You don't have permission to send for approval" : "Send for Approval"}
                                                            onClick={(e) => handleApprovalClick(e, course)}
                                                            disabled={!permissions.course_management_send_approval}
                                                        >
                                                            <Icon icon="fluent:checkmark-24-regular" width="16" height="16" />
                                                        </button>
                                                        <button
                                                            className="btn btn-outline-dark btn-sm border border-dark"
                                                            style={{ width: '36px', height: '36px' }}
                                                            title={!permissions.course_management_edit ? "You don't have permission to edit courses" : "Edit Course"}
                                                            onClick={(e) => handleEditCourse(e, course)}
                                                            disabled={!permissions.course_management_edit}
                                                        >
                                                            <Icon icon="fluent:edit-24-regular" width="16" height="16" />
                                                        </button>
                                                        <button
                                                            className="btn btn-outline-dark btn-sm border border-dark"
                                                            style={{ width: '36px', height: '36px' }}
                                                            title={!permissions.course_management_delete ? "You don't have permission to delete courses" : "Delete Course"}
                                                            onClick={(e) => handleDeleteClick(e, course)}
                                                            disabled={!permissions.course_management_delete}
                                                        >
                                                            <Icon icon="fluent:delete-24-regular" width="16" height="16" />
                                                        </button>
                                                        <button
                                                            className="btn btn-outline-dark btn-sm border border-dark"
                                                            style={{ width: '36px', height: '36px' }}
                                                            title={!permissions.course_module_management_view ? "You don't have permission to view modules" : "View Module"}
                                                            onClick={(e) => {
                                                                e.stopPropagation();
                                                                handleNavigateToModule(course.id);
                                                            }}
                                                            disabled={!permissions.course_module_management_view}
                                                        >
                                                            <Icon icon="fluent:book-24-regular" width="16" height="16" />
                                                        </button>
                                                        <button
                                                            className="btn btn-outline-dark btn-sm border border-dark"
                                                            style={{ width: '36px', height: '36px' }}
                                                            title={!permissions.course_management_view_analytics ? "You don't have permission to view analytics" : "Course Analytics"}
                                                            onClick={(e) => handleNavigateToAnalytics(e, course.id)}
                                                            disabled={!permissions.course_management_view_analytics}
                                                        >
                                                            <Icon icon="fluent:chart-multiple-24-regular" width="16" height="16" />
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                        ))}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    )}
                </div>
            </div>

            {/* Record per page and pagination  */}
            {currentRecords.length > 0 && (
                <div className="row">
                    <div className="col-md-6 d-flex align-items-center gap-3">
                        <select 
                            className="form-select" 
                            style={{ width: 'auto' }}
                            value={recordsPerPage}
                            onChange={(e) => setRecordsPerPage(Number(e.target.value))}
                        >
                            <option value={10}>10 records per page</option>
                            <option value={25}>25 records per page</option>
                            <option value={50}>50 records per page</option>
                        </select>
                        <span className="text-muted">Total Records: {filteredCourses.length}</span>
                    </div>
                    <div className="col-md-6 d-flex justify-content-end align-items-center gap-3">
                        <button 
                            className="btn btn-primary" 
                            style={{ width: '150px' }}
                            onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                            disabled={currentPage === 1}
                        >
                            Prev
                        </button>
                        <span>Page {currentPage} of {totalPages || 1}</span>
                        <button 
                            className="btn btn-primary" 
                            style={{ width: '150px' }}
                            onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                            disabled={currentPage === totalPages || totalPages === 0}
                        >
                            Next
                        </button>
                    </div>
                </div>
            )}

            {/* Add Delete Confirmation Modal */}
            <Modal
                show={showDeleteModal}
                onHide={handleCloseModal}
                centered
            >
                <Modal.Header closeButton>
                    <Modal.Title>Confirm Delete</Modal.Title>
                </Modal.Header>
                <Modal.Body>
                    <p>Are you sure you want to delete the course "{selectedCourse?.course_name}"?</p>
                    <p className="text-danger mb-0">This action cannot be undone.</p>
                </Modal.Body>
                <Modal.Footer>
                    <button 
                        type="button" 
                        className="btn btn-secondary" 
                        onClick={handleCloseModal}
                    >
                        Cancel
                    </button>
                    <button 
                        type="button" 
                        className="btn btn-danger"
                        onClick={handleDeleteConfirm}
                    >
                        Delete
                    </button>
                </Modal.Footer>
            </Modal>

            {/* Approval Confirmation Modal */}
            <Modal
                show={showApprovalModal}
                onHide={handleCloseApprovalModal}
                centered
            >
                <Modal.Header closeButton>
                    <Modal.Title>Send Course for Approval</Modal.Title>
                </Modal.Header>
                <Modal.Body>
                    <div className="mb-3">
                        <h6>Important Information:</h6>
                        <ul className="text-danger">
                            <li>After sending for approval, you cannot edit the course details</li>
                            <li>You cannot update any modules or content in the course</li>
                            <li>Please verify all your course content before proceeding</li>
                        </ul>
                    </div>
                    <p>Are you sure you want to send "{selectedCourse?.course_name}" for approval?</p>
                </Modal.Body>
                <Modal.Footer>
                    <button 
                        type="button" 
                        className="btn btn-secondary" 
                        onClick={handleCloseApprovalModal}
                    >
                        Close
                    </button>
                    <button 
                        type="button" 
                        className="btn btn-primary"
                        onClick={handleApprovalConfirm}
                    >
                        Send for Approval
                    </button>
                </Modal.Footer>
            </Modal>
        </>
    )
}

export default Pending
