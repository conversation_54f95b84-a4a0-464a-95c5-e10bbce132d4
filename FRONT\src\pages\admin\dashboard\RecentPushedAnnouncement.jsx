import React from 'react';
import { Icon } from '@iconify/react';
import NoData from '../../../components/common/NoData';
import Loader from '../../../components/common/Loader';

const formatDate = (dateString) => {
  const date = new Date(dateString);
  const now = new Date();
  const diffInMs = now - date;
  const diffInHours = diffInMs / (1000 * 60 * 60);
  const diffInDays = diffInMs / (1000 * 60 * 60 * 24);

  if (diffInHours < 1) {
    return 'Just now';
  } else if (diffInHours < 24) {
    return `${Math.floor(diffInHours)} hours ago`;
  } else if (diffInDays < 7) {
    return `${Math.floor(diffInDays)} days ago`;
  } else {
    return date.toLocaleDateString();
  }
};

function RecentPushedAnnouncement({ announcements = [], loading = false }) {
  return (
    <div className="card p-3 shadow-sm" style={{ borderRadius: '12px', minHeight: '500px' }}>
      <h5 className="mb-3">Recent Announcements</h5>

      {loading ? (
        <div className="d-flex justify-content-center align-items-center" style={{ minHeight: '400px' }}>
          <Loader />
        </div>
      ) : announcements.length === 0 ? (
        <div className="d-flex justify-content-center align-items-center" style={{ minHeight: '400px' }}>
          <NoData />
        </div>
      ) : (
        <div style={{ height: '400px', overflowY: 'auto' }}>
          {announcements.map((item, index) => (
            <div
              key={item.id || index}
              className="mb-3 p-3"
              style={{
                borderLeft: '4px solid #d63384',
                background: '#fff',
                boxShadow: '0 1px 3px rgba(0,0,0,0.04)',
              }}
            >
              <div className="fw-semibold" style={{ fontSize: '15px', color: '#000' }}>
                {item.title || 'No Title'}
              </div>
              <div className="text-muted mb-1" style={{ fontSize: '12px' }}>
                {formatDate(item.createdAt)}
              </div>
              <div style={{ fontSize: '14px', color: '#444' }}>
                {item.message || 'No message available'}
              </div>
              {item.scheduleDateTime && (
                <div className="text-muted mt-1" style={{ fontSize: '12px' }}>
                  <Icon icon="lucide:calendar" width="12" height="12" className="me-1" />
                  Scheduled: {formatDate(item.scheduleDateTime)}
                </div>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );
}

export default RecentPushedAnnouncement;
