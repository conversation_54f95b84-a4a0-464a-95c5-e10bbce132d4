import React from 'react'
import { Icon } from '@iconify/react';
import { toast } from 'react-toastify';
import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import NoData from '../../../components/common/NoData';
import { courses_api } from '../../../services/adminService';
import Loader from '../../../components/admin/Loader';
import { encodeData } from '../../../utils/encodeAndEncode';
import { usePermissions } from '../../../context/PermissionsContext';

function Approved() {
    const navigate = useNavigate();
    const { permissions } = usePermissions();
    const [courseList, setCourseList] = useState([]);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    const [currentPage, setCurrentPage] = useState(1);
    const [recordsPerPage, setRecordsPerPage] = useState(10);
    const [searchTerm, setSearchTerm] = useState('');

    const fetchCourseList = async () => {
        try {
            setLoading(true);
            const startTime = Date.now();
            const response = await courses_api();
            console.log('Approved Courses Response', response);

            // Ensure loader shows for at least 500ms
            const elapsedTime = Date.now() - startTime;
            if (elapsedTime < 500) {
                await new Promise(resolve => setTimeout(resolve, 500 - elapsedTime));
            }

            if (response.success === true) {
                setCourseList(response.data.approved || []);
            } else {
                setError(response.message);
            }
        } catch (error) {
            setError(error.message);
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        fetchCourseList();
    }, []);

    // Filter courses based on search term
    const filteredCourses = courseList.filter(course => 
        course.course_name.toLowerCase().includes(searchTerm.toLowerCase())
    );

    // Calculate pagination
    const indexOfLastRecord = currentPage * recordsPerPage;
    const indexOfFirstRecord = indexOfLastRecord - recordsPerPage;
    const currentRecords = filteredCourses.slice(indexOfFirstRecord, indexOfLastRecord);
    const totalPages = Math.ceil(filteredCourses.length / recordsPerPage);

    const handleNavigateToModule = (courseId) => {
        if (!permissions.course_module_management_view) {
            toast.warning("You don't have permission to view course modules");
            return;
        }
        const encodedCourseId = encodeData(courseId);
        navigate(`/admin/courses/module/${encodedCourseId}`);
    };

    const handleNavigateToAnalytics = (e, courseId) => {
        e.stopPropagation();
        if (!permissions.course_management_view_analytics) {
            toast.warning("You don't have permission to view analytics");
            return;
        }
        const encodedCourseId = encodeData(courseId);
        navigate(`/admin/courses/analytics/${encodedCourseId}`);
    };



    return (
        <>
            {/* Search bar */}
            <div className="row d-flex mb-3">
                <div className="col-md-6 mt-2 d-flex justify-content-start align-items-center">
                    <input
                        type="search"
                        className="form-control"
                        placeholder="Search approved courses..."
                        style={{ maxWidth: '300px' }}
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                    />
                </div>
            </div>

            {/* Table */}
            <div className="row mb-3">
                <div className="col-12">
                    <div className="card">
                        <div className="table-responsive">
                            {loading ? (
                                <Loader
                                    caption="Loading Approved Courses..."
                                    minLoadTime={500}
                                    containerHeight="400px"
                                />
                            ) : (
                                <>
                                    <table className="table table-borderless mb-0" style={{ minWidth: '800px' }}>
                                        <thead>
                                            <tr>
                                                <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500', width: '40%' }}>Course Name</th>
                                                <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500', width: '100px' }}>Enrolled  </th>
                                                <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500', width: '100px' }}>Price</th>
                                                <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500', width: '100px' }}>Ratings</th>
                                                <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500', width: '100px' }}>Status</th>
                                                <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500', width: '100px' }}>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {currentRecords.map((course) => (
                                                <tr
                                                    key={course.id}
                                                    className="border-bottom"
                                                    style={{ cursor: 'pointer' }}
                                                    onClick={() => handleNavigateToModule(course.id)}
                                                >
                                                    <td className="py-3">
                                                        <div className="d-flex align-items-center">
                                                            <img
                                                                src={course.banner_image}
                                                                alt={course.course_name}
                                                                className="me-3 rounded"
                                                                style={{
                                                                    width: '160px',
                                                                    height: '90px',
                                                                    objectFit: 'cover',
                                                                    flexShrink: 0
                                                                }}
                                                            />
                                                            <div>
                                                                <div className="fw-medium mb-2">{course.course_name}</div>
                                                                <div className="d-flex flex-wrap" style={{ gap: '16px', fontSize: '14px', color: '#6c757d' }}>
                                                                    <span className="d-inline-flex align-items-center">
                                                                        <Icon icon="tabler:video" className="me-2" width="16" height="16" />
                                                                        {course.course_summary.total_videos} Videos
                                                                    </span>
                                                                    <span className="d-inline-flex align-items-center">
                                                                        <Icon icon="tabler:file-text" className="me-2" width="16" height="16" />
                                                                        {course.course_summary.total_documents} Documents
                                                                    </span>
                                                                    <span className="d-inline-flex align-items-center">
                                                                        <Icon icon="tabler:writing" className="me-2" width="16" height="16" />
                                                                        {course.course_summary.total_assessments} Assessments
                                                                    </span>
                                                                    <span className="d-inline-flex align-items-center">
                                                                        <Icon icon="tabler:chart-dots" className="me-2" width="16" height="16" />
                                                                        {course.course_summary.total_surveys} Surveys
                                                                    </span>
                                                                    <span className="d-inline-flex align-items-center">
                                                                        <Icon icon="tabler:clock" className="me-2" width="16" height="16" />
                                                                        {course.course_summary.total_duration} Hours
                                                                    </span>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td className="py-3 align-middle text-start">{course.course_summary.total_purchased || 0}</td>
                                                    <td className="py-3 align-middle text-start">{course.course_price || 'Free'}</td>
                                                    <td className="py-3 align-middle text-start">{course.course_summary.average_rating || '0.0'}</td>
                                                    <td className="py-3 align-middle text-start">
                                                        <span className="badge text-dark bg-success-subtle">
                                                            {course.course_status}
                                                        </span>
                                                    </td>
                                                        <td className="py-3 align-middle text-start">
                                                            <div className="d-flex gap-2">
                                                            <button
      className="btn btn-success btn-sm approved-pulse"
      style={{ width: '36px', height: '36px', cursor: 'not-allowed' }}
      title="Course Approved"
    >
      <Icon icon="fluent:checkmark-24-filled" width="16" height="16" />
    </button>

                                                            <button
                                                                className="btn btn-outline-dark btn-sm border border-dark"
                                                                style={{ width: '36px', height: '36px' }}
                                                                title={!permissions.course_module_management_view ? "You don't have permission to view modules" : "View Module"}
                                                                onClick={(e) => {
                                                                    e.stopPropagation();
                                                                    handleNavigateToModule(course.id);
                                                                }}
                                                                disabled={!permissions.course_module_management_view}
                                                            >
                                                                <Icon icon="fluent:book-24-regular" width="16" height="16" />
                                                            </button>
                                                            <button
                                                                className="btn btn-outline-dark btn-sm border border-dark"
                                                                style={{ width: '36px', height: '36px' }}
                                                                title={!permissions.course_management_view_analytics ? "You don't have permission to view analytics" : "Course Analytics"}
                                                                onClick={(e) => handleNavigateToAnalytics(e, course.id)}
                                                                disabled={!permissions.course_management_view_analytics}
                                                            >
                                                                <Icon icon="fluent:chart-multiple-24-regular" width="16" height="16" />
                                                            </button>
                                                        </div>
                                                    </td>
                                                </tr>
                                            ))}
                                        </tbody>
                                    </table>
                                    {currentRecords.length === 0 && (
                                        <div className="text-center py-4">
                                            <NoData />
                                        </div>
                                    )}
                                </>
                            )}
                        </div>
                    </div>
                </div>
            </div>

            {/* Pagination */}
            <div className="row">
                <div className="col-md-6 d-flex align-items-center gap-3">
                    <select
                        className="form-select"
                        style={{ width: 'auto' }}
                        value={recordsPerPage}
                        onChange={(e) => {
                            setRecordsPerPage(Number(e.target.value));
                            setCurrentPage(1);
                        }}
                    >
                        <option value={10}>10 records per page</option>
                        <option value={25}>25 records per page</option>
                        <option value={50}>50 records per page</option>
                    </select>
                    <span className="text-secondary">
                        Total Records: {filteredCourses.length}
                    </span>
                </div>
                <div className="col-md-6 d-flex justify-content-end align-items-center gap-3">
                    <button
                        className="btn btn-primary"
                        style={{ width: '150px' }}
                        onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                        disabled={currentPage === 1}
                    >
                        Prev
                    </button>
                    <span>Page {currentPage} of {totalPages || 1}</span>
                    <button
                        className="btn btn-primary"
                        style={{ width: '150px' }}
                        onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                        disabled={currentPage === totalPages || totalPages === 0}
                    >
                        Next
                    </button>
                </div>
            </div>
        </>
    )
}

export default Approved
