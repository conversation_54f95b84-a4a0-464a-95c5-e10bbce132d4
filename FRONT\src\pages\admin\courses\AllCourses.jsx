import React from 'react'
import { Icon } from '@iconify/react';
import { toast } from 'react-toastify';
import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import NoData from '../../../components/common/NoData';
import { courses_api, courseDelete, cancelApprovalCourse, sendApprovalCourse } from '../../../services/adminService';
import Loader from '../../../components/admin/Loader';
import { Modal } from 'react-bootstrap';
import { encodeData } from '../../../utils/encodeAndEncode';
import { usePermissions } from '../../../context/PermissionsContext';

function AllCourses() {
    const navigate = useNavigate();
    const { permissions } = usePermissions();
    const [courseList, setCourseList] = useState([]);
    const [loading, setLoading] = useState(true);
    const [createLoading, setCreateLoading] = useState(false);
    const [error, setError] = useState(null);
    const [currentPage, setCurrentPage] = useState(1);
    const [recordsPerPage, setRecordsPerPage] = useState(10);
    const [searchTerm, setSearchTerm] = useState('');
    const [statusFilter, setStatusFilter] = useState('');
    const [showDeleteModal, setShowDeleteModal] = useState(false);
    const [showCancelModal, setShowCancelModal] = useState(false);
    const [selectedCourse, setSelectedCourse] = useState(null);
    const [showApprovalModal, setShowApprovalModal] = useState(false);

    // fetch course list
    const fetchCourseList = async () => {
        try {
            const response = await courses_api();
            console.log('All Courses Response', response);

            if (response.success === true) {
                setCourseList(response.data.all || []);
                console.log('Course List', courseList);
            } else {
                setError(response.message);
            }
        } catch (error) {
            setError(error.message);
        } finally {
            setLoading(false);
        }
    };

    // fetch course list on component mount only    
    useEffect(() => {
        const minLoadTime = 500; // Minimum loading time in milliseconds
        const startTime = Date.now();

        fetchCourseList().then(() => {
            // Calculate remaining time to meet minimum loading duration
            const elapsedTime = Date.now() - startTime;
            if (elapsedTime < minLoadTime) {
                setTimeout(() => {
                    setLoading(false);
                }, minLoadTime - elapsedTime);
            }
        });
    }, []);

    // Filter courses based on search term and status
    const filteredCourses = courseList.filter(course => {
        const matchesSearch = course.course_name.toLowerCase().includes(searchTerm.toLowerCase());
        const matchesStatus = !statusFilter || course.course_status === statusFilter;
        return matchesSearch && matchesStatus;
    });

    // Calculate pagination
    const indexOfLastRecord = currentPage * recordsPerPage;
    const indexOfFirstRecord = indexOfLastRecord - recordsPerPage;
    const currentRecords = filteredCourses.slice(indexOfFirstRecord, indexOfLastRecord);
    const totalPages = Math.ceil(filteredCourses.length / recordsPerPage);

    const handleNavigateToModule = (courseId) => {
        if (!permissions.course_module_management_view) {
            toast.warning("You don't have permission to view course modules");
            return;
        }
        const encodedCourseId = encodeData(courseId);
        console.log('Encoded Course ID', encodedCourseId);
        navigate(`/admin/courses/module/${encodedCourseId}`);
    };

    const handleNavigateToCreateCourse = () => {
        if (!permissions.course_management_create) {
            toast.warning("You don't have permission to create courses");
            return;
        }
        setCreateLoading(true);
        navigate('/admin/courses/create');
    };

    const handleNavigateToAnalytics = (e, courseId) => {
        e.stopPropagation();
        if (!permissions.course_management_view_analytics) {
            toast.warning("You don't have permission to view analytics");
            return;
        }
        const encodedCourseId = encodeData(courseId); 
        console.log('Encoded Course ID for analytics', encodedCourseId);
        navigate(`/admin/courses/analytics/${encodedCourseId}`);
    };

    const handleEditCourse = (e, course) => {
        e.stopPropagation();
        if (!permissions.course_management_edit) {
            toast.warning("You don't have permission to edit courses");
            return;
        }
        navigate(`/admin/courses/edit-create/${encodeData(course.id)}`);
    };

    const getStatusClass = (status) => {
        const statusClasses = {
            'approved': 'bg-success-subtle',
            'pending': 'bg-warning-subtle',
            'rejected': 'bg-danger-subtle',
            'waiting_for_approval': 'bg-info-subtle'
        }
        return statusClasses[status] || 'bg-secondary-subtle'
    }

    const handleDeleteClick = (e, course) => {
        e.stopPropagation();
        if (!permissions.course_management_delete) {
            toast.warning("You don't have permission to delete courses");
            return;
        }
        setSelectedCourse(course);
        setShowDeleteModal(true);
    };

    const handleCloseModal = () => {
        setShowDeleteModal(false);
        setSelectedCourse(null);
    };

    const handleDeleteConfirm = async () => {
        try {
            const response = await courseDelete({ course_id: selectedCourse.id });
            if (response.success === true) {
                toast.success('Course deleted successfully', {
                    position: 'top-center',
                    autoClose: 3000
                });
                setCourseList(prevList => prevList.filter(course => course.id !== selectedCourse.id));
            } else {
                console.log('Failed to delete course', response);
                toast.error(response.message || 'Failed to delete course', {
                    position: 'top-center',
                    autoClose: 3000
                });
            }
        } catch (error) {
            toast.error('Error deleting course: ' + error.message, {
                position: 'top-center',
                autoClose: 3000
            });
        } finally {
            handleCloseModal();
        }
    };

    const handleCancelClick = (e, course) => {
        e.stopPropagation();
        if (!permissions.course_management_cancel_approval) {
            toast.warning("You don't have permission to cancel approval requests");
            return;
        }
        setSelectedCourse(course);
        setShowCancelModal(true);
    };

    const handleCloseCancelModal = () => {
        setShowCancelModal(false);
        setSelectedCourse(null);
    };

    const handleCancelConfirm = async () => {
        try {
            const response = await cancelApprovalCourse({ course_id: selectedCourse.id });

            if (response.success === true) {
                toast.success('Approval request cancelled successfully', {
                    position: 'top-center',
                    autoClose: 3000
                });
                // Refresh the course list
                fetchCourseList();
            } else {
                console.log('Failed to cancel approval request', response);
                toast.error(response.message || 'Failed to cancel approval request', {
                    position: 'top-center',
                    autoClose: 3000
                });
            }
        } catch (error) {
            toast.error('Error cancelling approval request: ' + error.message, {
                position: 'top-center',
                autoClose: 3000
            });
        } finally {
            handleCloseCancelModal();
        }
    };

    const handleSendApprovalClick = (e, course) => {
        e.stopPropagation();
        if (!permissions.course_management_resend_approval) {
            toast.warning("You don't have permission to resend approval requests");
            return;
        }
        setSelectedCourse(course);
        setShowApprovalModal(true);
    };

    const handleCloseApprovalModal = () => {
        setShowApprovalModal(false);
        setSelectedCourse(null);
    };

    const handleSendApprovalConfirm = async () => {
        try {
            const user_id = JSON.parse(localStorage.getItem('user'))?.id;
            const response = await sendApprovalCourse({ 
                user_id: user_id,
                course_id: selectedCourse.id 
            });

            if (response.success === true) {
                toast.success('Course resent for approval successfully', {
                    position: 'top-center',
                    autoClose: 3000
                });
                // Refresh the course list
                fetchCourseList();
            } else {
                console.log('Failed to send course for approval', response);
                toast.error(response.message || 'Failed to send course for approval', {
                    position: 'top-center',
                    autoClose: 3000
                });
            }
        } catch (error) {
            toast.error('Error sending course for approval: ' + error.message, {
                position: 'top-center',
                autoClose: 3000
            });
        } finally {
            handleCloseApprovalModal();
        }
    };

    return (
        <>
            {/* Search and Create course button  */}
            <div className="row d-flex mb-3">
                <div className="col-md-6 mt-2 d-flex justify-content-start align-items-center">
                    <input
                        type="search"
                        className="form-control"
                        placeholder="Search courses..."
                        style={{ maxWidth: '300px' }}
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                    />
                </div>
                <div className="col-md-6 text-end mt-2 d-flex justify-content-end align-items-center gap-2">
                    <select
                        className="form-select"
                        style={{ width: '180px' }}
                        value={statusFilter}
                        onChange={(e) => setStatusFilter(e.target.value)}
                    >
                        <option value="">Filter by Status</option>
                        <option value="pending">Pending</option>
                        <option value="approved">Approved</option>
                        <option value="waiting_for_approval">Waiting for Approval</option>
                        <option value="rejected">Rejected</option>
                    </select>
                    <button
                        className="btn btn-primary text-nowrap d-flex align-items-center justify-content-center gap-2"
                        style={{ width: '150px' }}
                        onClick={handleNavigateToCreateCourse}
                        disabled={createLoading || !permissions.course_management_create}
                        title={!permissions.course_management_create ? "You don't have permission to create courses" : ""}
                    >
                        {createLoading ? (
                            <>
                                <div className="spinner-border spinner-border-sm" role="status">
                                    <span className="visually-hidden">Loading...</span>
                                </div>
                                <span>Creating...</span>
                            </>
                        ) : (
                            'Create Course'
                        )}
                    </button>
                </div>
            </div>

            {/* Table  */}
            <div className="row mb-3">
                <div className="col-12">
                    <div className="card">
                        <div className="table-responsive">
                            {loading ? (
                                <Loader
                                    caption="Loading Courses..."
                                    minLoadTime={500}
                                    containerHeight="400px"
                                />
                            ) : (
                                <>
                                    <table className="table table-borderless mb-0" style={{ minWidth: '800px' }}>
                                        <thead>
                                            <tr>
                                                <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500', width: '40%' }}>Course Name</th>
                                                <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500', width: '100px' }}>Enrolled</th>
                                                <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500', width: '100px' }}>Price</th>
                                                <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500', width: '100px' }}>Ratings</th>
                                                <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500', width: '100px' }}>Status</th>
                                                <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500', width: '100px' }}>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {currentRecords.map((course) => (
                                                <tr
                                                    key={course.id}
                                                    className="border-bottom"
                                                    style={{ cursor: 'pointer' }}
                                                    onClick={() => handleNavigateToModule(course.id)}
                                                >
                                                    <td className="py-3">
                                                        <div className="d-flex align-items-center">
                                                            <img
                                                                src={course.banner_image}
                                                                alt={course.course_name}
                                                                className="me-3 rounded"
                                                                style={{
                                                                    width: '160px',
                                                                    height: '90px',
                                                                    objectFit: 'cover',
                                                                    flexShrink: 0
                                                                }}
                                                            />
                                                            <div>
                                                                <div className="fw-medium mb-2">{course.course_name}</div>
                                                                <div className="d-flex flex-wrap" style={{ gap: '16px', fontSize: '14px', color: '#6c757d' }}>
                                                                    <span className="d-inline-flex align-items-center align-items-center">
                                                                        <Icon icon="tabler:video" className="me-2" width="16" height="16" />
                                                                        {course.course_summary.total_videos} Videos
                                                                    </span>
                                                                    <span className="d-inline-flex align-items-center align-items-center">
                                                                        <Icon icon="tabler:file-text" className="me-2" width="16" height="16" />
                                                                        {course.course_summary.total_documents} Documents
                                                                    </span>
                                                                    <span className="d-inline-flex align-items-center align-items-center">
                                                                        <Icon icon="tabler:writing" className="me-2" width="16" height="16" />
                                                                        {course.course_summary.total_assessments} Assessments
                                                                    </span>
                                                                    <span className="d-inline-flex align-items-center align-items-center">
                                                                        <Icon icon="tabler:chart-dots" className="me-2" width="16" height="16" />
                                                                        {course.course_summary.total_surveys} Surveys
                                                                    </span>
                                                                    <span className="d-inline-flex align-items-center align-items-center">
                                                                        <Icon icon="tabler:clock" className="me-2" width="16" height="16" />
                                                                        {course.course_summary.total_duration} Hours
                                                                    </span>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td className="py-3 align-middle text-start">{course.course_summary.total_purchased || 0}</td>
                                                    <td className="py-3 align-middle text-start">{course.course_price || 'Free'}</td>
                                                    <td className="py-3 align-middle text-start">{course.course_summary.average_rating || '0.0'}</td>
                                                    <td className="py-3 align-middle text-start">
                                                        <span className={`badge text-dark ${getStatusClass(course.course_status)}`}>
                                                            {course.course_status}
                                                        </span>
                                                    </td>
                                                    <td className="py-3 align-middle text-start">
                                                        <div className="d-flex gap-2">
                                                            {course.course_status === 'approved' && (
                                                                <button
                                                                    className="btn btn-success btn-sm approved-pulse"
                                                                    style={{ width: '36px', height: '36px', cursor: 'not-allowed' }}
                                                                    title="Course Approved"
                                                                >
                                                                    <Icon icon="fluent:checkmark-24-filled" width="16" height="16" />
                                                                </button>
                                                            )}
                                                            {course.course_status === 'rejected' && (
                                                                <>
                                                                    <button
                                                                        className="btn btn-outline-success btn-sm border border-success"
                                                                        style={{ width: '36px', height: '36px' }}
                                                                        title={!permissions.course_management_resend_approval ? "You don't have permission to resend approval" : "Send Approval Request Again"}
                                                                        onClick={(e) => handleSendApprovalClick(e, course)}
                                                                        disabled={!permissions.course_management_resend_approval}
                                                                    >
                                                                        <Icon icon="fluent:arrow-repeat-all-24-regular" width="16" height="16" />
                                                                    </button>
                                                                    <button
                                                                        className="btn btn-outline-dark btn-sm border border-dark"
                                                                        style={{ width: '36px', height: '36px' }}
                                                                        title={!permissions.course_management_cancel_approval ? "You don't have permission to cancel approval" : "Cancel Approval Request"}
                                                                        onClick={(e) => handleCancelClick(e, course)}
                                                                        disabled={!permissions.course_management_cancel_approval}
                                                                    >
                                                                        <Icon icon="fluent:dismiss-24-regular" width="16" height="16" />
                                                                    </button>
                                                                </>
                                                            )}
                                                            {course.course_status !== 'waiting_for_approval' && course.course_status !== 'rejected' && (
                                                                <button
                                                                    className="btn btn-outline-dark btn-sm border border-dark"
                                                                    style={{ width: '36px', height: '36px' }}
                                                                    title={
                                                                        course.course_status === 'approved' 
                                                                            ? 'Cannot edit approved course' 
                                                                            : !permissions.course_management_edit 
                                                                                ? "You don't have permission to edit courses"
                                                                                : 'Edit Course'
                                                                    }
                                                                    onClick={(e) => handleEditCourse(e, course)}
                                                                    disabled={course.course_status === 'approved' || !permissions.course_management_edit}
                                                                >
                                                                    <Icon icon="fluent:edit-24-regular" width="16" height="16" />
                                                                </button>
                                                            )}
                                                            {course.course_status === 'waiting_for_approval' && (
                                                                <button
                                                                    className="btn btn-outline-dark btn-sm border border-dark"
                                                                    style={{ width: '36px', height: '36px' }}
                                                                    title={!permissions.course_management_cancel_approval ? "You don't have permission to cancel approval" : "Cancel Approval Request"}
                                                                    onClick={(e) => handleCancelClick(e, course)}
                                                                    disabled={!permissions.course_management_cancel_approval}
                                                                >
                                                                    <Icon icon="fluent:dismiss-24-regular" width="16" height="16" />
                                                                </button>
                                                            )}
                                                            <button
                                                                className="btn btn-outline-dark btn-sm border border-dark"
                                                                style={{ width: '36px', height: '36px' }}
                                                                title={
                                                                    course.course_status === 'approved' 
                                                                        ? 'Cannot delete approved course' 
                                                                        : !permissions.course_management_delete
                                                                            ? "You don't have permission to delete courses"
                                                                            : 'Delete Course'
                                                                }
                                                                onClick={(e) => handleDeleteClick(e, course)}
                                                                disabled={course.course_status === 'approved' || !permissions.course_management_delete}
                                                            >
                                                                <Icon icon="fluent:delete-24-regular" width="16" height="16" />
                                                            </button>
                                                            <button
                                                                className="btn btn-outline-dark btn-sm border border-dark"
                                                                style={{ width: '36px', height: '36px' }}
                                                                title={!permissions.course_module_management_view ? "You don't have permission to view modules" : "View Module"}
                                                                onClick={(e) => {
                                                                    e.stopPropagation();
                                                                    handleNavigateToModule(course.id);
                                                                }}
                                                                disabled={!permissions.course_module_management_view}
                                                            >
                                                                <Icon icon="fluent:book-24-regular" width="16" height="16" />
                                                            </button>
                                                            <button
                                                                className="btn btn-outline-dark btn-sm border border-dark"
                                                                style={{ width: '36px', height: '36px' }}
                                                                title={!permissions.course_management_view_analytics ? "You don't have permission to view analytics" : "Course Analytics"}
                                                                onClick={(e) => handleNavigateToAnalytics(e, course.id)}
                                                                disabled={!permissions.course_management_view_analytics}
                                                            >
                                                                <Icon icon="fluent:chart-multiple-24-regular" width="16" height="16" />
                                                            </button>
                                                        </div>
                                                    </td>
                                                </tr>
                                            ))}
                                        </tbody>
                                    </table>
                                    {currentRecords.length === 0 && (
                                        <div className="text-center py-4">
                                            <NoData />
                                        </div>
                                    )}
                                </>
                            )}
                        </div>
                    </div>
                </div>
            </div>

            {/* Record per page and pagination  */}
            <div className="row">
                <div className="col-md-6 d-flex align-items-center gap-3">
                    <select
                        className="form-select"
                        style={{ width: 'auto' }}
                        value={recordsPerPage}
                        onChange={(e) => {
                            setRecordsPerPage(Number(e.target.value));
                            setCurrentPage(1);
                        }}
                    >
                        <option value={10}>10 records per page</option>
                        <option value={25}>25 records per page</option>
                        <option value={50}>50 records per page</option>
                    </select>
                    <span className="text-secondary">
                        Total Records: {filteredCourses.length}
                    </span>
                </div>
                <div className="col-md-6 d-flex justify-content-end align-items-center gap-3">
                    <button
                        className="btn btn-primary"
                        style={{ width: '150px' }}
                        onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                        disabled={currentPage === 1}
                    >
                        Prev
                    </button>
                    <span>Page {currentPage} of {totalPages || 1}</span>
                    <button
                        className="btn btn-primary"
                        style={{ width: '150px' }}
                        onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                        disabled={currentPage === totalPages || totalPages === 0}
                    >
                        Next
                    </button>
                </div>
            </div>

            {/* Delete Confirmation Modal */}
            <Modal
                show={showDeleteModal}
                onHide={handleCloseModal}
                centered
            >
                <Modal.Header closeButton>
                    <Modal.Title>Confirm Delete</Modal.Title>
                </Modal.Header>
                <Modal.Body>
                    <p>Are you sure you want to delete the course "{selectedCourse?.course_name}"?</p>
                    <p className="text-danger mb-0">This action cannot be undone.</p>
                </Modal.Body>
                <Modal.Footer>
                    <button 
                        type="button" 
                        className="btn btn-secondary" 
                        onClick={handleCloseModal}
                    >
                        Cancel
                    </button>
                    <button 
                        type="button" 
                        className="btn btn-danger"
                        onClick={handleDeleteConfirm}
                    >
                        Delete
                    </button>
                </Modal.Footer>
            </Modal>

            {/* Cancel Approval Modal */}
            <Modal
                show={showCancelModal}
                onHide={handleCloseCancelModal}
                centered
            >
                <Modal.Header closeButton>
                    <Modal.Title>Cancel Approval Request</Modal.Title>
                </Modal.Header>
                <Modal.Body>
                    <p>Are you sure you want to cancel the approval request for "{selectedCourse?.course_name}"?</p>
                    <p>After cancelling:</p>
                    <ul>
                        <li>The course will return to pending status</li>
                        <li>You can edit the course details again</li>
                        <li>You'll need to submit for approval again when ready</li>
                    </ul>
                </Modal.Body>
                <Modal.Footer>
                    <button
                        type="button"
                        className="btn btn-secondary"
                        onClick={handleCloseCancelModal}
                    >
                        Close
                    </button>
                    <button
                        type="button"
                        className="btn btn-warning"
                        onClick={handleCancelConfirm}
                    >
                        Cancel Approval
                    </button>
                </Modal.Footer>
            </Modal>

            {/* Send Approval Modal */}
            <Modal show={showApprovalModal} onHide={handleCloseApprovalModal} centered>
                <Modal.Header closeButton>
                    <Modal.Title>Resend Course for Approval</Modal.Title>
                </Modal.Header>
                <Modal.Body>
                    <div className="mb-3">
                        <h6>Important Information:</h6>
                        <ul className="text-danger">
                            <li>After resending for approval, you cannot edit the course details</li>
                            <li>You cannot update any modules or content in the course</li>
                            <li>Please verify all your course content before proceeding</li>
                        </ul>
                    </div>
                    <p>Are you sure you want to resend "{selectedCourse?.course_name}" for approval?</p>
                </Modal.Body>
                <Modal.Footer>
                    <button 
                        type="button" 
                        className="btn btn-secondary" 
                        onClick={handleCloseApprovalModal}
                    >
                        Close
                    </button>
                    <button 
                        type="button" 
                        className="btn btn-primary"
                        onClick={handleSendApprovalConfirm}
                    >
                        Resend for Approval
                    </button>
                </Modal.Footer>
            </Modal>
        </>
    )
}

export default AllCourses

