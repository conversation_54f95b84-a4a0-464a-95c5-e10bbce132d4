import { Icon } from '@iconify/react';
import './Error.css';

const Error401 = () => {

  const RedirectToLogin = () => {
    window.location.href = '/auth/login';
  };

  return (
    <div className="error-page">
      <div className="error-content">
        <div className="error-number">401</div>
        <h1 className="error-title">Session Expired</h1>
        <p className="error-description">
          Your session has expired or is no longer valid. Please log in again to continue using the application.
        </p>
        <div className="error-actions">
          <button 
            className="btn-home" 
            onClick={RedirectToLogin}
          >
            <Icon icon="mdi:login" className="home-icon" />
            Login Again
          </button>
        </div>
      </div>
    
    </div>
  );
};

export default Error401; 