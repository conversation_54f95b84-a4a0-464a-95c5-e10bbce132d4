.quiz-container {
    max-width: 900px;
    margin: 0 auto;
    /* padding: 2rem; */
  }
  
  /* Quiz Intro Styles */
  .quiz-intro {
    text-align: center;
    background: white;
    /* padding: 2rem; */
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  }
  
  .quiz-info {
    display: flex;
    justify-content: center;
    gap: 2rem;
    margin: 2rem 0;
  }
  
  .info-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }
  
  .info-icon {
    font-size: 1.5rem;
    color: #4a90e2;
  }
  
  .instructions {
    text-align: left;
    margin: 2rem 0;
    /* padding: 1.5rem; */
    background: #f8f9fa;
    border-radius: 8px;
  }
  
  .instructions ul {
    list-style-type: none;
    padding: 0;
  }
  
  .instructions li {
    margin: 1rem 0;
    padding-left: 1.5rem;
    position: relative;
  }
  
  .instructions li:before {
    content: "•";
    color: #4a90e2;
    position: absolute;
    left: 0;
  }
  
  .start-quiz-btn {
    background: #4a90e2;
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 8px;
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin: 2rem auto;
    cursor: pointer;
    transition: background-color 0.3s;
  }
  
  .start-quiz-btn:hover {
    background: #357abd;
  }
  
  /* Quiz Header Styles */
  .quiz-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding: 1rem;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
  
  .question-progress {
    font-size: 1.2rem;
    font-weight: 500;
  }
  
  .current-question {
    color: #4a90e2;
  }
  
  .timer {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.2rem;
    color: #dc3545;
  }
  
  .timer-icon {
    font-size: 1.4rem;
  }
  
  /* Question Container Styles */
  .question-container {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
  }
  
  .question-text {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    line-height: 1.6;
  }

  .question-title-line {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
  }

  .question-label {
    font-size: 0.9rem;
    font-weight: 500;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  .question-number {
    font-size: 1.1rem;
    font-weight: 600;
    color: #495057;
  }

  .question-content {
    font-size: 1.2rem;
    line-height: 1.6;
    color: #212529;
    word-wrap: break-word;
    overflow-wrap: break-word;
    margin-bottom: 1.5rem;
  }

  .question-header {
    margin-bottom: 1.5rem;
  }
  
  .options-container {
    display: grid;
    gap: 1rem;
  }
  
  .option {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s;
  }
  
  .option:hover {
    border-color: #4a90e2;
    background: #f8f9fa;
  }
  
  .option.selected {
    border-color: #4a90e2;
    background: #e3f2fd;
  }
  
  .option-number {
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #4a90e2;
    color: white;
    border-radius: 50%;
    font-weight: 500;
  }
  
  .option-text {
    flex: 1;
  }
  
  /* Navigation Buttons Styles */
  .navigation-buttons {
    display: flex;
    justify-content: space-between;
    gap: 1rem;
  }
  
  .nav-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.8rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s;
  }
  
  .prev-btn {
    background: #6c757d;
    color: white;
  }
  
  .next-btn {
    background: #4a90e2;
    color: white;
  }
  
  .submit-btn {
    background: #28a745;
    color: white;
  }
  
  .nav-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
  
  .nav-btn:not(:disabled):hover {
    transform: translateY(-2px);
  }
  
  /* Results Container Styles */
  .results-container {
    background: white;
    padding: 3rem;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    text-align: center;
  }
  
  .score-circle {
    width: 200px;
    height: 200px;
    border-radius: 50%;
    border: 10px solid #4a90e2;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin: 2rem auto;
  }
  
  .score-number {
    font-size: 3rem;
    font-weight: bold;
    color: #4a90e2;
  }
  
  .score-label {
    font-size: 1.2rem;
    color: #6c757d;
  }
  
  .results-details {
    display: grid;
    gap: 1rem;
    max-width: 400px;
    margin: 2rem auto;
  }
  
  .result-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
  }
  
  .result-icon {
    font-size: 1.5rem;
  }
  
  .result-icon.correct {
    color: #28a745;
  }
  
  .result-icon.incorrect {
    color: #dc3545;
  }
  
  .pass-fail-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.8rem 1.5rem;
    border-radius: 50px;
    font-size: 1.2rem;
    font-weight: 500;
    margin-top: 2rem;
  }
  
  .pass-fail-badge.passed {
    background: #d4edda;
    color: #28a745;
  }
  
  .pass-fail-badge.failed {
    background: #f8d7da;
    color: #dc3545;
  }
  
  /* Responsive Styles */
  @media (max-width: 768px) {
    .quiz-container {
      padding: 1rem;
    }
  
    .quiz-info {
      flex-direction: column;
      gap: 1rem;
    }
  
    .navigation-buttons {
      flex-direction: column;
    }
  
    .nav-btn {
      width: 100%;
      justify-content: center;
    }
  
    .score-circle {
      width: 150px;
      height: 150px;
    }
  
    .score-number {
      font-size: 2.5rem;
    }
  }

.survey-container {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Start Survey Screen */
.start-survey {
  max-width: 600px;
  margin: 0 auto;
}

.instruction-list {
  background: #f8f9fa;
  padding: 2rem;
  border-radius: 8px;
}

.instruction-item {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
  font-size: 1.1rem;
  color: #495057;
}

.instruction-item:last-child {
  margin-bottom: 0;
}

.instruction-item svg {
  color: #0d6efd;
  margin-right: 1rem;
}

/* Completed Survey Screen */
.completed-survey {
  max-width: 600px;
  margin: 0 auto;
}

.completed-icon {
  color: #28a745;
}

.pulse {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.total-questions {
  font-size: 0.9rem;
  font-weight: 500;
}

.progress-wrapper {
  margin-bottom: 2rem;
}

.progress {
  background-color: #e9ecef;
  border-radius: 10px;
  overflow: hidden;
}

.progress-bar {
  transition: width 0.3s ease;
}

.options-list {
  margin-top: 1.5rem;
}

.option-item {
  border: 1px solid #dee2e6;
  cursor: pointer;
  transition: all 0.2s ease;
}

.option-item:hover {
  background-color: #f8f9fa;
  border-color: #0d6efd;
}

.option-item.selected {
  background-color: #e7f1ff;
  border-color: #0d6efd;
  color: #0d6efd;
}

.empty-survey-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  padding: 2rem;
}

.empty-survey-content {
  text-align: center;
}

.empty-icon {
  color: #6c757d;
  margin-bottom: 1rem;
}

.bounce {
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-20px);
  }
  60% {
    transform: translateY(-10px);
  }
} 