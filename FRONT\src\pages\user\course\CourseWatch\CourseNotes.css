.course-notes-container {
  /* padding: 20px; */
  background: #fff;
  min-height: calc(100vh - 400px); /* Account for header, video player, and tabs */
  display: flex;
  flex-direction: column;
}

.notes-header {
  margin-bottom: 20px;
}

.add-note-btn {
  display: flex;
  align-items: center;
  gap: 5px;
}

.note-input-container {
  background-color: #f8f9fa;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 20px;
}

.note-input {
  width: 100%;
  min-height: 100px;
  padding: 12px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  margin-bottom: 15px;
  resize: vertical;
  font-size: 14px;
}

.note-input:focus {
  outline: none;
  border-color: #3152e8;
}

.note-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.notes-list {
  flex: 1;
  overflow-y: auto;
  max-height: calc(100vh - 550px); /* Adjust based on other elements */
  padding-right: 10px; /* Space for scrollbar */
}

/* Custom scrollbar for notes list */
.notes-list::-webkit-scrollbar {
  width: 6px;
}

.notes-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.notes-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.notes-list::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.note-item {
  background: #fff;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 15px;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.note-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.note-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 10px;
}

.note-meta {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.note-timestamp {
  font-weight: 600;
  color: #3152e8;
  font-size: 14px;
}

.clickable-timestamp {
  cursor: pointer;
  transition: all 0.2s ease;
  padding: 2px 6px;
  border-radius: 4px;
  background-color: rgba(49, 82, 232, 0.1);
}

.clickable-timestamp:hover {
  background-color: rgba(49, 82, 232, 0.2);
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(49, 82, 232, 0.2);
}

.note-date {
  color: #666;
  font-size: 13px;
}

.note-edited {
  color: #888;
  font-size: 12px;
  font-style: italic;
}

.note-text {
  color: #333;
  line-height: 1.5;
  margin: 0;
  font-size: 14px;
  white-space: pre-wrap;
}

.empty-notes {
  text-align: center;
  padding: 40px 20px;
  color: #666;
  background: #f8f9fa;
  border-radius: 8px;
  margin-top: 20px;
}

.empty-notes p {
  margin: 0;
  font-size: 15px;
}

.no-module-message {
  text-align: center;
  padding: 40px 20px;
  color: #666;
  background: #f8f9fa;
  border-radius: 8px;
  font-size: 15px;
}

/* Button styles */
.note-actions button {
  padding: 6px 8px;
  margin-left: 8px;
  transition: all 0.2s ease;
}

.note-actions button:hover {
  opacity: 0.8;
}

.edit-icon, .delete-icon {
  width: 18px;
  height: 18px;
}

/* Dark mode support */
[data-theme='dark'] .note-item {
  background: var(--bg-secondary);
}

[data-theme='dark'] .btn-icon:hover {
  background-color: var(--bg-tertiary);
}
