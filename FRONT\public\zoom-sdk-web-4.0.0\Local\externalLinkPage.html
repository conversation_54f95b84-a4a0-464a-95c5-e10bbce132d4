<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta http-equiv="X-UA-Compatible" content="ie=edge" />
    <title>External link warning</title>
    <style>
      body {
        margin: 0 !important;
      }
      .logo-container {
        height: 56px;
        box-shadow: 0px 1px 0px rgba(0, 0, 0, 0.1);
        display: flex;
        align-items: center;
      }
      #logo {
        margin-left: 24px;
      }
      #warning {
        margin-top: 15px;
        margin-bottom: 42px;
      }
      .container {
        width: 506px;
        margin: 24px auto 0 auto;
        text-align: center;
        margin-top: 80px;
      }
      .header {
        font-family: Lato;
        font-style: normal;
        font-weight: 700;
        font-size: 24px;
        line-height: 24px;
        color: #232333;
      }
      .text {
        margin-top: 32px;
        margin-bottom: 24px;
        font-size: 14px;
        font-family: Lato;
        text-align: left;
      }
      .button {
        font-family: Lato;
        font-weight: bold;
        font-size: 16px;
        background-color: #0E72ED;
        color: white;
        border-radius: 8px;
        padding: 9px 15px;
        text-decoration: none;
        display: inline-block;
      }
      .urlArea {
        word-break: break-all;
        margin-bottom: 34px;
        display: flex;
        justify-content: center;
        align-items: flex-start;
        padding: 8px 16px;
        border: 1px solid rgba(82, 82, 128, 0.18);
        border-radius: 8px;
      }
      .iconImage {
        width: 16px;
        height: 16px;
        margin-top: 1px;
      }
      .url {
        font-size: 14px;
        text-align: left;
        margin-left: 10px;
        font-family: Lato;
        color: rgba(4, 4, 19, 0.56);
      }
      .center {
        text-align: center;
      }
    </style>
  </head>

  <body>
    <div class="logo-container">
      <svg id="logo" width="90" height="20" viewBox="0 0 90 20" fill="none"> <path fill-rule="evenodd" clip-rule="evenodd" d="M36.1691 17.0711C40.0314 13.1658 40.0314 6.83418 36.1691 2.92895C34.2395 0.97793 31.711 0.00161441 29.1694 0C26.6404 0.00161441 24.1119 0.97793 22.1824 2.92895C18.32 6.83418 18.32 13.1658 22.1824 17.0711C26.0447 20.9763 32.3068 20.9763 36.1691 17.0711ZM33.3717 14.2425C35.6891 11.8993 35.6891 8.10037 33.3717 5.75722C31.0543 3.41406 27.2971 3.41406 24.9797 5.75722C22.6623 8.10037 22.6623 11.8993 24.9797 14.2425C27.2971 16.5856 31.0543 16.5856 33.3717 14.2425ZM57.4327 2.92895C61.2951 6.83418 61.2951 13.1658 57.4327 17.0711C53.5704 20.9763 47.3084 20.9763 43.446 17.0711C39.5837 13.1658 39.5837 6.83418 43.446 2.92895C45.3756 0.97793 47.9041 0.00161441 50.4331 0C52.9747 0.00161441 55.5032 0.97793 57.4327 2.92895ZM54.6354 5.75722C56.9528 8.10037 56.9528 11.8993 54.6354 14.2425C52.318 16.5856 48.5607 16.5856 46.2434 14.2425C43.9259 11.8993 43.9259 8.10037 46.2434 5.75722C48.5607 3.41406 52.318 3.41406 54.6354 5.75722ZM74.1262 8C74.0879 7.24898 73.9816 6.58351 73.6428 5.99375C72.9579 4.80159 71.6813 4 70.2196 4C68.7592 4 67.4837 4.80005 66.7983 5.99029C66.4583 6.58083 66.3547 7.24786 66.313 8L66.2635 9V16L66.2141 17.0004C66.1495 18.6605 64.9483 19.8401 63.2965 19.95L62.3075 20V0C63.2965 0 65.019 0.505638 65.7885 1.37131C67.0527 0.505638 68.5777 0 70.2196 0C72.5827 0 74.7039 1.04751 76.1536 2.70835C77.6034 1.04751 79.7246 0 82.0877 0C86.4574 0 89.9998 3.58172 89.9998 8V9.00903V20L89.0117 19.95C87.3775 19.8542 86.1958 18.644 86.0932 16.999L86.0437 16V8.99893L85.9943 8C85.9551 7.26721 85.8509 6.58767 85.514 5.99912C84.8299 4.804 83.5516 4 82.0877 4C80.629 4 79.3547 4.79826 78.6688 5.98632C78.3273 6.57775 78.2197 7.25832 78.1811 8L78.1317 9V20L77.1436 19.95C75.5118 19.8455 74.3229 18.6344 74.2251 16.999L74.1756 16V9L74.1262 8ZM4.94506 20L3.95604 19.95C2.31347 19.8406 1.13603 18.6476 1.03846 16.9991L0.989011 16L12.8571 4H3.95604L2.96583 3.95C1.34815 3.85556 0.177592 2.62595 0.0494498 0.999056L0 7.42403e-06L14.8352 0.000912409L15.8241 0.0499992C17.4625 0.137543 18.6634 1.34167 18.7418 3.00124L18.7912 4L6.92308 16H15.8242L16.8132 16.05C18.4453 16.1531 19.5984 17.3544 19.7308 19.0009L19.7802 20H4.94506Z" fill="#0E71EB"/> </svg>
    </div>
    <div class="container">
      <svg id="warning" width="78" height="70" viewBox="0 0 78 70" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path fill-rule="evenodd" clip-rule="evenodd" d="M68.8937 70.0002H9.1062C2.79639 70.0002 -1.17775 63.205 1.9157 57.7055L31.8095 4.56106C34.9635 -1.04607 43.0364 -1.04607 46.1905 4.56106L76.0842 57.7055C79.1777 63.205 75.2035 70.0002 68.8937 70.0002ZM35.4254 41.2167V24.9917C35.4254 23.0172 37.0259 21.4167 39.0004 21.4167C40.9748 21.4167 42.5754 23.0172 42.5754 24.9917V41.2167C42.5754 43.1911 40.9748 44.7917 39.0004 44.7917C37.0259 44.7917 35.4254 43.1911 35.4254 41.2167ZM39.0001 59.4635C41.281 59.4635 43.1301 57.6144 43.1301 55.3334C43.1301 53.0525 41.281 51.2034 39.0001 51.2034C36.7191 51.2034 34.8701 53.0525 34.8701 55.3334C34.8701 57.6144 36.7191 59.4635 39.0001 59.4635Z" fill="#DE2828"/>
      </svg>

      <h1 id="header" class="header">
        Potential risk in this link
      </h1>
      <p id="mention" class="text">
        This website might be trying to steal your personal information. Make sure the URL is valid and copy and paste the link below to continue to this site.
      </p>
      <div class="urlArea">
        <img class="iconImage" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAxCAYAAACcXioiAAAACXBIWXMAABYlAAAWJQFJUiTwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAN+SURBVHgB7Zi/T9tAFMffnc8Ip1JaOUOlhqFSWSrB0AHGdujAWoYO7diVf4WlAytjWTs2a0cYYWCAjVQFSpCoigO27/peHJNUcXznn0v9WQjxxf6+e+f3C6ChoaGh4X+GQe0sOQDeihDKVYo5jCkvCFQf4NEpwJkHGanRgLbLuf2Bc7Y8b4VSsB+G9z2AmwEYUosBtt1ZQ3Gb+DjHYPkAPbILcNU3WAsWVEwknn1E8Xb8nZTqBI/OfhCER4yxc7xuMwbt8WWHc3glZesYj9pv3f0r9cBEfISUMJCS7QFcnsyu7qxzrjY45+74C/TE4rbuvajMAwni+1Iufgb4eZH8C6+vlH2EBqyPvYWeuPel9E7TnsOhAuaI39FHmRvcdbE3+Z+9AQ2lG7Cw8HQln/iY80OMR+O19NI73bTVpRuAYXA1/pxdfIRS8jD+bNutZ2lrBZRMGIY9zq0u7aKUzm6e5JSF0g2gc4w7vw2FmOQL35fDtJWVvMRFwZwwde6HqVm5Ag8kseRY1vCdUsqV0t9LKxWEePIa93WUCyhvUHhNu3PuPEChEqC1qVQbXfznAlLECzHcwl19iVnXZcxylLo9Sl7b6WIeeMjaYai+6gzI5YE4zmPKB86DbhDAIaSIxw8PRwJFnc4TLwRsxec/2v2rfdCQ+R2YTVJsznGYFY/iesmiZsVLeb8DBmQyYH6GNRMfBJffwFi8WUltXMyZlwf1iSeMPGBeHmQRTxFHfSoi3tCANoW+TdCKx5BmeRum4sf3giLiCW0UsqwFEuVOPQjLg18G5UG6+Oh+wQ7nYg3/HuQRT2gNwM5pNX5VomZk/oPC0OkJcYtNOrtGI7UhMCqfoQcF0Bjw+MW/cTmpk5rmzCsqKCup74Bti7i9Q09IoyY7GRqlLJk09JnJkIl5zrKYQqU3CpWMdb74/tUBlEiGRCZz7OBMnO9CyaQa4Pv+Q92ChdhytmMwm6TC0P8OJaOpRu/w2DjLVEVShciYL5TyjkFL8QxriracVqrFcBw46nPRkOfYJl6nl7j1iScM+gESG3uByme2ylgLB7PiR+ShGCojxFu8/r4u8YRhMUeDWbE1NTUbQWVFNAJhOIRS7nQvW4d4IsNose1SWYGd1bpuJc0+da1jWeSYjdIME9aSxuSRcDgw6aTKosBwl0Lq3TiuS4pWg6pnQA0NDQ0NpfMXTvPlE07HVxkAAAAASUVORK5CYII=">
        <span class="url" id="2"></span>
      </div>
    </div>
  </body>
  <script>
    var address ='';
    function getQueryString() {
      var tmpArr = window.location.href.split('?ref=');
      var tmpUrl = tmpArr.slice(1, tmpArr.length).join('?ref=');
      try {
        return decodeURIComponent(tmpUrl);
      } catch(e) {
        return tmpUrl;
      }
    }
    function isURL(str) {
     var url = /^https:\/\/([\w-]+\.)+[\w-]+(\/[\w- .\/?%&=]*)?/i;
     return url.test(str);
    }
    function maySpoof (url) {
      if (!url) {
        return true;
      }

      var searchAdressSignCount = (url.search.match(new RegExp("@", "g")) || []).length;
      var hrefAdressSignCount = (url.href.match(new RegExp("@", "g")) || []).length;
      
      return hrefAdressSignCount !== searchAdressSignCount;
    }
    function createProceedButton(href) {
      var container = document.getElementsByClassName("container")[0];
      var btn = document.createElement("a");
      btn.className = "button";
      btn.href = href;
      btn.innerText = "Proceed";
      container.appendChild(btn);
    }
    function changeMentions() {
      var warning = document.getElementById("warning");
      var header = document.getElementById("header");
      var mention = document.getElementById("mention");
      var headerText = "You are leaving Zoom";
      var mentionText = "Click \"Proceed\" to continue to the link below.";
      warning.parentNode.removeChild(warning);
      header.innerText = headerText;
      mention.innerText = mentionText;
      mention.className = mention.className + ' ' + 'center';
    }
    function getURLInstance(ref) {
      var url = undefined;
      try {
        url = new URL(ref);
      } catch (error) {
      }
      return url;
    }
    function addDomainTip(domain) {
      var mention = document.getElementById("mention");
      mention.innerHTML = "You are accessing <b>" + domain + "</b>. " + mention.innerHTML;
    }

    var ref = getQueryString("ref");

    document.getElementById("2").innerText = ref;
    var isRefValidHttpsUrl = isURL(ref);    
    var URLInstance = getURLInstance(ref);
    if (URLInstance) {
      var notSpoof = !maySpoof(URLInstance);

      document.getElementById("2").innerText = URLInstance.href;

      if (isRefValidHttpsUrl && notSpoof) {
        changeMentions();
        createProceedButton(ref);
      }

      if (URLInstance.username || !notSpoof) {
        addDomainTip(URLInstance.hostname);  
      }
    }
    
  </script>
</html>
