<!DOCTYPE html>

<head>
    <title>Zoom MeetingSDK Local</title>
    <meta charset="utf-8" />
    <meta name="format-detection" content="telephone=no">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <meta http-equiv="origin-trial" content="">
</head>

<body>

    <script src="node_modules/react/umd/react.production.min.js"></script>
    <script src="node_modules/react-dom/umd/react-dom.production.min.js"></script>
    <script src="node_modules/redux/dist/redux.min.js"></script>
    <script src="node_modules/redux-thunk/dist/redux-thunk.min.js"></script>
    <script src="js/tool.js"></script>
    <script src="./static/meeting.min.js"></script>
    <script>
        const simd = async () => WebAssembly.validate(new Uint8Array([0, 97, 115, 109, 1, 0, 0, 0, 1, 4, 1, 96, 0, 0, 3, 2, 1, 0, 10, 9, 1, 7, 0, 65, 0, 253, 15, 26, 11]))
        simd().then((res) => {
          console.log("simd check", res);
        });
    </script>
</body>

</html>