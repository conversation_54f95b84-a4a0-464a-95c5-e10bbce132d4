import React from 'react';
import { Icon } from '@iconify/react';
import NoData from '../../../components/common/NoData';
import Loader from '../../../components/common/Loader';

const getStatusStyle = (status) => {
  switch (status) {
    case 'Pending':
      return { backgroundColor: '#ffc107', color: '#000' };
    case 'In Progress':
      return { backgroundColor: '#0dcaf0', color: '#000' };
    case 'Solved':
      return { backgroundColor: '#198754', color: '#fff' };
    case 'Cancelled':
      return { backgroundColor: '#dc3545', color: '#fff' };
    default:
      return { backgroundColor: '#6c757d', color: '#fff' };
  }
};

const formatDate = (dateString) => {
  const date = new Date(dateString);
  const now = new Date();
  const diffInMs = now - date;
  const diffInHours = diffInMs / (1000 * 60 * 60);
  const diffInDays = diffInMs / (1000 * 60 * 60 * 24);

  if (diffInHours < 1) {
    return 'Just now';
  } else if (diffInHours < 24) {
    return `${Math.floor(diffInHours)} hours ago`;
  } else if (diffInDays < 7) {
    return `${Math.floor(diffInDays)} days ago`;
  } else {
    return date.toLocaleDateString();
  }
};

function Ticket({ tickets = [], loading = false }) {
  return (
    <div className="card p-3 shadow-sm" style={{ borderRadius: '12px', minHeight: '500px' }}>
      <h5 className="mb-3">Latest Support Tickets</h5>
      
      {loading ? (
        <div className="d-flex justify-content-center align-items-center" style={{ minHeight: '400px' }}>
          <Loader />
        </div>
      ) : tickets.length === 0 ? (
        <div className="d-flex justify-content-center align-items-center" style={{ minHeight: '400px' }}>
          <NoData />
        </div>
      ) : (
        <div style={{ height: '400px', overflowY: 'auto' }}>
          {tickets.map((ticket, index) => (
            <div
              key={ticket.id || index}
              className="mb-3 p-3"
              style={{
                border: '1px solid #e1e1e1',
                background: '#f9f9f9',
                borderRadius: '8px',
                boxShadow: '0 1px 2px rgba(0,0,0,0.05)',
                position: 'relative',
              }}
            >
              <div className="d-flex justify-content-between align-items-start mb-1">
                <div className="fw-semibold" style={{ fontSize: '15px', color: '#333' }}>
                  {ticket.subject || 'No Subject'}
                </div>
                <span
                  style={{
                    ...getStatusStyle(ticket.status),
                    padding: '2px 8px',
                    borderRadius: '6px',
                    fontSize: '12px',
                    fontWeight: 500,
                    whiteSpace: 'nowrap',
                  }}
                >
                  {ticket.status || 'Unknown'}
                </span>
              </div>
              <div style={{ fontSize: '14px', color: '#555' }}>
                Ticket ID: {ticket.ticket_id || 'N/A'}
              </div>
              {/* <div style={{ fontSize: '14px', color: '#555' }}>
                Sender ID: {ticket.sender_id || 'N/A'}
              </div> */}
              <div className="text-end text-muted mt-1" style={{ fontSize: '12px' }}>
                {formatDate(ticket.createdAt)}
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}

export default Ticket;
