const { hashPassword, Validate, generateAccessToken, generateRefreshToken } = require('../../../tools/tools');
const { mysqlServerConnection } = require("../../../db/db");
const { loginValidation } = require('../../../middleware/Validator');
const { validationResult } = require('express-validator');
const bcrypt = require('bcrypt');
const { createOrganization } = require('../../db/utils/createOrganizationDb');
const admin = require('../../../helper/firbaseCongfig');
const maindb = process.env.MAIN_DB;
const webpush = require('web-push');

// Get Notifications for Super Admin
const getSuperAdminNotifications = async (req, res) => {
    const userId = req.user.id; // Assuming req.user contains the Super Admin's user information
    const page = req.body.page || 1;
    const limit = req.body.limit || 10;
    const offset = (page - 1) * limit;

    console.log("Super Admin: Request received++++++", req.data, maindb);
    console.log(`Page number: ${page}`);

    const sqlQuery = `
        SELECT * FROM ${maindb}.notifications
        WHERE is_deleted = FALSE
        ORDER BY createdAt DESC
        LIMIT ? OFFSET ?
    `;

    const placeholders = [limit, offset];

    try {
        // Fetch notifications with pagination
        const [notifications] = await mysqlServerConnection.query(sqlQuery, placeholders);

        // Fetch total count of notifications
        const countQuery = `
            SELECT COUNT(*) AS totalCount FROM ${maindb}.notifications
            WHERE is_deleted = FALSE
        `;
        const [countResult] = await mysqlServerConnection.query(countQuery);
        const totalCount = countResult[0].totalCount;

        // Calculate total pages
        const totalPages = Math.ceil(totalCount / limit);

        // Send response
        res.status(200).json({
            success: true,
            data: {
                response: {
                    notifications,
                    pagination: {
                        page,
                        limit,
                        totalPages,
                        totalCount
                    }
                }
            }
        });
    } catch (error) {
        console.error("Error while fetching Super Admin notifications:", error);
        res.status(500).json({ success: false, data: { message: 'Internal server error.' } });
    }
};

// Mark a single Notification as Read for Super Admin
const markSuperAdminNotificationAsRead = async (req, res) => {
    const userId = req.user.id; // Assuming req.user contains the Super Admin's user information
    const notificationId = req.body.notificationId;

    console.log("Super Admin ID:", notificationId);

    const sqlQuery = `
        UPDATE ${maindb}.notifications
        SET is_read = TRUE 
        WHERE id = ?
    `;

    const placeholders = [notificationId];

    try {
        const [result] = await mysqlServerConnection.query(sqlQuery, placeholders);
        
        if (result.affectedRows === 0) {
            return res.status(404).json({ success: false, data: { message: 'Notification not found.' } });
        }

        res.status(200).json({ success: true, data: { message: 'Notification marked as read.' } });
    } catch (error) {
        console.error("Error while marking Super Admin notification as read:", error);
        res.status(500).json({ success: false, data: { message: 'Internal server error.' } });
    }
};

// Mark All Notifications as Read for Super Admin
const markAllSuperAdminNotificationsAsRead = async (req, res) => {

    console.log("Super Admin ID from all Notifications as read:");

    const sqlQuery = `
        UPDATE ${maindb}.notifications
        SET is_read = TRUE 
        WHERE is_deleted = FALSE
    `;



    try {
        await mysqlServerConnection.query(sqlQuery);
        res.status(200).json({ success: true, data: { message: 'All notifications marked as read.' } });
    } catch (error) {
        console.error("Error while marking all Super Admin notifications as read:", error);
        res.status(500).json({ success: false, data: { message: 'Internal server error.' } });
    }
};

module.exports = {
    // New Super Admin APIs
    getSuperAdminNotifications,
    markSuperAdminNotificationAsRead,
    markAllSuperAdminNotificationsAsRead
};
