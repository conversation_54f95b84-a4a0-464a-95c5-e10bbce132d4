const jwt = require('jsonwebtoken');
const { mysqlServerConnection } = require('../db/db');
require('dotenv').config();
const maindb = process.env.MAIN_DB;



const getRoleByName = async (data) => {
    const query = `SELECT id FROM ${data.db_name}.roles WHERE role_type=?`;
    const [role_id] = await mysqlServerConnection.query(query, [data.type]);
    return role_id[0].id;
}


module.exports = {
    getRoleByName
}