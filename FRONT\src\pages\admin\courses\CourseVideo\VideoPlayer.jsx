import React, { useEffect, useRef, useState } from 'react';
import { useParams } from 'react-router-dom';
import { Icon } from '@iconify/react';
import CourseOverview from './CourseOverview';
import CourseComment from './CourseComment';
import CourseRating from './CourseRating';
import './VideoPlayer.css';
import { decodeData } from '../../../../utils/encodeAndEncode';
import { getVideoDetailsById, EditVideoContent } from '../../../../services/adminService';
import { usePermissions } from '../../../../context/PermissionsContext';
import { toast } from 'react-toastify';

const bitmovinKey = process.env.REACT_APP_BITMOVIN_PLAYER_KEY;

function VideoPlayer() {
  const { courseId, moduleId, contentId } = useParams();
  const decodedCourseId = decodeData(courseId);
  const decodedModuleId = decodeData(moduleId);
  const decodedContentId = decodeData(contentId);
  const { permissions } = usePermissions();

  const playerRef = useRef(null);
  const playerContainerRef = useRef(null);
  const [activeTab, setActiveTab] = useState('overview');
  const [key, setKey] = useState(0);
  const [videoData, setVideoData] = useState(null);
  const [playerInitialized, setPlayerInitialized] = useState(false);

  // Edit modal states
  const [showEditModal, setShowEditModal] = useState(false);
  const [editForm, setEditForm] = useState({
    title: '',
    description: '',
    newVideoFile: null,
    newVideoPreview: null
  });

  // Upload progress modal states
  const [showUploadModal, setShowUploadModal] = useState(false);
  const [uploadStatus, setUploadStatus] = useState('uploading'); // 'uploading', 'success', 'error'
  const [uploadProgress, setUploadProgress] = useState(0);

  // Add validation errors state
  const [errors, setErrors] = useState({
    title: '',
    description: '',
    videoFile: ''
  });

  // Fetch video details
  useEffect(() => {
    const fetchVideoDetails = async () => {
      try {
        const response = await getVideoDetailsById({
          video_id: decodedContentId,
          module_id: decodedModuleId
        });
        console.log('Video Details Response:', response);

        if (response.success) {
          setVideoData(response.data);
          // Pre-populate edit form with current data
          setEditForm({
            title: response.data.video_title || response.data.video_name || '',
            description: response.data.video_description || '',
            newVideoFile: null,
            newVideoPreview: null
          });
        }
      } catch (error) {
        console.error('Error fetching video details:', error);
      }
    };

    if (decodedContentId && decodedModuleId) {
      fetchVideoDetails();
    }
  }, [decodedContentId, decodedModuleId]);

  // Reset when content changes
  useEffect(() => {
    setActiveTab('overview');
    setKey(prevKey => prevKey + 1);
  }, [decodedContentId]);

  // Initialize Bitmovin player
  useEffect(() => {
    let script = null;
    let link = null;

    const initPlayer = () => {
      if (!window.bitmovin?.player || !playerContainerRef.current) {
        console.error('Bitmovin player not loaded or container not available');
        return;
      }

      const playerConfig = {
        key: bitmovinKey,
        playback: {
          autoplay: false,
          muted: false
        },
        ui: {
          playbackSpeedSelectionEnabled: true,
          pictureInPictureConfig: {
            enabled: true
          },
          enableFullscreen: true,
          keyboard: {
            fullscreen: true
          }
        },
        style: {
          width: '100%',
          aspectratio: '16:9',
          controls: true
        },
        adaptation: {
          preload: true
        }
      };

      try {
        const container = playerContainerRef.current;
        if (playerRef.current) {
          playerRef.current.destroy();
        }

        const player = new window.bitmovin.player.Player(container, playerConfig);
        playerRef.current = player;
        setPlayerInitialized(true);

        // Hide watermark after player is ready
        player.on('ready', () => {
          console.log('Player is ready');
          setTimeout(() => {
            const watermark = document.querySelector('.bmpui-ui-watermark');
            if (watermark) {
              watermark.style.display = 'none';
            }
          }, 100);
        });

        player.on('error', (error) => {
          console.error('Player error:', error);
        });

        player.on('fullscreenenter', () => {
          const playerContainer = playerContainerRef.current;
          if (playerContainer) {
            playerContainer.style.maxWidth = '100vw';
            playerContainer.style.maxHeight = '100vh';
          }
        });

        player.on('fullscreenexit', () => {
          const playerContainer = playerContainerRef.current;
          if (playerContainer) {
            playerContainer.style.maxWidth = '';
            playerContainer.style.maxHeight = '';
          }
        });

      } catch (error) {
        console.error('Error initializing player:', error);
      }
    };

    // Load Bitmovin scripts
    if (!document.querySelector('script[src*="bitmovinplayer.js"]')) {
      script = document.createElement('script');
      script.src = 'https://cdn.bitmovin.com/player/web/8/bitmovinplayer.js';
      script.async = true;
      script.onload = initPlayer;
      document.body.appendChild(script);
    } else {
      initPlayer();
    }

    if (!document.querySelector('link[href*="bitmovinplayer-ui.css"]')) {
      link = document.createElement('link');
      link.href = 'https://cdn.bitmovin.com/player/web/8/bitmovinplayer-ui.css';
      link.rel = 'stylesheet';
      document.head.appendChild(link);
    }

    return () => {
      if (playerRef.current) {
        playerRef.current.destroy();
        playerRef.current = null;
      }
      setPlayerInitialized(false);
    };
  }, []);

  // Load video when both player is initialized and videoData is available
  useEffect(() => {
    if (playerInitialized && playerRef.current && videoData?.video_url) {
      console.log('Loading video URL:', videoData.video_url);

      const source = {
        progressive: videoData.video_url,
        title: videoData.video_title || videoData.video_name || 'Video',
        poster: videoData.thumbnail_url || '',
        options: {
          adaptation: {
            preload: true
          },
          scaling: {
            fit: 'contain'
          }
        }
      };

      playerRef.current.load(source).then(() => {
        console.log('Video loaded successfully');
      }).catch((error) => {
        console.error('Error loading video source:', error);
      });
    }
  }, [playerInitialized, videoData]);

  const HandleEditVideo = () => {
    if (!permissions.course_module_content_management_edit) {
      toast.warning("You don't have permission to edit video content");
      return;
    }
    setShowEditModal(true);
  };

  const handleCloseEditModal = () => {
    setShowEditModal(false);
    // Reset form to current video data
    setEditForm({
      title: videoData?.video_title || videoData?.video_name || '',
      description: videoData?.video_description || '',
      newVideoFile: null,
      newVideoPreview: null
    });
  };

  const validateForm = () => {
    let isValid = true;
    const newErrors = {
      title: '',
      description: '',
      videoFile: ''
    };

    // Title validation
    if (!editForm.title.trim()) {
      newErrors.title = 'Video title is required';
      isValid = false;
    } else if (editForm.title.length > 100) {
      newErrors.title = 'Title must be less than 100 characters';
      isValid = false;
    }

    // Description validation
    if (!editForm.description.trim()) {
      newErrors.description = 'Video description is required';
      isValid = false;
    } else if (editForm.description.length > 500) {
      newErrors.description = 'Description must be less than 500 characters';
      isValid = false;
    }

    // Video file validation
    if (editForm.newVideoFile) {
      const maxSize = 150 * 1024 * 1024; // 150MB
      if (editForm.newVideoFile.size > maxSize) {
        newErrors.videoFile = 'File size must be less than 150MB';
        isValid = false;
      }
      if (!editForm.newVideoFile.type.startsWith('video/')) {
        newErrors.videoFile = 'Please upload a valid video file';
        isValid = false;
      }
    }

    setErrors(newErrors);
    return isValid;
  };

  const handleEditFormChange = (e) => {
    const { name, value } = e.target;
    
    // Check character limits
    if (name === 'title' && value.length > 100) return;
    if (name === 'description' && value.length > 500) return;
    
    setEditForm(prev => ({
      ...prev,
      [name]: value
    }));
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const handleVideoFileChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      // Clear previous error
      setErrors(prev => ({
        ...prev,
        videoFile: ''
      }));

      // Validate file type
      if (!file.type.startsWith('video/')) {
        setErrors(prev => ({
          ...prev,
          videoFile: 'Please select a valid video file'
        }));
        e.target.value = '';
        return;
      }

      // Validate file size (150MB limit)
      const maxSize = 150 * 1024 * 1024;
      if (file.size > maxSize) {
        setErrors(prev => ({
          ...prev,
          videoFile: 'File size must be less than 150MB'
        }));
        e.target.value = '';
        return;
      }

      setEditForm(prev => ({
        ...prev,
        newVideoFile: file,
        newVideoPreview: URL.createObjectURL(file)
      }));
    }
  };

  const handleSaveChanges = async () => {
    if (!validateForm()) {
      return;
    }

    try {
      // Show upload modal
      setShowUploadModal(true);
      setUploadStatus('uploading');
      setUploadProgress(0);

      const formData = new FormData();
      formData.append("video_id", decodedContentId);
      formData.append("video_title", editForm.title);
      formData.append("module_id", decodedModuleId);
      formData.append("description", editForm.description);
      formData.append("duration", videoData?.video_duration || '');

      // Simulate upload progress
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return 90;
          }
          return prev + 10;
        });
      }, 1000);

      // Add flags to indicate video status
      const videoChanged = editForm.newVideoFile !== null;

      formData.append("video_changed", videoChanged ? "true" : "false");
      formData.append("video_removed", "false");

      // Only append video file if it has changed
      if (videoChanged && editForm.newVideoFile) {
        formData.append("upload_file", editForm.newVideoFile);
        console.log("Appending video file:", editForm.newVideoFile.name, "Size:", editForm.newVideoFile.size);
      } else {
        console.log("No video file to upload, videoChanged:", videoChanged);
      }

      // Debug logging
      console.log("=== FormData Debug ===");
      for (let pair of formData.entries()) {
        if (pair[1] instanceof File) {
          console.log(`${pair[0]}:`, `File - ${pair[1].name} (${pair[1].size} bytes, ${pair[1].type})`);
        } else {
          console.log(`${pair[0]}:`, pair[1]);
        }
      }
      console.log("=== End FormData Debug ===");

      const response = await EditVideoContent(formData);

      // Clear the progress interval
      clearInterval(progressInterval);

      if (response.success) {
        // Set upload status to success
        setUploadStatus('success');
        setUploadProgress(100);
        
        // Refresh video data
        const videoResponse = await getVideoDetailsById({
          video_id: decodedContentId,
          module_id: decodedModuleId
        });
        if (videoResponse.success) {
          setVideoData(videoResponse.data);
        }
        
        // Close the modal after a short delay
        setTimeout(() => {
          setShowUploadModal(false);
          setShowEditModal(false);
          // Reset form
          setEditForm({
            title: videoResponse.data?.video_title || videoResponse.data?.video_name || '',
            description: videoResponse.data?.video_description || '',
            newVideoFile: null,
            newVideoPreview: null
          });
        }, 2000);
      } else {
        setUploadStatus('error');
        setTimeout(() => {
          setShowUploadModal(false);
        }, 2000);
      }
    } catch (error) {
      console.log(error);
      setUploadStatus('error');
      setTimeout(() => {
        setShowUploadModal(false);
      }, 2000);
    }
  };

  return (
    <>
      <div className="video-section-wrapper">
        <div className="row mb-4">
          <div className="col-12 d-flex flex-column flex-md-row justify-content-between align-items-center">
            <h4 className="mb-0">{videoData?.video_title || videoData?.video_name || 'Video Player'}</h4>
            {permissions.course_module_content_management_edit && (
              <button 
                className='edit-video-btn'
                onClick={HandleEditVideo}
                disabled={!permissions.course_module_content_management_edit}
                title={!permissions.course_module_content_management_edit ? "You don't have permission to edit video" : "Edit Video"}
              >
                <Icon icon="fluent:edit-24-regular" />
                Edit Video
              </button>
            )}
          </div>
        </div>

        <div className="video-player-container">
          <div className="player-wrapper">
            <div ref={playerContainerRef} className="bitmovin-player"></div>
          </div>
        </div>

        <div className="video-tabs">
          <div className="tab-header">
            <ul className="nav nav-tabs" role="tablist">
              <li className="nav-item">
                <button
                  className={`nav-link ${activeTab === 'overview' ? 'active' : ''}`}
                  onClick={() => setActiveTab('overview')}
                  role="tab"
                >
                  <Icon icon="fluent:info-24-regular" className="me-2" />
                  Overview
                </button>
              </li>
              <li className="nav-item">
                <button
                  className={`nav-link ${activeTab === 'comments' ? 'active' : ''}`}
                  onClick={() => setActiveTab('comments')}
                  role="tab"
                >
                  <Icon icon="fluent:comment-24-regular" className="me-2" />
                  Comments
                </button>
              </li>
              <li className="nav-item">
                <button
                  className={`nav-link ${activeTab === 'ratings' ? 'active' : ''}`}
                  onClick={() => setActiveTab('ratings')}
                  role="tab"
                >
                  <Icon icon="fluent:star-24-regular" className="me-2" />
                  Ratings
                </button>
              </li>
            </ul>
          </div>

          <div className="tab-content">
            {activeTab === 'overview' && (
              <div className="tab-pane fade show active p-0" role="tabpanel">
                <CourseOverview key={`overview-${key}`} videoData={videoData} />
              </div>
            )}

            {activeTab === 'comments' && (
              <div className="tab-pane fade show active p-0" role="tabpanel">
                <CourseComment
                  contentData={{
                    id: decodedContentId,
                    module_id: decodedModuleId
                  }}
                  player={playerRef.current}
                />
              </div>
            )}

            {activeTab === 'ratings' && (
              <div className="tab-pane fade show active p-0" role="tabpanel">
                <CourseRating
                  key={`ratings-${key}`}
                  contentData={{
                    id: decodedContentId,
                    course_id: decodedCourseId
                  }}
                />
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Edit Video Modal */}
      {showEditModal && (
        <div className="modal show d-block fade" tabIndex="-1" style={{ backgroundColor: "rgba(0,0,0,0.5)" }}>
          <div className="modal-dialog modal-lg">
            <div className="modal-content">
              <div className="modal-header">
                <h5 className="modal-title">Edit Video</h5>
                <button 
                  type="button" 
                  className="btn-close" 
                  onClick={handleCloseEditModal}
                ></button>
              </div>
              <div className="modal-body">
                {/* Video Title */}
                <div className="mb-3">
                  <label className="form-label">Video Title *</label>
                  <input
                    type="text"
                    className={`form-control ${errors.title ? 'is-invalid' : ''}`}
                    name="title"
                    value={editForm.title}
                    onChange={handleEditFormChange}
                    placeholder="Enter video title"
                    maxLength={100}
                  />
                  <div className="d-flex justify-content-between mt-1">
                    <small className="text-muted">
                      Maximum 100 characters allowed ({100 - editForm.title.length} remaining)
                    </small>
                    {errors.title && (
                      <small className="text-danger">{errors.title}</small>
                    )}
                  </div>
                </div>

                {/* Video Description */}
                <div className="mb-3">
                  <label className="form-label">Video Description *</label>
                  <textarea
                    className={`form-control ${errors.description ? 'is-invalid' : ''}`}
                    name="description"
                    value={editForm.description}
                    onChange={handleEditFormChange}
                    placeholder="Enter video description"
                    rows="3"
                    maxLength={500}
                  />
                  <div className="d-flex justify-content-between mt-1">
                    <small className="text-muted">
                      Maximum 500 characters allowed ({500 - editForm.description.length} remaining)
                    </small>
                    {errors.description && (
                      <small className="text-danger">{errors.description}</small>
                    )}
                  </div>
                </div>

                {/* Upload New Video */}
                <div className="mb-3">
                  <label className="form-label">Upload New Video</label>
                  <div className={`border rounded p-4 text-center ${errors.videoFile ? 'border-danger' : ''}`}>
                    <Icon icon="fluent:video-add-24-regular" width="48" height="48" className="text-primary mb-2" />
                    <p className="mb-2 text-primary">Upload New Video</p>
                    <p className="text-muted small mb-3">Maximum file size: 150MB</p>
                    <input
                      type="file"
                      className={`form-control ${errors.videoFile ? 'is-invalid' : ''}`}
                      accept="video/*"
                      onChange={handleVideoFileChange}
                    />
                    {errors.videoFile && (
                      <div className="text-danger small mt-2">{errors.videoFile}</div>
                    )}
                    {editForm.newVideoPreview && (
                      <div className="mt-3">
                        <p className="text-success small">New video selected: {editForm.newVideoFile?.name}</p>
                      </div>
                    )}
                  </div>
                </div>

                {/* Current Video Preview */}
                <div className="mb-3">
                  <label className="form-label">Current Video</label>
                  <div className="border rounded p-3">
                    {editForm.newVideoPreview ? (
                      <div>
                        <video
                          src={editForm.newVideoPreview}
                          className="w-100"
                          style={{ maxHeight: '200px' }}
                          controls
                          preload="metadata"
                        >
                          Your browser does not support the video tag.
                        </video>
                        <div className="mt-2">
                          <small className="text-muted d-block">
                            File: {editForm.newVideoFile?.name || 'Untitled'}
                          </small>
                          <small className="text-muted d-block">
                            Size: {(editForm.newVideoFile?.size / (1024 * 1024)).toFixed(2)} MB
                          </small>
                          <small className="text-muted d-block">
                            Type: {editForm.newVideoFile?.type || 'Unknown'}
                          </small>
                          <div className="mt-2">
                            <span className="badge bg-info">New Video Selected</span>
                          </div>
                        </div>
                      </div>
                    ) : videoData?.video_url ? (
                      <div>
                        <video
                          src={videoData.video_url}
                          className="w-100"
                          style={{ maxHeight: '200px' }}
                          controls
                          preload="metadata"
                        >
                          Your browser does not support the video tag.
                        </video>
                        <div className="mt-2">
                          <small className="text-muted d-block">
                            Duration: {videoData.video_duration || 'Unknown'}
                          </small>
                          <small className="text-muted d-block">
                            File: {videoData.video_title || videoData.video_name || 'Untitled'}
                          </small>
                        </div>
                      </div>
                    ) : (
                      <div className="text-center py-3">
                        <Icon icon="fluent:video-24-regular" width="32" height="32" className="text-muted" />
                        <p className="text-muted mb-0">No video available</p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
              <div className="modal-footer">
                <button 
                  type="button" 
                  className="btn btn-secondary" 
                  onClick={handleCloseEditModal}
                >
                  Cancel
                </button>
                <button 
                  type="button" 
                  className="btn btn-primary"
                  onClick={handleSaveChanges}
                >
                  Save Changes
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Upload Progress Modal */}
      {showUploadModal && (
        <div className="modal show d-block fade" tabIndex="-1" style={{ backgroundColor: "rgba(0,0,0,0.8)" }}>
          <div className="modal-dialog modal-dialog-centered">
            <div className="modal-content">
              <div className="modal-body text-center p-4">
                {uploadStatus === 'uploading' && (
                  <>
                    <div className="mb-3">
                      <Icon icon="fluent:cloud-arrow-up-24-regular" width="48" height="48" className="text-primary" />
                    </div>
                    <h5 className="mb-3">Uploading Video...</h5>
                    <div className="progress mb-3" style={{ height: '20px' }}>
                      <div 
                        className="progress-bar progress-bar-striped progress-bar-animated" 
                        role="progressbar" 
                        style={{ width: `${uploadProgress}%` }}
                        aria-valuenow={uploadProgress} 
                        aria-valuemin="0" 
                        aria-valuemax="100"
                      >
                        {uploadProgress}%
                      </div>
                    </div>
                    <p className="text-muted mb-0">Please wait while we process your video...</p>
                  </>
                )}

                {uploadStatus === 'success' && (
                  <>
                    <div className="mb-3">
                      <Icon icon="fluent:checkmark-circle-24-filled" width="48" height="48" className="text-success" />
                    </div>
                    <h5 className="text-success mb-3">Upload Successful!</h5>
                    <p className="text-muted mb-0">Your video has been updated successfully.</p>
                  </>
                )}

                {uploadStatus === 'error' && (
                  <>
                    <div className="mb-3">
                      <Icon icon="fluent:error-circle-24-filled" width="48" height="48" className="text-danger" />
                    </div>
                    <h5 className="text-danger mb-3">Upload Failed</h5>
                    <p className="text-muted mb-0">There was an error uploading your video. Please try again.</p>
                  </>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
}

export default VideoPlayer;
