import React, { useState, useEffect } from 'react';
import { Icon } from '@iconify/react';
import { getFullModulegetFullModuleHierarchyWithActionsHierarchy, updateRolePermission, insertRoleAndPermissions, deleteRoleAndPermissions } from '../../../services/adminService';
import { toast } from 'react-toastify';
import { Modal } from 'react-bootstrap';
import './Permission.css';
import { usePermissions } from '../../../context/PermissionsContext';

function Permission() {
  const { permissions } = usePermissions();
  const [roles, setRoles] = useState([]);
  const [selectedRole, setSelectedRole] = useState(null);
  const [activeModules, setActiveModules] = useState({
    parentId: null,
    childId: null,
    grandChildId: null
  });
  const [isEditMode, setIsEditMode] = useState(false);
  const [permissionChanges, setPermissionChanges] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [editedRole, setEditedRole] = useState({ name: '', description: '' });
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [roleToDelete, setRoleToDelete] = useState(null);
  const [isDeleting, setIsDeleting] = useState(false);
  const [newRole, setNewRole] = useState({
    name: '',
    description: ''
  });
  const [isCreating, setIsCreating] = useState(false);

  const getRoles = async () => {
    try {
      const response = await getFullModulegetFullModuleHierarchyWithActionsHierarchy();
      console.log('roles response ----------------', response);
      if (response.success && response.data && response.data.length > 0) {
        setRoles(response.data);
        setSelectedRole(response.data[0]);
        setEditedRole({ name: response.data[0].name, description: response.data[0].description });
      }
    } catch (error) {
      console.error('Error fetching roles:', error);
    }
  };

  useEffect(() => {
    getRoles();
  }, []);

  const toggleModule = (moduleId, level, parentId = null) => {
    setActiveModules(prev => {
      const newState = { ...prev };

      switch (level) {
        case 'parent':
          if (prev.parentId === moduleId) {
            return { parentId: null, childId: null, grandChildId: null };
          }
          return { parentId: moduleId, childId: null, grandChildId: null };

        case 'child':
          if (parentId === prev.parentId) {
            if (prev.childId === moduleId) {
              return { ...prev, childId: null, grandChildId: null };
            }
            return { ...prev, childId: moduleId, grandChildId: null };
          }
          return prev;

        case 'grandChild':
          if (parentId === prev.childId) {
            return { ...prev, grandChildId: prev.grandChildId === moduleId ? null : moduleId };
          }
          return prev;

        default:
          return prev;
      }
    });
  };

  const handleRoleSelect = (role) => {
    if (!permissions.roles_and_access_permissions_view) {
      toast.warning("You don't have permission to view role details");
      return;
    }
    setSelectedRole(role);
    setActiveModules({ parentId: null, childId: null, grandChildId: null });
    setIsEditMode(false);
    setPermissionChanges([]);
    setEditedRole({ name: role.name, description: role.description });
  };

  const handleEditToggle = () => {
    if (!permissions.roles_and_access_permissions_edit) {
      toast.warning("You don't have permission to edit roles and permissions");
      return;
    }

    if (isEditMode) {
      // Save changes
      handleSavePermissions();
    } else {
      // Enable edit mode
      setIsEditMode(true);
      setPermissionChanges([]);
      setEditedRole({ name: selectedRole.name, description: selectedRole.description });
    }
  };

  const handleRoleFieldChange = (field, value) => {
    setEditedRole(prev => ({ ...prev, [field]: value }));
  };

  const handlePermissionChange = (actionId, actionSlug, currentValue) => {
    if (!isEditMode || !permissions.roles_and_access_permissions_edit) {
      if (!permissions.roles_and_access_permissions_edit) {
        toast.warning("You don't have permission to edit permissions");
      }
      return;
    }

    const newValue = currentValue === 1 ? 0 : 1;

    // Update the permission changes array
    setPermissionChanges(prev => {
      const existingIndex = prev.findIndex(change => change.permission_id === actionId);
      if (existingIndex !== -1) {
        // Update existing change
        const newChanges = [...prev];
        newChanges[existingIndex] = {
          role_id: selectedRole.id,
          permission_id: actionId,
          is_granted: newValue
        };
        return newChanges;
      } else {
        // Add new change
        return [...prev, {
          role_id: selectedRole.id,
          permission_id: actionId,
          is_granted: newValue
        }];
      }
    });

    // Update the UI immediately
    setSelectedRole(prev => {
      const updateActionsInModule = (module) => {
        const updatedModule = { ...module };

        // Update actions in current module
        if (updatedModule.actions) {
          updatedModule.actions = updatedModule.actions.map(action =>
            action.slug === actionSlug ? { ...action, is_granted: newValue } : action
          );
        }

        // Update actions in children
        if (updatedModule.children) {
          updatedModule.children = updatedModule.children.map(child => updateActionsInModule(child));
        }

        return updatedModule;
      };

      return {
        ...prev,
        permissions: prev.permissions.map(permission => updateActionsInModule(permission))
      };
    });
  };

  const handleSavePermissions = async () => {
    if (!permissions.roles_and_access_permissions_edit) {
      toast.warning("You don't have permission to edit roles and permissions");
      return;
    }

    setIsLoading(true);
    try {
      const updateData = {
        role_id: selectedRole.id,
        name: editedRole.name,
        description: editedRole.description,
        permissionsToUpdate: permissionChanges
      };

      console.log('Updating permissions:', updateData);
      const response = await updateRolePermission(updateData);
      console.log('response ----------------', response);

      if (response.success) {
        // Get fresh data after updating permissions
        const rolesResponse = await getFullModulegetFullModuleHierarchyWithActionsHierarchy();

        if (rolesResponse.success && rolesResponse.data) {
          // Find the current role in the fresh data
          const updatedRole = rolesResponse.data.find(role => role.id === selectedRole.id);

          if (updatedRole) {
            // Update roles state with complete data
            setRoles(rolesResponse.data);
            // Update selected role with fresh data
            setSelectedRole(updatedRole);
            setEditedRole({
              name: updatedRole.name,
              description: updatedRole.description
            });
          }
        }

        toast.success(response.message || 'Role and permissions updated successfully', {
          position: "top-center",
          autoClose: 3000
        });
        setIsEditMode(false);
        setPermissionChanges([]);
      } else {
        toast.error(response.message || 'Failed to update permissions', {
          position: "top-center",
          autoClose: 5000
        });
      }
    } catch (error) {
      toast.error('An error occurred while updating permissions', {
        position: "top-center",
        autoClose: 5000
      });
      console.error('Error updating permissions:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancelEdit = () => {
    setIsEditMode(false);
    setPermissionChanges([]);
    setEditedRole({ name: selectedRole.name, description: selectedRole.description });
    // Refresh data to revert any UI changes
    getRoles();
  };

  const handleCreateRole = async () => {
    if (!permissions.roles_and_access_permissions_create) {
      toast.warning("You don't have permission to create new roles");
      return;
    }

    if (!newRole.name.trim()) {
      toast.error('Role name is required');
      return;
    }

    setIsCreating(true);
    try {
      // Add 2 second delay
      await new Promise(resolve => setTimeout(resolve, 2000));

      const response = await insertRoleAndPermissions({
        name: newRole.name.trim(),
        description: newRole.description.trim()
      });

      if (response.success) {
        // Get fresh data to ensure we have the complete role structure with permissions
        const rolesResponse = await getFullModulegetFullModuleHierarchyWithActionsHierarchy();

        if (rolesResponse.success && rolesResponse.data) {
          // Find the newly created role in the fresh data
          const newRoleData = rolesResponse.data.find(role => role.id === response.data.role_id);

          if (newRoleData) {
            // Update roles state with complete data
            setRoles(rolesResponse.data);

            // Select the newly created role
            setSelectedRole(newRoleData);
            setEditedRole({
              name: newRoleData.name,
              description: newRoleData.description
            });

            // Reset active modules when switching to new role
            setActiveModules({
              parentId: null,
              childId: null,
              grandChildId: null
            });
          }
        }

        toast.success(response.message || 'Role created successfully', {
          position: "top-center",
          autoClose: 3000
        });

        setShowCreateModal(false);
        setNewRole({ name: '', description: '' });
      } else {
        toast.error(response.message || 'Failed to create role');
      }
    } catch (error) {
      console.error('Error creating role:', error);
      toast.error(error.response.data.message || 'An error occurred while creating the role');
    } finally {
      setIsCreating(false);
    }
  };

  const handleDeleteClick = (role) => {

    setRoleToDelete(role);
    setShowDeleteModal(true);
  };

  const handleDeleteConfirm = async () => {
    if (!roleToDelete) return;

    setIsDeleting(true);
    try {
      const response = await deleteRoleAndPermissions({ role_id: roleToDelete.id });

      if (response.success) {
        toast.success(response.message || 'Role deleted successfully');
        // Refresh roles list
        await getRoles();
        // If the deleted role was selected, select the first available role
        if (selectedRole?.id === roleToDelete.id) {
          const remainingRoles = roles.filter(role => role.id !== roleToDelete.id);
          if (remainingRoles.length > 0) {
            setSelectedRole(remainingRoles[0]);
            setEditedRole({
              name: remainingRoles[0].name,
              description: remainingRoles[0].description
            });
          } else {
            setSelectedRole(null);
            setEditedRole({ name: '', description: '' });
          }
        }
      } else {
        toast.error(response.response.data.message || 'Failed to delete role');
      }
    } catch (error) {
      console.error('Error deleting role:', error);
      toast.error(error.response.data.message || 'An error occurred while deleting the role');
    } finally {
      setIsDeleting(false);
      setShowDeleteModal(false);
      setRoleToDelete(null);
    }
  };

  const renderModulePermissions = (module, level = 'parent', parentId = null) => {
    const isActive = activeModules[level === 'parent' ? 'parentId' : level === 'child' ? 'childId' : 'grandChildId'] === module.id;

    // Count granted permissions
    const grantedPermissions = module.actions?.filter(action => action.is_granted === 1) || [];
    const totalPermissions = module.actions?.length || 0;

    // Get names of granted permissions
    const grantedPermissionNames = grantedPermissions.map(action => action.name).join(', ');

    return (
      <div key={module.id} className="permission-module mb-2">
        <div
          className={`module-header d-flex justify-content-between align-items-center p-3 rounded ${isActive ? 'bg-light' : ''}`}
          style={{ cursor: 'pointer', border: '1px solid #dee2e6' }}
          onClick={() => toggleModule(module.id, level, parentId)}
        >
          <div className="d-flex align-items-center gap-3 flex-grow-1">
            <div className="d-flex align-items-center gap-2">
              <Icon
                icon={module.icon || "mdi:folder-outline"}
                className={`fs-5 ${isActive ? 'text-primary' : ''}`}
              />
              <span className="module-name fw-medium">{module.name}</span>
            </div>
            
            {/* Show permissions summary when not expanded */}
            {!isActive && grantedPermissions.length > 0 && (
              <div className="d-flex align-items-center ms-3">
                <span className="badge border border-success text-success bg-success-subtle d-inline-flex align-items-center gap-1 p-2">
                  <Icon icon="fluent:key-24-regular" width="14" height="14" />
                  {grantedPermissions.length}/{totalPermissions} Permissions
                </span>
                <small className="text-muted text-truncate ms-2" style={{ maxWidth: '300px' }}>
                  {grantedPermissionNames}
                </small>
              </div>
            )}
          </div>
          
          <div className="d-flex align-items-center gap-2">
            {/* Show count badge */}
            {isActive && (
              <span className="badge border border-primary text-primary bg-primary-subtle d-inline-flex align-items-center gap-1 p-2">
                <Icon icon="fluent:lock-closed-24-regular" width="14" height="14" />
                {grantedPermissions.length}/{totalPermissions}
              </span>
            )}
            <Icon
              icon={isActive ? "mdi:chevron-up" : "mdi:chevron-down"}
              className="fs-5"
            />
          </div>
        </div>

        {isActive && (
          <div className="module-permissions p-3 border border-top-0 rounded-bottom">
            <div className="row g-3">
              {module.actions?.map((action) => (
                <div key={action.slug} className="col-md-6">
                  <div className="permission-action p-2 rounded hover-bg-light">
                    <label className="d-flex align-items-center gap-2 mb-0" style={{ cursor: 'pointer' }}>
                      <input
                        type="checkbox"
                        checked={action.is_granted === 1}
                        onChange={() => handlePermissionChange(action.id, action.slug, action.is_granted)}
                        disabled={!isEditMode}
                        className="form-check-input"
                      />
                      <div>
                        <div className="action-name">{action.action_name}</div>
                        <small className="text-muted d-block">{action.description}</small>
                      </div>
                    </label>
                  </div>
                </div>
              ))}
            </div>
            
            {module.children?.map((childModule) =>
              renderModulePermissions(
                childModule,
                level === 'parent' ? 'child' : level === 'child' ? 'grandChild' : 'grandChild',
                module.id
              )
            )}
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="p-4">
      <div className="d-flex justify-content-end align-items-center mb-4">
        {permissions.roles_and_access_permissions_create && (
          <button
            className="btn btn-primary w-auto"
            onClick={() => setShowCreateModal(true)}
          >
            <Icon icon="fluent:add-24-regular" className="me-2" />
            Create New Role
          </button>
        )}
      </div>

      {/* Create Role Modal */}
      <Modal show={showCreateModal} onHide={() => setShowCreateModal(false)}>
        <Modal.Header closeButton>
          <Modal.Title>Create New Role</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {!permissions.roles_and_access_permissions_create ? (
            <div className="alert alert-warning">
              <Icon icon="mdi:warning" className="me-2" />
              You don't have permission to create new roles.
            </div>
          ) : (
            <>
              <div className="alert alert-info mb-3">
                <Icon icon="mdi:information" className="me-2" />
                Create a new role first. After creation, you can assign permissions to this role.
              </div>
              <div className="mb-3">
                <label className="form-label">Role Name</label>
                <input
                  type="text"
                  className="form-control"
                  value={newRole.name}
                  onChange={(e) => setNewRole(prev => ({ ...prev, name: e.target.value }))}
                  placeholder="Enter role name"
                />
              </div>
              <div className="mb-3">
                <label className="form-label">Description</label>
                <textarea
                  className="form-control"
                  rows="3"
                  value={newRole.description}
                  onChange={(e) => setNewRole(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="Enter role description"
                />
              </div>
            </>
          )}
        </Modal.Body>
        <Modal.Footer>
          <button
            className="btn btn-secondary"
            onClick={() => setShowCreateModal(false)}
            disabled={isCreating}
          >
            Cancel
          </button>
          <button
            className="btn btn-primary"
            onClick={handleCreateRole}
            disabled={isCreating}
          >
            {isCreating ? (
              <>
                <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                Creating...
              </>
            ) : (
              'Create Role'
            )}
          </button>
        </Modal.Footer>
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal show={showDeleteModal} onHide={() => setShowDeleteModal(false)} centered>
        <Modal.Header closeButton>
          <Modal.Title className="d-flex align-items-center gap-2">
            <Icon icon="fluent:warning-24-regular" className="text-danger" width="24" height="24" />
            Confirm Deletion
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <p>Are you sure you want to delete the role <strong>{roleToDelete?.name}</strong>?</p>
          <p className="text-danger mb-0">This action cannot be undone.</p>
        </Modal.Body>
        <Modal.Footer>
          <button
            className="btn btn-light"
            onClick={() => setShowDeleteModal(false)}
            disabled={isDeleting}
          >
            Cancel
          </button>
          <button
            className="btn btn-danger d-flex align-items-center gap-2"
            onClick={handleDeleteConfirm}
            disabled={isDeleting}
          >
            {isDeleting ? (
              <>
                <span className="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                Deleting...
              </>
            ) : (
              'Delete Role'
            )}
          </button>
        </Modal.Footer>
      </Modal>

      <div className="row">
        {/* Roles List */}
        <div className="col-md-3">
          <div className="card">
            <div className="card-header">
              <h6 className="mb-0">Available Roles</h6>
            </div>
            <div className="card-body p-0">
              <div className="list-group list-group-flush">
                {roles.map((role) => (
                  <button
                    key={role.id}
                    className={`list-group-item list-group-item-action border-0 p-3 ${selectedRole?.id === role.id ? 'active bg-primary' : ''
                      }`}
                    onClick={() => handleRoleSelect(role)}
                    disabled={!permissions.roles_and_access_permissions_view}
                  >
                    <div className="d-flex align-items-center gap-2">
                      <Icon
                        icon="mdi:user-circle-outline"
                        className={`fs-4 ${selectedRole?.id === role.id ? 'text-white' : 'text-primary'}`}
                      />
                      <div>
                        <div className={`fw-medium text-capitalize mb-0 ${selectedRole?.id === role.id ? 'text-white' : ''}`}>
                          {role.name}
                        </div>
                        <small className={selectedRole?.id === role.id ? 'text-white-50' : 'text-muted'}>
                          {role.description}
                        </small>
                      </div>
                    </div>
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Permissions List */}
        <div className="col-md-9">
          <div className="card">
            <div className="card-header d-flex justify-content-between align-items-center">
              {selectedRole && (
                <h6 className="mb-0">
                  Permissions for {selectedRole.name}
                </h6>
              )}
              <div className="d-flex gap-2">
              <button
                    className="btn btn-outline-danger btn-sm"
                    onClick={() => handleDeleteClick(selectedRole)}
                    disabled={isLoading}
                  >
                    <Icon icon="fluent:delete-24-regular" className="me-1" />
                    Delete
                  </button>
                {isEditMode && permissions.roles_and_access_permissions_edit && (
                  <button
                    className="btn btn-secondary btn-sm"
                    onClick={handleCancelEdit}
                    disabled={isLoading}
                  >
                    Cancel
                  </button>
                )}
            
                {permissions.roles_and_access_permissions_edit && (
                  <button
                    className="btn btn-outline-primary btn-sm"
                    onClick={handleEditToggle}
                    disabled={isLoading}
                  >
                    {isLoading ? (
                      <>
                        <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                        Saving...
                      </>
                    ) : (
                      <>
                        <Icon icon={isEditMode ? "mdi:content-save" : "mdi:pencil"} className="me-1" />
                        {isEditMode ? 'Save' : 'Edit'}
                      </>
                    )}
                  </button>
                )}
              </div>
            </div>
            <div className="card-body p-3">
              {!permissions.roles_and_access_permissions_view ? (
                <div className="alert alert-warning">
                  <Icon icon="mdi:warning" className="me-2" />
                  You don't have permission to view role details and permissions.
                </div>
              ) : selectedRole && (
                <div className="permissions-container">
                  <div className="d-flex align-items-center gap-2 mb-4">


                  </div>
                  {isEditMode && (
                    <div className="alert alert-info mb-3">
                      <Icon icon="mdi:information" className="me-2" />
                      Edit mode is enabled. Make changes to role details and permissions, then click Save to update.
                    </div>
                  )}
                  <div className="modules-list">

                    <div className="flex-grow-1">
                      <div className="mb-2">
                        <label className="form-label mb-1 fw-medium">Role Name</label>
                        {isEditMode ? (
                          <input
                            type="text"
                            className="form-control"
                            value={editedRole.name}
                            onChange={(e) => handleRoleFieldChange('name', e.target.value)}
                            placeholder="Enter role name"
                          />
                        ) : (
                          <div className="form-control-plaintext">{selectedRole.name}</div>
                        )}
                      </div>
                      <div className="mb-2">
                        <label className="form-label mb-1 fw-medium">Description</label>
                        {isEditMode ? (
                          <textarea
                            className="form-control"
                            rows="2"
                            value={editedRole.description}
                            onChange={(e) => handleRoleFieldChange('description', e.target.value)}
                            placeholder="Enter role description"
                          />
                        ) : (
                          <div className="form-control-plaintext">{selectedRole.description}</div>
                        )}
                      </div>
                    <hr />

                    </div>
                    {selectedRole.permissions?.map((module) => renderModulePermissions(module))}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default Permission;
