.course-content {
  padding: 24px;
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  /* min-height: calc(100vh - 100px);
  height: 100%; */
  overflow-y: auto;
}

.course-content h2 {
  font-size: 24px;
  margin-bottom: 10px;
  color: #333;
}

.progress-info {
  margin: 20px 0;
  color: #666;
  font-size: 14px;
}

.progress-bar-container {
  height: 8px;
  background-color: #e0e0e0;
  border-radius: 4px;
  overflow: hidden;
  margin-top: 8px;
}

.progress-bar {
  height: 100%;
  background-color: #3152e8;
  border-radius: 4px;
  transition: width 0.3s ease;
}

.modules-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-top: 20px;
}

.module-item {
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  margin-bottom: 5px;
}

.module-header {
  padding: 18px 20px;
  background-color: #f8f9fa;
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  transition: all 0.2s ease;
  border-left: 4px solid transparent;
}

.module-header:hover {
  background-color: #f0f0f0;
}

.module-header.expanded {
  background-color: #3152e8;
  color: white;
  border-left: 4px solid #1e3ad2;
}

.module-title h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
}

.module-title p {
  margin: 5px 0 0;
  font-size: 14px;
  opacity: 0.8;
}

.module-content {
  padding: 0;
  background-color: white;
  max-height: 400px;
  min-height: 200px;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: #3152e8 #f0f0f0;
}

/* Webkit scrollbar styles */
.module-content::-webkit-scrollbar {
  width: 8px;
}

.module-content::-webkit-scrollbar-track {
  background: #f0f0f0;
  border-radius: 4px;
}

.module-content::-webkit-scrollbar-thumb {
  background: #3152e8;
  border-radius: 4px;
}

.module-content::-webkit-scrollbar-thumb:hover {
  background: #1e3ad2;
}

.content-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
  transition: all 0.2s ease;
  position: relative;
}

.content-item:hover {
  background-color: #f6f7f9;
}

.content-item.active {
  background-color: #eef1ff;
  border-left: 4px solid #3152e8;
}

.content-item.active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 4px;
  background-color: #3152e8;
}

.content-item.active .content-icon {
  background-color: rgba(49, 82, 232, 0.2);
}

.content-item.active .content-title {
  color: #3152e8;
  font-weight: 600;
}

.content-left {
  display: flex;
  align-items: center;
  gap: 14px;
  flex: 1;
}

.content-item:last-child {
  border-bottom: none;
}

.content-icon {
  color: #3152e8;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  background-color: rgba(49, 82, 232, 0.1);
  border-radius: 8px;
  transition: background-color 0.2s ease;
}

.content-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.content-title {
  font-size: 15px;
  font-weight: 500;
  color: #333;
  transition: all 0.2s ease;
}

.content-type {
  font-size: 12px;
  color: #666;
  text-transform: capitalize;
}

.content-status {
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.content-status:hover {
  background-color: #f0f0f0;
}

.content-status .completed {
  color: #4CAF50;
  filter: drop-shadow(0 0 2px rgba(76, 175, 80, 0.3));
}

.content-status .pending {
  color: #ddd;
}

.content-status:hover .pending {
  color: #bbb;
}

@media (max-width: 768px) {
  .course-content {
    padding: 15px;
  }

  .content-details {
    flex-direction: column;
    align-items: flex-start;
  }

  .content-meta {
    font-size: 11px;
  }

  .module-content {
    max-height: 300px;
    min-height: 150px;
  }
}
