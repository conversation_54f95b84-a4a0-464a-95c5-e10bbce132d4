import React, { useState, useEffect } from 'react';
import { Icon } from '@iconify/react';
import { useParams } from 'react-router-dom';
import { toast } from 'react-toastify';
import { decodeData, encodeData } from "../../../utils/encodeAndEncode";
import { getVideoDetailsById } from '../../../services/adminService';

function CourseVideo() {
    const { courseId, moduleId, contentId } = useParams();
    const decodedCourseId = decodeData(courseId);
    const decodedModuleId = decodeData(moduleId);
    const decodedContentId = decodeData(contentId);

    useEffect(() => {
        const fetchVideoDetails = async () => {
            try {
                const response = await getVideoDetailsById({
                    video_id: decodedContentId,
                    module_id: decodedModuleId
                });
                console.log('Video Details Response:', response);
                
                if (response.success) {
                    setVideoData(response.data);
                }
            } catch (error) {
                console.error('Error fetching video details:', error);
            }
        };

        fetchVideoDetails();
    }, [decodedContentId, decodedModuleId]);

    console.log('decodedCourseId', decodedCourseId);
    console.log('decodedModuleId', decodedModuleId);
    console.log('decodedContentId', decodedContentId);
    const [activeTab, setActiveTab] = useState('overview');
    const [showEditModal, setShowEditModal] = useState(false);
    const [videoData, setVideoData] = useState({
        title: 'Introduction to the Course',
        description: 'This video covers the basic concepts and course overview.',
        videoUrl: 'video-url.mp4',
        thumbnail: '/src/assets/images/course/course1.png'
    });
    const [newVideo, setNewVideo] = useState(null);

    const handleVideoRemove = () => {
        setVideoData(prev => ({
            ...prev,
            videoUrl: '',
            thumbnail: ''
        }));
    };

    const handleVideoUpload = (e) => {
        const file = e.target.files[0];
        if (file) {
            // Validate file type
            if (!file.type.startsWith('video/')) {
                toast.error('Please upload a valid video file', {
                    position: 'top-center',
                    autoClose: 3000
                });
                return;
            }

            // Validate file size (150MB limit)
            if (file.size > 150 * 1024 * 1024) {
                toast.error('File size should not exceed 150MB', {
                    position: 'top-center',
                    autoClose: 3000
                });
                return;
            }

            setNewVideo(file);
            setVideoData(prev => ({
                ...prev,
                videoUrl: URL.createObjectURL(file)
            }));
        }
    };

    const handleSaveChanges = () => {
        // Here you would typically handle the API call to save changes
        setShowEditModal(false);
    };

    return (
        <>
            <div className="row">
                <div className="col-12 mb-2 d-flex justify-content-between align-items-center">
                    <h4 className="mb-0">Course Video</h4>
                    <button 
                        className="btn btn-primary text-nowrap" 
                        style={{ width: '150px' }}
                        onClick={() => setShowEditModal(true)}
                    >
                        Edit Video
                    </button>
                </div>


                {/* Video Player */}
                <div className="col-12">
                    <div className="card">
                        <div className="card-body p-0">
                            <div style={{ maxWidth: '720px', margin: '0 auto' }}>
                                <div className="ratio ratio-16x9">
                                    <video 
                                        controls 
                                        className="w-100"
                                        poster={videoData.thumbnail}
                                    >
                                        <source src={videoData.videoUrl} type="video/mp4" />
                                        Your browser does not support the video tag.
                                    </video>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div className="col-12 mt-4">
                    <div className="card border-0">
                        <div className="card-body p-0">
                            {/* Tabs Navigation */}
                            <div className="tab-header border-bottom mb-0">
                                <ul className="nav nav-tabs">
                                    <li className="nav-item">
                                        <button
                                            className={`nav-link ${activeTab === 'overview' ? 'active' : ''}`}
                                            onClick={() => setActiveTab('overview')}
                                        >
                                            Overview
                                        </button>
                                    </li>
                                    <li className="nav-item">
                                        <button
                                            className={`nav-link ${activeTab === 'notes' ? 'active' : ''}`}
                                            onClick={() => setActiveTab('notes')}
                                        >
                                            Notes
                                        </button>
                                    </li>
                                    <li className="nav-item">
                                        <button
                                            className={`nav-link ${activeTab === 'comments' ? 'active' : ''}`}
                                            onClick={() => setActiveTab('comments')}
                                        >
                                            Comments
                                        </button>
                                    </li>
                                    <li className="nav-item">
                                        <button
                                            className={`nav-link ${activeTab === 'ratings' ? 'active' : ''}`}
                                            onClick={() => setActiveTab('ratings')}
                                        >
                                            Ratings
                                        </button>
                                    </li>
                                </ul>
                            </div>

                            {/* Tab Content */}
                            <div className="tab-content">
                                <div className={`tab-pane p-4 ${activeTab === 'overview' ? 'show active' : ''}`}>
                                    <div className="overview-content">
                                        <div className="mb-3">
                                            <h6 className="text-muted mb-2">Duration</h6>
                                            <p className="mb-0">{videoData.duration || '0:00'}</p>
                                        </div>
                                        <div className="mb-3">
                                            <h6 className="text-muted mb-2">Title</h6>
                                            <h5 className="mb-0">{videoData.title}</h5>
                                        </div>
                                        <div>
                                            <h6 className="text-muted mb-2">Description</h6>
                                            <p className="mb-0">{videoData.description}</p>
                                        </div>
                                    </div>
                                </div>

                                <div className={`tab-pane p-4 ${activeTab === 'notes' ? 'show active' : ''}`}>
                                    <h5>Notes Section</h5>
                                </div>

                                <div className={`tab-pane p-4 ${activeTab === 'comments' ? 'show active' : ''}`}>
                                    <h5>Comments Section</h5>
                                </div>

                                <div className={`tab-pane p-4 ${activeTab === 'ratings' ? 'show active' : ''}`}>
                                    <h5>Ratings Section</h5>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {/* Edit Video Modal */}
            {showEditModal && (
                <div className="modal show d-block" style={{ backgroundColor: 'rgba(0,0,0,0.5)' }}>
                    <div className="modal-dialog modal-lg">
                        <div className="modal-content">
                            <div className="modal-header">
                                <h5 className="modal-title">Edit Video</h5>
                                <button 
                                    type="button" 
                                    className="btn-close"
                                    onClick={() => setShowEditModal(false)}
                                ></button>
                            </div>
                            <div className="modal-body">
                                {/* Title and Description Fields */}
                                <div className="mb-3">
                                    <label className="form-label">Video Title</label>
                                    <input
                                        type="text"
                                        className="form-control"
                                        value={videoData.title}
                                        onChange={(e) => setVideoData(prev => ({
                                            ...prev,
                                            title: e.target.value
                                        }))}
                                    />
                                </div>
                                <div className="mb-4">
                                    <label className="form-label">Video Description</label>
                                    <textarea
                                        className="form-control"
                                        rows="3"
                                        value={videoData.description}
                                        onChange={(e) => setVideoData(prev => ({
                                            ...prev,
                                            description: e.target.value
                                        }))}
                                    ></textarea>
                                </div>

                                {/* Current Video Preview */}
                                {videoData.videoUrl ? (
                                    <div className="card mb-3">
                                        <div className="card-body">
                                            <div className="d-flex justify-content-between align-items-center mb-3">
                                                <button 
                                                    className="btn btn-outline-danger btn-sm"
                                                    onClick={handleVideoRemove}
                                                >
                                                    <Icon icon="fluent:delete-24-regular" width="16" height="16" />
                                                    Remove Video
                                                </button>
                                            </div>
                                            <div className="ratio ratio-16x9">
                                                <video 
                                                    controls 
                                                    className="rounded"
                                                    poster={videoData.thumbnail}
                                                >
                                                    <source src={videoData.videoUrl} type="video/mp4" />
                                                </video>
                                            </div>
                                        </div>
                                    </div>
                                ) : (
                                    <div className="card mb-3">
                                        <div className="card-body">
                                            <div className="text-center p-4">
                                                <input
                                                    type="file"
                                                    id="videoUpload"
                                                    className="d-none"
                                                    accept="video/*"
                                                    onChange={handleVideoUpload}
                                                />
                                                <label 
                                                    htmlFor="videoUpload" 
                                                    className="btn btn-outline-primary mb-3 d-flex flex-column align-items-center gap-2 mx-auto"
                                                    style={{ width: 'fit-content', cursor: 'pointer' }}
                                                >
                                                    <Icon icon="fluent:video-add-24-regular" width="48" height="48" />
                                                    <span>Upload Video</span>
                                                    <small className="text-muted">Maximum file size: 150MB</small>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                )}
                            </div>
                            <div className="modal-footer">
                                <button 
                                    type="button" 
                                    className="btn btn-secondary"
                                    onClick={() => setShowEditModal(false)}
                                >
                                    Cancel
                                </button>
                                <button 
                                    type="button" 
                                    className="btn btn-primary"
                                    onClick={handleSaveChanges}
                                >
                                    Save Changes
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            )}
        </>
    );
}

export default CourseVideo;
