const { mysqlServerConnection } = require('../../../db/db');

const updateNotificationSettings = async (req, res) => {
    try {
        const { mail_notification, invoice_mail, push_notification } = req.body;
        const userId = req.user.userId;
        const dbName = req.user.db_name;

        // Step 1: Check if user already exists in the table
        const checkUserQuery = `SELECT COUNT(*) AS count FROM ${dbName}.notification_settings WHERE user_id = ?`;
        const [checkResult] = await mysqlServerConnection.query(checkUserQuery, [userId]);

        if (checkResult[0].count > 0) {

            const updateQuery = `
                UPDATE ${dbName}.notification_settings
                SET mail_notification = ?, invoice_mail = ?, push_notification = ?
                WHERE user_id = ?
            `;

            const [updateResult] = await mysqlServerConnection.query(updateQuery, [
                mail_notification || 0,
                invoice_mail || 0,
                push_notification || 0,
                userId
            ]);

            return res.status(200).json({
                success: true,
                data: { message: 'Settings updated.' }
            });

        }

        const insertQuery = `
                INSERT INTO ${dbName}.notification_settings 
                (user_id, mail_notification, invoice_mail, push_notification) 
                VALUES (?, ?, ?, ?)
            `;

        const insertParams = [
            userId,
            mail_notification || 0,
            invoice_mail || 0,
            push_notification || 0
        ];

        const [insertResult] = await mysqlServerConnection.query(insertQuery, insertParams);

        return res.status(201).json({
            success: true,
            data: { message: 'Settings updated.' }
        });


    } catch (error) {
        console.error('Error updating or inserting notification settings:', error);
        return res.status(500).json({
            success: false,
            data: { error_msg: 'Internal server error.' }
        });
    }
}
// create GET API for getting notification settings
const getNotificationSettings = async (req, res) => {
  try {
    const userId = req.user.userId;
    const dbName = req.user.db_name;
    console.log('Fetching notification settings for user:', userId);

    const getQuery = `
      SELECT 
        COALESCE(mail_notification, 0) AS mail_notification,
        COALESCE(invoice_mail, 0) AS invoice_mail,
        COALESCE(push_notification, 0) AS push_notification
      FROM ${dbName}.notification_settings
      WHERE user_id = ?
    `;

    const [result] = await mysqlServerConnection.query(getQuery, [userId]);

    const settings = result.length > 0
      ? result[0]
      : {
          mail_notification: 0,
          invoice_mail: 0,
          push_notification: 0
        };

    return res.status(200).json({
      success: true,
      message: "Notification settings fetched successfully!",
      data: settings
    });

  } catch (error) {
    console.error('Error retrieving notification settings:', error);
    return res.status(500).json({
      success: false,
      data: { error_msg: 'Internal server error while retrieving notification settings.' }
    });
  }
};



module.exports = { updateNotificationSettings , getNotificationSettings };
