import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Icon } from '@iconify/react';
import { useNavigate } from 'react-router-dom';
import './Notifications.css';
import { toast } from 'react-toastify';
import { newGetNotifications, markNotificationAsRead, markAllNotificationsAsRead } from '../../../services/userService';
import NoData from '../../../components/common/NoData';
import moment from 'moment-timezone';
 
function Notifications() {
  const navigate = useNavigate();
  const [notifications, setNotifications] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('');
  const [loading, setLoading] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [error, setError] = useState(null);
  
  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [totalCount, setTotalCount] = useState(0);
  
  // Intersection Observer ref
  const observerRef = useRef();
  const lastNotificationRef = useRef();

  // Debounce search term
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
    }, 500);

    return () => clearTimeout(timer);
  }, [searchTerm]);

  const fetchNotifications = useCallback(async (page = 1, append = false) => {
    try {
      if (page === 1) {
        setLoading(true);
      } else {
        setLoadingMore(true);
      }

      const response = await newGetNotifications({
        page,
        limit: 20,
        search: debouncedSearchTerm
      });

      console.log('Notifications response:', response);
      console.log('Response type:', typeof response);
      console.log('Response keys:', response ? Object.keys(response) : 'response is null/undefined');
      console.log('Response structure:', {
        hasResponse: !!response,
        hasNotifications: !!(response && response.notifications),
        notificationsLength: response?.notifications?.length,
        pagination: response?.pagination
      });

      if (response && response.notifications) {
        const newNotifications = response.notifications || [];
        const pagination = response.pagination;

        if (append) {
          setNotifications(prev => [...prev, ...newNotifications]);
        } else {
          setNotifications(newNotifications);
        }
        
        console.log('Updated notifications state:', {
          append,
          newNotificationsLength: newNotifications.length,
          totalNotificationsAfterUpdate: append ? notifications.length + newNotifications.length : newNotifications.length
        });

        setHasMore(pagination?.hasNextPage || false);
        setTotalCount(pagination?.totalCount || 0);
        setCurrentPage(page);
      } else {
        if (!append) {
          setNotifications([]);
        }
        setHasMore(false);
      }
    } catch (error) {
      console.error('Error fetching notifications:', error);
      setError('Failed to load notifications');
      toast.error('Failed to load notifications');
    } finally {
      setLoading(false);
      setLoadingMore(false);
    }
  }, [debouncedSearchTerm]);

  // Fetch notifications on mount and when search changes
  useEffect(() => {
    console.log('useEffect triggered - debouncedSearchTerm:', debouncedSearchTerm);
    setCurrentPage(1);
    setHasMore(true);
    fetchNotifications(1, false);
  }, [debouncedSearchTerm]);

  // Load more notifications
  const loadMore = useCallback(() => {
    if (!loadingMore && hasMore) {
      fetchNotifications(currentPage + 1, true);
    }
  }, [loadingMore, hasMore, currentPage, fetchNotifications]);

  // Intersection Observer for infinite scroll
  useEffect(() => {
    if (loading) return;

    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting && hasMore && !loadingMore) {
          loadMore();
        }
      },
      { threshold: 0.1 }
    );

    observerRef.current = observer;

    if (lastNotificationRef.current) {
      observer.observe(lastNotificationRef.current);
    }

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, [loading, hasMore, loadingMore, loadMore]);

  const handleSearch = (e) => {
    setSearchTerm(e.target.value);
  };

  const handleNotificationClick = async (notification) => {
    try {
      const response = await markNotificationAsRead({ notificationId: notification.id });

      if (response.success) {
        const updatedNotifications = notifications.map(n =>
          n.id === notification.id ? { ...n, is_read: true } : n
        );
        setNotifications(updatedNotifications);
      }

      navigate(`/user/notifications/notificationDetails`, {
        state: { notification }
      });
    } catch (error) {
      console.error('Error marking notification as read:', error);
      toast.error('Failed to mark notification as read');
      navigate(`/user/notifications/notificationDetails`, {
        state: { notification }
      });
    }
  };

  const handleMarkAllAsRead = async () => {
    try {
      const response = await markAllNotificationsAsRead();

      if (response.success) {
        const updatedNotifications = notifications.map(notification => ({
          ...notification,
          is_read: true
        }));
        setNotifications(updatedNotifications);
        toast.success('All notifications marked as read');
      }
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
      toast.error('Failed to mark all notifications as read');
    }
  };

  if (loading && currentPage === 1) {
    return (
      <div className="notifications-container">
        <div className="text-center p-5">
          <div className="spinner-border text-primary" role="status">
            <span className="visually-hidden">Loading...</span>
          </div>
          <p className="mt-2">Loading notifications...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="notifications-container">
        <div className="text-center p-5 text-danger">
          {error}
        </div>
      </div>
    );
  }

  console.log('Rendering notifications component:', {
    notificationsLength: notifications.length,
    loading,
    loadingMore,
    hasMore,
    searchTerm
  });

  return (
    <>
      <div className="row align-items-center my-2">
        <div className="col-12 col-md-4">
            <input
              type="text"
              className="form-control search-input"
              placeholder="Search notifications..."
              aria-label="Search notifications"
              value={searchTerm}
              onChange={handleSearch}
            />
          </div>
        <div className="col-12 col-md-8 text-end mt-2 mt-md-0">
          <button
            className="btn btn-primary w-auto"
            onClick={handleMarkAllAsRead}
            disabled={!notifications.some(n => !n.is_read)}
          >
            <span className='d-none d-md-block'>
              <Icon icon="mdi:check-all" width="20" height="20" />&nbsp;
            </span>
            Mark all as read
          </button>
        </div>
      </div>

      <div className="row">
        <div className="container">
          <div className="notifications-container p-0 p-md-2">
            <div className="notifications-list">
              {notifications.length > 0 ? (
                notifications.map((notification, index) => (
                  <div
                    key={notification.id}
                    ref={index === notifications.length - 1 ? lastNotificationRef : null}
                    className={`notification-item ${!notification.is_read ? 'unread' : ''}`}
                    onClick={() => handleNotificationClick(notification)}
                    role="button"
                    tabIndex={0}
                  >
                    <div className="notification-content">
                      <div className="notification-title">
                        <h3>{notification.title}</h3>
                        {!notification.is_read && (
                          <span className="unread-badge">New</span>
                        )}
                      </div>
                      <p className="notification-body">{notification.body}</p>
                      <div className="notification-meta">
                        <span className="notification-date">
                          <Icon icon="mdi:clock-outline" width="16" height="16" />
                          <span>{moment.utc(notification.createdAt).local().format('MMM DD, YYYY, hh:mm A')}</span>
                        </span>
                      </div>
                    </div>
                  </div>
                ))
              ) : (
                <NoData message={searchTerm ? "No notifications found matching your search." : "No notifications found."} />
              )}
              
              {/* Loading More Indicator */}
              {loadingMore && (
                <div className="text-center py-3">
                  <div className="spinner-border spinner-border-sm text-primary" role="status">
                    <span className="visually-hidden">Loading more...</span>
                  </div>
                  <span className="ms-2">Loading more notifications...</span>
                </div>
              )}
              
              {/* End of List Indicator */}
              {!hasMore && notifications.length > 0 && (
                <div className="text-center py-3">
                  <p className="text-muted mb-0">
                    <Icon icon="mdi:check-circle" className="me-2" />
                    You've seen all notifications
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </>
  );
}

export default Notifications;
