const formatDateTimeForMySQL = (dateStr) => {
    if (!dateStr) return null;
    
    try {
        // Parse the date string
        const date = new Date(dateStr);
        
        // Check if the date is valid
        if (isNaN(date.getTime())) {
            return null;
        }
        
        // For dates with timezone info (like +05:30), we need to preserve the exact time
        // as specified in the input, not convert to UTC
        
        // Get local parts of the date
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        const seconds = String(date.getSeconds()).padStart(2, '0');
        
        // Format as YYYY-MM-DD HH:MM:SS
        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    } catch (error) {
        console.error("Error formatting date:", error);
        return null;
    }
};

module.exports = { formatDateTimeForMySQL };