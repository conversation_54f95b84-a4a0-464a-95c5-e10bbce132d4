{"name": "websdk-component-demo", "version": "4.0.0", "type": "module", "config": {"commitizen": {"path": "@commitlint/cz-commitlint"}}, "dependencies": {"@zoom/meetingsdk": "4.0.0", "lodash": "^4.17.21", "react": "18.2.0", "react-dom": "18.2.0", "react-redux": "8.1.2", "redux": "4.2.1", "redux-thunk": "2.4.2"}, "devDependencies": {"@commitlint/cli": "^19.8.1", "@commitlint/cz-commitlint": "^19.8.1", "@types/react": "^18.2.34", "@types/react-dom": "^18.2.14", "@typescript-eslint/eslint-plugin": "^6.9.1", "@typescript-eslint/parser": "^6.9.1", "@vitejs/plugin-react-swc": "^3.10.2", "commitizen": "^4.3.1", "cross-env": "^7.0.3", "eslint": "^8.52.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-airbnb-typescript": "^17.1.0", "eslint-config-prettier": "^9.1.0", "eslint-config-standard-with-typescript": "^43.0.0", "eslint-import-resolver-typescript": "^3.6.1", "eslint-plugin-import": "^2.32.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-n": "^16.2.0", "eslint-plugin-prettier": "^5.5.1", "eslint-plugin-promise": "^6.2.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-react-refresh": "^0.4.20", "husky": "^9.1.7", "lint-staged": "^15.5.1", "lodash": "^4.17.21", "mkdirp": "^3.0.1", "ncp": "^2.0.0", "prettier": "^3.6.2", "prettier-eslint": "^16.4.2", "regenerator-runtime": "^0.14.1", "rimraf": "^6.0.1", "sass": "^1.89.2", "sass-loader": "^16.0.5", "stylelint": "^16.21.1", "stylelint-config-sass-guidelines": "^12.1.0", "stylelint-config-standard-scss": "^14.0.0", "stylelint-order": "^7.0.0", "targz": "^1.0.1", "typescript": "^5.8.3", "vite": "^7.0.4", "vite-plugin-svgr": "^4.3.0"}, "husky": {"hooks": {"prepare-commit-msg": "exec < /dev/tty && npx cz --hook || true"}}, "license": "MIT", "lint-staged": {"{src,test}/**/*.{js,json}": ["prettier --write --ignore-unknown", "eslint --max-warnings=0"]}, "private": true, "scripts": {"build": "tsc && vite build", "commit": "cz", "start": "node script.js && vite", "lint": "eslint --ext ts,tsx ./src  --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint --ext ts,tsx ./src --fix", "lint:style": "stylelint **/*.scss --max-warnings 4000", "prepare": "husky install", "preview": "vite preview", "stylelint": "npx stylelint \"**/*.scss\""}}