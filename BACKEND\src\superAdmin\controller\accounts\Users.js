const { hashPassword, Validate, generateAccessToken, generateRefreshToken } = require('../../../tools/tools');
const { mysqlServerConnection } = require("../../../db/db");
const { loginValidation } = require('../../../middleware/Validator');
const { validationResult } = require('express-validator');
const bcrypt = require('bcrypt');
const { createOrganization } = require('../../db/utils/createOrganizationDb');
const admin = require('../../../helper/firbaseCongfig');
const maindb = process.env.MAIN_DB;
const webpush = require('web-push');

const firebase = require('firebase/app');
// require('firebase/messaging');


function validateEmail(email) {
    let validationMessage = null

    switch (true) {
        case !email:
            validationMessage = "Email is required.";
            break;
        case !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email):
            validationMessage = "Must be a valid email address.";
            break;
        default:
            validationMessage = null;
    }

    return validationMessage;
}

const createUser = async (req, res) => {
    try {
        const result = req.body;
        if (Object.keys(result).length == 0) {
            return res.status(422).json({
                success: false,
                data: {
                    error_msg: 'Following fields are required',
                    response: {
                        profile_pic_url: "",
                        benner_img_url: "",
                        name: "",
                        username: "",
                        password: "",
                        email: "",
                        mobile: "",
                        country: "",
                        state: "",
                        zipcode: "",
                        address: "",
                        role_id: "",
                        is_verified: true
                    }
                }
            });
        }
        const [user] = await mysqlServerConnection.query(`SELECT * FROM ${maindb}.users WHERE username = ?`, [result.username]);
        if (user.length > 0) {
            return res.status(400).json({ message: 'Organization already exists.' });
        }
        const hashedPassword = await hashPassword(result.password)
        const [authuser] = await mysqlServerConnection.query(`INSERT INTO ${maindb}.users (profile_pic_url,benner_img_url,name,username,
                        password,email,mobile,country,state,zipcode,address,is_verified,db_name) VALUES (?, ?, ?, ?,?, ?, ?, ?,?, ?, ?, ?,?)`,
            [result.profile_pic_url, result.benner_img_url, result.name, result.username, hashedPassword, result.email,
            result.mobile, result.country, result.state, result.zipcode, result.address, result.is_verified, result.db_name]);

        console.log(authuser)

        await mysqlServerConnection.query(`INSERT INTO ${maindb}.user_roles (user_id,role_id) VALUES (?,?)`, [authuser.insertId, result.role_id]);
        console.log('Role created successfully.');

        console.log('User created successfully.');

        res.status(201).json({ success: true, data: { message: 'User created successfully.' } });
    } catch (error) {
        console.error('Error creating organization:', error);
        res.status(500).json({ success: false, data: { error_msg: 'Internal server error.' } });
    }
};

const createRoles = async (req, res) => {
    try {
        const result = req.body;
        if (Object.keys(result).length == 0 || !Array.isArray(result.permission)) {
            return res.status(422).json({
                success: false,
                data: {
                    error_msg: 'Following fields are required',
                    response: {
                        name: "",
                        description: "",
                        permission: []
                    }
                }
            });
        }
        else {
            const [roles_result] = await mysqlServerConnection.query(`INSERT INTO ${maindb}.roles (name, description) VALUES (?, ?)`,
                [result.name, result.description]);

            const roleId = roles_result.insertId
            const permissionValues = result.permission.map(permissionId => [roleId, permissionId]);
            await mysqlServerConnection.query(`INSERT INTO ${maindb}.role_permissions (role_id, permission_id) VALUES ?`, [permissionValues]);
            console.log('Role created successfully.');

            res.status(201).json({
                success: true, data: {
                    message: 'Roles created successfully.',
                    response: { id: roleId, name: result.name, description: result.description, permissions: result.permission }

                }
            });
        }
    } catch (error) {
        console.error('Error creating organization:', error);
        res.status(500).json({ success: false, data: { error_msg: 'Internal server error.' } });
    }
};

const showRoles = async (req, res) => {
    try {
        // Query to get roles with their associated permissions
        const query = `
            SELECT
                r.id as role_id,
                r.name as role_name,
                r.description as role_description,
                rp.permission_id,
                p.name as permission_name
            FROM
                ${maindb}.roles r
            LEFT JOIN
                ${maindb}.role_permissions rp ON r.id = rp.role_id
            LEFT JOIN
                ${maindb}.permissions p ON rp.permission_id = p.id
        `;

        const [rows] = await mysqlServerConnection.query(query);

        const roles = rows.reduce((acc, row) => {
            let role = acc.find(r => r.id === row.role_id);
            if (!role) {
                role = {
                    id: row.role_id,
                    name: row.role_name,
                    description: row.role_description,
                    permissions: []
                };
                acc.push(role);
            }
            if (row.permission_id) {
                role.permissions.push({
                    id: row.permission_id,
                    name: row.permission_name,
                    description: row.permission_description
                });
            }
            return acc;
        }, []);

        res.status(200).json({
            success: true,
            data: {
                message: '',
                response: roles
            }
        });

    } catch (error) {
        console.error('Error showing roles:', error);
        res.status(500).json({ success: false, data: { error_msg: 'Internal server error.' } });
    }
};

const editRoles = async (req, res) => {
    try {

        const result = req.body;

        const [Setrole] = await mysqlServerConnection.query(`UPDATE ${maindb}.roles SET name = ?, description = ? WHERE id = ?`, [result.name, result.description, result.id]);

        if (Setrole.affectedRows === 0) {
            return res.status(404).json({ success: false, data: { error_msg: 'Role not found.' } });
        }

        // Retrieve the updated role
        const [updatedRole] = await mysqlServerConnection.query(`SELECT id, name, description FROM ${maindb}.roles WHERE id = ?`, [result.id]);

        res.status(200).json({
            success: true,
            data: {
                message: 'Role updated successfully.',
                response: updatedRole[0]
            }
        });
    } catch (error) {
        console.error('Error:', error);
        res.status(500).json({ success: false, data: { error_msg: 'Internal server error.' } });
    }
};

const deleteRole = async (req, res) => {
    try {
        const result = req.body;

        if (Object.keys(result).length == 0) {
            return res.status(422).json({
                success: false,
                data: {
                    error_msg: 'Role ID is required',
                    response: { role_id: [] }
                }
            });
        }

        const roleIds = result.role_id.join(',');

        // Delete role permissions
        await mysqlServerConnection.query(`DELETE FROM ${maindb}.role_permissions WHERE role_id IN (${roleIds})`);
        // Delete the roles
        const [roles_result] = await mysqlServerConnection.query(`DELETE FROM ${maindb}.roles WHERE id IN (${roleIds})`);

        if (roles_result.affectedRows === 0) {
            return res.status(404).json({
                success: false,
                data: {
                    error_msg: 'No roles found for the provided IDs'
                }
            });
        }

        res.status(200).json({
            success: true,
            data: {
                message: 'Roles and their permissions deleted successfully.'
            }
        });
    } catch (error) {
        console.error('Error deleting role:', error);
        res.status(500).json({ success: false, data: { error_msg: 'Internal server error.' } });
    }
};


const createPermission = async (req, res) => {
    try {
        const data = req.body;
        if (Object.keys(data).length == 0) {
            return res.status(422).json({
                success: false,
                data: {
                    error_msg: 'Following fields are required',
                    response: {
                        name: "",
                        description: ""
                    }
                }
            });
        }
        const [duplicateName] = await mysqlServerConnection.query(
            `SELECT name FROM ${maindb}.permissions WHERE name = ?`,
            [data.name]
        );

        if (duplicateName.length != 0) {
            res.status(400).json({
                success: false,
                data: {
                    error_msg: `'${data.name}' already available`,
                }
            });
            return;
        }


        await mysqlServerConnection.query(`INSERT INTO ${maindb}.permissions (name, description) VALUES (?, ?)`,
            [data.name, data.description]);
        console.log('Role created successfully.');

        const databases = await mysqlServerConnection.query(`SELECT db_name FROM ${maindb}.organization`);
        console.log(databases)
        for (const db of databases[0]) {
            console.log(db.db_name)
            await mysqlServerConnection.query(`INSERT INTO ${db.db_name}.permissions (name, description) VALUES (?, ?)`,
                [data.name, data.description]);
        }


        // await createdb.createOrganization(db_name);

        res.status(201).json({
            success: true, data: {
                message: 'permission created successfully.',
            }
        });
    } catch (error) {
        console.error('Error creating organization:', error);
        res.status(500).json({ success: false, data: { error_msg: 'Internal server error.' } });
    }
};

const showPermissions = async (req, res) => {
    try {

        const [permissions] = await mysqlServerConnection.query(`SELECT * FROM ${maindb}.permissions`);

        console.log(permissions)
        res.status(200).json({
            success: true, data: {
                message: '',
                permisson: permissions
            }
        });
    } catch (error) {
        console.error('Error:', error);
        res.status(500).json({ success: false, data: { error_msg: 'Internal server error.' } });
    }
};





const loginUser = async (req, res) => {
    try {
        const result = req.body;
        console.log("This is from loginUser Mohan: ", result, maindb);
        if (Object.keys(result).length == 0) {
            return res.status(422).json({
                success: false,
                data: {
                    error_msg: 'Following fields are required for this operation',
                    response: {
                        username: "",
                        password: ""
                    }
                }
            });
        }
        else {
            const validationError = Validate(req, res);
            if (validationError) return validationError;

            const [rows] = await mysqlServerConnection.query(
                `SELECT users.*,roles.id as role_id, roles.name as role
                 FROM ${maindb}.users
                 JOIN ${maindb}.user_roles ON users.id = user_roles.user_id
                 JOIN ${maindb}.roles ON user_roles.role_id = roles.id
                 WHERE users.email = ?`,
                [result.username]
            );
            console.log("ROWS : ", rows)

            console.log(rows, '000000000')

            if (rows.length > 0) {
                const user = rows[0];
                const passwordMatch = await bcrypt.compare(result.password, user.password);
                const accessToken = await generateAccessToken({ id: user.id, db_name: maindb, username: user.username, role: user.role });
                const refreshToken = await generateRefreshToken({ id: user.id, db_name: maindb, username: user.username, role: user.role });

                console.log(user)
                await mysqlServerConnection.query(`
                INSERT INTO ${maindb}.user_logs (user_id,log_name,log_description)
                VALUES (?,?,?) 
                `, [user.id, "login", `${user.name} logged in`])

                if (passwordMatch) {
                    return res.status(200).json({
                        success: true, data: {
                            message: 'User login successfully.',
                            access_token: accessToken,
                            refresh_token: refreshToken,
                            role: user.role,
                            user_details: user
                        }
                    });
                }
                else {
                    return res.status(401).json({
                        success: false, data: {
                            error_msg: 'Username or Password is incorrect'
                        }
                    });

                }
            }
            else {
                return res.status(401).json({
                    success: false, data: {
                        error_msg: 'User is not Authorized'
                    }
                });

            }
        }

    } catch (error) {
        console.error('Error creating organization:', error);
        res.status(500).json({ success: false, data: { error_msg: 'Internal server error.' } });
    }
}
const getSuperAdminProfile = async (req, res) => {
    console.log("GET PROFILE REQUEST from Super Admin");
    try {
        const [user] = await mysqlServerConnection.query(
            `SELECT  * FROM ${maindb}.users WHERE id = ?`,
            [req.user.userId]
        );

        console.log("USER :", user)

        if (user.length === 0) {
            return res.status(404).json({
                success: false,
                data: {
                    error_msg: 'User not found'
                }
            });
        }

        // Format the response to match the profile structure
        const profileData = {
            profile_pic: user[0].profile_pic_url,
            personal_information: {
                name: user[0].name,
                email: user[0].email,
                mobile: user[0].mobile,
                profession: user[0].profession
            },
            address: {
                country: user[0].country,
                state: user[0].state,
                zipcode: user[0].zipcode,
                language: user[0].language,
                address: user[0].address
            }
        };

        return res.status(200).json({
            success: true,
            data: profileData
        });
    } catch (error) {
        console.error('Error retrieving profile:', error);
        return res.status(500).json({
            success: false,
            data: {
                error_msg: 'Internal server error.'
            }
        });
    }
};

const updateSuperAdminProfile = async (req, res) => {
    try {
        const result = req.body;
        const file = req.files;

        console.log(result, '====================++++++++++================')
        // Check if req.user and maindb are populated correctly
        if (!req.user || !maindb) {
            console.error('User database information is missing.');
            return res.status(400).json({
                success: false,
                data: { error_msg: 'User database information is missing.' }
            });
        }

        // Check if the request body is empty
        if (Object.keys(result).length === 0) {
            return res.status(422).json({
                success: false,
                data: {
                    error_msg: 'The following fields are required',
                    response: {
                        profile_pic: "file",
                        name: "",
                        email: "",
                        mobile: "",
                        country: "",
                        city: "",
                        zipcode: "",
                        address: ""
                    }
                }
            });
        }

        // Validate the email format
        const emailError = validateEmail(result.email);
        if (emailError) {
            return res.status(401).json({
                success: false,
                data: {
                    error_msg: emailError
                }
            });
        }

        // Check if user already exists by email or phone number
        const [user] = await mysqlServerConnection.query(
            `SELECT * FROM ${maindb}.users WHERE email = ? OR mobile = ?`,
            [result.email, result.mobile]
        );
        console.log("USER:", user);
        if (user.length > 0) {
            // Determine profile picture URL
            let profilePicUrl = user[0].profile_pic_url; // Default to existing picture URL
            console.log("FILE :", file)
            if (file && file.profile_pic) {
                profilePicUrl = `/uploads/${file.profile_pic[0].filename}`; // Update if new picture is uploaded
            }

            // Update the user profile if the user already exists
            await mysqlServerConnection.query(
                `UPDATE ${maindb}.users 
                    SET profile_pic_url = ?, 
                        name = ?, 
                        email = ?, 
                        mobile = ?, 
                        country = ?, 
                        state = ?, 
                        zipcode = ?,
                        address = ?
                    WHERE id = ?`,
                [profilePicUrl, result.name, result.email, result.mobile, result.country, result.state, result.zipcode, result.address, req.user.userId]
            );
            console.log("Profile updated successfully");
            return res.status(200).json({ success: true, data: { message: 'Profile updated successfully.' } });
        }

    } catch (error) {
        console.error('Error updating profile:', error);
        return res.status(500).json({ success: false, data: { error_msg: 'Internal server error.' } });
    }
};

// Settings Update Password 
const updateSuperAdminPasswordAPI = async (req, res) => {
    try {
        console.log("Update Password API")
        const { currentPassword, newPassword } = req.body; // Extract current and new password from request body
        const userId = req.user.userId; // Get user ID from authenticated request (JWT token)
        const dbName = req.user.db_name; // Assuming DB name is attached to the user object
        console.log("Data from the database Super Admin Password API", currentPassword, newPassword)
        if (!currentPassword || !newPassword) {
            return res.status(400).json({
                success: false,
                data: { error_msg: 'Both current and new passwords are required.' }
            });
        }
        console.log("CURRENT PASSWORD:", currentPassword);
        console.log("NEW PASSWORD:", newPassword);
        console.log("USER ID:", userId);
        console.log("DB NAME:", dbName);
        // Step 1: Query to fetch the hashed password from the database
        const selectQuery = `
            SELECT password
            FROM ${dbName}.users
            WHERE id = ?
        `;

        // Execute the query to get the user's password
        const [rows] = await mysqlServerConnection.query(selectQuery, [userId]);
        console.log("ROWS", rows);
        // Check if user data was found
        if (rows.length === 0) {
            return res.status(404).json({
                success: false,
                data: { error_msg: 'No user found with the specified ID.' }
            });
        }

        const userData = rows[0];
        const hashedPassword = userData.password; // This is the hashed password from the database

        // Step 2: Compare current password with hashed password in the database
        const passwordMatch = await bcrypt.compare(currentPassword, hashedPassword);

        if (!passwordMatch) {
            return res.status(200).json({
                success: false,
                data: { error_msg: "Current Password is incorrect." }
            });
        }

        // Step 3: If password matches, hash the new password
        const saltRounds = 10;
        const newHashedPassword = await bcrypt.hash(newPassword, saltRounds);

        // Step 4: Update the database with the new hashed password
        const updateQuery = `
            UPDATE ${dbName}.users
            SET password = ?
            WHERE id = ?
        `;

        await mysqlServerConnection.query(updateQuery, [newHashedPassword, userId]);

        // Return success response
        return res.status(200).json({
            success: true,
            data: { message: 'Password updated successfully.' }
        });

    } catch (error) {
        console.error('Error updating password:', error);
        return res.status(500).json({
            success: false,
            data: { error_msg: 'Internal server error.' }
        });
    }
};
const getOrganizations = async (req, res) => {
    try {
        console.log("Fetching all organization details from API:", maindb);

        // Query to select all records from the organization table
        const query = `SELECT * FROM ${maindb}.organization WHERE is_deleted = false`;
        
        // Execute the query to fetch all organization details
        const [organizationDetails] = await mysqlServerConnection.query(query);

        if (organizationDetails.length === 0) {
            return res.status(404).json({
                success: false,
                data: {
                    error_msg: 'No organizations found',
                    count: 0
                }
            });
        }

        // Fetch admin details for each organization
        const organizations = await Promise.all(organizationDetails.map(async (org) => {
            try {
                let adminDetails = null;
        
                // Check if org.db_name exists and is a valid string
                if (org.db_name) {
                    const [adminResult] = await mysqlServerConnection.query(
                        `SELECT * FROM ${org.db_name}.users WHERE id = 1`
                    );
                    adminDetails = adminResult.length > 0 ? adminResult[0] : null;
                }
        
                return {
                    org_id: org.id,
                    org_name: org.name,
                    db_name: org.db_name || null, // Set to null if db_name is missing
                    org_image: org.org_logo_url,
                    org_desc: org.org_desc,
                    country: org.country,
                    state: org.state,
                    zipcode: org.zipcode,
                    city: org.city,
                    authorized_person_name: org.authorized_person,
                    authorized_person_identity: org.authorized_person_identity,
                    authorized_person_phone: org.authorized_person_phone,
                    authorized_person_email: org.authorized_person_email,
                    require_doc_url: org.require_doc_url,
                    subscription_plan: org.subscription_plan,
                    createdAt: org.createdAt,
                    updatedAt: org.updatedAt,
                    admin: adminDetails
                };
            } catch (error) {
                console.error(`Error fetching admin details for org ${org.name}:`, error);
                return {
                    org_id: org.id,
                    org_name: org.name,
                    db_name: null, // Set to null in case of an error
                    org_image: org.org_logo_url,
                    org_desc: org.org_desc,
                    country: org.country,
                    state: org.state,
                    zipcode: org.zipcode,
                    city: org.city,
                    authorized_person_name: org.authorized_person_name,
                    authorized_person_identity: org.authorized_person_identity,
                    authorized_person_phone: org.authorized_person_phone,
                    authorized_person_email: org.authorized_person_email,
                    require_doc_url: org.require_doc_url,
                    subscription_plan: org.subscription_plan,
                    createdAt: org.createdAt,
                    updatedAt: org.updatedAt,
                    admin: null // Return null if an error occurs
                };
            }
        }));
        


        res.status(200).json({
            success: true,
            data: {
                message: 'All organization details fetched successfully',
                org_count: organizations.length,
                response: organizations
            }
        });
    } catch (error) {
        console.error('Error:', error);
        res.status(500).json({ success: false, data: { error_msg: 'Internal server error.' } });
    }
};


const addOrganization = async (req, res) => {
    try {
        const result = req.body;

        console.log("Adding organization details in API:", result);
        let orgImage = req.file ? req.file.path : "default_image";
        if (orgImage !== "default_image") {
            orgImage = orgImage.replace(/^src[\\/]+app[\\/]+/, '/').replace(/\\/g, '/');
        }

        if (!result.org_name || !result.authorized_person_email || !result.sub_domain) {
            return res.status(422).json({
                success: false,
                data: { error_msg: 'Following fields are required' }
            });
        }

        const [duplicateCheck] = await mysqlServerConnection.query(
            `SELECT * FROM ${maindb}.organization WHERE authorized_person_email = ?`,
            [result.authorized_person_email]
        );

        if (duplicateCheck.length > 0) {
            return res.status(409).json({ success: false, data: { error_msg: 'Admin Email already exists' } });
        }

        // **Generate a unique db_name**
        let dbName = result.org_name.replace(/\s+/g, '_').toLowerCase();
        if (dbName.length < 4) {
            dbName += Math.random().toString(36).substring(2, 6 - dbName.length); // Ensure at least 4 chars
        }

        // **Check uniqueness in the database**
        let isUnique = false;
        while (!isUnique) {
            const [existingDb] = await mysqlServerConnection.query(
                `SELECT COUNT(*) AS count FROM ${maindb}.organization WHERE db_name = ?`,
                [dbName]
            );

            if (existingDb[0].count === 0) {
                isUnique = true;
            } else {
                dbName += Math.floor(Math.random() * 10); // Append a random number if duplicate
            }
        }

        const [org] = await mysqlServerConnection.query(
            `SELECT auth_sub_domain FROM ${maindb}.organization WHERE auth_sub_domain = ?`,
            [result.sub_domain]
        );

        if (org.length > 0) {
            return res.status(400).json({ success: false, data: { error_msg: "Domain already exist." } });
        }

        const [organization] = await mysqlServerConnection.query(
            `INSERT INTO ${maindb}.organization (
                name, db_name, org_logo_url, org_desc, country, state, zipcode, office_address,
                authorized_person, authorized_person_identity, authorized_person_phone, auth_sub_domain,
                authorized_person_email, require_doc_url, subscription_plan, is_verified, is_deleted
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
            [
                result.org_name, dbName, orgImage, result.org_desc || "Description not provided",
                result.country || null, result.state || null, result.zipcode || null,
                result.office_address || "Address not provided",
                result.authorized_person_name, result.authorized_person_identity || null,
                result.authorized_person_phone || null,
                result.sub_domain, result.authorized_person_email,
                result.require_doc_url || "Document URL not provided",
                result.subscription_plan, result.is_verified ? 1 : 0, result.is_deleted ? 1 : 0
            ]
        );

        try {
            let password = '';
            if (result.authorized_person_name && result.admin_phone_number) {
                // Extract the first name (first word before space) and capitalize the first letter
                let firstName = result.authorized_person_name.split(' ')[0];
                firstName = firstName.charAt(0).toUpperCase() + firstName.slice(1);
                console.log(firstName, '-000000-0-0-----------00000000000000')

                // Extract last 4 digits of the phone number
                let phoneLast4 = result.admin_phone_number.slice(-4);
                console.log(phoneLast4, '-000000-0-0-----------00000000000000')
                // Generate password
                password = `${firstName}@${phoneLast4}`;

            }

            // Call createOrganization with the generated password
            console.log(password, '-000000-0-0-----------00000000000000')
            await createOrganization(dbName, result.admin_email, result.authorized_person_name, password);
        } catch (dbError) {
            console.error("Error creating organization database:", dbError);
            return res.status(500).json({ success: false, data: { error_msg: "Database creation failed." } });
        }

        return res.status(201).json({
            success: true,
            data: { message: "Organization created successfully", response: result }
        });

    } catch (error) {
        console.error("Error:", error);
        return res.status(500).json({ success: false, data: { error_msg: "Internal server error." } });
    }
};





const updateOrganization = async (req, res) => {
    try {
        console.log("Updating organization details in API:", maindb);
        const result = req.body;
        console.log("Received data:", result);

        // Check if org_image is uploaded and store its path
        let orgImage = result.org_image || "default_image"; // Check if org_image exists
        if (req.file) {
            orgImage = req.file.path.replace(/^src\\app\\/, '/').replace(/\\/g, '/'); // Modify path (remove src\\app\\ and convert slashes)
        }

        // Validation: Ensure the organization ID is provided
        if (!result.org_id) {
            return res.status(400).json({
                success: false,
                data: {
                    error_msg: 'Organization ID is required for updating.'
                }
            });
        }

        // Check for duplicate fields if these fields are provided in the update request
        const [duplicateCheck] = await mysqlServerConnection.query(
            `SELECT * FROM ${maindb}.organization WHERE 
                ( authorized_person_email = ? ) 
                AND id != ?`,
            [
                result.authorized_person_email,
                result.id // Exclude current organization ID
            ]
        );

        if (duplicateCheck.length > 0) {
            let duplicateField = '';
            if (duplicateCheck.some(org => org.authorized_person_email === result.authorized_person_email)) {
                duplicateField = 'authorized_person_email';
            } 

            return res.status(400).json({
                success: false,
                data: {
                    error_msg: `${duplicateField} already exists`
                }
            });
        }

        // Prepare the values for updating fields, including defaults for optional fields
        const orgDesc = result.org_desc || "Description not provided";
        const officeAddress = result.office_address || "Address not provided";
        const requireDocUrl = result.require_doc_url || "Document URL not provided";
        const maxUsers = result.max_users || 0;
        const paymentCardNumber = result.payment_card_number || null;
        const paymentExpiryDate = result.payment_expiry_date || null;
        const paymentCvv = result.payment_cvv || null;
        const billingAddress = result.billing_address || null;
        const isVerified = result.is_verified || false;
        const isDeleted = result.is_deleted || false;

        // Update query to modify the organization details
        const [updateResult] = await mysqlServerConnection.query(
            `UPDATE ${maindb}.organization SET 
                name = ?, 
                org_logo_url = ?,  
                org_desc = ?, 
                country = ?, 
                state = ?, 
                zipcode = ?, 
                authorized_person = ?, 
                authorized_person_identity = ?, 
                authorized_person_phone = ?, 
                authorized_person_email = ?, 
                require_doc_url = ?, 
                subscription_plan = ?, 
                
                is_verified = ?, 
                is_deleted = ?
            WHERE id = ?`,
            [
                result.org_name,
                orgImage, // Save modified org_image path
                orgDesc,
                result.country,
                result.state,
                result.zipcode,
                result.authorized_person_name,
                result.authorized_person_identity,
                result.authorized_person_phone,
                result.authorized_person_email,
                requireDocUrl,
                result.subscription_plan,
                
                isVerified,
                isDeleted,
                result.org_id // Where clause with organization ID
            ]
        );

        // Check if any rows were updated
        if (updateResult.affectedRows === 0) {
            return res.status(404).json({
                success: false,
                data: {
                    error_msg: 'Organization not found or no changes detected'
                }
            });
        }

        console.log("Organization updated:", result.org_id);

        res.status(200).json({
            success: true,
            data: {
                message: 'Organization updated successfully',
                response: result
            }
        });
    } catch (error) {
        console.error('Error:', error);
        res.status(500).json({ success: false, data: { error_msg: 'Internal server error.' } });
    }
};


const activateOrganization = async (req, res, next) => {
    try {
        const { org_id } = req.params;  // Get organization ID from request parameters
        const [result] = await mysqlServerConnection.query(
            `UPDATE ${maindb}.organization SET is_verified = 1 WHERE id = ?`,
            [org_id]
        );

        console.log("Organization ID:", org_id);

        // Check if the organization was found and updated
        if (result.affectedRows === 0) {
            return res.status(404).json({
                success: false,
                message: 'Organization not found or already verified'
            });
        }

        res.status(200).json({
            success: true,
            message: 'Organization activated successfully'
        });
    } catch (error) {
        return next({ statusCode: 500, message: error.message || 'Internal Server Error' });
    }
};

const deactivateOrganization = async (req, res, next) => {
    try {
        const { org_id } = req.params;  // Get organization ID from request parameters
        const [result] = await mysqlServerConnection.query(
            `UPDATE ${maindb}.organization SET is_verified = 0 WHERE id = ?`,
            [org_id]
        );

        console.log("Organization ID:", org_id);

        // Check if the organization was found and updated
        if (result.affectedRows === 0) {
            return res.status(404).json({
                success: false,
                message: 'Organization not found or already deactivated'
            });
        }

        res.status(200).json({
            success: true,
            message: 'Organization deactivated successfully'
        });
    } catch (error) {
        return next({ statusCode: 500, message: error.message || 'Internal Server Error' });
    }
};

const deleteOrganization = async (req, res, next) => {
    try {
        const { org_id } = req.params; // Get organization ID from request parameters
        console.log("Organization ID:", org_id);
        console.log("This is from Delete org", org_id);
        // Step 1: Fetch the organization's database name
        const [organization] = await mysqlServerConnection.query(
            `SELECT db_name FROM ${maindb}.organization WHERE id = ? AND is_deleted = 0`,
            [org_id]
        );

        if (organization.length === 0) {
            return res.status(404).json({
                success: false,
                message: 'Organization not found or already deleted'
            });
        }

        const dbName = organization[0].db_name;

        // Step 2: Mark the organization as deleted
        const [updateResult] = await mysqlServerConnection.query(
            `UPDATE ${maindb}.organization SET is_deleted = 1 WHERE id = ?`,
            [org_id]
        );

        if (updateResult.affectedRows === 0) {
            return res.status(404).json({
                success: false,
                message: 'Organization not found or already deleted'
            });
        }

        // Step 3: Drop the organization's database
        if (dbName) {
            try {
                await mysqlServerConnection.query(`DROP DATABASE IF EXISTS \`${dbName}\``);
                console.log(`Database ${dbName} deleted successfully.`);
            } catch (dbError) {
                console.error(`Failed to delete database ${dbName}:`, dbError);
                return res.status(500).json({
                    success: false,
                    message: `Failed to delete the organization's database.`,
                    error: dbError.message
                });
            }
        } else {
            console.warn(`No database name found for organization ID: ${org_id}`);
        }

        // Step 4: Send success response
        res.status(200).json({
            success: true,
            message: 'Organization and its database deleted successfully'
        });

    } catch (error) {
        console.error('Error:', error);
        return next({
            statusCode: 500,
            message: error.message || 'Internal Server Error'
        });
    }
};

const getOrganizationGrowth = async (req, res) => {
    try {
        console.log("Fetching organization growth data");

        // Query to count organizations created per month in the current year
        const query = `
            SELECT 
                MONTH(createdAt) AS month,
                COUNT(*) AS growth
            FROM ${maindb}.organization
            WHERE YEAR(createdAt) = YEAR(CURDATE()) and is_deleted = false
            GROUP BY MONTH(createdAt)
            ORDER BY month
        `;

        const [growthData] = await mysqlServerConnection.query(query);

        // Initialize an array for all 12 months to ensure data for each month
        const monthlyGrowth = Array(12).fill(0);

        // Populate the monthly growth array with data from the query
        growthData.forEach(row => {
            monthlyGrowth[row.month - 1] = row.growth;
        });

        res.status(200).json({
            success: true,
            data: {
                message: 'Organization growth data fetched successfully',
                org_count: monthlyGrowth.reduce((acc, curr) => acc + curr, 0), // Total organizations for the year
                response: monthlyGrowth // Array representing growth for each month
            }
        });
    } catch (error) {
        console.error('Error fetching organization growth data:', error);
        res.status(500).json({
            success: false,
            data: {
                error_msg: 'Failed to fetch organization growth data.'
            }
        });
    }
};

const getOrganizationRequests = async (req, res) => {
    try {
        console.log("Fetching all organization requests from API");

        // Query to fetch details from both organization_requests and organization tables
        const query = `
            SELECT 
                req.id,
                req.org_id,
                req.organization_name,
                req.contact_person,
                req.contact_email,
                req.contact_phone,
                req.request_date,
                req.request_status,
                req.request_type,
                req.request_description,
                req.approved_by,
                req.approved_date,
                req.remarks,
                req.createdAt,
                req.updatedAt,
                org.org_logo_url
            FROM 
                ${maindb}.organization_requests req
            INNER JOIN 
                ${maindb}.organization org
            ON 
                req.org_id = org.id;
        `;

        // Execute the query to fetch the joined details
        const [requestDetails] = await mysqlServerConnection.query(query);

        if (requestDetails.length === 0) {
            return res.status(404).json({
                success: false,
                data: {
                    error_msg: 'No organization requests found',
                    count: 0
                }
            });
        }

        // Format the response to include both organization_requests and organization details
        const requests = requestDetails.map(request => ({
            id: request.id,
            org_id: request.org_id,
            organization_name: request.organization_name,
            contact_person: request.contact_person,
            contact_email: request.contact_email,
            contact_phone: request.contact_phone,
            request_date: request.request_date,
            request_status: request.request_status,
            request_type: request.request_type,
            request_description: request.request_description,
            approved_by: request.approved_by,
            approved_date: request.approved_date,
            remarks: request.remarks,
            org_image: request.org_logo_url,
            createdAt: request.createdAt,
            updatedAt: request.updatedAt
        }));

        res.status(200).json({
            success: true,
            data: {
                message: 'All organization requests fetched successfully',
                request_count: requests.length,
                response: requests
            }
        });
    } catch (error) {
        console.error('Error:', error);
        res.status(500).json({ success: false, data: { error_msg: 'Internal server error.' } });
    }
};


const updateRequestStatus = async (req, res, next) => {
    try {
        const { request_id } = req.params; // Get request ID from request parameters
        const { status } = req.body; // Get the new status from the request body

        // Validate the status
        if (!['Pending', 'Approved', 'Rejected'].includes(status)) {
            return res.status(400).json({
                success: false,
                message: 'Invalid status value. Allowed values are "Pending", "Approved", "Rejected"',
            });
        }
        console.log("from status API")
        // Update the request status
        const [result] = await mysqlServerConnection.query(
            `UPDATE ${maindb}.organization_requests
             SET request_status = ?, 
                 approved_date = CASE WHEN ? = 'Approved' THEN CURRENT_TIMESTAMP ELSE NULL END
             WHERE id = ?`,
            [status, status, request_id]
        );

        console.log('Request ID:', request_id);

        // Check if the request was found and updated
        if (result.affectedRows === 0) {
            return res.status(404).json({
                success: false,
                message: 'Request not found or status already updated',
            });
        }

        res.status(200).json({
            success: true,
            message: `Request status updated successfully to "${status}"`,
        });
    } catch (error) {
        return next({
            statusCode: 500,
            message: error.message || 'Internal Server Error',
        });
    }
};


const SendNotificaiton = async (req, res) => {
    // const firebaseConfig ={
    //     "apiKey": "AIzaSyAonetFvaOuUCEmu4mdonUbZ0WM-9J2Ccg",
    //     "authDomain": "lms-system-26f41.firebaseapp.com",
    //     "projectId": "lms-system-26f41",
    //     "storageBucket": "lms-system-26f41.appspot.com",
    //     "messagingSenderId": "307508237000",
    //     "appId": "1:307508237000:web:6c5af2f8d1f49ad5d58bfd",
    //     "measurementId": "G-0ZBFCLJ5FB"
    //   }

    //   firebase.initializeApp(firebaseConfig);

    // const messaging = firebase.messaging();
    // const vapidKeys = webpush.generateVAPIDKeys();
    const { token } = req.body; // Token of the device and message content
    console.log("Creating token", token);
    // const t = await messaging.getToken({ vapidKeys });
    console.log("Token +++++++++++++++++++++++++++++++++++++++++++++++++")
    const messagePayload = {
        notification: {
            title: 'Test Notification',
            body: "test body description",
        },
        token: token,
    };

    try {
        console.log("Console log", messagePayload);
        const response = await admin.messaging().send(messagePayload);
        console.log("Console log from after response")
        res.status(200).send(`Notification sent successfully: ${response}`);
    } catch (error) {
        console.error('Error sending notification:', error);
        res.status(500).send(`Error sending notification: ${error}`);
    }
}

const SendAnnouncement = async (req, res) => {
    const { token, title, body } = req.body; // Device token and announcement content from the request
    console.log("token", token);
    // Define the message payload specifically for announcements
    const messagePayload = {
        notification: {
            title: title || 'Announcement', // Default title if none provided
            body: body || 'This is an announcement.', // Default body if none provided
        },
        token: token,
    };

    try {
        console.log("Sending announcement", messagePayload);

        // Send the announcement using Firebase Admin SDK
        const response = await admin.messaging().send(messagePayload);

        console.log("Announcement sent successfully");
        res.status(200).send(`Announcement sent successfully: ${response}`);
    } catch (error) {
        console.error('Error sending announcement:', error);
        res.status(500).send(`Error sending announcement: ${error}`);
    }
};

const createBulkPermissions = async (req, res) => {

    const { permissions } = req.body;

    try {
        for (const permission of permissions) {

            await mysqlServerConnection.query(`INSERT INTO ${maindb}.permissions (name, description) VALUES (?, ?)`,
                [permission.name, permission.description]);
            console.log('Role created successfully.');

            const databases = await mysqlServerConnection.query(`SELECT db_name FROM ${maindb}.organization`);
            console.log(databases)
            for (const db of databases[0]) {
                console.log(db.db_name)
                await mysqlServerConnection.query(`INSERT INTO ${db.db_name}.permissions (name, description) VALUES (?, ?)`,
                    [permission.name, permission.description]);
            }
        }
    } catch (error) {
        res.status(500).json({
            success: true,
            message: error.message || 'Internal Server Error'
        })
    }

    res.status(201).json({
        success: true,
        message: "Added permissions successfully"
    })

}


module.exports = { createUser, createRoles, showRoles, editRoles, deleteRole, createPermission, loginUser, showPermissions, getSuperAdminProfile, updateSuperAdminProfile, updateSuperAdminPasswordAPI, getOrganizations, addOrganization, updateOrganization, activateOrganization, deactivateOrganization, deleteOrganization, getOrganizationGrowth, getOrganizationRequests, updateRequestStatus, SendNotificaiton, SendAnnouncement, createBulkPermissions, validateEmail };