
const bcrypt = require('bcrypt');
const { validationResult } = require('express-validator');
const jwt = require('jsonwebtoken');
const { mysqlServerConnection } = require('../db/db');
const multer = require('multer');
require('dotenv').config();
const nodemailer = require('nodemailer');
const path = require('path');
const maindb = process.env.MAIN_DB;



async function hashPassword(password) {
  if (password === "") {
    throw new Error('Password is required');
  }
  return await bcrypt.hash(password, 10);
}

const Validate = (req, res) => {
  const errors = validationResult(req);
  console.log(errors)
  if (!errors.isEmpty()) {
    return res.status(401).json({
      success: false,
      data: {
        error_msg: 'Validation errors',
        response: errors.array().reduce((acc, error) => {
          acc[error.path] = error.msg
          return acc
        }, {})
      }
    });
  }
  return null
}

const generateAccessToken = async (user) => {
  try {
    const [permissionsResult] = await mysqlServerConnection.query(
      `SELECT p.name 
            FROM ${user.db_name}.roles r 
            JOIN ${user.db_name}.role_permissions rp ON r.id = rp.role_id 
            JOIN ${maindb}.permissions p ON rp.permission_id = p.id 
            WHERE r.id = ?`,
      [user.role_id]
    );

    // Extract permission names
    const permissions = permissionsResult.map(permission => permission.name);

    // Include user information and permissions in the token payload
    const payload = {
      userId: user.id,
      name: user.name,
      db_name: user.db_name,
      username: user.username,
      permissions: permissions
    };
    return jwt.sign(payload, process.env.JWT_ACCESS_KEY, { expiresIn: '3d' });
  } catch (error) {
    console.error('Error generating access token:', error);
    throw new Error('Could not generate access token');
  }
};

const generateRefreshToken = async (user) => {
  const [permissionsResult] = await mysqlServerConnection.query(
    `SELECT p.name 
        FROM ${user.db_name}.roles r 
        JOIN ${user.db_name}.role_permissions rp ON r.id = rp.role_id 
        JOIN ${maindb}.permissions p ON rp.permission_id = p.id 
        WHERE r.id = ?`,
    [user.role_id]
  );

  const permissions = permissionsResult.map(permission => permission.name);
  const payload = {
    userId: user.id,
    name: user.name,
    username: user.username,
    permissions: permissions,
    db_name: user.db_name
  };
  return jwt.sign(payload, process.env.JWT_REFRESH_KEY, { expiresIn: '3d' });
}


const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, 'src/app/uploads/');
  },
  filename: (req, file, cb) => {
    cb(null, `${Date.now()}-${file.originalname}`);
  },
});

const upload = multer({ storage });

// Ensure the uploads directory exists
const fs = require('fs');
const uploadDir = 'src/app/uploads';
if (!fs.existsSync(uploadDir)) {
  fs.mkdirSync(uploadDir);
}


const deleteFileIfExists = (filePath) => {
  const resolvedPath = path.resolve(filePath);
  if (fs.existsSync(resolvedPath)) {
    try {
      fs.unlinkSync(resolvedPath);
      console.log(`File at ${resolvedPath} has been deleted.`);
    } catch (err) {
      console.error(`Error deleting file at ${resolvedPath}:`, err.message);
    }
  } else {
    console.log(`File at ${resolvedPath} does not exist.`);
  } 
};

let transporter;
// Create transporter with Gmail SMTP
if (process.env.IS_PROD === 'false') {
transporter = nodemailer.createTransport({
  service: 'gmail',
  auth: {
    user: process.env.EMAIL,
    pass: process.env.PASSWORD
  },
  debug: true, // Enable debugging
  logger: true  // Log to console
});
} else {
  // smtp for aws
  transporter = nodemailer.createTransport({
    service: 'gmail',
    auth: {
      user: process.env.PROD_EMAIL,
      pass: process.env.PROD_PASSWORD
    },
    debug: true, // Enable debugging
    logger: true  // Log to console
  })
}

// Verify transporter connection configuration
transporter.verify(function(error, success) {
  if (error) {
    console.error('SMTP Connection Error:', error);
  } else {
    console.log('SMTP Server is ready to take our messages');
  }
});

// Function to send an email
const sendEmail = (to, subject, text) => {
  console.log('Attempting to send email...');
  console.log('From:', process.env.EMAIL);
  console.log('To:', to);
  console.log('Subject:', subject);
  
  const mailOptions = {
    from: `"LMS System" <${process.env.EMAIL}>`,
    to: to,
    subject: subject,
    html: text,
    // Add headers to help with debugging
    headers: {
      'X-LAZY-MANAGER-MICROSOFT': 'outlook' // Helps with some email clients
    }
  };

  console.log('Mail options prepared, sending...');
  
  return new Promise((resolve, reject) => {
    transporter.sendMail(mailOptions, (error, info) => {
      console.log('--- Email Send Attempt ---');
      console.log('Time:', new Date().toISOString());
      
      if (error) {
        console.error('❌ Email Error:', error.message);
        console.error('Error Code:', error.code);
        console.error('Error Response:', error.response);
        console.error('Full Error:', JSON.stringify(error, null, 2));
        reject(error);
      } else {
        console.log('✅ Email sent successfully!');
        console.log('Message ID:', info.messageId);
        console.log('Preview URL:', nodemailer.getTestMessageUrl(info));
        console.log('Response:', info.response);
        resolve(info);
      }
      console.log('--------------------------');
    });
  });
};


const LogsHandler = async (db_name, user_id, logs_name, logs_decription) => {
  await mysqlServerConnection.query(`
        INSERT INTO ${db_name}.user_logs (user_id, log_name, log_description)
        VALUES (?, ?, ?) 
    `, [user_id, logs_name, logs_decription]);

}


const validateEmail = (email) => {

  let validationMessage = null



  switch (true) {

    case !email:

      validationMessage = "Email is required.";

      break;

    case !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email):

      validationMessage = "Must be a valid email address.";

      break;

    default:

      validationMessage = null;

  }



  return validationMessage;

}

const validatePassword = (password) => {

  let validationMessage = null



  switch (true) {

    case password.length < 8:

      validationMessage = "Password must be at least 8 characters long.";

      break;

    case !/\d/.test(password):

      validationMessage = "Password must contain at least one number.";

      break;

    case !/[A-Z]/.test(password):

      validationMessage = "Password must contain at least one uppercase letter.";

      break;

    case !/[!@#$%^&*(),.?":{}|<>]/.test(password):

      validationMessage = "Password must contain at least one special character.";

      break;

    default:

      validationMessage = null

  }



  return validationMessage;

}


module.exports = { hashPassword, Validate, generateAccessToken, generateRefreshToken, upload, sendEmail, storage, deleteFileIfExists, LogsHandler, validateEmail, validatePassword };