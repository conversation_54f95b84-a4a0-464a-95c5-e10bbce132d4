import React, { useState, useEffect, useCallback } from 'react'
import { Icon } from '@iconify/react';
import { toast } from 'react-toastify';
import {
  getAboutOrganization,
  addAboutOrganization,
  updateAboutOrganization,
  updateAboutOrganizationStatus,
  deleteAboutOrganization
} from '../../../services/adminService';
import Loader from '../../../components/common/Loader';
import NoData from '../../../components/common/NoData';
import { usePermissions } from '../../../context/PermissionsContext';

function AboutOrganization() {
  const { permissions } = usePermissions();
  const [showModal, setShowModal] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);
  const [editingId, setEditingId] = useState(null);
  const [loading, setLoading] = useState(false);
  const [tableLoading, setTableLoading] = useState(false);
  const [organizationData, setOrganizationData] = useState([]);
  const [pagination, setPagination] = useState({
    currentPage: 1,
    totalPages: 1,
    totalRecords: 0,
    recordsPerPage: 10
  });
  const [search, setSearch] = useState('');
  const [formData, setFormData] = useState({
    name: '',
    description: '',
  });
  const [formErrors, setFormErrors] = useState({
    name: '',
    description: ''
  });
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [deleteOrgData, setDeleteOrgData] = useState(null);
  const [statusLoading, setStatusLoading] = useState(null);

  // Constants for validation
  const MAX_NAME_LENGTH = 100;
  const MAX_DESCRIPTION_LENGTH = 500;
  const MIN_NAME_LENGTH = 3;
  const MIN_DESCRIPTION_LENGTH = 10;

  // Validation function
  const validateForm = () => {
    let isValid = true;
    const errors = {
      name: '',
      description: ''
    };

    // Name validation
    if (!formData.name.trim()) {
      errors.name = 'Name is required';
      isValid = false;
    } else if (formData.name.trim().length < MIN_NAME_LENGTH) {
      errors.name = `Name must be at least ${MIN_NAME_LENGTH} characters`;
      isValid = false;
    } else if (formData.name.trim().length > MAX_NAME_LENGTH) {
      errors.name = `Name cannot exceed ${MAX_NAME_LENGTH} characters`;
      isValid = false;
    }

    // Description validation
    if (!formData.description.trim()) {
      errors.description = 'Description is required';
      isValid = false;
    } else if (formData.description.trim().length < MIN_DESCRIPTION_LENGTH) {
      errors.description = `Description must be at least ${MIN_DESCRIPTION_LENGTH} characters`;
      isValid = false;
    } else if (formData.description.trim().length > MAX_DESCRIPTION_LENGTH) {
      errors.description = `Description cannot exceed ${MAX_DESCRIPTION_LENGTH} characters`;
      isValid = false;
    }

    setFormErrors(errors);
    return isValid;
  };

  // Fetch organization data
  const fetchOrganizationData = useCallback(async () => {
    try {
      setTableLoading(true);
      const response = await getAboutOrganization({
        page: pagination.currentPage,
        limit: pagination.recordsPerPage,
        search: search
      });

      console.log('About Organization Response', response);

      if (response.success) {
        setOrganizationData(response.data.records);
        setPagination(response.data.pagination);
      } else {
        toast.error('Failed to fetch organization data');
      }
    } catch (error) {
      console.error('Error fetching organization data:', error);
      toast.error('Error fetching organization data');
    } finally {
      setTimeout(() => {
        setTableLoading(false);
      }, 500);
    }
  }, [pagination.currentPage, pagination.recordsPerPage, search]);

  // Load data on component mount and when dependencies change
  useEffect(() => {
    fetchOrganizationData();
  }, [fetchOrganizationData]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    
    // Clear error when user starts typing
    setFormErrors(prev => ({
      ...prev,
      [name]: ''
    }));

    // Prevent typing if max length is reached
    if (
      (name === 'name' && value.length <= MAX_NAME_LENGTH) ||
      (name === 'description' && value.length <= MAX_DESCRIPTION_LENGTH)
    ) {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    // Validate form before submission
    if (!validateForm()) {
      return;
    }

    // Check permissions based on mode
    if (isEditMode && !permissions.settings_about_organization_edit) {
      toast.warning("You don't have permission to edit organization information");
      return;
    }
    if (!isEditMode && !permissions.settings_about_organization_create) {
      toast.warning("You don't have permission to create organization information");
      return;
    }

    try {
      setLoading(true);
      setTableLoading(true);

      if (isEditMode && editingId) {
        const updateData = {
          id: editingId,
          name: formData.name.trim(),
          description: formData.description.trim()
        };
        
        const response = await updateAboutOrganization(editingId, updateData);
        if (response.success) {
          toast.success('Organization information updated successfully');
          await fetchOrganizationData();
        } else {
          toast.error(response.message || 'Failed to update organization information');
        }
      } else {
        const newOrgData = {
          name: formData.name.trim(),
          description: formData.description.trim()
        };
        const response = await addAboutOrganization(newOrgData);
        if (response.success) {
          toast.success('Organization information added successfully');
          await fetchOrganizationData();
        } else {
          toast.error(response.message || 'Failed to add organization information');
        }
      }

      setShowModal(false);
      resetForm();
    } catch (error) {
      console.error('Error submitting form:', error);
      toast.error(error.response?.data?.message || 'Failed to submit form');
    } finally {
      setLoading(false);
      setTableLoading(false);
    }
  };

  const resetForm = () => {
    setFormData({ name: '', description: '' });
    setFormErrors({ name: '', description: '' });
    setIsEditMode(false);
    setEditingId(null);
  };

  const handleEdit = (item) => {
    if (!item || !item.id) {
      toast.error('Invalid item data');
      return;
    }
    setFormData({
      name: item.name,
      description: item.description
    });
    setEditingId(item.id);
    setIsEditMode(true);
    setShowModal(true);
  };

  const handleDelete = async (id) => {
    if (!permissions.settings_about_organization_delete) {
      toast.warning("You don't have permission to delete organization information");
      return;
    }

    try {
      setLoading(true);
      setTableLoading(true);
      const response = await deleteAboutOrganization(id);
      if (response.success) {
        toast.success('Organization information deleted successfully');
        await fetchOrganizationData();
      } else {
        toast.error(response.message || 'Failed to delete organization information');
      }
    } catch (error) {
      console.error('Error deleting organization info:', error);
      toast.error(error.response?.data?.message || 'Error deleting organization information');
    } finally {
      setShowDeleteModal(false);
      setDeleteOrgData(null);
      setLoading(false);
      setTableLoading(false);
    }
  };

  const handleStatusToggle = async (id, currentStatus) => {
    if (!permissions.settings_about_organization_status) {
      toast.warning("You don't have permission to change organization status");
      return;
    }

    try {
      setStatusLoading(id);
      setTableLoading(true);
      const response = await updateAboutOrganizationStatus(id, { 
        id: id,
        status: !currentStatus 
      });
      if (response.success) {
        toast.success(`Organization information ${!currentStatus ? 'activated' : 'deactivated'} successfully`);
        await fetchOrganizationData();
      } else {
        toast.error(response.message || 'Failed to update status');
      }
    } catch (error) {
      console.error('Error updating status:', error);
      toast.error(error.response?.data?.message || 'Error updating status');
    } finally {
      setStatusLoading(null);
      setTableLoading(false);
    }
  };

  const handleDeleteClick = (e, id, name) => {
    e.stopPropagation();
    setDeleteOrgData({ id, name });
    setShowDeleteModal(true);
  };

  const handleSearchChange = (e) => {
    setSearch(e.target.value);
    setPagination(prev => ({ ...prev, currentPage: 1 }));
  };

  const handlePageChange = (newPage) => {
    setPagination(prev => ({ ...prev, currentPage: newPage }));
  };

  const handleLimitChange = (e) => {
    const newLimit = parseInt(e.target.value);
    setPagination(prev => ({
      ...prev,
      recordsPerPage: newLimit,
      currentPage: 1
    }));
  };

  const handleAddNew = () => {
    resetForm();
    setShowModal(true);
  };

  const getStatusBadge = (isActive) => {
    if (isActive) {
      return (
        <span className="badge border border-success bg-success bg-opacity-10 text-success d-inline-flex align-items-center gap-1">
          <Icon icon="mdi:check-circle-outline" className="fs-6" />
          Active
        </span>
      );
    }
    return (
      <span className="badge border border-danger bg-danger bg-opacity-10 text-danger d-inline-flex align-items-center gap-1">
        <Icon icon="mdi:close-circle-outline" className="fs-6" />
        Inactive
      </span>
    );
  };

  return (
    <>
      {/* search and add button  */}
      <div className="row mb-3">
        <div className="col-md-6 mt-2 d-flex justify-content-start align-items-center">
          <input
            type="search"
            className="form-control"
            placeholder="Search organization info..."
            style={{ maxWidth: '300px' }}
            value={search}
            onChange={handleSearchChange}
          />
        </div>
        <div className="col-md-6 text-end mt-2 d-flex justify-content-end align-items-center">
          <button
            className="btn btn-primary text-nowrap"
            style={{ width: '180px' }}
            onClick={handleAddNew}
            disabled={loading || !permissions.settings_about_organization_create}
            title={!permissions.settings_about_organization_create ? "You don't have permission to create organization information" : ""}
          >
            {loading ? 'Loading...' : 'Add Organization Info'}
          </button>
        </div>
      </div>

      {/* table */}
      <div className="row mb-3">
        <div className="col-12">
          <div className="card">
            <div className="table-responsive">
              <table className="table table-borderless mb-0" style={{ minWidth: '800px' }}>
                <thead>
                  <tr>
                    <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500', width: '80px' }}>SL No</th>
                    <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500', width: '20%' }}>Name</th>
                    <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500', width: '40%' }}>Description</th>
                    <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500', width: '100px' }}>Status</th>
                    <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500', width: '120px' }}>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {tableLoading ? (
                    <tr>
                      <td colSpan="5" style={{ height: '400px' }}>
                        <Loader
                          caption="Loading Organization Data..."
                          minLoadTime={500}
                          containerHeight="400px"
                        />
                      </td>
                    </tr>
                  ) : organizationData.length === 0 ? (
                    <tr>
                      <td colSpan="5" style={{ height: '400px' }}>
                        <NoData caption="No organization information found" />
                      </td>
                    </tr>
                  ) : (
                    organizationData.map((item, index) => (
                      <tr key={item.id} className="border-bottom">
                        <td className="py-3 align-middle">
                          {(pagination.currentPage - 1) * pagination.recordsPerPage + index + 1}
                        </td>
                        <td className="py-3 align-middle">{item.name}</td>
                        <td className="py-3 align-middle">{item.description}</td>
                        <td className="py-3 align-middle">
                          {getStatusBadge(item.is_active)}
                        </td>
                        <td className="py-3 align-middle">
                          <div className="d-flex gap-2">
                            <button
                              className="btn btn-outline-dark btn-sm border border-dark"
                              style={{ width: '36px', height: '36px' }}
                              title={!permissions.settings_about_organization_edit ? "You don't have permission to edit" : "Edit Info"}
                              onClick={() => handleEdit(item)}
                              disabled={loading || !permissions.settings_about_organization_edit}
                            >
                              <Icon icon="fluent:edit-24-regular" width="16" height="16" />
                            </button>
                            <button
                              className={`btn ${item.is_active ? "btn-success" : "btn-outline-warning"} btn-sm border`}
                              style={{ width: '36px', height: '36px' }}
                              title={!permissions.settings_about_organization_status ? "You don't have permission to change status" : (item.is_active ? "Deactivate" : "Activate")}
                              onClick={() => handleStatusToggle(item.id, item.is_active)}
                              disabled={statusLoading === item.id || !permissions.settings_about_organization_status}
                            >
                              {statusLoading === item.id ? (
                                <span className="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                              ) : (
                                <Icon 
                                  icon={item.is_active ? "fluent:checkmark-circle-24-regular" : "fluent:dismiss-circle-24-regular"}
                                  width="16" 
                                  height="16" 
                                />
                              )}
                            </button>
                            <button
                              className="btn btn-outline-danger btn-sm border border-danger"
                              style={{ width: '36px', height: '36px' }}
                              title={!permissions.settings_about_organization_delete ? "You don't have permission to delete" : "Delete Info"}
                              onClick={(e) => handleDeleteClick(e, item.id, item.name)}
                              disabled={loading || !permissions.settings_about_organization_delete}
                            >
                              <Icon icon="fluent:delete-24-regular" width="16" height="16" />
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))
                  )}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>

      {/* record per page and pagination */}
      <div className="row">
        <div className="col-md-6">
          <select
            className="form-select"
            style={{ width: 'auto' }}
            value={pagination.recordsPerPage}
            onChange={handleLimitChange}
          >
            <option value={10}>10 records per page</option>
            <option value={25}>25 records per page</option>
            <option value={50}>50 records per page</option>
          </select>
        </div>
        <div className="col-md-6 d-flex justify-content-end align-items-center gap-3">
          <button
            className="btn btn-primary"
            style={{ width: '150px' }}
            onClick={() => handlePageChange(pagination.currentPage - 1)}
            disabled={pagination.currentPage === 1 || loading}
          >
            Prev
          </button>
          <span>Page {pagination.currentPage} of {pagination.totalPages}</span>
          <button
            className="btn btn-primary"
            style={{ width: '150px' }}
            onClick={() => handlePageChange(pagination.currentPage + 1)}
            disabled={pagination.currentPage === pagination.totalPages || loading}
          >
            Next
          </button>
        </div>
      </div>

      {/* Add/Edit Modal */}
      {showModal && (
        <div className="modal show d-block" tabIndex="-1" style={{ backgroundColor: 'rgba(0,0,0,0.5)' }}>
          <div className="modal-dialog modal-dialog-centered">
            <div className="modal-content">
              <div className="modal-header border-0 pb-0">
                <h5 className="modal-title">
                  {isEditMode ? 'Edit Organization Information' : 'Add Organization Information'}
                </h5>
                <button
                  type="button"
                  className="btn-close"
                  onClick={() => {
                    setShowModal(false);
                    resetForm();
                  }}
                ></button>
              </div>
              <div className="modal-body pt-2">
                <form onSubmit={handleSubmit}>
                  <div className="mb-3">
                    <label className="form-label">
                      Name <span className="text-danger">*</span>
                    </label>
                    <input
                      type="text"
                      className={`form-control ${formErrors.name ? 'is-invalid' : ''}`}
                      name="name"
                      value={formData.name}
                      onChange={handleInputChange}
                      placeholder="Enter name"
                      disabled={loading}
                    />
                    {formErrors.name && (
                      <div className="invalid-feedback">{formErrors.name}</div>
                    )}
                    <small className="text-muted">
                      {formData.name.length}/{MAX_NAME_LENGTH} characters
                    </small>
                  </div>
                  <div className="mb-3">
                    <label className="form-label">
                      Description <span className="text-danger">*</span>
                    </label>
                    <textarea
                      className={`form-control ${formErrors.description ? 'is-invalid' : ''}`}
                      name="description"
                      value={formData.description}
                      onChange={handleInputChange}
                      placeholder="Enter description"
                      rows="4"
                      disabled={loading}
                    />
                    {formErrors.description && (
                      <div className="invalid-feedback">{formErrors.description}</div>
                    )}
                    <small className="text-muted">
                      {formData.description.length}/{MAX_DESCRIPTION_LENGTH} characters
                    </small>
                  </div>
                  <div className="modal-footer px-0 pb-0">
                    <button
                      type="button"
                      className="btn btn-secondary"
                      onClick={() => {
                        setShowModal(false);
                        resetForm();
                      }}
                      disabled={loading}
                    >
                      Cancel
                    </button>
                    <button
                      type="submit"
                      className="btn btn-primary"
                      disabled={loading}
                    >
                      {loading ? 'Saving...' : (isEditMode ? 'Update Information' : 'Save Information')}
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Delete Confirmation Modal */}
      {showDeleteModal && (
        <div className="modal show d-block" style={{ backgroundColor: 'rgba(0,0,0,0.5)' }}>
          <div className="modal-dialog modal-dialog-centered">
            <div className="modal-content">
              <div className="modal-header">
                <h5 className="modal-title">Delete Organization Information</h5>
                <button
                  type="button"
                  className="btn-close"
                  onClick={() => {
                    setShowDeleteModal(false);
                    setDeleteOrgData(null);
                  }}
                ></button>
              </div>
              <div className="modal-body">
                <div className="text-center mb-3">
                  <Icon icon="fluent:delete-24-regular" className="text-danger mb-2" width="48" height="48" />
                  <h5>Are you sure?</h5>
                  <p className="text-muted">
                    Do you really want to delete "{deleteOrgData?.name}"? This action cannot be undone.
                  </p>
                </div>
              </div>
              <div className="modal-footer">
                <button
                  type="button"
                  className="btn btn-secondary"
                  onClick={() => {
                    setShowDeleteModal(false);
                    setDeleteOrgData(null);
                  }}
                >
                  Cancel
                </button>
                <button
                  type="button"
                  className="btn btn-danger d-flex align-items-center gap-2"
                  onClick={() => handleDelete(deleteOrgData?.id)}
                  disabled={loading}
                >
                  {loading ? (
                    <>
                      <span className="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                      Deleting...
                    </>
                  ) : (
                    <>
                      <Icon icon="fluent:delete-24-regular" width="16" height="16" />
                      Delete Information
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  )
}

export default AboutOrganization
