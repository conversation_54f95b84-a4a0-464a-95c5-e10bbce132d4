const { mysqlServerConnection } = require('../../../db/db');
const { getRoleByName } = require('../../../tools/fintRole');


// const getDashBoardStats = async (req, res) => {

//     const db_name = req.user.db_name;
//     const tranee_id=await getRoleByName({db_name:db_name, type:'trainee'})
//     const queries = [
//         `SELECT COUNT(*) AS totalUsers
//             FROM ${db_name}.user_roles as ur
//             JOIN ${db_name}.roles as r ON ur.role_id = r.id
//             WHERE r.name != 'trainee' AND r.name != 'trainer'`, // Fixed closing quote
//         `SELECT COUNT(*) AS totalCourses FROM ${db_name}.courses WHERE is_approved=1`,
//         `SELECT COUNT(*) AS totalClassroom FROM ${db_name}.classroom WHERE is_deleted=0`,
//         `SELECT COUNT(*) AS classroomRequest FROM ${db_name}.classroom_approval WHERE is_approval=0`, // Removed extra period
//         `SELECT COUNT(*) AS totalStudents
//             FROM ${db_name}.user_roles as ur
//             JOIN ${db_name}.roles as r ON ur.role_id = r.id
//             WHERE r.name = 'trainee'`,
//         `SELECT COUNT(*) AS pendingStudents FROM ${db_name}.user_roles ur JOIN ${db_name}.users u ON ur.user_id = u.id WHERE ur.role_id = "${tranee_id}" AND u.is_blocked = 1`,
//         `SELECT COUNT(*) AS activeStudents FROM ${db_name}.user_roles ur JOIN ${db_name}.users u ON ur.user_id = u.id WHERE ur.role_id = "${tranee_id}" AND u.is_blocked = 0`,
//         `SELECT COUNT(*) AS totalPurchased FROM ${db_name}.courses WHERE course_type = 'paid'`,
//         `SELECT COUNT(*) AS totalTrainers
//             FROM ${db_name}.user_roles as ur
//             JOIN ${db_name}.roles as r ON ur.role_id = r.id
//             WHERE r.name = 'trainer'`,
//         `SELECT COUNT(*) AS totalActiveTrainers
//             FROM ${db_name}.user_roles as ur
//               JOIN ${db_name}.roles as r ON ur.role_id = r.id
//             JOIN ${db_name}.users as u ON ur.user_id = u.id
//             WHERE r.name = 'trainer'
//               AND u.is_blocked = 0`,
//           `SELECT COUNT(*) AS totalActiveUsers
//             FROM ${db_name}.user_roles as ur
//             JOIN ${db_name}.roles as r ON ur.role_id = r.id
//             JOIN ${db_name}.users as u ON ur.user_id = u.id
//             WHERE r.name != 'trainee' 
//               AND r.name != 'trainer'
//               AND u.is_blocked = 0`,

//               `SELECT COUNT(*) AS totalCourseRequest
//               FROM ${db_name}.course_approvals where
//                 course_id != null`

//     ];

//     try {
//         const queryPromises = queries.map(query => mysqlServerConnection.query(query));
//         const results = await Promise.all(queryPromises);

//         const response = {
//             "totalUsers": results[0][0][0]?.totalUsers || 0,
//             "totalCourses": results[1][0][0]?.totalCourses || 0,
//             "totalClassroom": results[2][0][0]?.totalClassroom || 0,
//             "classroomRequest": results[3][0][0]?.classroomRequest || 0,
//             "totalStudents": results[4][0][0]?.totalStudents || 0,
//             "pendingStudents": results[5][0][0]?.pendingStudents || 0,
//             "activeStudents": results[6][0][0]?.activeStudents || 0,
//             "totalPurchased": results[7][0][0]?.totalPurchased || 0,
//             "totalTrainer": results[8][0][0]?.totalTrainers || 0,
//             "totalActiveTrainer": results[9][0][0]?.totalActiveTrainers || 0,
//             "totalActiveUser": results[10][0][0]?.totalActiveUsers || 0,
//             "totalCourseRequest": results[11][0][0]?.totalCourseRequest || 0
//         };

//         console.log('dashboard cards response',response)

//         res.status(200).json({
//             success: true,
//             data: response
//         });
//     } catch (error) {
//         console.error("Error fetching dashboard stats: ", error);
//         res.status(500).json({
//             success: false,
//             message: 'Failed to fetch dashboard stats',
//             error: error.message
//         });
//     }
// };

// Dashboard

// Dashboard Stats

const getDashBoardStats = async (req, res) => {
  const dbName = req.user.db_name;

  try {
    // Queries
    const userQuery = `
      SELECT 
        COUNT(*) AS total_users,
        COUNT(CASE WHEN is_blocked = 0 AND is_deleted = 0 THEN 1 END) AS active_users,
        COUNT(CASE WHEN is_blocked = 1 OR is_deleted = 1 THEN 1 END) AS inactive_users
      FROM ${dbName}.users;
    `;

    const courseQuery = `
      SELECT 
        COUNT(*) AS total_courses,
        COUNT(CASE WHEN is_active = 1 AND is_deleted = 0 THEN 1 END) AS active_courses,
        COUNT(CASE WHEN is_approved = 1 AND is_deleted = 0 THEN 1 END) AS approved_courses,
        COUNT(CASE WHEN course_type = 'free' AND is_deleted = 0 THEN 1 END) AS total_free_courses,
        COUNT(CASE WHEN course_type = 'paid' AND is_deleted = 0 THEN 1 END) AS total_paid_courses
      FROM ${dbName}.courses;
    `;

    const classroomQuery = `
      SELECT 
        COUNT(*) AS total_classrooms,
        COUNT(CASE WHEN is_active = 1 AND is_deleted = 0 THEN 1 END) AS active_classrooms,
        COUNT(CASE WHEN is_active = 0 OR is_deleted = 1 THEN 1 END) AS inactive_classrooms
      FROM ${dbName}.classroom;
    `;

    const trainerQuery = `
      SELECT 
        COUNT(*) AS total_trainers,
        COUNT(CASE WHEN u.is_blocked = 0 AND u.is_deleted = 0 THEN 1 END) AS active_trainers,
        COUNT(CASE WHEN u.is_blocked = 1 OR u.is_deleted = 1 THEN 1 END) AS inactive_trainers
      FROM ${dbName}.user_roles ur
      JOIN ${dbName}.users u ON ur.user_id = u.id
      WHERE ur.role_id = 2;
    `;

    const traineeQuery = `
      SELECT 
        COUNT(*) AS total_trainees,
        COUNT(CASE WHEN u.is_blocked = 0 AND u.is_deleted = 0 THEN 1 END) AS active_trainees,
        COUNT(CASE WHEN u.is_blocked = 1 OR u.is_deleted = 1 THEN 1 END) AS inactive_trainees
      FROM ${dbName}.user_roles ur
      JOIN ${dbName}.users u ON ur.user_id = u.id
      WHERE ur.role_id = 2;
    `;

    const ticketQuery = `
      SELECT 
        COUNT(*) AS total_tickets,
        COUNT(CASE WHEN status = 'pending' THEN 1 END) AS pending_tickets,
        COUNT(CASE WHEN status = 'resolved' THEN 1 END) AS resolved_tickets
      FROM ${dbName}.ticket;
    `;

    // Execute queries
    const [userResult] = await mysqlServerConnection.query(userQuery);
    const [courseResult] = await mysqlServerConnection.query(courseQuery);
    const [classroomResult] = await mysqlServerConnection.query(classroomQuery);
    const [trainerResult] = await mysqlServerConnection.query(trainerQuery);
    const [traineeResult] = await mysqlServerConnection.query(traineeQuery);
    const [ticketResult] = await mysqlServerConnection.query(ticketQuery);

    // Extract values
    const total_users = userResult[0].total_users;
    const active_users = userResult[0].active_users;
    const inactive_users = userResult[0].inactive_users;

    const total_courses = courseResult[0].total_courses;
    const active_courses = courseResult[0].active_courses;
    const approved_courses = courseResult[0].approved_courses;
    const total_free_courses = courseResult[0].total_free_courses;
    const total_paid_courses = courseResult[0].total_paid_courses;

    const total_classrooms = classroomResult[0].total_classrooms;
    const active_classrooms = classroomResult[0].active_classrooms;
    const inactive_classrooms = classroomResult[0].inactive_classrooms;

    const total_trainers = trainerResult[0].total_trainers;
    const active_trainers = trainerResult[0].active_trainers;
    const inactive_trainers = trainerResult[0].inactive_trainers;

    const total_trainees = traineeResult[0].total_trainees;
    const active_trainees = traineeResult[0].active_trainees;
    const inactive_trainees = traineeResult[0].inactive_trainees;

    const total_tickets = ticketResult[0].total_tickets;
    const pending_tickets = ticketResult[0].pending_tickets;
    const resolved_tickets = ticketResult[0].resolved_tickets;

    return res.status(200).json({
      success: true,
      data: {
        total_users,
        active_users,
        inactive_users,

        total_courses,
        active_courses,
        approved_courses,
        total_free_courses,
        total_paid_courses,

        total_classrooms,
        active_classrooms,
        inactive_classrooms,

        total_trainers,
        active_trainers,
        inactive_trainers,

        total_trainees,
        active_trainees,
        inactive_trainees,

        total_tickets,
        pending_tickets,
        resolved_tickets,
      },
    });
  } catch (error) {
    console.error("Error fetching dashboard stats:", error);
    return res.status(500).json({
      success: false,
      message: "Failed to fetch dashboard statistics",
    });
  }
};


// User Queries
const getUserQueries = async (req, res) => {
  try {
    const dbName = req.user.db_name;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 4;
    const offset = (page - 1) * limit;


    // SQL query to fetch tickets along with profile_pic_url from users
    const query = `
            SELECT t.*, u.profile_pic_url,u.name
            FROM ${dbName}.ticket t
            JOIN ${dbName}.users u ON t.sender_id = u.id
            AND t.status = 'Pending'
            ORDER BY t.createdAt DESC
            LIMIT ? OFFSET ?;
        `;

    const [results] = await mysqlServerConnection.query(query, [limit, offset]);
    const countQuery = `SELECT COUNT(*) AS totalTickets FROM ${dbName}.ticket;`;
    const [countResult] = await mysqlServerConnection.query(countQuery);
    const totalTickets = countResult[0].totalTickets;

    res.status(200).json({
      success: true,
      data: results,
      pagination: {
        currentPage: page,
        totalPages: Math.ceil(totalTickets / limit),
        totalTickets: totalTickets,
        limit: limit,
      },
    });
  } catch (error) {
    console.error('Error fetching tickets:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching tickets',
    });
  }
};

// Org Performance Statistics
const getOrgPerformanceStatistics = async (req, res) => {
  try {
    const db_name = req.user.db_name;

    const queries = [
      `SELECT COUNT(*) AS courseCompletion FROM ${db_name}.certificates`, // Number of certificates generated
      `SELECT COUNT(*) AS totalStudents FROM ${db_name}.user_roles WHERE role_id = 2`, // Number of total students
      `SELECT SUM(subscribers) AS totalEnrolled FROM ${db_name}.courses WHERE course_type = 'paid'` // Total number of paid enrollments
    ];

    const queryPromises = queries.map(query => mysqlServerConnection.query(query));
    const results = await Promise.all(queryPromises);
    const courseCompletion = results[0][0][0]?.courseCompletion;
    const totalStudents = results[1][0][0]?.totalStudents;
    const totalEnrolled = results[2][0][0]?.totalEnrolled || 0; // Handle case if no courses are enrolled

    // Calculate the percentages
    const courseCompletionPercentage = totalStudents > 0 ? (courseCompletion / totalStudents) * 100 : 0;
    const totalEnrolledPercentage = totalStudents > 0 ? (totalEnrolled / totalStudents) * 100 : 0;

    // Prepare the response
    const response = {
      totalStudents,
      courseCompletionPercentage: courseCompletionPercentage.toFixed(2),
      totalEnrolledPercentage: totalEnrolledPercentage.toFixed(2),
    };
    res.status(200).json({
      success: true,
      data: response,
    });

  } catch (error) {
    console.error('Error fetching performance statistics:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching performance statistics',
    });
  }
};




// Users Monthly Wise
const getUsersMonthlyWise = async (req, res, next) => {
  try {
    const filter = req.query.filter || new Date().getFullYear(); // Default to current year if not passed
    const db_name = req.user.db_name;

    console.log('Requested Year:', filter);
    console.log('Using DB:', db_name);

    if (!filter) {
      return res.status(400).json({ error: "Year parameter is required" });
    }

    const monthlyQuery = `
        SELECT 
          MONTH(createdAt) AS month,
          COUNT(*) AS user_count
        FROM 
          ${db_name}.users
        WHERE 
          YEAR(createdAt) = ?
          AND is_deleted = 0
        GROUP BY 
          MONTH(createdAt)
        ORDER BY 
          month;
      `;

    const [results] = await mysqlServerConnection.query(monthlyQuery, [filter]);

    if (!results || results.length === 0) {
      // console.warn('⚠️ No user data found for year:', filter);
    }

    const monthlyData = Array.from({ length: 12 }, (_, index) => {
      const monthNumber = index + 1;
      const monthData = results.find(item => item.month === monthNumber);
      return {
        month: monthNumber,
        monthName: new Date(2000, index, 1).toLocaleString('default', { month: 'short' }),
        count: monthData ? monthData.user_count : 0
      };
    });

    // console.log('Monthly Data:', monthlyData);

    return res.json({
      success: true,
      year: parseInt(filter),
      monthlyData
    });

  } catch (error) {
    console.error('❌ Error in getUsersMonthlyWise:', error.message, error.stack);
    return res.status(500).json({ success: false, error_msg: error.message || 'Internal Server Error' });
  }
};

// Created Course And Classroom Monthly Wise
const getCreatedCourseAndClassroomMonthlyWise = async (req, res, next) => {
  try {
    const filter = req.query.filter || new Date().getFullYear();
    const db_name = req.user.db_name;

    // console.log('📅 Requested Year:', filter);
    // console.log('📂 Using Database:', db_name);

    if (!filter) {
      console.warn('⚠️ Year not provided or invalid');
      return res.status(400).json({ success: false, message: "Year parameter is required" });
    }

    // --- Course Query ---
    const courseMonthlyQuery = `
        SELECT 
          MONTH(createdAt) AS month,
          COUNT(*) AS course_count
        FROM ${db_name}.courses
        WHERE YEAR(createdAt) = ? AND is_deleted = 0
        GROUP BY MONTH(createdAt)
        ORDER BY month;
      `;

    // --- Classroom Query ---
    const classroomMonthlyQuery = `
        SELECT 
          MONTH(createdAt) AS month,
          COUNT(*) AS classroom_count
        FROM ${db_name}.classroom
        WHERE YEAR(createdAt) = ? AND is_deleted = 0
        GROUP BY MONTH(createdAt)
        ORDER BY month;
      `;

    // --- Execute queries ---
    const [courseResults] = await mysqlServerConnection.query(courseMonthlyQuery, [filter]);

    const [classroomResults] = await mysqlServerConnection.query(classroomMonthlyQuery, [filter]);

    if (!courseResults.length) {
      // console.warn('⚠️ No course data found for the given year.');
    }
    if (!classroomResults.length) {
      // console.warn('⚠️ No classroom data found for the given year.');
    }

    // --- Build Monthly Data ---
    const monthlyData = Array.from({ length: 12 }, (_, index) => {
      const monthNumber = index + 1;
      const courseData = courseResults.find(item => item.month === monthNumber);
      const classroomData = classroomResults.find(item => item.month === monthNumber);

      return {
        month: monthNumber,
        monthName: new Date(2000, index, 1).toLocaleString('default', { month: 'short' }),
        courses: courseData ? courseData.course_count : 0,
        classrooms: classroomData ? classroomData.classroom_count : 0
      };
    });

    // console.log('✅ Final Monthly Data:', monthlyData);

    return res.status(200).json({
      success: true,
      year: parseInt(filter),
      data: monthlyData
    });

  } catch (error) {
    console.error('❌ Error in getCreatedCourseAndClassroomMonthlyWise:', error.message);
    console.error('🛠️ Stack Trace:', error.stack);
    return res.status(500).json({
      success: false,
      message: error.message || 'Internal server error'
    });
  }
};




//get top trainer which courses have most ratings and most subscribers
const getTopTrainers = async (req, res) => {
  try {
    const [topTrainers] = await mysqlServerConnection.query(
      `SELECT u.id,u.profile_pic_url, u.name, u.email, 
                    COUNT(cr.id) AS total_courses, 
                    COALESCE(AVG(cr.total_rating) * 20, 0) AS total_rating_percentage, 
                    SUM(cr.subscribers) AS total_subscribers
             FROM ${req.user.db_name}.users u
             LEFT JOIN ${req.user.db_name}.courses cr ON u.name = cr.created_by
             GROUP BY u.id, u.name, u.email
             ORDER BY total_rating_percentage DESC, total_subscribers DESC
             LIMIT 5`
    );

    // console.log('topTrainers',topTrainers)

    res.status(200).json({
      success: true,
      data: topTrainers
    });
  } catch (error) {
    return res.status(500).json({ success: false, error_msg: error.message || 'Internal Server Error' });
  }
};


module.exports = {
  getDashBoardStats,
  getUserQueries,
  getOrgPerformanceStatistics,
  getUsersMonthlyWise,
  getCreatedCourseAndClassroomMonthlyWise,
  getTopTrainers
}