import React, { useEffect, useState } from "react";
import { successPayUPayment } from "../../../services/userService";
import { useNavigate, useLocation } from "react-router-dom";
import { Icon } from "@iconify/react";
import "./SuccessPayment.css";

const SuccessPayUPayment = () => {
  alert("SuccessPayUPayment");
  const navigate = useNavigate();
  const location = useLocation();

  const [data, setData] = useState({});
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const [retryCount, setRetryCount] = useState(0);
  const maxRetries = 3;

  // Extract query params
  const queryParams = new URLSearchParams(location.search);
  const courseId = queryParams.get("course_id");
  const txnId = queryParams.get("txnid");

  const sendToValidation = async (isRetry = false) => {
    console.log("=== PayU Success Payment Validation Started ===");
    console.log("Course ID:", courseId);
    console.log("Transaction ID:", txnId);
    console.log("Is Retry:", isRetry);

    if (!courseId || !txnId) {
      console.error("Missing course_id or txnid");
      setHasError(true);
      setIsLoading(false);
      return;
    }

    try {
      // Add a small delay for retries to allow backend to process
      if (isRetry) {
        await new Promise(resolve => setTimeout(resolve, 2000));
      }

      console.log("Calling successPayUPayment API...");
      const response = await successPayUPayment(courseId, txnId);
      console.log("Success PayU payment response:", response);

      if (response && response.success) {
        console.log("Payment validation successful:", response.data);
        setData(response.data);
        setHasError(false);
        setIsLoading(false);
      } else {
        console.error("Payment validation failed:", response);
        
        // Retry logic
        if (retryCount < maxRetries) {
          console.log(`Retrying payment validation... (${retryCount + 1}/${maxRetries})`);
          setRetryCount(prev => prev + 1);
          setTimeout(() => sendToValidation(true), 3000);
        } else {
          setHasError(true);
          setIsLoading(false);
        }
      }
    } catch (error) {
      console.error("Error in PayU success payment:", error);
      
      // Retry logic for errors
      if (retryCount < maxRetries) {
        console.log(`Retrying due to error... (${retryCount + 1}/${maxRetries})`);
        setRetryCount(prev => prev + 1);
        setTimeout(() => sendToValidation(true), 3000);
      } else {
        setHasError(true);
        setIsLoading(false);
      }
    }
  };

  const redirectToCourse = () => {
    navigate("/user/courses");
  };

  const handleManualRetry = () => {
    console.log('Manual retry triggered');
    setRetryCount(0);
    setIsLoading(true);
    setHasError(false);
    sendToValidation(true);
  };

  // Start validation when component mounts
  useEffect(() => {
    if (courseId && txnId) {
      sendToValidation();
    } else {
      console.error("Missing required parameters");
      setHasError(true);
      setIsLoading(false);
    }
  }, []);

  if (isLoading) {
    return (
      <div className="d-flex justify-content-center align-items-center" style={{ minHeight: "60vh" }}>
        <div className="text-center">
          <div className="spinner-border text-primary mb-3" role="status" />
          <p className="text-muted">
            {retryCount > 0
              ? `Retrying payment verification... (Attempt ${retryCount + 1}/${maxRetries})`
              : "Verifying your PayU payment..."}
          </p>
          {retryCount > 0 && (
            <small className="text-info d-block mt-2">
              Please wait, we're ensuring your payment is properly processed...
            </small>
          )}
        </div>
      </div>
    );
  }

  if (hasError) {
    return (
      <div className="payment-error-container">
        <div className="error-content text-center">
          <Icon icon="mdi:alert-circle" className="text-danger mb-3" width="64" height="64" />
          <h3 className="text-danger mb-3">Payment Verification Failed</h3>
          <p className="text-muted mb-4">
            We couldn't verify your payment automatically. This might be due to a temporary issue.
          </p>
          <div className="d-flex justify-content-center gap-3">
            <button className="btn btn-primary" onClick={handleManualRetry}>
              <Icon icon="mdi:refresh" className="me-2" />
              Try Again
            </button>
            <button className="btn btn-outline-secondary" onClick={redirectToCourse}>
              Go to Courses
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="success-payment-container">
      <div className="success-payment-card">
        {/* Success Icon */}
        <div className="success-icon-wrapper mb-4">
          <Icon icon="mdi:check-circle" className="text-success" width="64" height="64" />
        </div>

        {/* Success Message */}
        <h2 className="success-title">PayU Payment Successful!</h2>
        <p className="success-subtitle">Your transaction has been completed successfully via PayU.</p>

        {/* Amount */}
        <div className="amount-display my-4">
          <span className="amount-value">₹{data.price ?? "0"}</span>
        </div>

        {/* Transaction Details */}
        <div className="transaction-details">
          <div className="detail-row">
            <span className="detail-label">Transaction ID</span>
            <span className="detail-value">
              #{data.payment_id ? data.payment_id.toString().toUpperCase().slice(0, 15) : "-"}
            </span>
          </div>
          <div className="detail-row">
            <span className="detail-label">PayU Transaction ID</span>
            <span className="detail-value">
              #{data.transaction_id ? data.transaction_id.toString().toUpperCase().slice(0, 15) : "-"}
            </span>
          </div>
          <div className="detail-row">
            <span className="detail-label">Date</span>
            <span className="detail-value">
              {new Date().toLocaleDateString("en-IN", {
                year: "numeric",
                month: "long",
                day: "numeric",
              })}
            </span>
          </div>
          <div className="detail-row">
            <span className="detail-label">Status</span>
            <span className="detail-value status-success">
              <Icon icon="mdi:check-circle" className="me-1" />
              Completed
            </span>
          </div>
          <div className="detail-row">
            <span className="detail-label">Payment Method</span>
            <span className="detail-value">PayU Gateway</span>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="action-buttons mt-4">
          <button className="btn btn-primary btn-lg w-100 mb-3" onClick={redirectToCourse}>
            <Icon icon="mdi:play-circle" className="me-2" />
            Go Back to Course
          </button>
        </div>
      </div>
    </div>
  );
};

export default SuccessPayUPayment;
