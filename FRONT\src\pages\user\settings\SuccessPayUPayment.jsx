import React, { useEffect, useState, useRef, useContext } from "react";
import { successPayUPayment } from "../../../services/userService";
import { useNavigate, useLocation } from "react-router-dom";
import { Icon } from "@iconify/react";
import "./SuccessPayment.css";

const SuccessPayUPayment = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const hasInitialized = useRef(false);
  const authCheckInterval = useRef(null);
  const maxRetries = 5;

  const [queryReady, setQueryReady] = useState(false);
  const [courseId, setCourseId] = useState("");
  const [txnId, setTxnId] = useState("");

  const [data, setData] = useState({});
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const [retryCount, setRetryCount] = useState(0);
  const [isRetrying, setIsRetrying] = useState(false);
  const [authReady, setAuthReady] = useState(false);

  // Check if authentication token is available
  const checkAuthReady = () => {
    const token = localStorage.getItem('token') || sessionStorage.getItem('token');
    console.log('Auth check - Token available:', !!token);
    return !!token;
  };

  // Wait for authentication to be ready
  const waitForAuth = async (maxWaitTime = 10000) => {
    return new Promise((resolve) => {
      const startTime = Date.now();

      const checkAuth = () => {
        if (checkAuthReady()) {
          console.log('Authentication ready!');
          setAuthReady(true);
          resolve(true);
          return;
        }

        if (Date.now() - startTime > maxWaitTime) {
          console.log('Authentication wait timeout');
          resolve(false);
          return;
        }

        setTimeout(checkAuth, 500);
      };

      checkAuth();
    });
  };

  const sendtovalidation = async (isRetryAttempt = false, forceRetry = false) => {
    console.log("=== PayU Success Payment Validation Started ===");
    console.log("Course ID:", courseId);
    console.log("Transaction ID:", txnId);
    console.log("Is Retry Attempt:", isRetryAttempt);
    console.log("Force Retry:", forceRetry);

    // Prevent multiple simultaneous calls
    if (hasInitialized.current && !isRetryAttempt && !forceRetry) {
      console.log('Already initialized, skipping...');
      return;
    }

    if (!hasInitialized.current) {
      hasInitialized.current = true;
    }

    if (isRetryAttempt || forceRetry) {
      setIsRetrying(true);
      setHasError(false);
      if (forceRetry) {
        setIsLoading(true);
      }
    }

    // Check localStorage first for cached data
    if (!isRetryAttempt && !forceRetry) {
      try {
        const cachedData = localStorage.getItem(`payu_success_${txnId}`);
        if (cachedData) {
          console.log('Found cached success data, using it...');
          const parsedData = JSON.parse(cachedData);
          setData(parsedData);
          setIsLoading(false);
          setHasError(false);
          return;
        }
      } catch (e) {
        console.warn('Could not read cached data:', e);
      }
    }

    // Wait for authentication to be ready
    if (!checkAuthReady()) {
      console.log('Authentication not ready, waiting...');
      const authReady = await waitForAuth(8000);
      if (!authReady) {
        console.log('Authentication timeout, proceeding anyway...');
      }
    }

    // Add a small delay to ensure everything is loaded
    if (!isRetryAttempt && !forceRetry) {
      await new Promise(resolve => setTimeout(resolve, 1500));
    }

    try {
      console.log("Calling successPayUPayment API...");
      const response = await successPayUPayment(courseId, txnId);
      console.log("Success PayU payment response:", response);

      if (response && response.success) {
        console.log("Payment validation successful:", response.data);
        setData(response.data);
        setHasError(false);

        // Cache the success data
        try {
          localStorage.setItem(`payu_success_${txnId}`, JSON.stringify(response.data));
        } catch (e) {
          console.warn('Could not cache success data:', e);
        }
      } else {
        console.error("Payment validation failed:", response);
        // Auto-retry logic
        if (retryCount < maxRetries && !forceRetry) {
          console.log(`Auto-retrying payment validation... (${retryCount + 1}/${maxRetries})`);
          setRetryCount((prev) => prev + 1);
          setTimeout(() => sendtovalidation(true, false), 3000);
          return;
        }
        setHasError(true);
      }
    } catch (error) {
      console.error("Error in PayU success payment:", error);
      console.error("Error details:", {
        message: error.message,
        status: error.status,
        response: error.response
      });

      // Auto-retry logic for errors
      if (retryCount < maxRetries && !forceRetry) {
        console.log(`Auto-retrying due to error... (${retryCount + 1}/${maxRetries})`);
        setRetryCount((prev) => prev + 1);
        setTimeout(() => sendtovalidation(true, false), 3000);
        return;
      }
      setHasError(true);
    } finally {
      console.log("Setting loading to false");
      setIsLoading(false);
      setIsRetrying(false);
    }
  };

  const redirectToCourse = () => {
    navigate("/user/courses");
  };

  const handleManualRetry = () => {
    console.log('Manual retry triggered');
    hasInitialized.current = false;
    setRetryCount(0);
    setIsLoading(true);
    setHasError(false);
    sendtovalidation(false, true);
  };

  // Extract query params when location.search changes
  useEffect(() => {
    const queryParams = new URLSearchParams(location.search);
    const course_id = queryParams.get("course_id");
    const txnid = queryParams.get("txnid");

    if (course_id && txnid) {
      setCourseId(course_id);
      setTxnId(txnid);
      setQueryReady(true);
    } else {
      setIsLoading(false);
      setHasError(true);
    }
  }, [location.search]);

  // Run validation only when query params are set
  useEffect(() => {
    if (queryReady && courseId && txnId && !hasInitialized.current) {
      console.log('Starting payment validation...');
      sendtovalidation();
    }
  }, [queryReady, courseId, txnId]);

  // Additional effect to handle page visibility changes (when user comes back from PayU)
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible' && courseId && txnId && !data.payment_id && !isLoading) {
        console.log('Page became visible, retrying validation...');
        hasInitialized.current = false;
        setIsLoading(true);
        setHasError(false);
        sendtovalidation();
      }
    };

    const handleWindowFocus = () => {
      if (courseId && txnId && !data.payment_id && !isLoading) {
        console.log('Window focused, retrying validation...');
        hasInitialized.current = false;
        setIsLoading(true);
        setHasError(false);
        sendtovalidation();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    window.addEventListener('focus', handleWindowFocus);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('focus', handleWindowFocus);
      if (authCheckInterval.current) {
        clearInterval(authCheckInterval.current);
      }
    };
  }, [courseId, txnId, data.payment_id, isLoading]);

  // Emergency fallback - try to load data immediately on mount
  useEffect(() => {
    const emergencyTimeout = setTimeout(() => {
      if (courseId && txnId && isLoading && !data.payment_id && retryCount === 0) {
        console.log('Emergency fallback triggered - forcing validation...');
        hasInitialized.current = false;
        sendtovalidation(false, true);
      }
    }, 3000);

    return () => clearTimeout(emergencyTimeout);
  }, [courseId, txnId, isLoading, data.payment_id, retryCount]);

  // Periodic authentication check
  useEffect(() => {
    if (courseId && txnId && isLoading) {
      authCheckInterval.current = setInterval(() => {
        if (checkAuthReady() && !authReady) {
          console.log('Authentication became ready during loading');
          setAuthReady(true);
          if (!data.payment_id) {
            hasInitialized.current = false;
            sendtovalidation(false, true);
          }
        }
      }, 2000);

      return () => {
        if (authCheckInterval.current) {
          clearInterval(authCheckInterval.current);
        }
      };
    }
  }, [courseId, txnId, isLoading, authReady, data.payment_id]);

  if (isLoading) {
    return (
      <div className="d-flex justify-content-center align-items-center" style={{ minHeight: "60vh" }}>
        <div className="text-center">
          <div className="spinner-border text-primary mb-3" role="status" />
          <p className="text-muted">
            {isRetrying
              ? `Retrying payment verification... (Attempt ${retryCount + 1}/${maxRetries})`
              : "Verifying your PayU payment..."}
          </p>
          {retryCount > 0 && (
            <small className="text-info d-block mt-2">
              Please wait, we're ensuring your payment is properly processed...
            </small>
          )}
          {!authReady && (
            <small className="text-warning d-block mt-2">
              <Icon icon="mdi:shield-check" className="me-1" />
              Waiting for secure authentication...
            </small>
          )}
        </div>
      </div>
    );
  }

  if (hasError) {
    return (
      <div className="payment-error-container">
        <div className="error-content text-center">
          <Icon icon="mdi:alert-circle" className="text-danger mb-3" width="64" height="64" />
          <h3>Payment Verification Issue</h3>
          <p className="text-muted mb-4">
            We're having trouble verifying your PayU payment. Your payment may have been successful, but we need to
            confirm it.
          </p>
          <div className="d-flex gap-3 justify-content-center">
            <button className="btn btn-primary px-4 py-2" onClick={handleManualRetry}>
              <Icon icon="mdi:refresh" className="me-2" />
              Try Again
            </button>
            <button className="btn btn-outline-secondary px-4 py-2" onClick={() => navigate("/")}>
              Back to Home
            </button>
          </div>
          <div className="mt-3">
            <small className="text-muted">
              Transaction ID: {txnId}
              <br />
              If the issue persists, please contact support with this transaction ID.
            </small>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="success-payment-container">
      <div className="success-payment-card">
        {/* Success Icon */}
        <div className="success-icon-wrapper mb-4">
          <Icon icon="mdi:check-circle" className="text-success" width="64" height="64" />
        </div>

        {/* Success Message */}
        <h2 className="success-title">PayU Payment Successful!</h2>
        <p className="success-subtitle">Your transaction has been completed successfully via PayU.</p>

        {/* Amount */}
        <div className="amount-display my-4">
          <span className="amount-value">₹{data.price ?? "0"}</span>
        </div>

        {/* Transaction Details */}
        <div className="transaction-details">
          <div className="detail-row">
            <span className="detail-label">Transaction ID</span>
            <span className="detail-value">
              #{data.payment_id ? data.payment_id.toString().toUpperCase().slice(0, 15) : "-"}
            </span>
          </div>
          <div className="detail-row">
            <span className="detail-label">PayU Transaction ID</span>
            <span className="detail-value">
              #{data.transaction_id ? data.transaction_id.toString().toUpperCase().slice(0, 15) : "-"}
            </span>
          </div>
          <div className="detail-row">
            <span className="detail-label">Date</span>
            <span className="detail-value">
              {new Date().toLocaleDateString("en-IN", {
                year: "numeric",
                month: "long",
                day: "numeric",
              })}
            </span>
          </div>
          <div className="detail-row">
            <span className="detail-label">Status</span>
            <span className="detail-value status-success">
              <Icon icon="mdi:check-circle" className="me-1" />
              Completed
            </span>
          </div>
          <div className="detail-row">
            <span className="detail-label">Payment Method</span>
            <span className="detail-value">PayU Gateway</span>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="action-buttons mt-4">
          <button className="btn btn-primary btn-lg w-100 mb-3" onClick={redirectToCourse}>
            <Icon icon="mdi:play-circle" className="me-2" />
            Go Back to Course
          </button>
        </div>
      </div>
    </div>
  );
};

export default SuccessPayUPayment;
