import React, { useState, useEffect, useCallback } from 'react';
import { Icon } from '@iconify/react';
import { useParams, useLocation } from 'react-router-dom';
import { decodeData } from '../../../utils/encodeAndEncode';
import { toast } from 'react-toastify';
import { getModuleQuestions, getCourseVideos, addQuestion, editQuestion, deleteQuestion } from '../../../services/adminService';
import { usePermissions } from '../../../context/PermissionsContext';

function CourseQuiz() {
    const { courseId, moduleId, contentId } = useParams();
    const location = useLocation();
    const { permissions } = usePermissions();

    // Decode IDs
    const decodedCourseId = decodeData(courseId);
    const decodedModuleId = decodeData(moduleId);
    const decodedContentId = decodeData(contentId);

    // API data states
    const [apiQuestions, setApiQuestions] = useState([]);
    const [courseVideos, setCourseVideos] = useState([]);
    const [loading, setLoading] = useState(true);
    const [videosLoading, setVideosLoading] = useState(true);

    // Form data states
    const [questionData, setQuestionData] = useState({
        question: '',
        question_type: 'mcq',
        question_content_type: 'text',
        question_media_file: null,
        question_media_preview: null,
        options: {},
    });

    // UI control states
    const [showModal, setShowModal] = useState(false);
    const [selectedVideo, setSelectedVideo] = useState("");
    const [showVideoPlayer, setShowVideoPlayer] = useState(false);
    const [timestamp, setTimestamp] = useState("");

    // Options states
    const [options, setOptions] = useState([{
        id: Date.now(),
        type: 'text',
        text: '',
        file: null,
        isCorrect: false,
        optionKey: 'option1'
    }]);
    const [optionMediaPreviews, setOptionMediaPreviews] = useState([]);
    const [optionMediaFiles, setOptionMediaFiles] = useState([]);
    const [changedOptions, setChangedOptions] = useState({});
    const [optionContentTypes, setOptionContentTypes] = useState([]);
    const [correctAnswers, setCorrectAnswers] = useState([]);

    // Edit mode states
    const [isEditMode, setIsEditMode] = useState(false);
    const [editingQuestion, setEditingQuestion] = useState(null);

    // Delete confirmation states
    const [showDeleteModal, setShowDeleteModal] = useState(false);
    const [questionToDelete, setQuestionToDelete] = useState(null);

    // Add new state for submit loading
    const [submitting, setSubmitting] = useState(false);

    // Validation states
    const [errors, setErrors] = useState({
        question: '',
        questionMedia: '',
        options: [],
        correctOption: ''
    });

    // Reset errors when modal opens
    useEffect(() => {
        if (showModal) {
            setErrors({
                question: '',
                questionMedia: '',
                options: [],
                correctOption: ''
            });
        }
    }, [showModal]);

    // Fetch course videos
    useEffect(() => {
        const fetchCourseVideos = async () => {
            try {
                setVideosLoading(true);
                if (!decodedCourseId || !decodedModuleId) {
                    toast.error('Missing required parameters for videos');
                    return;
                }

                const response = await getCourseVideos({
                    course_id: decodedCourseId,
                    module_id: decodedModuleId
                });

                console.log('Course Videos Response:', response);
                if (response.success) {
                    setCourseVideos(response.data || []);
                } else {
                    toast.error('Failed to load videos');
                }
            } catch (error) {
                console.error('Error fetching course videos:', error);
                toast.error('Failed to fetch course videos');
            } finally {
                setVideosLoading(false);
            }
        };

        fetchCourseVideos();
    }, [decodedCourseId, decodedModuleId]);

    // Existing questions fetch effect
    useEffect(() => {
        const fetchQuestions = async () => {
            try {
                if (!decodedModuleId || !decodedContentId) {
                    toast.error('Missing required parameters');
                    return;
                }

                const response = await getModuleQuestions({
                    module_id: decodedModuleId,
                    assessment_id: decodedContentId
                });

                console.log('Questions Response:', response);

                if (response.success && response.data.response) {
                    setApiQuestions(response.data.response);
                } else {
                    toast.error('Failed to load questions');
                }
            } catch (error) {
                console.error('Error fetching questions:', error);
                toast.error('Failed to fetch questions');
            } finally {
                setLoading(false);
            }
        };

        fetchQuestions();
    }, [decodedModuleId, decodedContentId]);

    // Handle question text change
    const handleQuestionChange = (e) => {
        setQuestionData(prev => ({
            ...prev,
            question: e.target.value
        }));
        if (errors.question) {
            setErrors(prev => ({
                ...prev,
                question: ''
            }));
        }
    };

    // Handle question media file change
    const handleQuestionMediaChange = (e) => {
        const file = e.target.files[0];
        if (file) {
            // Create a URL for the file preview
            const previewUrl = URL.createObjectURL(file);
            setQuestionData(prev => ({
                ...prev,
                question_media_file: file,
                question_media_preview: previewUrl
            }));
            if (errors.questionMedia) {
                setErrors(prev => ({
                    ...prev,
                    questionMedia: ''
                }));
            }

            // Clean up the old preview URL when component unmounts
            return () => URL.revokeObjectURL(previewUrl);
        }
    };

    // Handle option change
    const handleOptionChange = (index, field, value) => {
        const newOptions = [...options];
        newOptions[index][field] = value;
        setOptions(newOptions);

        // Clear error for this option
        if (errors.options[index]) {
            const newOptionErrors = [...errors.options];
            newOptionErrors[index] = [];
            setErrors(prev => ({
                ...prev,
                options: newOptionErrors
            }));
        }

        // Update options data for API
        const optionKey = `option${index + 1}`;
        setQuestionData(prev => ({
            ...prev,
            options: {
                ...prev.options,
                [optionKey]: value
            }
        }));

        // Mark option as changed
        setChangedOptions(prev => ({
            ...prev,
            [optionKey]: true
        }));

        // If changing the type, update content types array
        if (field === 'type') {
            const newContentTypes = [...optionContentTypes];
            newContentTypes[index] = value;
            setOptionContentTypes(newContentTypes);
        }
    };

    // Handle option media change
    const handleOptionMediaChange = (index, file) => {
        if (file) {
            // Create preview URL for the new file
            const previewUrl = URL.createObjectURL(file);
            
            // Update option media files
            const newOptionMediaFiles = [...optionMediaFiles];
            newOptionMediaFiles[index] = file;
            setOptionMediaFiles(newOptionMediaFiles);

            // Update option media previews
            const newOptionMediaPreviews = [...optionMediaPreviews];
            newOptionMediaPreviews[index] = previewUrl;
            setOptionMediaPreviews(newOptionMediaPreviews);

            // Mark option as changed
            const option = options[index];
            setChangedOptions(prev => ({
                ...prev,
                [option.optionKey]: true
            }));

            // Clear any errors
            if (errors.options[index]?.includes(`Option ${options[index].type} file is required`)) {
                const newOptionErrors = [...errors.options];
                newOptionErrors[index] = newOptionErrors[index].filter(
                    error => !error.includes(`Option ${options[index].type} file is required`)
                );
                setErrors(prev => ({
                    ...prev,
                    options: newOptionErrors
                }));
            }

            // Clean up old preview URL when component unmounts
            return () => URL.revokeObjectURL(previewUrl);
        }
    };

    // Handle option content type change
    const handleOptionContentTypeChange = (index, type) => {
        const newContentTypes = [...optionContentTypes];
        newContentTypes[index] = type;
        setOptionContentTypes(newContentTypes);

        // Mark option as changed
        const option = options[index];
        setChangedOptions(prev => ({
            ...prev,
            [option.optionKey]: true
        }));
    };

    // Handle correct option change
    const handleCorrectOptionChange = (index, value) => {
        const newOptions = [...options];
        newOptions[index].isCorrect = value;
        setOptions(newOptions);

        // Clear correct option error if at least one option is now marked as correct
        if (value && errors.correctOption) {
            setErrors(prev => ({
                ...prev,
                correctOption: ''
            }));
        }

        // Update correctAnswers array based on the selected options
        const selectedOption = newOptions[index];
        const optionIdentifier = selectedOption.text || `option_${selectedOption.optionKey}`;

        if (value) {
            setCorrectAnswers(prev => {
                if (!prev.includes(optionIdentifier)) {
                    return [...prev, optionIdentifier];
                }
                return prev;
            });
        } else {
            setCorrectAnswers(prev => prev.filter(text => text !== optionIdentifier));
        }
    };

    // Handle edit button click
    const handleEditClick = (question) => {
        if (!permissions.course_module_content_management_edit) {
            toast.warning("You don't have permission to edit assessment questions");
            return;
        }
        setIsEditMode(true);
        setEditingQuestion(question);

        // Set question data
        setQuestionData({
            question: question.question,
            question_type: question.question_type,
            question_content_type: question.question_content_type,
            question_media_file: null,
            question_media_preview: question.question_media_url,
            options: {},
        });

        // Set options and their media previews
        const formattedOptions = question.formatted_options.map((opt, index) => ({
            id: Date.now() + index,
            type: opt.content_type,
            text: opt.content_value,
            file: null,
            isCorrect: opt.isCorrect,
            optionKey: opt.key,
            media_url: opt.media_url
        }));
        setOptions(formattedOptions);

        // Set option media previews and content types
        const previews = question.formatted_options.map(opt => opt.media_url || null);
        const contentTypes = question.formatted_options.map(opt => opt.content_type || 'text');
        setOptionMediaPreviews(previews);
        setOptionContentTypes(contentTypes);
        setOptionMediaFiles(new Array(previews.length).fill(null));

        // Set correct answers
        const correctAnswers = question.formatted_options
            .filter(opt => opt.isCorrect)
            .map(opt => opt.content_value);
        setCorrectAnswers(correctAnswers);

        // Reset changed options tracking
        setChangedOptions({});

        // Set video data if exists
        if (question.ref_video_id) {
            setSelectedVideo(question.ref_video_id.toString());
            setShowVideoPlayer(true);
        }
        if (question.video_timestamp) {
            setTimestamp(question.video_timestamp);
        }

        setShowModal(true);
    };

    // Reset form function
    const resetForm = () => {
        setQuestionData({
            question: '',
            question_type: 'mcq',
            question_content_type: 'text',
            question_media_file: null,
            question_media_preview: null,
            options: {},
        });
        setOptions([{
            id: Date.now(),
            type: 'text',
            text: '',
            file: null,
            isCorrect: false,
            optionKey: 'option1'
        }]);
        // Reset all option-related states
        setOptionMediaPreviews([]);
        setOptionMediaFiles([]);
        setChangedOptions({});
        setOptionContentTypes([]);
        setCorrectAnswers([]);
        setSelectedVideo('');
        setShowVideoPlayer(false);
        setTimestamp('');
        setIsEditMode(false);
        setEditingQuestion(null);
    };

    const validateForm = () => {
        const newErrors = {
            question: '',
            questionMedia: '',
            options: [],
            correctOption: ''
        };
        let isValid = true;

        // Validate question text
        if (!questionData.question.trim()) {
            newErrors.question = 'Question text is required';
            isValid = false;
        } else if (questionData.question.trim().length > 500) {
            newErrors.question = 'Question text cannot exceed 500 characters';
            isValid = false;
        }

        // Validate question media if content type is not text
        if (questionData.question_content_type !== 'text') {
            if (!questionData.question_media_file && !questionData.question_media_preview) {
                newErrors.questionMedia = `Question ${questionData.question_content_type} is required`;
                isValid = false;
            }
        }

        // Validate options
        const optionErrors = options.map(option => {
            const errors = [];

            // Validate option text
            if (option.type === 'text') {
                if (!option.text.trim()) {
                    errors.push('Option text is required');
                    isValid = false;
                }
            } else {
                // For media options, validate both fallback text and media file
                if (!option.text.trim()) {
                    errors.push('Fallback text is required for media option');
                    isValid = false;
                }

                // Check for media file in both new uploads and existing previews
                const optionIndex = options.indexOf(option);
                if (!optionMediaFiles[optionIndex] && !optionMediaPreviews[optionIndex]) {
                    errors.push(`Option ${option.type} file is required`);
                    isValid = false;
                }
            }
            return errors;
        });

        // Check if at least one option is marked as correct
        const hasCorrectOption = options.some(option => option.isCorrect);
        if (!hasCorrectOption) {
            newErrors.correctOption = 'Please mark at least one option as correct';
            isValid = false;
        }

        newErrors.options = optionErrors;
        setErrors(newErrors);
        return isValid;
    };

    // Modify handleSubmit to handle both add and edit
    const handleSubmit = async () => {
        if (!validateForm()) {
            return;
        }
        try {
            setSubmitting(true); // Start loading
            const formData = new FormData();

            if (isEditMode) {
                // Edit mode payload
                formData.append('id', editingQuestion.id);
                formData.append('question_type', questionData.question_type);
                formData.append('question_text', questionData.question);
                formData.append('question_content_type', questionData.question_content_type);
                formData.append('assessment_id', decodedContentId);
                formData.append('module_id', decodedModuleId);

                // Convert options to the format expected by the API
                const formattedOptions = {};
                options.forEach(option => {
                    formattedOptions[option.optionKey] = option.text || `option_${option.optionKey}`;
                });
                formData.append('options', JSON.stringify(formattedOptions));

                // Add correct options - ensure consistency with option values
                const correctOptionTexts = options
                    .filter(option => option.isCorrect)
                    .map(option => option.text || `option_${option.optionKey}`);

                // Debug logging
                console.log('🔍 Edit Mode - Options with isCorrect status:', options.map(opt => ({
                    key: opt.optionKey,
                    text: opt.text,
                    isCorrect: opt.isCorrect
                })));
                console.log('🔍 Edit Mode - Correct option texts being sent:', correctOptionTexts);

                formData.append('correct_option', JSON.stringify(correctOptionTexts));

                // Handle question media
                if (questionData.question_media_file) {
                    formData.append('question_media', questionData.question_media_file);
                } else if (questionData.question_media_preview) {
                    formData.append('existing_question_media_url', questionData.question_media_preview);
                }

                // Handle options data with media
                const optionsData = options.map((option, index) => ({
                    option_key: option.optionKey,
                    content_type: optionContentTypes[index] || 'text',
                    text_value: option.text || `option_${option.optionKey}`, // Use same fallback as correct_option
                    is_correct: option.isCorrect,
                    is_changed: changedOptions[option.optionKey] || false,
                    existing_media_url: optionMediaPreviews[index] && !optionMediaFiles[index]
                        ? optionMediaPreviews[index]
                        : null
                }));

                // Find deleted options by comparing original options with current options
                const originalOptionKeys = editingQuestion.formatted_options.map(opt => opt.key);
                const deletedOptionKeys = originalOptionKeys.filter(originalKey =>
                    !options.some(opt => opt.optionKey === originalKey)
                );

                // Append option media files
                options.forEach((option, index) => {
                    if (optionMediaFiles[index]) {
                        formData.append(`option_media_${option.optionKey}`, optionMediaFiles[index]);
                    }
                });

                formData.append('options_data', JSON.stringify(optionsData));
                formData.append('deleted_option_keys', JSON.stringify(deletedOptionKeys));

                // Add video reference data
                if (selectedVideo) {
                    const selectedVideoData = courseVideos.find(v => v.id.toString() === selectedVideo);
                    if (selectedVideoData) {
                        formData.append('ref_video_id', selectedVideoData.id);
                    }
                }
                if (timestamp) {
                    formData.append('video_timestamp', timestamp);
                }

                // Log FormData for debugging
                for (let pair of formData.entries()) {
                    console.log(pair[0] + ': ' + pair[1]);
                }

                const response = await editQuestion(formData);
                console.log('Edit question response:', response);

                if (response && response.success) {
                    toast.success('Question updated successfully!');
                    setShowModal(false);
                    setIsEditMode(false);
                    setChangedOptions({});
                    resetForm();

                    // Refresh questions from API to get updated media URLs
                    // This ensures that newly uploaded media files show their correct server URLs
                    const questionsResponse = await getModuleQuestions({
                        module_id: decodedModuleId,
                        assessment_id: decodedContentId
                    });
                    if (questionsResponse.success && questionsResponse.data.response) {
                        setApiQuestions(questionsResponse.data.response);
                    }
                } else {
                    toast.error(response?.error || 'Failed to update question');
                }
            } else {
                // Add mode payload
                formData.append('module_id', decodedModuleId);
                formData.append('assessment_id', decodedContentId);
                formData.append('question_type', questionData.question_type);
                formData.append('question_text', questionData.question);
                formData.append('question_content_type', questionData.question_content_type);

                // Convert options to the format expected by the API
                const formattedOptions = {};
                options.forEach(option => {
                    formattedOptions[option.optionKey] = option.text;
                });
                formData.append('options', JSON.stringify(formattedOptions));

                // Add correct options - ensure consistency with text_value sent in optionsData
                const correctOptionTexts = options
                    .filter(option => option.isCorrect)
                    .map(option => option.text || `option_${option.optionKey}`); // Use same fallback as in optionsData

                // Debug logging
                console.log('🔍 Add Mode - Options with isCorrect status:', options.map(opt => ({
                    key: opt.optionKey,
                    text: opt.text,
                    type: opt.type,
                    isCorrect: opt.isCorrect
                })));
                console.log('🔍 Add Mode - Correct option texts being sent:', correctOptionTexts);

                formData.append('correct_option', JSON.stringify(correctOptionTexts));

                // Handle question media
                if (questionData.question_media_file) {
                    formData.append('question_media', questionData.question_media_file);
                }

                // Handle options data with media
                const optionsData = options.map((option, index) => ({
                    option_key: option.optionKey,
                    content_type: option.type,
                    text_value: option.text || `option_${option.optionKey}`, // Use same fallback as correct_option
                    is_correct: option.isCorrect
                }));

                // Append option media files
                options.forEach((option, index) => {
                    if (optionMediaFiles[index]) {
                        formData.append(`option_media_${option.optionKey}`, optionMediaFiles[index]);
                    }
                });

                formData.append('options_data', JSON.stringify(optionsData));

                // Add video reference data
                if (selectedVideo) {
                    const selectedVideoData = courseVideos.find(v => v.id.toString() === selectedVideo);
                    if (selectedVideoData) {
                        formData.append('ref_video_id', selectedVideoData.id);
                    }
                }
                if (timestamp) {
                    formData.append('video_timestamp', timestamp);
                }

                const response = await addQuestion(formData);
                console.log('Add question response:', response);

                if (response && response.success) {
                    toast.success('Question added successfully!');
                    setShowModal(false);
                    resetForm();

                    // Refresh questions from API to get updated media URLs
                    // This ensures that newly uploaded media files show their correct server URLs
                    const questionsResponse = await getModuleQuestions({
                        module_id: decodedModuleId,
                        assessment_id: decodedContentId
                    });
                    if (questionsResponse.success && questionsResponse.data.response) {
                        setApiQuestions(questionsResponse.data.response);
                    }
                } else {
                    toast.error(response?.error || 'Failed to add question');
                }
            }
        } catch (error) {
            console.error(`Error ${isEditMode ? 'editing' : 'adding'} question:`, error);
            toast.error(error.message || `An error occurred while ${isEditMode ? 'editing' : 'adding'} the question`);
        } finally {
            setSubmitting(false); // Stop loading
        }
    };

    const addOption = () => {
        const newOptionIndex = options.length + 1;
        setOptions([
            ...options,
            {
                id: Date.now(),
                type: 'text',
                text: '',
                file: null,
                isCorrect: false,
                optionKey: `option${newOptionIndex}`
            }
        ]);

        // Initialize related arrays for the new option
        setOptionMediaPreviews(prev => [...prev, null]);
        setOptionMediaFiles(prev => [...prev, null]);
        setOptionContentTypes(prev => [...prev, 'text']);
    };

    const handleDeleteOption = (index) => {
        const newOptions = options.filter((_, i) => i !== index);
        setOptions(newOptions);

        // Update related arrays to remove entries at the deleted index
        setOptionMediaPreviews(prev => prev.filter((_, i) => i !== index));
        setOptionMediaFiles(prev => prev.filter((_, i) => i !== index));
        setOptionContentTypes(prev => prev.filter((_, i) => i !== index));

        // Update correctAnswers to remove any reference to the deleted option
        const deletedOption = options[index];
        if (deletedOption && deletedOption.isCorrect) {
            const optionIdentifier = deletedOption.text || `option_${deletedOption.optionKey}`;
            setCorrectAnswers(prev => prev.filter(text => text !== optionIdentifier));
        }
    };

    const renderQuestionContent = (question) => {
        switch (question.question_content_type) {
            case 'image':
                return (
                    <div className="mb-3">
                        <img
                            src={question.question_media_url}
                            alt={question.question}
                            className="img-fluid"
                            style={{ maxHeight: '300px' }}
                        />
                        <p className="mt-2">{question.question}</p>
                    </div>
                );
            case 'audio':
                return (
                    <div className="mb-3">
                        <audio
                            controls
                            preload="metadata"
                            className="w-100 mb-2"
                            style={{ minWidth: '250px' }}
                        >
                            <source src={question.question_media_url} type="audio/mpeg" />
                            <source src={question.question_media_url} type="audio/wav" />
                            <source src={question.question_media_url} type="audio/ogg" />
                            Your browser does not support the audio element.
                        </audio>
                        <p>{question.question}</p>
                    </div>
                );
            default: // text
                return <h6 className="mb-3">{question.question}</h6>;
        }
    };

    const renderOptionContent = (option) => {
        switch (option.content_type) {
            case 'image':
                return (
                    <div className="d-flex align-items-center gap-2">
                        <img
                            src={option.media_url}
                            alt={option.text_fallback}
                            style={{ height: '50px', width: '50px', objectFit: 'cover' }}
                        />
                        <span>{option.text_fallback}</span>
                    </div>
                );
            case 'audio':
                return (
                    <div className="d-flex align-items-center gap-2">
                        <audio
                            controls
                            preload="metadata"
                            style={{ height: '40px', minWidth: '250px' }}
                        >
                            <source src={option.media_url} type="audio/mpeg" />
                            <source src={option.media_url} type="audio/wav" />
                            <source src={option.media_url} type="audio/ogg" />
                            Your browser does not support the audio element.
                        </audio>
                        <span>{option.text_fallback || option.content_value}</span>
                    </div>
                );
            default: // text
                return <span>{option.content_value}</span>;
        }
    };

    // Handle delete question
    const handleDeleteClick = (question) => {
        if (!permissions.course_module_content_management_delete) {
            toast.warning("You don't have permission to delete assessment questions");
            return;
        }
        setQuestionToDelete(question);
        setShowDeleteModal(true);
    };

    const handleDeleteConfirm = async () => {
        try {
            if (!questionToDelete) return;

            const response = await deleteQuestion({
                question_id: questionToDelete.id
            });

            if (response && response.success) {
                toast.success('Question deleted successfully');
                // Remove the question from the state
                setApiQuestions(prev => prev.filter(q => q.id !== questionToDelete.id));
            } else {
                toast.error(response?.error || 'Failed to delete question');
            }
        } catch (error) {
            console.error('Error deleting question:', error);
            toast.error(error.message || 'An error occurred while deleting the question');
        } finally {
            setShowDeleteModal(false);
            setQuestionToDelete(null);
        }
    };

    // Handle add question click
    const handleAddClick = () => {
        if (!permissions.course_module_content_management_create) {
            toast.warning("You don't have permission to create assessment questions");
            return;
        }
        resetForm();
        setShowModal(true);
    };

    return (
        <>
            <div className="row">
                <div className="col-12 mb-2 d-flex justify-content-between align-items-center mt-2">
                    <h4 className="mb-0">Course Assessment</h4>
                    <button
                        className="btn btn-primary text-nowrap d-flex align-items-center gap-2"
                        style={{ width: '150px' }}
                        onClick={handleAddClick}
                        disabled={!permissions.course_module_content_management_create}
                        title={!permissions.course_module_content_management_create ? "You don't have permission to create questions" : "Add new question"}
                    >
                        <Icon icon="fluent:add-24-regular" width="20" height="20" />
                        Add Question
                    </button>
                </div>

                <div className="col-12">
                    <div className="card">
                        <div className="card-body">
                            {loading ? (
                                <div className="text-center py-4">
                                    <div className="spinner-border text-primary" role="status">
                                        <span className="visually-hidden">Loading...</span>
                                    </div>
                                </div>
                            ) : apiQuestions.length === 0 ? (
                                <div className="text-center py-4">
                                    <p>No questions available</p>
                                </div>
                            ) : (
                                apiQuestions.map((question, index) => (
                                    <div className="card mb-3" key={question.id}>
                                        <div className="row">
                                            <div className="col-12 d-flex justify-content-end p-2">
                                                <div className="d-flex gap-2">
                                                    <button
                                                        className="btn btn-outline-dark btn-sm border border-dark"
                                                        style={{ width: '40px', height: '40px' }}
                                                        title={!permissions.course_module_content_management_edit ? "You don't have permission to edit questions" : "Edit Question"}
                                                        onClick={() => handleEditClick(question)}
                                                        disabled={!permissions.course_module_content_management_edit}
                                                    >
                                                        <Icon icon="fluent:edit-24-regular" width="16" height="16" />
                                                    </button>
                                                    <button
                                                        className="btn btn-outline-danger btn-sm border border-danger"
                                                        style={{ width: '40px', height: '40px' }}
                                                        title={!permissions.course_module_content_management_delete ? "You don't have permission to delete questions" : "Delete Question"}
                                                        onClick={() => handleDeleteClick(question)}
                                                        disabled={!permissions.course_module_content_management_delete}
                                                    >
                                                        <Icon icon="fluent:delete-24-regular" width="16" height="16" />
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                        <div className="card-body">
                                            {/* Question Section */}
                                            <div className="question-section mb-4">
                                                <div className="d-flex align-items-center gap-2 mb-3">
                                                    <span className="badge bg-primary">{index + 1}</span>
                                                    <h6 className="mb-0 flex-grow-1">{question.question}</h6>
                                                </div>
                                                
                                                {/* Question Media */}
                                                {question.question_content_type !== 'text' && (
                                                    <div className="question-media mb-3 bg-light rounded p-3">
                                                        {question.question_content_type === 'image' ? (
                                                            <img 
                                                                src={question.question_media_url} 
                                                                alt={question.question} 
                                                                className="img-fluid rounded"
                                                                style={{ maxHeight: '300px' }}
                                                            />
                                                        ) : (
                                                            <audio 
                                                                controls 
                                                                preload="metadata"
                                                                className="w-100"
                                                                style={{ minWidth: '250px' }}
                                                            >
                                                                <source src={question.question_media_url} type="audio/mpeg" />
                                                                Your browser does not support the audio element.
                                                            </audio>
                                                        )}
                                                    </div>
                                                )}

                                                {/* Video Reference */}
                                                {question.video_url && (
                                                    <div className="video-reference bg-light rounded p-2">
                                                        <div className="d-flex align-items-center gap-2">
                                                            <Icon icon="fluent:video-24-regular" className="text-primary" />
                                                            <span>
                                                                {(() => {
                                                                    const video = courseVideos.find(v => v.id === question.ref_video_id);
                                                                    return video ? (video.video_title || video.video_name) : 'Untitled Video';
                                                                })()}
                                                            </span>
                                                            {question.video_timestamp && (
                                                                <span className="badge bg-light text-dark border">
                                                                    <Icon icon="fluent:clock-24-regular" className="me-1" />
                                                                    {question.video_timestamp}
                                                                </span>
                                                            )}
                                                        </div>
                                                    </div>
                                                )}
                                            </div>

                                            {/* Options Section */}
                                            <div className="options-section">
                                                <div className="mb-2">
                                                    <small className="text-muted text-uppercase">Options</small>
                                                </div>
                                                <div className="options-list">
                                                    {question.formatted_options.map((option, optIndex) => (
                                                        <div 
                                                            key={option.key} 
                                                            className={`option-item p-3 rounded mb-2 ${option.isCorrect ? 'bg-success-subtle border border-success' : 'bg-light border'}`}
                                                        >
                                                            {/* Option Header with Icon and Text */}
                                                            <div className="d-flex align-items-center gap-3 mb-2">
                                                                <div className="option-indicator">
                                                                    <Icon 
                                                                        icon={option.isCorrect ? "fluent:checkmark-circle-24-filled" : "fluent:circle-24-regular"} 
                                                                        className={option.isCorrect ? "text-success" : "text-muted"}
                                                                        width="20"
                                                                    />
                                                                </div>
                                                                <div className="option-text flex-grow-1">
                                                                    <span className="d-block">
                                                                        {option.content_type === 'text' 
                                                                            ? option.content_value 
                                                                            : option.text_fallback || option.content_value
                                                                        }
                                                                    </span>
                                                                </div>
                                                            </div>

                                                            {/* Option Media Content */}
                                                            {option.content_type !== 'text' && (
                                                                <div className="option-media ms-4 ps-2 mt-2">
                                                                    {option.content_type === 'image' ? (
                                                                        <div className="bg-white p-2 rounded border">
                                                                            <img 
                                                                                src={option.media_url} 
                                                                                alt={option.text_fallback} 
                                                                                className="rounded"
                                                                                style={{ maxHeight: '150px', maxWidth: '100%', objectFit: 'contain' }}
                                                                            />
                                                                        </div>
                                                                    ) : option.content_type === 'audio' && (
                                                                        <div className="bg-white p-2 rounded border">
                                                                            <audio
                                                                                controls
                                                                                preload="metadata"
                                                                                className="w-100"
                                                                                style={{ maxWidth: '300px' }}
                                                                                key={optionMediaPreviews[optIndex] || option.media_url}
                                                                            >
                                                                                <source src={optionMediaPreviews[optIndex] || option.media_url} type="audio/mpeg" />
                                                                                Your browser does not support the audio element.
                                                                            </audio>
                                                                        </div>
                                                                    )}
                                                                </div>
                                                            )}
                                                        </div>
                                                    ))}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                ))
                            )}
                        </div>
                    </div>
                </div>
            </div>

            {/* Delete Confirmation Modal */}
            {showDeleteModal && (
                <div className="modal show d-block fade" tabIndex="-1" style={{ backgroundColor: "rgba(0,0,0,0.5)" }}>
                    <div className="modal-dialog">
                        <div className="modal-content">
                            <div className="modal-header">
                                <h5 className="modal-title">Delete Question</h5>
                                <button
                                    type="button"
                                    className="btn-close"
                                    onClick={() => {
                                        setShowDeleteModal(false);
                                        setQuestionToDelete(null);
                                    }}
                                ></button>
                            </div>
                            <div className="modal-body">
                                <p>Are you sure you want to delete this question? This action cannot be undone.</p>
                            </div>
                            <div className="modal-footer">
                                <button
                                    type="button"
                                    className="btn btn-secondary"
                                    onClick={() => {
                                        setShowDeleteModal(false);
                                        setQuestionToDelete(null);
                                    }}
                                >
                                    Cancel
                                </button>
                                <button
                                    type="button"
                                    className="btn btn-danger"
                                    onClick={handleDeleteConfirm}
                                >
                                    Delete
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            )}

            {/* Question Modal */}
            {showModal && (
                <div className="modal show d-block fade" tabIndex="-1" style={{ backgroundColor: "rgba(0,0,0,0.5)" }}>
                    <div className="modal-dialog modal-xxl">
                        <div className="modal-content">
                            <div className="modal-header">
                                <h5 className="modal-title">
                                    {isEditMode ? 'Edit Question' : 'Add New Question'}
                                </h5>
                                <button
                                    type="button"
                                    className="btn-close"
                                    onClick={() => {
                                        setShowModal(false);
                                        resetForm();
                                    }}
                                ></button>
                            </div>
                            <div className="modal-body">
                                {/* Question Text Input */}
                                <div className="mb-3">
                                    <label className="form-label">Question Text *</label>
                                    <input
                                        type="text"
                                        className={`form-control ${errors.question ? 'is-invalid' : ''}`}
                                        value={questionData.question}
                                        onChange={handleQuestionChange}
                                        placeholder="Enter your question"
                                        maxLength={500}
                                    />
                                    {errors.question ? (
                                        <div className="invalid-feedback">{errors.question}</div>
                                    ) : (
                                        <small className="text-muted">Maximum 500 characters allowed ({500 - questionData.question.length} remaining)</small>
                                    )}
                                </div>

                                {/* Question Content Type */}
                                <div className="mb-3">
                                    <label className="form-label">Question Content Type</label>
                                    <select
                                        className="form-select"
                                        value={questionData.question_content_type}
                                        onChange={(e) => setQuestionData(prev => ({
                                            ...prev,
                                            question_content_type: e.target.value
                                        }))}
                                    >
                                        <option value="text">Text</option>
                                        <option value="image">Image</option>
                                        <option value="audio">Audio</option>
                                    </select>
                                </div>

                                {/* Question Media Upload */}
                                {questionData.question_content_type !== 'text' && (
                                    <div className="mb-3">
                                        <label className="form-label">Upload {questionData.question_content_type} *</label>
                                        <input
                                            type="file"
                                            className={`form-control ${errors.questionMedia ? 'is-invalid' : ''}`}
                                            accept={questionData.question_content_type === 'image' ? 'image/*' : 'audio/*'}
                                            onChange={handleQuestionMediaChange}
                                        />
                                        {errors.questionMedia && (
                                            <div className="invalid-feedback">{errors.questionMedia}</div>
                                        )}
                                        
                                        {/* Current Media Preview */}
                                        {questionData.question_media_preview && (
                                            <div className="mt-3 p-3 bg-light rounded">
                                                <div className="d-flex align-items-center gap-2 mb-2">
                                                    <Icon 
                                                        icon={questionData.question_content_type === 'audio' ? 'mdi:music-note' : 'mdi:image'} 
                                                        className="text-primary" 
                                                        width="20"
                                                    />
                                                    <small className="text-muted">
                                                        {questionData.question_media_file ? 'New' : 'Current'} {questionData.question_content_type}:
                                                    </small>
                                                </div>
                                                {questionData.question_content_type === 'image' ? (
                                                    <img
                                                        src={questionData.question_media_preview}
                                                        alt="Question media"
                                                        className="img-fluid rounded"
                                                        style={{ maxHeight: '200px' }}
                                                    />
                                                ) : (
                                                    <div className="audio-preview">
                                                        <audio
                                                            controls
                                                            className="w-100"
                                                            style={{ maxWidth: '300px' }}
                                                            key={questionData.question_media_preview}
                                                        >
                                                            <source src={questionData.question_media_preview} type="audio/mpeg" />
                                                            Your browser does not support the audio element.
                                                        </audio>
                                                        <small className="d-block mt-1 text-muted">
                                                            {questionData.question_media_file ? 
                                                                `Selected file: ${questionData.question_media_file.name}` : 
                                                                'Original file'}
                                                        </small>
                                                    </div>
                                                )}
                                            </div>
                                        )}
                                    </div>
                                )}

                                {/* Video Selection */}
                                <div className="mb-3">
                                    <label className="form-label">Select Video</label>
                                    <select
                                        className="form-select"
                                        value={selectedVideo}
                                        onChange={(e) => {
                                            setSelectedVideo(e.target.value);
                                            setShowVideoPlayer(e.target.value !== "");
                                        }}
                                        disabled={videosLoading}
                                    >
                                        <option value="">
                                            {videosLoading ? "Loading videos..." : "Choose a video..."}
                                        </option>
                                        {courseVideos.map(video => (
                                            <option key={video.id} value={video.id}>
                                                {video.video_title || video.video_name} ({video.video_duration})
                                            </option>
                                        ))}
                                    </select>
                                    {courseVideos.length === 0 && !videosLoading && (
                                        <div className="form-text text-muted">
                                            No videos found for this module.
                                        </div>
                                    )}
                                </div>

                                {/* Video Player */}
                                {showVideoPlayer && selectedVideo && (() => {
                                    const selectedVideoData = courseVideos.find(video => video.id.toString() === selectedVideo);
                                    return selectedVideoData ? (
                                        <div className="mb-3">
                                            <div className="card">
                                                <div className="card-body p-0">
                                                    <div style={{ maxWidth: '720px', margin: '0 auto' }}>
                                                        <div className="ratio ratio-16x9">
                                                            <video
                                                                controls
                                                                className="w-100"
                                                                id="questionVideo"
                                                                poster="/src/assets/images/course/course1.png"
                                                                onTimeUpdate={(e) => {
                                                                    const video = e.target;
                                                                    const currentTime = video.currentTime;
                                                                    const minutes = Math.floor(currentTime / 60);
                                                                    const seconds = Math.floor(currentTime % 60);
                                                                    const formattedTime = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
                                                                    setTimestamp(formattedTime);
                                                                }}
                                                            >
                                                                <source src={selectedVideoData.video_url} type="video/mp4" />
                                                                Your browser does not support the video tag.
                                                            </video>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    ) : null;
                                })()}

                                {/* Timestamp Display */}
                                {showVideoPlayer && selectedVideo && (
                                    <div className="mb-3">
                                        <label className="form-label">Video Timestamp (MM:SS)</label>
                                        <input
                                            type="text"
                                            className="form-control"
                                            placeholder="00:00"
                                            value={timestamp}
                                            onChange={(e) => {
                                                setTimestamp(e.target.value);
                                                const video = document.getElementById('questionVideo');
                                                if (video && e.target.value.match(/^\d{2}:\d{2}$/)) {
                                                    const [minutes, seconds] = e.target.value.split(':');
                                                    const timeInSeconds = (parseInt(minutes) * 60) + parseInt(seconds);
                                                    video.currentTime = timeInSeconds;
                                                }
                                            }}
                                            pattern="[0-9]{2}:[0-9]{2}"
                                        />
                                        <small className="text-muted">
                                            Timestamp updates automatically as you move through the video. You can also manually enter a timestamp in MM:SS format.
                                        </small>
                                    </div>
                                )}

                                {/* Options Section */}
                                <div className="mb-3">
                                    <label className="form-label">Options *</label>
                                    {errors.correctOption && (
                                        <div className="alert alert-danger py-2 mb-2">
                                            {errors.correctOption}
                                        </div>
                                    )}
                                    {options.map((option, index) => (
                                        <div className="border rounded p-2 mb-2" key={option.id}>
                                            <div className='d-flex justify-content-between align-items-center'>
                                                <div className="form-check mb-2">
                                                    <input
                                                        className="form-check-input"
                                                        type="checkbox"
                                                        checked={option.isCorrect}
                                                        onChange={(e) => handleCorrectOptionChange(index, e.target.checked)}
                                                    />
                                                    <label className="form-check-label">
                                                        Correct Option
                                                    </label>
                                                </div>
                                                <button
                                                    className={`btn p-1 d-flex align-items-center justify-content-center ${options.length <= 1 ? 'bg-primary text-white' : 'text-danger'}`}
                                                    style={{
                                                        width: '32px',
                                                        height: '32px',
                                                        border: 'none',
                                                        borderRadius: '4px'
                                                    }}
                                                    title={options.length <= 1 ? "Cannot delete the only option" : "Delete option"}
                                                    disabled={options.length <= 1}
                                                    onClick={() => handleDeleteOption(index)}
                                                >
                                                    <Icon icon="fluent:delete-24-regular" width="18" height="18" />
                                                </button>
                                            </div>
                                            <div className="mb-2">
                                                <label className="form-label">Select Option Type</label>
                                                <select
                                                    className="form-select"
                                                    value={option.type}
                                                    onChange={(e) => handleOptionChange(index, 'type', e.target.value)}
                                                >
                                                    <option value="text">Text</option>
                                                    <option value="image">Image</option>
                                                    <option value="audio">Audio</option>
                                                </select>
                                            </div>
                                            {option.type === 'text' && (
                                                <input
                                                    type="text"
                                                    className={`form-control ${errors.options[index]?.length ? 'is-invalid' : ''}`}
                                                    placeholder={`Option ${index + 1}`}
                                                    value={option.text}
                                                    onChange={(e) => handleOptionChange(index, 'text', e.target.value)}
                                                />
                                            )}
                                            {option.type === 'image' && (
                                                <>
                                                    <input
                                                        type="text"
                                                        className={`form-control mb-2 ${errors.options[index]?.includes('Fallback text is required') ? 'is-invalid' : ''}`}
                                                        placeholder={`Fallback Text for Option ${index + 1} (Required)`}
                                                        value={option.text}
                                                        onChange={(e) => handleOptionChange(index, 'text', e.target.value)}
                                                    />
                                                    <input
                                                        type="file"
                                                        className={`form-control ${errors.options[index]?.includes('file is required') ? 'is-invalid' : ''}`}
                                                        accept="image/*"
                                                        onChange={(e) => handleOptionMediaChange(index, e.target.files[0])}
                                                    />
                                                    {/* Image Preview */}
                                                    {(optionMediaPreviews[index] || option.media_url) && (
                                                        <div className="mt-2 p-2 bg-light rounded">
                                                            <div className="d-flex align-items-center gap-2 mb-2">
                                                                <Icon icon="mdi:image" className="text-primary" width="20" />
                                                                <small className="text-muted">
                                                                    {optionMediaFiles[index] ? 'New' : 'Current'} image:
                                                                </small>
                                                            </div>
                                                            <img
                                                                src={optionMediaPreviews[index] || option.media_url}
                                                                alt="Option preview"
                                                                className="img-fluid rounded"
                                                                style={{ maxHeight: '150px', objectFit: 'contain' }}
                                                            />
                                                            {optionMediaFiles[index] && (
                                                                <small className="d-block mt-1 text-muted">
                                                                    Selected file: {optionMediaFiles[index].name}
                                                                </small>
                                                            )}
                                                        </div>
                                                    )}
                                                </>
                                            )}
                                            {option.type === 'audio' && (
                                                <>
                                                    <input
                                                        type="text"
                                                        className={`form-control mb-2 ${errors.options[index]?.includes('Fallback text is required') ? 'is-invalid' : ''}`}
                                                        placeholder={`Fallback Text for Option ${index + 1} (Required)`}
                                                        value={option.text}
                                                        onChange={(e) => handleOptionChange(index, 'text', e.target.value)}
                                                    />
                                                    <input
                                                        type="file"
                                                        className={`form-control ${errors.options[index]?.includes('file is required') ? 'is-invalid' : ''}`}
                                                        accept="audio/*"
                                                        onChange={(e) => handleOptionMediaChange(index, e.target.files[0])}
                                                    />
                                                    {/* Audio Preview */}
                                                    {(optionMediaPreviews[index] || option.media_url) && (
                                                        <div className="mt-2 p-2 bg-light rounded">
                                                            <div className="d-flex align-items-center gap-2 mb-2">
                                                                <Icon icon="mdi:music-note" className="text-primary" width="20" />
                                                                <small className="text-muted">
                                                                    {optionMediaFiles[index] ? 'New' : 'Current'} audio:
                                                                </small>
                                                            </div>
                                                            <audio
                                                                controls
                                                                className="w-100"
                                                                style={{ maxWidth: '300px' }}
                                                                key={optionMediaPreviews[index] || option.media_url}
                                                            >
                                                                <source src={optionMediaPreviews[index] || option.media_url} type="audio/mpeg" />
                                                                Your browser does not support the audio element.
                                                            </audio>
                                                            {optionMediaFiles[index] && (
                                                                <small className="d-block mt-1 text-muted">
                                                                    Selected file: {optionMediaFiles[index].name}
                                                                </small>
                                                            )}
                                                        </div>
                                                    )}
                                                </>
                                            )}
                                            {errors.options[index]?.map((error, errorIndex) => (
                                                <div key={errorIndex} className="invalid-feedback d-block">
                                                    {error}
                                                </div>
                                            ))}
                                        </div>
                                    ))}
                                    <button type="button" className="btn btn-outline-primary btn-sm" onClick={addOption}>
                                        + Add Option
                                    </button>
                                </div>
                            </div>
                            <div className="modal-footer">
                                <button
                                    className="btn btn-secondary"
                                    onClick={() => {
                                        setShowModal(false);
                                        resetForm();
                                    }}
                                    disabled={submitting}
                                >
                                    Cancel
                                </button>
                                <button
                                    className="btn btn-primary d-flex align-items-center gap-2"
                                    onClick={handleSubmit}
                                    disabled={submitting}
                                >
                                    {submitting ? (
                                        <>
                                            <span className="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                            <span>{isEditMode ? 'Updating...' : 'Saving...'}</span>
                                        </>
                                    ) : (
                                        <span>{isEditMode ? 'Update Question' : 'Save Question'}</span>
                                    )}
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            )}
        </>
    );
}

export default CourseQuiz;