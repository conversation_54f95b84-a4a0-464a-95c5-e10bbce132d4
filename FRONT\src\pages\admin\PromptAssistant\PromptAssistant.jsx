import React, { useState } from "react";

// Main App component for the Gemini AI Prompt Playground
const PromptAssistant = () => {
  // State variables to manage prompt input, generated output, and loading status
  const [prompt, setPrompt] = useState("");
  const [output, setOutput] = useState("");
  const [loading, setLoading] = useState(false);

  // The API key is intentionally left as an empty string.
  // The Canvas environment will automatically provide the actual key at runtime.
  const apiKey = "";
  // The API URL for the gemini-2.0-flash model as per guidelines.
  const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${apiKey}`;

  /**
   * Handles the generation of AI output based on the user's prompt.
   * It makes a fetch call to the Gemini API, sets loading states,
   * and updates the output or handles errors.
   */
  const handleGenerate = async () => {
    // Prevent generation if the prompt is empty or just whitespace
    if (!prompt.trim()) {
      setOutput("Please enter a prompt to generate output.");
      return;
    }

    setLoading(true); // Set loading state to true
    setOutput(""); // Clear previous output

    try {
      // Prepare the chat history payload for the API request
      const chatHistory = [];
      chatHistory.push({ role: "user", parts: [{ text: prompt }] });

      // Define the generation configuration with a maximum output token limit.
      // Approximately 2500 tokens correspond to about 2000 words (1 token ~ 4 characters).
      const generationConfig = {
        maxOutputTokens: 2500,
      };

      const payload = {
        contents: chatHistory,
        generationConfig: generationConfig, // Include the generation configuration
      };

      // Make the API call using fetch
      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload)
      });

      // Parse the JSON response
      const result = await response.json();

      // Check if the response contains valid candidates and content
      if (result.candidates && result.candidates.length > 0 &&
          result.candidates[0].content && result.candidates[0].content.parts &&
          result.candidates[0].content.parts.length > 0) {
        const generatedText = result.candidates[0].content.parts[0].text;
        setOutput(generatedText); // Set the generated text as output
      } else {
        // Handle cases where the response structure is unexpected or content is missing
        setOutput("No output received or unexpected response structure.");
        console.error("Unexpected API response structure:", result);
      }
    } catch (error) {
      console.error("Error generating output:", error);
      setOutput("❌ Failed to generate output. Please try again.");
    } finally {
      setLoading(false); // Reset loading state
    }
  };

  return (
    // Include Bootstrap CSS from CDN. For a full-fledged React app, this would typically be in index.html.
    // For a self-contained immersive, including it here ensures it works directly.
    <>
      <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet" xintegrity="sha384-QWTKZyjpPEjISv5WaRU9OFeRpok6YctnYmDr5pNlyT2bRjXh0JMhjY6hW+ALEwIH" crossOrigin="anonymous"></link>
      <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js" xintegrity="sha384-YvpcrYf0tY3lHB60NNkmXc5s9fDVZLESaAA55NDzOxhy9GkcIdslK1eN7N6jIeHz" crossOrigin="anonymous"></script>

      {/* Main container with Bootstrap styling */}
      <div className="container my-5 p-4 bg-white shadow-lg rounded-3">
        {/* Page Title */}
        <h2 className="text-center mb-4 display-6 fw-bold text-dark">
          <span role="img" aria-label="magnifying glass">🔍</span> Gemini AI Prompt Playground
        </h2>

        {/* Prompt Input Section */}
        <div className="mb-4">
          <label htmlFor="prompt-input" className="form-label fs-5 fw-semibold text-dark">
            <span role="img" aria-label="pencil and paper">📝</span> Type your request:
          </label>
          <textarea
            id="prompt-input"
            rows="6"
            className="form-control rounded-3"
            placeholder="e.g. Write a support ticket for a payment failure issue"
            value={prompt}
            onChange={(e) => setPrompt(e.target.value)}
            aria-label="Prompt input area"
          />
        </div>

        {/* Generate Button */}
        <button
          onClick={handleGenerate}
          disabled={loading}
          className={`btn btn-primary btn-lg w-100 rounded-3 ${loading ? 'opacity-75' : ''}`}
          aria-live="polite" // Announce changes for screen readers
        >
          {loading ? (
            <span className="d-flex align-items-center justify-content-center">
              <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
              Generating...
            </span>
          ) : (
            "Generate"
          )}
        </button>

        {/* Output Display Section */}
        <div className="mt-4">
          <label htmlFor="output-display" className="form-label fs-5 fw-semibold text-dark">
            <span role="img" aria-label="document">📄</span> Output:
          </label>
          <div
            id="output-display"
            className="form-control bg-light border rounded-3 p-4"
            style={{ minHeight: "150px", whiteSpace: "pre-line", overflow: "auto" }}
            aria-live="assertive" // Announce content changes for screen readers
          >
            {output || "Your generated output will appear here."}
          </div>
        </div>
      </div>
    </>
  );
};

export default PromptAssistant;
