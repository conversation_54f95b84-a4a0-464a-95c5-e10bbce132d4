const router = require("express").Router();
const jwt = require("jsonwebtoken");
const multer = require("multer");
const {
  registration,
  organizationRoles,
  showOrganizationRoles,
  editOrganizationRoles,
  deleteOrganizationRole,
  organizationLoginUser,
  googleLoginorSignup,
  addNewOrganization,
  showOrganizationPermissions,
  sendLinkforlogin,
  forgotPassword,
  verifyRegisterOTP,
  resetPassword,
  sendotp,
  verifyOtp,
  updateUserProfile,
  getProfile,
  getCertificateData,
  getProfileComments,
  updatePasswordAPI,
  deleteUserAccount,
  softDeleteUserAccount,
  addClassroomApproval,
  checkClassroomApprovalStatus,
  organizationLogoutUser,
  updateProfilePic,
  changePassword    ,
  newRegistration   ,
  SendOTPForAccountActivationFromLogin
} = require("../controller/accounts/orgUser");
const {
  loginValidation,
  authenticateJWT,
  authorizePermissions,
  registerValidation,
} = require("../../middleware/Validator");
const upload = multer({ storage: multer.memoryStorage() });
const {
  createCourse,
  showAllCourses,
  createCourseModule,
  getModule,
  addVideoAndDetails,
  videoStream,
  getMyAllCourses,
  getCourseDetails,
  getRecentVideos,
  saveRecentVideo,
  getCourseCompletionPercentage,
  fetchCoursesByClassroom,
  addToMyCourse,
} = require("../controller/courses/courses");
const {
  getFaqs,
  raiseTicket,
  userAllRaiseTicket,
  raiseTicketV2,
  DeleteTicket
} = require("../controller/faq/faq");
const {
  getAllAssessmentStatus,
  getAllAssessmentData,
  getQuestionsByAssessmentId,
  submitAnswers,
  restartQuestion,
  getAllAssessmentDataByClassId,
  getCompleteAndPendingAssessments,
  submitSurveyAnswers,
  getSurveyQuestionsByAssessmentId,
  getCourseAssessment,
  RestartSurvey,
  getAssessmentResults,
  getAssessmentResultsDetailed,
  getVideoByID,
  getAllResourcesByClassroomId,
  markResourceAsSeen,
  submitAssessmentWithResult
} = require("../controller/assessments/assessment");
const {
  getNotifications,
  NewgetNotifications,
  
  markNotificationAsRead,
  markAllNotificationsAsRead,
} = require("../controller/notifications/notifications");
const {
  getTopCourses,
  getUserLogs,
  DashboardStatus,
  DashboardStatistic,
  getTaskLists,
  markTaskAsSeen,

  getAllTasksByUser,
  addTask,
  editTaskById,
  deleteTaskById,
  markTaskAsCompleted,
  getDashboardData
} = require("../controller/dashboard/dashboard");
const {
  getCourseClassroomData,
  getGroupMessages,
  sendMessage,
} = require("../controller/courses/classroom");

const {
  getClassroomResourcesForTrainees,
} = require("../controller/classroom/classroomResources");


const {
  getAssignments,
  submitAssignment,
  getCompleteAndPendingAssignments,
} = require("../controller/assignment/assignment");
const { getRankings } = require("../controller/courses/performance");
const {
  getNotes,
  addNote,
  getComments,
  addComment,

  
  addCourseReview,
  getCourseReviews,
  deleteReview,
  getModuleNotes,
  getModuleComments,
  deleteNotes,
  updateNote,
  addReplyComment,
  editReplyComment,
  getRepliesByCommentId,
  deleteComment,
  updateComment,
  deleteReplyComment,
  updateLike,
  getAttachments,

  editCourseReview
} = require("../controller/courses/notes");
const {
  updateNotificationSettings,
} = require("../controller/settings/notifications_settings");
const { getOrgInfomation } = require("../controller/settings/organizationInfo");
const {
  fetchUserRankings,
  getClassroomPerformanceGraph,
  fetchClassroomCourses,
  getClassCourseCompletionPercentage,
} = require("../controller/statistics/statistics");
const {
  getTraineeCertificates,
  generateCertificate, // Route handler function
  createCertificate,
} = require("../controller/certificates/certificate");
const {
  getGlobalAssessmentStatus,
  getGlobalAssessmentData,
} = require("../controller/assessments/globalAssessment");
const {
  processPayment,
  successPayment,
  cancelPayment,
  getPaymentHistory,
  getAllUserTransactions
} = require("../controller/payment/payment");
const {
  processPayUPayment,
  successPayUPayment,
  cancelPayUPayment
} = require("../controller/payment/Payment_with_payu");
const {
  getTraineeAssessments,
  getTraineeClassrooms,
  getTraineeClassroomDashboardDetails,
  submitTraineeAssessment,
  getQuestionsForTraineeAssessment,
} = require("../organisationController/classroom/userClassroomAssessment");
const {
  logUserJoinLiveClass,
  logUserLeaveLiveClass,
} = require("../organisationController/classroom/classroom");
const {
  getUserClassroomAssignments,
  submitClassroomAssignment,
} = require("../organisationController/classroom/classroomAssignments");

// router.post('/create_organization', create_organization.create_organization);
router.post(
  "/user_registration",
  upload.fields([{ name: "profile_pic" }, { name: "banner_img" }]),
  registerValidation,
  registration
);
router.post("/organizatin_roles", authenticateJWT, organizationRoles);
router.post("/show_permissions", authenticateJWT, showOrganizationPermissions);
router.get("/show_organization_roles", authenticateJWT, showOrganizationRoles);
router.put("/edit_organization_roles", authenticateJWT, editOrganizationRoles);
router.post(
  "/delete_organization_roles",
  authenticateJWT,
  deleteOrganizationRole
);























/**
 * @swagger
 * /org/user_login:
 *   post:
 *     summary: Login to the LMS system
 *     tags:
 *       - Authentication
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               organization_url:
 *                 type: string
 *                 description: Subdomain of the organization
 *                 example: "http://abid.localhost:3001"
 *               username:
 *                 type: string
 *                 description: Email or mobile number of the user
 *                 example: "<EMAIL>"
 *               password:
 *                 type: string
 *                 description: User's password
 *                 example: "Abid@7890"
 *             required:
 *               - organization_url
 *               - username
 *               - password
 *     responses:
 *       200:
 *         description: Login successful
 *       401:
 *         description: Authentication failed
 *       422:
 *         description: Validation error
 *       500:
 *         description: Internal server error
 */

router.post("/user_login", loginValidation(), organizationLoginUser);
// google login
router.post("/googleauth", googleLoginorSignup);
router.get("/user_logout", authenticateJWT, organizationLogoutUser);
router.post("/update_profile_pic",upload.single("profileImage"), authenticateJWT, updateProfilePic);
router.get("/user_dashboard_status", authenticateJWT, DashboardStatus);
router.get("/dashboard_performance", authenticateJWT, DashboardStatistic);
router.post("/get_task_lists", authenticateJWT, getTaskLists);
router.put("/mark_task_as_seen", authenticateJWT, markTaskAsSeen);

router.get("/get_all_tasks", authenticateJWT, getAllTasksByUser);
router.post("/add_task", authenticateJWT, addTask);
router.put("/edit_task", authenticateJWT, editTaskById);
router.delete("/delete_task", authenticateJWT, deleteTaskById);
router.put("/mark_task_as_completed", authenticateJWT, markTaskAsCompleted);
router.get("/get_dashboard_data", authenticateJWT, getDashboardData);



router.post("/active_account_otp_from_login_page", SendOTPForAccountActivationFromLogin);



router.post(
  "/create_organization",
  authenticateJWT,
  //authorizePermissions("can_create_organization"),
  addNewOrganization
);

router.post("/send_link", sendLinkforlogin);
// changePassword
router.post(
  "/change_password",
  authenticateJWT,
  changePassword
);
// new registration
router.post(
  "/new_registration",
  newRegistration
);

router.post("/forgot_password", forgotPassword);
router.post("/verify_register_otp", verifyRegisterOTP);
router.post("/reset_password", resetPassword);
router.post("/send_otp", sendotp);
router.post("/verify_otp", verifyOtp);
router.post(
  "/edit_profile",
  authenticateJWT,
  upload.single("profile_pic"),
  updateUserProfile
);
router.get("/get_profile", authenticateJWT, getProfile);
// router.get('/certificates',authenticateJWT, getCertificateData);
router.get("/get_profile_comments/:user_id", authenticateJWT, getProfileComments);
// update password from settings
router.put("/update_passwordAPI", authenticateJWT, updatePasswordAPI);


//Course Routers

router.post(
  "/create_course",
  authenticateJWT,
  //authorizePermissions("can_create_course"),
  upload.fields([{ name: "banner_image" }]),
  createCourse
);

router.post(
  "/get_all_course",
  authenticateJWT,
  //authorizePermissions("can_see_all_course"),
  showAllCourses
);

router.post(
  "/get_my_course",
  authenticateJWT,
  //authorizePermissions("can_see_all_course"),
  getMyAllCourses
);

router.post(
  "/get_course_details",
  authenticateJWT,
  //authorizePermissions("can_see_all_course"),
  getCourseDetails
);

router.post("/create_course_module", authenticateJWT, createCourseModule);
router.post("/get_module_list", authenticateJWT, getModule);
router.post(
  "/add_video_details",
  authenticateJWT,
  upload.fields([{ name: "video" }, { name: "attachment_file" }]),
  addVideoAndDetails
);
router.post("/get_all_modules_videos", authenticateJWT, addVideoAndDetails);
router.get(
  "/course_completion/:course_id/:video_id",
  authenticateJWT,
  getCourseCompletionPercentage
);
router.get("/add_to_mycourse", authenticateJWT, addToMyCourse);
// Notes
router.get("/get_notes_details", authenticateJWT, getNotes);
router.post("/add_note", authenticateJWT, addNote);
router.get("/get_module_notes", authenticateJWT, getModuleNotes);
router.delete("/delete_note/:noteId", authenticateJWT, deleteNotes);
router.post("/edit_note", authenticateJWT, updateNote);



// Comments
router.get("/get_comments_details", authenticateJWT, getComments);
router.post("/add_comment", authenticateJWT, addComment);
router.delete("/delete_comment/:commentId", authenticateJWT, deleteComment);
router.post("/update_comment", authenticateJWT, updateComment);
router.post("/update_like", authenticateJWT, updateLike);
// Reply comments
router.post("/add_reply_comment", authenticateJWT, addReplyComment);
router.post(
  "/get_replies_by_comment_id", 
  authenticateJWT,
  getRepliesByCommentId
);

router.post("/update_reply_comment", authenticateJWT, editReplyComment);

router.post(
  "/delete_reply_comment",
  authenticateJWT,
  deleteReplyComment
);

// Ratings
router.post("/add_course_review", authenticateJWT, addCourseReview);
router.post("/edit_course_review", authenticateJWT, editCourseReview);
router.get("/get_course_reviews", authenticateJWT, getCourseReviews);
router.delete("/delete_review/:review_id", authenticateJWT, deleteReview);

// Help and support
router.get("/get_faqs", authenticateJWT, getFaqs);
router.post(
  "/raise_ticket",
  authenticateJWT,
  upload.fields([{ name: "upload_file" }]),
  raiseTicket
);
router.post("/get_all_raise_ticket", authenticateJWT, userAllRaiseTicket);
router.post("/raise_ticket_v2", authenticateJWT, raiseTicketV2);
router.delete("/delete_ticket/:ticket_id", authenticateJWT, DeleteTicket);

//Assessments
router.post("/get_course_assessment", authenticateJWT, getAllAssessmentStatus);
router.post(
  "/submit_assessment_with_result",
  authenticateJWT,
  submitAssessmentWithResult
);
router.post(
  "/get_user_assessment/:class_id",
  authenticateJWT,
  getAllAssessmentData
);
router.post(
  "/get_class_assessment",
  authenticateJWT,
  getAllAssessmentDataByClassId
);
// router.post("/get_question_with_option",authenticateJWT, getQuestionsWithOptions)
router.get(
  "/get_assessment_count/:class_id",
  authenticateJWT,
  getCompleteAndPendingAssessments
);

//notifications

router.post("/get_notifications", authenticateJWT, getNotifications);
router.post("/new_get_notifications", authenticateJWT, NewgetNotifications);

router.post(
  "/mark_notification_as_read",
  authenticateJWT,
  markNotificationAsRead
);
router.post(
  "/mark_all_notifications_as_read",
  authenticateJWT,
  markAllNotificationsAsRead
);

router.get("/top-courses", authenticateJWT, getTopCourses);
router.get("/getUserLogs", authenticateJWT, getUserLogs);

router.post("/get_classroom_resources_for_trainees", authenticateJWT, getClassroomResourcesForTrainees);

// Classroom API
router.post("/course_data", authenticateJWT, getCourseClassroomData);
router.get(
  "/classroom_courses/:class_id",
  authenticateJWT,
  fetchCoursesByClassroom
);
// Recorded video
router.post("/recorded_video", authenticateJWT, getModule);
router.post("/get_questions", authenticateJWT, getQuestionsByAssessmentId);
router.post("/submit_answers", authenticateJWT, submitAnswers);
router.get(
  "/get_assessment_results/:assessment_id",
  authenticateJWT,
  getAssessmentResults
);
router.get(
  "/get_assessment_results_detailed/:assessment_id",
  authenticateJWT,
  getAssessmentResultsDetailed
);
router.get("/get_video_by_id", authenticateJWT, getVideoByID);
router.post("/get_all_resources", authenticateJWT, getAllResourcesByClassroomId);
router.post("/mark_resource_as_seen", authenticateJWT, markResourceAsSeen);
router.post("/restart_assessment", authenticateJWT, restartQuestion);
router.post(
  "/get_survey_questions",
  authenticateJWT,
  getSurveyQuestionsByAssessmentId
);

router.post(
  "/get_course_assessment_by_assessment_id",
  authenticateJWT,
  getCourseAssessment
);
router.post("/submit_survey_answers", authenticateJWT, submitSurveyAnswers);
router.get("/getmessges", authenticateJWT, getGroupMessages);
router.post("/send_messges", authenticateJWT, sendMessage);
router.post("/restart_assessment", authenticateJWT, restartQuestion);

// Assignment
router.get("/get_assignments", authenticateJWT, getAssignments);
// router.get("/get_rankings", authenticateJWT, getRankings);
// router.post(
//   "/submit_assignment",
//   authenticateJWT,
//   upload.fields([{ name: "upload_file" }]),
//   submitAssignment
// );
router.get(
  "/assignment_count/:class_id",
  authenticateJWT,
  getCompleteAndPendingAssignments
);

// Recent Videos
router.get("/get_recent_videos", authenticateJWT, getRecentVideos);
router.post("/save_recent_video", authenticateJWT, saveRecentVideo);

// Notifications Settings
router.put(
  "/update_notification_settings",
  authenticateJWT,
  updateNotificationSettings
);

// Delete User Account
router.post("/delete_account", authenticateJWT, deleteUserAccount);
// Soft Delete User Account
router.post("/soft_delete_account", authenticateJWT, softDeleteUserAccount);
router.post("/add_classroom_approval", authenticateJWT, addClassroomApproval);
router.get(
  "/check_classroom_approval",
  authenticateJWT,
  checkClassroomApprovalStatus
);

//Attachments
router.get("/attachments/:video_id", authenticateJWT, getAttachments);
router.get("/get_orginfo", authenticateJWT, getOrgInfomation);

//Rankings
router.get("/trainee_rankings", authenticateJWT, fetchUserRankings);
router.get(
  "/performance_graph/:class_id",
  authenticateJWT,
  getClassroomPerformanceGraph
);
router.get(
  "/classroom_courses/performance/:class_id",
  authenticateJWT,
  fetchClassroomCourses
);
router.get(
  "/classroom_course_percentage/:course_id",
  authenticateJWT,
  getClassCourseCompletionPercentage
);

//Certificates
router.get("/get_certificates", authenticateJWT, getTraineeCertificates);
router.post("/generate_certificate", authenticateJWT, generateCertificate);


router.post(
  "/get_global_assessments",
  authenticateJWT,
  getGlobalAssessmentStatus
);
router.post(
  "/get_global_assessment_data",
  authenticateJWT,
  getGlobalAssessmentData
);

// payment
router.post("/process_payment", authenticateJWT, processPayment);
router.get("/success_payment", authenticateJWT, successPayment);
router.get("/cancel_payment", authenticateJWT, cancelPayment);
router.get("/get_payment_history", authenticateJWT, getPaymentHistory);
router.get("/get_user_transactions", authenticateJWT, getAllUserTransactions);

// PayU payment routes
router.post("/process_payu_payment", authenticateJWT, processPayUPayment);
router.get("/success_payu_payment", authenticateJWT, successPayUPayment);
router.get("/cancel_payu_payment", authenticateJWT, cancelPayUPayment);

//Trainee Classroom Assessment apis
/**
 * @swagger
 * /org/classroom/trainee_assessments:
 *   post:
 *     tags:
 *       - Trainee Classroom Assessments
 *     summary: Get all assessments for a trainee in a classroom
 *     description: Retrieves all assessments for a specific trainee in a classroom with pagination and search functionality
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - class_id
 *               - user_id
 *             properties:
 *               class_id:
 *                 type: integer
 *                 description: ID of the classroom
 *               user_id:
 *                 type: integer
 *                 description: ID of the trainee
 *               page:
 *                 type: integer
 *                 description: Page number for pagination
 *                 default: 1
 *               limit:
 *                 type: integer
 *                 description: Number of records per page
 *                 default: 10
 *               search:
 *                 type: string
 *                 description: Search term to filter assessments by name
 *                 default: ""
 *     responses:
 *       200:
 *         description: Assessments retrieved successfully
 *       400:
 *         description: Bad request - Missing required fields
 *       403:
 *         description: User is not enrolled in the classroom
 *       404:
 *         description: User or classroom not found
 *       500:
 *         description: Server error
 */
router.post(
  "/classroom/trainee_assessments",
  authenticateJWT,
  getTraineeAssessments
);

/**
 * @swagger
 * /org/classroom/trainee/classrooms:
 *   post:
 *     summary: Get all classrooms for a trainee
 *     description: Fetches all classroom details that a user belongs to with pagination and search functionality
 *     tags:
 *       - Trainee Classroom Assessments
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - user_id
 *             properties:
 *               user_id:
 *                 type: integer
 *                 description: ID of the trainee
 *               page:
 *                 type: integer
 *                 default: 1
 *                 description: Page number for pagination
 *               limit:
 *                 type: integer
 *                 default: 10
 *                 description: Number of results per page
 *               search:
 *                 type: string
 *                 description: Search term to filter classrooms by name, description, or collaboration name
 *     responses:
 *       200:
 *         description: Classrooms fetched successfully
 *       400:
 *         description: Bad request - Missing required fields
 *       404:
 *         description: User not found
 *       500:
 *         description: Server error
 */
router.post(
  "/classroom/trainee/classrooms",
  authenticateJWT,
  getTraineeClassrooms
);

router.post(
  "/classroom/dashboard",
  authenticateJWT,
  getTraineeClassroomDashboardDetails
);

/**
 * @swagger
 * /org/classroom/assessment/submit:
 *   post:
 *     summary: Submit trainee assessment answers
 *     description: Handles the submission of answers for an assessment by a trainee
 *     tags:
 *       - Trainee Classroom Assessments
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - class_id
 *               - assessment_id
 *               - user_id
 *               - quiz_answers
 *             properties:
 *               class_id:
 *                 type: integer
 *                 description: ID of the classroom
 *               assessment_id:
 *                 type: integer
 *                 description: ID of the assessment
 *               user_id:
 *                 type: integer
 *                 description: ID of the user submitting the assessment
 *               quiz_answers:
 *                 type: array
 *                 description: Array of question answers
 *                 items:
 *                   type: object
 *                   required:
 *                     - question_id
 *                     - option
 *                   properties:
 *                     question_id:
 *                       type: integer
 *                       description: ID of the question being answered
 *                     option:
 *                       type: string
 *                       description: The selected answer option
 *     responses:
 *       200:
 *         description: Assessment submitted successfully
 *       400:
 *         description: Bad request - Missing required fields or user already submitted
 *       403:
 *         description: User is not enrolled in this classroom
 *       404:
 *         description: Assessment, classroom, or user not found
 *       500:
 *         description: Server error
 */
router.post(
  "/classroom/assessment/submit",
  authenticateJWT,
  submitTraineeAssessment
);

/**
 * @swagger
 * /org/classroom/assessment/questions:
 *   post:
 *     summary: Get questions for trainee assessment
 *     description: Fetches all details of an assessment along with questions and options
 *     tags:
 *       - Trainee Classroom Assessments
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - assessment_id
 *               - class_id
 *             properties:
 *               assessment_id:
 *                 type: integer
 *                 description: ID of the assessment
 *               class_id:
 *                 type: integer
 *                 description: ID of the classroom
 *     responses:
 *       200:
 *         description: Assessment questions fetched successfully
 *       400:
 *         description: Bad request - Missing required fields
 *       404:
 *         description: Assessment not found or does not belong to the specified classroom
 *       500:
 *         description: Server error
 */
router.post(
  "/classroom/assessment/questions",
  authenticateJWT,
  getQuestionsForTraineeAssessment
);

// Logging apis for live class , swagger documentation needs to be added.
router.post(
  "/classroom/live_class/join_live_class",
  authenticateJWT,
  logUserJoinLiveClass
);
router.post(
  "/classroom/live_class/leave_live_class",
  authenticateJWT,
  logUserLeaveLiveClass
);

//Trainee Classroom Assignment APIs

/**
 * @swagger
 * /org/classroom/get_assignments:
 *   post:
 *     summary: Get all assignments for a user in a specific class
 *     tags: [Classroom Assignments]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - class_id
 *             properties:
 *               class_id:
 *                 type: integer
 *                 description: ID of the classroom
 *               page:
 *                 type: integer
 *                 description: Page number for pagination
 *                 default: 1
 *               limit:
 *                 type: integer
 *                 description: Number of items per page
 *                 default: 10
 *               search:
 *                 type: string
 *                 description: Search term for filtering assignments
 *     responses:
 *       200:
 *         description: Assignments retrieved successfully
 *       400:
 *         description: Invalid request parameters
 *       404:
 *         description: Classroom not found
 *       500:
 *         description: Server error
 */
router.post(
  "/classroom/user_assignments",
  authenticateJWT,
  getUserClassroomAssignments
);

/**
 * @swagger
 * /org/classroom/submit_assignment:
 *   post:
 *     summary: Submit an assignment (for students)
 *     tags: [Classroom Assignments]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             required:
 *               - assignment_id
 *               - class_id
 *             properties:
 *               assignment_id:
 *                 type: integer
 *                 description: ID of the assignment
 *               class_id:
 *                 type: integer
 *                 description: ID of the classroom
 *               assignment_file:
 *                 type: string
 *                 format: binary
 *                 description: Assignment file to upload
 *     responses:
 *       201:
 *         description: Assignment submitted successfully
 *       200:
 *         description: Assignment resubmitted successfully
 *       400:
 *         description: Invalid request parameters
 *       403:
 *         description: Not enrolled in the class
 *       404:
 *         description: Assignment not found
 *       500:
 *         description: Server error
 */
router.post(
  "/classroom/submit_assignment",
  authenticateJWT,
  upload.single("upload_file"),
  submitClassroomAssignment
);

module.exports = router;
