import React, { useState, useEffect } from 'react';
import { Icon } from '@iconify/react';
import { useNavigate } from 'react-router-dom'; 
import moment from 'moment-timezone';
import { getAllAssessmentToClassroomInUser } from '../../../services/userService';
import { encodeData } from '../../../utils/encodeAndEncode';
import Loader from '../../../components/common/Loader';

function Assessments({ classroom_id }) {
  const userId = JSON.parse(localStorage.getItem('user'))?.id;
  const navigate = useNavigate();
  const [search, setSearch] = useState('');
  const [page, setPage] = useState(1);
  const [itemsPerPage] = useState(10);
  const [assessments, setAssessments] = useState([]);
  const [showModal, setShowModal] = useState(false);
  const [selectedAssessment, setSelectedAssessment] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const userTimeZone = moment.tz.guess(); // Get user's local timezone

  const formatDate = (utcDateString) => {
    if (!utcDateString) return '';
    
    // Convert UTC to user's local timezone
    const utcDate = moment.utc(utcDateString);
    const localDate = utcDate.tz(userTimeZone);
    
    return `${localDate.format('MMMM D, YYYY h:mm A')} (${moment.tz(userTimeZone).zoneAbbr()})`;
  };

  useEffect(() => {
    const fetchAssessments = async () => {
      try {
        setIsLoading(true);
        const payload = {
          class_id: classroom_id,
          user_id: userId,
          page: page,
          limit: itemsPerPage,
          search: search
        };

        const response = await getAllAssessmentToClassroomInUser(payload);
        // console.log("Assessments Response:------------------------------------------", response);
        if (response.success) {
          setAssessments(response.assessments);
        }
      } catch (error) {
        console.error("Error fetching assessments:", error);
      } finally {
        setIsLoading(false);
      }
    };

    if (classroom_id && userId) {
      fetchAssessments();
    }
  }, [classroom_id, userId, page, itemsPerPage, search]);

  const handleSearch = (e) => {
    setSearch(e.target.value);
    setPage(1);
  };

  const isAssessmentEnabled = (assessment) => {
    if (assessment.is_time_based === 0) {
      return assessment.is_active === 1;
    } else {
      const currentTime = moment();
      const activateTime = moment.utc(assessment.activate_time).tz(userTimeZone);
      const deactivateTime = moment.utc(assessment.deactivate_time).tz(userTimeZone);
      return assessment.is_active === 1 && currentTime.isBetween(activateTime, deactivateTime, null, '[]');
    }
  };

  const handleStartAssessment = (assessment) => {
    setSelectedAssessment(assessment);
    setShowModal(true);
  };

  const handleStartQuiz = () => {
    const encodedAssessmentId = encodeData(selectedAssessment.assessment_id);
    const encodedClassroomId = encodeData(classroom_id);
    navigate(`/user/assessmentQuiz/${encodeURIComponent(encodedAssessmentId)}?classroomid=${encodeURIComponent(encodedClassroomId)}`);
    setShowModal(false);
  };

  const handleCloseModal = () => {
    setShowModal(false);
    setSelectedAssessment(null);
  };
    
  return (
    <div className="assessment">
      <div className="row mb-4 mt-2">
        <div className="col-12 col-md-4 col-lg-4">
          <div className="seach-control">
            <input 
              type="text" 
              className="form-control search-input" 
              placeholder="Search assessments..."
              aria-label="Search assessments"
              value={search}
              onChange={handleSearch}
            />
          </div>
        </div>
      
      </div>

      {isLoading ? (
        <div className="text-center py-5">
          <Loader />
        </div>
      ) : (
        <div className="row">
          {assessments.length === 0 ? (
            <div className="col-12">
              <div className="text-center py-5">
                <Icon icon="mdi:folder-open" className="text-muted mb-3" width="64" height="64" />
                <h5 className="text-muted">No Assessment Available</h5>
                <p className="text-muted">Assessment will appear here when they are added by the instructor.</p>
              </div>
            </div>
          ) : (
            assessments.map((assessment) => (
              <div key={assessment.assessment_id} className="col-md-6 col-lg-4 mb-4">
                <div className="card h-100">
                  <div className="card-body">
                    {/* Header Info */}
                    <div className="d-flex justify-content-between align-items-start mb-2">
                      <div>
                        <div className="mb-2">
                          <small className={`${assessment.is_time_based ? 'text-info' : 'text-primary'} d-flex align-items-center`}>
                            <Icon 
                              icon={assessment.is_time_based ? "mdi:clock-outline" : "mdi:infinity"} 
                              className="me-1" 
                              width="14" 
                              height="14" 
                            />
                            {assessment.is_time_based ? 'Time-bound Assessment' : 'No time-bound Assessment'}
                          </small>
                        </div>
                        <h5 className="mb-1" style={{
                          display: '-webkit-box',
                          WebkitLineClamp: '2',
                          WebkitBoxOrient: 'vertical',
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          minHeight: '48px', // Approximately 2 lines of text
                          marginBottom: '8px'
                        }}>
                          {assessment.assessment_name}
                        </h5>
                        <div className="d-flex align-items-center text-muted">
                          <Icon icon="mdi:clock-outline" className="me-2" />
                          Duration: {assessment.duration} mins
                        </div>
                      </div>
                      <span className={`badge ${assessment.is_active ? 'border border-success text-success bg-success-subtle' : 'border border-danger text-danger bg-danger-subtle'}`} 
                        style={{
                          fontWeight: '500',
                          fontSize: '12px',
                          padding: '6px 12px',
                          display: 'flex',
                          alignItems: 'center',
                          gap: '4px'
                        }}
                      >
                        <Icon 
                          icon={assessment.is_active ? "mdi:check-circle" : "mdi:close-circle"} 
                          width="14" 
                          height="14" 
                        />
                        {assessment.is_active ? 'Active' : 'Inactive'}
                      </span>
                    </div>

                    <hr />
                    {/* Availability */}
                    <div className={`d-flex align-items-start mb-3 ${isAssessmentEnabled(assessment) ? 'text-success' : 'text-danger'}`} style={{ minHeight: '40px' }}>
                      <span className="me-2" style={{ fontSize: '12px' }}>●</span>
                      <div>
                        <small className="fw-medium d-block">
                          {assessment.is_time_based === 0 ? 'Always available (Not time-bound)' : 'Available:'}
                        </small>
                        <small className="text-muted d-block" style={{ fontSize: '11px', visibility: assessment.is_time_based ? 'visible' : 'hidden' }}>
                        {assessment.is_time_based ? 
  `${new Date(assessment.activate_time).toLocaleString('en-US', {
    year: 'numeric',
    month: 'short',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    hour12: true
  })} - ${new Date(assessment.deactivate_time).toLocaleString('en-US', {
    year: 'numeric',
    month: 'short',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    hour12: true
  })}` :
  'placeholder text for height'
}

                        </small>
                      </div> 
                    </div>
                    



                    {/* Stats */}
                    <div className="row text-center mb-3">
                      <div className="col-6 col-md-3">
                        <div className="fw-bold text-primary">{assessment.highest_percentage}%</div>
                        <small className="text-muted">Highest %</small>
                      </div>
                      <div className="col-6 col-md-3">
                        <div className="fw-bold text-primary">{assessment.no_of_times_attended}</div>
                        <small className="text-muted">Attempts</small>
                      </div>
                      <div className="col-6 col-md-3">
                        <div className="fw-bold text-primary">{assessment.total_questions}</div>
                        <small className="text-muted">Questions</small>
                      </div>
                      <div className="col-6 col-md-3">
                        <div className="fw-bold text-primary">{assessment.pass_percentage}%</div>
                        <small className="text-muted">Pass %</small>
                      </div>
                    </div>

                    {/* Action */}
                    <div className="d-grid">
                      <button 
                        className="btn btn-primary d-flex align-items-center justify-content-center gap-2"
                        onClick={() => handleStartAssessment(assessment)}
                        disabled={!isAssessmentEnabled(assessment)}
                      >
                        <Icon icon="mdi:play" width="18" />
                        <span className="fw-medium">Start Assessment</span>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      )}

      {/* Assessment Info Modal */}
      {showModal && selectedAssessment && (
        <div className="modal show d-block" tabIndex="-1" style={{ backgroundColor: 'rgba(0,0,0,0.5)' }}>
          <div className="modal-dialog modal-dialog-centered modal-lg">
            <div className="modal-content">
              <div className="modal-header">
                <h5 className="modal-title d-flex align-items-center">
                  <Icon icon="mdi:clipboard-text" className="me-2" width="24" height="24" />
                  Assessment Information
                </h5>
                <button 
                  type="button" 
                  className="btn-close" 
                  onClick={handleCloseModal}
                  aria-label="Close"
                ></button>
              </div>
              <div className="modal-body">
                <div className="row">
                  <div className="col-12">
                    <h6 className="text-primary mb-3">{selectedAssessment.assessment_name}</h6>
                    
                    <div className="row mb-4">
                      <div className="col-md-6">
                        <div className="d-flex align-items-center mb-2">
                          <Icon icon="mdi:clock-outline" className="text-info me-2" width="18" />
                          <strong>Duration:</strong>
                          <span className="ms-2">
                            {selectedAssessment.duration > 0 ? `${selectedAssessment.duration} minutes` : 'Untimed'}
                          </span>
                        </div>
                        <div className="d-flex align-items-center mb-2">
                          <Icon icon="mdi:help-circle" className="text-info me-2" width="18" />
                          <strong>Total Questions:</strong>
                          <span className="ms-2">{selectedAssessment.total_questions}</span>
                        </div>
                        <div className="d-flex align-items-center mb-2">
                          <Icon icon="mdi:target" className="text-info me-2" width="18" />
                          <strong>Pass Percentage:</strong>
                          <span className="ms-2">{selectedAssessment.pass_percentage}%</span>
                        </div>
                      </div>
                      <div className="col-md-6">
                        <div className="d-flex align-items-center mb-2">
                          <Icon icon="mdi:chart-line" className="text-success me-2" width="18" />
                          <strong>Your Best Score:</strong>
                          <span className="ms-2">{selectedAssessment.highest_percentage}%</span>
                        </div>
                        <div className="d-flex align-items-center mb-2">
                          <Icon icon="mdi:repeat" className="text-warning me-2" width="18" />
                          <strong>Attempts Made:</strong>
                          <span className="ms-2">{selectedAssessment.no_of_times_attended}</span>
                        </div>
                        <div className="d-flex align-items-center mb-2">
                          <Icon 
                            icon={selectedAssessment.is_active ? "mdi:check-circle" : "mdi:close-circle"} 
                            className={`${selectedAssessment.is_active ? 'text-success' : 'text-danger'} me-2`} 
                            width="18" 
                          />
                          <strong>Status:</strong>
                          <span className={`ms-2 ${selectedAssessment.is_active ? 'text-success' : 'text-danger'}`}>
                            {selectedAssessment.is_active ? 'Active' : 'Inactive'}
                          </span>
                        </div>
                      </div>
                    </div>

                    {selectedAssessment.is_time_based === 1 && (
                      <div className="alert alert-info mb-3">
                        <div className="d-flex align-items-start">
                          <Icon icon="mdi:information" className="me-2 mt-1" width="18" />
                          <div>
                            <strong>Time-bound Assessment</strong>
                            <div className="mt-1">
                              <small>
                                Available from: {new Date(selectedAssessment.activate_time).toLocaleString()}
                                <br />
                                Available until: {new Date(selectedAssessment.deactivate_time).toLocaleString()}
                              </small>
                            </div>
                          </div>
                        </div>
                      </div>
                    )}

                    {selectedAssessment.is_time_based === 0 && (
                      <div className="alert alert-success mb-3">
                        <div className="d-flex align-items-center">
                          <Icon icon="mdi:infinity" className="me-2" width="18" />
                          <strong>No Time Restrictions - Available anytime</strong>
                        </div>
                      </div>
                    )}

                    <div className="alert alert-warning">
                      <div className="d-flex align-items-start">
                        <Icon icon="mdi:alert" className="me-2 mt-1" width="18" />
                                                 <div>
                           <strong>Important Instructions:</strong>
                           <ul className="mt-2 mb-0">
                             <li>Make sure you have a stable internet connection</li>
                             <li>Once started, the assessment must be completed in one sitting</li>
                             {selectedAssessment.duration > 0 && (
                               <li>You have {selectedAssessment.duration} minutes to complete the assessment</li>
                             )}
                             <li>You can navigate between questions during the assessment</li>
                             {selectedAssessment.duration > 0 ? (
                               <li>In time-bound assessments, answers will be auto-submitted when time expires</li>
                             ) : (
                               <li>Your answers are saved automatically when you select them</li>
                             )}
                           </ul>
                         </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div className="modal-footer">
                <button 
                  type="button" 
                  className="btn btn-secondary" 
                  onClick={handleCloseModal}
                >
                  <Icon icon="mdi:close" className="me-1" width="16" />
                  Cancel
                </button>
                <button 
                  type="button" 
                  className="btn btn-primary" 
                  onClick={handleStartQuiz}
                  disabled={!isAssessmentEnabled(selectedAssessment)}
                >
                  <Icon icon="mdi:play" className="me-1" width="16" />
                  Start Quiz
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default Assessments;
