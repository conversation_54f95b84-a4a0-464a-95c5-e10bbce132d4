require("dotenv").config(); // Ensure this is at the top
const express = require("express");
const path = require("path");
// const https = require('https');
const http = require("http");
const fs = require("fs");
const app = express();
const bodyParser = require("body-parser");
const cors = require("cors");
const socketIo = require("socket.io");
const { mysqlServerConnection } = require("../db/db");
const { startHealthCheck } = require("../db/healthCheck");
const jwt = require("jsonwebtoken");
const { upload } = require("../../src/tools/tools");
const errorHandler = require("../middleware/error");
const { exec } = require("child_process");
const AWS = require("@aws-sdk/client-s3");
const cron = require("cron");
const os = require("os");
const mysql = require("mysql2");
const { Server } = require("socket.io");
const { swaggerSpec, swaggerUi } = require("../swagger");

// Middleware

// mysqlServerConnection.query(`CREATE DATABASE IF NOT EXISTS ${orgDBName}`, (error, results) => {
//     if (error) {
//         console.error("Error creating database:", error);
//         return;
//     }
//     console.log("Database created successfully:", results);
// });

// const [origin]=await mysqlServerConnection.query(`SELECT auth_sub_domain form auth.organization`);
// console.log(origin)

// Abhi Changes the cores for the subdomain access
// Old Cors Start
// const origins = ['http://**************:3000', 'http://localhost:3000', 'http://localhost:3001', 'http://localhost:3002','http://localhost:3003', 'https://*************:8443', "http://app.lms.smartguru.in", "https://app.lms.smartguru.in", "http://************:8003", "https://lms.creatorfoundation.in", "https://lms.nxgenvarsity.com"]
// app.use("*", cors({
//     origin: origins, // Allow requests from these origins
//     methods: 'GET,HEAD,PUT,PATCH,POST,DELETE', // Allow these HTTP methods
//     allowedHeaders: ['Content-Type', 'Authorization', 'auth_token'], // Allow these headers
// }));

// Old Cors End

// New Cors Start

// **Dynamically Generate Allowed Origins**
const getAllowedOrigins = (req) => {
  let allowedOrigins = [
    "http://**************:3000",
    "http://localhost:3000",
    "http://localhost:3001",
    "http://localhost:3002",
    "http://localhost:3003",
    "http://localhost:5001",
    "https://*************:8443",
    "http://app.lms.smartguru.in",
    "https://app.lms.smartguru.in",
    "http://************:8003",
    "https://lms.creatorfoundation.in",
    "https://lms.nxgenvarsity.com",
    "https://lms.tpi.sg",
    "https://7c2d-27-4-63-147.ngrok-free.app",
    "https://7c2d-27-4-63-147.ngrok-free.app:3001",
    "https://api.nxgenvarsity.com"
  ]; // Always allow the main domain
  const origin = req.header("Origin");

  // Debug logging
  // console.log('🔍 CORS Debug - Request Origin:', origin);
  // console.log('🔍 CORS Debug - Allowed Origins:', allowedOrigins);
  // console.log('🔍 CORS Debug - Origin Allowed:', allowedOrigins.includes(origin));

  if (origin) {
    // Match subdomains like abhi.localhost:3000
    const regex = /^http:\/\/([a-zA-Z0-9-]+)\.localhost:3001$/;
    if (regex.test(origin)) {
      allowedOrigins.push(origin); // Allow the subdomain dynamically
    }
  }
  return allowedOrigins;
};

// **Express CORS Middleware**
const corsOptionsDelegate = function (req, callback) {
  // console.log('🚀 CORS Middleware Called!');
  // console.log('🚀 Request URL:', req.url);
  // console.log('🚀 Request Method:', req.method);

  const allowedOrigins = getAllowedOrigins(req);
  const requestOrigin = req.header("Origin");

  // console.log('🔍 CORS Request from origin:', requestOrigin);
  // console.log('🔍 CORS Allowed origins:', allowedOrigins);

  const origin = allowedOrigins.includes(requestOrigin)
    ? requestOrigin
    : false;

  const corsOptions = {
    origin: origin,
    methods: "GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS",
    allowedHeaders: ["Content-Type", "Authorization", "auth_token", "X-Requested-With"],
    credentials: true,
    optionsSuccessStatus: 200 // Some legacy browsers (IE11, various SmartTVs) choke on 204
  };

  // console.log('🔍 CORS decision for origin:', requestOrigin, '-> allowed:', !!origin);
  // console.log('🔍 CORS options:', corsOptions);
  callback(null, corsOptions);
};

app.use(cors(corsOptionsDelegate));

// Handle preflight requests explicitly
app.options('*', cors(corsOptionsDelegate));

// New Cors End

const port = process.env.PORT || 4000;
const db = require("../db/utils/cretaeMainDb");
const superAdminRoute = require("../superAdmin/routes/route");
const organizationRoute = require("../Organization/routes/organizationroutes");
const { authenticateJWT } = require("../middleware/Validator");
const mainOrganisationRoute = require("../Organization/routes/mainOrgRoutes");
const { url } = require("inspector");

// Database initialization
// db.createMainDatabase();

// Start database health check
// startHealthCheck();

// Handle uncaught exceptions and unhandled rejections
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
  // Don't exit the process, just log the error
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  // Don't exit the process, just log the error
});

// Middleware
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Static file serving
app.use("/uploads", express.static(path.join(__dirname, "uploads")));

// Swagger
app.use("/api-docs", swaggerUi.serve, swaggerUi.setup(swaggerSpec));

// Routes
app.use("/superadmin", superAdminRoute);
app.use("/org", organizationRoute);
app.use("/mainOrg", mainOrganisationRoute);
app.get("/", (req, res) => {
  res.send("Express is running...");
});

// Test CORS route
app.get("/test-cors", (req, res) => {
  console.log('🧪 Test CORS route hit');
  console.log('🧪 Origin:', req.header("Origin"));
  res.json({
    success: true,
    message: "CORS is working!",
    origin: req.header("Origin"),
    timestamp: new Date().toISOString()
  });
});

app.use(errorHandler);

app.get('/zoom-webhook', (req, res) => {
  console.log("Zoom Webhook Received");
  // const { event, payload } = req.body;
  
  // if (event === 'meeting.ended') {
  //   const meetingId = payload.object.id;
  //   // Call your endLiveClass API
  //   endLiveClass({ live_class_id: meetingId });
  // }
  
  res.status(200).send();
});


app.post('/zoom-webhook-post', (req, res) => {
  console.log("Zoom Webhook Received");
  // const { event, payload } = req.body;
  
  // if (event === 'meeting.ended') {
  //   const meetingId = payload.object.id;
  //   // Call your endLiveClass API
  //   endLiveClass({ live_class_id: meetingId });
  // }
  
  res.status(200).send();
});

// backup_db.js:

if (process.env.IS_PROD === true) {
  const s3 = new AWS.S3({
    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
    region: process.env.AWS_REGION,
  });

  // Function to get the IP address of the server
  const getIPAddress = () => {
    const networkInterfaces = os.networkInterfaces();
    for (const interfaceName in networkInterfaces) {
      const interfaces = networkInterfaces[interfaceName];
      for (const iface of interfaces) {
        if (iface.family === "IPv4" && !iface.internal) {
          if (iface.address !== "************") {
            return "local_server";
          }
          return iface.address;
        }
      }
    }
    return "localhost"; // Fallback if no IP address is found
  };

  const backupDatabase = () => {
    const timestamp = new Date().toISOString().replace(/[:.]/g, "-");
    const ipAddress = getIPAddress(); // Automatically fetch the IP address
    const backupFile = `${ipAddress}_lms_org_${timestamp}.sql`;

    console.log("Starting database backup process...");

    // Run the mysqldump command to create a complete backup including schema, data, triggers, and routines
    let dumpCommand;

    dumpCommand = `mysqldump -h ${process.env.PROD_DB_IP} -u ${process.env.PROD_DB_USER} -p${process.env.PROD_DB_PASSWORD} --databases ${process.env.DATABASE} --routines --triggers --single-transaction --skip-lock-tables --add-drop-table --complete-insert > ${backupFile}`;

    exec(dumpCommand, (err) => {
      if (err) {
        console.error("Error creating database backup:", err);
        return;
      }
      console.log(`Database backup created: ${backupFile}`);

      // Read the backup file and upload it to S3
      fs.readFile(backupFile, (err, data) => {
        if (err) {
          console.error("Error reading backup file:", err);
          return;
        }

        // Upload the backup to S3
        const params = {
          Bucket: process.env.S3_BUCKET_NAME,
          Key: `sql/${backupFile}`, // Specify folder path here
          Body: data,
        };

        s3.upload(params, (err, data) => {
          if (err) {
            console.error("Error uploading to S3:", err);
          } else {
            console.log(`Backup successfully uploaded to S3: ${data.Location}`);
          }
          // Remove the local backup file after uploading
          fs.unlinkSync(backupFile);
        });
      });
    });
  };

  // Schedule the backup to run at 9 AM and 9 PM every day
  const backupJob = new cron.CronJob("0 9,21 * * *", backupDatabase);
  backupJob.start();
  console.log("Backup job scheduled to run at 9 AM and 9 PM every day.");

  // Trigger an immediate backup now
  backupDatabase();
  console.log("Immediate backup triggered.");

  // conn.connect((err) => {
  //     if (err) throw err;
  //     console.log('Mysql Connected...');
  // });
}

// const privateKey = fs.readFileSync(path.join(__dirname, 'cert/selfsigned.key'), 'utf8');
// const certificate = fs.readFileSync(path.join(__dirname, 'cert/selfsigned.crt'), 'utf8');

// const credentials = { key: privateKey, cert: certificate };

const httpsServer = http.createServer(app);
// Old Socket change reason is Cors
// const io = new Server(httpsServer, {
//     cors: {
//         origin: origins,
//         methods: ['GET', 'POST', 'PUT', 'PATCH'],
//         credentials: true // This is important
//     }
// });
// Old Code End

// New Code Start
// **Socket.io CORS Handling**
const io = new Server(httpsServer, {
  cors: {
    origin: (origin, callback) => {
      const allowedOrigins = getAllowedOrigins({ header: () => origin });
      console.log('Socket.io CORS check for origin:', origin);
      console.log('Socket.io allowed origins:', allowedOrigins);

      if (allowedOrigins.includes(origin) || !origin) { // Allow requests with no origin (like mobile apps)
        callback(null, origin);
      } else {
        console.log('Socket.io CORS rejected origin:', origin);
        callback(new Error("Not allowed by CORS"));
      }
    },
    methods: ["GET", "POST", "PUT", "PATCH"],
    credentials: true, // This is important for cookies and authentication
  },
  // Increase buffer size for file uploads (25MB)
  maxHttpBufferSize: 25 * 1024 * 1024, // 25MB
  // Increase ping timeout for large uploads
  pingTimeout: 120000, // 2 minutes
  pingInterval: 25000, // 25 seconds
});
// New Code End

const onlineUsers = new Map();
global._io = io;

io.on("connection", (socket) => {
  
  socket.on("joinRoom", (userId) => {
    if (!userId) return;

    onlineUsers.set(userId.toString(), socket.id);
    console.log(`User ${userId} joined and added to online users map`);
    // Leave previous rooms (if needed)
    Object.keys(socket.rooms).forEach((room) => {
      if (room !== socket.id) {
        socket.leave(room);
      }
    });

    // Join the correct room
    socket.join(userId.toString());
    // console.log(`✅ User ${userId} joined room ${userId}`);
  });

  socket.on("joinChatRoom", async (groupId) => {
    if (!groupId) {
      console.error("⛔ Invalid groupId received");
      return;
    }

    console.log(`📢 Request received to join room: ${groupId} by ${socket.id}`);

    socket.join(groupId); // ✅ Ensure joining happens

    setTimeout(async () => {
      const socketsInRoom = await io.in(groupId).allSockets();
      console.log(`👥 Users in room ${groupId}:`, [...socketsInRoom]);

      if (socketsInRoom.size === 0) {
        console.warn(
          `⚠️ Room ${groupId} is still empty. Possible issue with socket connections.`
        );
      }
    }, 1000);
  });

  // Get messages for a room
socket.on("getMessages", async (groupId, page = 1, messagesPerPage = 20) => {
  try {
    const token = socket.handshake.query.token;

    console.log(
      "📩 Get chat history request:",
      token,
      groupId,
      page,
      messagesPerPage
    );

    if (!token) {
      console.error("⛔ Token not provided");
      return socket.emit("error", "Token is required.");
    }

    const { userId, db_name } = jwt.verify(token, process.env.JWT_ACCESS_KEY);

    // ✅ Check group membership
    const [userGroupStatus] = await mysqlServerConnection.query(
      `SELECT is_blocked, hide_comments 
       FROM ${db_name}.group_members 
       WHERE group_id = ? AND user_id = ?`,
      [groupId, userId]
    );

    if (userGroupStatus.length === 0) {
      return socket.emit("error", "You are not a member of this group.");
    }

    const { is_blocked, hide_comment } = userGroupStatus[0];

    if (is_blocked || hide_comment) {
      console.warn(`⛔ User ${userId} is restricted in group ${groupId}`);
      socket.emit("setting", { is_blocked, hide_comment });
    }

    // ✅ Total messages for pagination
    const [totalCountResult] = await mysqlServerConnection.query(
      `SELECT COUNT(*) as total 
       FROM ${db_name}.group_messages 
       WHERE group_id = ?`,
      [groupId]
    );

    const totalMessages = totalCountResult[0].total;
    const totalPages = Math.ceil(totalMessages / messagesPerPage);

    // ✅ Fetch latest messages first
    const [messages] = await mysqlServerConnection.query(
      `SELECT
          gm.id,
          gm.user_id,
          gm.message,
          gm.message_type,
          gm.attachment,
          gm.media_url,
          gm.media_metadata,
          gm.emoji_reactions,
          gm.reply_to_message_id,
          gm.likes,
          gm.dislikes,
          DATE_FORMAT(gm.createdAt, '%Y-%m-%dT%H:%i:%sZ') AS createdAt,
          u.name,
          u.email,
          u.profile_pic_url,
          r.name as role,
          gmb.is_blocked,
          gmb.hide_comments
       FROM ${db_name}.group_messages gm
       LEFT JOIN ${db_name}.users u ON gm.user_id = u.id
       LEFT JOIN ${db_name}.user_roles ur ON u.id = ur.user_id
       LEFT JOIN ${db_name}.roles r ON ur.role_id = r.id
       LEFT JOIN ${db_name}.group_members gmb
             ON gm.group_id = gmb.group_id
            AND gmb.user_id = gm.user_id
       WHERE gm.group_id = ?
       ORDER BY gm.createdAt DESC
       LIMIT ? OFFSET ?`,
      [groupId, messagesPerPage, (page - 1) * messagesPerPage]
    );

    console.log("🔍 Raw messages from DB:", messages.length > 0 ? {
      id: messages[0].id,
      createdAt: messages[0].createdAt,
      createdAtType: typeof messages[0].createdAt,
      message: messages[0].message
    } : "No messages");

    // ✅ Convert Date objects to MySQL datetime format (YYYY-MM-DD HH:mm:ss)
    const formattedMessages = messages.map(message => ({
      ...message,
      createdAt: message.createdAt instanceof Date
        ? message.createdAt.toISOString().slice(0, 19).replace('T', ' ')
        : message.createdAt
    }));

    console.log("📤 Formatted messages to send:", formattedMessages.length > 0 ? {
      id: formattedMessages[0].id,
      createdAt: formattedMessages[0].createdAt,
      createdAtType: typeof formattedMessages[0].createdAt,
      message: formattedMessages[0].message
    } : "No messages");

    // ✅ Reverse for UI display: oldest at top, newest at bottom
    formattedMessages.reverse();

    // ✅ Emit response
    socket.emit("chatHistory", {
      messages: formattedMessages,
      pagination: {
        currentPage: page,
        totalPages,
        totalMessages,
        messagesPerPage,
      },
    });
  } catch (error) {
    console.error("⛔ Error fetching messages:", error);
    socket.emit("error", "Failed to load chat history.");
  }
});


  socket.on("getMessagesMetaData", async (data) => {
    try {
      const { groupId, messagesPerPage = 20 } = data;
      console.log("GET MESSAGE META DATA", data);
      const token = socket.handshake.query.token; // Extract the token from handshake query

      if (!token) {
        console.error("⛔ Token not provided");
        return socket.emit("error", "Token is required.");
      }

      const { userId, db_name } = jwt.verify(token, process.env.JWT_ACCESS_KEY);

      // Get total count of messages for pagination
      const [totalCountResult] = await mysqlServerConnection.query(
        `SELECT COUNT(*) as total FROM ${db_name}.group_messages WHERE group_id = ?`,
        [groupId]
      );

      const totalMessages = totalCountResult[0].total;
      const totalPages = Math.ceil(totalMessages / messagesPerPage);

      // Emit with the correct event name that matches the frontend
      socket.emit("messageMetadata", {
        totalPages,
        totalMessages,
        messagesPerPage,
      });

      console.log(
        `Sent message metadata for group ${groupId}: ${totalMessages} messages, ${totalPages} pages`
      );
    } catch (error) {
      console.error("⛔ Error fetching messages metadata:", error);
      socket.emit("error", "Failed to load messages metadata.");
    }
  });

  socket.on("sendMessage", async (data, callback) => {
    const { groupId, message, token } = data;
    console.log("SEND MESSAGE DATA", data);
    if (!token) {
      console.error("⛔ Token not provided");
      return;
    }

    try {
      const { userId, db_name } = jwt.verify(token, process.env.JWT_ACCESS_KEY);

      console.log(
        `✅ Received sendMessage request for group ${groupId} from user ${userId}`
      );
      console.log("📥 Message Data:", message);

      // Create UTC timestamp for consistent timezone handling
      const utcTimestamp = new Date().toISOString().slice(0, 19).replace('T', ' ');

  const [result] = await mysqlServerConnection.query(
    `INSERT INTO ${db_name}.group_messages (group_id, user_id, message, message_type, attachment, media_url, media_metadata, reply_to_message_id, createdAt, likes, dislikes, emoji_reactions) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
    [
      groupId,
      userId,
      message.message,
      message.message_type || "text",
      message.attachment || null,
      message.media_url || null,
      message.media_metadata
        ? JSON.stringify(message.media_metadata)
        : null,
      message.reply_to_message_id || null,
      utcTimestamp,
      "[]",
      "[]",
      "{}",
    ]
  );

      const messageId = result.insertId;

      // Fetch only the newly created message with user details
      const [newMessageDetails] = await mysqlServerConnection.query(
        `SELECT
                     gm.id,
                     gm.user_id,
                     gm.message,
                     gm.message_type,
                     gm.attachment,
                     gm.media_url,
                     gm.media_metadata,
                     gm.emoji_reactions,
                     gm.reply_to_message_id,
                     gm.likes,
                     gm.dislikes,
                     gm.createdAt,
                     u.name,
                     u.email,
                     u.profile_pic_url,
                     r.name as role,
                     gmb.is_blocked,
                     gmb.hide_comments
                  FROM ${db_name}.group_messages gm
                  LEFT JOIN ${db_name}.users u ON gm.user_id = u.id
                  LEFT JOIN ${db_name}.user_roles ur ON u.id = ur.user_id
                  LEFT JOIN ${db_name}.roles r ON ur.role_id = r.id
                  LEFT JOIN ${db_name}.group_members gmb
                        ON gm.group_id = gmb.group_id
                       AND gmb.user_id = gm.user_id
                  WHERE gm.id = ?`,
        [messageId]
      );

      if (newMessageDetails.length > 0) {
        // Convert Date object to MySQL datetime format (YYYY-MM-DD HH:mm:ss)
        const messageToEmit = {
          ...newMessageDetails[0],
          createdAt: newMessageDetails[0].createdAt instanceof Date
            ? newMessageDetails[0].createdAt.toISOString().slice(0, 19).replace('T', ' ')
            : newMessageDetails[0].createdAt
        };

        // Emit only the new message to all users in the room
        io.to(groupId).emit("newMessage", messageToEmit);

        // Update total message count and emit metadata update
        //  const [totalCountResult] = await mysqlServerConnection.query(
        //      `SELECT COUNT(*) as total FROM ${db_name}.group_messages WHERE group_id = ?`,
        //      [groupId]
        //  );

        //  const totalMessages = totalCountResult[0].total;
        //  const totalPages = Math.ceil(totalMessages / 20); // Using default messagesPerPage

        //  // Emit updated metadata to all users in the room
        //  io.to(groupId).emit('messageMetadata', {
        //      totalPages,
        //      totalMessages,
        //      messagesPerPage: 20
        //  });
      }

      // Also send the new message ID back to the sender via callback
      if (callback) callback({ messageId });
    } catch (error) {
      console.error("⛔ Error in sendMessage:", error);
    }
  });



  // Edit a message
  socket.on("editMessage", async (data) => {
    const { messageId, newMessage } = data;
    const token = data.token;
    if (!token) {
      console.error("Token not provided");
      return;
    }

    try {
      const { userId, db_name } = jwt.verify(token, process.env.JWT_ACCESS_KEY);
      await mysqlServerConnection.query(
        `UPDATE ${db_name}.group_messages SET message = ? WHERE id = ?`,
        [newMessage, messageId]
      );

      // Fetch the updated message with all details
      const [updatedMessageDetails] = await mysqlServerConnection.query(
        `SELECT
                    gm.id,
                    gm.user_id,
                    gm.message,
                    gm.message_type,
                    gm.attachment,
                    gm.media_url,
                    gm.media_metadata,
                    gm.emoji_reactions,
                    gm.reply_to_message_id,
                    gm.likes,
                    gm.dislikes,
                    gm.createdAt,
                    u.name,
                    u.email,
                    u.profile_pic_url,
                    r.name as role,
                    gmb.is_blocked,
                    gmb.hide_comments
                 FROM ${db_name}.group_messages gm
                 LEFT JOIN ${db_name}.users u ON gm.user_id = u.id
                 LEFT JOIN ${db_name}.user_roles ur ON u.id = ur.user_id
                 LEFT JOIN ${db_name}.roles r ON ur.role_id = r.id
                 LEFT JOIN ${db_name}.group_members gmb
                    ON gm.group_id = gmb.group_id
                     AND gmb.user_id = gm.user_id
                 WHERE gm.id = ?`,
        [messageId]
      );

      if (updatedMessageDetails.length > 0) {
        // Get the group ID to emit to the correct room
        const groupId = updatedMessageDetails[0].group_id;

        // If group_id is not included in the result, fetch it separately
        let groupIdToUse = groupId;
        if (!groupIdToUse) {
          const [messageGroup] = await mysqlServerConnection.query(
            `SELECT group_id FROM ${db_name}.group_messages WHERE id = ?`,
            [messageId]
          );
          if (messageGroup.length > 0) {
            groupIdToUse = messageGroup[0].group_id;
          }
        }

        io.to(groupIdToUse).emit("chatMessageUpdate", updatedMessageDetails[0]);
      }
    } catch (error) {
      console.error("Error updating message:", error);
    }
  });

  socket.on("deleteMessage", async (data) => {
    const { messageId, groupId, token } = data;
    console.log("DELETE MESSAGE DATA", data);
    if (!token) {
      console.error("Token not provided");
      return;
    }

    try {
      const { userId, db_name } = jwt.verify(token, process.env.JWT_ACCESS_KEY);
      await mysqlServerConnection.query(
        `DELETE FROM ${db_name}.group_messages WHERE id = ?`,
        [messageId]
      );

      // Emit the deleted message ID to the group
      io.to(groupId).emit("messageDeleted", { messageId });

      // Update total message count and emit metadata update
      const [totalCountResult] = await mysqlServerConnection.query(
        `SELECT COUNT(*) as total FROM ${db_name}.group_messages WHERE group_id = ?`,
        [groupId]
      );

      const totalMessages = totalCountResult[0].total;
      const totalPages = Math.ceil(totalMessages / 20); // Using default messagesPerPage

      // Emit updated metadata to all users in the room
      // io.to(groupId).emit('messageMetadata', {
      //     totalPages,
      //     totalMessages,
      //     messagesPerPage: 20
      // });
    } catch (error) {
      console.error("Error deleting message:", error);
    }
  });
  // Like comment API
  socket.on("likeComment", async (data) => {
    const { commentId, token } = data;

    if (!token) {
      console.error("Token not provided");
      return;
    }

    try {
      const { userId, db_name } = jwt.verify(token, process.env.JWT_ACCESS_KEY);

      // Fetch the existing likes and dislikes
      const [existingReaction] = await mysqlServerConnection.query(
        `SELECT likes, dislikes, group_id FROM ${db_name}.group_messages WHERE id = ?`,
        [commentId]
      );

      if (existingReaction.length === 0) {
        console.error("Comment not found");
        return;
      }

      let likesArray = [];
      let dislikesArray = [];

      try {
        // ✅ Convert NULL or empty strings to valid JSON before parsing
        let likesData = existingReaction[0].likes;
        let dislikesData = existingReaction[0].dislikes;
        (likesArray =
          likesData && likesData !== "null" && likesData !== "" && likesData),
          dislikesData.length > 0 ? JSON.parse(likesData) : [];
        (dislikesArray =
          dislikesData &&
          dislikesData !== "null" &&
          dislikesData !== "" &&
          dislikesData),
          dislikesData.length > 0 ? JSON.parse(dislikesData) : [];
      } catch (error) {
        console.error("Invalid JSON format for likes or dislikes:", error);
      }

      // ✅ Ensure arrays are always arrays
      likesArray = Array.isArray(likesArray) ? likesArray : [];
      dislikesArray = Array.isArray(dislikesArray) ? dislikesArray : [];

      const groupId = existingReaction[0].group_id;
      console.log("Likes Array:", likesArray);
      console.log("Dislikes Array:", dislikesArray);
      // ✅ Like/Dislike Logic
      if (!likesArray.includes(userId)) {
        likesArray.push(userId);
        dislikesArray = dislikesArray.filter((id) => id !== userId);
      }

      // ✅ Update the database
      await mysqlServerConnection.query(
        `UPDATE ${db_name}.group_messages SET likes = ?, dislikes = ? WHERE id = ?`,
        [JSON.stringify(likesArray), JSON.stringify(dislikesArray), commentId]
      );

      let [result] = await mysqlServerConnection.query(
        `SELECT gm.*, u.name 
                 FROM ${db_name}.group_messages gm
                 JOIN ${db_name}.users u ON gm.user_id = u.id
                 WHERE gm.id = ?`,
        [commentId]
      );

      console.log(result);
      // ✅ Emit updated like/dislike count
      io.to(groupId).emit("messageUpdated", result[0]);
    } catch (error) {
      console.error("Error updating likes for the comment:", error);
    }
  });

  socket.on("dislikeComment", async (data) => {
    const { commentId, token } = data;
    console.log("DISLIKE COMMENT DATA", data);
    if (!token) {
      console.error("⛔ Token not provided");
      return;
    }

    try {
      const { userId, db_name } = jwt.verify(token, process.env.JWT_ACCESS_KEY);

      // Fetch the existing likes and dislikes
      const [existingReaction] = await mysqlServerConnection.query(
        `SELECT likes, dislikes, group_id FROM ${db_name}.group_messages WHERE id = ?`,
        [commentId]
      );

      if (existingReaction.length === 0) {
        console.error("⛔ Comment not found");
        return;
      }

      let likesArray = [];
      let dislikesArray = [];

      try {
        (likesArray =
          likesData && likesData !== "null" && likesData !== "" && likesData),
          dislikesData.length > 0 ? JSON.parse(likesData) : [];
        (dislikesArray =
          dislikesData &&
          dislikesData !== "null" &&
          dislikesData !== "" &&
          dislikesData),
          dislikesData.length > 0 ? JSON.parse(dislikesData) : [];
      } catch (error) {
        console.error("⛔ Invalid JSON format for likes or dislikes:", error);
      }

      // Ensure arrays are always arrays
      likesArray = Array.isArray(likesArray) ? likesArray : [];
      dislikesArray = Array.isArray(dislikesArray) ? dislikesArray : [];

      const groupId = existingReaction[0].group_id;

      // **Dislike/Like Logic**
      if (dislikesArray.includes(userId)) {
        // If user already disliked, remove dislike
        dislikesArray = dislikesArray.filter((id) => id !== userId);
      } else {
        // If not disliked, add dislike and remove from likes
        dislikesArray.push(userId);
        likesArray = likesArray.filter((id) => id !== userId);
      }

      // **Update the database**
      await mysqlServerConnection.query(
        `UPDATE ${db_name}.group_messages SET likes = ?, dislikes = ? WHERE id = ?`,
        [JSON.stringify(likesArray), JSON.stringify(dislikesArray), commentId]
      );

      // **Fetch updated comment with user details**
      const [updatedComment] = await mysqlServerConnection.query(
        `SELECT gm.*, u.name 
                 FROM ${db_name}.group_messages gm
                 JOIN ${db_name}.users u ON gm.user_id = u.id
                 WHERE gm.id = ?`,
        [commentId]
      );

      console.log("✅ Updated Dislike Data:", updatedComment);

      // **Emit updated like/dislike count**
      io.to(groupId).emit("messageUpdated", updatedComment[0]);
    } catch (error) {
      console.error("⛔ Error updating dislikes for the comment:", error);
    }
  });

  // Add emoji reaction to message
  socket.on("addEmojiReaction", async (data) => {
    const { messageId, emoji, token, groupId } = data;

    if (!token) {
      console.error("Token not provided");
      return;
    }

    try {
      const { userId, db_name } = jwt.verify(token, process.env.JWT_ACCESS_KEY);

      // Get current emoji reactions
      const [currentMessage] = await mysqlServerConnection.query(
        `SELECT emoji_reactions FROM ${db_name}.group_messages WHERE id = ?`,
        [messageId]
      );

      if (currentMessage.length === 0) {
        console.error("Message not found");
        return;
      }

      let emojiReactions = {};
      try {
        const reactionsData = currentMessage[0].emoji_reactions;
        if (typeof reactionsData === "string") {
          emojiReactions = JSON.parse(reactionsData || "{}");
        } else if (
          typeof reactionsData === "object" &&
          reactionsData !== null
        ) {
          emojiReactions = reactionsData;
        } else {
          emojiReactions = {};
        }
      } catch (error) {
        console.error("Error parsing emoji reactions:", error);
        emojiReactions = {};
      }

      // Toggle user's reaction for this emoji
      if (!emojiReactions[emoji]) {
        emojiReactions[emoji] = [];
      }

      const userIndex = emojiReactions[emoji].indexOf(userId);
      if (userIndex > -1) {
        // Remove reaction
        emojiReactions[emoji].splice(userIndex, 1);
        if (emojiReactions[emoji].length === 0) {
          delete emojiReactions[emoji];
        }
      } else {
        // Add reaction
        emojiReactions[emoji].push(userId);
      }

      // Update database
      await mysqlServerConnection.query(
        `UPDATE ${db_name}.group_messages SET emoji_reactions = ? WHERE id = ?`,
        [JSON.stringify(emojiReactions), messageId]
      );

      // Get updated message with user details
      const [updatedMessage] = await mysqlServerConnection.query(
        `SELECT gm.*, u.name, u.profile_pic_url, r.name as role
         FROM ${db_name}.group_messages gm
         JOIN ${db_name}.users u ON gm.user_id = u.id
         LEFT JOIN ${db_name}.user_roles ur ON u.id = ur.user_id
         LEFT JOIN ${db_name}.roles r ON ur.role_id = r.id
         WHERE gm.id = ?`,
        [messageId]
      );

      // Emit updated message to all users in the group
      io.to(groupId).emit("messageUpdated", updatedMessage[0]);
    } catch (error) {
      console.error("Error updating emoji reaction:", error);
    }
  });

  // Upload media file
  socket.on("uploadMedia", async (data) => {
    console.log(
      `📤 Upload request received: ${data?.fileName} (${
        data?.fileType
      }) - Size: ${
        data?.fileSize
          ? (data.fileSize / (1024 * 1024)).toFixed(2) + "MB"
          : "unknown"
      }`
    );
    const { fileData, fileName, fileType, fileSize, groupId, token } = data;
    console.log("UPLOAD MEDIA DATA************************************", data);

    if (!token) {
      console.error("Token not provided");
      socket.emit("uploadError", {
        message: "Authentication token is required",
      });
      return;
    }

    try {
      const { userId, db_name } = jwt.verify(token, process.env.JWT_ACCESS_KEY);

      // Validate file size (20MB limit)
      const maxSize = 20 * 1024 * 1024; // 20MB
      if (fileSize && fileSize > maxSize) {
        socket.emit("uploadError", {
          message: `File size (${(fileSize / (1024 * 1024)).toFixed(
            2
          )}MB) exceeds the 20MB limit`,
        });
        return;
      }

      console.log(
        `Starting upload: ${fileName} (${
          fileSize
            ? (fileSize / (1024 * 1024)).toFixed(2) + "MB"
            : "unknown size"
        })`
      );

      // Import S3 upload functions
      const {
        uploadImageToS3,
        uploadVideoToS3,
        uploadDocToS3,
        uploadAudioToS3,
      } = require("../tools/aws");

      let uploadResult;
      let messageType;

      // Create a timeout promise
      const uploadTimeout = new Promise((_, reject) => {
        const timeoutDuration = Math.max(
          120000,
          120000 + (fileSize ? (fileSize / (1024 * 1024)) * 15000 : 0)
        ); // 2 minutes + 15s per MB
        setTimeout(() => {
          reject(
            new Error(`Upload timeout after ${timeoutDuration / 1000} seconds`)
          );
        }, timeoutDuration);
      });

      console.log("File Type:******************************************************", fileType);

      // Create upload promise
      const uploadPromise = (async () => {
        // Determine upload function based on file type
        if (fileType.startsWith("image/")) {
          console.log("Uploading image to S3...*********************************^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^");
          uploadResult = await uploadImageToS3(fileData);
          messageType = "image";
        } else if (fileType.startsWith("video/")) {
          uploadResult = await uploadVideoToS3(fileData, fileName);
          messageType = "video";
        } else if (fileType.startsWith("audio/")) {
          uploadResult = await uploadAudioToS3(fileData, fileName);
          messageType = "audio";
        } else {
          uploadResult = await uploadDocToS3(fileData, fileName);
          messageType = "document";
        }
        return { uploadResult, messageType };
      })();

      // Race between upload and timeout
      const { uploadResult: result, messageType: type } = await Promise.race([
        uploadPromise,
        uploadTimeout,
      ]);

      console.log(`Upload completed successfully: ${fileName}`);
      

      // Send back the upload result
      socket.emit("mediaUploaded", {
        success: true,
        mediaUrl: result.path,
        messageType: type,
        fileName,
      });
    } catch (error) {
      console.error("Error uploading media:", error);
      socket.emit("uploadError", {
        message: error.message || "Upload failed due to server error",
      });
    }
  });

  socket.on("getNotifications", async () => {
    try {
      const token = socket.handshake.query.token;

      if (!token) {
        return socket.emit("error", {
          message: "Authentication token is missing",
        });
      }

      const { userId, db_name } = jwt.verify(token, process.env.JWT_ACCESS_KEY);

      const [notifications] = await mysqlServerConnection.query(
        `SELECT * FROM ${db_name}.notifications WHERE user_id = ? AND is_read = false`,
        [userId]
      );

      // console.log(notifications);
      socket.emit("pushNotifications", { notifications });
    } catch (error) {
      console.error("Error in getNotifications:", error.message);
      if (error.name === "JsonWebTokenError") {
        socket.emit("error", {
          message: "Invalid or expired authentication token",
        });
      } else {
        socket.emit("error", { message: "Internal server error" });
      }
    }
  });

  socket.on("sendLiveClassNotification", async (data) => {
    try {
      const { live_class_id, class_id, token } = data;
      console.log("EMIT LIVE CLASS NOTIFICATION", data);
      // Verify token
      let decoded;
      try {
        decoded = jwt.verify(token, process.env.JWT_ACCESS_KEY);
      } catch (err) {
        socket.emit("notificationError", { error: "Invalid token" });
        return;
      }

      const db_name = decoded.db_name;

      // Get live class details
      const [liveClassData] = await mysqlServerConnection.query(
        `
                SELECT class_name, live_class_url, class_id 
                FROM ${db_name}.live_class 
                WHERE id = ? AND is_deleted = 0
                `,
        [live_class_id]
      );

      if (liveClassData.length === 0) {
        socket.emit("notificationError", { error: "Live class not found" });
        return;
      }

      const liveClass = liveClassData[0];

      // Get classroom name
      const [classroomData] = await mysqlServerConnection.query(
        `
                SELECT cls_name 
                FROM ${db_name}.classroom 
                WHERE id = ? AND is_deleted = 0
                `,
        [class_id]
      );

      if (classroomData.length === 0) {
        socket.emit("notificationError", { error: "Classroom not found" });
        return;
      }

      const classroomName = classroomData[0].cls_name;

      // Create notification content
      const title = "Live Class Started";

      // Generate join URL
      const baseUrl = process.env.PROD_FRONTEND_URL
        ? process.env.PROD_FRONTEND_URL
        : process.env.DEV_FRONTEND_URL;
      const joinUrl = `${baseUrl}/classroom/live-class${liveClass.live_class_url}`;

      // Embed the URL in an HTML anchor tag within the message body
      const message = `Live Class "${liveClass.class_name}" for ${classroomName} has started.Join the class from your respective class room dashboard`;

      // Get all users in the classroom
      const [users] = await mysqlServerConnection.query(
        `
                SELECT ct.user_id, u.email 
                FROM ${db_name}.classroom_trainee ct
                JOIN ${db_name}.users u ON ct.user_id = u.id
                WHERE ct.class_id = ? AND u.is_blocked = 0 AND u.is_deleted = 0
                `,
        [class_id]
      );

      if (users.length === 0) {
        socket.emit("notificationSent", {
          success: false,
          message: "No users found in this class",
        });
        return;
      }

      // Track how many online users were notified
      let onlineUsersNotified = 0;

      // Add notifications to the database for each user
      for (const user of users) {
        const notificationData = await mysqlServerConnection.query(
          `
                    INSERT INTO ${db_name}.notifications
                    (user_id, title, body, is_read, is_deleted) 
                    VALUES (?, ?, ?, ?, ?)
                    `,
          [user.user_id, title, message, false, false]
        );

        // console.log("Notification DATA -->>>",notificationData);
        const notification = await mysqlServerConnection.query(
          `SELECT * FROM ${db_name}.notifications WHERE id = ?`,
          [notificationData[0].insertId]
        );

        // console.log("LIVE CLASS USER NOTIFICATION-->>>>",notification);

        // Emit notification to the specific user if they are online
        const userSocketId = onlineUsers.get(user.user_id.toString());
        if (userSocketId) {
          io.to(userSocketId).emit("liveClassNotification", notification[0][0]);
          onlineUsersNotified++;
        }
      }
    } catch (error) {
      console.error("Error sending class notification:", error);
      socket.emit("notificationError", {
        error: "Failed to send notification",
      });
    }
  });

  // Handle ending a live class and notify all users in the class
  socket.on("endLiveClass", async (data) => {
    console.log("END LIVE CLASS NOTIFICATION", data);
    try {
      const { live_class_id, class_id, token } = data;

      // Verify token
      let decoded;
      try {
        decoded = jwt.verify(token, process.env.JWT_ACCESS_KEY);
      } catch (err) {
        socket.emit("notificationError", { error: "Invalid token" });
        return;
      }

      const db_name = decoded.db_name;

      // Get live class details for logging purposes
      const [liveClassData] = await mysqlServerConnection.query(
        `
                SELECT class_name 
                FROM ${db_name}.live_class 
                WHERE id = ? AND is_deleted = 0
                `,
        [live_class_id]
      );

      if (liveClassData.length === 0) {
        socket.emit("notificationError", { error: "Live class not found" });
        return;
      }

      // Get all users in the classroom
      const [users] = await mysqlServerConnection.query(
        `
                SELECT ct.user_id
                FROM ${db_name}.classroom_trainee ct
                JOIN ${db_name}.users u ON ct.user_id = u.id
                WHERE ct.class_id = ? AND u.is_blocked = 0 AND u.is_deleted = 0
                `,
        [class_id]
      );

      if (users.length === 0) {
        socket.emit("endLiveClassResponse", {
          success: false,
          message: "No users found in this class",
        });
        return;
      }

      // Track how many online users were notified
      let onlineUsersNotified = 0;

      // Notify all online users in the class that the live class has ended
      for (const user of users) {
        console.log("USER", user);
        const userSocketId = onlineUsers.get(user.user_id.toString());
        if (userSocketId) {
          io.to(userSocketId).emit("liveClassEnded", {
            live_class_id,
            class_id,
            message: "This live class has ended",
          });
          onlineUsersNotified++;
        }
      }

      // Send response back to sender
      socket.emit("endLiveClassResponse", {
        success: true,
        message: `Notified ${onlineUsersNotified} online users that the live class has ended`,
        online_users_notified: onlineUsersNotified,
      });
    } catch (error) {
      console.error("Error ending live class:", error);
      socket.emit("notificationError", {
        error: "Failed to end live class notification",
      });
    }
  });

  // Handle marking a notification as read
  socket.on("markNotificationAsRead", async (notificationId) => {
    try {
      console.log("Marking notification as read:", notificationId);
      const token = socket.handshake.query.token;

      if (!token) {
        return socket.emit("error", {
          message: "Authentication token is missing",
        });
      }

      const { userId, db_name } = jwt.verify(token, process.env.JWT_ACCESS_KEY);

      // Update the notification to mark it as read
      await mysqlServerConnection.query(
        `UPDATE ${db_name}.notifications SET is_read = true WHERE id = ? AND user_id = ?`,
        [notificationId, userId]
      );

      // Get all unread notifications for the user
      const [notifications] = await mysqlServerConnection.query(
        `SELECT * FROM ${db_name}.notifications WHERE user_id = ? AND is_read = false ORDER BY createdAt DESC`,
        [userId]
      );

      // Send the updated notifications list back to the client
      socket.emit("pushNotifications", { notifications });
    } catch (error) {
      console.error("Error marking notification as read:", error.message);
      if (error.name === "JsonWebTokenError") {
        socket.emit("error", {
          message: "Invalid or expired authentication token",
        });
      } else {
        socket.emit("error", { message: "Internal server error" });
      }
    }
  });

  // Handle notifications for scheduled live classes
  socket.on("liveClassScheduleNotification", async (data) => {
    try {
      console.log("Live class schedule notification:", data);
      const { live_class_id, class_id } = data;
      const token = socket.handshake.query.token;

      if (!token) {
        return socket.emit("error", {
          message: "Authentication token is missing",
        });
      }

      const decoded = jwt.verify(token, process.env.JWT_ACCESS_KEY);
      const db_name = decoded.db_name;

      // Get live class details
      const [liveClassData] = await mysqlServerConnection.query(
        `
                SELECT class_name, class_desc, class_date, start_time, end_time 
                FROM ${db_name}.live_class 
                WHERE id = ? AND is_deleted = 0
                `,
        [live_class_id]
      );

      if (liveClassData.length === 0) {
        socket.emit("notificationError", { error: "Live class not found" });
        return;
      }

      const liveClass = liveClassData[0];

      // Get classroom name
      const [classroomData] = await mysqlServerConnection.query(
        `
                SELECT cls_name 
                FROM ${db_name}.classroom 
                WHERE id = ? AND is_deleted = 0
                `,
        [class_id]
      );

      if (classroomData.length === 0) {
        socket.emit("notificationError", { error: "Classroom not found" });
        return;
      }

      const classroomName = classroomData[0].cls_name;

      // Format date and time for notification
      const formattedDate = new Date(liveClass.class_date).toLocaleDateString(
        "en-US",
        {
          weekday: "long",
          year: "numeric",
          month: "long",
          day: "numeric",
        }
      );

      const formattedStartTime = new Date(
        liveClass.start_time
      ).toLocaleTimeString("en-US", {
        hour: "2-digit",
        minute: "2-digit",
      });

      const formattedEndTime = new Date(liveClass.end_time).toLocaleTimeString(
        "en-US",
        {
          hour: "2-digit",
          minute: "2-digit",
        }
      );

      // Create notification content
      const title = "New Live Class Scheduled";
      const message = `Live Class: "${liveClass.class_name}" has been scheduled for ${classroomName}, Date: ${formattedDate}, Time: ${formattedStartTime} - ${formattedEndTime}`;

      // Get all users in the classroom
      const [users] = await mysqlServerConnection.query(
        `
                SELECT ct.user_id, u.email 
                FROM ${db_name}.classroom_trainee ct
                JOIN ${db_name}.users u ON ct.user_id = u.id
                WHERE ct.class_id = ? AND u.is_blocked = 0 AND u.is_deleted = 0
                `,
        [class_id]
      );

      if (users.length === 0) {
        socket.emit("notificationSent", {
          success: false,
          message: "No users found in this class",
        });
        return;
      }

      // Track how many online users were notified
      let onlineUsersNotified = 0;

      // Add notifications to the database for each user
      for (const user of users) {
        const notificationData = await mysqlServerConnection.query(
          `
                    INSERT INTO ${db_name}.notifications
                    (user_id, title, body, is_read, is_deleted) 
                    VALUES (?, ?, ?, ?, ?)
                    `,
          [user.user_id, title, message, false, false]
        );

        const notification = await mysqlServerConnection.query(
          `SELECT * FROM ${db_name}.notifications WHERE id = ?`,
          [notificationData.insertId]
        );
        // Emit notification to the specific user if they are online
        const userSocketId = onlineUsers.get(user.user_id.toString());
        if (userSocketId) {
          io.to(userSocketId).emit("liveClassScheduled", notification);
          onlineUsersNotified++;
        }
      }

      // Send response back to sender
      socket.emit("notificationSent", {
        success: true,
        message: `Notification sent to ${users.length} users (${onlineUsersNotified} online)`,
        users_notified: users.length,
        online_users_notified: onlineUsersNotified,
      });
    } catch (error) {
      console.error("Error sending live class schedule notification:", error);
      socket.emit("notificationError", {
        error: "Failed to send notification",
      });
    }
  });

  // Handle client disconnect
  socket.on("disconnect", () => {
    // console.log(`Client disconnected: ${socket.id}`);
    onlineUsers.delete(socket.id);
    console.log(
      `User ${socket.id} disconnected and removed from online users map`
    );
  });
  
});

// Start the HTTPS server
httpsServer.listen(port, () => {
  console.log(`Listening at http://localhost:${port}`);
});

module.exports = { io };
