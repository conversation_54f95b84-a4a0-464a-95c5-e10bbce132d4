import React, { useState, useEffect } from 'react';
import { getAboutUs } from '../../../services/userService';
import NoData from '../../../components/common/NoData';
import './AboutUs.css';

function AboutUs() {
  const [aboutUs, setAboutUs] = useState([]);

  useEffect(() => {
    getAboutUsData();
  }, []);

  const getAboutUsData = async () => {
    try {
      const response = await getAboutUs();
      if (response.success && response.data?.response) {
        setAboutUs(response.data.response);
      } else {
        setAboutUs([]);
      }
    } catch (error) {
      console.error('Error fetching about us data:', error);
      setAboutUs([]);
    }
  };

  return (
    <div className="about-us-container">
      <div className="section-header">
        <h3>About Us</h3>
        <p>Learn more about our mission, values, and who we are</p>
      </div>

      <div className="about-us-content">
        {aboutUs.length > 0 ? (
          aboutUs.map((item, index) => (
            <div className="about-us-item" key={index}>
              <div className="about-us-title">{item.name}</div>
              <div className="about-us-description">{item.description}</div>
            </div>
          ))
        ) : (
          <NoData message="No About Us content available." />
        )}
      </div>
    </div>
  );
}

export default AboutUs;
