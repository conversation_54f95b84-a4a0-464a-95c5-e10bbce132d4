import React, { useState, useEffect } from 'react';
import NoData from '../../../components/common/NoData';
import Loader from '../../../components/common/Loader';
import { Icon } from '@iconify/react';
import { getTraineeAnalytics } from '../../../services/traineeService';
import { toast } from 'react-toastify';

function TraineeAnalyticsCertificatesTab({ traineeId }) {
  const [loading, setLoading] = useState(false);
  const [certificatesData, setCertificatesData] = useState([]);

  const fetchCertificatesData = async () => {
    try {
      setLoading(true);
      const response = await getTraineeAnalytics({ trainee_id: traineeId });
      console.log('certificates Data---------------------:', response.data.certificates);

      if (response.success) {
        setCertificatesData(response.data.certificates || []);
      } else {
        toast.error(response.message || 'Failed to fetch certificates data');
      }
    } catch (error) {
      console.error('Error fetching certificates data:', error);
      toast.error('Something went wrong while fetching certificates data');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (traineeId) {
      fetchCertificatesData();
    }
  }, [traineeId]);

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const handleViewCertificate = (certificate) => {
    if (!certificate.certificate_url) {
      toast.warning('Certificate not available');
      return;
    }

    // Create a link element
    const link = document.createElement('a');
    link.href = certificate.certificate_url;
    link.target = '_blank';
    link.rel = 'noopener noreferrer';
    
    // Add download attribute with filename
    const fileName = `${certificate.course_name}_${certificate.certificate_name}.pdf`;
    link.download = fileName;
    
    // Trigger click
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  if (loading) {
    return (
      <div className="d-flex justify-content-center align-items-center" style={{ minHeight: '200px' }}>
        <Loader />
      </div>
    );
  }

  if (!certificatesData || certificatesData.length === 0) {
    return <NoData message="No certificates found" />;
  }

  return (
    <div className="card">
      <div className="table-responsive">
        <table className="table table-borderless mb-0">
          <thead>
            <tr>
              <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500', width: '80px' }}>SL No.</th>
              <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500', width: '250px' }}>Course Name</th>
              <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500', width: '200px' }}>Issue Date</th>
              <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500', width: '100px' }}>Actions</th>
            </tr>
          </thead>
          <tbody>
            {certificatesData.map((certificate, index) => (
              <tr key={certificate.id} className="border-bottom">
                <td className="py-3 align-middle">{index + 1}</td>
                <td className="py-3 align-middle d-flex flex-column">
                  <span className="text-truncate d-inline-block" style={{ maxWidth: '130px' }}>
                    {certificate.course_name}
                    <br />
                  </span>
                  <span className='text-muted'>
                    {certificate.certificate_name}
                  </span>
                </td>
                <td className="py-3 align-middle">
                  {formatDate(certificate.issued_date)}
                </td>
                <td className="py-3 align-middle">
                  <button
                    className="btn btn-outline-dark btn-sm border border-dark"
                    style={{ width: '36px', height: '36px' }}
                    title="Download Certificate"
                    onClick={() => handleViewCertificate(certificate)}
                  >
                    <Icon 
                      icon="fluent:document-pdf-24-regular"
                      width="16" 
                      height="16" 
                    />
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}

export default TraineeAnalyticsCertificatesTab; 