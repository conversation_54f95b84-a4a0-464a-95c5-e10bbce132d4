import React, { useState, useEffect, useMemo } from 'react'
import { Icon } from '@iconify/react';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import course1 from '../../../assets/images/admin/course/course1.png'
import './Classroom.css';
import NoData from '../../../components/common/NoData'
import { getClassroomDetails, getAllTrainers, createClassroom, updateClassroomDetails, updateClassroomStatus, deleteClassroom } from '../../../services/adminService';
import Loader from '../../../components/common/Loader';
import { encodeData, decodeData } from '../../../utils/encodeAndEncode';
import { usePermissions } from '../../../context/PermissionsContext';

function AllClassroom() {
    const navigate = useNavigate();
    const { permissions } = usePermissions();
    const [showCreateModal, setShowCreateModal] = useState(false);
    const [showEditModal, setShowEditModal] = useState(false);
    const [showDeleteModal, setShowDeleteModal] = useState(false);
    const [deleteClassroomData, setDeleteClassroomData] = useState(null);
    const [editingClassroom, setEditingClassroom] = useState(null);
    const [formData, setFormData] = useState({
        classroomName: '',
        description: '',
        collaborationName: '',
        uploadFile: null,
        bannerPreview: null,
        selectedTrainers: []
    });

    const [formErrors, setFormErrors] = useState({
        classroomName: '',
        description: '',
        collaborationName: '',
        uploadFile: ''
    });

    const user = JSON.parse(localStorage.getItem('user') || '{}');
    const user_id = user?.id;
    console.log('User ID:', user_id);

    const [isSubmitting, setIsSubmitting] = useState(false);

    const [AllClassroomData, setAllClassroomData] = useState([]);
    const [AllTrainersData, setAllTrainersData] = useState([]);

    const [searchQuery, setSearchQuery] = useState('');
    const [currentPage, setCurrentPage] = useState(1);
    const [itemsPerPage, setItemsPerPage] = useState(10);
    const [totalPages, setTotalPages] = useState(1);
    const [totalRecords, setTotalRecords] = useState(0);
    const [statusFilter, setStatusFilter] = useState('all');
    const [statusLoading, setStatusLoading] = useState(null);
    const [loading, setLoading] = useState(true);

    const getAllClassroomData = async () => {
        try {
            const response = await getClassroomDetails({
                page: currentPage,
                limit: itemsPerPage,
                search: searchQuery,
                user_id: user_id
            });
            console.log('Classroom data Response', response);
            if (response.success && response.data) {
                setAllClassroomData(response.data.classes || []);
                setTotalPages(response.data.pagination?.totalPages || 1);
                setTotalRecords(response.data.pagination?.totalRecords || 0);
            }
        } catch (error) {
            console.log(error);
            toast.error('Failed to fetch classrooms', {
                position: "top-center"
            });
        } finally {
            setTimeout(() => {
                setLoading(false);
            }, 500);
        }
    };

    const getAllTrainersData = async () => {
        try {
            const response = await getAllTrainers();
            console.log('Trainers data Response', response);
            if (response.success) {
                setAllTrainersData(response.trainers || []);
            }
        } catch (error) {
            // toast.error(error.response?.data?.message || 'Failed to fetch trainers', {
            //     position: "top-center"
            // });
            console.log('Trainers data error', error);
        }
    }

    useEffect(() => {
        const minLoadTime = 500; // Minimum loading time in milliseconds
        const startTime = Date.now();

        getAllClassroomData().then(() => {
            // Calculate remaining time to meet minimum loading duration
            const elapsedTime = Date.now() - startTime;
            if (elapsedTime < minLoadTime) {
                setTimeout(() => {
                    setLoading(false);
                }, minLoadTime - elapsedTime);
            }
        });
        getAllTrainersData();
    }, [currentPage, itemsPerPage, searchQuery]);

    const handleInputChange = (e) => {
        const { name, value } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: value
        }));
        // Clear error when user starts typing
        setFormErrors(prev => ({
            ...prev,
            [name]: ''
        }));
    };

    const handleBannerUpload = (e) => {
        const file = e.target.files[0];
        if (file) {
            if (file.size > 50 * 1024 * 1024) { // 50MB
                alert('File size should not exceed 50MB');
                return;
            }
            setFormData(prev => ({
                ...prev,
                uploadFile: file,
                bannerPreview: URL.createObjectURL(file)
            }));
            // Clear error when file is uploaded
            setFormErrors(prev => ({
                ...prev,
                uploadFile: ''
            }));
        }
    };

    const handleRemoveBanner = () => {
        if (formData.bannerPreview && formData.bannerPreview.startsWith('blob:')) {
            URL.revokeObjectURL(formData.bannerPreview);
        }
        setFormData(prev => ({
            ...prev,
            uploadFile: null,
            bannerPreview: null
        }));
        // Clear the upload file error when banner is removed, but it will be required again
        setFormErrors(prev => ({
            ...prev,
            uploadFile: ''
        }));
    };

    const handleTrainerSelect = (e) => {
        const trainerId = parseInt(e.target.value);
        if (trainerId) {
            const trainer = AllTrainersData.find(t => t.user_id === trainerId);
            if (trainer && !formData.selectedTrainers.some(t => t.user_id === trainer.user_id)) {
                setFormData(prev => ({
                    ...prev,
                    selectedTrainers: [...prev.selectedTrainers, trainer]
                }));
            }
            // Reset the select element to default option
            e.target.value = '';
        }
    };

    const handleRemoveTrainer = (trainerId) => {
        setFormData(prev => ({
            ...prev,
            selectedTrainers: prev.selectedTrainers.filter(t => t.user_id !== trainerId)
        }));
    };

    const handleSearchChange = (e) => {
        setSearchQuery(e.target.value);
        setCurrentPage(1); // Reset to first page when search changes
    };

    const handlePageChange = (newPage) => {
        setCurrentPage(newPage);
    };

    const handleLimitChange = (e) => {
        setItemsPerPage(parseInt(e.target.value));
        setCurrentPage(1); // Reset to first page when limit changes
    };

    const handleStatusFilterChange = (e) => {
        setStatusFilter(e.target.value);
    };

    // Filter classrooms based on status
    const filteredClassrooms = useMemo(() => {
        if (statusFilter === 'all') {
            return AllClassroomData;
        }
        return AllClassroomData.filter(classroom => {
            if (statusFilter === 'active') {
                return classroom.is_active === 1;
            } else if (statusFilter === 'inactive') {
                return classroom.is_active === 0;
            }
            return true;
        });
    }, [AllClassroomData, statusFilter]);

    const handleCreateClassroom = async () => {
        // Validate form
        const errors = {};
        if (!formData.classroomName.trim()) {
            errors.classroomName = 'Classroom Title is required';
        }
        if (!formData.description.trim()) {
            errors.description = 'Classroom Description is required';
        }
        if (!formData.collaborationName.trim()) {
            errors.collaborationName = 'Collaboration Name is required';
        }
        if (!formData.uploadFile) {
            errors.uploadFile = 'Banner image is required';
        }

        if (Object.keys(errors).length > 0) {
            setFormErrors(errors);
            return;
        }

        try {
            setIsSubmitting(true);
            setLoading(true);

            // Create FormData object for API call
            const classroomForm = new FormData();
            classroomForm.append("cls_name", formData.classroomName);
            classroomForm.append("cls_desc", formData.description);
            classroomForm.append("collaboration_name", formData.collaborationName);
            classroomForm.append("banner_img", formData.uploadFile);
            classroomForm.append("trainers", JSON.stringify(formData.selectedTrainers));
            classroomForm.append("page", currentPage);
            classroomForm.append("limit", itemsPerPage);
            classroomForm.append("search", searchQuery);
            classroomForm.append("user_id", localStorage.getItem('user_id'));

            const response = await createClassroom(classroomForm);
            console.log('API Response:', response);

            if (response.success) {
                toast.success('Classroom created successfully!', {
                    position: "top-center"
                });
        setShowCreateModal(false);
        setFormData({
                    classroomName: '',
            description: '',
            collaborationName: '',
                    uploadFile: null,
            bannerPreview: null,
            selectedTrainers: []
        });
                setFormErrors({
                    classroomName: '',
                    description: '',
                    collaborationName: '',
                    uploadFile: ''
                });
                await getAllClassroomData();
            } else {
                toast.error(response.message || 'Failed to create classroom', {
                    position: "top-center"
                });
            }
        } catch (error) {
            console.error('Error creating classroom:', error);
            toast.error(error.response?.data?.message || 'Failed to create classroom', {
                position: "top-center"
            });
        } finally {
            setIsSubmitting(false);
            setLoading(false);
        }
    };

    const handleNavigateToClassroom = (classroomId) => {
        if (!permissions.classroom_management_view_analytics) {
            toast.warning("You don't have permission to view classroom analytics");
            return;
        }
        const encodedClassroomId = encodeData(classroomId);
        navigate(`/admin/classrooms/classroom-dashboard/${encodedClassroomId}`);
    };

    const handleNavigateToCreateClassroom = () => {
        if (!permissions.classroom_management_create) {
            toast.warning("You don't have permission to create classrooms");
            return;
        }
        setShowCreateModal(true);
    };

    const handleEditClassroom = (classroom) => {
        if (!permissions.classroom_management_edit) {
            toast.warning("You don't have permission to edit classrooms");
            return;
        }
        setEditingClassroom(classroom);
        setFormData({
            classroomName: classroom.cls_name,
            description: classroom.cls_desc,
            collaborationName: classroom.collaboration_name,
            uploadFile: null,
            bannerPreview: classroom.banner_img,
            selectedTrainers: classroom.trainers || []
        });
        setShowEditModal(true);
    };

    const handleUpdateClassroom = async () => {
        // Validate form
        const errors = {};
        if (!formData.classroomName.trim()) {
            errors.classroomName = 'Classroom Title is required';
        }
        if (!formData.description.trim()) {
            errors.description = 'Classroom Description is required';
        }
        if (!formData.collaborationName.trim()) {
            errors.collaborationName = 'Collaboration Name is required';
        }
        if (!formData.bannerPreview && !formData.uploadFile) {
            errors.uploadFile = 'Banner image is required';
        }

        if (Object.keys(errors).length > 0) {
            setFormErrors(errors);
            return;
        }

        try {
            setIsSubmitting(true);
            setLoading(true);

            const classroomForm = new FormData();
            classroomForm.append("class_id", editingClassroom.id);
            classroomForm.append("user_id", user_id);
            classroomForm.append("cls_name", formData.classroomName);
            classroomForm.append("cls_desc", formData.description);
            classroomForm.append("collaboration_name", formData.collaborationName);
            classroomForm.append("trainers", JSON.stringify(formData.selectedTrainers));

            if (formData.uploadFile) {
                classroomForm.append("banner_img", formData.uploadFile);
            }

            const response = await updateClassroomDetails(classroomForm);
            console.log('Update API Response:', response);

            if (response && (response.data?.success || response.status === 200 || response.success)) {
                toast.success('Classroom updated successfully!', {
                    position: "top-center"
                });
                setShowEditModal(false);
                setEditingClassroom(null);
                setFormData({
                    classroomName: '',
                    description: '',
                    collaborationName: '',
                    uploadFile: null,
                    bannerPreview: null,
                    selectedTrainers: []
                });
                setFormErrors({
                    classroomName: '',
                    description: '',
                    collaborationName: '',
                    uploadFile: ''
                });
                await getAllClassroomData();
            } else {
                toast.error(response?.data?.message || response?.message || 'Failed to update classroom', {
                    position: "top-center"
                });
            }
        } catch (error) {
            console.error('Error updating classroom:', error);
            toast.error(error.response?.data?.message || 'Failed to update classroom', {
                position: "top-center"
            });
        } finally {
            setIsSubmitting(false);
            setLoading(false);
        }
    };

    const handleToggleStatus = async (e, classroom) => {
        e.stopPropagation();
        if (!permissions.classroom_management_status) {
            toast.warning("You don't have permission to change classroom status");
            return;
        }

        try {
            setStatusLoading(classroom.id);
            setLoading(true);

            const newStatus = classroom.is_active === 1 ? 0 : 1;
            const payload = {
                class_id: classroom.id,
                status: newStatus
            };

            const response = await updateClassroomStatus(payload);

            if (response.success) {
                toast.success(`Classroom ${newStatus ? 'activated' : 'deactivated'} successfully!`, {
                    position: "top-center"
                });
                await getAllClassroomData();
            } else {
                toast.error(response.message || 'Failed to update classroom status', {
                    position: "top-center"
                });
            }
        } catch (error) {
            console.error('Error updating classroom status:', error);
            toast.error(error.response?.data?.message || 'Failed to update classroom status', {
                position: "top-center"
            });
        } finally {
            setStatusLoading(null);
            setLoading(false);
        }
    };

    const handleDeleteClick = (e, classroomId, classroomName) => {
        e.stopPropagation();
        if (!permissions.classroom_management_delete) {
            toast.warning("You don't have permission to delete classrooms");
            return;
        }
        setDeleteClassroomData({ id: classroomId, name: classroomName });
        setShowDeleteModal(true);
    };

    const handleDeleteConfirm = async () => {
        if (!deleteClassroomData) return;

        try {
            setLoading(true);
            const response = await deleteClassroom({
                classroom_id: deleteClassroomData.id
            });

            if (response.success) {
                toast.success('Classroom deleted successfully!', {
                    position: "top-center"
                });
                await getAllClassroomData();
            } else {
                toast.error(response.message || 'Failed to delete classroom', {
                    position: "top-center"
                });
            }
        } catch (error) {
            console.error('Error deleting classroom:', error);
            toast.error(error.response?.data?.message || 'Failed to delete classroom', {
                position: "top-center"
            });
        } finally {
            setShowDeleteModal(false);
            setDeleteClassroomData(null);
            setLoading(false);
        }
    };

    // -- Get status class
    const getStatusClass = (isActive) => {
        return isActive === 1 ? 'bg-success-subtle' : 'bg-secondary-subtle';
    }

    const getStatusText = (isActive) => {
        return isActive === 1 ? 'Active' : 'Inactive';
    }

    return (
        <>
            {/* Search and Create button  */}
            <div className="row d-flex mb-3">
                <div className="col-md-6 mt-2 d-flex justify-content-start align-items-center">
                    <input
                        type="search"
                        className="form-control"
                        placeholder="Search classrooms..."
                        style={{ maxWidth: '300px' }}
                        value={searchQuery}
                        onChange={handleSearchChange}
                    />
                </div>
                <div className="col-md-6 text-end mt-2 d-flex justify-content-end align-items-center gap-2">
                    <select
                        className="form-select"
                        style={{ width: '180px' }}
                        value={statusFilter}
                        onChange={handleStatusFilterChange}
                    >
                        <option value="all">All Status</option>
                        <option value="active">Active</option>
                        <option value="inactive">Inactive</option>
                    </select>
                    <button
                        className="btn btn-primary text-nowrap"
                        style={{ width: '150px' }}
                        onClick={handleNavigateToCreateClassroom}
                        disabled={!permissions.classroom_management_create}
                        title={!permissions.classroom_management_create ? "You don't have permission to create classrooms" : "Create Classroom"}
                    >
                        Create Classroom
                    </button>
                </div>
            </div>

            {/* Table  */}
            <div className="row mb-3">
                <div className="col-12">
                    <div className="card">
                        <div className="table-responsive">
                            {loading ? (
                                <Loader
                                    caption="Loading Classrooms..."
                                    minLoadTime={500}
                                    containerHeight="400px"
                                />
                            ) : (
                            <table className="table table-borderless mb-0" style={{ minWidth: '800px' }}>
                                <thead>
                                    <tr>
                                        <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500', width: '40%' }}>Classroom</th>
                                        <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500', width: '100px' }}>Trainees</th>
                                        <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500', width: '100px' }}>Trainers</th>
                                        <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500', width: '100px' }}>Status</th>
                                        <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500', width: '100px' }}>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {filteredClassrooms.length > 0 ? filteredClassrooms.map((classroom) => (
                                        <tr
                                            key={classroom.id}
                                            className={`border-bottom ${permissions.classroom_management_view_analytics ? 'cursor-pointer hover-bg-light' : ''}`}
                                            onClick={() => handleNavigateToClassroom(classroom.id)}
                                            style={{ cursor: permissions.classroom_management_view_analytics ? 'pointer' : 'default' }}
                                            title={!permissions.classroom_management_view_analytics ? "You don't have permission to view classroom analytics" : "Click to view classroom analytics"}
                                        >
                                            <td className="py-3">
                                                <div className="d-flex align-items-center">
                                                    <img
                                                        src={classroom.banner_img || course1}
                                                        alt={classroom.cls_name}
                                                        className="me-3 rounded"
                                                        style={{ width: '180px', height: '100px', objectFit: 'cover' }}
                                                    />
                                                    <div>
                                                        <div className="fw-medium mb-2">{classroom.cls_name}</div>
                                                        <div className="d-flex flex-wrap" style={{ gap: '16px', fontSize: '14px', color: '#6c757d' }}>
                                                            <span className="d-inline-flex align-items-center">
                                                                <Icon icon="fluent:clipboard-task-24-regular" className="me-2" width="16" height="16" />
                                                                {classroom.total_assignments || 0} Assignments
                                                            </span>
                                                            <span className="d-inline-flex align-items-center">
                                                                <Icon icon="fluent:quiz-new-24-regular" className="me-2" width="16" height="16" />
                                                                {classroom.total_assessments || 0} Assessments
                                                            </span>
                                                        </div>
                                                        <div className="d-flex flex-wrap mt-2" style={{ gap: '16px', fontSize: '14px', color: '#6c757d' }}>
                                                            <span className="d-inline-flex align-items-center">
                                                                <Icon icon="fluent:video-clip-24-regular" className="me-2" width="16" height="16" />
                                                                {classroom.total_recorded || 0} Recorded
                                                            </span>
                                                            <span className="d-inline-flex align-items-center">
                                                                <Icon icon="fluent:video-person-24-regular" className="me-2" width="16" height="16" />
                                                                {classroom.total_live_classes || 0} Live Classes
                                                            </span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </td>
                                            <td className="py-3 align-middle text-start">
                                                <div className="d-flex align-items-center">
                                                    <Icon icon="fluent:people-24-regular" className="me-2" width="16" height="16" />
                                                    {classroom.total_trainees || 0}
                                                </div>
                                            </td>
                                            <td className="py-3 align-middle text-start">
                                                <div className="d-flex align-items-center">
                                                    <Icon icon="fluent:person-24-regular" className="me-2" width="16" height="16" />
                                                    {classroom.total_trainers || 0}
                                                </div>
                                            </td>
                                            <td className="py-3 align-middle text-start">
                                                <span className={`badge text-dark ${getStatusClass(classroom.is_active)}`}>
                                                    {getStatusText(classroom.is_active)}
                                                </span>
                                            </td>
                                            <td className="py-3 align-middle text-start">
                                                <div className="d-flex gap-2">
                                                    <button
                                                        className="btn btn-outline-dark btn-sm border border-dark"
                                                        style={{ width: '36px', height: '36px' }}
                                                        title={!permissions.classroom_management_edit ? "You don't have permission to edit classrooms" : "Edit Classroom"}
                                                        onClick={(e) => {
                                                            e.stopPropagation();
                                                            handleEditClassroom(classroom);
                                                        }}
                                                        disabled={!permissions.classroom_management_edit}
                                                    >
                                                        <Icon icon="fluent:edit-24-regular" width="16" height="16" />
                                                    </button>
                                                    <button
                                                        className="btn btn-outline-dark btn-sm border border-dark"
                                                        style={{ width: '36px', height: '36px' }}
                                                        title={!permissions.classroom_management_delete ? "You don't have permission to delete classrooms" : "Delete Classroom"}
                                                        onClick={(e) => handleDeleteClick(e, classroom.id, classroom.cls_name)}
                                                        disabled={!permissions.classroom_management_delete}
                                                    >
                                                        <Icon icon="fluent:delete-24-regular" width="16" height="16" />
                                                    </button>
                                                    <button
                                                        className={`btn ${classroom.is_active === 1 ? "btn-success" : "btn-outline-warning"} btn-sm border`}
                                                        style={{ width: '36px', height: '36px' }}
                                                        title={!permissions.classroom_management_status ? "You don't have permission to change status" : `${classroom.is_active === 1 ? 'Deactivate' : 'Activate'} Classroom`}
                                                        onClick={(e) => handleToggleStatus(e, classroom)}
                                                        disabled={statusLoading === classroom.id || !permissions.classroom_management_status}
                                                    >
                                                        {statusLoading === classroom.id ? (
                                                            <span className="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                                        ) : (
                                                        <Icon
                                                                icon={classroom.is_active === 1 ?
                                                                    "fluent:checkmark-circle-24-regular" :
                                                                    "fluent:dismiss-circle-24-regular"
                                                            }
                                                            width="16"
                                                            height="16"
                                                        />
                                                        )}
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    )) : (
                                        <tr>
                                            <td colSpan="5" className="text-center" style={{ height: '400px', verticalAlign: 'middle' }}>
                                                <NoData caption='No classrooms found' />
                                            </td>
                                        </tr>
                                    )}
                                </tbody>
                            </table>
                            )}
                        </div>
                    </div>
                </div>
            </div>

            {/* Record per page and pagination  */}
            <div className="row">
                <div className="col-md-6 d-flex align-items-center gap-3">
                    <select
                        className="form-select"
                        style={{ width: 'auto' }}
                        value={itemsPerPage}
                        onChange={handleLimitChange}
                    >
                        <option value={10}>10 records per page</option>
                        <option value={25}>25 records per page</option>
                        <option value={50}>50 records per page</option>
                    </select>
                    <span className="text-muted">
                        Total Records: {filteredClassrooms.length}
                    </span>
                </div>
                <div className="col-md-6 d-flex justify-content-end align-items-center gap-3">
                    <button
                        className="btn btn-primary"
                        style={{ width: '150px' }}
                        onClick={() => handlePageChange(currentPage - 1)}
                        disabled={currentPage === 1}
                    >
                        Prev
                    </button>
                    <span>Page {currentPage} of {Math.ceil(filteredClassrooms.length / itemsPerPage)}</span>
                    <button
                        className="btn btn-primary"
                        style={{ width: '150px' }}
                        onClick={() => handlePageChange(currentPage + 1)}
                        disabled={currentPage === Math.ceil(filteredClassrooms.length / itemsPerPage)}
                    >
                        Next
                    </button>
                </div>
            </div>

            {/* Create Classroom Modal */}
            {showCreateModal && (
                <div className="modal show d-block" style={{ backgroundColor: 'rgba(0,0,0,0.5)' }}>
                    <div className="modal-dialog modal-dialog-centered">
                        <div className="modal-content">
                            <div className="modal-header">
                                <h5 className="modal-title">Create New Classroom</h5>
                                <button
                                    type="button"
                                    className="btn-close"
                                    onClick={() => setShowCreateModal(false)}
                                ></button>
                            </div>
                            <div className="modal-body">
                                {/* Title */}
                                <div className="mb-3">
                                    <label className="form-label">Classroom Title*</label>
                                    <input
                                        type="text"
                                        className={`form-control ${formErrors.classroomName ? 'is-invalid' : ''}`}
                                        name="classroomName"
                                        value={formData.classroomName}
                                        onChange={handleInputChange}
                                        placeholder="Enter classroom title"
                                        maxLength={100}
                                    />
                                    {formErrors.classroomName ? (
                                        <div className="invalid-feedback">{formErrors.classroomName}</div>
                                    ) : (
                                        <small className="text-muted">Maximum 100 characters allowed ({100 - formData.classroomName.length} remaining)</small>
                                    )}
                                </div>

                                {/* Description */}
                                <div className="mb-3">
                                    <label className="form-label">Classroom Description*</label>
                                    <textarea
                                        className={`form-control ${formErrors.description ? 'is-invalid' : ''}`}
                                        name="description"
                                        value={formData.description}
                                        onChange={handleInputChange}
                                        rows="3"
                                        placeholder="Enter classroom description"
                                        maxLength={500}
                                    />
                                    {formErrors.description ? (
                                        <div className="invalid-feedback">{formErrors.description}</div>
                                    ) : (
                                        <small className="text-muted">Maximum 500 characters allowed ({500 - formData.description.length} remaining)</small>
                                    )}
                                </div>

                                {/* Collaboration Name */}
                                <div className="mb-3">
                                    <label className="form-label">Collaboration Name (Chat)*</label>
                                    <input
                                        type="text"
                                        className={`form-control ${formErrors.collaborationName ? 'is-invalid' : ''}`}
                                        name="collaborationName"
                                        value={formData.collaborationName}
                                        onChange={handleInputChange}
                                        placeholder="Enter collaboration name"
                                        maxLength={100}
                                    />
                                    {formErrors.collaborationName ? (
                                        <div className="invalid-feedback">{formErrors.collaborationName}</div>
                                    ) : (
                                        <small className="text-muted">Maximum 100 characters allowed ({100 - formData.collaborationName.length} remaining)</small>
                                    )}
                                </div>

                                {/* Banner Upload */}
                                <div className="mb-3">
                                    <label className="form-label">Upload Banner*</label>
                                    {!formData.bannerPreview ? (
                                        <div
                                            className={`upload-area ${formErrors.uploadFile ? 'border-danger' : ''}`}
                                            onClick={() => document.getElementById('bannerUpload').click()}
                                        >
                                            <input
                                                type="file"
                                                id="bannerUpload"
                                                className="d-none"
                                                onChange={handleBannerUpload}
                                                accept="image/*"
                                            />
                                            <div className="text-center">
                                                <Icon icon="fluent:document-add-24-regular" className={`mb-2 ${formErrors.uploadFile ? 'text-danger' : 'text-primary'}`} width="48" height="48" />
                                                <div className="upload-text">Upload Banner</div>
                                                <div className="upload-hint">Maximum file size: 50MB</div>
                                                {formErrors.uploadFile && (
                                                    <div className="text-danger mt-2">{formErrors.uploadFile}</div>
                                                )}
                                            </div>
                                        </div>
                                    ) : (
                                        <div className="position-relative">
                                            <img
                                                src={formData.bannerPreview}
                                                alt="Banner Preview"
                                                className="img-fluid rounded"
                                                style={{ maxHeight: '200px', width: '100%', objectFit: 'cover' }}
                                            />
                                            <button
                                                className="btn btn-link text-danger position-absolute top-0 end-0 p-2"
                                                onClick={handleRemoveBanner}
                                                type="button"
                                            >
                                                <Icon icon="fluent:delete-24-regular" width="24" height="24" />
                                            </button>
                                        </div>
                                    )}
                                </div>

                                {/* Trainer Selection */}
                                <div className="mb-3">
                                    <label className="form-label">Select Trainers</label>
                                    <select
                                        className="form-select"
                                        onChange={handleTrainerSelect}
                                        defaultValue=""
                                    >
                                        <option value="">Choose trainer...</option>
                                        {AllTrainersData
                                            .filter(trainer => !formData.selectedTrainers.some(t => t.user_id === trainer.user_id))
                                            .map(trainer => (
                                                <option key={trainer.user_id} value={trainer.user_id}>
                                                    {trainer.name}
                                                </option>
                                            ))
                                        }
                                    </select>

                                    {/* Selected Trainers Display */}
                                    {formData.selectedTrainers.length > 0 && (
                                        <div className="trainers-container mt-2">
                                            {formData.selectedTrainers.map(trainer => (
                                                <div key={trainer.user_id} className="selected-trainer">
                                                    <span className="d-flex align-items-center">
                                                        <Icon icon="fluent:person-24-regular" className="me-2" width="16" height="16" />
                                                        {trainer.name}
                                                    </span>
                                                    <button
                                                        type="button"
                                                        className="remove-trainer"
                                                        onClick={() => handleRemoveTrainer(trainer.user_id)}
                                                        title="Remove trainer"
                                                    >
                                                        <Icon icon="fluent:dismiss-24-filled" width="16" height="16" />
                                                    </button>
                                                </div>
                                            ))}
                                        </div>
                                    )}
                                </div>
                            </div>
                            <div className="modal-footer">
                                <button
                                    type="button"
                                    className="btn btn-secondary"
                                    onClick={() => {
                                        setShowCreateModal(false);
                                        handleRemoveBanner();
                                        setFormData({
                                            classroomName: '',
                                            description: '',
                                            collaborationName: '',
                                            uploadFile: null,
                                            bannerPreview: null,
                                            selectedTrainers: []
                                        });
                                        setFormErrors({
                                            classroomName: '',
                                            description: '',
                                            collaborationName: '',
                                            uploadFile: ''
                                        });
                                    }}
                                    disabled={isSubmitting}
                                >
                                    Cancel
                                </button>
                                <button
                                    type="button"
                                    className="btn btn-primary d-flex align-items-center gap-2"
                                    onClick={handleCreateClassroom}
                                    disabled={isSubmitting}
                                >
                                    {isSubmitting ? (
                                        <>
                                            <span className="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                            Creating...
                                        </>
                                    ) : (
                                        'Create Classroom'
                                    )}
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            )}

            {/* Edit Classroom Modal */}
            {showEditModal && (
                <div className="modal show d-block" style={{ backgroundColor: 'rgba(0,0,0,0.5)' }}>
                    <div className="modal-dialog modal-dialog-centered">
                        <div className="modal-content">
                            <div className="modal-header">
                                <h5 className="modal-title">Edit Classroom</h5>
                                <button
                                    type="button"
                                    className="btn-close"
                                    onClick={() => {
                                        setShowEditModal(false);
                                        setEditingClassroom(null);
                                        handleRemoveBanner();
                                        setFormData({
                                            classroomName: '',
                                            description: '',
                                            collaborationName: '',
                                            uploadFile: null,
                                            bannerPreview: null,
                                            selectedTrainers: []
                                        });
                                        setFormErrors({
                                            classroomName: '',
                                            description: '',
                                            collaborationName: '',
                                            uploadFile: ''
                                        });
                                    }}
                                ></button>
                            </div>
                            <div className="modal-body">
                                {/* Title */}
                                <div className="mb-3">
                                    <label className="form-label">Classroom Title*</label>
                                    <input
                                        type="text"
                                        className={`form-control ${formErrors.classroomName ? 'is-invalid' : ''}`}
                                        name="classroomName"
                                        value={formData.classroomName}
                                        onChange={handleInputChange}
                                        placeholder="Enter classroom title"
                                        maxLength={100}
                                    />
                                    {formErrors.classroomName ? (
                                        <div className="invalid-feedback">{formErrors.classroomName}</div>
                                    ) : (
                                        <small className="text-muted">Maximum 100 characters allowed ({100 - formData.classroomName.length} remaining)</small>
                                    )}
                                </div>

                                {/* Description */}
                                <div className="mb-3">
                                    <label className="form-label">Classroom Description*</label>
                                    <textarea
                                        className={`form-control ${formErrors.description ? 'is-invalid' : ''}`}
                                        name="description"
                                        value={formData.description}
                                        onChange={handleInputChange}
                                        rows="3"
                                        placeholder="Enter classroom description"
                                        maxLength={500}
                                    />
                                    {formErrors.description ? (
                                        <div className="invalid-feedback">{formErrors.description}</div>
                                    ) : (
                                        <small className="text-muted">Maximum 500 characters allowed ({500 - formData.description.length} remaining)</small>
                                    )}
                                </div>

                                {/* Collaboration Name */}
                                <div className="mb-3">
                                    <label className="form-label">Collaboration Name (Chat)*</label>
                                    <input
                                        type="text"
                                        className={`form-control ${formErrors.collaborationName ? 'is-invalid' : ''}`}
                                        name="collaborationName"
                                        value={formData.collaborationName}
                                        onChange={handleInputChange}
                                        placeholder="Enter collaboration name"
                                        maxLength={100}
                                    />
                                    {formErrors.collaborationName ? (
                                        <div className="invalid-feedback">{formErrors.collaborationName}</div>
                                    ) : (
                                        <small className="text-muted">Maximum 100 characters allowed ({100 - formData.collaborationName.length} remaining)</small>
                                    )}
                                </div>

                                {/* Banner Upload */}
                                <div className="mb-3">
                                    <label className="form-label">Upload Banner*</label>
                                    {!formData.bannerPreview ? (
                                        <div
                                            className={`upload-area ${formErrors.uploadFile ? 'border-danger' : ''}`}
                                            onClick={() => document.getElementById('editBannerUpload').click()}
                                        >
                                            <input
                                                type="file"
                                                id="editBannerUpload"
                                                className="d-none"
                                                onChange={handleBannerUpload}
                                                accept="image/*"
                                            />
                                            <div className="text-center">
                                                <Icon icon="fluent:document-add-24-regular" className={`mb-2 ${formErrors.uploadFile ? 'text-danger' : 'text-primary'}`} width="48" height="48" />
                                                <div className="upload-text">Upload Banner</div>
                                                <div className="upload-hint">Maximum file size: 50MB</div>
                                                {formErrors.uploadFile && (
                                                    <div className="text-danger mt-2">{formErrors.uploadFile}</div>
                                                )}
                                            </div>
                                        </div>
                                    ) : (
                                        <div className="position-relative">
                                            <img
                                                src={formData.bannerPreview}
                                                alt="Banner Preview"
                                                className="img-fluid rounded"
                                                style={{ maxHeight: '200px', width: '100%', objectFit: 'cover' }}
                                            />
                                            <button
                                                className="btn btn-link text-danger position-absolute top-0 end-0 p-2"
                                                onClick={handleRemoveBanner}
                                                type="button"
                                            >
                                                <Icon icon="fluent:delete-24-regular" width="24" height="24" />
                                            </button>
                                        </div>
                                    )}
                                    {formErrors.uploadFile && !formData.bannerPreview && (
                                        <div className="text-danger mt-2">{formErrors.uploadFile}</div>
                                    )}
                                </div>

                                {/* Trainer Selection */}
                                <div className="mb-3">
                                    <label className="form-label">Select Trainers</label>
                                    <select
                                        className="form-select"
                                        onChange={handleTrainerSelect}
                                        defaultValue=""
                                    >
                                        <option value="">Choose trainer...</option>
                                        {AllTrainersData
                                            .filter(trainer => !formData.selectedTrainers.some(t => t.user_id === trainer.user_id))
                                            .map(trainer => (
                                                <option key={trainer.user_id} value={trainer.user_id}>
                                                    {trainer.name}
                                                </option>
                                            ))
                                        }
                                    </select>

                                    {/* Selected Trainers Display */}
                                    {formData.selectedTrainers.length > 0 && (
                                        <div className="trainers-container mt-2">
                                            {formData.selectedTrainers.map(trainer => (
                                                <div key={trainer.user_id} className="selected-trainer">
                                                    <span className="d-flex align-items-center">
                                                        <Icon icon="fluent:person-24-regular" className="me-2" width="16" height="16" />
                                                        {trainer.name}
                                                    </span>
                                                    <button
                                                        type="button"
                                                        className="remove-trainer"
                                                        onClick={() => handleRemoveTrainer(trainer.user_id)}
                                                        title="Remove trainer"
                                                    >
                                                        <Icon icon="fluent:dismiss-24-filled" width="16" height="16" />
                                                    </button>
                                                </div>
                                            ))}
                                        </div>
                                    )}
                                </div>
                            </div>
                            <div className="modal-footer">
                                <button
                                    type="button"
                                    className="btn btn-secondary"
                                    onClick={() => {
                                        setShowEditModal(false);
                                        setEditingClassroom(null);
                                        handleRemoveBanner();
                                        setFormData({
                                            classroomName: '',
                                            description: '',
                                            collaborationName: '',
                                            uploadFile: null,
                                            bannerPreview: null,
                                            selectedTrainers: []
                                        });
                                        setFormErrors({
                                            classroomName: '',
                                            description: '',
                                            collaborationName: '',
                                            uploadFile: ''
                                        });
                                    }}
                                    disabled={isSubmitting}
                                >
                                    Cancel
                                </button>
                                <button
                                    type="button"
                                    className="btn btn-primary d-flex align-items-center gap-2"
                                    onClick={handleUpdateClassroom}
                                    disabled={isSubmitting}
                                >
                                    {isSubmitting ? (
                                        <>
                                            <span className="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                            Updating...
                                        </>
                                    ) : (
                                        'Update Classroom'
                                    )}
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            )}

            {/* Delete Confirmation Modal */}
            {showDeleteModal && (
                <div className="modal show d-block" style={{ backgroundColor: 'rgba(0,0,0,0.5)' }}>
                    <div className="modal-dialog modal-dialog-centered">
                        <div className="modal-content">
                            <div className="modal-header">
                                <h5 className="modal-title">Delete Classroom</h5>
                                <button
                                    type="button"
                                    className="btn-close"
                                    onClick={() => {
                                        setShowDeleteModal(false);
                                        setDeleteClassroomData(null);
                                    }}
                                ></button>
                            </div>
                            <div className="modal-body">
                                <div className="text-center mb-3">
                                    <Icon icon="fluent:delete-24-regular" className="text-danger mb-2" width="48" height="48" />
                                    <h5>Are you sure?</h5>
                                    <p className="text-muted">
                                        Do you really want to delete the classroom "{deleteClassroomData?.name}"? This action cannot be undone.
                                    </p>
                                </div>
                            </div>
                            <div className="modal-footer">
                                <button
                                    type="button"
                                    className="btn btn-secondary"
                                    onClick={() => {
                                        setShowDeleteModal(false);
                                        setDeleteClassroomData(null);
                                    }}
                                >
                                    Cancel
                                </button>
                                <button
                                    type="button"
                                    className="btn btn-danger d-flex align-items-center gap-2"
                                    onClick={handleDeleteConfirm}
                                    disabled={loading}
                                >
                                    {loading ? (
                                        <>
                                            <span className="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                            Deleting...
                                        </>
                                    ) : (
                                        <>
                                            <Icon icon="fluent:delete-24-regular" width="16" height="16" />
                                            Delete Classroom
                                        </>
                                    )}
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            )}
        </>
    )
}

export default AllClassroom

