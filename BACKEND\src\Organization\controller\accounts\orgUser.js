const { response } = require("express");
const jwt = require("jsonwebtoken");
const bcrypt = require("bcrypt");
const { mysqlServerConnection } = require("../../../db/db");
const createdb = require("../../../superAdmin/db/utils/createOrganizationDb");

const {
  Validate,
  hashPassword,
  generateAccessToken,
  generateRefreshToken,
  sendEmail,
  LogsHandler,
} = require("../../../tools/tools");
const {
  sendMagicLink,
  sendForgotPasswordLink,
  sendOTP,
  sentEmailNotification,
  registerOTP,
  forgotPasswordOTP,
  ActiveAccountOTPFromLoginPage
} = require("../../../tools/emailtemplates");
const { body } = require("express-validator");
const crypto = require("crypto");
const {
  SetNotification,
  SendPushNotificaiton,
} = require("../../../utils/sendNotificaiton");
const { uploadImageToS3 } = require("../../../tools/aws");
const maindb = process.env.MAIN_DB;
const { OAuth2Client } = require("google-auth-library");
const client = new OAuth2Client(process.env.GOOGLE_CLIENT_ID); // or directly paste your clientId string

const googleLoginorSignup = async (req, res) => {
  console.log("googleLoginorSignup called");
  const { tokenId } = req.body;
  const { organization_url } = req.body;
  console.log("organization_url", organization_url);
  const defaultDbName = "admin_lms";

  try {
    // Verify Google token
    const ticket = await client.verifyIdToken({
      idToken: tokenId,
      audience: process.env.GOOGLE_CLIENT_ID,
    });

    const payload = ticket.getPayload();
    const { email, name, picture, sub, aud } = payload;

    console.log("Google Token Verified:");
    console.log({ email, name, picture, aud, sub });

    // Check if user exists
    const [users] = await mysqlServerConnection.query(
      `SELECT * FROM ${defaultDbName}.users WHERE email = ? LIMIT 1`,
      [email]
    );

    console.log("User lookup result:", users);

    let user = users[0];

    if (user) {
      if (user.auth_provider === "google") {
        // Generate JWT for existing Google user
        const token = jwt.sign(
          { id: user.id, email: user.email, db_name: user.db_name },
          process.env.JWT_ACCESS_KEY,
          { expiresIn: "7d" }
        );

        console.log("JWT:", token);

        // Format response to match the structure expected by the frontend
        const response = {
          success: true,
          message: "Login successful via Google",
          data: {
            access_token: token,
            role: "trainee", // Default role for Google users
            user_details: user,
            dashboard: {
              // Add any dashboard data if available
              name: "LMS Dashboard",
              logo: user.profile_pic_url || null,
              favicon: null,
            },
          },
        };
        console.log("Response:", response);
        return res.status(200).json(response);
      } else {
        // Existing user but not registered with Google
        const response = {
          success: false,
          message:
            "You registered using email/password. Please login with credentials.",
        };
        console.log("Response:", response);
        return res.status(403).json(response);
      }
    }

    // Register new Google user with default values for required fields
    const currentDate = new Date().toISOString().slice(0, 19).replace("T", " ");
    const defaultPassword = await bcrypt.hash(
      Math.random().toString(36).slice(-8),
      10
    ); // Generate random password

    // Set a default db_name value - using 'admin_lms' as it's the database we're working with

    const [result] = await mysqlServerConnection.query(
      `INSERT INTO ${defaultDbName}.users 
            (name, email, profile_pic_url, is_email_verified, auth_provider, 
             password, createdAt, updatedAt, is_phone_verified, is_blocked, 
             is_verified, is_deleted, db_name) 
            VALUES (?, ?, ?, 1, 'google', ?, ?, ?, 0, 0, 1, 0, ?)`,
      [
        name,
        email,
        picture,
        defaultPassword, // Hashed random password
        currentDate, // createdAt
        currentDate, // updatedAt
        defaultDbName, // db_name
      ]
    );

    console.log("Insert result:", result);

    // Get the full user record
    const [newUser] = await mysqlServerConnection.query(
      `SELECT * FROM ${defaultDbName}.users WHERE id = ? LIMIT 1`,
      [result.insertId]
    );

    user = newUser[0];

    const token = jwt.sign(
      { id: user.id, email: user.email, db_name: user.db_name },
      process.env.JWT_ACCESS_KEY,
      { expiresIn: "7d" }
    );

    console.log("JWT:", token);

    // Format response to match the structure expected by the frontend
    const response = {
      success: true,
      message: "User registered and logged in via Google",
      data: {
        access_token: token,
        role: "trainee", // Default role for Google users
        user_details: user,
        dashboard: {
          // Add any dashboard data if available
          name: "LMS Dashboard",
          logo: user.profile_pic_url || null,
          favicon: null,
        },
      },
    };
    console.log("Response:", response);
    return res.status(201).json(response);
  } catch (error) {
    console.error("Google Auth Error:", error);
    const response = {
      success: false,
      message: "Google login failed",
      error: error.message,
    };
    console.log("Error Response:", response);
    return res.status(400).json(response);
  }
};

const organizationLoginUser = async (req, res) => {
  console.log("📥 ➡️ organizationLoginUser called");

  try {
    let { email, password, fcm_token, organization_url } = req.body;

    console.log("🟡 Incoming Request Body:", req.body);

    email = email?.trim().toLowerCase();

    if (!email || !password) {
      console.log("❌ Missing email or password");
      return res.status(400).json({
        success: false,
        status: 404,
        message: "Email and password are required.",
      });
    }
    console.log("✅ Email and password received");

    if (!organization_url) {
      console.log("❌ Missing organization URL");
      return res.status(400).json({
        success: false,
        status: 404,
        message: "Organization URL is required.",
      });
    }
    console.log("✅ Organization URL received");

    console.log("🔍 Looking up organization...");
    const [dbmain] = await mysqlServerConnection.query(
      `SELECT db_name, org_logo_url, org_favicon_url, name 
       FROM ${maindb}.organization 
       WHERE auth_sub_domain = ?`,
      [organization_url]
    );

    if (dbmain.length === 0) {
      console.log("❌ Organization not found");
      return res.status(404).json({
        success: false,
        status: 404,
        message: "Invalid organization URL.",
      });
    }

    const orgdb = dbmain[0];
    console.log("✅ Organization found:", orgdb);
    console.log("✅ Organization DB Name:", orgdb.db_name);

    console.log("📧 Checking if email exists...");
    const [checkemail] = await mysqlServerConnection.query(
      `SELECT * FROM ${orgdb.db_name}.users WHERE email = ?`,
      [email]
    );

    if (checkemail.length === 0) {
      console.log("❌ Email not registered in users table");
      return res.status(404).json({
        success: false,
        status: 404,
        message: "Email is not registered.",
      });
    }
    console.log("✅ Email exists in users table");

    console.log("👤 Fetching user and role...");
    const [userRows] = await mysqlServerConnection.query(
      `SELECT users.*, roles.name AS role, roles.id AS role_id
       FROM ${orgdb.db_name}.users
       JOIN ${orgdb.db_name}.user_roles ON users.id = user_roles.user_id
       JOIN ${orgdb.db_name}.roles ON user_roles.role_id = roles.id
       WHERE users.email = ?`,
      [email]
    );

    console.log("🔍 User and role query executed", userRows);

    if (userRows.length === 0) {
      console.log("❌ User + role not found");
      return res
        .status(401)
        .json({ success: false, status: 404, message: "User not found." });
    }

    const user = userRows[0];
    console.log("✅ User with role found:", {
      id: user.id,
      name: user.name,
      email: user.email,
      role: user.role,
      role_id: user.role_id,
    });

    // ✅ Custom check for is_email_verified === 0
    if (user.is_email_verified === 0) {
      console.log("❌ Email is not verified (value is 0)");
      return res.status(200).json({
        success: true,
        is_email_verified: 0,
        message: "Please verify your email before logging in.",
      });
    }

    if (user.is_deleted === 1) {
      console.log("❌ User is marked as deleted");
      return res.status(403).json({
        success: false,
        status: 404,
        message: "Your account has been deleted.",
      });
    }

    if (user.is_blocked === 1) {
      console.log("❌ User is blocked");
      return res.status(403).json({
        success: false,
        status: 404,
        message: "Your account is blocked. Please contact the admin.",
      });
    }

    if (user.is_email_verified !== 1) {
      console.log("❌ Email not verified");
      return res.status(403).json({
        success: false,
        status: 404,
        message: "Please activate your email to continue.",
      });
    }
    console.log("✅ Email is verified");

    console.log("🔁 Updating FCM token...");
    await mysqlServerConnection.query(
      `UPDATE ${orgdb.db_name}.users 
       SET fcm_token = ? 
       WHERE email = ? AND is_verified = true`,
      [fcm_token, email]
    );
    console.log("✅ FCM token updated");

    console.log("🔐 Verifying password...");
    const passwordMatch = await bcrypt.compare(password, user.password);
    if (!passwordMatch) {
      console.log("❌ Password does not match");
      return res
        .status(401)
        .json({ success: false, status: 404, message: "Invalid password." });
    }
    console.log("✅ Password matched");

    console.log("📝 Logging login event...");
    LogsHandler(orgdb.db_name, user.id, "Login", `${user.name} is Logged In`);
    console.log("✅ Login event logged");

    console.log("🧹 Cleaning user data...");
    const cleanedUser = (({
      is_blocked,
      is_deleted,
      password,
      db_name,
      company_description,
      ic_no,
      fin_no,
      gender,
      date_of_birth,
      date_joined,
      pwm_rank,
      job_title,
      ...rest
    }) => rest)(user);
    console.log("✅ Cleaned User Object:", cleanedUser);

    console.log("🔑 Generating access and refresh tokens...");
    const accessToken = await generateAccessToken({
      id: user.id,
      db_name: orgdb.db_name,
      name: user.name,
      email: user.email,
      role: user.role,
      role_id: user.role_id,
    });

    const refreshToken = await generateRefreshToken({
      id: user.id,
      db_name: orgdb.db_name,
      name: user.name,
      email: user.email,
      role: user.role,
      role_id: user.role_id,
    });
    console.log("✅ Tokens generated successfully");

    const [permissionsResult] = await mysqlServerConnection.query(
      `SELECT rp.permission_id, rp.is_granted, p.slug
       FROM ${orgdb.db_name}.role_permissions rp
       JOIN ${maindb}.permission_modules_and_actions_mapping p 
       ON rp.permission_id = p.id
       WHERE rp.role_id = ?`,
      [user.role_id]
    );

    console.log("✅ Permissions query executed");
    console.log("📊 Number of permissions found:", permissionsResult.length);

    console.log("🔄 Mapping permissions...");
    const permissions = permissionsResult.map(permission => ({
      permission_id: permission.permission_id,
      slug: permission.slug,
      is_granted: permission.is_granted
    }));
    console.log("✅ Permissions mapped successfully");

    console.log("🔧 Preparing response payload...");
    const responsePayload = {
      success: true,
      data: {
        message: "Login Successful.",
        access_token: accessToken,
        refresh_token: refreshToken,
        role: user.role,
        user_details: cleanedUser,
        permissions: permissions,
        dashboard: {
          logo: orgdb.org_logo_url,
          favicon: orgdb.org_favicon_url,
          name: orgdb.name,
        },
      },
    };
    console.log("✅ Response payload prepared");
    console.log("📦 Response payload size:", JSON.stringify(responsePayload).length, "bytes");

    console.log("📤 Attempting to send response...");
    try {
      return res.status(200).json(responsePayload);
    } catch (error) {
      console.error("❌ Error sending response:", error);
      console.error("🔍 Error details:", {
        name: error.name,
        message: error.message,
        stack: error.stack
      });
      throw error;
    }
    console.log("✅ Response sent successfully");
  } catch (error) {
    console.error("❌ Error in organizationLoginUser:", error);
    console.error("🔍 Detailed error information:", {
      name: error.name,
      message: error.message,
      code: error.code,
      stack: error.stack,
      response: error.response ? {
        status: error.response.status,
        data: error.response.data
      } : null
    });

    if (res.headersSent) {
      console.error("⚠️ Headers already sent, cannot send error response");
      return;
    }

    console.log("📤 Sending error response...");
    return res.status(500).json({
      success: false,
      message: "Internal server error",
      error: error.message,
      code: error.code || 'UNKNOWN'
    });
  }
};


// -------------------------------------------------------------------------------------

function validatePassword(password) {
  let validationMessage = null;

  switch (true) {
    case password.length < 8:
      validationMessage = "Password must be at least 8 characters long.";

      break;

    case !/\d/.test(password):
      validationMessage = "Password must contain at least one number.";

      break;

    case !/[A-Z]/.test(password):
      validationMessage =
        "Password must contain at least one uppercase letter.";

      break;

    case !/[!@#$%^&*(),.?":{}|<>]/.test(password):
      validationMessage =
        "Password must contain at least one special character.";

      break;

    default:
      validationMessage = null;
  }

  return validationMessage;
}

function validateEmail(email) {
  let validationMessage = null;

  switch (true) {
    case !email:
      validationMessage = "Email is required.";

      break;

    case !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email):
      validationMessage = "Must be a valid email address.";

      break;

    default:
      validationMessage = null;
  }

  return validationMessage;
}

const registration = async (req, res) => {

  try {
    const result = req.body;

    // Check if required fields are present

    if (Object.keys(result).length === 0) {
      return res.status(422).json({
        success: false,

        data: {
          error_msg: "Following fields are required",

          response: {
            profile_pic: "file",

            benner_img: "file",

            name: "",

            password: "",

            organization_id: "",

            email: "",

            mobile: "",

            country: "",

            state: "",

            zipcode: "",

            address: "",

            role_id: "",

            is_verified: false,
          },
        },
      });
    }

    // Validate password and email

    const passwordError = validatePassword(result.password);

    const emailError = validateEmail(result.email);

    if (passwordError) {
      return res.status(401).json({
        success: false,

        data: { error_msg: passwordError },
      });
    }

    if (emailError) {
      return res.status(401).json({
        success: false,

        data: { error_msg: emailError },
      });
    }

    // Get organization database

    const [dbmain] = await mysqlServerConnection.query(
      `SELECT db_name FROM ${maindb}.organization WHERE id = ?`,

      [result.organization_id]
    );

    // Check if the user already exists

    const [user] = await mysqlServerConnection.query(
      `SELECT * FROM ${dbmain[0].db_name}.users WHERE email = ? OR mobile = ?`,

      [result.email, result.mobile]
    );

    // Generate OTP and expiry time

    const otp = crypto.randomInt(100000, 999999);

    const otpExpiry = new Date(Date.now() + 10 * 60 * 1000); // OTP expires in 10 minutes

    if (user.length > 0) {
      // If user exists and is verified

      if (user[0].is_verified === 1) {
        if (user[0].mobile === result.mobile) {
          return res.status(400).json({
            success: false,
            data: { error_msg: "Phone number already exists." },
          });
        }

        if (user[0].email === result.email) {
          return res.status(400).json({
            success: false,
            data: { error_msg: "Email already exists." },
          });
        }
      } else {
        // If user exists but is not verified, update user details and send OTP

        const hashedPassword = await hashPassword(result.password);

        await mysqlServerConnection.query(
          `UPDATE ${dbmain[0].db_name}.users 

                     SET profile_pic_url = ?, benner_img_url = ?, name = ?, db_name = ?, password = ?, email = ?, 

                         mobile = ?, country = ?, state = ?, zipcode = ?, address = ?, is_verified = ?, fcm_token = ? 

                     WHERE id = ?`,

          [
            result.profile_pic,
            result.benner_img,
            result.name,
            dbmain[0].db_name,
            hashedPassword,
            result.email,

            result.mobile,
            result.country,
            result.state,
            result.zipcode,
            result.address,
            0,
            result.fcm_token,

            user[0].id,
          ]
        );

        // Update user roles

        await mysqlServerConnection.query(
          `UPDATE ${dbmain[0].db_name}.user_roles SET role_id = ? WHERE user_id = ?`,

          [result.role_id, user[0].id]
        );

        // Insert OTP

        await mysqlServerConnection.query(
          `INSERT INTO ${dbmain[0].db_name}.otps (email, otp, otp_expire_time) 

                     VALUES (?, ?, ?) ON DUPLICATE KEY UPDATE otp = ?, otp_expire_time = ?`,

          [result.email, otp, otpExpiry, otp, otpExpiry]
        );

        // Send OTP

        sendOTP(
          result.email,
          "Verify your account using the verification code",
          otp
        );

        return res.status(200).json({
          success: true,
          data: { message: "OTP sent to your email to verify your account" },
        });
      }
    } else {
      // If user doesn't exist, create a new user

      const hashedPassword = await hashPassword(result.password);

      const [authuser] = await mysqlServerConnection.query(
        `INSERT INTO ${dbmain[0].db_name}.users (profile_pic_url, benner_img_url, name, db_name, password, email, 

                 mobile, country, state, zipcode, address, is_verified, fcm_token) 

                 VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,

        [
          result.profile_pic,
          result.benner_img,
          result.name,
          dbmain[0].db_name,
          hashedPassword,
          result.email,

          result.mobile,
          result.country,
          result.state,
          result.zipcode,
          result.address,
          0,
          result.fcm_token,
        ]
      );

      // Insert user role

      await mysqlServerConnection.query(
        `INSERT INTO ${dbmain[0].db_name}.user_roles (user_id, role_id) VALUES (?, ?)`,

        [authuser.insertId, result.role_id]
      );

      // Insert OTP for new user

      await mysqlServerConnection.query(
        `INSERT INTO ${dbmain[0].db_name}.otps (email, otp, otp_expire_time) 

                 VALUES (?, ?, ?) ON DUPLICATE KEY UPDATE otp = ?, otp_expire_time = ?`,

        [result.email, otp, otpExpiry, otp, otpExpiry]
      );

      // Send OTP

      await sendOTP(
        result.email,
        "Verify your account using the verification code",
        otp
      );

      return res.status(201).json({
        success: true,
        data: { message: "OTP sent to your email to verify your account" },
      });
    }
  } catch (error) {
    console.error("Error creating User:", error);

    return res
      .status(500)
      .json({ success: false, data: { error_msg: "Internal server error." } });
  }
};

const newRegistration = async (req, res) => {
  const { name, email, phone, password, countryCode, organization_url } = req.body;

  console.log("📥 ➡️ newRegistration called with data:", req.body);

  if (!name || !email || !phone || !password || !countryCode || !organization_url) {
    console.warn("⚠️ Missing required fields.");
    return res.status(400).json({
      success: false,
      data: { error_msg: "All fields are required." },
    });
  }

  try {
    // 🔍 Find organization DB name by subdomain
    const [orgData] = await mysqlServerConnection.query(
      `SELECT db_name FROM ${maindb}.organization WHERE auth_sub_domain = ?`,
      [organization_url]
    );

    if (!orgData || orgData.length === 0) {
      console.error("❌ Organization not found for:", organization_url);
      return res.status(404).json({
        success: false,
        message: "Organization not found."
      });
    }

    const dbName = orgData[0].db_name;
    console.log("✅ Organization DB resolved:", dbName);

    // 🔍 Check if user already exists
    const [existingUser] = await mysqlServerConnection.query(
      `SELECT id FROM ${dbName}.users WHERE email = ? OR mobile = ?`,
      [email, phone]
    );

    if (existingUser.length > 0) {
      console.warn("⚠️ User already exists:", existingUser[0]);
      return res.status(409).json({
        success: false,
        message: "This email address or phone number is already registered."
      });
    }

    // 🔐 Hash the password
    const hashedPassword = await bcrypt.hash(password, 10);

    // 🔑 Generate OTP
    const otp = Math.floor(100000 + Math.random() * 900000).toString();
    const otpExpiry = new Date(Date.now() + 10 * 60 * 1000); // 10 min

    // 📝 Insert new user
    await mysqlServerConnection.query(
      `INSERT INTO ${dbName}.users (
        name, email, password, mobile, mobileno_code,
        is_verified, is_email_verified, is_phone_verified, is_blocked, is_deleted,
        otp_code, otp_expiry, db_name, createdAt
      ) VALUES (?, ?, ?, ?, ?, 0, 0, 0, 0, 0, ?, ?, ?, NOW())`,
      [name, email, hashedPassword, phone, countryCode, otp, otpExpiry, dbName]
    );

    await mysqlServerConnection.query(
      `INSERT INTO ${dbName}.user_roles (user_id, role_id) VALUES (LAST_INSERT_ID(), ?)`,
      [req.body.role_id || 2] // Default to role_id 2 if not provided
    );
    

    // ✉️ Send OTP via email
    await registerOTP(email, 'Email Verification Code', otp);

    console.log("✅ User registered successfully and OTP sent.");

    return res.status(201).json({
      success: true,
      data: {
        message: "User registered successfully. OTP sent.",
        email: email
      }
    });

  } catch (error) {
    console.error("🚨 Registration error:", error);
    return res.status(500).json({
      success: false,
      data: { error_msg: "Internal server error." }
    });
  }
};



const organizationRoles = async (req, res) => {
  try {
    const result = req.body;

    if (Object.keys(result).length == 0 || !Array.isArray(result.permission)) {
      return res.status(422).json({
        success: false,

        data: {
          error_msg: "Following fields are required",

          response: {
            name: "",

            description: "",

            permission: [],
          },
        },
      });
    } else {
      const [roles_result] = await mysqlServerConnection.query(
        `INSERT INTO ${req.user.db_name}.roles (name, description,created_by) VALUES (?,?,?)`,

        [result.name, result.description, req.user.userId]
      );

      const roleId = roles_result.insertId;

      const permissionValues = result.permission.map((permissionId) => [
        roleId,
        permissionId,
      ]);

      await mysqlServerConnection.query(
        `INSERT INTO ${req.user.db_name}.role_permissions (role_id, permission_id) VALUES ?`,
        [permissionValues]
      );

      console.log("Role created successfully.");

      res.status(201).json({
        success: true,
        data: {
          message: "Roles created successfully.",

          response: {
            id: roleId,
            name: result.name,
            description: result.description,
            permissions: result.permission,
          },
        },
      });
    }
  } catch (error) {
    console.error("Error creating organization:", error);

    res
      .status(500)
      .json({ success: false, data: { error_msg: "Internal server error." } });
  }
};

const showOrganizationRoles = async (req, res) => {
  try {
    // Query to get roles with their associated permissions

    const user = req.user;

    const query = `

            SELECT

                r.id as role_id,

                r.name as role_name,

                r.description as role_description,

                rp.permission_id,

                p.name as permission_name

            FROM

                ${user.db_name}.roles r

            LEFT JOIN

                ${user.db_name}.role_permissions rp ON r.id = rp.role_id

            LEFT JOIN

                ${maindb}.permissions p ON rp.permission_id = p.id

        `;

    const [rows] = await mysqlServerConnection.query(query);

    const roles = rows.reduce((acc, row) => {
      let role = acc.find((r) => r.id === row.role_id);

      if (!role) {
        role = {
          id: row.role_id,

          name: row.role_name,

          description: row.role_description,

          permissions: [],
        };

        acc.push(role);
      }

      if (row.permission_id) {
        role.permissions.push({
          id: row.permission_id,

          name: row.permission_name,

          description: row.permission_description,
        });
      }

      return acc;
    }, []);

    res.status(200).json({
      success: true,

      data: {
        message: "",

        response: roles,
      },
    });
  } catch (error) {
    console.error("Error showing roles:", error);

    res
      .status(500)
      .json({ success: false, data: { error_msg: "Internal server error." } });
  }
};

const editOrganizationRoles = async (req, res) => {
  try {
    const user = req.user;

    const result = req.body;

    const [Setrole] = await mysqlServerConnection.query(
      `UPDATE ${user.db_name}.roles SET name = ?, description = ? WHERE id = ?`,
      [result.name, result.description, result.id]
    );

    if (Setrole.affectedRows === 0) {
      return res
        .status(404)
        .json({ success: false, data: { error_msg: "Role not found." } });
    }

    // Retrieve the updated role

    const [updatedRole] = await mysqlServerConnection.query(
      `SELECT id, name, description FROM ${user.db_name}.roles WHERE id = ?`,
      [result.id]
    );

    const roleId = updatedRole[0].id;

    const [existingPermissions] = await mysqlServerConnection.query(
      `SELECT permission_id FROM ${mysqlServerConnection.escapeId(
        user.db_name
      )}.role_permissions WHERE role_id = ?`,

      [roleId]
    );

    const existingPermissionIds = existingPermissions.map(
      (p) => p.permission_id
    );

    // Filter permissions that are not already added

    const permissionsToAdd = result.permission.filter(
      (p) => !existingPermissionIds.includes(p)
    );

    const permissionsToRemove = existingPermissionIds.filter(
      (p) => !result.permission.includes(p)
    );

    // Add new permissions only

    if (permissionsToAdd.length > 0) {
      const permissionValues = permissionsToAdd.map((permissionId) => [
        roleId,
        permissionId,
      ]);

      await mysqlServerConnection.query(
        `INSERT INTO ${mysqlServerConnection.escapeId(
          user.db_name
        )}.role_permissions (role_id, permission_id) VALUES ?`,

        [permissionValues]
      );
    }

    if (permissionsToRemove.length > 0) {
      await mysqlServerConnection.query(
        `DELETE FROM ${mysqlServerConnection.escapeId(
          user.db_name
        )}.role_permissions WHERE role_id = ? AND permission_id IN (?)`,

        [roleId, permissionsToRemove]
      );
    }

    res.status(200).json({
      success: true,

      data: {
        message: "Role updated successfully.",

        response: updatedRole[0],
      },
    });
  } catch (error) {
    console.error("Error:", error);

    res
      .status(500)
      .json({ success: false, data: { error_msg: "Internal server error." } });
  }
};

const showOrganizationPermissions = async (req, res) => {
  try {
    const [permissions] = await mysqlServerConnection.query(
      `SELECT * FROM ${maindb}.permissions`
    );

    res.status(200).json({
      success: true,
      data: {
        message: "",

        permisson: permissions,
      },
    });
  } catch (error) {
    console.error("Error:", error);

    res
      .status(500)
      .json({ success: false, data: { error_msg: "Internal server error." } });
  }
};

const deleteOrganizationRole = async (req, res) => {
  try {
    const result = req.body;

    const user = req.user;

    if (Object.keys(result).length == 0) {
      return res.status(422).json({
        success: false,

        data: {
          error_msg: "Role ID is required",

          response: { role_id: [] },
        },
      });
    }

    const roleIds = result.role_id.join(",");

    // Delete role permissions

    await mysqlServerConnection.query(
      `DELETE FROM ${user.db_name}.role_permissions WHERE role_id IN (${roleIds})`
    );

    // Delete the roles

    const [roles_result] = await mysqlServerConnection.query(
      `DELETE FROM ${user.db_name}.roles WHERE id IN (${roleIds})`
    );

    if (roles_result.affectedRows === 0) {
      return res.status(404).json({
        success: false,

        data: {
          error_msg: "No roles found for the provided IDs",
        },
      });
    }

    res.status(200).json({
      success: true,

      data: {
        message: "Roles and their permissions deleted successfully.",
      },
    });
  } catch (error) {
    console.error("Error deleting role:", error);

    res
      .status(500)
      .json({ success: false, data: { error_msg: "Internal server error." } });
  }
};

const organizationLogoutUser = async (req, res) => {
  console.log("logout API triggered");

  try {
    const authHeader = req.headers.authorization;

    if (!authHeader) {
      return res.status(401).json({
        success: false,

        data: { error_msg: "Authorization token is missing" },
      });
    }

    // Verify token to extract the expiry date

    const decodedToken = jwt.verify(authHeader, process.env.JWT_ACCESS_KEY);

    const expiryDate = new Date(decodedToken.exp * 1000); // Convert expiry to milliseconds

    // Log the logout action (optional)

    // if (req.user && req.user.userId) {

    //     await mysqlServerConnection.query(`

    //         INSERT INTO ${req.user.db_name}.user_logs (user_id, log_name, log_description)

    //         VALUES (?, ?, ?)

    //     `, [req.user.userId, "logout", `${req.user.username} logged out`]);

    // }

    LogsHandler(
      req.user.db_name,
      req.user.userId,
      "Log Out",
      `${req.user.username} is Loged Out`
    );

    // Add the access token to the blacklist

    await mysqlServerConnection.query(
      `

            INSERT INTO ${req.user.db_name}.token_blacklist (token, expires_at)

            VALUES (?, ?)

        `,
      [authHeader, expiryDate]
    );

    // Clear the token and response headers

    req.user = null;

    res.setHeader("Authorization", "");

    console.log("User logout successful");

    return res.status(200).json({
      success: true,

      data: {
        message: "Logout Successful.",
      },
    });
  } catch (error) {
    console.error("Error during logout:", error);

    res.status(500).json({
      success: false,

      data: { error_msg: "Internal server error during logout." },
    });
  }
};

const addNewOrganization = async (req, res) => {
  try {
    const result = req.body;

    if (Object.keys(result).length == 0) {
      return res.status(422).json({
        success: false,
        data: {
          error_msg: "Following fields are required",
          response: {
            name: "",
            db_name: "",
            org_image: "",
            country: "",
            state: "",
            zipcode: "",
            office_address: "",
            authorized_person: "",
            authorized_person_identity: "",
            authorized_person_phone: "",
            authorized_person_email: "",
            require_doc_url: "",
            subscription_plan: "",
            is_verified: "",
          },
        },
      });
    } else {
      // Get a dedicated connection for transaction
      const connection = await mysqlServerConnection.getConnection();

      try {
        // Start transaction
        await connection.query("START TRANSACTION");

        const [duplicateCheck] = await connection.query(
          `SELECT * FROM ${maindb}.organization WHERE authorized_person_identity = ? OR authorized_person_email = ? OR authorized_person_phone = ? `,
          [
            result.authorized_person_identity,
            result.authorized_person_email,
            result.authorized_person_phone,
          ]
        );

        if (duplicateCheck.length > 0) {
          let duplicateField = "";

          if (
            duplicateCheck.some(
              (org) =>
                org.authorized_person_identity ===
                result.authorized_person_identity
            )
          ) {
            duplicateField = "authorized_person_identity";
          } else if (
            duplicateCheck.some(
              (org) =>
                org.authorized_person_email === result.authorized_person_email
            )
          ) {
            duplicateField = "authorized_person_email";
          } else if (
            duplicateCheck.some(
              (org) =>
                org.authorized_person_phone === result.authorized_person_phone
            )
          ) {
            duplicateField = "authorized_person_phone";
          }

          // Release connection before returning response
          connection.release();

          return res.status(409).json({
            success: false,
            data: {
              error_msg: `${duplicateField} already exist`,
            },
          });
        }

        // Insert organization record
        const [organization] = await connection.query(
          `INSERT INTO ${maindb}.organization (name,db_name,org_image,country,state,zipcode,office_address,authorized_person,authorized_person_identity,authorized_person_phone,authorized_person_email,require_doc_url,subscription_plan,is_verified) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?)`,
          [
            result.name,
            result.db_name,
            result.org_image,
            result.country,
            result.state,
            result.zipcode,
            result.office_address,
            result.authorized_person,
            result.authorized_person_identity,
            result.authorized_person_phone,
            result.authorized_person_email,
            result.require_doc_url,
            result.subscription_plan,
            result.is_verified,
          ]
        );

        try {
          // Create organization database and setup
          await createOrganization(result.db_name);

          // If everything is successful, commit the transaction
          await connection.query("COMMIT");
          console.log(`Organization ${result.db_name} created successfully`);

          res.status(201).json({
            success: true,
            data: {
              message: "Organization created successfully",
              response: result,
            },
          });
        } catch (dbError) {
          // If database creation fails, rollback the transaction
          await connection.query("ROLLBACK");
          console.error("Error creating organization database:", dbError);

          res.status(500).json({
            success: false,
            data: {
              error_msg: "Failed to create organization database",
              details: dbError.message,
            },
          });
        }

        // Release the connection
        connection.release();
      } catch (error) {
        // Handle any other errors and ensure connection is released
        try {
          await connection.query("ROLLBACK");
        } catch (rollbackError) {
          console.error("Error during rollback:", rollbackError);
        }

        connection.release();
        throw error; // Re-throw to be caught by outer catch block
      }
    }
  } catch (error) {
    console.error("Error adding organization:", error);
    res.status(500).json({
      success: false,
      data: {
        error_msg: "Internal server error",
        details: error.message,
      },
    });
  }
};

const forgotPassword = async (req, res) => {
  try {
    let { email, domain } = req.body;

    email = email?.trim().toLowerCase();
    domain = domain?.trim().toLowerCase();

    console.log("📧 Email:", email);
    console.log("🌐 Domain:", domain);

    // Step 1: Validate inputs
    if (!email) {
      return res
        .status(400)
        .json({ success: false, status: 404, message: "Email is required." });
    }

    if (!domain) {
      return res
        .status(400)
        .json({ success: false, status: 404, message: "Domain is required." });
    }

    console.log("✅ Email and domain received");

    // Step 2: Get organization DB from main database
    console.log("🔍 Looking up organization by domain...");
    const [dbmain] = await mysqlServerConnection.query(
      `SELECT db_name, org_logo_url, org_favicon_url, name 
         FROM ${maindb}.organization 
         WHERE auth_sub_domain = ?`,
      [domain]
    );

    if (dbmain.length === 0) {
      console.log("❌ Organization not found");
      return res
        .status(404)
        .json({ success: false, status: 404, message: "Invalid domain." });
    }

    const orgdb = dbmain[0];
    const orgDbName = orgdb.db_name;

    console.log("✅ Organization found:", orgdb.name);
    console.log("✅ Organization database name:", orgDbName);

    // Step 3: Check if user exists in organization DB
    console.log("🔍 Looking for user in org DB...");
    const [userResult] = await mysqlServerConnection.query(
      `SELECT * FROM ${orgDbName}.users WHERE email = ?`,
      [email]
    );

    if (userResult.length === 0) {
      console.log("❌ User not found in organization DB");
      return res.status(404).json({
        success: false,
        status: 404,
        message: "Email not registered.",
      });
    }

    const user = userResult[0];
    console.log("✅ User found:", user.email);

    // Step 4: Check user status
    if (user.is_deleted) {
      console.log("⛔ User account is deleted");
      return res.status(403).json({
        success: false,
        status: 404,
        message: "Your account has been deleted. Please contact support.",
      });
    }

    if (user.is_blocked) {
      console.log("⛔ User account is blocked");
      return res.status(403).json({
        success: false,
        status: 404,
        message: "Your account is blocked. Please contact your administrator.",
      });
    }

    if (!user.is_email_verified) {
      console.log("⛔ User email is not verified");
      return res.status(403).json({
        success: false,
        status: 404,
        message: "Please activate your email to continue.",
      });
    }

    console.log("✅ User is valid and eligible for password reset");

    // Step 5: Generate unique OTP
    console.log("🔐 Generating unique OTP...");

    let otpCode;
    let isUnique = false;

    while (!isUnique) {
      otpCode = Math.floor(100000 + Math.random() * 900000).toString();

      const [existingOtp] = await mysqlServerConnection.query(
        `SELECT id FROM ${orgDbName}.users WHERE otp_code = ? AND otp_expiry > NOW()`,
        [otpCode]
      );

      if (existingOtp.length === 0) {
        isUnique = true;
      }
    }

    const otpExpiry = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes
    console.log("✅ OTP generated:", otpCode);
    console.log("⏳ OTP valid till:", otpExpiry.toISOString());

    // Step 6: Update user's OTP in DB
    await mysqlServerConnection.query(
      `UPDATE ${orgDbName}.users SET otp_code = ?, otp_expiry = ? WHERE id = ?`,
      [otpCode, otpExpiry, user.id]
    );
    console.log("✅ OTP and expiry updated in DB");

    // Step 7: Send OTP via email
    await forgotPasswordOTP(email, "Password Reset Verification Code", otpCode);
    console.log("📨 OTP email sent to:", email);

    // Step 8: Log the action
    LogsHandler(
      orgDbName,
      user.id,
      "Password Reset OTP Sent",
      `OTP sent to user with email ${email}`
    );
    console.log("🗒️ Action logged");

    // Step 9: Final response
    return res.status(200).json({
      success: true,
      data: {
        message: "OTP sent successfully.",
        response: {
          email,
          otp: otpCode, // ⚠️ Remove this in production
          otp_expiry: otpExpiry,
        },
      },
    });
  } catch (error) {
    console.error("❌ Error in forgotPassword:", error);
    return res.status(500).json({
      success: false,
      message: "Internal server error.",
    });
  }
};

// const verifyRegisterOTP = async (req, res) => {
//     let connection;
//     try {
//         const { email, organization_url, domain, otp } = req.body;
//         console.log('🟡 Verify OTP Request Body:', req.body);

//         // Input validation
//         if (!email || !organization_url || !domain || !otp) {
//             return res.status(400).json({
//                 success: false,
//                 data: { error_msg: 'Email, Organization URL, Domain, and OTP are required.' }
//             });
//         }

//         if (!email || !organization_url || !domain) {
//             return res.status(400).json({ success: false, data: { error_msg: 'Email, Organization URL and Domain are required.' } });
//         }

//         // Get the organization's DB
//         const [dbmain] = await mysqlServerConnection.query(
//             `SELECT db_name, id FROM ${maindb}.organization WHERE auth_sub_domain = ?`,
//             [organization_url]
//         );

//         if (dbmain.length === 0) {
//             return res.status(404).json({ success: false, data: { error_msg: 'Organization not found.' } });
//         }

//         const orgDb = dbmain[0].db_name;

//         console.log('🟢 Org Data Result:', orgData);

//         if (!orgData || orgData.length === 0) {
//             await connection.rollback();
//             return res.status(404).json({
//                 success: false,
//                 data: { error_msg: 'Organization not found. Check subdomain or DB config.' }
//             });
//         }

//         const orgId = orgData[0].id;

//         // Find user with matching email and valid OTP
//         const [userResult] = await connection.query(
//             `SELECT id, email
//              FROM ${orgDb}.users
//              WHERE email = ?
//              AND otp_code = ?
//              AND otp_expiry > NOW()
//              AND is_verified = 1
//              LIMIT 1`,
//             [email, otp]
//         );

//         console.log('🟢 User Search Result:', userResult);

//         if (!userResult || userResult.length === 0) {
//             await connection.rollback();
//             return res.status(400).json({
//                 success: false,
//                 data: { error_msg: 'Invalid or expired OTP. Please request a new one.' }
//             });
//         }

//         const user = userResult[0];

//         // Clear OTP fields after successful verification
//         await connection.query(
//             `UPDATE ${orgDb}.users
//              SET otp_code = NULL,
//                  otp_expiry = NULL,
//                  updated_at = NOW()
//              WHERE id = ?`,
//             [user.id]
//         );

//         // Commit the transaction
//         await connection.commit();

//         // Log the successful verification
//         await LogsHandler(orgDb, user.id, 'OTP Verified', `OTP verified for ${email}`);

//         return res.status(200).json({
//             success: true,
//             data: {
//                 message: 'OTP verified successfully',
//                 response: {
//                     email,
//                     organization_id: orgId
//                 }
//             }
//         });

//     } catch (error) {
//         if (connection) await connection.rollback();
//         console.error('🔴 Error in verifyRegisterOTP:', error);

//         return res.status(500).json({
//             success: false,
//             data: {
//                 error_msg: error.code === 'ER_NO_SUCH_TABLE'
//                     ? 'Database configuration error. Please contact support.'
//                     : 'An unexpected error occurred. Please try again.'
//             }
//         });
//     } finally {
//         if (connection) {
//             try {
//                 await connection.release();
//             } catch (releaseError) {
//                 console.error('🔴 Error releasing DB connection:', releaseError);
//             }
//         }
//     }
// };

const verifyRegisterOTP = async (req, res) => {
  let connection;
  try {
    const { email, domain, otp } = req.body;
    console.log("main maindb name", maindb);
    console.log("🟡 Verify OTP Request Body:", req.body);

    // Step 1: Input validation
    if (!email || !domain || !otp) {
      return res.status(400).json({
        success: false,
        message: "Email, domain, and OTP are required.",
      });
    }

    // Step 2: DB connection and transaction
    connection = await mysqlServerConnection.getConnection();
    await connection.beginTransaction();

    // Step 3: Lookup organization DB by domain
    console.log("🔍 Looking up organization using domain...");
    const [orgRows] = await connection.query(
      `SELECT db_name, id FROM ${maindb}.organization WHERE auth_sub_domain = ?`,
      [domain]
    );

    if (orgRows.length === 0) {
      await connection.rollback();
      return res.status(404).json({
        success: false,
        data: { error_msg: "Organization not found." },
      });
    }

    const orgDb = orgRows[0].db_name;
    console.log("✅ Org DB Name:", orgDb);

    // Step 4: Lookup user by email + OTP
    const [userRows] = await connection.query(
      `SELECT id, email, otp_code, otp_expiry
         FROM ${orgDb}.users 
         WHERE email = ? AND otp_code = ?
         LIMIT 1`,
      [email, otp]
    );

    console.log("🟢 OTP Match Result:", userRows);

    if (!userRows || userRows.length === 0) {
      await connection.rollback();
      return res.status(400).json({
        success: false,
        message: "Invalid OTP or email.",
      });
    }

    const user = userRows[0];

    // Step 5: Check OTP expiry
    if (!user.otp_expiry || new Date(user.otp_expiry) < new Date()) {
      await connection.rollback();
      return res.status(400).json({
        success: false,
        message: "OTP has expired. Please request a new one.",
      });
    }

    // Step 6: Clear OTP fields after successful verification
    await connection.query(
      `UPDATE ${orgDb}.users 
         SET otp_code = NULL, 
             otp_expiry = NULL,
             updatedAt = NOW(),
             is_email_verified = 1,
             is_verified = 1
         WHERE id = ?`,
      [user.id]
    );

    // Step 7: Commit transaction
    await connection.commit();

    // Step 8: Log the action
    await LogsHandler(
      orgDb,
      user.id,
      "OTP Verified",
      `OTP verified for ${email}`
    );
    console.log("🗒️ OTP verification logged");

    // Step 9: Final response
    return res.status(200).json({
      success: true,
      data: {
        message: "OTP verified successfully",
        response: {
          email,
          domain,
        },
      },
    });
  } catch (error) {
    console.error("❌ Error in verifyRegisterOTP:", error);
    if (connection) await connection.rollback();
    return res.status(500).json({
      success: false,
      data: {
        error_msg:
          error.code === "ER_NO_SUCH_TABLE"
            ? "Database configuration error. Please contact support."
            : "An unexpected error occurred. Please try again.",
      },
    });
  } finally {
    if (connection) {
      try {
        await connection.release();
      } catch (releaseError) {
        console.error("Error releasing DB connection:", releaseError);
      }
    }
  }
};

const resetPassword = async (req, res) => {
  try {
    const { newPassword, email, domain } = req.body;

    if (!newPassword) {
      return res.status(400).json({
        success: false,
        data: { message: "New password is required." },
      });
    }

    if (!email) {
      return res
        .status(400)
        .json({ success: false, data: { message: "Email is required." } });
    }

    if (!domain) {
      return res
        .status(400)
        .json({ success: false, data: { message: "Domain is required." } });
    }

    // Get the database name from maindb using organization_url
    const [dbmain] = await mysqlServerConnection.query(
      `SELECT db_name FROM ${maindb}.organization WHERE auth_sub_domain = ?`,
      [domain]
    );

    const orgDb = dbmain[0].db_name;

    console.log("Org DB Name:", orgDb);

    if (dbmain.length === 0) {
      return res
        .status(404)
        .json({ success: false, data: { message: "Organization not found." } });
    }

    // Find the user by email
    const [user] = await mysqlServerConnection.query(
      `SELECT * FROM ${orgDb}.users WHERE email = ?`,
      [email]
    );

    console.log("-----------User found:", user);

    const [is_email_verifie] = await mysqlServerConnection.query(
      `SELECT * FROM ${orgDb}.users WHERE email = ? AND is_email_verified = 1`,
      [email]
    );

    if (is_email_verifie.length === 0) {
      return res.status(404).json({
        success: false,
        data: { message: "Please verify your email to continue." },
      });
    }

    const userId = user[0].id;

    // Update the user's password
    const hashedPassword = await bcrypt.hash(newPassword, 10);
    await mysqlServerConnection.query(
      `UPDATE ${orgDb}.users SET password = ? WHERE id = ?`,
      [hashedPassword, userId]
    );

    // Log the password reset action
    LogsHandler(
      dbmain[0].db_name,
      userId,
      "Password Reset",
      `Password reset for user with email ${email}`
    );

    return res.status(200).json({
      success: true,
      data: {
        message: "Password successfully updated.",
      },
    });
  } catch (error) {
    console.error("Error during password reset:", error);
    return res
      .status(500)
      .json({ success: false, data: { error_msg: "Internal server error." } });
  }
};

const sendLinkforlogin = async (req, res) => {
  try {
    const result = req.body;

    if (Object.keys(result).length == 0) {
      return res.status(422).json({
        success: false,

        data: {
          error_msg: "Following fields are required",

          response: {
            email: "",

            organization_id: "",
          },
        },
      });
    } else {
      const [dbmain] = await mysqlServerConnection.query(
        `SELECT db_name FROM ${maindb}.organization WHERE id = ?`,
        [result.organization_id]
      );

      const [duplicateCheck] = await mysqlServerConnection.query(
        `SELECT * FROM ${dbmain[0].db_name}.users WHERE email = ? `,

        [result.email]
      );

      console.log(duplicateCheck);

      if (duplicateCheck.length == 0) {
        return res.status(409).json({
          success: false,

          data: {
            error_msg: `Email not register`,
          },
        });
      }

      const [rows] = await mysqlServerConnection.query(
        `SELECT users.*, roles.name AS role, roles.id AS role_id

                 FROM ${dbmain[0].db_name}.users

                 JOIN ${dbmain[0].db_name}.user_roles ON users.id = user_roles.user_id

                 JOIN ${dbmain[0].db_name}.roles ON user_roles.role_id = roles.id

                 WHERE users.email = ? AND users.is_blocked = 0 AND users.is_verified = 1`,

        [result.email]
      );

      const accessToken = await generateAccessToken({
        id: duplicateCheck.id,
        db_name: dbmain[0].db_name,
        username: duplicateCheck.email,
        role: rows[0].role,
        role_id: rows[0].role_id,
      });

      magicLink = `${result.url}/login?token=${accessToken}&role=${rows[0].role}`;

      (subject = "Your Magic Link to Access Your Account"), // Subject line
        sendMagicLink(result.email, subject, magicLink);

      res.status(201).json({
        success: true,
        data: {
          message: "Email send successfully",

          response: result,
        },
      });
    }
  } catch (error) {
    console.error("Error:", error);

    res
      .status(500)
      .json({ success: false, data: { error_msg: "Internal server error." } });
  }
};

const sendotp = async (req, res) => {
  console.log("From sendotp:----------------------------");
  const result = req.body;
  console.log("From sendotp:", result);

  const organization_url = result.organization_url;


  try {
    // ✅ Extract subdomain from organization_url


    // ✅ Get the organization DB name using the subdomain
    const [dbmain] = await mysqlServerConnection.query(
      `SELECT db_name FROM ${maindb}.organization WHERE auth_sub_domain = ?`,
      [organization_url]
    );

    if (dbmain.length === 0) {
      return res.status(404).json({
        success: false,
        data: { error_msg: "Organization not found." }
      });
    }

    const dbName = dbmain[0].db_name;

    // ✅ Check if email exists in the org's users table
    const [duplicateCheck] = await mysqlServerConnection.query(
      `SELECT * FROM ${dbName}.users WHERE email = ?`,
      [result.email]
    );

    if (duplicateCheck.length === 0) {
      return res.status(409).json({
        success: false,
        data: { error_msg: "Email not registered" }
      });
    }

    // ✅ Generate OTP
    const otp = Math.floor(100000 + Math.random() * 900000).toString();
    const otpExpiry = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes

    // ✅ Update user's OTP and expiry in DB
    await mysqlServerConnection.query(
      `UPDATE ${dbName}.users SET otp_code = ?, otp_expiry = ? WHERE email = ?`,
      [otp, otpExpiry, result.email]
    );

    // ✅ Send the OTP email
    await sendOTP(
      result.email,
      "Verify your account using the verification code",
      otp
    );

    return res.status(201).json({
      success: true,
      data: {
        message: "OTP sent to your email to verify your account"
      }
    });

  } catch (error) {
    console.error("Error in sendotp:", error);
    return res.status(500).json({
      success: false,
      data: { error_msg: "Internal server error." }
    });
  }
};


const verifyOtp = async (req, res) => {
  const { email, otp, organization_url } = req.body;

  console.log("➡️ Verify OTP Request Body:", req.body);

  if (!email || !otp || !organization_url) {
    return res.status(400).json({
      success: false,
      data: { error_msg: "Email, OTP, and Organization URL are required." },
    });
  }

  try {
    // 1. Resolve organization and database
    const [orgData] = await mysqlServerConnection.query(
      `SELECT db_name, org_logo_url, org_favicon_url, name 
       FROM ${maindb}.organization 
       WHERE auth_sub_domain = ?`,
      [organization_url]
    );

    if (!orgData || orgData.length === 0) {
      return res.status(404).json({
        success: false,
        data: { error_msg: "Organization not found." },
      });
    }

    const dbName = orgData[0].db_name;
    console.log("✅ Resolved DB:", dbName);

    // 2. Get user by email and check OTP
    const [userResult] = await mysqlServerConnection.query(
      `SELECT id, name, email, otp_code, otp_expiry, fcm_token 
       FROM ${dbName}.users 
       WHERE email = ?`,
      [email]
    );

    if (!userResult || userResult.length === 0) {
      return res.status(404).json({
        success: false,
        data: { error_msg: "User not found." },
      });
    }

    const user = userResult[0];

    console.log("🔑 Stored OTP:", user.otp_code);
    console.log("🧾 Input OTP:", otp);
    console.log("⌛ OTP Expiry:", user.otp_expiry);

    // 3. Validate OTP
    if (user.otp_code !== otp) {
      return res.status(400).json({
        success: false,
        data: { error_msg: "Invalid OTP." },
      });
    }

    if (new Date() > new Date(user.otp_expiry)) {
      return res.status(400).json({
        success: false,
        data: { error_msg: "OTP expired." },
      });
    }

    // 4. OTP is valid – update verification
    await mysqlServerConnection.query(
      `UPDATE ${dbName}.users 
       SET is_verified = 1, otp_code = NULL, otp_expiry = NULL 
       WHERE email = ?`,
      [email]
    );

    // 5. Send notification
    const title = "Welcome to LMS!";
    const dec = `We are thrilled to have you join our learning community...`;

    await SetNotification({
      dbName,
      userId: user.id,
      sendFrom: null,
      title,
      body: dec,
    });

    await SendPushNotificaiton(user.fcm_token, title, dec);
    // await sentEmailNotification(user.email, title, title, dec, user.name);

    console.log("✅ OTP verified and notification sent");

    res.status(200).json({
      success: true,
      data: { message: "OTP verified successfully." },
    });
  } catch (error) {
    console.error("🚨 Error during OTP verification:", error);
    res.status(500).json({
      success: false,
      data: { error_msg: "Internal server error." },
    });
  }
};





// const verifyOtp = async (req, res) => {
//   const { email, otp, organization_id } = req.body;

//   console.log("Verify OTP Request Body:", req.body);

//   if (!email || !otp) {
//     return res.status(400).json({
//       success: false,
//       data: { error_msg: "Email and OTP are required." },
//     });
//   }

//   try {
//     const [dbmain] = await mysqlServerConnection.query(
//       `SELECT db_name FROM ${maindb}.organization WHERE id = ?`,
//       [organization_id]
//     );

//     // Retrieve the OTP from the database

//     console.log(otp);

//     const [results] = await mysqlServerConnection.query(
//       `SELECT otp, otp_expire_time FROM ${dbmain[0].db_name}.otps WHERE email = ? AND otp = ?`,

//       [email, otp]
//     );

//     if (results.length === 0) {
//       return res.status(404).json({
//         success: false,
//         data: { error_msg: "No OTP request found for this email." },
//       });
//     }

//     console.log(results[0].otp, otp);

//     if (results[0].otp !== otp) {
//       return res
//         .status(400)
//         .json({ success: false, data: { error_msg: "Invalid OTP." } });
//     }

//     if (new Date() > new Date(results[0].expiry)) {
//       return res
//         .status(400)
//         .json({ success: false, data: { error_msg: "OTP expired." } });
//     }

//     // OTP is valid

//     // Optionally: Clear the OTP from the database

//     await mysqlServerConnection.query(
//       `DELETE FROM ${dbmain[0].db_name}.otps WHERE email = ?`,
//       [email]
//     );

//     await mysqlServerConnection.query(
//       `UPDATE ${dbmain[0].db_name}.users SET is_verified = ? WHERE email = ?`,
//       [true, email]
//     );

//     const [user] = await mysqlServerConnection.query(
//       `SELECT * from ${dbmain[0].db_name}.users WHERE email = ?`,
//       [email]
//     );

//     console.log(user, "+++++++++++++++===");

//     let dec =
//       "We are thrilled to have you join our learning community, where education meets innovation. LMS is designed to empower learners like you by providing personalized learning experiences, interactive features, and the flexibility to learn at your own pace. Whether you're looking to gain new skills, achieve professional goals, or simply explore new areas of interest, our platform offers a wide range of resources to support your journey. With engaging content, progress tracking, and anytime, anywhere access, LMS ensures that learning is not only effective but also enjoyable. Let's embark on this journey of growth and discovery together!";

//     let title = "Welcome to LMS!";

//     await SetNotification({
//       dbName: dbmain[0].db_name,
//       userId: user[0].id,
//       sendFrom: null,
//       title: title,
//       body: dec,
//     });

//     await SendPushNotificaiton(user[0].fcm_token, title, dec);

//     sentEmailNotification(user[0].email, title, title, dec, user[0].name);

//     res
//       .status(200)
//       .json({ success: true, data: { message: "OTP verified successfully." } });
//   } catch (error) {
    
//     console.error("Error during OTP verification:", error);

//     res
//       .status(500)
//       .json({ success: false, data: { error_msg: "Internal server error." } });
//   }
// };

// Update the Profile
const updateUserProfile = async (req, res) => {
  try {
    const {
      profile_pic_url,
      name,
      mobileno_code,
      mobile,
      bio,
      date_of_birth,
      gender,
      country,
      state,
      zipcode,
      address,
      language,
      profession,
      qualification,
      experience,
      is_profile_pic_changed,
    } = req.body;

    console.log("-----------------------------", req.body);

    // const file = req.file;
    // let pathNew = null;

    // if (is_profile_pic_changed == true) {
    // const filedata = await uploadImageToS3(file.buffer);
    // pathNew = filedata.path;
    // } else {
    pathNew = profile_pic_url;
    // }

    if (!req.user || !req.user.db_name) {
      return res.status(400).json({
        success: false,
        data: { error_msg: "User database information is missing." },
      });
    }

    const dbName = req.user.db_name;

    await mysqlServerConnection.query(
      `UPDATE ${dbName}.users 
            SET 
                profile_pic_url = ?, 
                name = ?, 
                mobileno_code = ?, 
                mobile = ?, 
                bio = ?, 
                date_of_birth = ?, 
                gender = ?, 
                country = ?, 
                state = ?, 
                zipcode = ?, 
                address = ?, 
                language = ?, 
                profession = ?, 
                qualification = ?, 
                experience = ?
             WHERE id = ?`,
      [
        pathNew,
        name,
        mobileno_code,
        mobile,
        bio,
        date_of_birth ? date_of_birth.split("T")[0] : null, // 👈 FIXED HERE
        gender,
        country,
        state,
        zipcode,
        address,
        language,
        profession,
        qualification,
        experience,
        req.user.userId,
      ]
    );

    return res.status(200).json({
      success: true,
      data: { message: "Profile updated successfully." },
    });
  } catch (error) {
    console.error("Error updating profile:", error);
    return res.status(500).json({
      success: false,
      data: { error_msg: "Internal server error." },
    });
  }
};

// Get the profile data
const getProfile = async (req, res) => {
  try {
    const dbName = req.user.db_name;
    const userId = req.user.userId;

    const [user] = await mysqlServerConnection.query(
      `SELECT * FROM ${dbName}.users WHERE id = ?`,
      [userId]
    );

    if (user.length === 0) {
      return res.status(404).json({
        success: false,
        data: {
          error_msg: "User not found",
        },
      });
    }

    const u = user[0];

    const profileData = {
      id: u.id,
      profile_pic_url: u.profile_pic_url,
      name: u.name,
      email: u.email,
      mobileno_code: u.mobileno_code,
      mobile: u.mobile,
      bio: u.bio,
      date_of_birth: u.date_of_birth,
      gender: u.gender,
      country: u.country,
      state: u.state,
      zipcode: u.zipcode,
      address: u.address,
      language: u.language,
      profession: u.profession,
      qualification: u.qualification,
      experience: u.experience,
      created_at: u.created_at,
    };

    // console.log("Profile Data:", profileData);

    return res.status(200).json({
      success: true,
      data: profileData,
    });
  } catch (error) {
    console.error("Error retrieving profile:", error);
    return res.status(500).json({
      success: false,
      data: {
        error_msg: "Internal server error.",
      },
    });
  }
};

const getProfileComments = async (req, res) => {
  console.log("🔍 Fetching user profile comments...");
  try {
    const dbName = req.user.db_name;
    const { user_id } = req.params;
    console.log("🔍 Request received to fetch comments for user_id:", user_id);
    console.log("🔍 Database Name:--------------------------------", dbName);

    if (!user_id) {
      console.warn("⚠️ Missing user_id in params");
      return res.status(400).json({
        success: false,
        data: { error_msg: "User ID is required in params." },
      });
    }

    const [user] = await mysqlServerConnection.query(
      `SELECT id, name, profile_pic_url FROM ${dbName}.users WHERE id = ?`,
      [user_id]
    );

    console.log("📦 Query result for user:", user);

    if (user.length === 0) {
      console.warn("❌ No user found with ID:", user_id);
      return res.status(404).json({
        success: false,
        data: { error_msg: "User not found." },
      });
    }

    const profileData = {
      id: user[0].id,
      name: user[0].name,
      profile_pic: user[0].profile_pic_url,
    };

    console.log("✅ Final response profile data:", profileData);

    return res.status(200).json({
      success: true,
      data: profileData,
    });
  } catch (error) {
    console.error("💥 Error retrieving user profile:", error);
    return res.status(500).json({
      success: false,
      data: { error_msg: "Internal server error." },
    });
  }
};

// Get Certificate Data by User ID or Certificate ID
const getCertificateData = async (req, res) => {
  try {
    const dbName = req.user.db_name;

    const userId = req.user.userId;

    // console.log("USER ID:", userId);

    // Construct the query

    let query = `

            SELECT 

                c.id, 

                c.certificate_name, 

                c.issued_date, 

                c.expiration_date, 

                c.certificate_url,

                u.name AS user_name, 

                u.email AS user_email,

                ct.template_name

            FROM ${dbName}.certificates c

            JOIN ${dbName}.users u ON c.user_id = u.id

            JOIN ${dbName}.certificate_templates ct ON c.template_id = ct.id

            WHERE c.is_deleted = 0

            AND c.user_id = ?

        `;

    const queryParams = [userId];

    const [certificates] = await mysqlServerConnection.query(
      query,
      queryParams
    );

    if (certificates.length === 0) {
      return res.status(200).json({
        success: true,

        data: [],
      });
    }

    // Format the certificate data for response

    const certificateData = certificates.map((cert) => ({
      id: cert.id,

      certificate_name: cert.certificate_name,

      issued_date: cert.issued_date,

      expiration_date: cert.expiration_date,

      certificate_url: cert.certificate_url,

      user: {
        name: cert.user_name,

        email: cert.user_email,
      },

      template: {
        name: cert.template_name,

        url: cert.template_url, // Now this will work correctly
      },
    }));

    return res.status(200).json({
      success: true,

      data: certificateData,
    });
  } catch (error) {
    console.error("Error retrieving certificate data:", error);

    return res.status(500).json({
      success: false,

      data: {
        error_msg: "Internal server error.",
      },
    });
  }
};

const updatePasswordAPI = async (req, res) => {
  try {
    console.log("Update Password API");

    const { currentPassword, newPassword } = req.body; // Extract current and new password from request body

    const userId = req.user.userId; // Get user ID from authenticated request (JWT token)

    const dbName = req.user.db_name; // Assuming DB name is attached to the user object

    console.log("Data from the database");

    if (!currentPassword || !newPassword) {
      return res.status(400).json({
        success: false,

        data: { error_msg: "Both current and new passwords are required." },
      });
    }

    console.log("CURRENT PASSWORD:", currentPassword);

    console.log("NEW PASSWORD:", newPassword);

    console.log("USER ID:", userId);

    console.log("DB NAME:", dbName);

    // Step 1: Query to fetch the hashed password from the database

    const selectQuery = `

            SELECT password

            FROM ${dbName}.users

            WHERE id = ?

        `;

    // Execute the query to get the user's password

    const [rows] = await mysqlServerConnection.query(selectQuery, [userId]);

    console.log("ROWS", rows);

    // Check if user data was found

    if (rows.length === 0) {
      return res.status(404).json({
        success: false,

        data: { error_msg: "No user found with the specified ID." },
      });
    }

    const userData = rows[0];

    const hashedPassword = userData.password; // This is the hashed password from the database

    // Step 2: Compare current password with hashed password in the database

    const passwordMatch = await bcrypt.compare(currentPassword, hashedPassword);

    if (!passwordMatch) {
      return res.status(200).json({
        success: false,

        data: { error_msg: "Current Password is incorrect." },
      });
    }

    // Step 3: If password matches, hash the new password

    const saltRounds = 10;

    const newHashedPassword = await bcrypt.hash(newPassword, saltRounds);

    // Step 4: Update the database with the new hashed password

    const updateQuery = `

            UPDATE ${dbName}.users

            SET password = ?

            WHERE id = ?

        `;

    await mysqlServerConnection.query(updateQuery, [newHashedPassword, userId]);

    // Return success response

    return res.status(200).json({
      success: true,

      data: { message: "Password updated successfully." },
    });
  } catch (error) {
    console.error("Error updating password:", error);

    return res.status(500).json({
      success: false,

      data: { error_msg: "Internal server error." },
    });
  }
};

const deleteUserAccount = async (req, res) => {
  const userId = req.user.userId;
  const dbName = req.user.db_name;

  const { action } = req.body;

  console.log("USER ID:", userId, dbName, "Action:", action);

  try {
    if (action === "deactivate") {
      // Check if the user already exists in deletion_table

      const checkUserQuery = `SELECT * FROM ${dbName}.deletion_table WHERE user_id = ?`;

      const [rows] = await mysqlServerConnection.execute(checkUserQuery, [
        userId,
      ]);

      if (rows.length > 0) {
        return res
          .status(400)
          .json({ success: false, error_msg: "Account already deactivated." });
      }

      // Insert new record into deletion_table with current timestamp

      const insertUserQuery = `

                INSERT INTO ${dbName}.deletion_table (user_id, execute_time)

                VALUES (?, CURRENT_TIMESTAMP)

            `;

      await mysqlServerConnection.execute(insertUserQuery, [userId]);

      // Create a MySQL event to delete the user after 24 hours

      const createEventQuery = `

                CREATE EVENT IF NOT EXISTS ${dbName}.delete_user_event_${userId}

                ON SCHEDULE AT CURRENT_TIMESTAMP + INTERVAL 24 HOUR

                DO

                BEGIN

                    DELETE FROM ${dbName}.users WHERE id = ${userId};

                    DELETE FROM ${dbName}.deletion_table WHERE user_id = ${userId};

                END;

            `;

      await mysqlServerConnection.query(createEventQuery); // Use query instead of execute

      console.log("create event", createEventQuery);

      return res.status(200).json({
        success: true,
        message:
          "Account deactivated successfully! Will be deleted after 24 hours.",
      });
    } else if (action === "activate") {
      // Delete the user from the deletion_table

      const deleteUserQuery = `DELETE FROM ${dbName}.deletion_table WHERE user_id = ?`;

      const [result] = await mysqlServerConnection.execute(deleteUserQuery, [
        userId,
      ]);

      if (result.affectedRows === 0) {
        return res.status(400).json({
          success: false,
          error_msg: "No deactivated account found for the user.",
        });
      }

      // Drop the event if the user is activated

      const dropEventQuery = `DROP EVENT IF EXISTS ${dbName}.delete_user_event_${userId}`;

      await mysqlServerConnection.query(dropEventQuery); // Use query instead of execute

      console.log("drop event", dropEventQuery);

      return res
        .status(200)
        .json({ success: true, message: "Account activated successfully!" });
    } else {
      return res
        .status(400)
        .json({ success: false, error_msg: "Invalid action provided." });
    }
  } catch (error) {
    console.error("Error handling account:", error);

    return res
      .status(500)
      .json({ success: false, error_msg: "Internal server error." });
  }
};

const softDeleteUserAccount = async (req, res) => {
  const userId = req.user.userId;
  const dbName = req.user.db_name;

  try {
    // Step 1: Check if user exists
    const checkUserQuery = `SELECT id FROM ${dbName}.users WHERE id = ? AND is_deleted = 0`;
    const [userRows] = await mysqlServerConnection.execute(checkUserQuery, [
      userId,
    ]);

    if (userRows.length === 0) {
      return res.status(404).json({
        success: false,
        error_msg: "User not found or already deleted.",
      });
    }

    // Step 2: Soft delete the user (set is_deleted = 1)
    const softDeleteQuery = `UPDATE ${dbName}.users SET is_deleted = 1, updatedAt = CURRENT_TIMESTAMP WHERE id = ?`;
    await mysqlServerConnection.execute(softDeleteQuery, [userId]);

    return res.status(200).json({
      success: true,
      message: "User account has been soft-deleted successfully.",
    });
  } catch (error) {
    console.error("Error during soft delete:", error);
    return res.status(500).json({
      success: false,
      error_msg: "Internal server error while soft deleting user.",
    });
  }
};

const addClassroomApproval = async (req, res) => {
  try {
    const { classroom_id } = req.body;
    const userId = req.user.userId;
    const dbName = req.user.db_name;

    console.log("Request:", req);
    console.log("Classroom ID:", classroom_id, "User ID:", userId);

    // Validate required fields
    if (!classroom_id) {
      return res.status(400).json({
        success: false,
        data: { error_msg: "Classroom ID is required." },
      });
    }

    // Query to check if the row with the same user_id and classroom_id already exists
    const checkQuery = `
            SELECT * FROM ${dbName}.classroom_approval
            WHERE user_id = ? AND classroom_id = ?
        `;

    const [existingRows] = await mysqlServerConnection.query(checkQuery, [
      userId,
      classroom_id,
    ]);

    if (existingRows.length > 0) {
      // If the row exists, update is_approval to NULL (or another value if needed)
      const updateQuery = `
                UPDATE ${dbName}.classroom_approval
                SET is_approval = NULL
                WHERE user_id = ? AND classroom_id = ?
            `;

      await mysqlServerConnection.query(updateQuery, [userId, classroom_id]);

      return res.status(200).json({
        success: true,
        data: {
          message: "Approval request updated. Waiting for approval.",
          approval: false,
        },
      });
    } else {
      // If no existing row, insert a new row into the classroom_approval table
      const insertQuery = `
                INSERT INTO ${dbName}.classroom_approval (user_id, classroom_id, is_approval)
                VALUES (?, ?, NULL)
            `;

      await mysqlServerConnection.query(insertQuery, [userId, classroom_id]);

      return res.status(200).json({
        success: true,
        data: {
          message: "Approval request submitted. Waiting for approval.",
          approval: false,
        },
      });
    }
  } catch (error) {
    console.error("Error adding/updating classroom approval:", error);
    return res.status(500).json({
      success: false,
      data: { error_msg: "Internal server error." },
    });
  }
};

const checkClassroomApprovalStatus = async (req, res) => {
  try {
    const { classroom_id } = req.query; // Get classroom_id from query parameters

    const userId = req.user.userId; // Get user ID from token

    const dbName = req.user.db_name; // Get database name from token

    // Query to check if a row exists for the user and classroom

    const checkQuery = `

            SELECT * FROM ${dbName}.classroom_approval

            WHERE user_id = ? AND classroom_id = ?

        `;

    const [rows] = await mysqlServerConnection.query(checkQuery, [
      userId,
      classroom_id,
    ]);

    // If no row exists, return success with no data

    if (rows.length === 0) {
      return res.status(200).json({
        success: true,

        data: { approval: null },
      });
    }

    // Return the approval status

    return res.status(200).json({
      success: true,

      data: { approval: rows[0] },
    });
  } catch (error) {
    console.error("Error checking classroom approval:", error);

    return res.status(500).json({
      success: false,

      data: { error_msg: "Internal server error." },
    });
  }
};

const updateProfilePic = async (req, res) => {
  try {
    console.log("🟡 Incoming request to update profile picture");
    console.log("📦 Request body:", req.body);
    console.log("📁 Request file:", req.file);

    // Log the file info if it exists
    if (req.file) {
      console.log("📋 File details:", {
        fieldname: req.file.fieldname,
        originalname: req.file.originalname,
        mimetype: req.file.mimetype,
        size: req.file.size,
      });
    } else {
      console.warn("⚠️ No file found in the request");
    }

    // Use req.user.userId from JWT token instead of user_id from body for security
    const userId = req.user.userId;
    const profileImage = req.file; // Changed from req.files?.profileImage

    console.log("➡️ User ID (from JWT):", userId);
    console.log("📸 Uploaded File:", profileImage);

    if (!userId || !profileImage) {
      console.warn("❗ Missing user authentication or profile image");
      return res.status(400).json({
        success: false,
        error_msg: "User authentication and profile image are required",
      });
    }

    // Upload image to S3
    console.log("🟢 Uploading image to S3...");
    const uploadResult = await uploadImageToS3(profileImage.buffer); // Changed to profileImage.buffer

    console.log("📤 S3 Upload Result:", uploadResult);

    if (!uploadResult.success) {
      console.error("❌ Failed to upload image to S3");
      return res.status(500).json({
        success: false,
        error_msg: "Failed to upload image to S3",
      });
    }

    const imageUrl = uploadResult.path;
    console.log("✅ Image URL:", imageUrl);

    // Update user's profile picture URL in database
    const query = `UPDATE ${req.user.db_name}.users SET profile_pic_url = ? WHERE id = ?`;
    const values = [imageUrl, userId]; // Use userId from JWT token

    console.log("🛠️ Running DB Query:", query, values);

    try {
      const [results] = await mysqlServerConnection.query(query, values);

      if (results.affectedRows === 0) {
        console.warn("⚠️ No user found with given ID");
        return res.status(404).json({
          success: false,
          error_msg: "User not found",
        });
      }

      console.log("✅ Profile picture updated in DB");
      res.json({
        success: true,
        message: "Profile picture updated successfully",
        data: {
          profile_pic_url: imageUrl,
        },
      });
    } catch (dbError) {
      console.error("🚨 DB Error:", dbError);
      return res.status(500).json({
        success: false,
        error_msg: "Database error while updating profile picture",
      });
    }
  } catch (error) {
    console.error("🔥 Internal Server Error:", error);
    res.status(500).json({
      success: false,
      error_msg: "Internal server error",
    });
  }
};

// Function: changePassword ---------
const changePassword = async (req, res) => {
  console.log("🔐 ➡️ changePassword called");

  try {
    const userId = req.user.userId;
    const dbName = req.user.db_name;
    const { currentPassword, newPassword } = req.body;

    console.log("📥 Request Data:", req.body);

    // Check if current and new passwords are provided
    if (!currentPassword || !newPassword) {
      console.log("❌ Missing passwords");
      return res
        .status(400)
        .json({
          success: false,
          message: "Current and new password are required.",
        });
    }

    // Step 1: Fetch user from database
    const [userResult] = await mysqlServerConnection.query(
      `SELECT password FROM ${dbName}.users WHERE id = ? AND is_deleted = 0`,
      [userId]
    );

    if (userResult.length === 0) {
      console.log("❌ User not found or deleted");
      return res
        .status(404)
        .json({ success: false, message: "User not found." });
    }

    const user = userResult[0];

    // Step 2: Compare current password
    const isMatch = await bcrypt.compare(currentPassword, user.password);
    if (!isMatch) {
      console.log("❌ Incorrect current password");
      return res
        .status(401)
        .json({ success: false, message: "Incorrect current password." });
    }

    // Step 3: Hash new password
    const hashedPassword = await bcrypt.hash(newPassword, 10);

    // Step 4: Update password in DB
    await mysqlServerConnection.query(
      `UPDATE ${dbName}.users SET password = ? WHERE id = ?`,
      [hashedPassword, userId]
    );

    console.log("✅ Password changed successfully");

    return res.status(200).json({
      success: true,
      message: "Password changed successfully.",
    });
  } catch (error) {
    console.error("❌ Error in changePassword:", error);
    return res
      .status(500)
      .json({ success: false, message: "Internal server error" });
  }
};

const SendOTPForAccountActivationFromLogin = async (req, res) => {
  console.log("🔍 [SendOTPForAccountActivationFromLogin] - Request:", req.body);
  try {
    const { domain, email } = req.body;

    const [dbmain] = await mysqlServerConnection.query(
      `SELECT db_name, org_logo_url, org_favicon_url, name 
       FROM ${maindb}.organization 
       WHERE auth_sub_domain = ?`,
      [domain]
    );

    console.log("🔍 DB Main:", dbmain);

    if (!dbmain || dbmain.length === 0) {
      return res.status(404).json({ success: false, message: "Organization not found for the provided domain" });
    }

    const orgdb = dbmain[0].db_name;

    console

    const [user] = await mysqlServerConnection.query(
      `SELECT * FROM ${orgdb}.users WHERE email = ?`,
      [email]
    );

    if (!user || user.length === 0) {
      return res.status(404).json({ success: false, message: "User not found" });
    }

    const otp = Math.floor(100000 + Math.random() * 900000);

    await mysqlServerConnection.query(
      `UPDATE ${orgdb}.users SET otp_code = ?, otp_expiry = ? WHERE email = ?`,
      [otp, new Date(Date.now() + 10 * 60 * 1000), email] // OTP valid for 10 mins
    );
    
    console.log("🔍 OTP generated and saved:", { email, otp });

    ActiveAccountOTPFromLoginPage(
      email,
      "Verify your account using the verification code",
      otp
    );

    // TODO: Send OTP via email here

    return res.status(200).json({ success: true, message: "OTP sent successfully", data: { otp: otp } });

  } catch (error) {
    console.error("❌ Error in ActiveAccountOTPFromLoginPage:", error);
    return res.status(500).json({ success: false, message: "Internal server error" });
  }
};



module.exports = {
  registration,
  organizationRoles,
  showOrganizationRoles,
  editOrganizationRoles,
  deleteOrganizationRole,
  organizationLoginUser,
  addNewOrganization,
  showOrganizationPermissions,
  sendLinkforlogin,
  forgotPassword,
  verifyRegisterOTP,
  resetPassword,
  sendotp,
  verifyOtp,
  googleLoginorSignup,
  updateUserProfile,
  getProfile,
  getCertificateData,
  updatePasswordAPI,
  deleteUserAccount,
  addClassroomApproval,
  checkClassroomApprovalStatus,
  validateEmail,
  validatePassword,
  organizationLogoutUser,
  updateProfilePic,
  softDeleteUserAccount,
  changePassword,
  getProfileComments,
  sendMagicLink,
  newRegistration,
  SendOTPForAccountActivationFromLogin
};
