import React, { useState } from 'react'
import { Icon } from '@iconify/react';
import ClassroomAssessmentQuestions from './ClassroomAssessmentQuestions'
import ClassroomAssessmentResult from './ClassroomAssessmentResult'
import './Classroom.css';
import { useParams } from 'react-router-dom';
import { decodeData } from '../../../utils/encodeAndEncode';
import { usePermissions } from '../../../context/PermissionsContext';
import { toast } from 'react-toastify';

function ClassroomAssessmentDetails() {
  const { permissions } = usePermissions();
  const { classroomId, assessmentId } = useParams();
  const decodedClassroomId = decodeData(classroomId);
  const decodedAssessmentId = decodeData(assessmentId);
  console.log('decodedClassroomId',decodedClassroomId);
  console.log('decodedAssessmentId',decodedAssessmentId);
  const [activeTab, setActiveTab] = useState('questions');

  const handleTabChange = (tab) => {
    if (tab === 'questions' && !permissions.classroom_assessment_question_view) {
      toast.warning("You don't have permission to view questions");
      return;
    }
    if (tab === 'results' && !permissions.classroom_assessment_result_view) {
      toast.warning("You don't have permission to view results");
      return;
    }
    setActiveTab(tab);
  };

  return (
    <div className="row">
      <div className="col-md-12">
        <div className="card">
          <div className="card-body p-0">
          
            {/* Tabs */}
            <div className="border-bottom">
              <ul className="nav nav-tabs border-0">
                <li className="nav-item">
                  <button
                    className={`nav-link border-0 d-flex align-items-center ${activeTab === 'questions' ? 'active' : ''} ${!permissions.classroom_assessment_question_view ? 'disabled opacity-75' : ''}`}
                    onClick={() => handleTabChange('questions')}
                    disabled={!permissions.classroom_assessment_question_view}
                    title={!permissions.classroom_assessment_question_view ? "You don't have permission to view questions" : "View assessment questions"}
                    style={{ cursor: !permissions.classroom_assessment_question_view ? 'not-allowed' : 'pointer' }}
                  >
                    <Icon icon="fluent:quiz-new-24-regular" className="me-2" width="20" height="20" />
                    Questions
                  </button>
                </li>
                <li className="nav-item">
                  <button
                    className={`nav-link border-0 d-flex align-items-center ${activeTab === 'results' ? 'active' : ''} ${!permissions.classroom_assessment_result_view ? 'disabled opacity-75' : ''}`}
                    onClick={() => handleTabChange('results')}
                    disabled={!permissions.classroom_assessment_result_view}
                    title={!permissions.classroom_assessment_result_view ? "You don't have permission to view results" : "View assessment results"}
                    style={{ cursor: !permissions.classroom_assessment_result_view ? 'not-allowed' : 'pointer' }}
                  >
                    <Icon icon="fluent:clipboard-task-24-regular" className="me-2" width="20" height="20" />
                    Results
                  </button>
                </li>
              </ul>
            </div>

            {/* Tab Content */}
            <div className="tab-content p-2">
              {activeTab === 'questions' && permissions.classroom_assessment_question_view && (
                <div className="questions">
                  <ClassroomAssessmentQuestions decodedClassroomId={decodedClassroomId} decodedAssessmentId={decodedAssessmentId} />
                </div>
              )}

              {activeTab === 'results' && permissions.classroom_assessment_result_view && (
                <div className="results">
                  <ClassroomAssessmentResult decodedClassroomId={decodedClassroomId} decodedAssessmentId={decodedAssessmentId} />
                </div>
              )}

              {((activeTab === 'questions' && !permissions.classroom_assessment_question_view) ||
                (activeTab === 'results' && !permissions.classroom_assessment_result_view)) && (
                <div className="alert alert-warning m-3">
                  You don't have permission to view this content.
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ClassroomAssessmentDetails
