.message-actions {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 8px;
  padding-top: 8px;
}

.action-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  border: none;
  background: transparent;
  color: #6c757d;
  transition: all 0.2s ease;
  padding: 0;
  position: relative;
}

@media screen and (max-width: 768px) {
    .action-button {
        padding: 2px !important;
        gap: 2px !important;
    }

    .message-actions {
        gap: 0.4rem !important;
    }

    .profile-chat{
        display: none;
    }
}

.action-button:hover {
  background-color: rgba(108, 117, 125, 0.1);
  color: #495057;
}

.action-button.liked {
  color: #0d6efd;
  background-color: rgba(13, 110, 253, 0.1);
}

.action-button.disliked {
  color: #dc3545;
  background-color: rgba(220, 53, 69, 0.1);
}

.action-button.reply {
  color: #9c27b0;
  background: #f3e5f5;
}

.action-button.edit {
  color: #ff9800;
  background: #fff3e0;
}

.action-button.share {
  color: #4caf50;
  background: #e8f5e9;
}

.action-button.delete {
  color: #f44336;
  background: #ffebee;
}

.action-button .count {
  font-size: 12px;
  margin-left: 4px;
}

.action-button .iconify {
  color: #0d6efd;
  transition: transform 0.2s ease;
}

.action-button:hover .iconify {
  transform: scale(1.1);
}

/* Disabled state */
.action-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Loading state */
.action-button.loading {
  position: relative;
  color: transparent;
}

.action-button.loading::after {
  content: "";
  position: absolute;
  width: 16px;
  height: 16px;
  border: 2px solid currentColor;
  border-right-color: transparent;
  border-radius: 50%;
  animation: spin 0.8s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Upload Modal Styles */
.upload-area {
  border-style: dashed !important;
  background-color: #ffffff;
}

.upload-area:hover {
  border-color: var(--bs-primary) !important;
  background-color: var(--bs-primary-bg-subtle);
}

.upload-type-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  background: none;
  border: none;
  padding: 12px 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  border-radius: 8px;
}

.upload-type-btn:hover:not(:disabled) {
  background-color: rgba(13, 110, 253, 0.1);
}

.upload-type-btn .icon-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background-color: #f8f9fa;
  margin-bottom: 8px;
  color: #0d6efd;
  transition: all 0.2s ease;
}

.upload-type-btn:hover:not(:disabled) .icon-wrapper {
  background-color: #0d6efd;
  color: white;
  transform: translateY(-2px);
}

.upload-type-btn span {
  font-size: 14px;
  color: #495057;
}

.upload-type-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Action Icons in Upload Modal */
.action-icon-btn {
  border: none;
  background: none;
  padding: 0;
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-icon-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Profile Image Styling */
.profile-chat {
  margin-top: auto;
  margin-bottom: auto;
}

/* Send Button Container */
.send-button-container {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4px;
}

/* Message Input Container */
.message-input-container {
  position: relative;
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
}

/* Emoji Button */
.emoji-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: none;
  background: transparent;
  color: #6c757d;
  transition: all 0.2s ease;
  padding: 0;
}

.emoji-button:hover {
  background-color: rgba(108, 117, 125, 0.1);
  color: #495057;
}
