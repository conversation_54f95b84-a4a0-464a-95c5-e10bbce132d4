-- Hierarchical Permission System Implementation
-- This script creates the new hierarchical permission structure

-- 1) The hierarchy of UI-nodes you'll lock down
CREATE TABLE IF NOT EXISTS permission_modules (
  id         INT AUTO_INCREMENT PRIMARY KEY,
  name       VARCHAR(100)    NOT NULL,       -- e.g. "Course", "Course Module", "Module Content"
  slug       VARCHAR(100)    NOT NULL UNIQUE,
  parent_id  INT              NULL,
  `order`    SMALLINT        NOT NULL DEFAULT 1,
  is_active  TINYINT(1)      NOT NULL DEFAULT 1,
  icon       VARCHAR(50)     NULL,           -- Icon class for UI
  description TEXT           NULL,           -- Description of the module
  created_at TIMESTAMP       NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP       NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY(parent_id) REFERENCES permission_modules(id)
    ON DELETE SET NULL ON UPDATE CASCADE
);

-- 2) The atomic actions you can grant (extending existing actions)
-- We'll keep the existing actions table but add more actions if needed
CREATE TABLE IF NOT EXISTS actions (
  id   INT AUTO_INCREMENT PRIMARY KEY,
  name VARCHAR(50) NOT NULL UNIQUE   -- e.g. "create","read","edit","delete","approve"
);

INSERT INTO actions (name) VALUES
  ('create'),('read'),('edit'),('delete'),('approve'),('activate'),('deactivate'),('publish'),('unpublish')
ON DUPLICATE KEY UPDATE name=VALUES(name);

-- 3) Enhanced role_permissions table to work with hierarchical modules
-- We'll create a new table for hierarchical permissions while keeping the old one for backward compatibility
CREATE TABLE IF NOT EXISTS hierarchical_role_permissions (
  role_id   INT NOT NULL,
  module_id INT NOT NULL,
  action_id INT NOT NULL,
  created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY(role_id, module_id, action_id),
  FOREIGN KEY(module_id) REFERENCES permission_modules(id) ON DELETE CASCADE,
  FOREIGN KEY(action_id) REFERENCES actions(id) ON DELETE CASCADE
);

-- Note: For organization databases, we need to create similar tables but without the auth prefix
-- This will be handled in the organization database creation script



-- 4) Seed the hierarchical permission tree based on your requirements
INSERT INTO `permission_modules` VALUES 
 (1,'Course Management','course',NULL,1,1,'fas fa-graduation-cap','Manage courses, modules, and content','2025-06-11 06:41:16','2025-06-11 06:41:16')
,(2,'Classroom Management','classroom',NULL,2,1,'fas fa-chalkboard-teacher','Manage classrooms and related activities','2025-06-11 06:41:16','2025-06-11 06:41:16')
,(3,'User Management','user',NULL,3,1,'fas fa-users','Manage users and trainees','2025-06-11 06:41:16','2025-06-11 06:41:16')
,(4,'Assessment Management','assessment',2,4,1,'fas fa-clipboard-check','Manage assessments and questions','2025-06-11 06:41:16','2025-06-11 06:55:07')
,(5,'Certificate Management','certificate',NULL,5,1,'fas fa-certificate','Manage certificates and templates','2025-06-11 06:41:16','2025-06-11 06:41:16')
,(6,'Announcement Management','announcement',NULL,6,1,'fas fa-bullhorn','Manage announcements','2025-06-11 06:41:16','2025-06-11 06:41:16')
,(7,'Analytics & Reports','analytics',NULL,7,1,'fas fa-chart-bar','View analytics and reports','2025-06-11 06:41:16','2025-06-11 06:41:16')
,(10,'Course Operations','course_operations',1,1,1,'fas fa-cogs','Basic course operations','2025-06-11 06:41:16','2025-06-11 06:41:16')
,(11,'Course Modules','course_modules',1,2,1,'fas fa-puzzle-piece','Manage course modules','2025-06-11 06:41:16','2025-06-11 06:41:16')
,(12,'Module Content','module_content',11,1,1,'fas fa-file-alt','Manage content within modules','2025-06-11 06:41:16','2025-06-11 06:41:16')
,(13,'Course Approval','course_approval',NULL,1,1,'fas fa-check-circle','Course approval workflow','2025-06-11 06:41:16','2025-06-11 07:07:10')
,(20,'Classroom Operations','classroom_operations',2,1,1,'fas fa-door-open','Basic classroom operations','2025-06-11 06:41:16','2025-06-11 06:41:16')
,(21,'Live Classes','live_classes',2,2,1,'fas fa-video','Manage live classes','2025-06-11 06:41:16','2025-06-11 06:41:16')
,(22,'Classroom Trainees','classroom_trainees',2,3,1,'fas fa-user-graduate','Manage classroom trainees','2025-06-11 06:41:16','2025-06-11 06:41:16')
,(23,'Classroom Resources','classroom_resources',2,4,1,'fas fa-folder-open','Manage classroom resources','2025-06-11 06:41:16','2025-06-11 06:41:16')
,(24,'Assignment Management','assignments',2,5,1,'fas fa-tasks','Manage assignments','2025-06-11 06:41:16','2025-06-11 06:56:10')
,(25,'Assignment Responses','assignment_responses',24,1,1,'fas fa-reply','Manage assignment responses','2025-06-11 06:41:16','2025-06-11 06:41:16')
,(30,'Assessment Operations','assessment_operations',4,1,1,'fas fa-edit','Basic assessment operations','2025-06-11 06:41:16','2025-06-11 06:41:16')
,(31,'Assessment Questions','assessment_questions',4,2,1,'fas fa-question-circle','Manage assessment questions','2025-06-11 06:41:16','2025-06-11 06:41:16')
,(32,'Assessment Results','assessment_results',4,3,1,'fas fa-poll','View assessment results','2025-06-11 06:41:16','2025-06-11 06:41:16')
,(33,'Question Bank','question_bank',4,4,1,'fas fa-database','Manage question bank','2025-06-11 06:41:16','2025-06-11 06:41:16')
,(40,'Admin Users','admin_users',3,1,1,'fas fa-user-shield','Manage admin users','2025-06-11 06:41:16','2025-06-11 06:41:16')
,(41,'Trainees','trainees',3,2,1,'fas fa-user-graduate','Manage trainees','2025-06-11 06:41:16','2025-06-11 06:41:16')
,(42,'Roles & Permissions','roles_permissions',3,3,1,'fas fa-key','Manage roles and permissions','2025-06-11 06:41:16','2025-06-11 06:41:16')
,(43,'Course Module Operations','course_module_operations',11,1,1,'fas fa-cogs','Basic course module operations','2025-06-11 07:08:30','2025-06-11 07:10:39');

