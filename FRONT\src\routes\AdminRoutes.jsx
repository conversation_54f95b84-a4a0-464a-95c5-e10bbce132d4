import 'react-toastify/dist/ReactToastify.css';

import React, { lazy } from 'react';
import { Route, Routes } from 'react-router-dom';

import AdminLayout from '../layouts/AdminLayout';
import AllClassroom from '../pages/admin/classroom/AllClassroom';
import AllCourses from '../pages/admin/courses/AllCourses';
import ApprovalRequest from '../pages/admin/approvalRequest/ApprovalRequest';
import ClassroomAssessments from '../pages/admin/classroom/ClassroomAssessments';
import ClassroomAssignments from '../pages/admin/classroom/ClassroomAssignments';
import ClassroomCommunity from '../pages/admin/classroom/ClassroomCommunity';
import ClassroomDashboard from '../pages/admin/classroom/ClassroomDashboard';
import ClassroomLiveClasses from '../pages/admin/classroom/ClassroomLiveClasses';
import ClassroomTrainees from '../pages/admin/classroom/ClassroomTrainees';
import ClassrooomAssessmentDetails from '../pages/admin/classroom/ClassroomAssessmentDetails';
import ClassrooomAssignmentDetails from '../pages/admin/classroom/ClassroomAssignmentDetails';
import QuestionBank from '../pages/admin/questionBank/QuestionBank';


import AllCertificates from '../pages/admin/certificates/AllCertificates';
import Announcement from '../pages/admin/announcement/announcement';

import RolesAndAccess from '../pages/admin/roles_and_access/RolesAndAccess';


import CourseAnalytics from '../pages/admin/courses/CourseAnalytics';
import CourseAnalyticsAssessmentDetails from '../pages/admin/courses/CourseAnalyticsAssessmentDetails'
import CourseAnalyticsSurveyDetails from '../pages/admin/courses/CourseAnalyticsSurveyDetails'
import CourseDocument from '../pages/admin/courses/CourseDocument';
import CourseModule from '../pages/admin/courses/CourseModule';
import CourseQuiz from '../pages/admin/courses/CourseQuiz';
import CourseSurvey from '../pages/admin/courses/CourseSurvey';
import CourseVideo from '../pages/admin/courses/CourseVideo';
import Courses from '../pages/admin/courses/Courses';
import CreateCourse from '../pages/admin/courses/CreateCourse';
import Dashboard from '../pages/admin/dashboard/Dashboard';
import ModuleContent from '../pages/admin/courses/ModuleContent';
import NotificationDetails from '../pages/admin/Notifications/NotificationDetails';
import Notifications from '../pages/admin/Notifications/Notifications';
import Profile from '../pages/admin/profile/Profile';
import Settings from '../pages/admin/settings/Settings';
import { ToastContainer } from 'react-toastify';
import TraineeAnalytics from '../pages/admin/trainees/TraineeAnalytics';
import Trainees from '../pages/admin/trainees/trainees';
import VideoPlayer from '../pages/admin/courses/CourseVideo/VideoPlayer';







import PromptAssistant from '../pages/admin/PromptAssistant/PromptAssistant';
import ZoomMeeting from '../components/zoom/ZoomMeeting';







function AdminRoutes() {
  return (
    <>
      <Routes>
        {/* Standalone Zoom Meeting Route - No Layout */}
        <Route path="/zoom-meeting" element={<ZoomMeeting />} />

        <Route path="/admin" element={<AdminLayout />}>
          {/* Dashboard */}
          <Route path="dashboard" element={<Dashboard />} />
          <Route path="profile" element={<Profile />} />

          {/* Courses */}
          <Route path="courses" element={<Courses />} />
          <Route path="AllCourses" element={<AllCourses />} />
          <Route path="courses/module/:courseId" element={<CourseModule />} />
          <Route path="courses/module/:courseId/content/:moduleId" element={<ModuleContent />} />

          <Route path="courses/module/:courseId/content/:moduleId/document/:contentId" element={<CourseDocument />} />
          <Route path="courses/module/:courseId/content/:moduleId/survey/:contentId" element={<CourseSurvey />} />
          <Route path="courses/module/:courseId/content/:moduleId/quiz/:contentId" element={<CourseQuiz />} />
          <Route path="courses/module/:courseId/content/:moduleId/video/:contentId" element={<VideoPlayer />} />



          {/* <Route path="courses/module/:courseId/content/:moduleId/video/:contentId" element={<CourseVideo />} /> */}


 

          <Route path="courses/create" element={<CreateCourse />} />
          <Route path="courses/edit-create/:courseId" element={<CreateCourse />} />

           {/* Course Analytics */}
          <Route path="courses/analytics/:courseId" element={<CourseAnalytics />} />
          <Route path="courses/analytics/:courseId/CourseAnalyticsAssessmentDetails/:assessmentId" element={<CourseAnalyticsAssessmentDetails />} />
          <Route path="courses/analytics/:courseId/CourseAnalyticsSurveyDetails/:surveyId" element={<CourseAnalyticsSurveyDetails />} />



          <Route path="CourseModule" element={<CourseModule />} />
          <Route path="CourseModuleContent" element={<ModuleContent />} />
          <Route path="CourseVideo" element={<CourseVideo />} />
          <Route path="CourseDocument" element={<CourseDocument />} />
          <Route path="CourseQuiz" element={<CourseQuiz />} />
          <Route path="CourseSurvey" element={<CourseSurvey />} />





          {/* Roles and Access */}
          <Route path="roles-and-access" element={<RolesAndAccess />} />





          {/* Approval Request */}
          <Route path="approvalRequest" element={<ApprovalRequest />} />
          {/* Classroom */}
          <Route path="classrooms" element={<AllClassroom />} />
          <Route path="classrooms/classroom-dashboard/:classroomId" element={<ClassroomDashboard />} />
          <Route path="classrooms/classroom-dashboard/:classroomId/trainees/:classroomId" element={<ClassroomTrainees />} />

          <Route path="ClassroomAssignments" element={<ClassroomAssignments />} />
          <Route path="classrooms/classroom-dashboard/:classroomId/assingment/:assignmentId/ClassroomAssignmentDetails" element={<ClassrooomAssignmentDetails />} />

          <Route path="ClassroomAssessments" element={<ClassroomAssessments />} />
          <Route path="classrooms/classroom-dashboard/:classroomId/assessment/:assessmentId/ClassroomAssessmentDetails" element={<ClassrooomAssessmentDetails />} />




          {/* Question Bank */}
          <Route path="question-bank" element={<QuestionBank />} />



          
          <Route path="ClassroomLiveClasses" element={<ClassroomLiveClasses />} />
          <Route path="ClassroomCommunity" element={<ClassroomCommunity />} />


          {/* Trainees */}
          <Route path="trainees" element={<Trainees />} />
          <Route path="traineesAnalytics/:traineeId" element={<TraineeAnalytics />} />

          {/* Certificates */}
          <Route path="certificates" element={<AllCertificates />} />

          {/* Announcement */}
          <Route path="announcement" element={<Announcement />} />

          {/* Notifications */}
          <Route path="notifications" element={<Notifications />} />
          <Route path="notifications/:id" element={<NotificationDetails />} />

          {/* Settings */}
          <Route path="settings" element={<Settings />} />


          {/* Prompt Assistant */}
          <Route path="prompt-assistant" element={<PromptAssistant />} />



        </Route>
      </Routes> 
      <ToastContainer limit={3} />
    </>
  );
}

export default AdminRoutes;

