const { mysqlServerConnection } = require('../../../db/db');

// Get rankings data
const getRankings = async (req, res) => {
    try {
        console.log("Fetching Rankings...");

        // SQL query to join rankings and users table to fetch user_id, score, and user name
        const query = `
            SELECT r.user_id, r.score, u.name 
            FROM ${req.user.db_name}.rankings r
            INNER JOIN ${req.user.db_name}.users u ON r.user_id = u.id
            ORDER BY r.score DESC
        `;

        const [results] = await mysqlServerConnection.query(query);

        if (!results.length) {
            return res.status(404).json({
                success: false,
                data: {
                    error_msg: 'No rankings found.'
                }
            });
        }

        // Calculate ranks based on scores
        const calculateRanks = (students) => {
            const sortedStudents = [...students]; // Already sorted by SQL query
            let currentRank = 1;
            let rankOffset = 1;

            return sortedStudents.map((student, index) => {
                if (index > 0 && student.score === sortedStudents[index - 1].score) {
                    return { ...student, rank: currentRank };
                } else {
                    currentRank = rankOffset;
                    rankOffset++;
                    return { ...student, rank: currentRank };
                }
            });
        };

        const rankedResults = calculateRanks(results);

        res.status(200).json({
            success: true,
            data: rankedResults  // Removed extra nested object for cleaner response
        });

    } catch (error) {
        console.error('Error fetching rankings:', error);
        res.status(500).json({
            success: false,
            data: {
                error_msg: 'Internal server error while fetching rankings.',
                details: error.message
            }
        });
    }
};

module.exports = { getRankings };
