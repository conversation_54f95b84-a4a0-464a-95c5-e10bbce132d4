const { response, query } = require("express");
const jwt = require("jsonwebtoken");
const bcrypt = require("bcrypt");
const { mysqlServerConnection } = require("../../../db/db");
const createdb = require("../../../superAdmin/db/utils/createOrganizationDb");
const {
  Validate,
  hashPassword,
  generateAccessToken,
  generateRefreshToken,
  sendEmail,
} = require("../../../tools/tools");
const {
  sendMagicLink,
  sendForgotPasswordLink,
  sendOTP,
} = require("../../../tools/emailtemplates");
const { body } = require("express-validator");
const crypto = require("crypto");
const { stringify } = require("querystring");
const MaindbName = process.env.MAIN_DB;

const submitAnswers = async (req, res) => {
  console.log("answers");
  console.log("submitAnswers : ", req.body);
  try {
    const userId = req.user.userId; // Read user ID from the request context (authentication middleware)
    const { assessment_id, question_id, options, current_question, is_final } =
      req.body; // Read assessment_id and answers from the request body

    console.log("answers : ", req.body);

    // Validate input
    if ((!assessment_id || question_id, options.length === 0)) {
      return res.status(400).json({
        success: false,
        data: {
          error_msg: "User ID, Assessment ID, and Question ID are required",
          response: {
            assessment_id: "",
            question_id: "",
            options: [],
            current_question: 0,
          },
        },
      });
    }

    const validationResults = [];
    const [assessmentDetails] = await mysqlServerConnection.query(
      `SELECT earn_point, earn_point_applicable_after, class_id, module_id, pass_percentage FROM ${req.user.db_name}.assessments WHERE id = ?`,
      [assessment_id]
    );

    await mysqlServerConnection.query(
      `INSERT INTO ${req.user.db_name}.assessment_users (assessment_id, user_id, status, is_eligible)
                SELECT ?, ?, ?, ?
                FROM DUAL
                WHERE NOT EXISTS (
                    SELECT 1 FROM ${req.user.db_name}.assessment_users 
                    WHERE assessment_id = ? AND user_id = ?
                );`,
      [assessment_id, userId, 1, 1, assessment_id, userId]
    );

    if (assessmentDetails.length === 0) {
      return res.status(400).json({
        success: false,
        data: {
          error_msg: "Invalid assessment ID provided",
        },
      });
    }

    const {
      earn_point,
      earn_point_applicable_after,
      module_id,
      pass_percentage,
    } = assessmentDetails[0];

    try {
      // First try to fetch correct options from question_options table
      const [correctOptionRecords] = await mysqlServerConnection.query(
        `SELECT id FROM ${req.user.db_name}.question_options WHERE question_id = ? AND is_correct = 1`,
        [question_id]
      );

      // Extract the option IDs from the records
      const correctOptionIds = correctOptionRecords.map((record) =>
        record.id.toString()
      );

      // If no correct options found in question_options table, fall back to correct_option in questions table
      if (!correctOptionIds || correctOptionIds.length === 0) {
        console.log(
          "No correct options found in question_options table, falling back to questions.correct_option"
        );

        // Fetch the correct options from the questions table
        const [questionData] = await mysqlServerConnection.query(
          `SELECT correct_option, options FROM ${req.user.db_name}.questions WHERE id = ?`,
          [question_id]
        );

        if (!questionData || questionData.length === 0) {
          return res.status(400).json({
            success: false,
            data: {
              error_msg: "Question not found",
            },
          });
        }

        // Parse the correct_option field
        let correctOptionArray = [];
        try {
          correctOptionArray = JSON.parse(questionData[0].correct_option);
          if (!Array.isArray(correctOptionArray)) {
            correctOptionArray = [correctOptionArray];
          }
        } catch (e) {
          console.error("Error parsing correct_option:", e);
          correctOptionArray = questionData[0].correct_option
            ? [questionData[0].correct_option]
            : [];
        }

        // Get the option IDs from the request
        const optionIds = req.body.option_ids || options;

        // Parse the options field to map indices to option text
        let optionsArray = [];
        try {
          optionsArray = JSON.parse(questionData[0].options);
        } catch (e) {
          console.error("Error parsing options:", e);
        }

        // Check if any of the selected options match the correct options
        let isCorrect = false;

        if (optionIds && optionIds.length > 0) {
          // First check: If optionIds contains keys that match correctOptionArray values
          isCorrect = optionIds.some((optionId) => {
            // If optionId is a key in optionsArray and its value matches any correct option
            if (
              optionsArray[optionId] &&
              correctOptionArray.includes(optionsArray[optionId])
            ) {
              return true;
            }
            return false;
          });

          // Second check: If not correct yet, try direct matching with correctOptionArray
          if (!isCorrect) {
            isCorrect = optionIds.some((optionId) =>
              correctOptionArray.includes(optionId)
            );
          }

          // Third check: Try numeric index matching (0-based)
          if (!isCorrect) {
            const numericOptionIds = optionIds.filter(
              (id) => !isNaN(parseInt(id))
            );
            if (numericOptionIds.length > 0) {
              isCorrect = numericOptionIds.some((optionId) => {
                const index = parseInt(optionId);
                // Check if this is an array or object
                if (Array.isArray(optionsArray)) {
                  // For array format
                  return (
                    index >= 0 &&
                    index < optionsArray.length &&
                    correctOptionArray.includes(optionsArray[index])
                  );
                } else {
                  // For object format with numeric keys
                  const keys = Object.keys(optionsArray);
                  if (index >= 0 && index < keys.length) {
                    return correctOptionArray.includes(
                      optionsArray[keys[index]]
                    );
                  }
                }
                return false;
              });
            }
          }
        }

        // For debugging
        console.log("Raw data (fallback):", {
          question_id,
          options,
          optionIds,
          correctOptionArray,
          optionsArray,
        });

        console.log("Answer validation (fallback):", {
          userOptions: options,
          correctOptions: correctOptionArray,
          isCorrect: isCorrect,
        });

        // Use the result from the fallback method
        let collectPoint = 0;
        if (isCorrect) {
          if (
            parseInt(current_question) > parseInt(earn_point_applicable_after)
          ) {
            collectPoint = parseInt(earn_point);
          }
        }

        // Rest of the code for handling the answer...
        // Check if the user already has an answer for this question
        const [existingAnswer] = await mysqlServerConnection.query(
          `SELECT id, collect_point FROM ${req.user.db_name}.question_answer WHERE question_id = ? AND user_id = ?`,
          [question_id, userId]
        );

        // Continue with the existing logic...
        if (existingAnswer.length > 0) {
          // Update existing answer
          await mysqlServerConnection.query(
            `UPDATE ${req.user.db_name}.question_answer 
                         SET answer = ?, is_correct = ?, collect_point = ?, updatedAt = CURRENT_TIMESTAMP 
                         WHERE id = ?`,
            [
              JSON.stringify(options),
              isCorrect ? 1 : 0,
              collectPoint,
              existingAnswer[0].id,
            ]
          );
        } else {
          // Insert new answer
          await mysqlServerConnection.query(
            `INSERT INTO ${req.user.db_name}.question_answer 
                         (question_id, assessment_id, user_id, answer, is_correct, collect_point, question_type, createdAt, updatedAt)
                         VALUES (?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)`,
            [
              question_id,
              assessment_id,
              userId,
              JSON.stringify(options),
              isCorrect ? 1 : 0,
              collectPoint,
              "normal",
            ]
          );
        }

        // Return early with the result from the fallback method
        // Calculate the total correct answers
        const totalCorrectAnswers = validationResults.reduce(
          (count, result) => (result.isCorrect ? count + 1 : count),
          0
        );

        // Check if there are more questions to answer
        const [remainingQuestionsCount] = await mysqlServerConnection.query(
          `SELECT COUNT(*) as count
                    FROM ${req.user.db_name}.questions q
                    WHERE q.assessment_id = ?
                    AND NOT EXISTS (
                        SELECT 1 FROM ${req.user.db_name}.question_answer qa
                        WHERE qa.user_id = ? AND qa.question_id = q.id
                    )`,
          [assessment_id, userId]
        );

        const remainingCount = remainingQuestionsCount[0].count;

        // Check if this is the final submission
        if (req.body.is_final) {
          // All questions answered, return completion status
          const [totalQuestionsCount] = await mysqlServerConnection.query(
            `SELECT COUNT(*) as total FROM ${req.user.db_name}.questions WHERE assessment_id = ?`,
            [assessment_id]
          );

          const [correctAnswersCount] = await mysqlServerConnection.query(
            `SELECT COUNT(*) as correct FROM ${req.user.db_name}.question_answer 
                        WHERE assessment_id = ? AND user_id = ? AND is_correct = 1`,
            [assessment_id, userId]
          );

          return res.status(200).json({
            success: true,
            data: {
              isCompleted: true,
              totalQuestions: totalQuestionsCount[0].total,
              totalCorrectAnswers: correctAnswersCount[0].correct,
              score: Math.round(
                (correctAnswersCount[0].correct /
                  totalQuestionsCount[0].total) *
                  100
              ),
              passingScore: pass_percentage || 60,
            },
          });
        }

        if (remainingCount > 0) {
          // Fetch the next question
          const nextQuestion = await getQuestionsByAssessmentId(
            {
              user: req.user,
              body: { assessmentId: assessment_id },
            },
            {
              status: function (code) {
                return this;
              },
              json: function (data) {
                return data;
              },
            }
          );

          return res.status(200).json({
            success: true,
            data: {
              total_correct_answers: totalCorrectAnswers,
              answers: validationResults,
              message: "Answers processed successfully",
              question: nextQuestion.data.question,
              totalQuestions: nextQuestion.data.totalQuestions,
              remainingQuestions: nextQuestion.data.remainingQuestions,
              isCompleted: false,
            },
          });
        } else {
          // No more questions, quiz is completed
          return res.status(200).json({
            success: true,
            data: {
              total_correct_answers: totalCorrectAnswers,
              answers: validationResults,
              message: "All questions answered",
              isCompleted: true,
              totalQuestions: totalQuestions[0].total,
              correctAnswers: correctAnswers[0].correct,
              score: Math.round(scorePercentage),
            },
          });
        }
      }

      // Get the option IDs from the request
      const optionIds = req.body.option_ids || options;

      // Check if ALL selected options are correct and NO incorrect options are selected
      let isCorrect = false;

      if (optionIds && optionIds.length > 0) {
        // Convert all option IDs to strings for consistent comparison
        const normalizedOptionIds = optionIds.map((id) => id.toString());

        // Check if every selected option is correct (no incorrect options)
        const allSelectedOptionsAreCorrect = normalizedOptionIds.every(
          (optionId) => correctOptionIds.includes(optionId)
        );

        // Only mark as correct if all selected options are correct
        isCorrect =
          allSelectedOptionsAreCorrect && normalizedOptionIds.length > 0;
      }

      // For debugging
      console.log("Raw data:", {
        question_id,
        options,
        optionIds,
        correctOptionIds,
      });

      console.log("Answer validation:", {
        userOptions: options,
        correctOptions: correctOptionIds,
        isCorrect: isCorrect,
      });

      let collectPoint = 0;
      if (isCorrect) {
        if (
          parseInt(current_question) > parseInt(earn_point_applicable_after)
        ) {
          collectPoint = parseInt(earn_point);
        }
      }

      // Check if the user already has an answer for this question
      const [existingAnswer] = await mysqlServerConnection.query(
        `SELECT id, collect_point FROM ${req.user.db_name}.question_answer WHERE question_id = ? AND user_id = ?`,
        [question_id, userId]
      );

      if (existingAnswer.length > 0) {
        await mysqlServerConnection.query(
          `UPDATE ${req.user.db_name}.question_answer SET answer = ?, collect_point = ?, is_correct=?, assessment_id=? WHERE id = ?`,
          [
            `${JSON.stringify(options)}`,
            collectPoint,
            isCorrect,
            assessment_id,
            existingAnswer[0].id,
          ]
        );
        validationResults.push({
          question_id,
          options,
          isCorrect,
          error_msg: null,
        });
      } else {
        // Insert a new record for this answer
        await mysqlServerConnection.query(
          `INSERT INTO ${req.user.db_name}.question_answer (question_id, user_id, answer, is_correct, assessment_id, collect_point) VALUES (?, ?, ?, ?, ?, ?)`,
          [
            question_id,
            userId,
            `${JSON.stringify(options)}`,
            isCorrect ? 1 : 0,
            assessment_id,
            collectPoint,
          ]
        );
        validationResults.push({
          question_id,
          options,
          isCorrect,
          error_msg: null,
        });
      }
      if (!module_id) {
        if (parseInt(collectPoint) > 0) {
          await mysqlServerConnection.query(
            `INSERT INTO ${req.user.db_name}.user_points (user_id, total_earn) VALUES (?, ?)`,
            [userId, collectPoint]
          );
        }
      }
      const [totalQuestions] = await mysqlServerConnection.query(
        `SELECT COUNT(*) AS total FROM ${req.user.db_name}.questions WHERE assessment_id = ?`,
        [assessment_id]
      );

      const [correctAnswers] = await mysqlServerConnection.query(
        `SELECT COUNT(*) AS correct FROM ${req.user.db_name}.question_answer WHERE assessment_id = ? AND user_id = ? AND is_correct = 1`,
        [assessment_id, userId]
      );
      const [answeredQuestions] = await mysqlServerConnection.query(
        `SELECT COUNT(*) AS answered FROM ${req.user.db_name}.question_answer WHERE assessment_id = ? AND user_id = ?`,
        [assessment_id, userId]
      );

      // Calculate progress percentage based on answered questions
      const progressPercentage =
        (answeredQuestions[0].answered / totalQuestions[0].total) * 100;

      // Calculate score percentage based on correct answers
      const scorePercentage =
        (correctAnswers[0].correct / totalQuestions[0].total) * 100;

      // Quiz is completed when is_final is true or all questions are answered
      const isCompleted =
        is_final === true ||
        answeredQuestions[0].answered >= totalQuestions[0].total;

      // Quiz is passed if score is at least the pass_percentage (default to 60 if not set)
      const passThreshold = pass_percentage || 60;
      const isPassed = scorePercentage >= passThreshold;

      console.log("Quiz progress:", {
        totalQuestions: totalQuestions[0].total,
        answeredQuestions: answeredQuestions[0].answered,
        correctAnswers: correctAnswers[0].correct,
        progressPercentage,
        scorePercentage,
        passThreshold,
        isCompleted,
        isPassed,
      });

      // Update the assessment_users table with completion status only (score column doesn't exist)
      await mysqlServerConnection.query(
        `UPDATE ${req.user.db_name}.assessment_users SET is_completed = ?, is_passed = ? WHERE assessment_id = ? AND user_id = ?`,
        [isCompleted ? 1 : 0, isPassed ? 1 : 0, assessment_id, userId]
      );
    } catch (error) {
      validationResults.push({
        question_id,
        options,
        isCorrect: false,
        error_msg: "Invalid",
      });
      console.error(
        `Error processing answer for questionId ${question_id}:`,
        error
      );
    }

    // Calculate the total correct answers based on the
    const totalCorrectAnswers = validationResults.reduce(
      (count, result) => (result.isCorrect ? count + 1 : count),
      0
    );

    // Check if there are more questions to answer
    const [remainingQuestionsCount] = await mysqlServerConnection.query(
      `SELECT COUNT(*) as count
             FROM ${req.user.db_name}.questions q
             WHERE q.assessment_id = ?
             AND NOT EXISTS (
                 SELECT 1 FROM ${req.user.db_name}.question_answer qa
                 WHERE qa.user_id = ? AND qa.question_id = q.id
             )`,
      [assessment_id, userId]
    );

    const remainingCount = remainingQuestionsCount[0].count;

    // Check if this is the final submission
    if (req.body.is_final) {
      // All questions answered, return completion status
      const [totalQuestionsCount] = await mysqlServerConnection.query(
        `SELECT COUNT(*) as total FROM ${req.user.db_name}.questions WHERE assessment_id = ?`,
        [assessment_id]
      );

      const [correctAnswersCount] = await mysqlServerConnection.query(
        `SELECT COUNT(*) as correct FROM ${req.user.db_name}.question_answer 
                 WHERE assessment_id = ? AND user_id = ? AND is_correct = 1`,
        [assessment_id, userId]
      );

      console.log("totalQuestionsCount : ", totalQuestionsCount);
      console.log("correctAnswersCount : ", correctAnswersCount);

      // Calculate score properly to ensure consistency
      const totalQuestions = parseInt(totalQuestionsCount[0].total) || 1; // Avoid division by zero
      const correctAnswers = parseInt(correctAnswersCount[0].correct) || 0;
      const scorePercentage = Math.round(
        (correctAnswers / totalQuestions) * 100
      );

      console.log(
        "Final score calculation: ",
        scorePercentage,
        "% (",
        correctAnswers,
        "/",
        totalQuestions,
        ")"
      );

      return res.status(200).json({
        success: true,
        data: {
          isCompleted: true,
          totalQuestions: totalQuestions,
          totalCorrectAnswers: correctAnswers,
          score: scorePercentage,
          passingScore: pass_percentage || 60,
        },
      });
    }

    if (remainingCount > 0) {
      // Fetch the next question
      const nextQuestion = await getQuestionsByAssessmentId(
        {
          user: req.user,
          body: { assessmentId: assessment_id },
        },
        {
          status: function (code) {
            return this;
          },
          json: function (data) {
            return data;
          },
        }
      );

      return res.status(200).json({
        success: true,
        data: {
          total_correct_answers: totalCorrectAnswers,
          answers: validationResults,
          message: "Answers processed successfully",
          question: nextQuestion.data.question,
          totalQuestions: nextQuestion.data.totalQuestions,
          remainingQuestions: nextQuestion.data.remainingQuestions,
          isLastQuestion: remainingCount === 1,
        },
      });
    } else {
      // All questions answered, return completion status
      const [totalQuestionsCount] = await mysqlServerConnection.query(
        `SELECT COUNT(*) as total FROM ${req.user.db_name}.questions WHERE assessment_id = ?`,
        [assessment_id]
      );

      const [correctAnswersCount] = await mysqlServerConnection.query(
        `SELECT COUNT(*) as correct FROM ${req.user.db_name}.question_answer 
                 WHERE assessment_id = ? AND user_id = ? AND is_correct = 1`,
        [assessment_id, userId]
      );

      console.log("totalQuestionsCount : ", totalQuestionsCount);
      console.log("correctAnswersCount : ", correctAnswersCount);

      // Calculate score properly to ensure consistency
      const totalQuestions = parseInt(totalQuestionsCount[0].total) || 1; // Avoid division by zero
      const correctAnswers = parseInt(correctAnswersCount[0].correct) || 0;
      const scorePercentage = Math.round(
        (correctAnswers / totalQuestions) * 100
      );

      console.log(
        "Final score calculation: ",
        scorePercentage,
        "% (",
        correctAnswers,
        "/",
        totalQuestions,
        ")"
      );

      return res.status(200).json({
        success: true,
        data: {
          isCompleted: true,
          totalQuestions: totalQuestions,
          totalCorrectAnswers: correctAnswers,
          score: scorePercentage,
          passingScore: pass_percentage || 60,
        },
      });
    }
  } catch (error) {
    console.error("Error submitting answers:", error);
    return res.status(500).json({
      success: false,
      data: {
        error_msg: "Internal server error.",
      },
    });
  }
};

const getAllAssessmentStatus = async (req, res) => {
  try {
    const page = parseInt(req.body.page, 10) || 1;
    const limit = parseInt(req.body.limit, 10) || 10;
    const search = req.body.search ? `%${req.body.search}%` : "";

    const offset = (page - 1) * limit;
    const searchCondition = search
      ? "AND (a.assessment_name LIKE ? OR a.status LIKE ?)"
      : "";

    if (!req.user || !req.user.db_name || !req.user.userId) {
      return res.status(400).json({
        success: false,
        data: { error_msg: "Invalid user information." },
      });
    }

    // Query to fetch assessments
    const [modules] = await mysqlServerConnection.query(
      `SELECT a.id, u.name AS created_by_name, a.assessment_name, a.course_id, c.course_name, a.is_active, a.is_approved
             FROM ${req.user.db_name}.assessments a
             LEFT JOIN ${req.user.db_name}.users u ON a.created_by = u.id
             LEFT JOIN ${req.user.db_name}.courses c ON a.course_id = c.id
             LEFT JOIN ${req.user.db_name}.mycourses mc ON a.course_id = mc.course_id
             WHERE mc.user_id = ? ${searchCondition}
             LIMIT ? OFFSET ?`,
      search
        ? [req.user.userId, search, search, limit, offset]
        : [req.user.userId, limit, offset]
    );

    // Query to fetch total count
    const [[{ count }]] = await mysqlServerConnection.query(
      `SELECT COUNT(*) AS count 
             FROM ${req.user.db_name}.assessments a
             LEFT JOIN ${req.user.db_name}.mycourses mc ON a.course_id = mc.course_id
             WHERE mc.user_id = ? ${searchCondition}`,
      search ? [req.user.userId, search, search] : [req.user.userId]
    );

    // Group assessments by course
    const groupedByCourse = modules.reduce((acc, module) => {
      const courseName = module.course_name || "Unknown Course";
      if (!acc[courseName]) {
        acc[courseName] = {
          course_id: module.course_id,
          course_name: courseName,
          created_by_name: module.created_by_name,
          assessment_count: 0,
          completed_count: 0,
        };
      }
      acc[courseName].assessment_count++;
      if (module.status === "completed") {
        acc[courseName].completed_count++;
      }
      return acc;
    }, {});

    // Calculate total pages
    const totalPages = Math.ceil(count / limit);

    // Respond with grouped data and pagination info
    res.status(200).json({
      success: true,
      data: {
        message: "Courses and assessment counts retrieved successfully.",
        response: {
          courses: Object.values(groupedByCourse),
          pagination: {
            page,
            limit,
            totalPages,
            totalCount: count,
          },
        },
      },
    });
  } catch (error) {
    console.error("Error retrieving assessments:", error);
    res
      .status(500)
      .json({ success: false, data: { error_msg: "Internal server error." } });
  }
};

const getAllAssessmentData = async (req, res) => {
  try {
    // Get page, limit, and search from request body
    const page = parseInt(req.body.page, 10) || 1; // Default to page 1 if not provided
    const limit = parseInt(req.body.limit, 10) || 10; // Default to 10 items per page
    const search = req.body.search ? `%${req.body.search}%` : ""; // Default to empty string if not provided
    const courseId = parseInt(req.body.course_id, 10); // Get course_id from request body

    // Get class_id from request parameters
    const classId = req.params.class_id
      ? parseInt(req.params.class_id, 10)
      : null;

    // Validate course_id if class_id is not provided
    if (!classId && isNaN(courseId)) {
      return res
        .status(400)
        .json({ success: false, data: { error_msg: "Invalid course_id." } });
    }

    // Calculate the offset for pagination
    const offset = (page - 1) * limit;

    // Construct the condition based on class_id or course_id
    const condition = classId
      ? "a.class_id = ?" // Use class_id condition if provided
      : "ass.course_id = ?"; // Otherwise, default to course_id condition

    // Construct the search condition for `status` or `assessment_name`
    const searchCondition = search
      ? "AND (a.status LIKE ? OR ass.assessment_name LIKE ?)"
      : "";

    // Fetch assessment data based on class_id or course_id with pagination, search, and join with assessments
    const queryParams = search
      ? [classId || courseId, search, search, limit, offset]
      : [classId || courseId, limit, offset];

    const [modules] = await mysqlServerConnection.query(
      `SELECT 
                a.id, 
                u.name AS created_by_name, 
                a.assessment_id, 
                ass.assessment_name, 
                a.user_id, 
                a.status, 
                a.is_eligible, 
                a.createdAt,
                (SELECT COUNT(*) FROM ${req.user.db_name}.questions aq WHERE aq.assessment_id = a.assessment_id) AS question_count,
                (SELECT COUNT(*) 
                 FROM ${req.user.db_name}.question_answer qa 
                 INNER JOIN ${req.user.db_name}.questions q ON qa.question_id = q.id 
                 WHERE q.assessment_id = a.assessment_id AND qa.user_id = a.user_id) AS completed_question_count
             FROM ${req.user.db_name}.assessment_users a
             LEFT JOIN ${req.user.db_name}.users u ON a.user_id = u.id
             LEFT JOIN ${req.user.db_name}.assessments ass ON a.assessment_id = ass.id
             WHERE ${condition} ${searchCondition}
             LIMIT ? OFFSET ?`,
      queryParams
    );

    // Add detailed question data and pass/fail status for each module
    for (const module of modules) {
      const [completedQuestions] = await mysqlServerConnection.query(
        `SELECT 
                    qa.id AS answer_id, 
                    qa.question_id, 
                    q.question, 
                    qa.answer, 
                    qa.collect_point, 
                    qo.options AS correct_option, 
                    qo.is_correct,
                    (CASE WHEN qa.answer = qo.id AND qo.is_correct = 1 THEN 1 ELSE 0 END) AS is_correct_answer
                 FROM ${req.user.db_name}.question_answer qa
                 INNER JOIN ${req.user.db_name}.questions q ON qa.question_id = q.id
                 LEFT JOIN ${req.user.db_name}.question_options qo ON q.id = qo.question_id AND qo.is_correct = 1
                 WHERE q.assessment_id = ? AND qa.user_id = ?`,
        [module.assessment_id, module.user_id]
      );

      module.completed_questions = completedQuestions; // Add detailed completed questions to each module

      // Calculate correct answers and pass/fail status
      const correctAnswersCount = completedQuestions.reduce(
        (count, question) => count + question.is_correct_answer,
        0
      );
      const totalQuestions = module.question_count;

      const correctPercentage = (correctAnswersCount / totalQuestions) * 100;
      module.result = correctPercentage >= 60 ? "Pass" : "Fail";
      module.correct_percentage = correctPercentage.toFixed(2);
    }

    // Fetch total count for pagination metadata
    const totalCountQueryParams = search
      ? [classId || courseId, search, search]
      : [classId || courseId];

    const [[{ count }]] = await mysqlServerConnection.query(
      `SELECT COUNT(*) AS count 
             FROM ${req.user.db_name}.assessment_users a
             LEFT JOIN ${req.user.db_name}.assessments ass ON a.assessment_id = ass.id
             WHERE ${condition} ${searchCondition}`,
      totalCountQueryParams
    );

    // Calculate total pages
    const totalPages = Math.ceil(count / limit);

    // Respond with data and pagination info
    res.status(200).json({
      success: true,
      data: {
        message: "Assessment data retrieved successfully.",
        response: {
          modules, // Now includes question details and pass/fail status
          pagination: {
            page,
            limit,
            totalPages,
            totalCount: count,
          },
        },
      },
    });
  } catch (error) {
    console.error("Error retrieving assessment data:", error);
    res
      .status(500)
      .json({ success: false, data: { error_msg: "Internal server error." } });
  }
};

const getQuestionsByAssessmentId = async (req, res) => {
  try {
    const { assessmentId } = req.body;
    const userId = req.user.userId;

    if (!assessmentId || !userId) {
      return res.status(400).json({
        success: false,
        data: {
          error_msg: "Assessment ID and User ID are required",
        },
      });
    }

    // Check if the user has already attempted any question in this assessment
    const [attemptedBefore] = await mysqlServerConnection.query(
      `SELECT COUNT(*) as attemptCount 
       FROM ${req.user.db_name}.question_answer 
       WHERE user_id = ? AND question_id IN (
         SELECT id FROM ${req.user.db_name}.questions WHERE assessment_id = ?
       )`,
      [userId, assessmentId]
    );
    const isFirstAttempt = attemptedBefore[0]?.attemptCount === 0;

    // Total question count
    const [totalQuestions] = await mysqlServerConnection.query(
      `SELECT COUNT(*) as total FROM ${req.user.db_name}.questions WHERE assessment_id = ?`,
      [assessmentId]
    );
    const totalQuestionCount = totalQuestions[0]?.total || 0;

    // Fetch assessment details
    const [assessmentDetails] = await mysqlServerConnection.query(
      `SELECT id, assessment_name, duration FROM ${req.user.db_name}.assessments WHERE id = ?`,
      [assessmentId]
    );

    if (assessmentDetails.length === 0) {
      return res.status(404).json({
        success: false,
        data: {
          error_msg: "Assessment not found",
        },
      });
    }

    // Get one unanswered question
    const query = `
      SELECT q.id, q.assessment_id, q.question, q.question_type, q.options, 
             q.question_content_type, q.question_media_url, q.correct_option
      FROM ${req.user.db_name}.questions q
      WHERE q.assessment_id = ?
      AND NOT EXISTS (
          SELECT 1 FROM ${req.user.db_name}.question_answer qa
          WHERE qa.user_id = ? AND qa.question_id = q.id
      )
      ORDER BY RAND()
      LIMIT 1
    `;
    const [questions] = await mysqlServerConnection.query(query, [
      assessmentId,
      userId,
    ]);

    // Count of answered questions
    const [answeredQuestions] = await mysqlServerConnection.query(
      `SELECT COUNT(*) as answeredCount 
       FROM ${req.user.db_name}.question_answer 
       WHERE user_id = ? 
       AND question_id IN (
         SELECT id FROM ${req.user.db_name}.questions WHERE assessment_id = ?
       )`,
      [userId, assessmentId]
    );
    const answeredCount = answeredQuestions[0]?.answeredCount || 0;
    const remainingQuestions = totalQuestionCount - answeredCount;

    // If assessment is completed
    if (questions.length === 0 && remainingQuestions === 0) {
      const [correctAnswers] = await mysqlServerConnection.query(
        `SELECT COUNT(*) AS correct FROM ${req.user.db_name}.question_answer 
         WHERE user_id = ? 
         AND question_id IN (
           SELECT id FROM ${req.user.db_name}.questions WHERE assessment_id = ?
         ) 
         AND is_correct = 1`,
        [userId, assessmentId]
      );

      const totalCorrectAnswers = correctAnswers[0]?.correct || 0;
      const completionPercentage =
        (totalCorrectAnswers / totalQuestionCount) * 100;
      const isCompleted = completionPercentage >= 60;

      return res.status(200).json({
        success: true,
        data: {
          assessment: {
            id: assessmentDetails[0].id,
            assessment_name: assessmentDetails[0].assessment_name,
            duration: assessmentDetails[0].duration,
          },
          totalQuestions: totalQuestionCount,
          totalCorrectAnswers: totalCorrectAnswers,
          isCompleted: isCompleted,
          message: "Assessment completed successfully",
          completionPercentage,
          trainee_first_attempt: isFirstAttempt,
        },
      });
    }

    // Prepare question and options
    const question = questions[0];
    let formattedOptions = {};

    if (question.options) {
      const parsedOptions = JSON.parse(question.options);

      try {
        const [optionDetails] = await mysqlServerConnection.query(
          `SELECT id, question_id, option_key, content_type, media_url, content_value, text_fallback 
           FROM ${req.user.db_name}.question_options 
           WHERE question_id = ?`,
          [question.id]
        );

        if (optionDetails && optionDetails.length > 0) {
          optionDetails.forEach((detail) => {
            formattedOptions[detail.option_key] = {
              id: detail.id.toString(),
              text: detail.text_fallback || detail.content_value,
              content_type: detail.content_type || "text",
              media_url: detail.media_url || null,
            };
          });
        } else {
          Object.entries(parsedOptions).forEach(([key, value]) => {
            formattedOptions[key] = {
              id: key,
              text: value,
              content_type: "text",
              media_url: null,
            };
          });
        }
      } catch (error) {
        console.error("Error fetching option details:", error);
        Object.entries(parsedOptions).forEach(([key, value]) => {
          formattedOptions[key] = {
            id: key,
            text: value,
            content_type: "text",
            media_url: null,
          };
        });
      }
    }

    return res.status(200).json({
      success: true,
      data: {
        assessment: {
          id: assessmentDetails[0].id,
          assessment_name: assessmentDetails[0].assessment_name,
          duration: assessmentDetails[0].duration,
        },
        question: {
          id: question.id,
          assessment_id: question.assessment_id,
          question_text: question.question,
          question_type: question.question_type,
          question_content_type: question.question_content_type || "text",
          question_media_url: question.question_media_url,
          options: formattedOptions,
          correct_option: question.correct_option
            ? JSON.parse(question.correct_option)
            : [],
        },
        totalQuestions: totalQuestionCount,
        answeredQuestions: answeredCount,
        remainingQuestions: remainingQuestions,
        trainee_first_attempt: isFirstAttempt,
      },
    });
  } catch (error) {
    console.error("Error in getQuestionsByAssessmentId:", error);
    return res.status(500).json({
      success: false,
      data: {
        error_msg: "Internal server error.",
      },
    });
  }
};


// const getSurveyQuestionsByAssessmentId = async (req, res) => {
//   try {
//     const { assessmentId } = req.body; // Read assessmentId from the request body
//     const userId = req.user.userId;

//     if (!assessmentId || !userId) {
//       return res.status(400).json({
//         success: false,
//         data: {
//           error_msg: "Assessment ID and User ID are required",
//         },
//       });
//     }

//     // Query to fetch assessment details to check if it is a survey
//     const [assessmentDetails] = await mysqlServerConnection.query(
//       `SELECT id, assessment_name, duration, assessment_type
//              FROM ${req.user.db_name}.assessments 
//              WHERE id = ?`,
//       [assessmentId]
//     );

//     // Check if the assessment exists and is a survey
//     if (
//       assessmentDetails.length === 0 ||
//       assessmentDetails[0].assessment_type !== "survey"
//     ) {
//       return res.status(404).json({
//         success: false,
//         data: {
//           error_msg:
//             "Survey not found or this is not a survey type assessment.",
//         },
//       });
//     }

//     // Query to get total question count for the specific survey
//     const [totalQuestions] = await mysqlServerConnection.query(
//       `SELECT COUNT(*) as total FROM ${req.user.db_name}.questions WHERE assessment_id = ?`,
//       [assessmentId]
//     );
//     const totalQuestionCount = totalQuestions[0]?.total || 0;

//     const query = `
//         SELECT id, assessment_id, question, question_type, options
//         FROM ${req.user.db_name}.questions q
//         WHERE q.assessment_id = ?
//         AND 
//            (
//             NOT EXISTS (
//                 SELECT 1 
//                 FROM ${req.user.db_name}.question_answer qa
//                 WHERE qa.user_id = ?
//                 AND qa.question_id = q.id
//                 AND qa.is_correct = 1
//             )
//             )
       
        
//         ORDER BY RAND()
//         LIMIT 1
//     `;

//     const [questions] = await mysqlServerConnection.query(query, [
//       assessmentId,
//       userId,
//     ]);
//     console.log("questions--------------", questions);

//     const [answeredQuestions] = await mysqlServerConnection.query(
//       `SELECT COUNT(*) as answeredCount 
//              FROM ${req.user.db_name}.question_answer 
//              WHERE user_id = ? AND is_correct = 1 AND question_id IN (SELECT id FROM ${req.user.db_name}.questions WHERE assessment_id = ?)`,
//       [userId, assessmentId]
//     );

//     const remainingQuestions =
//       totalQuestionCount - (answeredQuestions[0]?.answeredCount || 0);

//     if (remainingQuestions === 0) {
//       await mysqlServerConnection.query(
//         `UPDATE ${req.user.db_name}.assessment_users 
//                  SET is_completed = 1 
//                  WHERE user_id = ? AND assessment_id = ?`,
//         [userId, assessmentId]
//       );

//       return res.status(200).json({
//         success: true,
//         data: {
//           completed: true,
//           message: "You have completed the survey.",
//           assessment: {
//             id: assessmentDetails[0].id,
//             assessment_name: assessmentDetails[0].assessment_name,
//           },
//         },
//       });
//     }

//     // Get a single shuffled survey question
//     const question = questions[0];

//     // Format the question with its options
//     const questionWithOptions = {
//       ...question,
//     };

//     // Return the result with survey question
//     return res.status(200).json({
//       success: true,
//       data: {
//         completed: false,
//         assessment: {
//           id: assessmentDetails[0].id,
//           assessment_name: assessmentDetails[0].assessment_name,
//           duration: assessmentDetails[0].duration,
//         },
//         totalQuestions: totalQuestionCount,
//         remainingQuestions,
//         question: questionWithOptions,
//       },
//     });
//   } catch (error) {
//     console.error("Error retrieving survey questions:", error);
//     return res.status(500).json({
//       success: false,
//       data: {
//         error_msg: "Internal server error.",
//       },
//     });
//   }
// };

const getSurveyQuestionsByAssessmentId = async (req, res) => {
  try {
    const { assessmentId } = req.body;
    const userId = req.user.userId;
    const dbName = req.user.db_name;

    if (!assessmentId || !userId) {
      return res.status(400).json({
        success: false,
        data: { error_msg: "Assessment ID and User ID are required" },
      });
    }

    // 🔹 Get assessment title
    const [assessmentTitleResult] = await mysqlServerConnection.query(
      `SELECT assessment_name FROM ${dbName}.assessments WHERE id = ?`,
      [assessmentId]
    );
    const assessmentTitle = assessmentTitleResult?.[0]?.assessment_name || '';

    // ✅ Step 0: Check if user has already submitted answers for this assessment
    const attendanceQuery = `
      SELECT COUNT(*) AS count
      FROM ${dbName}.question_answer
      WHERE assessment_id = ? AND user_id = ?
    `;
    const [attendanceResult] = await mysqlServerConnection.query(attendanceQuery, [assessmentId, userId]);
    const isAttended = attendanceResult[0].count > 0 ? "yes" : "no";

    // Step 1: Get all questions
    const questionQuery = `
      SELECT id, question
      FROM ${dbName}.questions
      WHERE assessment_id = ? AND is_deleted = 0
    `;
    const [questions] = await mysqlServerConnection.query(questionQuery, [assessmentId]);

    if (!Array.isArray(questions) || questions.length === 0) {
      return res.status(200).json({
        success: true,
        data: {
          assessment_title: assessmentTitle,
          total_questions: 0,
          questions: [],
          is_attended: isAttended
        }
      });
    }

    // Step 2: Extract question IDs
    const questionIds = questions.map(q => q.id);
    const placeholders = questionIds.map(() => '?').join(',');

    // Step 3: Fetch options with id
    const optionQuery = `
      SELECT id, question_id, option_key, content_value
      FROM ${dbName}.question_options
      WHERE question_id IN (${placeholders})
    `;
    const [options] = await mysqlServerConnection.query(optionQuery, questionIds);

    // Step 4: Group options by question_id
    const optionsMap = {};
    options.forEach(opt => {
      if (!optionsMap[opt.question_id]) optionsMap[opt.question_id] = [];
      optionsMap[opt.question_id].push({
        id: opt.id,
        key: opt.option_key,
        value: opt.content_value
      });
    });

    // Step 5: Combine question + options
    const questionList = questions.map(q => ({
      id: q.id,
      question: q.question,
      options: optionsMap[q.id] || []
    }));

    // Step 6: Return final structure with assessment title
    return res.status(200).json({
      success: true,
      data: {
        assessment_title: assessmentTitle,
        total_questions: questionList.length,
        questions: questionList,
        is_attended: isAttended
      }
    });

  } catch (error) {
    console.error("🔥 Error in getSurveyQuestionsByAssessmentId:", error);
    return res.status(500).json({
      success: false,
      data: { error_msg: "Internal server error." },
    });
  }
};




// const submitSurveyAnswers = async (req, res) => {
//   try {
//     const userId = req.user.userId; // Read user ID from the request context (authentication middleware)
//     const { assessment_id, options, question_id } = req.body; // Read assessment_id and answers from the request body

//     console.log("survey answers : ", req.body);

//     // Validate input
//     if (!assessment_id || !options || options.length === 0) {
//       return res.status(400).json({
//         success: false,
//         data: {
//           error_msg: "Assessment ID and Options are required.",
//         },
//       });
//     }

//     await mysqlServerConnection.query(
//       `INSERT INTO ${req.user.db_name}.assessment_users (assessment_id, user_id, status, is_eligible)
//                 SELECT ?, ?, ?, ?
//                 FROM DUAL
//                 WHERE NOT EXISTS (
//                     SELECT 1 FROM ${req.user.db_name}.assessment_users 
//                     WHERE assessment_id = ? AND user_id = ?
//                 );`,
//       [assessment_id, userId, 1, 1, assessment_id, userId]
//     );
//     // Fetch assessment details to check if it's a survey
//     const [assessmentDetails] = await mysqlServerConnection.query(
//       `SELECT assessment_type FROM ${req.user.db_name}.assessments WHERE id = ? AND assessment_type = 'survey'`,
//       [assessment_id]
//     );

//     if (assessmentDetails.length === 0) {
//       return res.status(400).json({
//         success: false,
//         data: {
//           error_msg: "Invalid assessment ID provided",
//         },
//       });
//     }

//     const [existingAnswer] = await mysqlServerConnection.query(
//       `SELECT id, answer, is_correct FROM ${req.user.db_name}.question_answer WHERE question_id = ? AND user_id = ?`,
//       [question_id, userId]
//     );

//     if (existingAnswer.length > 0) {
//       const { id, is_correct } = existingAnswer[0];

//       if (is_correct === 0) {
//         await mysqlServerConnection.query(
//           `UPDATE ${req.user.db_name}.question_answer SET answer = ?, permanent_answer = ?, is_correct = ? WHERE id = ?`,
//           [JSON.stringify(options), JSON.stringify(options), 1, id]
//         );
//         return res.status(200).json({
//           success: true,
//           data: {
//             message: "Your answer has been updated.",
//           },
//         });
//       } else {
//         return res.status(400).json({
//           success: false,
//           data: {
//             error_msg:
//               "You have already submitted a correct answer for this question.",
//           },
//         });
//       }
//     } else {
//       // If no previous answer exists, we insert a new record
//       await mysqlServerConnection.query(
//         `INSERT INTO ${req.user.db_name}.question_answer (collect_point,is_correct, assessment_id, user_id, question_id, answer, permanent_answer) VALUES (0,1,?, ?, ?, ?, ?)`,
//         [
//           assessment_id,
//           userId,
//           question_id,
//           JSON.stringify(options),
//           JSON.stringify(options),
//         ]
//       );
//     }

//     const completedAssessments = await mysqlServerConnection.query(
//       `SELECT COUNT(*) as completed FROM ${req.user.db_name}.question_answer WHERE assessment_id = ? AND user_id = ? AND is_correct = 0`,
//       [assessment_id, userId]
//     );

//     console.log(completedAssessments, "completedAssessments");

//     return res.status(200).json({
//       success: true,
//       data: {
//         message: "Survey answers submitted successfully.",
//       },
//     }); 


//   } catch (error) {
//     console.error("Error submitting survey answers:", error);
//     return res.status(500).json({
//       success: false,
//       data: {
//         error_msg: "Internal server error.",
//       },
//     });
//   }
// };


const submitSurveyAnswers = async (req, res) => {
  console.log("submitSurveyAnswers----------------------------------------", req.body);
  try {
    const userId = req.user.userId;
    const dbName = req.user.db_name;
    const answers = req.body;

    if (!Array.isArray(answers) || answers.length === 0) {
      return res.status(400).json({
        success: false,
        data: { error_msg: "No answers submitted." },
      });
    }

    const insertQuery = `
      INSERT INTO ${dbName}.question_answer (
        question_id, user_id, answer, assessment_id,
        collect_point, is_correct, createdAt, updatedAt
      )
      VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())
    `;

    for (const item of answers) {
      const { assessment_id, question_id, selected_option_id, selected_value } = item;

      // Fetch the correct answer for the current question from the `question_options` table
      const correctAnswerQuery = `
        SELECT is_correct 
        FROM ${dbName}.question_options 
        WHERE question_id = ? AND option_key = ? LIMIT 1
      `;
      
      const [correctAnswerRows] = await mysqlServerConnection.query(correctAnswerQuery, [
        question_id,
        selected_value // or selected_option_id, depending on your logic
      ]);

      let answer = selected_value.trim() !== '' ? selected_value : null;
      let isCorrect = null;

      if (selected_option_id === null && answer === null) {
        answer = null;
        isCorrect = null;
      } else {
        isCorrect = (correctAnswerRows.length > 0 && correctAnswerRows[0].is_correct === 1) ? 1 : 0;
      }

      await mysqlServerConnection.query(insertQuery, [
        question_id,
        userId,
        answer,
        assessment_id,
        0,
        isCorrect
      ]);
    }

    // ✅ Insert assessment_users record only if not already inserted
    const { assessment_id } = answers[0];
    const [[existingStatus]] = await mysqlServerConnection.query(
      `SELECT id FROM ${dbName}.assessment_users WHERE user_id = ? AND assessment_id = ?`,
      [userId, assessment_id]
    );

    if (!existingStatus) {
      await mysqlServerConnection.query(
        `INSERT INTO ${dbName}.assessment_users 
         (user_id, assessment_id, status, is_completed, is_eligible, is_passed, createdAt, updatedAt)
         VALUES (?, ?, 1, 1, 1, 1, NOW(), NOW())`,
        [userId, assessment_id]
      );
    }

    return res.status(200).json({
      success: true,
      data: { message: "Thank you for submitting your feedback." },
    });

  } catch (error) {
    console.error("Error submitting survey answers:", error);
    return res.status(500).json({
      success: false,
      data: { error_msg: "Internal server error." },
    });
  }
};





const RestartSurvey = async (req, res) => {
  try {
    const userId = req.user.userId;
    const { assessment_id } = req.body;

    console.log("survey answers:", req.body);

    // Validate input
    if (!assessment_id) {
      return res.status(400).json({
        success: false,
        data: {
          error_msg: "Assessment ID is required.",
          response: { assessment_id: "" },
        },
      });
    }

    // Fetch all records matching the assessment_id and user_id
    const [records] = await mysqlServerConnection.query(
      `SELECT id 
            FROM ${req.user.db_name}.question_answer 
            WHERE assessment_id = ? AND user_id = ?`,
      [assessment_id, userId]
    );

    console.log("records", records);

    if (records.length === 0) {
      return res.status(404).json({
        success: false,
        data: {
          message: "No matching records found.",
        },
      });
    }

    // Update each record's is_correct field
    for (const record of records) {
      console.log("record", record);
      await mysqlServerConnection.query(
        `UPDATE ${req.user.db_name}.question_answer 
                SET is_correct = 0 
                WHERE id = ?`,
        [record.id]
      );
      console.log(`Updated record with ID: ${record.id}`);
    }

    return res.status(200).json({
      success: true,
      data: {
        message: "Survey answers reset successfully.",
        updatedRecords: records.length,
      },
    });
  } catch (error) {
    console.error("Error resetting survey answers:", error);
    return res.status(500).json({
      success: false,
      data: {
        error_msg: "Internal server error.",
      },
    });
  }
};

const restartQuestion = async (req, res) => {
  try {
    const userId = req.user.userId; // Read user ID from the request context (authentication middleware)
    const { assessment_id } = req.body; // Read assessment_id and answers from the request body
    // Validate input
    if (!assessment_id) {
      return res.status(400).json({
        success: false,
        data: {
          error_msg: "Assessment ID are required",
        },
      });
    }
    await mysqlServerConnection.query(
      `DELETE FROM ${req.user.db_name}.question_answer WHERE user_id = ? AND question_id IN (SELECT id FROM ${req.user.db_name}.questions WHERE assessment_id = ?)`,
      [userId, assessment_id]
    );

    return res.status(200).json({
      success: true,
      data: {
        message: "Sussessfully restart assessment",
      },
    });
  } catch (error) {
    console.error("Error submitting answers:", error);
    return res.status(500).json({
      success: false,
      data: {
        error_msg: "Internal server error.",
      },
    });
  }
};

const getAllAssessmentDataByClassId = async (req, res) => {
  try {
    // Get page, limit, class_id, and search from request body and query
    const page = parseInt(req.body.page, 10) || 1; // Default to page 1 if not provided
    const limit = parseInt(req.body.limit, 10) || 10; // Default to 10 items per page
    const search = req.body.search ? `%${req.body.search}%` : ""; // Default to empty string if not provided
    const classId = req.body.class_id ? parseInt(req.body.class_id, 10) : null; // Get class_id from request body (it can be null)

    console.log("CLASS_ID--->", classId);

    // Validate class_id if it's not null
    if (classId && isNaN(classId)) {
      return res
        .status(400)
        .json({ success: false, data: { error_msg: "Invalid class_id." } });
    }

    // Calculate the offset for pagination
    const offset = (page - 1) * limit;

    // Construct the search condition for `status` or `assessment_name`
    const searchCondition = search
      ? "AND (a.status LIKE ? OR ass.assessment_name LIKE ?)"
      : "";
    const classCondition = classId ? "AND ass.class_id = ?" : ""; // If class_id is provided, add it to the query

    // console.log("CLASS CONDITION", classCondition);

    // Fetch assessment data based on class_id with pagination, search, and class_id filter (if provided)
    const [modules] = await mysqlServerConnection.query(
      `SELECT 
                a.id, 
                u.name AS created_by_name, 
                a.assessment_id, 
                ass.assessment_name, 
                a.user_id, 
                a.status, 
                a.is_eligible, 
                a.createdAt,
                ass.class_id,
                (SELECT COUNT(*) FROM ${req.user.db_name}.questions aq WHERE aq.assessment_id = a.assessment_id) AS question_count,
                (SELECT COUNT(*) 
                 FROM ${req.user.db_name}.question_answer qa 
                 INNER JOIN ${req.user.db_name}.questions q ON qa.question_id = q.id 
                 WHERE q.assessment_id = a.assessment_id AND qa.user_id = a.user_id) AS completed_question_count
             FROM ${req.user.db_name}.assessment_users a
             LEFT JOIN ${req.user.db_name}.users u ON a.user_id = u.id
             LEFT JOIN ${req.user.db_name}.assessments ass ON a.assessment_id = ass.id
             WHERE 1=1 ${searchCondition} ${classCondition}
             LIMIT ? OFFSET ?`,
      search
        ? [search, search, classId, limit, offset]
        : [classId, limit, offset]
    );

    console.log("ASSESSMENT MODULES", modules);

    // Add completed question details and check pass/fail status for each assessment
    for (const module of modules) {
      const [completedQuestions] = await mysqlServerConnection.query(
        `SELECT 
                    qa.id AS answer_id, 
                    qa.question_id, 
                    q.question, 
                    qa.answer, 
                    qa.collect_point, 
                    qo.options AS correct_option, 
                    qo.is_correct,
                    (CASE WHEN qa.answer = qo.id AND qo.is_correct = 1 THEN 1 ELSE 0 END) AS is_correct_answer
                 FROM ${req.user.db_name}.question_answer qa
                 INNER JOIN ${req.user.db_name}.questions q ON qa.question_id = q.id
                 LEFT JOIN ${req.user.db_name}.question_options qo ON q.id = qo.question_id AND qo.is_correct = 1
                 WHERE q.assessment_id = ? AND qa.user_id = ?`,
        [module.assessment_id, module.user_id]
      );

      module.completed_questions = completedQuestions; // Add detailed completed questions to each module

      // Calculate the number of correct answers
      const correctAnswersCount = completedQuestions.reduce(
        (count, question) => count + question.is_correct_answer,
        0
      );
      const totalQuestions = module.question_count;

      // Determine the percentage of correct answers
      const correctPercentage = (correctAnswersCount / totalQuestions) * 100;

      // Determine pass or fail
      module.result = correctPercentage >= 60 ? "Pass" : "Fail";
      module.correct_percentage = correctPercentage.toFixed(2); // Optionally add percentage to the response
    }

    // Fetch total count for pagination metadata
    const [[{ count }]] = await mysqlServerConnection.query(
      `SELECT COUNT(*) AS count 
             FROM ${req.user.db_name}.assessment_users a
             LEFT JOIN ${req.user.db_name}.assessments ass ON a.assessment_id = ass.id
             WHERE 1=1 ${searchCondition} ${classCondition}`,
      search ? [search, search, classId] : [classId]
    );

    // Calculate total pages
    const totalPages = Math.ceil(count / limit);

    // Respond with data and pagination info
    res.status(200).json({
      success: true,
      data: {
        success: "true",
        data: {
          modules,
          pagination: {
            page,
            limit,
            totalPages,
            totalCount: count,
          },
        },
      },
    });
  } catch (error) {
    console.error("Error retrieving assessment data:", error);
    res
      .status(500)
      .json({ success: false, data: { error_msg: "Internal server error." } });
  }
};

const getCompleteAndPendingAssessments = async (req, res) => {
  const { class_id } = req.params;

  if (!class_id) {
    res.status(400).json({
      success: false,
      message: "Please provide Class Id",
    });
  }

  try {
    const [totalAssessmentCount] = await mysqlServerConnection.query(
      `
        SELECT COUNT(*) as assessmentCount
        FROM ${req.user.db_name}.assessments
        WHERE class_id = ?
      `,
      [class_id]
    );

    // Query to get the completed assignments count
    const [completedAssessments] = await mysqlServerConnection.query(
      `
        SELECT COUNT(*) as completed_assessments
        FROM ${req.user.db_name}.assessment_users
        WHERE class_id = ? AND user_id = ? AND status = '1'
      `,
      [class_id, req.user.userId]
    );

    // Extracting counts from the results
    const total_assessments = totalAssessmentCount[0].assessmentCount;
    const completed_assessments = completedAssessments[0].completed_assessments;

    // Calculate pending assignments
    const pending_assessments = total_assessments - completed_assessments;

    // Return the result
    res.status(200).json({
      success: true,
      data: {
        total: total_assessments,
        completed: completed_assessments,
        pending: pending_assessments,
      },
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: error.message || "Internal Server Error",
    });
  }
};

const getAssessmentResults = async (req, res) => {
  try {
    const userId = req.user.userId;
    const { assessment_id } = req.params;

    if (!assessment_id) {
      return res.status(400).json({
        success: false,
        data: {
          error_msg: "Assessment ID is required",
        },
      });
    }

    // Get assessment details including pass_percentage
    const [assessmentDetails] = await mysqlServerConnection.query(
      `SELECT pass_percentage FROM ${req.user.db_name}.assessments WHERE id = ?`,
      [assessment_id]
    );

    if (assessmentDetails.length === 0) {
      return res.status(404).json({
        success: false,
        data: {
          error_msg: "Assessment not found",
        },
      });
    }

    const { pass_percentage } = assessmentDetails[0];

    // Get total questions count
    const [totalQuestionsCount] = await mysqlServerConnection.query(
      `SELECT COUNT(*) as total FROM ${req.user.db_name}.questions WHERE assessment_id = ?`,
      [assessment_id]
    );

    // Get correct answers count
    const [correctAnswersCount] = await mysqlServerConnection.query(
      `SELECT COUNT(*) as correct FROM ${req.user.db_name}.question_answer 
             WHERE assessment_id = ? AND user_id = ? AND is_correct = 1`,
      [assessment_id, userId]
    );

    // Get total answered questions count
    const [answeredQuestions] = await mysqlServerConnection.query(
      `SELECT COUNT(*) as answered FROM ${req.user.db_name}.question_answer 
             WHERE assessment_id = ? AND user_id = ?`,
      [assessment_id, userId]
    );

    // Calculate score percentage
    const scorePercentage =
      totalQuestionsCount[0].total > 0
        ? Math.round(
            (correctAnswersCount[0].correct / totalQuestionsCount[0].total) *
              100
          )
        : 0;

    // Check if assessment is completed and passed
    const [assessmentStatus] = await mysqlServerConnection.query(
      `SELECT is_completed, is_passed FROM ${req.user.db_name}.assessment_users 
             WHERE assessment_id = ? AND user_id = ?`,
      [assessment_id, userId]
    );

    const isCompleted =
      assessmentStatus.length > 0
        ? assessmentStatus[0].is_completed === 1
        : false;
    const isPassed =
      assessmentStatus.length > 0 ? assessmentStatus[0].is_passed === 1 : false;

    return res.status(200).json({
      success: true,
      data: {
        isCompleted,
        isPassed,
        totalQuestions: totalQuestionsCount[0].total,
        totalCorrectAnswers: correctAnswersCount[0].correct,
        answeredQuestions: answeredQuestions[0].answered,
        score: scorePercentage,
        passingScore: pass_percentage || 60,
      },
    });
  } catch (error) {
    console.error("Error fetching assessment results:", error);
    return res.status(500).json({
      success: false,
      data: {
        error_msg: "Internal server error",
      },
    });
  }
};

const getAssessmentResultsDetailed = async (req, res) => {
  try {
    const userId = req.user.userId;
    const { assessment_id } = req.params;

    if (!assessment_id) {
      return res.status(400).json({
        success: false,
        data: {
          error_msg: "Assessment ID is required",
        },
      });
    }

    // Get assessment details with course information
    const [assessmentDetails] = await mysqlServerConnection.query(
      `SELECT a.assessment_name, a.pass_percentage, a.duration, a.course_id, c.course_name
       FROM ${req.user.db_name}.assessments a
       LEFT JOIN ${req.user.db_name}.courses c ON a.course_id = c.id
       WHERE a.id = ?`,
      [assessment_id]
    );

    if (assessmentDetails.length === 0) {
      return res.status(404).json({
        success: false,
        data: {
          error_msg: "Assessment not found",
        },
      });
    }

    // Get questions with user answers and video references
    const [questionsWithAnswers] = await mysqlServerConnection.query(
      `SELECT
          q.id as question_id,
          q.question,
          q.question_type,
          q.options,
          q.correct_option,
          q.question_content_type,
          q.question_media_url,
          q.ref_video_id,
          q.video_timestamp,
          v.video_name,
          v.video_title,
          v.video_url,
          qa.answer as user_answer,
          qa.is_correct as user_is_correct,
          qa.createdAt as answered_at
        FROM ${req.user.db_name}.questions q
        LEFT JOIN ${req.user.db_name}.question_answer qa ON q.id = qa.question_id AND qa.user_id = ?
        LEFT JOIN ${req.user.db_name}.videos v ON q.ref_video_id = v.id
        WHERE q.assessment_id = ?
        ORDER BY q.id ASC`,
      [userId, assessment_id]
    );

    // Fetch all options from question_options table, including option ID
    const [questionOptions] = await mysqlServerConnection.query(
      `SELECT id, question_id, option_key, content_type, media_url, content_value, text_fallback
       FROM ${req.user.db_name}.question_options`
    );

    // Group options by question_id
    const optionsMap = {};
    for (const option of questionOptions) {
      if (!optionsMap[option.question_id]) {
        optionsMap[option.question_id] = [];
      }
      optionsMap[option.question_id].push({
        id: option.id,
        option_key: option.option_key,
        content_type: option.content_type,
        media_url: option.media_url,
        content_value: option.content_value,
        text_fallback: option.text_fallback
      });
    }

    // Inject parsed options and correct options into each question
    for (let question of questionsWithAnswers) {
      if (question.question_type !== "written") {
        try {
          question.correct_option = JSON.parse(question.correct_option || '[]');
        } catch (e) {
          console.error("Error parsing correct_option:", e);
        }

        question.options = optionsMap[question.question_id] || [];
      }
    }

    // Calculate summary statistics
    const totalQuestions = questionsWithAnswers.length;
    const answeredQuestions = questionsWithAnswers.filter(q => q.user_answer !== null).length;
    const correctAnswers = questionsWithAnswers.filter(q => q.user_is_correct === 1).length;
    const scorePercentage =
      totalQuestions > 0 ? Math.round((correctAnswers / totalQuestions) * 100) : 0;
    const passed = scorePercentage >= (assessmentDetails[0].pass_percentage || 60);

    return res.status(200).json({
      success: true,
      data: {
        assessment: {
          id: assessment_id,
          name: assessmentDetails[0].assessment_name,
          duration: assessmentDetails[0].duration,
          pass_percentage: assessmentDetails[0].pass_percentage || 60,
          course_id: assessmentDetails[0].course_id,
          course_name: assessmentDetails[0].course_name,
        },
        summary: {
          totalQuestions,
          answeredQuestions,
          correctAnswers,
          scorePercentage,
          passed,
        },
        questions: questionsWithAnswers,
      },
    });
  } catch (error) {
    console.error("Error fetching detailed assessment results:", error);

    return res.status(500).json({
      success: false,
      data: {
        error_msg: "Internal server error",
      },
    });
  }
};

// const getCourseAssessment = async (req, res) => {
//   try {
//     const { assessment_id } = req.body;
//     const userId = req.user.userId;

//     console.log("assessment_id:", assessment_id);
//     console.log("userId:", userId);

//     if (!assessment_id || !userId) {
//       return res.status(400).json({
//         success: false,
//         message: "Assessment ID and User ID are required",
//       });
//     }

//     // 1. Check if user has attempted the assessment
//     const [attemptRows] = await mysqlServerConnection.query(
//       `SELECT COUNT(*) as count 
//        FROM ${req.user.db_name}.question_answer 
//        WHERE assessment_id = ? AND user_id = ?`,
//       [assessment_id, userId]
//     );
//     const is_attempted = attemptRows[0]?.count > 0 ? 1 : 0;

//     // 2. Fetch assessment duration and pass percentage
//     const [assessmentRows] = await mysqlServerConnection.query(
//       `SELECT duration, pass_percentage 
//        FROM ${req.user.db_name}.assessments 
//        WHERE id = ? AND is_deleted = 0`,
//       [assessment_id]
//     );

//     if (!assessmentRows.length) {
//       return res.status(404).json({
//         success: false,
//         message: "Assessment not found"
//       });
//     }

//     const { duration, pass_percentage } = assessmentRows[0];

//     // 3. Fetch questions
//     const [questions] = await mysqlServerConnection.query(
//       `SELECT id, question, question_content_type, question_media_url
//        FROM ${req.user.db_name}.questions
//        WHERE assessment_id = ? AND is_deleted = 0`,
//       [assessment_id]
//     );

//     if (!questions.length) {
//       return res.status(404).json({
//         success: false,
//         message: "No questions found for this assessment.",
//         is_attempted,
//         duration,
//         pass_percentage
//       });
//     }

//     // 4. Fetch options
//     const questionIds = questions.map(q => q.id);
//     const [options] = await mysqlServerConnection.query(
//       `SELECT id, question_id, content_type, content_value, media_url, is_correct
//        FROM ${req.user.db_name}.question_options
//        WHERE question_id IN (?)`,
//       [questionIds]
//     );

//     // 5. Group options by question
//     const optionsMap = {};
//     options.forEach(opt => {
//       if (!optionsMap[opt.question_id]) {
//         optionsMap[opt.question_id] = [];
//       }
//       optionsMap[opt.question_id].push({
//         id: opt.id,
//         content_type: opt.content_type,
//         content_value: opt.content_value,
//         media_url: opt.media_url,
//         is_correct: opt.is_correct === 1
//       });
//     });

//     // 6. Attach options to questions
//     const questionList = questions.map(q => ({
//       id: q.id,
//       question: q.question,
//       question_content_type: q.question_content_type,
//       question_media_url: q.question_media_url,
//       options: optionsMap[q.id] || []
//     }));

//     // 7. Return final response
//     return res.status(200).json({
//       success: true,
//       is_attempted,
//       duration,
//       pass_percentage,
//       data: questionList
//     });

//   } catch (error) {
//     console.error("Error:", error);
//     return res.status(500).json({
//       success: false,
//       message: "Internal server error"
//     });
//   }
// };


const getCourseAssessment = async (req, res) => {
  try {
    const { assessment_id } = req.body;
    const userId = req.user.userId;

    if (!assessment_id || !userId) {
      return res.status(400).json({
        success: false,
        message: "Assessment ID and User ID are required"
      });
    }

    // 1. Check if attempted
    const [[attemptCheck]] = await mysqlServerConnection.query(
      `SELECT COUNT(*) AS count 
       FROM ${req.user.db_name}.question_answer 
       WHERE assessment_id = ? AND user_id = ?`,
      [assessment_id, userId]
    );
    const is_attempted = attemptCheck.count > 0 ? 1 : 0;

    // 2. Get assessment metadata
    const [[assessmentMeta]] = await mysqlServerConnection.query(
      `SELECT duration, pass_percentage 
       FROM ${req.user.db_name}.assessments 
       WHERE id = ? AND is_deleted = 0`,
      [assessment_id]
    );

    if (!assessmentMeta) {
      return res.status(404).json({
        success: false,
        message: "Assessment not found"
      });
    }

    const { duration, pass_percentage } = assessmentMeta;

    // 3. Get questions
    const [questionRows] = await mysqlServerConnection.query(
      `SELECT id, question, question_content_type, question_media_url 
       FROM ${req.user.db_name}.questions 
       WHERE assessment_id = ? AND is_deleted = 0`,
      [assessment_id]
    );

    const questionIds = questionRows.map(q => q.id);
    const total_questions = questionIds.length;

    if (total_questions === 0) {
      return res.status(404).json({
        success: false,
        is_attempted,
        duration,
        pass_percentage,
        total_questions: 0,
        result: null,
        message: "No questions found for this assessment"
      });
    }

    // 4. Get options
    const [optionRows] = await mysqlServerConnection.query(
      `SELECT id, question_id, content_type, content_value, media_url, is_correct 
       FROM ${req.user.db_name}.question_options 
       WHERE question_id IN (?)`,
      [questionIds]
    );

    // 5. Map options
    const optionsMap = {};
    optionRows.forEach(opt => {
      if (!optionsMap[opt.question_id]) optionsMap[opt.question_id] = [];
      optionsMap[opt.question_id].push({
        id: opt.id,
        content_value: opt.content_value,
        content_type: opt.content_type,
        media_url: opt.media_url,
        is_correct: opt.is_correct === 1
      });
    });

    // 6. Build question data
    const data = questionRows.map(q => ({
      id: q.id,
      question: q.question,
      question_content_type: q.question_content_type,
      question_media_url: q.question_content_type === 'text' ? null : q.question_media_url,
      options: optionsMap[q.id] || []
    }));

    // 7. Build result if attempted
    let result = null;

    if (is_attempted) {
      // Get attempted questions (distinct question_id)
      const [[attemptedRow]] = await mysqlServerConnection.query(
        `SELECT COUNT(DISTINCT question_id) AS attempted_questions
         FROM ${req.user.db_name}.question_answer 
         WHERE assessment_id = ? AND user_id = ?`,
        [assessment_id, userId]
      );

      // Get correct answers
      const [[correctRow]] = await mysqlServerConnection.query(
        `SELECT COUNT(*) AS correct_answers
         FROM ${req.user.db_name}.question_answer 
         WHERE assessment_id = ? AND user_id = ? AND is_correct = 1`,
        [assessment_id, userId]
      );

      const attempted_questions = attemptedRow?.attempted_questions ?? 0;
      const correct_answers = correctRow?.correct_answers ?? 0;
      const wrong_answers = attempted_questions - correct_answers;

      const percentage = attempted_questions > 0
        ? Math.round((correct_answers / attempted_questions) * 100)
        : 0;

      const pass_status = percentage >= pass_percentage ? 'pass' : 'fail';

      result = {
        total_questions: attempted_questions,
        attempted_questions,
        correct_answers,
        wrong_answers,
        percentage,
        pass_status
      };
    }

    // 8. Final response
    return res.status(200).json({
      success: true,
      is_attempted,
      duration,
      pass_percentage,
      total_questions,
      result,
      data
    });

  } catch (error) {
    console.error("Error:", error);
    return res.status(500).json({
      success: false,
      message: "Internal server error"
    });
  }
};


const submitAssessmentWithResult = async (req, res) => {
  try {
    const { assessment_id, answers } = req.body;
    const user_id = req.user.userId;

    console.log("📩 Received submission:", {
      assessment_id,
      user_id,
      answers: answers.map(a => ({
        ...a,
        selected_option_id: JSON.stringify(a.selected_option_id),
        correct_option_id: JSON.stringify(a.correct_option_id),
        correct_value: JSON.stringify(a.correct_value)
      }))
    });

    if (!assessment_id || !Array.isArray(answers) || answers.length === 0) {
      console.warn("⚠️ Missing assessment_id or answers");
      return res.status(400).json({
        success: false,
        message: "Assessment ID and answer list are required"
      });
    }

    // 1. Check if user has already attempted
    const [[attemptStatus]] = await mysqlServerConnection.query(
      `SELECT COUNT(*) as count 
       FROM ${req.user.db_name}.question_answer 
       WHERE assessment_id = ? AND user_id = ?`,
      [assessment_id, user_id]
    );
    const is_attempted = attemptStatus.count > 0 ? 1 : 0;
    console.log("📝 Already attempted:", is_attempted);

    // 2. Delete old answers if previously attempted
    if (is_attempted) {
      console.log("🧹 Deleting previous answers...");
      await mysqlServerConnection.query(
        `DELETE FROM ${req.user.db_name}.question_answer 
         WHERE assessment_id = ? AND user_id = ?`,
        [assessment_id, user_id]
      );
    }

    // 3. Get assessment pass_percentage
    const [[assessmentDetails]] = await mysqlServerConnection.query(
      `SELECT pass_percentage 
       FROM ${req.user.db_name}.assessments 
       WHERE id = ? AND is_deleted = 0`,
      [assessment_id]
    );

    if (!assessmentDetails) {
      console.error("❌ Assessment not found:", assessment_id);
      return res.status(404).json({ success: false, message: "Assessment not found" });
    }

    const passPercentageRequired = assessmentDetails.pass_percentage;
    console.log("✅ Pass percentage required:", passPercentageRequired);

    // 4. Get total number of questions in assessment
    const [questionRows] = await mysqlServerConnection.query(
      `SELECT id FROM ${req.user.db_name}.questions 
       WHERE assessment_id = ? AND is_deleted = 0`,
      [assessment_id]
    );
    const total_questions = questionRows.length;
    console.log("📊 Total questions in assessment:", total_questions);

    // 5. Insert submitted answers
    const insertValues = answers.map(({ question_id, selected_option_id, correct_option_id, correct_value }) => {
      const selected = Array.isArray(selected_option_id)
        ? selected_option_id.map(String)
        : [String(selected_option_id)];

      const correct = Array.isArray(correct_option_id)
        ? correct_option_id.map(String)
        : [String(correct_option_id)];

      const is_correct =
        selected.length === correct.length &&
        selected.every((val) => correct.includes(val)) ? 1 : 0;

      return [
        question_id,
        user_id,
        JSON.stringify(selected),
        0, // collect_point
        is_correct,
        assessment_id,
        JSON.stringify(correct_value || ''),
        'mcq'
      ];
    });

    console.log("📥 Inserting answers:", JSON.stringify(insertValues, null, 2));

    await mysqlServerConnection.query(
      `INSERT INTO ${req.user.db_name}.question_answer 
       (question_id, user_id, answer, collect_point, is_correct, assessment_id, permanent_answer, question_type)
       VALUES ?`,
      [insertValues]
    );

    // 6. Calculate result
    const [[correctRow]] = await mysqlServerConnection.query(
      `SELECT COUNT(*) as correct 
       FROM ${req.user.db_name}.question_answer 
       WHERE assessment_id = ? AND user_id = ? AND is_correct = 1`,
      [assessment_id, user_id]
    );

    const correct_answers = correctRow.correct;
    const attempted_questions = answers.length;
    const wrong_answers = attempted_questions - correct_answers;
    const percentage = total_questions > 0
      ? Math.round((correct_answers / total_questions) * 100)
      : 0;
    const pass_status = percentage >= passPercentageRequired ? 'pass' : 'fail';

    console.log("📈 Result calculation:", {
      correct_answers,
      attempted_questions,
      wrong_answers,
      percentage,
      pass_status
    });

    // 7. Update assessment_users if passed
    if (pass_status === 'pass') {
      const [[existingStatus]] = await mysqlServerConnection.query(
        `SELECT is_passed FROM ${req.user.db_name}.assessment_users 
         WHERE user_id = ? AND assessment_id = ?`,
        [user_id, assessment_id]
      );

      console.log("🔎 Previous pass status:", existingStatus);

      if (!existingStatus || existingStatus.is_passed !== 1) {
        console.log("✅ Updating assessment_users with pass result...");
        await mysqlServerConnection.query(
          `INSERT INTO ${req.user.db_name}.assessment_users 
           (user_id, assessment_id, status, is_completed, is_eligible, is_passed, createdAt, updatedAt)
           VALUES (?, ?, 1, 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
           ON DUPLICATE KEY UPDATE 
             is_passed = 1, 
             is_completed = 1, 
             status = 1, 
             updatedAt = CURRENT_TIMESTAMP`,
          [user_id, assessment_id]
        );
      }
    }

    // 8. Final response
    const responsePayload = {
      success: true,
      message: "Assessment submitted successfully",
      is_attempted,
      result: {
        total_questions,
        attempted_questions,
        correct_answers,
        wrong_answers,
        percentage,
        pass_status
      }
    };

    console.log("📤 Final response to client:", responsePayload);

    return res.status(200).json(responsePayload);

  } catch (error) {
    console.error("❗ Error in submitAssessmentWithResult:", error);
    return res.status(500).json({
      success: false,
      message: "Internal server error"
    });
  }
};




const getVideoByID = async (req, res) => {
  try {
    const { video_id } = req.body;

    if (!video_id) {
      return res.status(400).json({
        success: false,
        message: "Video ID is required"
      });
    }

    const [videoRows] = await mysqlServerConnection.query(
      `SELECT id, video_name, video_title, video_url 
       FROM ${req.user.db_name}.videos 
       WHERE id = ?`,
      [video_id]
    );

    if (videoRows.length === 0) {
      return res.status(404).json({
        success: false,
        message: "Video not found"
      });
    }

    return res.status(200).json({
      success: true,
      data: videoRows[0]
    });

  } catch (error) {
    console.error("Error fetching video:", error);
    return res.status(500).json({
      success: false,
      message: "Internal server error"
    });
  }
};

const getAllResourcesByClassroomId = async (req, res) => {
  try {
    const { classroom_id } = req.body;
    console.log("classroom_id:", classroom_id);

    if (!classroom_id) {
      return res.status(400).json({
        success: false,
        message: "classroom_id is required",
        data: []
      });
    }

    const dbName = req.user.db_name;

    const [resources] = await mysqlServerConnection.query(
      `SELECT 
         id,
         resource_name,
         resource_description,
         resource_type,
         media_url,
         pushed_at,
         seen,
         seen_at,
         is_active,
         is_deleted,
         created_at,
         updated_at
       FROM ${dbName}.classroom_resources
       WHERE classroom_id = ? AND is_deleted = 0 AND is_active = 1
       ORDER BY created_at DESC`,
      [classroom_id]
    );

    return res.status(200).json({
      success: true,
      message: resources.length > 0
        ? "Classroom resources fetched successfully"
        : "No resources found for this classroom",
      data: resources
    });

  } catch (error) {
    console.error("Error fetching classroom resources:", error);
    return res.status(500).json({
      success: false,
      message: "Internal server error",
      data: []
    });
  }
};

const markResourceAsSeen = async (req, res) => {
  try {
    const { classroom_id, resource_id } = req.body;
    const dbName = req.user.db_name;

    if (!classroom_id || !resource_id) {
      return res.status(400).json({
        success: false,
        message: "classroom_id and resource_id are required",
      });
    }

    const [result] = await mysqlServerConnection.query(
      `UPDATE ${dbName}.classroom_resources
       SET seen = 1, seen_at = NOW()
       WHERE id = ? AND classroom_id = ? AND is_deleted = 0`,
      [resource_id, classroom_id]
    );

    if (result.affectedRows === 0) {
      return res.status(404).json({
        success: false,
        message: "No matching resource found or already deleted",
      });
    }

    return res.status(200).json({
      success: true,
      message: "Resource marked as seen",
    });

  } catch (error) {
    console.error("Error updating seen status:", error);
    return res.status(500).json({
      success: false,
      message: "Internal server error",
    });
  }
};



module.exports = {
  getAllAssessmentStatus,
  getAllAssessmentData,
  getQuestionsByAssessmentId,
  submitAnswers,
  restartQuestion,
  getAllAssessmentDataByClassId,
  getCompleteAndPendingAssessments,
  getAssessmentResults,
  getAssessmentResultsDetailed,
  getSurveyQuestionsByAssessmentId,
  submitSurveyAnswers,
  RestartSurvey,
  getCourseAssessment,
  submitAssessmentWithResult,
  getVideoByID,
  getAllResourcesByClassroomId,
  markResourceAsSeen
};
