const { mysqlServerConnection } = require('../../../db/db');

const fetchUserRankings  = async(req,res)=>{
    try {
        const [rankings] = await mysqlServerConnection.query(`SELECT 
        r.id, r.user_id, r.score, r.total_rank, r.start_rating, u.name, u.email FROM ${req.user.db_name}.rankings r
        JOIN ${req.user.db_name}.users u ON r.user_id = u.id
        ORDER BY r.total_rank ASC
        LIMIT 10;
       `)

        const user_ranking = await mysqlServerConnection.query(`
        SELECT r.id, r.user_id, r.score, r.total_rank, r.start_rating,
        u.name, u.email FROM ${req.user.db_name}.rankings r
        JOIN ${req.user.db_name}.users u ON r.user_id = u.id 
        where r.user_id = ?`,[req.user.userId]
        )

        return res.status(200).json({
            success: true,
            data:{
                rankings,
                user_ranking
            }
        })

    } catch (error) {
        res.status(500).json({
            success: false,
            message: error.message || 'Internal Server Error'
        })
    }
}

const getClassroomPerformanceGraph = async (req, res) => {
    const { class_id } = req.params;
    const user_id = req.user.userId; 
    try {
        // Step 1: Fetch total courses count and courseIds
        const [totalCoursesData] = await mysqlServerConnection.query(`
            SELECT COUNT(*) as totalCourses, GROUP_CONCAT(course_id) as courseIds
            FROM ${req.user.db_name}.course_classroom
            WHERE classroom_id = ?
        `, [class_id]);

        const totalCourses = totalCoursesData[0].totalCourses;
        const courseIds = totalCoursesData[0].courseIds ? totalCoursesData[0].courseIds.split(',') : [];

        if (totalCourses === 0) {
            return res.status(200).json({
                success: true,
                data: {
                    totalCourses: 0,
                    completedCourses: 0,
                    inProgressCourses: 0,
                    pendingCourses: 0,
                    completedPercentage: 0,
                    inProgressPercentage: 0,
                    pendingPercentage: 0
                }
            });
        }

        // Step 2: Fetch completed courses count
        const [completedCoursesData] = await mysqlServerConnection.query(`
            SELECT COUNT(DISTINCT course_id) as completedCourses
            FROM ${req.user.db_name}.certificates
            WHERE user_id = ? AND course_id IN (?)
        `, [user_id, courseIds]);

        const completedCourses = completedCoursesData[0].completedCourses || 0;

        // Step 3: Fetch in-progress courses count
        const [inProgressCoursesData] = await mysqlServerConnection.query(`
            SELECT COUNT(DISTINCT videos.course_id) as inProgressCourses
            FROM ${req.user.db_name}.recent_videos AS recent_videos
            INNER JOIN ${req.user.db_name}.videos AS videos
            ON recent_videos.video_id = videos.id
            LEFT JOIN ${req.user.db_name}.certificates AS certificate
            ON videos.course_id = certificate.course_id AND certificate.user_id = ?
            WHERE recent_videos.user_id = ? AND videos.course_id IN (?)
            AND certificate.course_id IS NULL
        `, [user_id, user_id, courseIds]);

        const inProgressCourses = inProgressCoursesData[0].inProgressCourses || 0;

        // Step 4: Calculate pending courses count
        const pendingCourses = totalCourses - completedCourses - inProgressCourses;

        // Step 5: Calculate percentages
        const completedPercentage = ((completedCourses / totalCourses) * 100).toFixed(2);
        const inProgressPercentage = ((inProgressCourses / totalCourses) * 100).toFixed(2);
        const pendingPercentage = ((pendingCourses / totalCourses) * 100).toFixed(2);

        // Step 6: Return the data in the required format
        res.status(200).json({
            success: true,
            data: {
                totalCourses,
                completedCourses,
                inProgressCourses,
                pendingCourses,
                completedPercentage: parseFloat(completedPercentage),
                inProgressPercentage: parseFloat(inProgressPercentage),
                pendingPercentage: parseFloat(pendingPercentage)
            }
        });

    } catch (error) {
        res.status(500).json({
            success: false,
            message: error.message || 'Internal Server Error'
        });
    }
};

const fetchClassroomCourses = async (req,res)=>{
    const {class_id} = req.params;
    try {

        const [courses] = await mysqlServerConnection.query(`SELECT c.id,c.course_name from ${req.user.db_name}.courses c 
        join ${req.user.db_name}.mycourses mc on c.id = mc.course_id where mc.class_id = ?`,[class_id]);

        return res.status(200).json({
            success: true,
            courses
        })

    } catch (error) {
        res.status(500).json({
            success: false,
            message: error.message || 'Internal Server Error'
        })
    }
}

const getClassCourseCompletionPercentage = async (req, res) => {
    const { course_id } = req.params;
    const user_id = req.user.userId; // Assuming user_id comes from authenticated user
    try {
        // Step 1: Fetch the total number of videos for the given course_id
        const [totalVideosData] = await mysqlServerConnection.query(`
            SELECT COUNT(*) as totalVideos
            FROM ${req.user.db_name}.videos
            WHERE course_id = ?
        `, [course_id]);

        const totalVideos = totalVideosData[0]?.totalVideos || 0;

        // If there are no videos in the course, return 0% completion
        if (totalVideos === 0) {
            return res.status(200).json({
                success: true,
                data: {
                    course_id,
                    completionPercentage: 0,
                },
            });
        }

        // Step 2: Fetch the count of completed videos for the user in this course
        const [completedVideosData] = await mysqlServerConnection.query(`
            SELECT COUNT(DISTINCT recent_videos.video_id) as completedVideos
            FROM ${req.user.db_name}.recent_videos AS recent_videos
            INNER JOIN ${req.user.db_name}.videos AS videos
            ON recent_videos.video_id = videos.id
            WHERE recent_videos.user_id = ? AND videos.course_id = ? AND recent_videos.is_complete = 1
        `, [user_id, course_id]);

        const completedVideos = completedVideosData[0]?.completedVideos || 0;

        // Step 3: Calculate the completion percentage
        const completionPercentage = ((completedVideos / totalVideos) * 100).toFixed(2);

        // Return the result
        res.status(200).json({
            success: true,
            data: {
                course_id,
                totalVideos,
                completedVideos,
                completionPercentage: Number(completionPercentage),
            },
        });
    } catch (error) {
        console.error("Error fetching course completion percentage:", error);
        res.status(500).json({
            success: false,
            message: error.message || "Internal Server Error",
        });
    }
};



module.exports = {
    fetchUserRankings,
    getClassroomPerformanceGraph,
    fetchClassroomCourses,
    getClassCourseCompletionPercentage
}